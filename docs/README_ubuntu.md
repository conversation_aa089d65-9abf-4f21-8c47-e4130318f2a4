# Ubuntu 快速安装指南

> 🔗 **主要内容请参考** [项目主 README](../README.md)

本指南仅列出 Ubuntu 系统的特定差异，帮助 Ubuntu 用户快速上手。

## ⚡ 一键安装

```bash
# 1. 基础环境
sudo apt update && sudo apt install -y curl wget git build-essential lcov

# 2. 安装 Go (官方最新版本)
wget https://go.dev/dl/go1.24.0.linux-amd64.tar.gz
sudo rm -rf /usr/local/go && sudo tar -C /usr/local -xzf go1.24.0.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
echo 'export GOPRIVATE=github.com/MoeGolibrary' >> ~/.bashrc
source ~/.bashrc

# 3. 安装 Bazelisk
wget https://github.com/bazelbuild/bazelisk/releases/latest/download/bazelisk-linux-amd64
chmod +x bazelisk-linux-amd64
sudo mv bazelisk-linux-amd64 /usr/local/bin/bazelisk
sudo ln -sf /usr/local/bin/bazelisk /usr/local/bin/bazel

# 4. 验证安装
go version && bazelisk version

# 5. 克隆项目并初始化
git clone https://github.com/MoeGolibrary/moego.git
cd moego && make init
```

## 🔧 主要差异对比

| 工具 | macOS | Ubuntu |
|-----|-------|---------|
| **Go** | `brew install golang` | 手动下载 tar.gz |
| **lcov** | `brew install lcov` | `apt install lcov` |
| **buf** | `brew install bufbuild/buf/buf` | `make init` 自动安装 |
| **Bazelisk** | `brew install bazelisk` | 手动下载二进制 |

## ❗ 常见问题

**权限问题：**
```bash
# 确保可执行权限
sudo chmod +x /usr/local/bin/bazelisk /usr/local/bin/bazel

# 修复 Go 模块权限
sudo chown -R $USER:$USER $(go env GOPATH) ~/.cache
```

**网络问题：**
```bash
# 配置国内代理
export GOPROXY=https://goproxy.cn,direct
export GOSUMDB=sum.golang.org
```

**版本兼容性：**
```bash
# 卸载系统自带的旧版本 Go
sudo apt remove golang-go
```

## 🚀 性能优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# 创建 Bazel 配置
mkdir -p ~/.config/bazel
cat > ~/.config/bazel/bazelrc << EOF
build --jobs=$(nproc)
build --disk_cache=~/.cache/bazel
startup --host_jvm_args=-Xmx4g
EOF
```

## 📝 可选工具

**API 测试工具（可选）：**
```bash
# Node.js 和 OpenAPI Generator
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
npm install -g @openapitools/openapi-generator-cli
```

**VS Code 安装：**
```bash
wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg
sudo install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/
sudo sh -c 'echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" > /etc/apt/sources.list.d/vscode.list'
sudo apt update && sudo apt install code
```

---

💡 **遇到问题？** 在 [GitHub Issues](https://github.com/MoeGolibrary/moego/issues) 中添加 `ubuntu` 标签提问 