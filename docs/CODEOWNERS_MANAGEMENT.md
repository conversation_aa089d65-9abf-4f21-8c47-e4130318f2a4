# CODEOWNERS 权限管理指南

## 📋 概述

CODEOWNERS 系统提供基于文件路径的代码审查权限控制，确保关键代码变更由合适的人员审核。

**核心功能：**
- 🔐 **自动权限控制** - 特定路径的代码变更自动邀请对应负责人审查
- 🤖 **自动化管理** - 服务级别配置，自动聚合到根目录
- 👥 **团队协作** - 支持个人和团队级别的权限设置

## 🚀 快速开始

### 为新服务设置权限

```bash
# 1. 创建新服务时自动设置
make create module=platform service=analytics

# 2. 手动配置（编辑 metadata.yaml）
vim backend/app/analytics/metadata.yaml

# 3. 重新生成 CODEOWNERS 文件
make codeowners
```

### 更新现有服务权限

```bash
# 1. 编辑服务配置
vim backend/app/customer/metadata.yaml

# 2. 更新 CODEOWNERS
make codeowners

# 3. 提交变更
git add CODEOWNERS backend/app/customer/metadata.yaml
git commit -m "Update codeowners for customer service"
```

## ⚙️ 配置说明

### metadata.yaml 配置格式

```yaml
spec:
  name: moego-customer
  description: Customer management service
  cd: true
  codeowners:
    - "@moego-crm-team"        # 团队（推荐）
    - "<EMAIL>"        # 个人邮箱
    - "@alice"                 # GitHub 用户名
```

### 权限类型说明

| 类型 | 格式 | 示例 | 使用场景 |
|-----|------|------|---------|
| **团队** | `@team-name` | `@moego-crm-team` | 多人协作的服务 |
| **个人** | `@username` | `@alice` | 个人负责的工具 |
| **邮箱** | `email@domain` | `<EMAIL>` | 临时负责人 |

## 📁 生成的 CODEOWNERS 文件

```gitignore
# This file is auto-generated by scripts/generate_codeowners.sh
# Do not edit manually - edit metadata.yaml files instead

# Customer Management Service
/backend/app/customer/ @moego-crm-team <EMAIL>

# Sales Management Service  
/backend/app/sales/ @moego-sales-team

# Platform Tools
/backend/app/tools/ @moego-platform-team

# Frontend Applications
/frontend/app/console/ @moego-frontend-team

# Default owners for everything else
* <EMAIL> <EMAIL> <EMAIL>
```

## 🎯 实际使用场景

### 场景 1：新团队接手服务

```bash
# 原来：@alice 负责 customer 服务
# 现在：@moego-crm-team 接手

# 更新配置
vim backend/app/customer/metadata.yaml
# 修改 codeowners: ["@alice"] → ["@moego-crm-team"]

make codeowners
git commit -m "Transfer customer service ownership to CRM team"
```

### 场景 2：关键服务需要多人审核

```yaml
# backend/app/payment/metadata.yaml
spec:
  name: moego-payment
  codeowners:
    - "@moego-platform-team"   # 主要负责团队
    - "@moego-security-team"   # 安全审查
    - "<EMAIL>"    # 技术负责人审核
```

### 场景 3：共享组件权限管理

```yaml
# backend/common/rpc/metadata.yaml
spec:
  name: moego-rpc-framework
  codeowners:
    - "@moego-platform-team"   # 平台团队维护
    - "@moego-arch-team"       # 架构团队审核
```

### 场景 4：前端项目权限分配

```yaml
# frontend/app/console/metadata.yaml
spec:
  name: moego-admin-console
  codeowners:
    - "@moego-frontend-team"
    - "<EMAIL>"
```

## 🔧 管理命令

### 常用操作

```bash
# 重新生成 CODEOWNERS 文件
make codeowners

# 检查权限配置
./scripts/generate_codeowners.sh --dry-run

# 查看当前配置
grep -r "codeowners:" backend/app/*/metadata.yaml
```

### 备份和恢复

```bash
# 查看备份文件
ls -la backup/codeowners/

# 恢复特定时间的备份
cp backup/codeowners/CODEOWNERS.backup.20241218_143052 CODEOWNERS

# 手动备份当前配置
cp CODEOWNERS backup/codeowners/CODEOWNERS.manual.$(date +%Y%m%d_%H%M%S)
```

## 💡 最佳实践

### 1. 权限分配原则

**按业务域分配**
```bash
/backend/app/customer/     → @moego-crm-team
/backend/app/sales/        → @moego-sales-team
/backend/app/fulfillment/  → @moego-ops-team
```
### 2. 团队配置建议

**优先使用团队而非个人**
```yaml
# ✅ 推荐：团队级别权限
codeowners:
  - "@moego-crm-team"

# ❌ 避免：单个人负责
codeowners:
  - "<EMAIL>"
```

**关键服务多重审核**
```yaml
# 支付、认证等关键服务
codeowners:
  - "@moego-platform-team"
  - "@moego-security-team"
  - "<EMAIL>"
```

### 3. 维护规范

**定期审查权限**
```bash
# 每月检查权限配置
find backend/app -name metadata.yaml -exec grep -l "codeowners" {} \; | \
xargs grep -H "codeowners:"
```

**新服务必须配置权限**
```bash
# 在 scripts/.create_app.sh 中自动配置
# 避免服务没有明确负责人
```

## ⚠️ 注意事项

### 权限继承规则

1. **具体路径优先** - `/backend/app/customer/` 优先于 `/backend/app/`
2. **多个匹配** - 需要所有匹配规则的 CODEOWNER 审核
3. **默认规则** - `*` 规则为兜底，适用于未明确配置的路径

### 常见问题

**Q: 为什么我的 PR 需要这么多人审核？**
A: 可能触发了多个 CODEOWNERS 规则，检查文件变更路径。

**Q: 如何临时绕过 CODEOWNERS 审核？**
A: 管理员可以使用 "Administrator override" 功能，但需要谨慎使用。

**Q: 团队成员变更如何处理？**
A: 在 GitHub 团队设置中管理成员，CODEOWNERS 会自动生效。

## 🛠️ 故障排除

### 工具问题

```bash
# yq 工具缺失
brew install yq  # macOS
sudo apt install yq  # Ubuntu

# 脚本权限问题
chmod +x scripts/generate_codeowners.sh

# 备份目录不存在
mkdir -p backup/codeowners
```

### 配置问题

```bash
# 验证 metadata.yaml 格式
yq eval '.spec.codeowners' backend/app/customer/metadata.yaml

# 检查生成的 CODEOWNERS 语法
./scripts/generate_codeowners.sh --validate
```

---

## 维护者

- jett <<EMAIL>> 