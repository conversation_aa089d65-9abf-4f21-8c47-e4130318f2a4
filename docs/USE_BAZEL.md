# Bazel 使用指南

Bazel 是由 Google 开发的高性能构建工具，专为大规模项目设计。MoeGo 选择 Bazel 来解决传统构建工具在 Monorepo 环境下的性能和扩展性问题。

## 📖 目录

- [为什么选择 Bazel](#为什么选择-bazel)
- [快速上手](#快速上手)
- [常用操作](#常用操作)
- [项目结构](#项目结构)
- [故障排除](#故障排除)
- [性能优化](#性能优化)

## 🤔 为什么选择 Bazel

### 核心优势

**🚀 构建速度**
- **增量编译**：只重新编译修改过的部分
- **并行编译**：充分利用多核 CPU
- **智能缓存**：本地和远程缓存避免重复构建

**🌍 跨语言支持**
- 统一管理 Go、Proto、TypeScript 等
- 一致的构建命令和配置
- 精确的依赖关系管理

**🔒 构建一致性**
- 沙箱环境确保构建可重现
- 不依赖本地环境变量（如 GOPATH）
- 相同输入保证相同输出

### 实际效果对比

| 场景 | 传统 go build | Bazel |
|-----|---------------|-------|
| **全量构建** | 每次都重新编译所有代码 | 只编译变更部分 |
| **增加新依赖** | 重新下载编译所有依赖 | 智能缓存复用 |
| **跨模块变更** | 需要手动管理构建顺序 | 自动依赖分析 |
| **团队协作** | 环境差异导致构建不一致 | 沙箱保证一致性 |

## 🚀 快速上手

### 基础命令

```bash
# 编译所有后端服务
make build

# 编译指定服务
make build dir=//backend/app/customer

# 编译指定目录下的所有项目
make build dir=//backend/app/customer/...

# 运行测试
make test dir=//backend/app/customer

# 运行特定服务
make run app=customer
```

### 理解构建目标

Bazel 使用 `//` 开头的标签来标识构建目标：

```bash
//backend/app/customer              # customer 目录
//backend/app/customer:customer     # customer 二进制文件
//backend/app/customer/...          # customer 下所有目标
//backend/app/...                   # app 下所有目标
//...                               # 整个项目的所有目标
```

## 🔨 常用操作

### 编译相关

```bash
# 编译特定服务
bazelisk build //backend/app/customer

# 编译所有后端服务
bazelisk build //backend/app/...

# 编译 Proto 文件
bazelisk build //backend/proto:moego

# 查看编译输出位置
bazelisk info bazel-bin
```

### 测试相关

```bash
# 运行单元测试
bazelisk test //backend/app/customer/...

# 运行特定测试
bazelisk test //backend/app/customer/logic:greeter_test

# 查看测试日志
bazelisk test //backend/app/customer/... --test_output=all
```

### 运行服务

```bash
# 运行编译好的服务
bazelisk run //backend/app/customer:customer

# 带参数运行
bazelisk run //backend/app/customer:customer -- --config=local/config.yaml
```

### 清理操作

```bash
# 清理构建缓存
make clean

# 完全清理（包括外部缓存）
make clean cache=1

# 仅清理特定目标
bazelisk clean //backend/app/customer/...
```

## 📁 项目结构

### 关键文件说明

```
moego/
├── MODULE.bazel              # 项目依赖管理（Bazel 8.0+）
├── BUILD.bazel               # 根目录构建配置
├── .bazelrc                  # Bazel 配置文件
├── backend/
│   ├── app/
│   │   └── customer/
│   │       ├── BUILD.bazel   # 服务构建配置
│   │       └── main.go       # 服务入口
│   └── proto/
│       └── customer/
│           ├── BUILD.bazel   # Proto 构建配置
│           └── customer.proto
└── bazel/
    └── .bazelrc              # 平台特定配置
```

### BUILD.bazel 文件结构

每个包含代码的目录都有一个 `BUILD.bazel` 文件：

```python
# Go 二进制服务示例
load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "customer_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer",
    deps = [
        "//backend/app/customer/service",
        "//backend/common/rpc/rpc",
        "//backend/proto/customer/v1:customer",
    ],
)

go_binary(
    name = "customer",
    embed = [":customer_lib"],
    visibility = ["//visibility:public"],
)
```

### 依赖管理

**MODULE.bazel** 管理外部依赖：
```python
# Go 相关依赖
bazel_dep(name = "rules_go", version = "0.50.1")
bazel_dep(name = "gazelle", version = "0.40.0")

# Proto 相关依赖  
bazel_dep(name = "rules_proto", version = "7.0.2")
bazel_dep(name = "protobuf", version = "29.1")
```

**gazelle** 自动生成 BUILD.bazel 文件：
```bash
# 更新所有 BUILD.bazel 文件
make gazelle

# 当修改 go.mod 时，同步外部依赖
make gazelle  # 会自动检测并更新依赖
```

## 🔧 故障排除

### 常见问题

**编译失败：找不到依赖**
```bash
# 1. 检查 go.mod 是否有更新
go mod tidy

# 2. 重新生成 BUILD 文件
make gazelle

# 3. 清理并重新编译
make clean && make build
```

**Proto 编译错误**
```bash
# 清理 proto 生成文件
find backend/proto -name "*.pb.go" -delete

# 重新生成 proto 文件
make proto

# 重新生成 BUILD 文件
make gazelle
```

**依赖冲突**
```bash
# 查看依赖图
bazelisk query --output=graph //backend/app/customer/... | dot -Tpng > deps.png

# 查看特定目标的依赖
bazelisk query "deps(//backend/app/customer:customer)"
```

**编译缓存问题**
```bash
# 完全清理重新开始
bazelisk clean --expunge
make gazelle
make build
```

### 调试技巧

**查看详细构建日志**
```bash
# 显示详细错误信息
bazelisk build //backend/app/customer --verbose_failures

# 显示构建过程
bazelisk build //backend/app/customer -s
```

**查看生成的文件**
```bash
# 查看构建输出目录
bazelisk info bazel-bin

# 查看特定目标的输出文件
ls -la $(bazelisk info bazel-bin)/backend/app/customer/
```

## 🚀 性能优化

### 本地优化

**配置缓存**
```bash
# 在 ~/.bazelrc 中添加
build --disk_cache=~/.cache/bazel
build --repository_cache=~/.cache/bazel/repo

# 限制内存使用
startup --host_jvm_args=-Xmx4g
```

**并行构建**
```bash
# 使用所有 CPU 核心
build --jobs=auto

# 或者手动指定
build --jobs=8
```

**跳过不必要的步骤**
```bash
# 仅编译，不运行测试
bazelisk build //... --build_tests_only

# 跳过 lint 检查（仅用于快速测试）
bazelisk build //... --config=fast
```

### 团队优化

**共享构建缓存**（如果团队有条件）
```bash
# 在 .bazelrc 中配置远程缓存
build --remote_cache=https://your-cache-server.com
```

**优化 CI/CD**
```bash
# CI 环境使用更激进的并行设置
build --jobs=50
build --ram_utilization_factor=90
```

### 监控和分析

**构建性能分析**
```bash
# 生成构建性能报告
bazelisk build //... --profile=profile.json
bazelisk analyze-profile profile.json
```

**查看缓存命中率**
```bash
# 构建时显示缓存统计
bazelisk build //... --experimental_ui_show_cache_stats
```

## 📚 最佳实践

### 1. 日常开发工作流

```bash
# 每日开始工作
cd moego
git pull origin main
make gazelle  # 同步依赖变更

# 开发过程
vim backend/app/customer/service/customer.go
make lint     # 代码检查
make build dir=//backend/app/customer  # 快速编译验证
make test dir=//backend/app/customer   # 运行测试

# 提交前检查
make build    # 全量编译
make test     # 全量测试
```

### 2. 新功能开发

```bash
# 1. 更新 Proto 定义
vim backend/proto/customer/v1/customer.proto
make proto    # 生成 Go 代码

# 2. 实现业务逻辑
vim backend/app/customer/service/customer_service.go
make gazelle  # 更新 BUILD 文件（如果有新的 import）

# 3. 测试验证
make build dir=//backend/app/customer
make test dir=//backend/app/customer
make run app=customer local=1
```

### 3. 问题排查

```bash
# 构建问题
make clean && make build 2>&1 | tee build.log

# 依赖问题
bazelisk query "somepath(//backend/app/customer:customer, //problematic/target)"

# 性能问题
bazelisk build //... --profile=profile.json
```

---

## 📞 获取帮助

- **Bazel 官方文档**：https://bazel.build/
- **项目特定问题**：查看 [项目 README](../README.md) 或在 Issues 中提问
- **构建失败**：在 [GitHub Issues](https://github.com/MoeGolibrary/moego/issues) 中附上构建日志

---

💡 **记住**：Bazel 有学习曲线，但一旦掌握，会显著提升大型项目的构建效率。遇到问题时，优先尝试 `make clean && make gazelle && make build`。
