package aws

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"

	"github.com/MoeGolibrary/moego/cli/devops_executor/utils"
)

var (
	CreateSecretsManagerClient = createSecretsManagerClient
)

func CreateClientConfig(ctx context.Context, region, accessKey, secretkey, token string) (aws.Config, error) {
	if utils.IsBlank(accessKey) || utils.IsBlank(secretkey) {
		if utils.IsBlank(region) {
			return config.LoadDefaultConfig(ctx)
		}
		return config.LoadDefaultConfig(ctx, config.WithRegion(region))
	}

	provider := credentials.NewStaticCredentialsProvider(accessKey, secretkey, token)
	if utils.IsBlank(region) {
		return config.LoadDefaultConfig(ctx, config.WithCredentialsProvider(provider))
	}
	return config.LoadDefaultConfig(ctx, config.WithRegion(region), config.WithCredentialsProvider(provider))
}
