package aws

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager/types"

	"github.com/MoeGolibrary/moego/cli/devops_executor/utils"
)

func createSecretsManagerClient(ctx context.Context,
	region, accessKey, secretkey, token string) (*secretsmanager.Client, error) {
	config, err := CreateClientConfig(ctx, region, accessKey, secretkey, token)
	if err != nil {
		fmt.Println("Load AWS client config failed.")
		return nil, err
	}

	return secretsmanager.NewFromConfig(config), nil
}

func SecretsToMap(str *string) (map[string]string, error) {
	var secrets map[string]string
	if err := json.Unmarshal([]byte(aws.ToString(str)), &secrets); err != nil {
		return nil, fmt.Errorf("failed to unmarshal secret: %v", err)
	}

	return secrets, nil
}

func GetSecret(ctx context.Context,
	client *secretsmanager.Client,
	id string) (*secretsmanager.GetSecretValueOutput, error) {
	request := &secretsmanager.GetSecretValueInput{SecretId: aws.String(id)}
	return client.GetSecretValue(ctx, request)
}

func ListSecrets(ctx context.Context,
	client *secretsmanager.Client,
	prefixes []string) ([]types.SecretListEntry, error) {
	var entries []types.SecretListEntry
	var pageSize int32 = 100
	request := secretsmanager.ListSecretsInput{MaxResults: &pageSize}
	if len(prefixes) > 0 {
		filter := types.Filter{Key: types.FilterNameStringTypeName, Values: prefixes}
		request.Filters = []types.Filter{filter}
	}

	var next *string
	for {
		request.NextToken = next
		response, err := client.ListSecrets(ctx, &request)
		if err != nil {
			return nil, err
		}

		next = response.NextToken
		n := len(response.SecretList)
		if n > 0 {
			entries = append(entries, response.SecretList...)
		}
		if n < int(pageSize) || next == nil || utils.IsBlank(*next) {
			break
		}
	}

	return entries, nil
}

func BatchGetSecretValues(ctx context.Context,
	client *secretsmanager.Client,
	names []string) (map[string]string, error) {
	request := &secretsmanager.BatchGetSecretValueInput{SecretIdList: names}
	response, err := client.BatchGetSecretValue(ctx, request)
	if err != nil {
		return nil, err
	}
	result := make(map[string]string)
	for _, secret := range response.SecretValues {
		result[*secret.Name] = aws.ToString(secret.SecretString)
	}
	return result, nil
}

func DeleteSecret(ctx context.Context, client *secretsmanager.Client, id string) (string, error) {
	request := &secretsmanager.DeleteSecretInput{
		SecretId: aws.String(id),
	}
	response, err := client.DeleteSecret(ctx, request)
	if err != nil {
		return "", err
	}
	return aws.ToString(response.ARN), nil
}

func CreateSecret(ctx context.Context,
	client *secretsmanager.Client,
	name, description, str string,
	binary []byte,
	tags map[string]string) (string, error) {
	request := &secretsmanager.CreateSecretInput{Name: aws.String(name)}
	if utils.IsBlank(description) {
		request.Description = &description
	}
	if utils.IsBlank(str) {
		request.SecretBinary = binary
	} else {
		request.SecretString = aws.String(str)
	}
	if len(tags) > 0 {
		for k, v := range tags {
			request.Tags = append(request.Tags, types.Tag{Key: aws.String(k), Value: aws.String(v)})
		}
	}

	response, err := client.CreateSecret(ctx, request)
	if err != nil {
		return "", err
	}
	return aws.ToString(response.ARN), nil
}

func UpdateSecret(ctx context.Context,
	client *secretsmanager.Client,
	name, description, str string,
	binary []byte) (string, error) {
	request := &secretsmanager.UpdateSecretInput{SecretId: aws.String(name)}
	if utils.IsBlank(description) {
		request.Description = &description
	}
	if utils.IsBlank(str) {
		request.SecretBinary = binary
	} else {
		request.SecretString = aws.String(str)
	}

	response, err := client.UpdateSecret(ctx, request)
	if err != nil {
		return "", err
	}
	return aws.ToString(response.VersionId), nil
}
