package fs

import (
	"bufio"
	"errors"
	"os"
	"path/filepath"
)

func IsExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, err
	}
	return true, err
}

func IsDir(path string) (bool, error) {
	info, err := os.Stat(path)
	if err != nil {
		return false, err
	}
	return info.IsDir(), nil
}

func IsFile(path string) (bool, error) {
	info, err := os.Stat(path)
	if err != nil {
		return false, err
	}
	return info.Mode().IsRegular(), nil
}

// read text from a file
func ReadText(path string) (string, error) {
	bytes, err := os.ReadFile(path)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// read line from a file
func ReadLines(path string) ([]string, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}
	if err := scanner.Err(); err != nil {
		return lines, err
	}
	return lines, nil
}

// write text to a file
func WriteText(path string, text string) (int, error) {
	const defaultFilePerm = 0644
	const defaultDirPerm = 0755
	const defaultMode = os.O_WRONLY | os.O_CREATE | os.O_TRUNC

	file, err := os.OpenFile(path, defaultMode, defaultFilePerm)
	if err == nil {
		defer file.Close()
		return file.WriteString(text)
	}

	if errors.Is(err, os.ErrNotExist) {
		if err := os.MkdirAll(filepath.Dir(path), defaultDirPerm); err != nil {
			return 0, err
		}

		if file, err = os.OpenFile(path, defaultMode, defaultFilePerm); err != nil {
			return 0, err
		}
		defer file.Close()
		return file.WriteString(text)
	}

	return 0, err
}

// write lines to a file
func WriteLines(path string, lines []string, isAppend bool) (int, error) {
	const defaultFilePerm = 0644
	const defaultDirPerm = 0755
	defaultMode := os.O_WRONLY | os.O_CREATE
	if isAppend {
		defaultMode |= os.O_APPEND
	} else {
		defaultMode |= os.O_TRUNC
	}

	file, err := os.OpenFile(path, defaultMode, defaultFilePerm)
	if err == nil {
		defer file.Close()
		return writeLines(file, lines)
	}

	if errors.Is(err, os.ErrNotExist) {
		if err := os.MkdirAll(filepath.Dir(path), defaultDirPerm); err != nil {
			return 0, err
		}

		if file, err = os.OpenFile(path, defaultMode, defaultFilePerm); err != nil {
			return 0, err
		}
		defer file.Close()
		return writeLines(file, lines)
	}

	return 0, err
}

func writeLines(file *os.File, lines []string) (int, error) {
	totalBytes := 0
	writer := bufio.NewWriter(file)

	for _, line := range lines {
		bytesWritten, err := writer.WriteString(line + "\n")
		totalBytes += bytesWritten
		if err != nil {
			return totalBytes, err
		}
	}

	if err := writer.Flush(); err != nil {
		return totalBytes, err
	}

	return totalBytes, nil
}
