package reader

import (
	"context"
	"fmt"

	"k8s.io/apimachinery/pkg/api/errors"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"github.com/MoeGolibrary/moego/cli/devops_executor/utils"
)

type K8SSecretsReader struct {
	client    kubernetes.Interface
	Namespace string
	Name      string
	Prifix    string
	Optional  bool
}

func newK8SSecretsReader(client kubernetes.Interface, ns, name, prefix string, optional bool) PairsReader {
	return &K8SSecretsReader{client: client, Namespace: ns, Name: name, Prifix: prefix, Optional: optional}
}

func (reader *K8SSecretsReader) Type() string {
	return "K8SSecretsReader"
}

func (reader *K8SSecretsReader) Read(ctx context.Context) (map[string]string, error) {
	secret, err := reader.client.CoreV1().Secrets(reader.Namespace).Get(ctx, reader.Name, v1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			fmt.Printf("NOT found k8s secret: %s/%s\n", reader.Namespace, reader.Name)
			if reader.Optional {
				return make(map[string]string), nil
			}
			return nil, err
		}

		return nil, fmt.Errorf("failed to get secret: %s/%s, %v", reader.Namespace, reader.Name, err)
	}

	result := make(map[string]string)
	if utils.IsBlank(reader.Prifix) {
		for key, value := range secret.Data {
			result[key] = string(value)
		}
	} else {
		for key, value := range secret.Data {
			result[reader.Prifix+key] = string(value)
		}
	}

	return result, nil
}
