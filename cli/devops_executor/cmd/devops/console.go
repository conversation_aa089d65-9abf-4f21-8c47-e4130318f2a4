package devops

import (
	"github.com/spf13/cobra"

	"github.com/MoeGolibrary/moego/cli/devops_executor/logic/devops"
)

var consoleRequest devops.ConsoleRequest

var ConsoleCmd = &cobra.Command{
	Use:   "console",
	Short: "Send some requests to DevOps Console according to the resource files definition.",
	RunE: func(cmd *cobra.Command, args []string) error {
		return devops.SendConsoleRequest(cmd.Context(), consoleRequest)
	},
}

func init() {
	txtFile := "[required] Specify request resources file."
	txtHost := "[optional] Specify the requested host address."
	txtHeader := "[optional] Specify request default header."

	ConsoleCmd.Flags().SortFlags = false
	ConsoleCmd.Flags().StringVarP(&consoleRequest.File, "file", "f", "", txtFile)
	ConsoleCmd.Flags().StringVar(&consoleRequest.Host, "host", "", txtHost)
	ConsoleCmd.Flags().StringArrayVarP(&consoleRequest.Headers, "header", "H", []string{}, txtHeader)
	ConsoleCmd.MarkFlagRequired("file")
}
