package translator

import (
	"github.com/spf13/cobra"

	"github.com/MoeGolibrary/moego/cli/devops_executor/logic/translator"
)

var translateFileParams translator.TranslateFileParams

var FileCmd = &cobra.Command{
	Use:   "file",
	Short: "Translate one or more plain text files. The translation is replaced according to the variable placeholder.",
	Long: `Translate the plain text file. The source path can be a single file or a directory. The translation process replaces the placeholders in the file.
You can specify one or more of --set, --k8s-secret, --aws-secret, --properties to specify the value of the placeholder.
If different flags are specified, their priority is --set, --properties, --k8s-secret, --aws-secret.
If the same flag is specified multiple times, the later specified flag have higher priority.`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return translator.TranslateFile(cmd.Context(), translateFileParams)
	},
}

func init() {
	txtSource := "[required] Specify the path for the source template file(s), which can be a directory or a single file."
	txtOutput := "[optional] Specify the translated output path. If not specified, it will be the same as the source path."
	txtPairs := "[optional] Specify a key-value pair. e.g. '--set k1=v1 --set k2=v2'"
	txtProperties := "[optional] Specify a properties file, e.g. '--properties filename --properties file=name,prefix=xxx,optional=true'"
	txtK8SSecrets := "[optional] Specify a kubernetes secret name, e.g. '--k8s-secret ns/name --k8s-secret name=ns/name,prefix=xxx,optional=true'"
	txtAwsSecrets := "[optional] Specify a AWS secret manager name, e.g. '--aws-secret name --aws-secret name=name,prefix=xxx,optional=true'"

	FileCmd.Flags().SortFlags = false
	FileCmd.Flags().StringVarP(&translateFileParams.Source, "source", "", "", txtSource)
	FileCmd.Flags().StringVarP(&translateFileParams.Output, "output", "o", "", txtOutput)
	FileCmd.Flags().StringArrayVar(&translateFileParams.Pairs, "set", []string{}, txtPairs)
	FileCmd.Flags().StringArrayVar(&translateFileParams.Properties, "properties", []string{}, txtProperties)
	FileCmd.Flags().StringArrayVar(&translateFileParams.K8SSecrets, "k8s-secret", []string{}, txtK8SSecrets)
	FileCmd.Flags().StringArrayVar(&translateFileParams.AWSSecrets, "aws-secret", []string{}, txtAwsSecrets)
	FileCmd.MarkFlagRequired("source")
	FileCmd.MarkFlagsOneRequired("set", "properties", "k8s-secret", "aws-secret")
}
