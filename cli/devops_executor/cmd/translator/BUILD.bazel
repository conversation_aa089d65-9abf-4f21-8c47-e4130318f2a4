load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "translator",
    srcs = [
        "file.go",
        "grpc_transcoder.go",
        "kustomization.go",
        "virtual_service.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/cli/devops_executor/cmd/translator",
    visibility = ["//visibility:public"],
    deps = [
        "//cli/devops_executor/logic/translator",
        "//cli/devops_executor/logic/translator/istio",
        "@com_github_spf13_cobra//:cobra",
    ],
)
