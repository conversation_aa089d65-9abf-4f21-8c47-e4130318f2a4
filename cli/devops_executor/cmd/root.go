package cmd

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"github.com/spf13/pflag"

	"github.com/MoeGolibrary/moego/cli/devops_executor/cmd/cluster"
	"github.com/MoeGolibrary/moego/cli/devops_executor/cmd/devops"
	"github.com/MoeGolibrary/moego/cli/devops_executor/cmd/translator"
	"github.com/MoeGolibrary/moego/cli/devops_executor/config"
)

var (
	BuildDate = ""
	GitCommit = ""

	rootCmd = &cobra.Command{
		Use:   "devops-executor",
		Short: "A devops command line tool",
		Long:  "A devops command line tool that will be used to perform translation, cleaning, viewing, etc. of various resources",
		RunE: func(cmd *cobra.Command, args []string) error {
			return cmd.Help()
		},
	}
)

func init() {
	cobra.EnableCommandSorting = false

	translatorCmd := &cobra.Command{
		Use:   "translate",
		Short: "Translate various resource files.",
	}
	translatorCmd.AddCommand(translator.FileCmd)
	translatorCmd.AddCommand(translator.GrpcTranscoderCmd)
	translatorCmd.AddCommand(translator.VirtaulServiceCmd)
	translatorCmd.AddCommand(translator.KustomizationCmd)

	clusterCmd := &cobra.Command{
		Use:   "cluster",
		Short: "Various operations on the cluster, such as clean and view.",
	}
	clusterCmd.AddCommand(cluster.APIResourcesCmd)
	clusterCmd.AddCommand(cluster.CleanCmd)

	devopsCmd := &cobra.Command{
		Use:   "devops",
		Short: "Execute requests related for devops",
	}
	devopsCmd.AddCommand(devops.ConsoleCmd)

	versionCmd := &cobra.Command{
		Use:   "version",
		Short: "Show version information.",
		Run: func(cmd *cobra.Command, args []string) {
			showVersion()
		},
	}

	rootCmd.Flags().BoolP("version", "v", false, "Show version information")
	rootCmd.PersistentFlags().BoolVar(&config.GlobalFlags.DryRun, "dry-run", false, "Simulate execution without making changes")
	rootCmd.PersistentFlags().BoolVar(&config.GlobalFlags.Debug, "debug", false, "Enable debug output")

	rootCmd.PersistentPreRunE = persistentPreRun
	rootCmd.CompletionOptions.HiddenDefaultCmd = true
	rootCmd.AddCommand(clusterCmd, devopsCmd, translatorCmd, versionCmd)
}

func Execute() error {
	return rootCmd.Execute()
}

func persistentPreRun(cmd *cobra.Command, args []string) error {
	allowedRootFlags := map[string]bool{
		"version": true,
		"help":    true,
	}
	if cmd == rootCmd {
		// check other flag
		cmd.Flags().Visit(func(flag *pflag.Flag) {
			if !allowedRootFlags[flag.Name] {
				fmt.Printf("The %s flag must be used with a subcommand.\n", flag.Name)
				os.Exit(1)
			}
		})
		// check version flag
		if v, _ := cmd.Flags().GetBool("version"); v {
			showVersion()
			os.Exit(0)
		}
	}

	return nil
}

func showVersion() {
	fmt.Println("version: 2.0.0")
	fmt.Println("author: Frank")
	fmt.Println("email: <EMAIL>")
	fmt.Println("build/date:", BuildDate)
	fmt.Println("git/commit:", GitCommit)
}
