load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "cmd",
    srcs = ["root.go"],
    importpath = "github.com/MoeGolibrary/moego/cli/devops_executor/cmd",
    visibility = ["//visibility:public"],
    deps = [
        "//cli/devops_executor/cmd/cluster",
        "//cli/devops_executor/cmd/devops",
        "//cli/devops_executor/cmd/translator",
        "//cli/devops_executor/config",
        "@com_github_spf13_cobra//:cobra",
        "@com_github_spf13_pflag//:pflag",
    ],
)
