package cluster

import (
	"github.com/spf13/cobra"

	"github.com/MoeGolibrary/moego/cli/devops_executor/logic/k8s"
)

var apiResourcesParams k8s.APIResourcesParams

var APIResourcesCmd = &cobra.Command{
	Use:   "api-resources",
	Short: "Query cluster api resources version information.",
	Long: `Get a list of all resources in a specified cluster.
You can choose to sort them by group and output them to a specified file.
The main purpose of this function is to view which resources are registered in the cluster.`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return k8s.ClusterAPIResources(cmd.Context(), apiResourcesParams)
	},
}

func init() {
	txtCluster := "[optional] Specifies a cluster name. If not specified, it is determined based on the current context of kubeconfig."
	txtSort := "[optional] Specify whether to sort the API Groups, default: false."
	txtOutput := "[optional] Specify the output path of the information, default: ./api-resources.yaml."

	APIResourcesCmd.Flags().SortFlags = false
	APIResourcesCmd.Flags().StringVar(&apiResourcesParams.Cluster, "cluster", "", txtCluster)
	APIResourcesCmd.Flags().BoolVar(&apiResourcesParams.Sort, "sort", false, txtSort)
	APIResourcesCmd.Flags().StringVarP(&apiResourcesParams.Output, "output", "o", "", txtOutput)
}
