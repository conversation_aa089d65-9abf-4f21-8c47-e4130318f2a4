package cluster

import (
	"github.com/spf13/cobra"

	"github.com/MoeGolibrary/moego/cli/devops_executor/logic/k8s"
)

var configPath string

var CleanCmd = &cobra.Command{
	Use:   "clean",
	Short: "Clean up unused resources in the development cluster.",
	Long: `Clean up unused resources in the development cluster.
The resource types that support cleanup include: Deployment, Service, EnvoyFilter, PodDisruptionBudget.`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return k8s.CleanCluster(cmd.Context(), configPath)
	},
}

func init() {
	CleanCmd.Flags().SortFlags = false
	CleanCmd.Flags().StringVarP(&configPath, "config", "c", "", "[required] Specify the config file for clean policy.")
	CleanCmd.MarkFlagRequired("config")
}
