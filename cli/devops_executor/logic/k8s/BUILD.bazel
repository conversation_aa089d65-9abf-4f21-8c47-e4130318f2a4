load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "k8s",
    srcs = [
        "api_resources.go",
        "cleaner.go",
        "utils.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/cli/devops_executor/logic/k8s",
    visibility = ["//visibility:public"],
    deps = [
        "//cli/devops_executor/config",
        "//cli/devops_executor/utils",
        "//cli/devops_executor/utils/fs",
        "//cli/devops_executor/utils/k8s",
        "@com_github_samber_lo//:lo",
        "@in_gopkg_yaml_v3//:yaml_v3",
        "@io_k8s_api//apps/v1:apps",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_apimachinery//pkg/version",
        "@io_k8s_client_go//kubernetes",
    ],
)

go_test(
    name = "k8s_test",
    srcs = [
        "api_resources_test.go",
        "utils_test.go",
    ],
    data = glob(["testdata/**"]),
    deps = [
        ":k8s",
        "//cli/devops_executor/utils/k8s",
        "@com_github_stretchr_testify//require",
        "@io_k8s_api//apps/v1:apps",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_apimachinery//pkg/version",
        "@io_k8s_client_go//discovery",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_client_go//kubernetes/fake",
    ],
)
