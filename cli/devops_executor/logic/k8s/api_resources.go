package k8s

import (
	"context"
	"fmt"
	"sort"

	yaml "gopkg.in/yaml.v3"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/version"
	"k8s.io/client-go/kubernetes"

	"github.com/MoeGolibrary/moego/cli/devops_executor/config"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils/fs"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils/k8s"
)

type APIResourcesParams struct {
	Cluster string
	Sort    bool
	Output  string
}

func ClusterAPIResources(_ context.Context, params APIResourcesParams) error {
	if utils.IsBlank(params.Output) {
		params.Output = "api-resources.yaml"
	}
	if config.GlobalFlags.Debug || true {
		fmt.Println("cluster:", params.Cluster)
		fmt.Println("sort:", params.Sort)
		fmt.Println("output:", params.Output)
	}

	// create client
	client, err := k8s.CreateClient(params.Cluster)
	if err != nil {
		fmt.Println("create k8s client failed: ", err)
		return err
	}

	// get cluster version info
	clusterInfo, err := client.Discovery().ServerVersion()
	if err != nil {
		return err
	}
	fmt.Printf("cluster version:\n%+v\n", clusterInfo)

	// get api groups
	apiGroups, err := client.Discovery().ServerGroups()
	if err != nil {
		return err
	}

	apiGroupList := newAPIGroupList(apiGroups)

	// add core group
	coreGroup := newCoreGroup()
	coreGroup.fillResources(client)
	apiGroupList.Groups = append(apiGroupList.Groups, *coreGroup)

	// add 'apps' group
	for _, group := range apiGroups.Groups {
		//nolint:goconst
		if group.Name == "apps" {
			apiGroupList.addGroup(client, &group)
		}
	}

	// add other group
	for _, group := range apiGroups.Groups {
		if group.Name != "apps" {
			apiGroupList.addGroup(client, &group)
		}
	}

	// sort
	if params.Sort {
		sort.Slice(apiGroupList.Groups, func(i, j int) bool {
			return apiGroupList.Groups[i].Name < apiGroupList.Groups[j].Name
		})
	}

	// make a summary
	summary := &ClusterSummary{
		ClusterInfo: clusterInfo,
		APIGroups:   apiGroupList,
	}

	yamlData, err := yaml.Marshal(summary)
	if err != nil {
		return err
	}

	text := string(yamlData)
	if config.GlobalFlags.DryRun {
		fmt.Println(text)
		return nil
	}

	// write to file
	_, err = fs.WriteText(params.Output, text)
	return err
}

//nolint:revive
type ClusterSummary struct {
	ClusterInfo *version.Info   `yaml:"clusterInfo"`
	APIVersions *v1.APIVersions `yaml:"apiVersions"`
	APIGroups   *ApiGroupList   `yaml:"apiGroups"`
}

//nolint:revive
type ApiGroupList struct {
	APIVersion string     `yaml:"apiVersion"`
	Kind       string     `yaml:"kind"`
	Groups     []ApiGroup `yaml:"groups"`
}

//nolint:revive
type ApiGroup struct {
	APIVersion       string                        `yaml:"apiVersion"`
	Kind             string                        `yaml:"kind"`
	Name             string                        `yaml:"name"`
	Versions         []v1.GroupVersionForDiscovery `yaml:"versions"`
	PreferredVersion v1.GroupVersionForDiscovery   `yaml:"preferredVersion,omitempty"`
	apis             map[string]v1.APIResourceList `yaml:"apis,omitempty"`
}

func newAPIGroupList(apiGroupList *v1.APIGroupList) *ApiGroupList {
	return &ApiGroupList{
		APIVersion: apiGroupList.APIVersion,
		Kind:       apiGroupList.Kind,
	}
}

func newAPIGroup(group *v1.APIGroup) *ApiGroup {
	return &ApiGroup{
		APIVersion:       group.APIVersion,
		Kind:             group.Kind,
		Name:             group.Name,
		Versions:         group.Versions,
		PreferredVersion: group.PreferredVersion,
		apis:             make(map[string]v1.APIResourceList),
	}
}

func newCoreGroup() *ApiGroup {
	groupVersion := v1.GroupVersionForDiscovery{
		GroupVersion: "v1",
		Version:      "v1",
	}

	return &ApiGroup{
		APIVersion:       "vi",
		Kind:             "APIGroup",
		Name:             "api/v1",
		Versions:         []v1.GroupVersionForDiscovery{groupVersion},
		PreferredVersion: groupVersion,
		apis:             make(map[string]v1.APIResourceList),
	}
}

func (groupList *ApiGroupList) addGroup(client kubernetes.Interface, group *v1.APIGroup) {
	item := newAPIGroup(group)
	item.fillResources(client)
	groupList.Groups = append(groupList.Groups, *item)
}

func (group *ApiGroup) fillResources(client kubernetes.Interface) {
	for _, version := range group.Versions {
		resources, err := client.Discovery().ServerResourcesForGroupVersion(version.GroupVersion)
		fmt.Println("get list of resources for ", version.GroupVersion)
		if err == nil {
			group.apis[version.GroupVersion] = *resources
		} else {
			fmt.Printf("failed to get resources for group %s: %v\n", version.GroupVersion, err)
		}
	}
}
