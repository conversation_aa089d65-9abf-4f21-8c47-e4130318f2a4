package k8s

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/samber/lo"
	appsv1 "k8s.io/api/apps/v1"
	"k8s.io/client-go/kubernetes"

	"github.com/MoeGolibrary/moego/cli/devops_executor/utils"
)

func CleanCluster(_ context.Context, config string) error {
	fmt.Println("config:", config)

	return errors.New("NOT support clean cluster")
}

func ListTargetDepoyment(client kubernetes.Interface,
	namespace string,
	prefixes []string) ([]appsv1.Deployment, error) {
	deployments, err := ListDeployments(client, namespace, []string{"app", "branch"})
	if err != nil {
		fmt.Printf("list deployment in %s failed: %v", namespace, err)
		return nil, err
	}
	fmt.Printf("list deployments in %s: %d", namespace, len(deployments.Items))

	var predicate = func(deployment appsv1.Deployment, _ int) bool {
		return IsTargetDeployment(&deployment, prefixes)
	}
	return lo.Filter(deployments.Items, predicate), nil
}

func IsTargetDeployment(deployment *appsv1.Deployment, prefixes []string) bool {
	var name = deployment.Name
	var branch, hasBranch = deployment.Labels["branch"]
	var repo, hasRepo = deployment.Spec.Template.Labels["repo-name"]
	fmt.Printf("name: %s, repo: %s, branch: %s", name, repo, branch)
	if strings.HasPrefix(name, "moego") && hasRepo && !utils.IsBlank(repo) && hasBranch && !utils.IsBlank(branch) {
		for _, prefix := range prefixes {
			if strings.HasPrefix(branch, prefix) {
				return true
			}
		}
	}

	return false
}
