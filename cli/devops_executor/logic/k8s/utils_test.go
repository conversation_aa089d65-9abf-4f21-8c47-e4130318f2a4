package k8s_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	appsv1 "k8s.io/api/apps/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/fake"

	"github.com/MoeGolibrary/moego/cli/devops_executor/logic/k8s"
)

func TestListDeployments(t *testing.T) {
	// fake client
	client := fake.NewSimpleClientset()

	namespace := "ns-testing"
	deployments := []*appsv1.Deployment{
		{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "moego-server-business",
				Namespace: namespace,
				Labels:    map[string]string{"app": "moego-server-business", "branch": "main"},
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "moego-server-grooming",
				Namespace: namespace,
				Labels:    map[string]string{"app": "moego-server-grooming", "branch": "feature-test"},
			},
		},
	}

	// add deployments to fake client
	for _, dep := range deployments {
		_, err := client.AppsV1().Deployments(namespace).Create(context.TODO(), dep, metav1.CreateOptions{})
		if err != nil {
			t.Fatalf("add deployments failed: %v", err)
		}
	}

	tests := []struct {
		name      string
		namespace string
		labels    []string
		wantCount int
		wantErr   bool
	}{
		{
			name:      "get all of deployments",
			namespace: namespace,
			labels:    nil,
			wantCount: 2,
			wantErr:   false,
		},
		{
			name:      "filter single label",
			namespace: namespace,
			labels:    []string{"app=moego-server-business"},
			wantCount: 1,
			wantErr:   false,
		},
		{
			name:      "filter multiple labels",
			namespace: namespace,
			labels:    []string{"app=moego-server-business", "branch=main"},
			wantCount: 1,
			wantErr:   false,
		},
		{
			name:      "non-existent namespace",
			namespace: "non-existent-ns",
			labels:    nil,
			wantCount: 0,
			wantErr:   false,
		},
		{
			name:      "non-existent labels",
			namespace: namespace,
			labels:    []string{"app=non-existent"},
			wantCount: 0,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := k8s.ListDeployments(client, tt.namespace, tt.labels)
			require.Equal(t, tt.wantErr, (err != nil))
			require.Equal(t, tt.wantCount, len(got.Items))
		})
	}
}
