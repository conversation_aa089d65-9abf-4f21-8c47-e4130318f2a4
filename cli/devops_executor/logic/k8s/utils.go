package k8s

import (
	"context"
	"strings"

	appsv1 "k8s.io/api/apps/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

func ListDeployments(client kubernetes.Interface, namespace string, lables []string) (*appsv1.DeploymentList, error) {
	var opts = metav1.ListOptions{}
	if lables != nil && 0 < len(lables) {
		opts.LabelSelector = strings.Join(lables, ",")
	}
	return client.AppsV1().Deployments(namespace).List(context.TODO(), opts)
}
