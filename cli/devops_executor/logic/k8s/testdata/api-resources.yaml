clusterInfo:
  major: "1"
  minor: "32"
  gitversion: v1.32.2-eks-bc803b4
  gitcommit: ba544f1e7adc98f5a0a09cd98bf2c091572a701c
  gittreestate: clean
  builddate: "2025-02-17T20:41:12Z"
  goversion: go1.23.6
  compiler: gc
  platform: linux/amd64
apiVersions: null
apiGroups:
  apiVersion: ""
  kind: ""
  groups:
    - apiVersion: vi
      kind: APIGroup
      name: api/v1
      versions:
        - groupversion: v1
          version: v1
      preferredVersion:
        groupversion: v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: apps
      versions:
        - groupversion: apps/v1
          version: v1
      preferredVersion:
        groupversion: apps/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: ""
      versions:
        - groupversion: v1
          version: v1
      preferredVersion:
        groupversion: v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: apiregistration.k8s.io
      versions:
        - groupversion: apiregistration.k8s.io/v1
          version: v1
      preferredVersion:
        groupversion: apiregistration.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: events.k8s.io
      versions:
        - groupversion: events.k8s.io/v1
          version: v1
      preferredVersion:
        groupversion: events.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: authentication.k8s.io
      versions:
        - groupversion: authentication.k8s.io/v1
          version: v1
      preferredVersion:
        groupversion: authentication.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: authorization.k8s.io
      versions:
        - groupversion: authorization.k8s.io/v1
          version: v1
      preferredVersion:
        groupversion: authorization.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: autoscaling
      versions:
        - groupversion: autoscaling/v2
          version: v2
        - groupversion: autoscaling/v1
          version: v1
      preferredVersion:
        groupversion: autoscaling/v2
        version: v2
    - apiVersion: ""
      kind: ""
      name: batch
      versions:
        - groupversion: batch/v1
          version: v1
      preferredVersion:
        groupversion: batch/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: certificates.k8s.io
      versions:
        - groupversion: certificates.k8s.io/v1
          version: v1
      preferredVersion:
        groupversion: certificates.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: networking.k8s.io
      versions:
        - groupversion: networking.k8s.io/v1
          version: v1
      preferredVersion:
        groupversion: networking.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: policy
      versions:
        - groupversion: policy/v1
          version: v1
      preferredVersion:
        groupversion: policy/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: rbac.authorization.k8s.io
      versions:
        - groupversion: rbac.authorization.k8s.io/v1
          version: v1
      preferredVersion:
        groupversion: rbac.authorization.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: storage.k8s.io
      versions:
        - groupversion: storage.k8s.io/v1
          version: v1
        - groupversion: storage.k8s.io/v1beta1
          version: v1beta1
      preferredVersion:
        groupversion: storage.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: admissionregistration.k8s.io
      versions:
        - groupversion: admissionregistration.k8s.io/v1
          version: v1
      preferredVersion:
        groupversion: admissionregistration.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: apiextensions.k8s.io
      versions:
        - groupversion: apiextensions.k8s.io/v1
          version: v1
      preferredVersion:
        groupversion: apiextensions.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: scheduling.k8s.io
      versions:
        - groupversion: scheduling.k8s.io/v1
          version: v1
      preferredVersion:
        groupversion: scheduling.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: coordination.k8s.io
      versions:
        - groupversion: coordination.k8s.io/v1
          version: v1
      preferredVersion:
        groupversion: coordination.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: node.k8s.io
      versions:
        - groupversion: node.k8s.io/v1
          version: v1
      preferredVersion:
        groupversion: node.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: discovery.k8s.io
      versions:
        - groupversion: discovery.k8s.io/v1
          version: v1
      preferredVersion:
        groupversion: discovery.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: flowcontrol.apiserver.k8s.io
      versions:
        - groupversion: flowcontrol.apiserver.k8s.io/v1
          version: v1
      preferredVersion:
        groupversion: flowcontrol.apiserver.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: metrics.eks.amazonaws.com
      versions:
        - groupversion: metrics.eks.amazonaws.com/v1
          version: v1
      preferredVersion:
        groupversion: metrics.eks.amazonaws.com/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: acme.cert-manager.io
      versions:
        - groupversion: acme.cert-manager.io/v1
          version: v1
      preferredVersion:
        groupversion: acme.cert-manager.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: agent.k8s.elastic.co
      versions:
        - groupversion: agent.k8s.elastic.co/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: agent.k8s.elastic.co/v1alpha1
        version: v1alpha1
    - apiVersion: ""
      kind: ""
      name: apm.k8s.elastic.co
      versions:
        - groupversion: apm.k8s.elastic.co/v1
          version: v1
        - groupversion: apm.k8s.elastic.co/v1beta1
          version: v1beta1
      preferredVersion:
        groupversion: apm.k8s.elastic.co/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: argoproj.io
      versions:
        - groupversion: argoproj.io/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: argoproj.io/v1alpha1
        version: v1alpha1
    - apiVersion: ""
      kind: ""
      name: autoscaling.k8s.elastic.co
      versions:
        - groupversion: autoscaling.k8s.elastic.co/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: autoscaling.k8s.elastic.co/v1alpha1
        version: v1alpha1
    - apiVersion: ""
      kind: ""
      name: beat.k8s.elastic.co
      versions:
        - groupversion: beat.k8s.elastic.co/v1beta1
          version: v1beta1
      preferredVersion:
        groupversion: beat.k8s.elastic.co/v1beta1
        version: v1beta1
    - apiVersion: ""
      kind: ""
      name: cert-manager.io
      versions:
        - groupversion: cert-manager.io/v1
          version: v1
      preferredVersion:
        groupversion: cert-manager.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: clickhouse.altinity.com
      versions:
        - groupversion: clickhouse.altinity.com/v1
          version: v1
      preferredVersion:
        groupversion: clickhouse.altinity.com/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: crd.k8s.amazonaws.com
      versions:
        - groupversion: crd.k8s.amazonaws.com/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: crd.k8s.amazonaws.com/v1alpha1
        version: v1alpha1
    - apiVersion: ""
      kind: ""
      name: datadoghq.com
      versions:
        - groupversion: datadoghq.com/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: datadoghq.com/v1alpha1
        version: v1alpha1
    - apiVersion: ""
      kind: ""
      name: eks.amazonaws.com
      versions:
        - groupversion: eks.amazonaws.com/v1
          version: v1
        - groupversion: eks.amazonaws.com/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: eks.amazonaws.com/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: elasticsearch.k8s.elastic.co
      versions:
        - groupversion: elasticsearch.k8s.elastic.co/v1
          version: v1
        - groupversion: elasticsearch.k8s.elastic.co/v1beta1
          version: v1beta1
      preferredVersion:
        groupversion: elasticsearch.k8s.elastic.co/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: elbv2.k8s.aws
      versions:
        - groupversion: elbv2.k8s.aws/v1beta1
          version: v1beta1
        - groupversion: elbv2.k8s.aws/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: elbv2.k8s.aws/v1beta1
        version: v1beta1
    - apiVersion: ""
      kind: ""
      name: enterprisesearch.k8s.elastic.co
      versions:
        - groupversion: enterprisesearch.k8s.elastic.co/v1
          version: v1
        - groupversion: enterprisesearch.k8s.elastic.co/v1beta1
          version: v1beta1
      preferredVersion:
        groupversion: enterprisesearch.k8s.elastic.co/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: extensions.istio.io
      versions:
        - groupversion: extensions.istio.io/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: extensions.istio.io/v1alpha1
        version: v1alpha1
    - apiVersion: ""
      kind: ""
      name: external-secrets.io
      versions:
        - groupversion: external-secrets.io/v1beta1
          version: v1beta1
        - groupversion: external-secrets.io/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: external-secrets.io/v1beta1
        version: v1beta1
    - apiVersion: ""
      kind: ""
      name: flagger.app
      versions:
        - groupversion: flagger.app/v1beta1
          version: v1beta1
      preferredVersion:
        groupversion: flagger.app/v1beta1
        version: v1beta1
    - apiVersion: ""
      kind: ""
      name: generators.external-secrets.io
      versions:
        - groupversion: generators.external-secrets.io/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: generators.external-secrets.io/v1alpha1
        version: v1alpha1
    - apiVersion: ""
      kind: ""
      name: groupsnapshot.storage.k8s.io
      versions:
        - groupversion: groupsnapshot.storage.k8s.io/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: groupsnapshot.storage.k8s.io/v1alpha1
        version: v1alpha1
    - apiVersion: ""
      kind: ""
      name: helm.toolkit.fluxcd.io
      versions:
        - groupversion: helm.toolkit.fluxcd.io/v2
          version: v2
        - groupversion: helm.toolkit.fluxcd.io/v2beta2
          version: v2beta2
        - groupversion: helm.toolkit.fluxcd.io/v2beta1
          version: v2beta1
      preferredVersion:
        groupversion: helm.toolkit.fluxcd.io/v2
        version: v2
    - apiVersion: ""
      kind: ""
      name: image.toolkit.fluxcd.io
      versions:
        - groupversion: image.toolkit.fluxcd.io/v1beta2
          version: v1beta2
        - groupversion: image.toolkit.fluxcd.io/v1beta1
          version: v1beta1
      preferredVersion:
        groupversion: image.toolkit.fluxcd.io/v1beta2
        version: v1beta2
    - apiVersion: ""
      kind: ""
      name: install.istio.io
      versions:
        - groupversion: install.istio.io/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: install.istio.io/v1alpha1
        version: v1alpha1
    - apiVersion: ""
      kind: ""
      name: karpenter.sh
      versions:
        - groupversion: karpenter.sh/v1
          version: v1
      preferredVersion:
        groupversion: karpenter.sh/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: kibana.k8s.elastic.co
      versions:
        - groupversion: kibana.k8s.elastic.co/v1
          version: v1
        - groupversion: kibana.k8s.elastic.co/v1beta1
          version: v1beta1
      preferredVersion:
        groupversion: kibana.k8s.elastic.co/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: kustomize.toolkit.fluxcd.io
      versions:
        - groupversion: kustomize.toolkit.fluxcd.io/v1
          version: v1
        - groupversion: kustomize.toolkit.fluxcd.io/v1beta2
          version: v1beta2
        - groupversion: kustomize.toolkit.fluxcd.io/v1beta1
          version: v1beta1
      preferredVersion:
        groupversion: kustomize.toolkit.fluxcd.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: logstash.k8s.elastic.co
      versions:
        - groupversion: logstash.k8s.elastic.co/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: logstash.k8s.elastic.co/v1alpha1
        version: v1alpha1
    - apiVersion: ""
      kind: ""
      name: maps.k8s.elastic.co
      versions:
        - groupversion: maps.k8s.elastic.co/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: maps.k8s.elastic.co/v1alpha1
        version: v1alpha1
    - apiVersion: ""
      kind: ""
      name: nacos.io
      versions:
        - groupversion: nacos.io/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: nacos.io/v1alpha1
        version: v1alpha1
    - apiVersion: ""
      kind: ""
      name: networking.istio.io
      versions:
        - groupversion: networking.istio.io/v1
          version: v1
        - groupversion: networking.istio.io/v1beta1
          version: v1beta1
        - groupversion: networking.istio.io/v1alpha3
          version: v1alpha3
      preferredVersion:
        groupversion: networking.istio.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: networking.k8s.aws
      versions:
        - groupversion: networking.k8s.aws/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: networking.k8s.aws/v1alpha1
        version: v1alpha1
    - apiVersion: ""
      kind: ""
      name: notification.toolkit.fluxcd.io
      versions:
        - groupversion: notification.toolkit.fluxcd.io/v1
          version: v1
        - groupversion: notification.toolkit.fluxcd.io/v1beta3
          version: v1beta3
        - groupversion: notification.toolkit.fluxcd.io/v1beta2
          version: v1beta2
        - groupversion: notification.toolkit.fluxcd.io/v1beta1
          version: v1beta1
      preferredVersion:
        groupversion: notification.toolkit.fluxcd.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: security.istio.io
      versions:
        - groupversion: security.istio.io/v1
          version: v1
        - groupversion: security.istio.io/v1beta1
          version: v1beta1
      preferredVersion:
        groupversion: security.istio.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: snapshot.storage.k8s.io
      versions:
        - groupversion: snapshot.storage.k8s.io/v1
          version: v1
      preferredVersion:
        groupversion: snapshot.storage.k8s.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: source.toolkit.fluxcd.io
      versions:
        - groupversion: source.toolkit.fluxcd.io/v1
          version: v1
        - groupversion: source.toolkit.fluxcd.io/v1beta2
          version: v1beta2
        - groupversion: source.toolkit.fluxcd.io/v1beta1
          version: v1beta1
      preferredVersion:
        groupversion: source.toolkit.fluxcd.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: stackconfigpolicy.k8s.elastic.co
      versions:
        - groupversion: stackconfigpolicy.k8s.elastic.co/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: stackconfigpolicy.k8s.elastic.co/v1alpha1
        version: v1alpha1
    - apiVersion: ""
      kind: ""
      name: telemetry.istio.io
      versions:
        - groupversion: telemetry.istio.io/v1
          version: v1
        - groupversion: telemetry.istio.io/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: telemetry.istio.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: velero.io
      versions:
        - groupversion: velero.io/v1
          version: v1
        - groupversion: velero.io/v2alpha1
          version: v2alpha1
      preferredVersion:
        groupversion: velero.io/v1
        version: v1
    - apiVersion: ""
      kind: ""
      name: vpcresources.k8s.aws
      versions:
        - groupversion: vpcresources.k8s.aws/v1beta1
          version: v1beta1
        - groupversion: vpcresources.k8s.aws/v1alpha1
          version: v1alpha1
      preferredVersion:
        groupversion: vpcresources.k8s.aws/v1beta1
        version: v1beta1
    - apiVersion: ""
      kind: ""
      name: external.metrics.k8s.io
      versions:
        - groupversion: external.metrics.k8s.io/v1beta1
          version: v1beta1
      preferredVersion:
        groupversion: external.metrics.k8s.io/v1beta1
        version: v1beta1
    - apiVersion: ""
      kind: ""
      name: metrics.k8s.io
      versions:
        - groupversion: metrics.k8s.io/v1beta1
          version: v1beta1
      preferredVersion:
        groupversion: metrics.k8s.io/v1beta1
        version: v1beta1
