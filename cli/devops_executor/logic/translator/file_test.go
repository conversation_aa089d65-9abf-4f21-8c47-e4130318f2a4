package translator_test

import (
	"context"
	"errors"
	"fmt"
	"os"
	"testing"

	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"github.com/stretchr/testify/require"
	"k8s.io/client-go/kubernetes"

	"github.com/MoeGolibrary/moego/cli/devops_executor/logic/translator"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils/aws"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils/fs"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils/k8s"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils/reader"
)

func TestTranslateFile(t *testing.T) {
	originalNewK8SClient := k8s.CreateClient
	originalNewAWSSecretsManagerClient := aws.CreateSecretsManagerClient
	originalNewAWSReader := reader.NewAWSSecretsReader
	originalNewK8SReader := reader.NewK8SSecretsReader
	reader.NewAWSSecretsReader = func(_ *secretsmanager.Client, name, prefix string, optional bool) reader.PairsReader {
		return NewMockReader(name, prefix, optional, readAWSSecrets)
	}
	reader.NewK8SSecretsReader = func(_ kubernetes.Interface, _, name, prefix string, optional bool) reader.PairsReader {
		return NewMockReader(name, prefix, optional, readK8SSecrets)
	}
	k8s.CreateClient = func(_ string) (kubernetes.Interface, error) {
		return nil, nil
	}
	aws.CreateSecretsManagerClient = func(_ context.Context, _, _, _, _ string) (*secretsmanager.Client, error) {
		return nil, nil
	}
	defer func() {
		reader.NewAWSSecretsReader = originalNewAWSReader
		reader.NewK8SSecretsReader = originalNewK8SReader
		k8s.CreateClient = originalNewK8SClient
		aws.CreateSecretsManagerClient = originalNewAWSSecretsManagerClient
	}()

	// normal
	resourcesDir := "testdata"
	targetFilename := "kustomization.yaml"
	expectedDir := resourcesDir + "/expected"
	outputDir := resourcesDir + "/output"
	outputPath := outputDir + "/" + targetFilename
	sourcePath := resourcesDir + "/" + targetFilename
	targetPath := expectedDir + "/" + targetFilename
	err := translator.TranslateFile(context.Background(), translator.TranslateFileParams{
		Source: sourcePath,
		Output: outputPath,
		Pairs: []string{
			"NAMESPACE=ns-testing",
		},
		Properties: []string{
			fmt.Sprintf("file=%s/properties.txt,prefix=ns.", resourcesDir),
			"file=non-existent,optional=true",
		},
		K8SSecrets: []string{
			"ns-testing/secret-test",
			"name=non-existent,optional=true",
		},
		AWSSecrets: []string{
			"moego/testing/secrets",
			"name=non-existent,optional=true",
		},
	})
	require.Nil(t, err)
	actual, _ := fs.ReadText(outputPath)
	expected, _ := fs.ReadText(targetPath)
	require.Equal(t, expected, actual)

	// source non-existent
	err = translator.TranslateFile(context.Background(), translator.TranslateFileParams{
		Source: "non-existent",
		Pairs: []string{
			"NAMESPACE=ns-testing",
		},
	})
	require.Equal(t, true, err != nil)

	// pair format error
	err = translator.TranslateFile(context.Background(), translator.TranslateFileParams{
		Source: sourcePath,
		Pairs: []string{
			"NAMESPACE:ns-testing",
		},
	})
	require.Equal(t, true, err != nil)

	// prpperties non-existent
	err = translator.TranslateFile(context.Background(), translator.TranslateFileParams{
		Source:     sourcePath,
		Properties: []string{"non-existent"},
	})
	require.Equal(t, true, err != nil)

	err = translator.TranslateFile(context.Background(), translator.TranslateFileParams{
		Source:     sourcePath,
		Properties: []string{"prefix=xxx,optional=false"},
	})
	require.Equal(t, true, err != nil)

	err = translator.TranslateFile(context.Background(), translator.TranslateFileParams{
		Source:     sourcePath,
		Properties: []string{""},
	})
	require.Equal(t, true, err != nil)

	// k8s secrets non-existent
	err = translator.TranslateFile(context.Background(), translator.TranslateFileParams{
		Source:     sourcePath,
		K8SSecrets: []string{"ns-testing/must-exists"},
	})
	require.Equal(t, true, err != nil)

	err = translator.TranslateFile(context.Background(), translator.TranslateFileParams{
		Source:     sourcePath,
		K8SSecrets: []string{"prefix=xxx,optional=false"},
	})
	require.Equal(t, true, err != nil)

	err = translator.TranslateFile(context.Background(), translator.TranslateFileParams{
		Source:     sourcePath,
		K8SSecrets: []string{""},
	})
	require.Equal(t, true, err != nil)

	// aws secrets non-existent
	err = translator.TranslateFile(context.Background(), translator.TranslateFileParams{
		Source:     sourcePath,
		AWSSecrets: []string{"must-exists"},
	})
	require.Equal(t, true, err != nil)

	err = translator.TranslateFile(context.Background(), translator.TranslateFileParams{
		Source:     sourcePath,
		AWSSecrets: []string{"prefix=xxx,optional=false"},
	})
	require.Equal(t, true, err != nil)

	err = translator.TranslateFile(context.Background(), translator.TranslateFileParams{
		Source:     sourcePath,
		AWSSecrets: []string{""},
	})
	require.Equal(t, true, err != nil)

	os.RemoveAll(outputDir)
}

type ReadFuncType func(context.Context, string, string, bool) (map[string]string, error)

type MockReader struct {
	Name     string
	Prefix   string
	Optional bool
	readFunc ReadFuncType
}

func NewMockReader(name string, prefix string, optional bool, proc ReadFuncType) reader.PairsReader {
	return &MockReader{Name: name, Prefix: prefix, Optional: optional, readFunc: proc}
}

func (reader *MockReader) Type() string {
	return "MockReader"
}

func (reader *MockReader) Read(ctx context.Context) (map[string]string, error) {
	return reader.readFunc(ctx, reader.Name, reader.Prefix, reader.Optional)
}

func readK8SSecrets(_ context.Context, name, prefix string, optional bool) (map[string]string, error) {
	if name == "non-existent" && optional {
		return nil, nil
	}
	if name == "must-exists" {
		return nil, errors.New("NOT found secrets")
	}
	pairs := map[string]string{
		"NAMESPACE":       "ns-testing",
		"RELEASE_VERSION": "production",
		"RELEASE_BRANCH":  "main",
	}

	if utils.IsBlank(prefix) {
		return pairs, nil
	}

	result := make(map[string]string)
	for k, v := range pairs {
		result[prefix+k] = v
	}
	return result, nil
}

func readAWSSecrets(_ context.Context, name, prefix string, optional bool) (map[string]string, error) {
	if name == "non-existent" && optional {
		return nil, nil
	}
	if name == "must-exists" {
		return nil, errors.New("NOT found secrets")
	}
	pairs := map[string]string{
		"RELEASE_RESOURCE_PATH": "src/moego-servers/moego-server-grooming/development/ns-testing",
		"RELEASE_IMAGE_NAME":    "moego-server-grooming",
		"RELEASE_IMAGE_TAG":     "main",
	}

	if utils.IsBlank(prefix) {
		return pairs, nil
	}

	result := make(map[string]string)
	for k, v := range pairs {
		result[prefix+k] = v
	}
	return result, nil
}
