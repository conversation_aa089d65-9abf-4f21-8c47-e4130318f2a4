package translator

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"sigs.k8s.io/kustomize/kyaml/yaml"

	"github.com/MoeGolibrary/moego/cli/devops_executor/config"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils/fs"
)

type TranslateKustomizationParams struct {
	File      string
	Output    string
	Cluster   string
	Namespace string
	Repo      string
	Branch    string
}

func TranslateKustomization(_ context.Context, params TranslateKustomizationParams) error {
	if utils.IsBlank(params.Output) {
		params.Output = params.File
	}
	if config.GlobalFlags.Debug {
		fmt.Println("file: ", params.File)
		fmt.Println("output: ", params.Output)
		fmt.Println("cluster: ", params.Cluster)
		fmt.Println("namespace: ", params.Namespace)
		fmt.Println("repo: ", params.Repo)
		fmt.Println("branch: ", params.Branch)
	}

	kustomization, err := yaml.ReadFile(params.File)
	if err != nil {
		return err
	}

	patches, err := kustomization.Pipe(yaml.Lookup("patches"))
	if err != nil {
		return err
	}
	if patches == nil {
		fmt.Println("NOT found patches in kustomization.yaml")
		return nil
	}

	elements, err := patches.Elements()
	if err != nil {
		return nil
	}
	for _, element := range elements {
		target := element.Field("target")
		if target != nil {
			selector := target.Value.Field("labelSelector")
			if selector != nil {
				label := selector.Value.YNode().Value
				fmt.Println("found label selector: ", label)
				selector.Value.YNode().Value = translateLabelSeletor(label, params.Namespace, params.Branch)
			}
		}
	}

	if !config.GlobalFlags.DryRun {
		text, err := kustomization.String()
		if err != nil {
			return err
		}
		_, err = fs.WriteText(params.Output, text)
		return err
	}

	return nil
}

//nolint:goconst
func translateLabelSeletor(label, namespace, branch string) string {
	if namespace == "ns-testing" {
		key := "branch"
		normalPrefix := key + "="
		notEqualPrefix := key + "!="
		if strings.HasPrefix(label, normalPrefix) {
			pattern := label[len(normalPrefix):]
			rex := regexp.MustCompile(pattern)
			if rex.MatchString(branch) {
				fmt.Printf("branch: %s is match: %s\n", branch, label)
				return key + "=" + branch
			}

			return "unavailable/key=uninvalid_value"
		}

		if strings.HasPrefix(label, notEqualPrefix) {
			pattern := label[len(notEqualPrefix):]
			rex := regexp.MustCompile(pattern)
			if !rex.MatchString(branch) {
				fmt.Printf("branch: %s is match: %s\n", branch, label)
				return key + "=" + branch
			}

			return "unavailable/key=uninvalid_value"
		}
	}

	return label
}
