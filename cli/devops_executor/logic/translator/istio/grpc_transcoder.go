package istio

import (
	"context"
	"fmt"
	"os"
	"strings"

	"github.com/samber/lo"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/descriptorpb"
	"sigs.k8s.io/kustomize/kyaml/yaml"

	"github.com/MoeGolibrary/moego/cli/devops_executor/config"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils"
)

type TranslateEnvoyFilterParams struct {
	Descriptor  string
	EnvoyFilter string
	Output      string
}

func TranslateEnvoyFilter(_ context.Context, params TranslateEnvoyFilterParams) error {
	if utils.IsBlank(params.Output) {
		params.Output = params.EnvoyFilter
	}
	if config.GlobalFlags.Debug {
		fmt.Println("descriptor:", params.Descriptor)
		fmt.Println("envoyfilter:", params.EnvoyFilter)
		fmt.Println("output:", params.Output)
	}

	fmt.Println("start to translate GRPC-Transcoder: ", params.EnvoyFilter)
	// take list of services from a file
	services, err := takeServices(params.Descriptor)
	if err != nil {
		return err
	}
	// load EnvoyFilter from a file
	ef, err := yaml.ReadFile(params.EnvoyFilter)
	if err != nil {
		return err
	}
	// get patches
	patches, err := ef.Pipe(yaml.Lookup("spec", "configPatches"))
	if err != nil || patches == nil {
		return nil
	}
	elements, err := patches.Elements()
	if err != nil {
		return err
	}

	changed := false
	for _, element := range elements {
		value, err := element.Pipe(yaml.Lookup("patch", "value"))
		if err != nil {
			return err
		}
		if !checkPatchValue(value) {
			continue
		}

		typedConfig := value.Field("typed_config")
		if typedConfig == nil {
			continue
		}

		nodes := typedConfig.Value.Field("services")
		if nodes == nil {
			continue
		}

		var scopes []string
		if err = nodes.Value.YNode().Decode(&scopes); err != nil {
			return err
		}

		items := filterGrpcServices(services, scopes)
		if len(items) > 0 {
			fmt.Printf("take services: %v with scopes: %v\n", len(items), scopes)
			err = nodes.Value.YNode().Encode(items)
			if err != nil {
				return err
			}
			changed = true
		} else {
			fmt.Println("NOT found any matched GRPC Service with scopes:", scopes)
		}
	}

	if changed {
		if !config.GlobalFlags.DryRun {
			return yaml.WriteFile(ef, params.Output)
		}
	} else {
		fmt.Println("EnvoyFilter no change")
	}

	return nil
}

func checkPatchValue(node *yaml.RNode) bool {
	if node == nil {
		return false
	}
	name := node.Field("name")
	if name == nil || name.IsNilOrEmpty() {
		return false
	}

	return name.Value.YNode().Value == "envoy.filters.http.grpc_json_transcoder"
}

// take GRPC services from a protobuf descriptor file
func takeServices(descriptorFile string) ([]string, error) {
	var services []string

	data, err := os.ReadFile(descriptorFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read descriptor file: %v", err)
	}

	var descriptorSet descriptorpb.FileDescriptorSet
	if err := proto.Unmarshal(data, &descriptorSet); err != nil {
		return nil, fmt.Errorf("failed to parse descriptor file: %v", err)
	}

	for _, file := range descriptorSet.GetFile() {
		pkg := file.GetPackage()
		for _, service := range file.GetService() {
			fullName := pkg + "." + service.GetName()
			services = append(services, fullName)
		}
	}

	return services, nil
}

// filter services by prefixes
func filterGrpcServices(services []string, prefixes []string) []string {
	if lo.Contains(prefixes, "*") {
		return services
	}

	var items []string
	for _, service := range services {
		for _, prefix := range prefixes {
			if strings.HasPrefix(service, prefix) {
				items = append(items, service)
				break
			}
		}
	}

	return items
}

/*
func translateServices(value map[string]any, services []string) (*structpb.Struct, bool) {
	name, ok := value["name"].(string)
	if !ok || name != "envoy.filters.http.grpc_json_transcoder" {
		return nil, false
	}
	typedConfig, ok := value["typed_config"].(map[string]interface{})
	if !ok {
		return nil, false
	}
	scopes, ok := typedConfig["services"].([]string)
	if !ok {
		return nil, false
	}
	if scopes == nil || len(scopes) == 0 {
		return nil, false
	}
	items := filterGrpcServices(services, scopes)
	if items == nil || len(items) == 0 {
		return nil, false
	}

	typedConfig["services"] = items
	value["typed_config"] = typedConfig
	s, err := structpb.NewStruct(value)
	if err != nil {
		return nil, false
	}

	return s, true
}

func loadEnvoyFilter(file string) (*v1alpha3.EnvoyFilter, error) {
	data, err := os.ReadFile(file)
	if err != nil {
		return nil, err
	}

	var ef v1alpha3.EnvoyFilter
	err = yaml.Unmarshal(data, &ef)
	if err != nil {
		return nil, err
	}

	return &ef, nil
}
*/
