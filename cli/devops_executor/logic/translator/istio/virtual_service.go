package istio

import (
	"context"
	"errors"
	"fmt"
)

type TranslateVirtualServiceParams struct {
	Hosts     string
	Templates string
	Output    string
}

func TranslateVirtualService(_ context.Context, params TranslateVirtualServiceParams) error {
	fmt.Println("hosts:", params.Hosts)
	fmt.Println("templates:", params.Templates)
	fmt.Println("output:", params.Output)

	return errors.New("NOT support translate VirtualService")
}
