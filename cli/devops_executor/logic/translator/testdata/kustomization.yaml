resources:
  - ${RELEASE_RESOURCE_PATH}

images:
  - name: app-image
    newName: ${RELEASE_IMAGE_NAME}
    newTag: ${RELEASE_IMAGE_TAG}

transformers:
  - prefix-suffix-transformer.yaml
  - label-transformer-version.yaml
  - label-transformer-repo.yaml
  - label-transformer-datadog.yaml

patches:
  - patch: |-
      - op: add
        path: /metadata/labels/branch
        value: ${RELEASE_BRANCH}
      - op: add
        path: /spec/selector/matchLabels/version
        value: ${RELEASE_VERSION}
    target:
      kind: Deployment
      namespace: ns-testing
  - patch: |-
      - op: replace
        path: /spec/template/spec/nodeSelector
        value:
          moego.branch/general: "true"
    target:
      kind: Deployment
      namespace: ns-testing
      labelSelector: 'branch!=^(main|staging|production|feature-demo.*)$'
  - patch: |-
      - op: add
        path: /spec/selector/version
        value: ${RELEASE_VERSION}
    target:
      kind: Service
      namespace: ns-testing
  - patch: |-
      - op: add
        path: /spec/workloadSelector/labels/version
        value: ${RELEASE_VERSION}
    target:
      kind: EnvoyFilter
      namespace: ns-testing
  - patch: |-
      - op: add
        path: /spec/selector/matchLabels/version
        value: ${RELEASE_VERSION}
    target:
      kind: PodDisruptionBudget
      namespace: ${ns.NAMESPACE}