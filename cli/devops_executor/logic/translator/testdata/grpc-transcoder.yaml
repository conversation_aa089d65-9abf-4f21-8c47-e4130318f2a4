apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: ef-transcoder-moego-admin-api-v3
spec:
  workloadSelector:
    labels:
      app: moego-admin-api-v3
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_INBOUND
        listener:
          filterChain:
            filter:
              name: 'envoy.filters.network.http_connection_manager'
              subFilter:
                name: 'envoy.filters.http.router'
      patch:
        operation: INSERT_BEFORE
        value:
          name: envoy.filters.http.grpc_json_transcoder
          typed_config:
            '@type': 'type.googleapis.com/envoy.extensions.filters.http.grpc_json_transcoder.v3.GrpcJsonTranscoder'
            services:
              - 'moego.api'
              - 'moego.admin'
            auto_mapping: true
            ignore_unknown_query_parameters: true
            convert_grpc_status: true
            print_options:
              add_whitespace: false
              always_print_primitive_fields: true
              always_print_enums_as_ints: true
              preserve_proto_field_names: false
            proto_descriptor: /var/lib/istio/data/protos/moego.bin
