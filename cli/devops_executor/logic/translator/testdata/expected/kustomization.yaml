resources:
  - src/moego-servers/moego-server-grooming/development/ns-testing

images:
  - name: app-image
    newName: moego-server-grooming
    newTag: main

transformers:
  - prefix-suffix-transformer.yaml
  - label-transformer-version.yaml
  - label-transformer-repo.yaml
  - label-transformer-datadog.yaml

patches:
  - patch: |-
      - op: add
        path: /metadata/labels/branch
        value: main
      - op: add
        path: /spec/selector/matchLabels/version
        value: production
    target:
      kind: Deployment
      namespace: ns-testing
  - patch: |-
      - op: replace
        path: /spec/template/spec/nodeSelector
        value:
          moego.branch/general: "true"
    target:
      kind: Deployment
      namespace: ns-testing
      labelSelector: 'branch!=^(main|staging|production|feature-demo.*)$'
  - patch: |-
      - op: add
        path: /spec/selector/version
        value: production
    target:
      kind: Service
      namespace: ns-testing
  - patch: |-
      - op: add
        path: /spec/workloadSelector/labels/version
        value: production
    target:
      kind: EnvoyFilter
      namespace: ns-testing
  - patch: |-
      - op: add
        path: /spec/selector/matchLabels/version
        value: production
    target:
      kind: PodDisruptionBudget
      namespace: ns-from-properties