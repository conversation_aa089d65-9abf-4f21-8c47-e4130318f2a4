package translator

import (
	"context"
	"errors"
	"fmt"
	iofs "io/fs"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"github.com/samber/lo"
	"k8s.io/client-go/kubernetes"

	"github.com/MoeGolibrary/moego/cli/devops_executor/config"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils/aws"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils/fs"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils/k8s"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils/reader"
)

type TranslateFileParams struct {
	Source     string
	Output     string
	Pairs      []string
	Properties []string
	K8SSecrets []string
	AWSSecrets []string
}

func TranslateFile(ctx context.Context, params TranslateFileParams) error {
	if utils.IsBlank(params.Output) {
		params.Output = params.Source
	}
	if config.GlobalFlags.Debug {
		fmt.Println("source:", params.Source)
		fmt.Println("output:", params.Output)
		fmt.Println("pairs:", params.Pairs)
		fmt.Println("properties:", params.Properties)
		fmt.Println("k8sSecrets:", params.K8SSecrets)
		fmt.Println("awsSecrets:", params.AWSSecrets)
	}

	// build readers
	awsReaders, err := buildAWSSecretsReaders(ctx, params.AWSSecrets)
	if err != nil {
		return err
	}
	k8sReaders, err := buildK8SSecretsReaders(ctx, params.K8SSecrets)
	if err != nil {
		return err
	}
	propertiesReaders, err := buildPropertiesReaders(ctx, params.Properties)
	if err != nil {
		return err
	}
	readers := append(awsReaders, k8sReaders...)
	readers = append(readers, propertiesReaders...)

	// read to dictionary
	dictionary := make(map[string]string)
	if readers != nil && 0 < len(readers) {
		for _, r := range readers {
			secrets, err := r.Read(ctx)
			if err != nil {
				return err
			}
			utils.MergeMap(dictionary, secrets)
		}
	}

	// parse to dictionary
	for _, pair := range params.Pairs {
		k, v, err := utils.ParsePair(pair)
		if err != nil {
			return err
		}
		dictionary[k] = v
	}
	if config.GlobalFlags.Debug {
		for k, v := range dictionary {
			fmt.Println(k + ": " + utils.MaskText(v))
		}
	}

	// checkout source path
	if exists, err := fs.IsExists(params.Source); !exists || err != nil {
		return err
	}

	// walk the source path and translate each file
	return filepath.Walk(params.Source, func(path string, info iofs.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() {
			subpath, err := filepath.Rel(params.Source, path)
			if err != nil {
				return err
			}

			output := filepath.Join(params.Output, subpath)
			count, err := translateAndWrite(path, output, dictionary)
			if err != nil {
				return err
			}
			fmt.Printf("translate file: %s, count: %d, output: %s\n", path, count, output)
		}

		return nil
	})
}

func translateAndWrite(source string, output string, dictionary map[string]string) (int, error) {
	// read content from source file
	content, err := fs.ReadText(source)
	if err != nil {
		return 0, err
	}

	// replace content
	text, count := utils.Replace(content, dictionary)
	if !config.GlobalFlags.DryRun {
		// write to file
		_, err = fs.WriteText(output, text)
		if err != nil {
			return 0, err
		}
	}

	return count, nil
}

func buildPropertiesReaders(_ context.Context, files []string) ([]reader.PairsReader, error) {
	readers := make([]reader.PairsReader, len(files))
	if files != nil && 0 < len(files) {
		for i, item := range files {
			reader, err := buildPropertiesReader(item)
			if err != nil {
				return readers, err
			}
			readers[i] = reader
		}
	}

	return readers, nil
}

func buildK8SSecretsReaders(_ context.Context, k8sSecrets []string) ([]reader.PairsReader, error) {
	readers := make([]reader.PairsReader, len(k8sSecrets))
	if k8sSecrets != nil && 0 < len(k8sSecrets) {
		client, err := k8s.CreateClient("")
		if err != nil {
			return readers, err
		}
		for i, item := range k8sSecrets {
			reader, err := buildK8SSecretsReader(client, item)
			if err != nil {
				return readers, err
			}
			readers[i] = reader
		}
	}

	return readers, nil
}

func buildAWSSecretsReaders(ctx context.Context, awsSecrets []string) ([]reader.PairsReader, error) {
	readers := make([]reader.PairsReader, len(awsSecrets))
	if awsSecrets != nil && 0 < len(awsSecrets) {
		client, err := aws.CreateSecretsManagerClient(ctx, "", "", "", "")
		if err != nil {
			return readers, err
		}
		for i, item := range awsSecrets {
			reader, err := buildAWSSecretsReader(client, item)
			if err != nil {
				return readers, err
			}
			readers[i] = reader
		}
	}

	return readers, nil
}

func buildPropertiesReader(line string) (reader.PairsReader, error) {
	if utils.IsBlank(line) {
		return nil, errors.New("string is empty")
	}
	if !strings.Contains(line, ",") && !strings.Contains(line, "=") {
		return reader.NewDefaultPairsReader(strings.TrimSpace(line), "", false), nil
	}

	keys := map[string]bool{
		"file":     true,
		"prefix":   false,
		"optional": false,
	}
	pairs, err := utils.ParseLineToMap(line, ",", keys)
	if err != nil {
		return nil, err
	}

	optional, _ := strconv.ParseBool(lo.ValueOr(pairs, "optional", "false"))
	return reader.NewDefaultPairsReader(pairs["file"], pairs["prefix"], optional), nil
}

func buildK8SSecretsReader(client kubernetes.Interface, line string) (reader.PairsReader, error) {
	if utils.IsBlank(line) {
		return nil, errors.New("string is empty")
	}
	if !strings.Contains(line, ",") && !strings.Contains(line, "=") {
		namespace, name := parseNamespaceAndName(strings.TrimSpace(line))
		return reader.NewK8SSecretsReader(client, namespace, name, "", false), nil
	}

	keys := map[string]bool{
		"name":     true,
		"prefix":   false,
		"optional": false,
	}
	pairs, err := utils.ParseLineToMap(line, ",", keys)
	if err != nil {
		return nil, err
	}

	namespace, name := parseNamespaceAndName(pairs["name"])
	optional, _ := strconv.ParseBool(lo.ValueOr(pairs, "optional", "false"))
	return reader.NewK8SSecretsReader(client, namespace, name, pairs["prefix"], optional), nil
}

func buildAWSSecretsReader(client *secretsmanager.Client, line string) (reader.PairsReader, error) {
	if utils.IsBlank(line) {
		return nil, errors.New("string is empty")
	}
	if !strings.Contains(line, ",") && !strings.Contains(line, "=") {
		return reader.NewAWSSecretsReader(client, strings.TrimSpace(line), "", false), nil
	}

	keys := map[string]bool{
		"name":     true,
		"prefix":   false,
		"optional": false,
	}
	pairs, err := utils.ParseLineToMap(line, ",", keys)
	if err != nil {
		return nil, err
	}

	optional, _ := strconv.ParseBool(lo.ValueOr(pairs, "optional", "false"))
	return reader.NewAWSSecretsReader(client, pairs["name"], pairs["prefix"], optional), nil
}

func parseNamespaceAndName(part string) (string, string) {
	pairs := strings.SplitN(part, "/", 2)
	if 1 < len(pairs) {
		return pairs[0], pairs[1]
	}

	return "default", pairs[0]
}
