plugins {
  id 'org.springframework.boot'
}

apply from: "https://moego.s3.us-west-2.amazonaws.com/ops/sbc/${System.getProperty('sbc.scriptBranch', '')}SourceBranchControl.gradle"
applySourceBranchControlDependencies()

dependencies {
  implementation project(':moego-server-api')
  implementation 'org.springframework.boot:spring-boot-starter-web'
  implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.6.0'
}

tasks.register('genServers') {
  exec {
    commandLine 'sh', '-e', 'gen_servers.sh'
  }
}

classes.dependsOn genServers
