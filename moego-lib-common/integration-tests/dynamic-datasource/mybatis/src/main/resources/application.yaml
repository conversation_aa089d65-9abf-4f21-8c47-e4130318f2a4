spring:
  application:
    name: dynamic-datasource-mybatis-integration-tests
  datasource:
    name: main # Always use spring.datasource as the main DataSource
    driver-class-name: org.postgresql.Driver
    url: ******************************************************************
    username: moego_developer_240310_eff7a0dc
    password: G0MxI7NM_jX_f7Ky73vnrwej97xg1tly

moego:
  data-sources: # Additional DataSources
    - name: ds1
      driver-class-name: org.postgresql.Driver
      url: ******************************************************************_unit_test
      username: moego_developer_240310_eff7a0dc
      password: G0MxI7NM_jX_f7Ky73vnrwej97xg1tly
    - name: ds2
      driver-class-name: org.postgresql.Driver
      url: ******************************************************************_unit_test
      username: moego_developer_240310_eff7a0dc
      password: G0MxI7NM_jX_f7Ky73vnrwej97xg1tly
    - name: mysql
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ***************************************************************
      username: moego_developer_240310_eff7a0dc
      password: G0MxI7NM_jX_f7Ky73vnrwej97xg1tly

logging:
  level:
    example.mapper: debug
