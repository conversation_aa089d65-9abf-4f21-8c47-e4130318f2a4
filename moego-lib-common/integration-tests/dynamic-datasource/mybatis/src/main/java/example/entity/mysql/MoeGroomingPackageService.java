package example.entity.mysql;

import example.entity.po.PackageService;
import jakarta.annotation.Generated;
import java.util.List;

/**
 * Database Table Remarks:
 *   customer 打包用户信息
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_package_service
 */
public class MoeGroomingPackageService {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.id")
    private Integer id;

    /**
     * Database Column Remarks:
     *   打包id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.package_id")
    private Integer packageId;

    /**
     * Database Column Remarks:
     *   打包服务总数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.total_quantity")
    private Integer totalQuantity;

    /**
     * Database Column Remarks:
     *   打包服务剩余数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.remaining_quantity")
    private Integer remainingQuantity;

    /**
     * Database Column Remarks:
     *   package item 支持选择多个 service，多个 service 之间共享 quantity，单个 service 结构参考：com.moego.server.retail.dto.PackageInfoDto.Service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.services")
    private List<PackageService> services;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.id")
    public Integer getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.id")
    public void setId(Integer id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.package_id")
    public Integer getPackageId() {
        return packageId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.package_id")
    public void setPackageId(Integer packageId) {
        this.packageId = packageId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.total_quantity")
    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.total_quantity")
    public void setTotalQuantity(Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.remaining_quantity")
    public Integer getRemainingQuantity() {
        return remainingQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.remaining_quantity")
    public void setRemainingQuantity(Integer remainingQuantity) {
        this.remainingQuantity = remainingQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.services")
    public List<PackageService> getServices() {
        return services;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.services")
    public void setServices(List<PackageService> services) {
        this.services = services;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", packageId=").append(packageId);
        sb.append(", totalQuantity=").append(totalQuantity);
        sb.append(", remainingQuantity=").append(remainingQuantity);
        sb.append(", services=").append(services);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MoeGroomingPackageService other = (MoeGroomingPackageService) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getPackageId() == null ? other.getPackageId() == null : this.getPackageId().equals(other.getPackageId()))
            && (this.getTotalQuantity() == null ? other.getTotalQuantity() == null : this.getTotalQuantity().equals(other.getTotalQuantity()))
            && (this.getRemainingQuantity() == null ? other.getRemainingQuantity() == null : this.getRemainingQuantity().equals(other.getRemainingQuantity()))
            && (this.getServices() == null ? other.getServices() == null : this.getServices().equals(other.getServices()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPackageId() == null) ? 0 : getPackageId().hashCode());
        result = prime * result + ((getTotalQuantity() == null) ? 0 : getTotalQuantity().hashCode());
        result = prime * result + ((getRemainingQuantity() == null) ? 0 : getRemainingQuantity().hashCode());
        result = prime * result + ((getServices() == null) ? 0 : getServices().hashCode());
        return result;
    }
}