package example.mapper.postgres;

import example.models.BookingRequestModel.Attr;
import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class BookingRequestDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    public static final BookingRequest bookingRequest = new BookingRequest();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.id")
    public static final SqlColumn<Long> id = bookingRequest.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.company_id")
    public static final SqlColumn<Long> companyId = bookingRequest.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.business_id")
    public static final SqlColumn<Long> businessId = bookingRequest.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.customer_id")
    public static final SqlColumn<Long> customerId = bookingRequest.customerId;

    /**
     * Database Column Remarks:
     *   The appointment id generated after the business schedule
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.appointment_id")
    public static final SqlColumn<Long> appointmentId = bookingRequest.appointmentId;

    /**
     * Database Column Remarks:
     *   The start date of the booking request, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.start_date")
    public static final SqlColumn<LocalDate> startDate = bookingRequest.startDate;

    /**
     * Database Column Remarks:
     *   The start time of the booking request, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.start_time")
    public static final SqlColumn<Integer> startTime = bookingRequest.startTime;

    /**
     * Database Column Remarks:
     *   The end date of the booking request, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.end_date")
    public static final SqlColumn<LocalDate> endDate = bookingRequest.endDate;

    /**
     * Database Column Remarks:
     *   The end time of the booking request, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.end_time")
    public static final SqlColumn<Integer> endTime = bookingRequest.endTime;

    /**
     * Database Column Remarks:
     *   1: SUBMITTED, 2: WAIT_LIST, 3: SCHEDULED, 4: DECLINED, 5: DELETED
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.status")
    public static final SqlColumn<Integer> status = bookingRequest.status;

    /**
     * Database Column Remarks:
     *   Whether the booking request is prepaid
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.is_prepaid")
    public static final SqlColumn<Boolean> isPrepaid = bookingRequest.isPrepaid;

    /**
     * Database Column Remarks:
     *   Additional note for the booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.additional_note")
    public static final SqlColumn<String> additionalNote = bookingRequest.additionalNote;

    /**
     * Database Column Remarks:
     *   The platform where the booking request is submitted, RESERVE_WITH_GOOGLE, PET_PARENT_APP
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.source_platform")
    public static final SqlColumn<String> sourcePlatform = bookingRequest.sourcePlatform;

    /**
     * Database Column Remarks:
     *   The bitmap value for service, 0b1: grooming, 0b10: boarding, 0b100: daycare, 0b1000: evaluation test
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.service_type_include")
    public static final SqlColumn<Integer> serviceTypeInclude = bookingRequest.serviceTypeInclude;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = bookingRequest.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = bookingRequest.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.deleted_at")
    public static final SqlColumn<LocalDateTime> deletedAt = bookingRequest.deletedAt;

    /**
     * Database Column Remarks:
     *   存储一些通用信息，结构为 BookingRequestModel.Attr
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.attr")
    public static final SqlColumn<Attr> attr = bookingRequest.attr;

    /**
     * Database Column Remarks:
     *   payment status, see BookingRequestModel.PaymentStatus
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.payment_status")
    public static final SqlColumn<Integer> paymentStatus = bookingRequest.paymentStatus;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    public static final class BookingRequest extends AliasableSqlTable<BookingRequest> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<Long> customerId = column("customer_id", JDBCType.BIGINT);

        public final SqlColumn<Long> appointmentId = column("appointment_id", JDBCType.BIGINT);

        public final SqlColumn<LocalDate> startDate = column("start_date", JDBCType.DATE);

        public final SqlColumn<Integer> startTime = column("start_time", JDBCType.INTEGER);

        public final SqlColumn<LocalDate> endDate = column("end_date", JDBCType.DATE);

        public final SqlColumn<Integer> endTime = column("end_time", JDBCType.INTEGER);

        public final SqlColumn<Integer> status = column("\"status\"", JDBCType.INTEGER);

        public final SqlColumn<Boolean> isPrepaid = column("is_prepaid", JDBCType.BIT);

        public final SqlColumn<String> additionalNote = column("additional_note", JDBCType.VARCHAR);

        public final SqlColumn<String> sourcePlatform = column("source_platform", JDBCType.VARCHAR);

        public final SqlColumn<Integer> serviceTypeInclude = column("service_type_include", JDBCType.INTEGER);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Attr> attr = column("attr", JDBCType.OTHER, "example.mapper.typehandler.BookingRequestAttrTypeHandler");

        public final SqlColumn<Integer> paymentStatus = column("payment_status", JDBCType.SMALLINT);

        public BookingRequest() {
            super("booking_request", BookingRequest::new);
        }
    }
}