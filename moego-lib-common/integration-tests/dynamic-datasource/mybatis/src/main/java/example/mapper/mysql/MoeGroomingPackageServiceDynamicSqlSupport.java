package example.mapper.mysql;

import example.entity.po.PackageService;
import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.List;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MoeGroomingPackageServiceDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    public static final MoeGroomingPackageService moeGroomingPackageService = new MoeGroomingPackageService();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.id")
    public static final SqlColumn<Integer> id = moeGroomingPackageService.id;

    /**
     * Database Column Remarks:
     *   打包id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.package_id")
    public static final SqlColumn<Integer> packageId = moeGroomingPackageService.packageId;

    /**
     * Database Column Remarks:
     *   打包服务总数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.total_quantity")
    public static final SqlColumn<Integer> totalQuantity = moeGroomingPackageService.totalQuantity;

    /**
     * Database Column Remarks:
     *   打包服务剩余数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.remaining_quantity")
    public static final SqlColumn<Integer> remainingQuantity = moeGroomingPackageService.remainingQuantity;

    /**
     * Database Column Remarks:
     *   package item 支持选择多个 service，多个 service 之间共享 quantity，单个 service 结构参考：com.moego.server.retail.dto.PackageInfoDto.Service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_package_service.services")
    public static final SqlColumn<List<PackageService>> services = moeGroomingPackageService.services;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    public static final class MoeGroomingPackageService extends AliasableSqlTable<MoeGroomingPackageService> {
        public final SqlColumn<Integer> id = column("id", JDBCType.INTEGER);

        public final SqlColumn<Integer> packageId = column("package_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> totalQuantity = column("total_quantity", JDBCType.INTEGER);

        public final SqlColumn<Integer> remainingQuantity = column("remaining_quantity", JDBCType.INTEGER);

        public final SqlColumn<List<PackageService>> services = column("services", JDBCType.LONGVARCHAR, "example.mapper.typehandler.PackageServiceTypeHandler");

        public MoeGroomingPackageService() {
            super("moe_grooming_package_service", MoeGroomingPackageService::new);
        }
    }
}