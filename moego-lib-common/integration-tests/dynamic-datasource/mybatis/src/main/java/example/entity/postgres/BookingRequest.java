package example.entity.postgres;

import example.models.BookingRequestModel.Attr;
import jakarta.annotation.Generated;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Database Table Remarks:
 *   Booking request submitted by customer
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table booking_request
 */
public class BookingRequest {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.company_id")
    private Long companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.business_id")
    private Long businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.customer_id")
    private Long customerId;

    /**
     * Database Column Remarks:
     *   The appointment id generated after the business schedule
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.appointment_id")
    private Long appointmentId;

    /**
     * Database Column Remarks:
     *   The start date of the booking request, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.start_date")
    private LocalDate startDate;

    /**
     * Database Column Remarks:
     *   The start time of the booking request, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.start_time")
    private Integer startTime;

    /**
     * Database Column Remarks:
     *   The end date of the booking request, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.end_date")
    private LocalDate endDate;

    /**
     * Database Column Remarks:
     *   The end time of the booking request, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.end_time")
    private Integer endTime;

    /**
     * Database Column Remarks:
     *   1: SUBMITTED, 2: WAIT_LIST, 3: SCHEDULED, 4: DECLINED, 5: DELETED
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.status")
    private Integer status;

    /**
     * Database Column Remarks:
     *   Whether the booking request is prepaid
     * 
     * @deprecated use payment_status instead
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.is_prepaid")
    @Deprecated(since = "2025-01-22")
    private Boolean isPrepaid;

    /**
     * Database Column Remarks:
     *   Additional note for the booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.additional_note")
    private String additionalNote;

    /**
     * Database Column Remarks:
     *   The platform where the booking request is submitted, RESERVE_WITH_GOOGLE, PET_PARENT_APP
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.source_platform")
    private String sourcePlatform;

    /**
     * Database Column Remarks:
     *   The bitmap value for service, 0b1: grooming, 0b10: boarding, 0b100: daycare, 0b1000: evaluation test
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.service_type_include")
    private Integer serviceTypeInclude;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.created_at")
    private LocalDateTime createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.updated_at")
    private LocalDateTime updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.deleted_at")
    private LocalDateTime deletedAt;

    /**
     * Database Column Remarks:
     *   存储一些通用信息，结构为 BookingRequestModel.Attr
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.attr")
    private Attr attr;

    /**
     * Database Column Remarks:
     *   payment status, see BookingRequestModel.PaymentStatus
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.payment_status")
    private Integer paymentStatus;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.company_id")
    public Long getCompanyId() {
        return companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.company_id")
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.business_id")
    public Long getBusinessId() {
        return businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.business_id")
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.customer_id")
    public Long getCustomerId() {
        return customerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.customer_id")
    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.appointment_id")
    public Long getAppointmentId() {
        return appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.appointment_id")
    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.start_date")
    public LocalDate getStartDate() {
        return startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.start_date")
    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.start_time")
    public Integer getStartTime() {
        return startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.start_time")
    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.end_date")
    public LocalDate getEndDate() {
        return endDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.end_date")
    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.end_time")
    public Integer getEndTime() {
        return endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.end_time")
    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.status")
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * @deprecated use payment_status instead
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.is_prepaid")
    @Deprecated(since = "2025-01-22")
    public Boolean getIsPrepaid() {
        return isPrepaid;
    }

    /**
     * @deprecated use payment_status instead
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.is_prepaid")
    @Deprecated(since = "2025-01-22")
    public void setIsPrepaid(Boolean isPrepaid) {
        this.isPrepaid = isPrepaid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.additional_note")
    public String getAdditionalNote() {
        return additionalNote;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.additional_note")
    public void setAdditionalNote(String additionalNote) {
        this.additionalNote = additionalNote;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.source_platform")
    public String getSourcePlatform() {
        return sourcePlatform;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.source_platform")
    public void setSourcePlatform(String sourcePlatform) {
        this.sourcePlatform = sourcePlatform;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.service_type_include")
    public Integer getServiceTypeInclude() {
        return serviceTypeInclude;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.service_type_include")
    public void setServiceTypeInclude(Integer serviceTypeInclude) {
        this.serviceTypeInclude = serviceTypeInclude;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.updated_at")
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.updated_at")
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.deleted_at")
    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.deleted_at")
    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.attr")
    public Attr getAttr() {
        return attr;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.attr")
    public void setAttr(Attr attr) {
        this.attr = attr;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.payment_status")
    public Integer getPaymentStatus() {
        return paymentStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request.payment_status")
    public void setPaymentStatus(Integer paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BookingRequest other = (BookingRequest) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
            && (this.getCustomerId() == null ? other.getCustomerId() == null : this.getCustomerId().equals(other.getCustomerId()))
            && (this.getAppointmentId() == null ? other.getAppointmentId() == null : this.getAppointmentId().equals(other.getAppointmentId()))
            && (this.getStartDate() == null ? other.getStartDate() == null : this.getStartDate().equals(other.getStartDate()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getEndDate() == null ? other.getEndDate() == null : this.getEndDate().equals(other.getEndDate()))
            && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getIsPrepaid() == null ? other.getIsPrepaid() == null : this.getIsPrepaid().equals(other.getIsPrepaid()))
            && (this.getAdditionalNote() == null ? other.getAdditionalNote() == null : this.getAdditionalNote().equals(other.getAdditionalNote()))
            && (this.getSourcePlatform() == null ? other.getSourcePlatform() == null : this.getSourcePlatform().equals(other.getSourcePlatform()))
            && (this.getServiceTypeInclude() == null ? other.getServiceTypeInclude() == null : this.getServiceTypeInclude().equals(other.getServiceTypeInclude()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()))
            && (this.getAttr() == null ? other.getAttr() == null : this.getAttr().equals(other.getAttr()))
            && (this.getPaymentStatus() == null ? other.getPaymentStatus() == null : this.getPaymentStatus().equals(other.getPaymentStatus()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getCustomerId() == null) ? 0 : getCustomerId().hashCode());
        result = prime * result + ((getAppointmentId() == null) ? 0 : getAppointmentId().hashCode());
        result = prime * result + ((getStartDate() == null) ? 0 : getStartDate().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getEndDate() == null) ? 0 : getEndDate().hashCode());
        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getIsPrepaid() == null) ? 0 : getIsPrepaid().hashCode());
        result = prime * result + ((getAdditionalNote() == null) ? 0 : getAdditionalNote().hashCode());
        result = prime * result + ((getSourcePlatform() == null) ? 0 : getSourcePlatform().hashCode());
        result = prime * result + ((getServiceTypeInclude() == null) ? 0 : getServiceTypeInclude().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        result = prime * result + ((getAttr() == null) ? 0 : getAttr().hashCode());
        result = prime * result + ((getPaymentStatus() == null) ? 0 : getPaymentStatus().hashCode());
        return result;
    }
}