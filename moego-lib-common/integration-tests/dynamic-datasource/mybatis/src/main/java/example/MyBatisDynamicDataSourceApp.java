package example;

import static example.MyBatisDynamicDataSourceApp.MySQLMapperCfg.MYSQL_SQL_SESSION_FACTORY;
import static example.MyBatisDynamicDataSourceApp.PostgresMapperCfg.POSTGRES_SQL_SESSION_FACTORY;

import javax.sql.DataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@SpringBootApplication
public class MyBatisDynamicDataSourceApp {

    public static void main(String[] args) {
        SpringApplication.run(MyBatisDynamicDataSourceApp.class, args);
    }

    @Configuration(proxyBeanMethods = false)
    @MapperScan(value = "example.mapper.postgres", sqlSessionFactoryRef = POSTGRES_SQL_SESSION_FACTORY)
    static class PostgresMapperCfg {

        static final String POSTGRES_SQL_SESSION_FACTORY = "PostgresSqlSessionFactory";

        @Bean(POSTGRES_SQL_SESSION_FACTORY)
        @Primary
        public SqlSessionFactory postgresSqlSessionFactory(
                MybatisAutoConfiguration mybatisAutoConfiguration, DataSource dataSource) throws Exception {
            return mybatisAutoConfiguration.sqlSessionFactory(dataSource);
        }
    }

    @Configuration(proxyBeanMethods = false)
    @MapperScan(value = "example.mapper.mysql", sqlSessionFactoryRef = MYSQL_SQL_SESSION_FACTORY)
    static class MySQLMapperCfg {

        static final String MYSQL_SQL_SESSION_FACTORY = "MySQLSqlSessionFactory";

        @Bean(MYSQL_SQL_SESSION_FACTORY)
        public SqlSessionFactory mysqlSqlSessionFactory(
                MybatisAutoConfiguration mybatisAutoConfiguration, DataSource mysql) throws Exception {
            return mybatisAutoConfiguration.sqlSessionFactory(mysql);
        }
    }
}
