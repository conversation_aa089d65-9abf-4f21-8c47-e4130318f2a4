package com.moego.lib.common.observability.metrics.prometheus.grpc;

import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.assertj.core.api.Assertions.assertThatNoException;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Configuration;

class MetricsIntegrationTests {

    @Test
    void testMetricsEnabledByDefault() {
        ConfigurableApplicationContext ctx = new SpringApplicationBuilder(MetricsConfig.class)
                .web(WebApplicationType.NONE)
                .properties("moego.grpc.server.port=0")
                .run();
        assertThatNoException().isThrownBy(() -> ctx.getBean(MetricsServerInterceptor.class));
        assertThatNoException().isThrownBy(() -> ctx.getBean(MetricsHttpServer.class));
    }

    @Test
    void testMetricsDisabled() {
        ConfigurableApplicationContext ctx = new SpringApplicationBuilder(MetricsConfig.class)
                .web(WebApplicationType.NONE)
                .properties("moego.grpc.server.port=0")
                .properties("moego.grpc.server.observability.metrics.enabled=false")
                .run();
        assertThatExceptionOfType(NoSuchBeanDefinitionException.class)
                .isThrownBy(() -> ctx.getBean(MetricsServerInterceptor.class));
        assertThatExceptionOfType(NoSuchBeanDefinitionException.class)
                .isThrownBy(() -> ctx.getBean(MetricsHttpServer.class));
    }

    @EnableAutoConfiguration
    @Configuration(proxyBeanMethods = false)
    static class MetricsConfig {}
}
