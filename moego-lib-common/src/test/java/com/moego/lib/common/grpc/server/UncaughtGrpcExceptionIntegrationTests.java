package com.moego.lib.common.grpc.server;

import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.Empty;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.errors.v1.CommonError;
import com.moego.lib.common.EchoSvcController;
import com.moego.lib.common.GrpcServerPortSupplier;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.echo.v1.EchoGrpc;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.StatusRuntimeException;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Testing uncaught exceptions in gRPC calls.
 *
 * <AUTHOR>
 */
public class UncaughtGrpcExceptionIntegrationTests {

    static ConfigurableApplicationContext ctx;
    static ManagedChannel channel;

    @BeforeAll
    static void beforeAll() {
        ctx = new SpringApplicationBuilder(Config.class)
                .web(WebApplicationType.NONE)
                .properties("moego.grpc.server.port=0")
                .properties("moego.grpc.server.observability.metrics.enabled=false")
                .run();
        channel = ManagedChannelBuilder.forAddress("localhost", GrpcServerPortSupplier.getPort())
                .usePlaintext()
                .build();
    }

    @AfterAll
    static void afterAll() {
        if (ctx != null) {
            ctx.close();
        }
        if (channel != null) {
            channel.shutdown();
        }
    }

    @Test
    void testUncaughtGrpcException() {
        EchoGrpc.EchoBlockingStub client = EchoGrpc.newBlockingStub(channel);

        try {
            client.uncaughtException(Empty.newBuilder().build());
        } catch (StatusRuntimeException e) {
            CommonError ce = ExceptionUtil.extractCommonError(e);
            assertThat(ce).isNotNull();
            assertThat(ce.getCode()).isEqualTo(Code.CODE_SERVER_ERROR);
            assertThat(ce.getCausedBy()).isEqualTo("java.lang.RuntimeException: uncaught Exception");
        }
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    @Import({EchoSvcController.class, GrpcServerPortSupplier.class})
    static class Config {}
}
