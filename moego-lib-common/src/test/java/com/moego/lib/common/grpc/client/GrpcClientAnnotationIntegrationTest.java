package com.moego.lib.common.grpc.client;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.auth.GreeterGrpc;
import com.moego.lib.common.exception.HelperGrpc;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;

@SpringBootTest(
        classes = GrpcClientAnnotationIntegrationTest.Config.class,
        properties = {"moego.grpc.client.channels.auth=localhost:8080"})
public class GrpcClientAnnotationIntegrationTest {

    @GrpcClient("auth")
    GreeterGrpc.GreeterBlockingStub greeterBlockingStub;

    @GrpcClient("auth")
    GreeterGrpc.GreeterFutureStub greeterFutureStub;

    @GrpcClient("auth")
    GreeterGrpc.GreeterStub greeterStub;

    @GrpcClient("auth")
    GreeterGrpc.GreeterBlockingStub greeterBlockingStub2;

    @GrpcClient("auth")
    HelperGrpc.HelperBlockingStub helperBlockingStub;

    @Autowired
    ApplicationContext context;

    @Test
    public void testAnnotatedGrpcClientPropertyIsNotNull() {
        assertThat(greeterBlockingStub).isNotNull();
        assertThat(greeterFutureStub).isNotNull();
        assertThat(greeterStub).isNotNull();
        assertThat(greeterBlockingStub2).isNotNull();
        assertThat(helperBlockingStub).isNotNull();

        assertThat(greeterBlockingStub2 == greeterBlockingStub).isTrue();
    }

    @EnableAutoConfiguration
    static class Config {}
}
