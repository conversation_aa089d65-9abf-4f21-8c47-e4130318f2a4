package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.observability.metrics.prometheus.grpc.MetricsClientInterceptor;
import com.moego.lib.common.observability.metrics.prometheus.grpc.MetricsServerInterceptor;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;

/**
 * {@link com.moego.lib.common.autoconfigure.feature.Metrics.Grpc} tester.
 */
class MetricsGrpcTest {
    private final ApplicationContextRunner runner = new ApplicationContextRunner().withUserConfiguration(Metrics.class);

    @Test
    void testDisableGrpcMetrics() {
        runner.withPropertyValues(GrpcProperties.Observability.Metrics.PREFIX + ".enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(MetricsServerInterceptor.class);
                    assertThat(context).doesNotHaveBean(MetricsClientInterceptor.class);
                });
    }
}
