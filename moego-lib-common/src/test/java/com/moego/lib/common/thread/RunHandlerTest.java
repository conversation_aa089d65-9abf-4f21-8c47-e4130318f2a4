package com.moego.lib.common.thread;

import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.assertj.core.api.Assertions.assertThatNoException;

import org.junit.jupiter.api.Test;

/**
 * {@link RunHandler} tester.
 */
class RunHandlerTest {

    @Test
    void testSilent() {
        RunHandler rh = new RunHandler(
                () -> {
                    throw new RuntimeException("test");
                },
                true);
        assertThatNoException().isThrownBy(rh::run);
    }

    @Test
    void testNonSilent() {
        RunHandler rh = new RunHandler(
                () -> {
                    throw new RuntimeException("test");
                },
                false);
        assertThatExceptionOfType(RuntimeException.class).isThrownBy(rh::run);
    }
}
