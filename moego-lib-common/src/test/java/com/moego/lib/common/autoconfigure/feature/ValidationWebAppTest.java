package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import io.envoyproxy.pgv.grpc.ValidatingClientInterceptor;
import io.envoyproxy.pgv.grpc.ValidatingServerInterceptor;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.WebApplicationContextRunner;

/**
 * {@link Validation} tester.
 */
class ValidationWebAppTest {

    private final WebApplicationContextRunner runner =
            new WebApplicationContextRunner().withUserConfiguration(Validation.class);

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            // dependencies pass from api-definition
            assertThat(context).hasSingleBean(ValidatingServerInterceptor.class);
            assertThat(context).hasSingleBean(ValidatingClientInterceptor.class);
        });
    }

    @Test
    public void testDisableGrpc() {
        runner.withPropertyValues(GrpcProperties.PREFIX + ".enabled=false").run(context -> {
            assertThat(context).doesNotHaveBean(ValidatingServerInterceptor.class);
            assertThat(context).doesNotHaveBean(ValidatingClientInterceptor.class);
        });
    }

    @Test
    public void testGrpcServerDisabled() {
        runner.withPropertyValues(GrpcProperties.PREFIX + ".server.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(ValidatingServerInterceptor.class);
                    assertThat(context).hasSingleBean(ValidatingClientInterceptor.class);
                });
    }

    @Test
    public void testGrpcClientDisabled() {
        runner.withPropertyValues(GrpcProperties.PREFIX + ".client.enabled=false")
                .run(context -> {
                    assertThat(context).hasSingleBean(ValidatingServerInterceptor.class);
                    assertThat(context).doesNotHaveBean(ValidatingClientInterceptor.class);
                });
    }
}
