package com.moego.lib.common.util;

import static com.moego.lib.common.util.GreyUtil.getBranchFromGit;
import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;

/**
 * {@link GreyUtil}
 */
class GreyUtilTest {

    /**
     * {@link GreyUtil#getBranchFromGit(ApplicationContext)}
     */
    @Test
    void testGetBranchFromGit() {
        try (var ctx = new SpringApplicationBuilder(Cfg.class).run()) {
            // 在 test 时不能拿到 GitProperties
            assertThat(getBranchFromGit(ctx)).isNull();
        }
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    static class Cfg {}
}
