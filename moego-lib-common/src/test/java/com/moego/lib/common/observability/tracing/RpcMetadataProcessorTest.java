package com.moego.lib.common.observability.tracing;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadContextHolder;
import io.grpc.Metadata;
import io.grpc.stub.MetadataUtils;
import io.grpc.stub.StreamObserver;
import io.grpc.testing.protobuf.SimpleRequest;
import io.grpc.testing.protobuf.SimpleResponse;
import io.grpc.testing.protobuf.SimpleServiceGrpc;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@SpringBootTest(
        classes = RpcMetadataProcessorTest.Cfg.class,
        properties = {"moego.grpc.client.base-packages[0]=io.grpc"})
class RpcMetadataProcessorTest {

    @Autowired
    SimpleServiceGrpc.SimpleServiceBlockingStub stub;

    @Test
    void testRpcMetadataProcessor() {
        Metadata metadata = new Metadata();
        metadata.put(Metadata.Key.of(AuthContext.HK_ACCOUNT_ID, Metadata.ASCII_STRING_MARSHALLER), "111");
        metadata.put(Metadata.Key.of("xx", Metadata.ASCII_STRING_MARSHALLER), "foo");
        metadata.put(Metadata.Key.of("gv", Metadata.ASCII_STRING_MARSHALLER), "foo");
        metadata.put(Metadata.Key.of("gv-a", Metadata.ASCII_STRING_MARSHALLER), "foo");
        SimpleResponse response = stub.withInterceptors(MetadataUtils.newAttachHeadersInterceptor(metadata))
                .unaryRpc(SimpleRequest.getDefaultInstance());
        assertThat(response.getResponseMessage()).isEqualTo("OK");
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    @GrpcService
    static class Cfg extends SimpleServiceGrpc.SimpleServiceImplBase {

        @Autowired
        private SimpleServiceGrpc.SimpleServiceBlockingStub stub;

        @Override
        public void unaryRpc(SimpleRequest request, StreamObserver<SimpleResponse> responseObserver) {
            Headers headers = ThreadContextHolder.getContext(Headers.class);
            assertThat(headers).isNotNull();
            assertThat(headers.get(AuthContext.HK_ACCOUNT_ID)).isEqualTo("111");
            assertThat(headers.get("xx")).isNull();
            assertThat(headers.get("gv")).isNull();
            assertThat(headers.get("gv-a")).isEqualTo("foo");

            if (request.getRequestMessage().equals("OK")) {
                responseObserver.onNext(
                        SimpleResponse.newBuilder().setResponseMessage("OK").build());
                responseObserver.onCompleted();
                return;
            }
            String msg = stub.unaryRpc(
                            SimpleRequest.newBuilder().setRequestMessage("OK").build())
                    .getResponseMessage();
            responseObserver.onNext(
                    SimpleResponse.newBuilder().setResponseMessage(msg).build());
            responseObserver.onCompleted();
        }
    }
}
