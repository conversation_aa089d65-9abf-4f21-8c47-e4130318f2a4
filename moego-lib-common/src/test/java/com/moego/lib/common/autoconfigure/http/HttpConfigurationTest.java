package com.moego.lib.common.autoconfigure.http;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.WebApplicationContextRunner;

/**
 * {@link HttpConfiguration} tester.
 */
class HttpConfigurationTest {

    private final WebApplicationContextRunner runner =
            new WebApplicationContextRunner().withUserConfiguration(HttpConfiguration.class);

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).hasSingleBean(HttpConfiguration.class);
            assertThat(context).hasSingleBean(HttpClientConfiguration.class);
            assertThat(context).hasSingleBean(HttpServerConfiguration.class);
            assertThat(context).hasSingleBean(HttpProperties.class);
        });
    }

    @Test
    void testDisableHttp() {
        runner.withPropertyValues(HttpProperties.PREFIX + ".enabled=false").run(context -> {
            assertThat(context).doesNotHaveBean(HttpConfiguration.class);
            assertThat(context).doesNotHaveBean(HttpClientConfiguration.class);
            assertThat(context).doesNotHaveBean(HttpServerConfiguration.class);
            assertThat(context).doesNotHaveBean(HttpProperties.class);
        });
    }
}
