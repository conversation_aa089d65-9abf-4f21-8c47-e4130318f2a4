package com.moego.lib.common.autoconfigure.grpc;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.grpc.client.GrpcClientBeanPostProcessor;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;

/**
 * {@link GrpcClientConfiguration} tester.
 */
class GrpcClientConfigurationTest {

    private final ApplicationContextRunner runner =
            new ApplicationContextRunner().withUserConfiguration(GrpcClientConfiguration.class);

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).hasSingleBean(GrpcClientBeanPostProcessor.class);
        });
    }

    @Test
    public void testDisableGrpcClient() {
        runner.withPropertyValues(GrpcProperties.PREFIX + ".client.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(GrpcClientBeanPostProcessor.class);
                });
    }
}
