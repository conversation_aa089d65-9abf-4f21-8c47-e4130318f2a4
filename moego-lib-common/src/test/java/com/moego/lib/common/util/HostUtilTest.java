package com.moego.lib.common.util;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

class HostUtilTest {

    @Test
    void getPayOnlineClientHost() {
        assertEquals(
                "https://client.moego.pet", HostUtil.getPayOnlineClientHost(HostUtil.NAMESPACE_PROD, "production"));

        assertEquals(
                "https://client.t2.moego.dev",
                HostUtil.getPayOnlineClientHost(HostUtil.NAMESPACE_TESTING, "production"));

        assertEquals(
                "https://client.s1.moego.dev", HostUtil.getPayOnlineClientHost(HostUtil.NAMESPACE_STAGING, "staging"));
        String branch = "auto-receipt";
        assertEquals(
                "https://" + branch + "-grey-client.t2.moego.dev",
                HostUtil.getPayOnlineClientHost(HostUtil.NAMESPACE_TESTING, branch));

        assertEquals(
                "https://auto-receipt-grey-client.t2.moego.dev",
                HostUtil.getPayOnlineClientHost(HostUtil.NAMESPACE_TESTING, "feature-auto-receipt"));
    }

    @Test
    void testGateClientHost() {
        assertEquals(
                "https://online-grey-client.t2.moego.dev",
                HostUtil.getPayOnlineClientHost(HostUtil.NAMESPACE_TESTING, "online"));
        assertEquals(
                "https://gate-grey-client.t2.moego.dev",
                HostUtil.getPayOnlineClientHost(HostUtil.NAMESPACE_TESTING, "gate"));
        assertEquals(
                "https://online-grey-client.t2.moego.dev",
                HostUtil.getPayOnlineClientHost(HostUtil.NAMESPACE_TESTING, ""));
    }

    @Test
    void getBusinessHost() {
        assertEquals("https://go.moego.pet", HostUtil.getBusinessHost(HostUtil.NAMESPACE_PROD, "production"));
        assertEquals("https://go.t2.moego.dev", HostUtil.getBusinessHost(HostUtil.NAMESPACE_TESTING, "production"));

        assertEquals("https://go.s1.moego.dev", HostUtil.getBusinessHost(HostUtil.NAMESPACE_STAGING, "staging"));
        String branch = "auto-receipt";
        assertEquals(
                "https://" + branch + "-grey-go.t2.moego.dev",
                HostUtil.getBusinessHost(HostUtil.NAMESPACE_TESTING, branch));

        assertEquals(
                "https://auto-receipt-grey-go.t2.moego.dev",
                HostUtil.getBusinessHost(HostUtil.NAMESPACE_TESTING, "feature-auto-receipt"));
    }
}
