package com.moego.lib.common.util;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@SpringBootTest(
        classes = RequestUtilTest.Config.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        properties = {"moego.grpc.enabled=false", "moego.http.server.observability.metrics.enabled=false"})
public class RequestUtilTest {

    @LocalServerPort
    int port;

    @Autowired
    TestRestTemplate restTemplate;

    @Test
    public void test() throws Exception {
        String url = "http://localhost:" + port + "/hello";

        // without referer
        RequestHeader res = restTemplate.getForObject(url, RequestHeader.class);
        assertThat(res.getIp()).isNotEmpty();
        assertThat(res.getUserAgent()).isNotEmpty();
        assertThat(res.getReferer()).isEqualTo("");

        // with referer
        String referer = "www.moego.pet";
        RequestEntity request =
                RequestEntity.get(url).header("Referer", referer).build();
        ResponseEntity<RequestHeader> re = restTemplate.exchange(request, RequestHeader.class);
        assertThat(re.getBody()).isNotNull();
        assertThat(re.getBody().getReferer()).isEqualTo(referer);

        // with long referer
        referer =
                "www.moego.pet?name=ewijofknsdkbhfjdqwklmascnfhrijoeqwkldmadjhksfbcnkdjskbfshknlfhsbbknlsdjaskhvcasnfbeqewijofknsdkbhfjdqwklmascnfhrijoeqwkldmadjhksfbcnkdjskbfshknlfhsbbknlsdjaskhvcasnfbeqewijofknsdkbhfjdqwklmascnfhrijoeqwkldmadjhksfbcnkdjskbfshknlfhsbbknlsdjaskhvcasnfbeq";
        request = RequestEntity.get(url).header("Referer", referer).build();
        re = restTemplate.exchange(request, RequestHeader.class);
        assertThat(re.getBody()).isNotNull();
        assertThat(re.getBody().getReferer()).hasSize(255);
        assertThat(re.getBody().getReferer()).isEqualTo(referer.substring(0, 255));
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    @RestController
    static class Config {

        @GetMapping("/hello")
        @Auth(AuthType.ANONYMOUS)
        public RequestHeader hello(HttpServletRequest request) {
            RequestHeader requestHeader = new RequestHeader();
            requestHeader.setIp(RequestUtils.getIP(request));
            requestHeader.setUserAgent(RequestUtils.getUserAgent(request));
            requestHeader.setReferer(RequestUtils.getReferer(request));
            return requestHeader;
        }
    }

    @Data
    static class RequestHeader {

        private String ip;
        private String userAgent;
        private String referer;
    }
}
