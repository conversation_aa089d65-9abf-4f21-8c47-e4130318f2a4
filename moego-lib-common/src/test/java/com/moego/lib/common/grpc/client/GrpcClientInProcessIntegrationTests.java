package com.moego.lib.common.grpc.client;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.model.echo.v1.EchoRequest;
import com.moego.model.echo.v1.EchoResponse;
import com.moego.svc.echo.v1.EchoGrpc;
import io.grpc.stub.StreamObserver;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = GrpcClientInProcessIntegrationTests.Cfg.class)
@Disabled("It’s okay to run alone, but can’t pass it with './gradlew test', very strange :)")
class GrpcClientInProcessIntegrationTests {

    @Autowired
    private EchoGrpc.EchoBlockingStub echoStub;

    @Test
    void testInProcess() {
        EchoResponse resp =
                echoStub.echo(EchoRequest.newBuilder().setMessage("Hello").build());
        assertThat(resp.getMessage()).isEqualTo("Hello");
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    @GrpcService
    static class Cfg extends EchoGrpc.EchoImplBase {

        @Override
        public void echo(EchoRequest request, StreamObserver<EchoResponse> responseObserver) {
            responseObserver.onNext(
                    EchoResponse.newBuilder().setMessage(request.getMessage()).buildPartial());
            responseObserver.onCompleted();
        }
    }
}
