package com.moego.lib.common.auth.grpc;

import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.moego.api.api.GreeterGrpc.GreeterImplBase;
import io.grpc.stub.StreamObserver;
import org.assertj.core.api.AbstractThrowableAssert;
import org.junit.jupiter.api.Test;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Controller;

public class AuthApiLayerIntegrationTests {

    @Test
    public void testApiLayerServicesMustHaveAuthAnnotation() {
        AbstractThrowableAssert<?, ?> rootCause = assertThatThrownBy(
                        () -> new SpringApplicationBuilder(AuthApiLayerConfig.class)
                                .web(WebApplicationType.NONE)
                                .properties("moego.grpc.server.port=0")
                                .run())
                .getRootCause();
        rootCause.isInstanceOf(IllegalStateException.class);
        rootCause.hasMessageContaining("because it's a api-layer service");
    }

    @EnableAutoConfiguration
    @Import({AuthApiLayerGreetService.class})
    @Configuration(proxyBeanMethods = false)
    static class AuthApiLayerConfig {}
}

@Controller
class AuthApiLayerGreetService extends GreeterImplBase {

    @Override
    public void sayHello(
            com.moego.api.api.HelloRequest request, StreamObserver<com.moego.api.api.HelloReply> responseObserver) {
        super.sayHello(request, responseObserver);
    }
}
