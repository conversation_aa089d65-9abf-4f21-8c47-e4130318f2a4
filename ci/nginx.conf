server {
    listen       8080;
    server_name  _;
    gzip on;
    gzip_types *;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri /index.html;
    }

    location /rest/v3/business.json {
      rewrite .* /v3/api-docs break;
      proxy_http_version 1.1;
      proxy_pass http://moego-service-business:9203;
    }

    location /rest/v3/customer.json {
      rewrite .* /v3/api-docs break;
      proxy_http_version 1.1;
      proxy_pass http://moego-service-customer:9201;
    }

    location /rest/v3/grooming.json {
      rewrite .* /v3/api-docs break;
      proxy_http_version 1.1;
      proxy_pass http://moego-service-grooming:9206;
    }

    location /rest/v3/retail.json {
      rewrite .* /v3/api-docs break;
      proxy_http_version 1.1;
      proxy_pass http://moego-service-retail:9207;
    }

    location /rest/v3/message.json {
      rewrite .* /v3/api-docs break;
      proxy_http_version 1.1;
      proxy_pass http://moego-service-message:9205;
    }

    location /rest/v3/payment.json {
      rewrite .* /v3/api-docs break;
      proxy_http_version 1.1;
      proxy_pass http://moego-service-payment:9204;
    }

    location /rest/v3/service.json {
      rewrite .* /v3/api-docs break;
      proxy_http_version 1.1;
      proxy_pass http://moego-server-api:9290;
    }

    location ~* \.map$ {
      deny all;
    }
}
