/*
 * @since 2024-05-28 10:14:25
 * <AUTHOR> <<EMAIL>>
 */

const fs = require('fs');

const files = fs.readdirSync('out/redoc');
for (const f of files) {
  if (!f.endsWith('.json')) {
    continue;
  }
  const tagMap = {};
  const content = fs.readFileSync('out/redoc/' + f, 'utf-8');
  const data = JSON.parse(content);
  for (const path in data.paths) {
    const tag = path.split('/')[1];
    for (const method in data.paths[path]) {
      const exists = data.paths[path][method].tags[0].split('.').pop();
      const maps = (tagMap[exists] ??= []);
      maps.includes(tag) || maps.push(tag);
      data.paths[path][method].tags = [tag];
    }
  }

  for (const tag of data.tags) {
    if (tag['x-displayName']) {
      continue;
    }
    const nt = tagMap[tag.name]?.shift();
    if (!nt) {
      console.log(tag);
      throw new Error('cannot get tag name for ' + tag.name);
    }
    tag['x-displayName'] = tag.name;
    tag.name = nt;
  }

  const tg = {};
  for (const tag of data.tags) {
    const domain = tag.name.split('.').reverse().slice(1, 3).reverse().join('.');
    tg[domain] ||= { name: domain, tags: [] };
    tg[domain].tags.push(tag.name);
  }

  data['x-tagGroups'] = Object.values(tg).sort((a, b) => a.name.localeCompare(b.name));

  fs.writeFileSync('out/redoc/' + f, JSON.stringify(data, void 0, 2));
  console.log('transform %s', f);
}
