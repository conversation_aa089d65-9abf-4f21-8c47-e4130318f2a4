package com.moego.svc.activitylog.server.listener;

import static java.util.concurrent.CompletableFuture.runAsync;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.lib.messaging.Msg;
import com.moego.svc.activitylog.event.ActivityLogEvent;
import com.moego.svc.activitylog.server.entity.ActivityLog;
import com.moego.svc.activitylog.server.service.ActivityLogFiller;
import com.moego.svc.activitylog.server.service.ActivityLogService;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.CompletableFuture;
import java.util.stream.IntStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * {@link ActivityLogListener} tester.
 */
@ExtendWith(MockitoExtension.class)
class ActivityLogListenerTest {

    @Mock
    ActivityLogService activityLogService;

    @Mock
    ActivityLogFiller filler;

    @Mock
    BusinessServiceGrpc.BusinessServiceBlockingStub businessStub;

    ActivityLogListener listener;

    @BeforeEach
    void setup() {
        listener = new ActivityLogListener(activityLogService, filler, businessStub);
    }

    @Test
    void testOnEvent() {
        addItemToQueue(3);

        verify(filler, times(3)).populate(any());
        assertThat(getQueue()).hasSize(3);

        // queue size 为 1000，超过 1000 时会清空并执行批量插入
        addItemToQueue(1000);

        assertThat(getQueue()).hasSize(3);
        verify(activityLogService, times(1)).saveBatch(any());
    }

    @Test
    void testClearAndInsert_whenConcurrent_thenOnlyOneThreadCanInsert() throws InterruptedException {

        addItemToQueue(10);

        IntStream.range(0, 100)
                .mapToObj(__ -> runAsync(listener::clearAndInsert))
                .forEach(CompletableFuture::join);

        assertThat(getQueue()).isEmpty();

        verify(activityLogService, times(1)).saveBatch(any());
    }

    private void addItemToQueue(int size) {
        ActivityLogEvent event = mock(ActivityLogEvent.class);
        ActivityLog log = mock(ActivityLog.class);
        when(filler.populate(event)).thenReturn(log);

        for (int i = 0; i < size; i++) {
            listener.onEvent(new DummyMsg(event));
        }
    }

    @SuppressWarnings("unchecked")
    private Queue<ActivityLog> getQueue() {
        return (Queue<ActivityLog>) ReflectionTestUtils.getField(listener, "queue");
    }

    static class DummyMsg implements Msg<ActivityLogEvent> {
        private final ActivityLogEvent event;

        public DummyMsg(ActivityLogEvent event) {
            this.event = event;
        }

        @Override
        public ActivityLogEvent getBody() {
            return event;
        }

        @Override
        public Map<String, String> getHeaders() {
            return Map.of();
        }

        @Override
        public void ack() {}

        @Override
        public void nack() {}
    }
}
