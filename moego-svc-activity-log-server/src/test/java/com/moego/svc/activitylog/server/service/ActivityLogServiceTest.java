package com.moego.svc.activitylog.server.service;

import static org.assertj.core.api.Assertions.assertThat;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.moego.svc.activitylog.server.entity.ActivityLog;
import com.moego.svc.activitylog.server.service.param.SearchActivityLogParam;
import com.moego.svc.activitylog.server.service.param.SearchOwnerParam;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest
@ActiveProfiles("test")
class ActivityLogServiceTest {

    @Autowired
    private ActivityLogService service;

    @Test
    void insert() {
        ActivityLog al = new ActivityLog();
        al.setOperatorId("1");
        al.setOperatorName("Daniel");
        al.setOwnerId("1");
        al.setOwnerName("Freeman");
        al.setResourceId("1");
        al.setResourceName("Appointment at 2020-01-01 10:00:00");
        al.setResourceType("Appointment");
        al.setAction("Add");
        al.setBusinessId(1L);
        al.setTime(new Date());
        service.insert(al);
        assertThat(service.getById(al.getId())).isNotNull();
    }

    @Test
    void getById() {
        assertThat(service.getById("1")).isNull();
    }

    @Test
    void search() {
        SearchActivityLogParam param = new SearchActivityLogParam(
                List.of(1L),
                List.of("1", "2"),
                List.of("Add", "Update"),
                List.of("Appointment", "Pet"),
                null,
                List.of("1", "2"),
                new Date(),
                null);
        IPage<ActivityLog> page = service.search(param, new Page<>(2, 20));
        assertThat(page.getRecords()).isEmpty();
    }

    /**
     * {@link ActivityLogService#searchOwner(SearchOwnerParam, Page)}
     */
    @Test
    void searchOwner() {
        insert4Owner(1L, "Freeman");
        insert4Owner(1L, "Freeman");
        insert4Owner(1L, "Freeman");
        SearchOwnerParam param = new SearchOwnerParam(1L, "Freeman");
        IPage<ActivityLog> page = service.searchOwner(param, new Page<>(1, 1));
        assertThat(page.getRecords()).hasSize(1); // not 3 because of distinct
    }

    private void insert4Owner(Long businessId, String ownerName) {
        ActivityLog al = new ActivityLog();
        al.setOperatorId("1");
        al.setOperatorName("Daniel");
        al.setOwnerId("1");
        al.setOwnerName(ownerName);
        al.setResourceId("1");
        al.setResourceName("Appointment at 2020-01-01 10:00:00");
        al.setResourceType("Appointment");
        al.setAction("Add");
        al.setBusinessId(businessId);
        al.setTime(new Date());
        service.insert(al);
    }
}
