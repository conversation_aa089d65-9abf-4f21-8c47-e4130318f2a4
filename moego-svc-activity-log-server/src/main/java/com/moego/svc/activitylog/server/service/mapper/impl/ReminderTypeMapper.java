package com.moego.svc.activitylog.server.service.mapper.impl;

import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class ReminderTypeMapper implements Mapper<String> {

    private static final Map<String, String> VALUE_MAP = Map.of(
            "1", "1st reminder",
            "2", "2nd reminder",
            "3", "pet birthday reminder",
            "4", "rebook reminder",
            "6", "arrival window",
            "7", "general reminder");

    @Override
    public Map<String, String> map(Set<String> values) {
        return VALUE_MAP;
    }

    @Override
    public String getName(String value) {
        return VALUE_MAP.get(value);
    }
}
