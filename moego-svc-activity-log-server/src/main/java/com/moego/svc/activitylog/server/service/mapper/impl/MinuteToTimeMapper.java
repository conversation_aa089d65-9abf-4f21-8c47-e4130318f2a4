package com.moego.svc.activitylog.server.service.mapper.impl;

import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class MinuteToTimeMapper implements Mapper<String> {

    @Override
    public Map<String, String> map(Set<String> values) {
        Map<String, String> result = new HashMap<>(values.size());
        for (String value : values) {
            result.put(value, getName(value));
        }
        return result;
    }

    /**
     * 将分钟数转为时间
     * e.g.
     * 0 -> 00:00 AM
     * 630 -> 10:30 AM
     * 720 -> 12:00 PM
     * 780 -> 01:00 PM
     * 1440 -> 00:00 AM
     * @param value
     * @return
     */
    @Override
    public String getName(String value) {
        int intValue;
        try {
            intValue = Integer.parseInt(value);
        } catch (NumberFormatException e) {
            log.error("parse number error, value: {}", value);
            return null;
        }

        if (intValue == 0) {
            return "00:00 AM or no record";
        }

        int hour = intValue / 60;
        int minute = intValue % 60;

        // 中午 12 点之后（包括12点）是 PM，24点是 AM
        boolean isAM = (hour < 12) || hour == 24;
        // 13点开始才变为1点，中午12点还是12点，24点是0点
        if (hour > 12) {
            hour %= 12;
        }

        return String.format("%02d:%02d %s", hour, minute, isAM ? "AM" : "PM");
    }
}
