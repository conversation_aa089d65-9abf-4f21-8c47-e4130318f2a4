package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorInformer;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#CUSTOMER}.
 *
 * <AUTHOR>
 */
@Component
public class CustomerInformer extends AbstractStaffOperatorInformer<MoeBusinessCustomerDTO> {

    private final ICustomerCustomerService customerApi;

    public CustomerInformer(ICustomerCustomerService customerApi) {
        this.customerApi = customerApi;
    }

    @Override
    public String resourceType() {
        return ResourceType.CUSTOMER.toString();
    }

    @Override
    public MoeBusinessCustomerDTO resource(String resourceId) {
        return customerApi.getCustomerWithDeleted(Integer.parseInt(resourceId));
    }

    @Override
    public String resourceName(MoeBusinessCustomerDTO resource) {
        return resource.getFirstName() + " " + resource.getLastName();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }
}
