package com.moego.svc.activitylog.server;

import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.client.IBusinessTaxClient;
import com.moego.server.customer.client.ICustomerContactClient;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.ICustomerNoteClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.client.IPetNoteClient;
import com.moego.server.customer.client.IPetPhotoClient;
import com.moego.server.customer.client.IPetVaccineBindingClient;
import com.moego.server.grooming.client.IGroomingAppointmentClient;
import com.moego.server.grooming.client.IGroomingPackageClient;
import com.moego.server.grooming.client.IGroomingServiceClient;
import com.moego.server.grooming.client.IPetCustomizedServiceClient;
import com.moego.server.message.client.IMessageClient;
import com.moego.server.message.client.IMessageThreadClient;
import com.moego.server.payment.client.IPaymentCreditCardClient;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.client.IPaymentRefundClient;
import com.moego.server.retail.client.IPackageClient;
import com.moego.svc.activitylog.server.config.FieldMappingRuleProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 */
@SpringBootApplication(excludeName = {"com.moego.common.autoconfigure.CacheAutoConfiguration"})
@EnableFeignClients(
        clients = {
            IGroomingAppointmentClient.class,
            IPetClient.class,
            ICustomerCustomerClient.class,
            IBusinessStaffClient.class,
            IMessageClient.class,
            IMessageThreadClient.class,
            IPetNoteClient.class,
            ICustomerNoteClient.class,
            IPetPhotoClient.class,
            IPetVaccineBindingClient.class,
            IPetCustomizedServiceClient.class,
            ICustomerContactClient.class,
            IGroomingServiceClient.class,
            IBusinessTaxClient.class,
            IPackageClient.class,
            IGroomingPackageClient.class,
            IPaymentPaymentClient.class,
            IPaymentCreditCardClient.class,
            IPaymentPaymentClient.class,
            IPaymentRefundClient.class
        })
@MapperScan("com.moego.svc.activitylog.server.repository")
@EnableConfigurationProperties(FieldMappingRuleProperties.class)
public class ActivityLogApplication {

    public static void main(String[] args) {
        SpringApplication.run(ActivityLogApplication.class, args);
    }
}
