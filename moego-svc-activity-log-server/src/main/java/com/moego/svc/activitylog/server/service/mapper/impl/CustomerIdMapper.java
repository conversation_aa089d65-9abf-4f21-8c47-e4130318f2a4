package com.moego.svc.activitylog.server.service.mapper.impl;

import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class CustomerIdMapper implements Mapper<MoeBusinessCustomerDTO> {

    private final ICustomerCustomerService customerApi;

    /**
     * Map customer ids to names.
     * @param customerIds customer ids
     * @return map of customer id -> name
     */
    @Override
    public Map<String, String> map(Set<String> customerIds) {
        Map<String, String> result = new HashMap<>(customerIds.size());

        for (String customerId : customerIds) {
            try {
                var customerIdInt = Integer.parseInt(customerId);
                if (customerIdInt <= 0) {
                    continue;
                }
                var customer = customerApi.getCustomerWithDeleted(customerIdInt);
                if (customer != null) {
                    var name = getName(customer);
                    result.put(customerId, name);
                }
            } catch (Exception e) {
                log.error("Failed to get customer name for customer id: {}", customerId, e);
            }
        }
        return result;
    }

    @Override
    public String getName(MoeBusinessCustomerDTO customer) {
        return String.join(" ", customer.getFirstName(), customer.getLastName());
    }
}
