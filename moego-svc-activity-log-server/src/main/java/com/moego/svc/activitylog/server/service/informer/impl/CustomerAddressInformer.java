package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetCustomerAddressRequest;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorCustomerOwnerInformer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#CUSTOMER_ADDRESS}.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class CustomerAddressInformer extends AbstractStaffOperatorCustomerOwnerInformer<BusinessCustomerAddressModel> {

    private final BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub customerAddressApi;

    @Override
    public String resourceType() {
        return ResourceType.CUSTOMER_ADDRESS.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public String getOwnerId(BusinessCustomerAddressModel addressModel) {
        return String.valueOf(addressModel.getCustomerId());
    }

    @Override
    public BusinessCustomerAddressModel resource(String resourceId) {
        var request = GetCustomerAddressRequest.newBuilder()
                .setId(Long.parseLong(resourceId))
                .build();
        return customerAddressApi.getCustomerAddress(request).getAddress();
    }

    @Override
    public String resourceName(BusinessCustomerAddressModel addressModel) {
        return null;
    }
}
