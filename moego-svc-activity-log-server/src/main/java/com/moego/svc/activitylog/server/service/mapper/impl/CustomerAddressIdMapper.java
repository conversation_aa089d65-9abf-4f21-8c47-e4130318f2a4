package com.moego.svc.activitylog.server.service.mapper.impl;

import com.google.common.collect.Lists;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerAddressRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc;
import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
@AllArgsConstructor
public class CustomerAddressIdMapper implements Mapper<BusinessCustomerAddressModel> {

    private final BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub customerAddressApi;

    /**
     * Map customer address ids to names.
     * @param addressIds customer address ids
     * @return map of customer address id -> name
     */
    @Override
    public Map<String, String> map(Set<String> addressIds) {
        Map<String, String> result = new HashMap<>(addressIds.size());

        var addressIdsLong =
                addressIds.stream().map(Long::parseLong).filter(id -> id > 0).toList();
        if (CollectionUtils.isEmpty(addressIdsLong)) {
            return result;
        }
        Lists.partition(addressIdsLong, 100).forEach(partition -> {
            try {
                var request = BatchGetCustomerAddressRequest.newBuilder()
                        .addAllIds(partition)
                        .build();
                var addressesMap =
                        customerAddressApi.batchGetCustomerAddress(request).getAddressesMap();
                addressesMap.forEach((id, address) -> {
                    var name = getName(address);
                    result.put(String.valueOf(id), name);
                });
            } catch (Exception e) {
                log.error("Failed to get customer address names for customer address ids: {}", partition, e);
            }
        });
        return result;
    }

    @Override
    public String getName(BusinessCustomerAddressModel address) {
        return String.join(
                ", ",
                address.getCountry(),
                address.getState(),
                address.getCity(),
                address.getAddress1(),
                address.getAddress2());
    }
}
