package com.moego.svc.activitylog.server.service;

import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.jayway.jsonpath.TypeRef;
import com.jayway.jsonpath.spi.mapper.JacksonMappingProvider;
import com.moego.svc.activitylog.event.ActivityLogEvent;
import com.moego.svc.activitylog.server.config.FieldMappingRuleProperties;
import com.moego.svc.activitylog.server.entity.ActivityLog;
import com.moego.svc.activitylog.server.mapstruct.ActivityLogMapper;
import com.moego.svc.activitylog.server.service.context.MappingContext;
import com.moego.svc.activitylog.server.service.informer.Informer;
import com.moego.svc.activitylog.server.service.mapper.MapperName;
import com.moego.svc.activitylog.server.service.model.ActivityLogNameInfo;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityLogFiller {
    private final List<Informer<?>> informers;

    private final FieldMappingRuleProperties fieldMappingRuleProperties;

    private final MappingContext mappingContext;

    private static final Configuration CNF_VALUE = Configuration.defaultConfiguration()
            .mappingProvider(new JacksonMappingProvider())
            .addOptions(Option.SUPPRESS_EXCEPTIONS, Option.ALWAYS_RETURN_LIST);

    private static final Configuration CNF_PATH = Configuration.defaultConfiguration()
            .mappingProvider(new JacksonMappingProvider())
            .addOptions(Option.SUPPRESS_EXCEPTIONS, Option.ALWAYS_RETURN_LIST, Option.AS_PATH_LIST);

    /**
     * Populate {@link ActivityLog} from {@link ActivityLogEvent}.
     *
     * @param event {@link ActivityLogEvent}
     * @return {@link ActivityLog}
     */
    public ActivityLog populate(ActivityLogEvent event) {
        return mappingContext.callWithAutoClear(() -> {
            ActivityLog entity = ActivityLogMapper.INSTANCE.toEntity(event);

            var enhancedDetails = handleDetails(event.getClassName(), event.getDetails());
            entity.setDetails(enhancedDetails);

            // todo: informer 获取 name 也可以使用 mapper context 中的缓存
            for (Informer<?> informer : informers) {
                if (informer.support(entity.getResourceType())) {
                    ActivityLogNameInfo info = informer.fetch(entity.getResourceId(), entity.getOperatorId());
                    Optional.ofNullable(info.resource()).ifPresent(resource -> {
                        entity.setResourceId(resource.id());
                        entity.setResourceName(resource.name());
                        entity.setResourceType(resource.resourceType());
                    });
                    Optional.ofNullable(info.operator()).ifPresent(operator -> {
                        entity.setOperatorId(operator.id());
                        entity.setOperatorName(operator.name());
                    });
                    Optional.ofNullable(info.owner()).ifPresent(owner -> {
                        entity.setOwnerId(owner.id());
                        entity.setOwnerName(owner.name());
                    });
                    break;
                }
            }
            return entity;
        });
    }

    public String handleDetails(String className, String details) {
        log.info("class name: {}, details: {}", className, details);
        if (!StringUtils.hasText(details)) {
            return details;
        }
        // 根据类名（如有）获取映射规则
        var mappings = fieldMappingRuleProperties.getMappingRules(className);
        if (CollectionUtils.isEmpty(mappings)) {
            return details;
        }

        var jsonValue = JsonPath.using(CNF_VALUE).parse(details);
        var jsonPath = JsonPath.using(CNF_PATH).parse(details);
        for (var mapping : mappings) {
            if (mapping.isList()) {
                mapList(mapping.getPathPattern(), mapping.getMapperName(), jsonValue, jsonPath);
            } else {
                mapValue(mapping.getPathPattern(), mapping.getMapperName(), jsonValue, jsonPath);
            }
        }

        return jsonValue.jsonString();
    }

    private void mapValue(
            JsonPath pathPattern, MapperName mapperName, DocumentContext jsonValue, DocumentContext jsonPath) {
        // 1. 查找给定路径模版下的所有值，以及其路径 (两个 list 的 size 应当是一样的)
        List<String> fromValues = jsonValue.read(pathPattern, new TypeRef<>() {});
        List<String> fromPaths = jsonPath.read(pathPattern, new TypeRef<>() {});
        if (CollectionUtils.isEmpty(fromValues)) {
            return;
        }

        // 2. 批量查询是否有对应的映射值
        Map<String, String> toValues = mappingContext.getMappingResult(mapperName, fromValues);
        if (CollectionUtils.isEmpty(toValues)) {
            return;
        }

        // 3. 如果有映射值，则增加一个 key，比如：$.['customerId'] -> $.['customerId_name']
        for (int i = 0; i < fromValues.size(); i++) {
            var fromValue = fromValues.get(i);
            if (!StringUtils.hasText(fromValue)) {
                continue;
            }
            var toValue = toValues.get(fromValue);
            if (!StringUtils.hasText(toValue)) {
                continue;
            }

            var fromPath = fromPaths.get(i);
            addJsonValue(jsonValue, fromPath, toValue);
        }
    }

    private void mapList(
            JsonPath pathPattern, MapperName mapperName, DocumentContext jsonValue, DocumentContext jsonPath) {
        // 1. 查找给定路径模版下的所有值，以及其路径 (两个 list 的 size 应当是一样的)
        List<List<String>> fromValueLists = jsonValue.read(pathPattern, new TypeRef<>() {});
        List<String> fromPaths = jsonPath.read(pathPattern, new TypeRef<>() {});
        if (CollectionUtils.isEmpty(fromValueLists)) {
            return;
        }

        // 2. 批量查询是否有对应的映射值
        List<String> fromValues = fromValueLists.stream()
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .distinct()
                .toList();

        Map<String, String> toValues = mappingContext.getMappingResult(mapperName, fromValues);
        if (CollectionUtils.isEmpty(toValues)) {
            return;
        }

        // 3. 如果有映射值，则增加一个 key，比如：$.['customerId'] -> $.['customerId_name']
        for (int i = 0; i < fromValueLists.size(); i++) {
            var fromValueList = fromValueLists.get(i);
            if (CollectionUtils.isEmpty(fromValueList)) {
                continue;
            }

            var toValue = fromValueList.stream().map(toValues::get).collect(Collectors.joining(", "));

            var fromPath = fromPaths.get(i);
            addJsonValue(jsonValue, fromPath, toValue);
        }
    }

    private void addJsonValue(DocumentContext json, String fromPath, String toValue) {
        var idx = fromPath.lastIndexOf("[");
        if (idx < 0) {
            log.warn("Invalid path: {}", fromPath);
            return;
        }

        var parentPath = fromPath.substring(0, idx);

        // 去掉 [' 和 ']，然后加上 _name
        var keyName = fromPath.substring(idx + 2, fromPath.length() - 2) + "_name";
        json.put(parentPath, keyName, toValue);
    }
}
