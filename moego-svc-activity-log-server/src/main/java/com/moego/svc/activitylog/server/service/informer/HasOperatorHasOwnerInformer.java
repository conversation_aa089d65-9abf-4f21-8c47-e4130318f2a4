package com.moego.svc.activitylog.server.service.informer;

import static com.moego.svc.activitylog.server.util.Options.justDo;

import com.moego.svc.activitylog.server.service.model.ActivityLogNameInfo;
import com.moego.svc.activitylog.server.service.model.ActivityLogOperator;
import com.moego.svc.activitylog.server.service.model.ActivityLogOwner;
import com.moego.svc.activitylog.server.service.model.ActivityLogResource;

/**
 * Has operator and owner informer.
 *
 * @param <Resource> resource type
 * @param <Operator> operator type
 * @param <Owner>    owner type
 * <AUTHOR>
 */
public interface HasOperatorHasOwnerInformer<Resource, Operator, Owner>
        extends Informer<Resource>, HasOperator<Operator>, HasOwner<Owner> {

    String getOwnerId(Resource resource);

    @Override
    default ActivityLogNameInfo fetch(String resourceId, String operatorId) {
        Resource resource = resourceId != null ? justDo(() -> resource(resourceId)) : null;
        Operator operator = operatorId != null ? justDo(() -> operator(operatorId)) : null;
        String ownerId = resource != null ? justDo(() -> getOwnerId(resource)) : null;
        Owner owner = ownerId != null ? justDo(() -> owner(ownerId)) : null;
        return new ActivityLogNameInfo(
                resource != null ? new ActivityLogResource(resourceId, resourceType(), resourceName(resource)) : null,
                operator != null ? new ActivityLogOperator(operatorId, operatorName(operator)) : null,
                owner != null ? new ActivityLogOwner(ownerId, ownerName(owner)) : null);
    }
}
