package com.moego.svc.activitylog.server.service.informer;

/**
 * @param <O> operator type
 * <AUTHOR>
 */
public interface HasOperator<O> {
    /**
     * Get operator.
     *
     * @param operatorId operator id
     * @return operator
     */
    O operator(String operatorId);

    /**
     * Get human-readable operator name.
     *
     * @param operator operator
     * @return human-readable operator name
     */
    String operatorName(O operator);
}
