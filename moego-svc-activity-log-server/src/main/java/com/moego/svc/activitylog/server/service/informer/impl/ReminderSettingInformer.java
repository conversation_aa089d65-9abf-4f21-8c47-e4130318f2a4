package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.message.enums.ReminderTypeEnum;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorInformer;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#REMINDER_SETTING}.
 *
 * <AUTHOR>
 */
@Component
public class ReminderSettingInformer extends AbstractStaffOperatorInformer<ReminderTypeEnum> {

    @Override
    public String resourceType() {
        return ResourceType.REMINDER_SETTING.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public ReminderTypeEnum resource(String type) {
        return ReminderTypeEnum.getByReminderType(Integer.parseInt(type));
    }

    @Override
    public String resourceName(ReminderTypeEnum reminderTypeEnum) {
        return reminderTypeEnum.getDesc();
    }
}
