package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.customer.api.IPetService;
import com.moego.server.grooming.api.IPetCustomizedServiceService;
import com.moego.server.grooming.dto.PetCustomizedServiceDTO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorPetOwnerInformer;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#PET_CUSTOMIZED_SERVICE}.
 *
 * <AUTHOR>
 */
@Component
public class PetCustomizedServiceInformer extends AbstractStaffOperatorPetOwnerInformer<PetCustomizedServiceDTO> {

    private final IPetCustomizedServiceService petCustomizedServiceApi;

    public PetCustomizedServiceInformer(
            IBusinessStaffService staffApi, IPetService petApi, IPetCustomizedServiceService petCustomizedServiceApi) {
        super(staffApi, petApi);
        this.petCustomizedServiceApi = petCustomizedServiceApi;
    }

    @Override
    public String resourceType() {
        return ResourceType.PET_CUSTOMIZED_SERVICE.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public String getOwnerId(PetCustomizedServiceDTO petCustomizedServiceDTO) {
        return String.valueOf(petCustomizedServiceDTO.getPetId());
    }

    @Override
    public PetCustomizedServiceDTO resource(String resourceId) {
        return petCustomizedServiceApi.getPetCustomizedServiceById(Integer.parseInt(resourceId));
    }

    @Override
    public String resourceName(PetCustomizedServiceDTO petCustomizedServiceDTO) {
        return null;
    }
}
