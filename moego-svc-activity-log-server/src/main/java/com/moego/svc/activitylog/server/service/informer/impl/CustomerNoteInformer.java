package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.customer.api.ICustomerNoteService;
import com.moego.server.customer.dto.CustomerNoteDto;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorCustomerOwnerInformer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#CUSTOMER_NOTE}.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class CustomerNoteInformer extends AbstractStaffOperatorCustomerOwnerInformer<CustomerNoteDto> {

    private final ICustomerNoteService customerNoteApi;

    @Override
    public String resourceType() {
        return ResourceType.CUSTOMER_NOTE.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public String getOwnerId(CustomerNoteDto customerNoteDto) {
        return String.valueOf(customerNoteDto.getCustomerId());
    }

    @Override
    public CustomerNoteDto resource(String resourceId) {
        return customerNoteApi.getCustomerNoteById(Integer.parseInt(resourceId));
    }

    @Override
    public String resourceName(CustomerNoteDto customerNoteDto) {
        return customerNoteDto.getNote();
    }
}
