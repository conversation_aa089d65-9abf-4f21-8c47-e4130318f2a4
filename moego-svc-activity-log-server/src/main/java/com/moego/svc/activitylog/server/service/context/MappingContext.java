package com.moego.svc.activitylog.server.service.context;

import com.moego.svc.activitylog.server.service.mapper.Mapper;
import com.moego.svc.activitylog.server.service.mapper.MapperName;
import jakarta.annotation.PostConstruct;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Component
@AllArgsConstructor
public class MappingContext {

    private final ApplicationContext applicationContext;

    private final Map<MapperName, Mapper<?>> mapperMap = new EnumMap<>(MapperName.class);

    @PostConstruct
    private void initMapperMap() {
        for (MapperName mapperName : MapperName.values()) {
            var mapperClass = mapperName.getMapperClass();
            if (mapperClass != null) {
                var mapper = applicationContext.getBean(mapperClass);
                mapperMap.put(mapperName, mapper);
            }
        }
    }

    private final ThreadLocal<Map<MapperName, Map<String, String>>> resultCacheMap =
            ThreadLocal.withInitial(HashMap::new);

    public Map<String, String> getMappingResult(MapperName mapperName, List<String> valueList) {
        Map<String, String> result = new HashMap<>(valueList.size());

        if (CollectionUtils.isEmpty(valueList)) {
            return result;
        }

        var resultCache = resultCacheMap.get().get(mapperName);
        Set<String> missValues = new HashSet<>();

        // mapperName 第一次使用时，需要为其创建一个缓存 map
        if (resultCache == null) {
            resultCache = new HashMap<>();
            resultCacheMap.get().put(mapperName, resultCache);
        }

        for (String fromValue : valueList) {
            // valueList 里可能包含空值，需要过滤掉
            if (!StringUtils.hasText(fromValue)) {
                continue;
            }

            // 如果已经缓存了，直接取出来，否则放到 missValueList 中，需要查询
            var toValue = resultCache.get(fromValue);
            if (toValue != null) {
                result.put(fromValue, toValue);
            } else {
                missValues.add(fromValue);
            }
        }

        if (CollectionUtils.isEmpty(missValues)) {
            return result;
        }

        // 找到对应的 mapper 查询没命中缓存的值，然后放到缓存和结果中
        var mapper = mapperMap.get(mapperName);
        if (mapper == null) {
            log.warn("No mapper found for mapper name: {}", mapperName);
            return result;
        }

        var valueMap = mapper.map(missValues);
        resultCache.putAll(valueMap);
        result.putAll(valueMap);

        return result;
    }

    /**
     * 需要使用 mapping context 的方法应当作为 callable 传入，这样可以保证在方法执行完毕后清除缓存。
     * callable 抛出的异常会被包装成 RuntimeException 抛出。
     *
     * @param callable 需要使用 mapping context 的方法
     * @return callable 方法的返回值
     * @param <T> callable 方法的返回值类型
     */
    @SneakyThrows
    public <T> T callWithAutoClear(Callable<T> callable) {
        try {
            return callable.call();
        } finally {
            resultCacheMap.remove();
        }
    }
}
