package com.moego.svc.activitylog.server.service.mapper.impl;

import com.moego.server.grooming.api.IGroomingServiceService;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.params.CommonIdsParams;
import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
@AllArgsConstructor
public class GroomingServiceIdMapper implements Mapper<MoeGroomingServiceDTO> {

    private final IGroomingServiceService groomingServiceApi;

    /**
     * Map service ids to service names
     * @param serviceIds service ids
     * @return map of service id -> service name
     */
    @Override
    public Map<String, String> map(Set<String> serviceIds) {
        Map<String, String> result = new HashMap<>(serviceIds.size());

        var intServiceIdList = serviceIds.stream().map(Integer::parseInt).toList();

        CommonIdsParams params = new CommonIdsParams();
        params.setIds(intServiceIdList);

        try {
            var response = groomingServiceApi.getServicesByServiceIds(params);
            if (response == null || CollectionUtils.isEmpty(response.getData())) {
                return result;
            }

            response.getData().forEach(service -> {
                var id = String.valueOf(service.getId());
                var name = getName(service);
                result.put(id, name);
            });

        } catch (Exception e) {
            log.error("Failed to get grooming service name for service id: {}", serviceIds, e);
        }

        return result;
    }

    @Override
    public String getName(MoeGroomingServiceDTO service) {
        return service.getName();
    }
}
