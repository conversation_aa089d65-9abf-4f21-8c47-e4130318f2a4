package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorInformer;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#AUTO_REPLY_SETTING}.
 *
 * <AUTHOR>
 */
@Component
public class AutoReplySettingInformer extends AbstractStaffOperatorInformer<String> {

    @Override
    public String resourceType() {
        return ResourceType.AUTO_REPLY_SETTING.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }
}
