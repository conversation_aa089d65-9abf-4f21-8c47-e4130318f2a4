package com.moego.svc.activitylog.server.service.mapper.impl;

import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class PetTypeMapper implements Mapper<String> {
    private static final Map<String, String> VALUE_MAP = Map.ofEntries(
            Map.entry("1", "Dog"),
            Map.entry("2", "Cat"),
            Map.entry("3", "<PERSON>"),
            Map.entry("4", "Rabbit"),
            Map.entry("5", "Guinea pig"),
            Map.entry("6", "Horse"),
            Map.entry("7", "Rat"),
            Map.entry("8", "Mouse"),
            Map.entry("9", "Hamster"),
            Map.entry("10", "Chinchilla"),
            Map.entry("11", "Other"));

    @Override
    public Map<String, String> map(Set<String> values) {
        return VALUE_MAP;
    }

    @Override
    public String getName(String value) {
        return VALUE_MAP.get(value);
    }
}
