package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.payment.client.IPaymentCreditCardClient;
import com.moego.server.payment.dto.CardDTO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorCustomerOwnerInformer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PaymentStripeCardInformer extends AbstractStaffOperatorCustomerOwnerInformer<CardDTO> {
    private final IPaymentCreditCardClient creditCardClient;

    @Override
    public CardDTO resource(String resourceId) {
        return creditCardClient.getByCardId(resourceId);
    }

    @Override
    public String resourceType() {
        return ResourceType.STRIPE_CARD.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public String getOwnerId(CardDTO card) {
        return String.valueOf(card.getCustomerId());
    }
}
