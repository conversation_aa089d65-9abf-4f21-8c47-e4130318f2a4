package com.moego.svc.activitylog.server.service.mapper.impl;

import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class CustomerContactTypeMapper implements Mapper<String> {
    private static final Map<String, String> VALUE_MAP = Map.of(
            "1", "customer owner",
            "2", "additional contact");

    @Override
    public Map<String, String> map(Set<String> values) {
        return VALUE_MAP;
    }

    @Override
    public String getName(String value) {
        return VALUE_MAP.get(value);
    }
}
