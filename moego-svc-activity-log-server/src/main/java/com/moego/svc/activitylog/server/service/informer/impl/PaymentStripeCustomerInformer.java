package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.payment.client.IPaymentCreditCardClient;
import com.moego.server.payment.dto.CustomerDTO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorCustomerOwnerInformer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PaymentStripeCustomerInformer extends AbstractStaffOperatorCustomerOwnerInformer<CustomerDTO> {

    private final IPaymentCreditCardClient creditCardClient;

    @Override
    public String resourceType() {
        return ResourceType.STRIPE_CUSTOMER.toString();
    }

    @Override
    public String getOwnerId(CustomerDTO moeStripeCustomerDTO) {
        return String.valueOf(moeStripeCustomerDTO.getCustomerId());
    }

    @Override
    public CustomerDTO resource(String resourceId) {
        return creditCardClient.getByStripeCustomerId(resourceId);
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }
}
