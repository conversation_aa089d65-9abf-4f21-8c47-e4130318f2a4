package com.moego.svc.activitylog.server.service.mapper.impl;

import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class ReminderBeforeDayMapper implements Mapper<String> {

    private static final Map<String, String> VALUE_MAP = Map.ofEntries(
            Map.entry("0", "same day"),
            Map.entry("1", "1 day"),
            Map.entry("2", "2 days"),
            Map.entry("3", "3 days"),
            Map.entry("4", "4 days"),
            Map.entry("5", "5 days"),
            Map.entry("6", "6 days"),
            Map.entry("7", "7 days"),
            Map.entry("8", "Do not send / switch off"),
            Map.entry("14", "2 weeks"),
            Map.entry("21", "3 weeks"),
            Map.entry("28", "4 weeks"));

    @Override
    public Map<String, String> map(Set<String> values) {
        return VALUE_MAP;
    }

    @Override
    public String getName(String value) {
        return VALUE_MAP.get(value);
    }
}
