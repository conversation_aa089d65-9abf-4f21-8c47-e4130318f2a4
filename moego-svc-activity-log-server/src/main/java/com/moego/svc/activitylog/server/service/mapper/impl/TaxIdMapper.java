package com.moego.svc.activitylog.server.service.mapper.impl;

import com.moego.server.business.client.IBusinessTaxClient;
import com.moego.server.business.dto.MoeBusinessTaxDto;
import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
@AllArgsConstructor
public class TaxIdMapper implements Mapper<MoeBusinessTaxDto> {

    private final IBusinessTaxClient taxApi;

    @Override
    public Map<String, String> map(Set<String> taxIds) {
        Map<String, String> result = new HashMap<>(taxIds.size());

        var intTaxIdList = taxIds.stream().map(Integer::parseInt).toList();

        try {
            var taxList = taxApi.getTaxListByIds(intTaxIdList);
            if (CollectionUtils.isEmpty(taxList)) {
                return result;
            }

            taxList.forEach(tax -> {
                var id = String.valueOf(tax.getId());
                var name = getName(tax);
                result.put(id, name);
            });

        } catch (Exception e) {
            log.error("Failed to get tax name for tax id: {}", taxIds, e);
        }

        return result;
    }

    @Override
    public String getName(MoeBusinessTaxDto tax) {
        return "%s (%s%%)".formatted(tax.getTaxName(), tax.getTaxRate());
    }
}
