package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.idl.models.account.v1.AccountModel;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorInformer;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#ACCOUNT}.
 *
 * <AUTHOR>
 */
@Component
public class AccountInformer extends AbstractStaffOperatorInformer<AccountModel> {

    @Override
    public String resourceType() {
        return ResourceType.ACCOUNT.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }
}
