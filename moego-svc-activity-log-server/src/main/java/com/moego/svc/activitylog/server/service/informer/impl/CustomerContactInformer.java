package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.customer.api.ICustomerContactService;
import com.moego.server.customer.dto.CustomerContactDto;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorCustomerOwnerInformer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#CUSTOMER_CONTACT}.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class CustomerContactInformer extends AbstractStaffOperatorCustomerOwnerInformer<CustomerContactDto> {

    private final ICustomerContactService customerContactApi;

    @Override
    public CustomerContactDto resource(String resourceId) {
        return customerContactApi.getCustomerContactById(Integer.parseInt(resourceId));
    }

    @Override
    public String resourceName(CustomerContactDto customerContactDto) {
        return customerContactDto.getFirstName() + " " + customerContactDto.getLastName();
    }

    @Override
    public String resourceType() {
        return ResourceType.CUSTOMER_CONTACT.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public String getOwnerId(CustomerContactDto customerContactDto) {
        return String.valueOf(customerContactDto.getCustomerId());
    }
}
