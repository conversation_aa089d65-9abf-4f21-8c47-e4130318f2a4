package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.customer.api.IPetService;
import com.moego.server.customer.api.IPetVaccineBindingService;
import com.moego.server.customer.dto.PetVaccineBindingDTO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorPetOwnerInformer;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#PET_VACCINE_BINDING}.
 *
 * <AUTHOR>
 */
@Component
public class PetVaccineBindingInformer extends AbstractStaffOperatorPetOwnerInformer<PetVaccineBindingDTO> {

    private final IPetVaccineBindingService petVaccineBindingApi;

    public PetVaccineBindingInformer(
            IBusinessStaffService staffApi, IPetService petApi, IPetVaccineBindingService petVaccineBindingApi) {
        super(staffApi, petApi);
        this.petVaccineBindingApi = petVaccineBindingApi;
    }

    @Override
    public String resourceType() {
        return ResourceType.PET_VACCINE_BINDING.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public String getOwnerId(PetVaccineBindingDTO petVaccineBindingDTO) {
        return String.valueOf(petVaccineBindingDTO.getPetId());
    }

    @Override
    public PetVaccineBindingDTO resource(String resourceId) {
        return petVaccineBindingApi.getPetVaccineBindingById(Integer.parseInt(resourceId));
    }

    @Override
    public String resourceName(PetVaccineBindingDTO petVaccineBindingDTO) {
        return "Expired at " + petVaccineBindingDTO.getExpirationDate();
    }
}
