package com.moego.svc.activitylog.server.util;

import lombok.experimental.UtilityClass;
import xyz.downgoon.snowflake.Snowflake;

/**
 * Id util.
 *
 * <AUTHOR>
 */
@UtilityClass
public class IdUtil {

    private static final Snowflake SNOWFLAKE;

    static {
        // Do not use the same workerId in different instances !
        double dataCenter = Math.random() * 32;
        double workerId = Math.random() * 32;
        SNOWFLAKE = new Snowflake((int) dataCenter, (int) workerId);
    }

    /**
     * Get next id, implemented by Snowflake id.
     *
     * @return id
     * @see <a href="https://github.com/twitter-archive/snowflake">Snowflake</a>
     */
    public static String nextId() {
        return String.valueOf(SNOWFLAKE.nextId());
    }
}
