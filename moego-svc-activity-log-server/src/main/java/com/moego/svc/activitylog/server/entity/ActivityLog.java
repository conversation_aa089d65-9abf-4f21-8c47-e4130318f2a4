package com.moego.svc.activitylog.server.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName("all_activity_log")
public class ActivityLog {

    /**
     * id
     */
    @TableId
    private String id;

    /**
     * business_id
     */
    private Long businessId;

    /**
     * operator_id
     */
    private String operatorId;

    /**
     * operator_name
     */
    private String operatorName;

    /**
     * action
     */
    private String action;

    /**
     * resource_type
     */
    private String resourceType;

    /**
     * resource_id
     */
    private String resourceId;

    /**
     * resource_name
     */
    private String resourceName;

    /**
     * owner_id
     */
    private String ownerId;

    /**
     * owner_name
     */
    private String ownerName;

    /**
     * time, milliseconds
     */
    private Date time;

    /**
     * updated value, JSON value
     */
    private String details;

    /**
     * is root activity log
     */
    private Boolean isRoot;

    /**
     * request_id
     */
    private String requestId;

    /**
     * created time
     */
    private Date createdAt;

    /**
     * company id
     */
    private Long companyId;
}
