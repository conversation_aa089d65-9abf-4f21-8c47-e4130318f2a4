package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorInformer;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#CUSTOMER_TAG}.
 *
 * <AUTHOR>
 */
@Component
public class CustomerTagInformer extends AbstractStaffOperatorInformer<String> {

    @Override
    public String resourceType() {
        return ResourceType.CUSTOMER_TAG.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }
}
