package com.moego.svc.activitylog.server.service.informer;

import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.StaffIdParams;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Has staff operator informer.
 *
 * @param <Resource> resource type
 */
public abstract class AbstractStaffOperatorInformer<Resource> implements HasOperatorInformer<Resource, MoeStaffDto> {

    private IBusinessStaffService staffApi;

    @Autowired
    public void setStaffApi(IBusinessStaffService staffApi) {
        this.staffApi = staffApi;
    }

    @Override
    public MoeStaffDto operator(String operatorId) {
        StaffIdParams params = new StaffIdParams();
        params.setStaffId(Integer.valueOf(operatorId));
        return staffApi.getStaff(params);
    }

    @Override
    public String operatorName(MoeStaffDto operator) {
        return operator.getFirstName() + " " + operator.getLastName();
    }
}
