package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorInformer;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#PET_VACCINE}.
 *
 * <AUTHOR>
 */
@Component
public class PetVaccineInformer extends AbstractStaffOperatorInformer<String> {

    @Override
    public String resourceType() {
        return ResourceType.PET_VACCINE.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }
}
