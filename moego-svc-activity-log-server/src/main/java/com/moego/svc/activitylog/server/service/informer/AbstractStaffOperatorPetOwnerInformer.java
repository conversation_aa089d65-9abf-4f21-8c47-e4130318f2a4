package com.moego.svc.activitylog.server.service.informer;

import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.StaffIdParams;
import com.moego.server.customer.api.IPetService;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.util.CollectionUtils;

/**
 * Has staff operator and customer owner informer.
 *
 * @param <Resource> resource type
 */
@AllArgsConstructor
public abstract class AbstractStaffOperatorPetOwnerInformer<Resource>
        implements HasOperatorHasOwnerInformer<Resource, MoeStaffDto, CustomerPetDetailDTO> {

    private final IBusinessStaffService staffApi;
    private final IPetService petApi;

    @Override
    public MoeStaffDto operator(String operatorId) {
        StaffIdParams params = new StaffIdParams();
        params.setStaffId(Integer.valueOf(operatorId));
        return staffApi.getStaff(params);
    }

    @Override
    public String operatorName(MoeStaffDto operator) {
        return operator.getFirstName() + " " + operator.getLastName();
    }

    @Override
    public CustomerPetDetailDTO owner(String ownerId) {
        List<CustomerPetDetailDTO> pets = petApi.getCustomerPetListByIdList(List.of(Integer.valueOf(ownerId)));
        return !CollectionUtils.isEmpty(pets) ? pets.get(0) : null;
    }

    @Override
    public String ownerName(CustomerPetDetailDTO pet) {
        return pet.getPetName();
    }
}
