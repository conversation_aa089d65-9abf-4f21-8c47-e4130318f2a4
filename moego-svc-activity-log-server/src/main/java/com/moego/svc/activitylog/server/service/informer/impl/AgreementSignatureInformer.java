package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.HasOperatorInformer;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#AGREEMENT_SIGNATURE}.
 *
 * <AUTHOR>
 */
@Component
public class AgreementSignatureInformer implements HasOperatorInformer<Object, Object> {

    @Override
    public String resourceType() {
        return ResourceType.AGREEMENT_SIGNATURE.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public Object operator(String operatorId) {
        return "Client via shared link";
    }

    @Override
    public String operatorName(Object operator) {
        return "Client via shared link";
    }
}
