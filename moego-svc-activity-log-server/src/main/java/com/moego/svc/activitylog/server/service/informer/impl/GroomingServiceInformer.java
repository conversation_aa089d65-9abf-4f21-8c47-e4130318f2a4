package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.grooming.api.IGroomingServiceService;
import com.moego.server.grooming.dto.GroomingServiceDTO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorInformer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#GROOMING_SERVICE}.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class GroomingServiceInformer extends AbstractStaffOperatorInformer<GroomingServiceDTO> {

    private final IGroomingServiceService groomingServiceApi;

    @Override
    public String resourceType() {
        return ResourceType.GROOMING_SERVICE.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public GroomingServiceDTO resource(String resourceId) {
        return groomingServiceApi.getGroomingServiceById(Integer.parseInt(resourceId));
    }

    @Override
    public String resourceName(GroomingServiceDTO groomingServiceDTO) {
        return groomingServiceDTO.getName();
    }
}
