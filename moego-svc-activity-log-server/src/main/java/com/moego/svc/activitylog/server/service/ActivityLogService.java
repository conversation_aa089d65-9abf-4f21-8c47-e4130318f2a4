package com.moego.svc.activitylog.server.service;

import static com.moego.svc.activitylog.server.util.Options.doWhenNotEmpty;
import static com.moego.svc.activitylog.server.util.Options.doWhenNotNull;
import static java.time.temporal.ChronoUnit.DAYS;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.moego.svc.activitylog.server.entity.ActivityLog;
import com.moego.svc.activitylog.server.repository.ActivityLogRepository;
import com.moego.svc.activitylog.server.service.model.ActivityLogOperator;
import com.moego.svc.activitylog.server.service.model.ActivityLogOwner;
import com.moego.svc.activitylog.server.service.param.SearchActionParam;
import com.moego.svc.activitylog.server.service.param.SearchActivityLogParam;
import com.moego.svc.activitylog.server.service.param.SearchOperatorParam;
import com.moego.svc.activitylog.server.service.param.SearchOwnerParam;
import com.moego.svc.activitylog.server.service.param.SearchResourceTypeParam;
import com.moego.svc.activitylog.server.util.IdUtil;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ActivityLogService extends ServiceImpl<ActivityLogRepository, ActivityLog> {

    private final ActivityLogRepository activityLogRepository;

    /**
     * Insert an {@link ActivityLog}.
     *
     * @param activityLog {@link ActivityLog}
     * @return {@link ActivityLog} with id
     */
    public ActivityLog insert(ActivityLog activityLog) {
        activityLog.setId(IdUtil.nextId());
        activityLog.setCreatedAt(new Date());
        activityLogRepository.insert(activityLog);
        return activityLog;
    }

    /**
     * Get {@link ActivityLog} by id.
     *
     * @param id id
     * @return {@link ActivityLog}
     */
    public ActivityLog getById(String id) {
        return activityLogRepository.selectById(id);
    }

    /**
     * Search {@link ActivityLog} with pagination.
     *
     * @param param {@link SearchActivityLogParam}
     * @param page  {@link Page}
     * @return {@link ActivityLog} with pagination
     */
    public IPage<ActivityLog> search(SearchActivityLogParam param, Page<ActivityLog> page) {
        LambdaQueryWrapper<ActivityLog> query = Wrappers.lambdaQuery();
        doWhenNotEmpty(param.businessIds(), businessIds -> query.in(ActivityLog::getBusinessId, businessIds));
        doWhenNotEmpty(param.operatorIds(), operatorIds -> query.in(ActivityLog::getOperatorId, operatorIds));
        doWhenNotEmpty(param.actions(), actions -> query.in(ActivityLog::getAction, actions));
        doWhenNotEmpty(param.resourceTypes(), resourceTypes -> query.in(ActivityLog::getResourceType, resourceTypes));
        doWhenNotEmpty(param.resourceIds(), resourceIds -> query.in(ActivityLog::getResourceId, resourceIds));
        doWhenNotEmpty(param.ownerIds(), ownerIds -> query.in(ActivityLog::getOwnerId, ownerIds));
        doWhenNotNull(param.startTime(), startTime -> query.ge(ActivityLog::getCreatedAt, startTime));
        doWhenNotNull(param.endTime(), endTime -> query.le(ActivityLog::getCreatedAt, endTime));
        query.orderByDesc(List.of(ActivityLog::getTime, ActivityLog::getCreatedAt, ActivityLog::getId));
        return activityLogRepository.selectPage(page, query);
    }

    /**
     * Search {@link ActivityLogOwner} with pagination.
     *
     * @param param {@link SearchOwnerParam}
     * @param page  {@link Page}
     * @return {@link ActivityLogOwner} with pagination
     */
    public IPage<ActivityLog> searchOwner(SearchOwnerParam param, Page<ActivityLog> page) {
        var query = Wrappers.<ActivityLog>query()
                .select("DISTINCT owner_id, owner_name")
                .lambda();

        query.eq(param.businessId() != null, ActivityLog::getBusinessId, param.businessId())
                .ne(ActivityLog::getOwnerName, "");

        // 如果没有传入 ownerName，则查询最近 15 天的操作记录
        if (StringUtils.hasText(param.ownerName())) {
            query.like(ActivityLog::getOwnerName, param.ownerName());
        } else {
            query.ge(ActivityLog::getCreatedAt, Date.from(Instant.now().minus(15, DAYS)));
        }

        query.orderByAsc(ActivityLog::getOwnerName);

        return activityLogRepository.selectPage(page, query);
    }

    /**
     * Search {@link ActivityLogOperator} with pagination.
     *
     * @param param {@link SearchOperatorParam}
     * @param page  {@link Page}
     * @return {@link ActivityLogOperator} with pagination
     */
    public IPage<ActivityLog> searchOperator(SearchOperatorParam param, Page<ActivityLog> page) {
        var query = Wrappers.<ActivityLog>query()
                .select("DISTINCT operator_id, operator_name")
                .lambda();

        query.eq(param.businessId() != null, ActivityLog::getBusinessId, param.businessId())
                .ne(ActivityLog::getOperatorName, "");

        // 如果没有传入 operatorName，则查询最近 15 天的操作记录
        if (StringUtils.hasText(param.operatorName())) {
            query.like(ActivityLog::getOperatorName, param.operatorName());
        } else {
            query.ge(ActivityLog::getCreatedAt, Date.from(Instant.now().minus(15, DAYS)));
        }

        query.orderByAsc(ActivityLog::getOperatorName);

        return activityLogRepository.selectPage(page, query);
    }

    /**
     * Search resource type with pagination.
     *
     * @param param {@link SearchResourceTypeParam}
     * @param page  {@link Page}
     * @return resource type with pagination
     */
    public IPage<ActivityLog> searchResourceType(SearchResourceTypeParam param, Page<ActivityLog> page) {
        var query =
                Wrappers.<ActivityLog>query().select("DISTINCT resource_type").lambda();

        query.eq(param.businessId() != null, ActivityLog::getBusinessId, param.businessId())
                .ne(ActivityLog::getResourceType, "");

        // 如果没有传入 resourceType，则查询最近 15 天的操作记录
        if (StringUtils.hasText(param.resourceType())) {
            query.like(ActivityLog::getResourceType, param.resourceType());
        } else {
            query.ge(ActivityLog::getCreatedAt, Date.from(Instant.now().minus(15, ChronoUnit.DAYS)));
        }

        query.orderByAsc(ActivityLog::getResourceType);

        return activityLogRepository.selectPage(page, query);
    }

    /**
     * Search action with pagination.
     *
     * @param param {@link SearchActionParam}
     * @param page  {@link Page}
     * @return action with pagination
     */
    public Page<ActivityLog> searchAction(SearchActionParam param, Page<ActivityLog> page) {
        var query = Wrappers.<ActivityLog>query().select("DISTINCT action").lambda();

        query.eq(param.businessId() != null, ActivityLog::getBusinessId, param.businessId())
                .ne(ActivityLog::getAction, "");

        // 如果没有传入 action，则查询最近 15 天的操作记录
        if (StringUtils.hasText(param.action())) {
            query.like(ActivityLog::getAction, param.action());
        } else {
            query.ge(ActivityLog::getCreatedAt, Date.from(Instant.now().minus(15, DAYS)));
        }

        query.orderByAsc(ActivityLog::getAction);

        return activityLogRepository.selectPage(page, query);
    }

    /**
     * Search {@link ActivityLog} by condition.
     *
     * @param entity Search condition, ignore null property, nullable
     * @return List of {@link ActivityLog}
     */
    public List<ActivityLog> search(ActivityLog entity) {
        return activityLogRepository.selectList(Wrappers.lambdaQuery(entity));
    }
}
