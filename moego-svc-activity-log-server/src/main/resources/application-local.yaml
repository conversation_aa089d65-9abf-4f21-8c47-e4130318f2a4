spring:
  datasource:
    driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
    url: ****************************************************************************
    username: 'moego_developer'
    password: 'Ym8CM&rW*n7Zjr!2~0wvlw+yaIOFcyg%-fao=xDc'

moego:
  grpc:
    server:
      debug-enabled: true
  messaging:
    enabled: true
    pulsar:
      service-url: pulsar.t2.moego.pet:40650
      authentication: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      tenant: test2

logging:
  level:
    com.moego.lib.messaging: trace
    com.moego.svc.activitylog.server.repository: debug
