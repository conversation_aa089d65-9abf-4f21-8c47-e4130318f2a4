package com.moego.server.grooming.helper;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.service.order.v1.CancelOrderResponse;
import com.moego.idl.service.order.v1.GetOrderListResponse;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class OrderHelperTest {

    @Mock
    private OrderServiceGrpc.OrderServiceBlockingStub orderClient;

    @InjectMocks
    private OrderHelper orderHelper;

    @Test
    void testCancelNoShowOrderForAppointment_NoOrderFound() {
        Long businessId = 1L;
        Integer appointmentId = 123;
        Long operatorId = 2L;

        OrderHelper orderHelperSpy = spy(orderHelper);
        doReturn(null).when(orderHelperSpy).getAppointmentLatestNoShowOrder(appointmentId);

        orderHelperSpy.cancelNoShowOrderForAppointment(businessId, appointmentId, operatorId);
    }

    @Test
    void testCancelNoShowOrderForAppointment_OrderAlreadyCancelled() {
        Long businessId = 1L;
        Integer appointmentId = 123;
        Long operatorId = 2L;

        OrderHelper orderHelperSpy = spy(orderHelper);
        doReturn(OrderDetailModel.newBuilder()
                        .setOrder(OrderModel.newBuilder()
                                .setStatus(InvoiceStatusEnum.INVOICE_STATUS_REMOVED)
                                .build())
                        .build())
                .when(orderHelperSpy)
                .getAppointmentLatestNoShowOrder(appointmentId);
        orderHelperSpy.cancelNoShowOrderForAppointment(businessId, appointmentId, operatorId);
    }

    @Test
    void testCancelNoShowOrderForAppointment_SuccessfulCancellation() {
        Long businessId = 1L;
        Integer appointmentId = 123;
        Long operatorId = 2L;

        OrderHelper orderHelperSpy = spy(orderHelper);
        doReturn(OrderDetailModel.newBuilder()
                        .setOrder(OrderModel.newBuilder()
                                .setId(1L)
                                .setStatus(InvoiceStatusEnum.INVOICE_STATUS_COMPLETED)
                                .build())
                        .build())
                .when(orderHelperSpy)
                .getAppointmentLatestNoShowOrder(appointmentId);

        doReturn(CancelOrderResponse.getDefaultInstance()).when(orderClient).cancelOrder(any());

        orderHelperSpy.cancelNoShowOrderForAppointment(businessId, appointmentId, operatorId);
    }

    @Test
    void testGetAppointmentLatestNoShowOrders_EmptyInput() {
        List<Integer> groomingIds = List.of();

        var result = orderHelper.getAppointmentLatestNoShowOrders(groomingIds);
        assertThat(result).isEmpty();
    }

    @Test
    void testGetAppointmentLatestNoShowOrders_NoOrdersFound() {
        List<Integer> groomingIds = List.of(1, 2, 3);

        when(orderClient.getOrderList(any()))
                .thenReturn(GetOrderListResponse.newBuilder().build());

        var result = orderHelper.getAppointmentLatestNoShowOrders(groomingIds);

        assertThat(result).isEmpty();
    }

    @Test
    void testGetAppointmentLatestNoShowOrders_WithOrders() {
        List<Integer> groomingIds = List.of(1, 2);

        OrderDetailModel order1 = createOrderDetailModel(1L, 101L);
        OrderDetailModel order2 = createOrderDetailModel(1L, 102L); // 最新订单
        OrderDetailModel order3 = createOrderDetailModel(2L, 201L);

        when(orderClient.getOrderList(any()))
                .thenReturn(GetOrderListResponse.newBuilder()
                        .addAllOrderList(List.of(order1, order2, order3))
                        .build());

        var result = orderHelper.getAppointmentLatestNoShowOrders(groomingIds);
        var expect = Map.of(1, order2, 2, order3);
        assertThat(result).isEqualTo(expect);
    }

    private OrderDetailModel createOrderDetailModel(Long sourceId, Long orderId) {
        OrderModel order =
                OrderModel.newBuilder().setSourceId(sourceId).setId(orderId).build();

        return OrderDetailModel.newBuilder().setOrder(order).build();
    }
}
