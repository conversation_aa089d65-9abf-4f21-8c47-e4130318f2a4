package com.moego.server.grooming.service;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.moego.idl.models.organization.v1.AvailabilityType;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.idl.service.online_booking.v1.UpdateStaffAvailabilityResponse;
import com.moego.idl.service.organization.v1.GetBusinessStaffAvailabilityTypeResponse;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.grooming.mapper.MoeBookOnlineAvailableStaffMapper;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAvailableStaff;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class StaffAvailableSyncServiceTest {

    @Mock
    private MoeBusinessBookOnlineMapper bookOnlineMapper;

    @Mock
    private MoeBookOnlineAvailableStaffMapper bookOnlineAvailableStaffMapper;

    @Mock
    private OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceBlockingStub
            obStaffAvailabilityServiceBlockingStub;

    @Mock
    private IBusinessStaffClient iBusinessStaffClient;

    @Mock
    private com.moego.idl.service.organization.v1.StaffServiceGrpc.StaffServiceBlockingStub staffService;

    @InjectMocks
    private StaffAvailableSyncService staffAvailableSyncService;

    @Test
    public void queryStaffObAvailableTest() {
        assertFalse(staffAvailableSyncService.queryStaffObAvailable(1, 1));
    }

    @Test
    public void asyncAvailableTypeSync() {
        // test null
        staffAvailableSyncService.asyncAvailableTypeSync(1);
        // test not null
        var moeBusinessBookOnline = new MoeBusinessBookOnline();
        moeBusinessBookOnline.setId(1);
        moeBusinessBookOnline.setCompanyId(2L);
        when(bookOnlineMapper.selectByBusinessId(1)).thenReturn(moeBusinessBookOnline);
        when(staffService.getBusinessStaffAvailabilityType(any()))
                .thenReturn(GetBusinessStaffAvailabilityTypeResponse.newBuilder()
                        .setAvailabilityType(AvailabilityType.BY_TIME)
                        .build());
        staffAvailableSyncService.asyncAvailableTypeSync(1);
    }

    @Test
    public void asyncStaffAvailable() {
        // test null
        staffAvailableSyncService.asyncStaffAvailable(1, 1);
        // test not null
        var availableStaff = new MoeBookOnlineAvailableStaff();
        availableStaff.setCompanyId(1L);
        availableStaff.setBusinessId(1);
        availableStaff.setStaffId(1);
        when(bookOnlineAvailableStaffMapper.selectByBusinessIdAndStaffId(1, 1))
                .thenReturn(Collections.singletonList(availableStaff));
        when(obStaffAvailabilityServiceBlockingStub.updateStaffAvailability(any()))
                .thenReturn(UpdateStaffAvailabilityResponse.getDefaultInstance());
        staffAvailableSyncService.asyncStaffAvailable(1, 1);
    }

    @Test
    public void taskSyncAllTimeRecordAndSyncOneStaffTimesRecord() {
        // test syncOneBidAvilable
        when(iBusinessStaffClient.getStaffList(any())).thenReturn(Collections.emptyList());
        staffAvailableSyncService.syncOneBidAvailable(1);
        // test taskSyncAllAvilableRecord
        var ob = new MoeBusinessBookOnline();
        when(bookOnlineMapper.listAll()).thenReturn(Collections.singletonList(ob));
        staffAvailableSyncService.taskSyncAllAvailableRecord();
    }
}
