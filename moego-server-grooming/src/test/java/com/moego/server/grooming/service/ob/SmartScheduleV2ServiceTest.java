package com.moego.server.grooming.service.ob;

import com.moego.server.grooming.service.TimeSlot;
import java.util.ArrayList;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class SmartScheduleV2ServiceTest {

    @InjectMocks
    private SmartScheduleV2Service smartScheduleV2Service;

    @Test
    public void filterTodayTimeSlotFilterTwo() {
        List<TimeSlot> availableTimeSlots = new ArrayList<>();
        TimeSlot ts1 = new TimeSlot();
        ts1.setStart(540);
        ts1.setEnd(600);
        TimeSlot ts2 = new TimeSlot();
        ts2.setStart(720);
        ts2.setEnd(1080);
        availableTimeSlots.add(ts1);
        availableTimeSlots.add(ts2);
        List<TimeSlot> filterTimeSlots = smartScheduleV2Service.filterTodayTimeSlot(500, availableTimeSlots);
        Assertions.assertThat(filterTimeSlots).hasSize(2);
    }

    @Test
    public void filterTodayTimeSlotFilterTwoStart() {
        List<TimeSlot> availableTimeSlots = new ArrayList<>();
        TimeSlot ts1 = new TimeSlot();
        ts1.setStart(540);
        ts1.setEnd(600);
        TimeSlot ts2 = new TimeSlot();
        ts2.setStart(720);
        ts2.setEnd(1080);
        availableTimeSlots.add(ts1);
        availableTimeSlots.add(ts2);
        int nowMinutes = 560;
        List<TimeSlot> filterTimeSlots = smartScheduleV2Service.filterTodayTimeSlot(nowMinutes, availableTimeSlots);
        Assertions.assertThat(filterTimeSlots).hasSize(2);
        Assertions.assertThat(filterTimeSlots.get(0).getStart()).isEqualTo(nowMinutes);
    }

    @Test
    public void filterTodayTimeSlotFilterOne() {
        List<TimeSlot> availableTimeSlots = new ArrayList<>();
        TimeSlot ts1 = new TimeSlot();
        ts1.setStart(540);
        ts1.setEnd(600);
        TimeSlot ts2 = new TimeSlot();
        ts2.setStart(720);
        ts2.setEnd(1080);
        availableTimeSlots.add(ts1);
        availableTimeSlots.add(ts2);
        List<TimeSlot> filterTimeSlots = smartScheduleV2Service.filterTodayTimeSlot(660, availableTimeSlots);
        Assertions.assertThat(filterTimeSlots).hasSize(1);
    }

    @Test
    public void filterTodayTimeSlotMidStartEnd() {
        List<TimeSlot> availableTimeSlots = new ArrayList<>();
        TimeSlot ts1 = new TimeSlot();
        ts1.setStart(540);
        ts1.setEnd(600);
        TimeSlot ts2 = new TimeSlot();
        ts2.setStart(720);
        ts2.setEnd(1080);
        availableTimeSlots.add(ts1);
        availableTimeSlots.add(ts2);
        int nowMinutes = 800;
        List<TimeSlot> filterTimeSlots = smartScheduleV2Service.filterTodayTimeSlot(nowMinutes, availableTimeSlots);
        Assertions.assertThat(filterTimeSlots).hasSize(1);
        Assertions.assertThat(filterTimeSlots.get(0).getStart()).isEqualTo(nowMinutes);
    }

    @Test
    public void filterTodayTimeSlotFilterZero() {
        List<TimeSlot> availableTimeSlots = new ArrayList<>();
        TimeSlot ts1 = new TimeSlot();
        ts1.setStart(540);
        ts1.setEnd(600);
        TimeSlot ts2 = new TimeSlot();
        ts2.setStart(720);
        ts2.setEnd(1080);
        availableTimeSlots.add(ts1);
        availableTimeSlots.add(ts2);
        List<TimeSlot> filterTimeSlots = smartScheduleV2Service.filterTodayTimeSlot(1100, availableTimeSlots);
        Assertions.assertThat(filterTimeSlots).isEmpty();
    }

    @Test
    public void filterTodayTimeSlotFilterEquals() {
        List<TimeSlot> availableTimeSlots = new ArrayList<>();
        TimeSlot ts1 = new TimeSlot();
        ts1.setStart(540);
        ts1.setEnd(540);
        TimeSlot ts2 = new TimeSlot();
        ts2.setStart(600);
        ts2.setEnd(660);
        TimeSlot ts3 = new TimeSlot();
        ts3.setStart(720);
        ts3.setEnd(1080);
        availableTimeSlots.add(ts1);
        availableTimeSlots.add(ts2);
        availableTimeSlots.add(ts3);
        int nowMinutes = 630;
        List<TimeSlot> filterTimeSlots = smartScheduleV2Service.filterTodayTimeSlot(nowMinutes, availableTimeSlots);
        Assertions.assertThat(filterTimeSlots).hasSize(2);
        Assertions.assertThat(filterTimeSlots.get(0).getStart()).isEqualTo(nowMinutes);
    }
}
