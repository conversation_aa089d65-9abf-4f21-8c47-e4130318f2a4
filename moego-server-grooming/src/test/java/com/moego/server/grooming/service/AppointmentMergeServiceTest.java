package com.moego.server.grooming.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.server.grooming.dto.CustomerPetMergeRelationDTO;
import com.moego.server.grooming.dto.PetMergeRelationDTO;
import com.moego.server.grooming.mapper.MoeGroomingPackageMapper;
import com.moego.server.grooming.mapper.ObConfigClientReviewMapper;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.transaction.annotation.Transactional;

@ExtendWith(MockitoExtension.class)
public class AppointmentMergeServiceTest {

    @Mock
    private ObConfigClientReviewMapper obConfigClientReviewMapper;

    @Mock
    private MoeGroomingPackageMapper groomingPackageMapper;

    @InjectMocks
    private AppointmentMergeService appointmentMergeService;

    @Test
    @Transactional
    void mergeCustomerObConfigData_WithoutPets_UpdatesCustomerOnly() {
        // Arrange
        CustomerPetMergeRelationDTO mergeRelation = buildCustomerPetMergeRelationDTO(false);
        when(obConfigClientReviewMapper.selectByExample(any())).thenReturn(List.of());

        // Act
        appointmentMergeService.mergeCustomerObConfigData(mergeRelation);

        // Verify
        verify(obConfigClientReviewMapper).selectByExample(any());
        verify(obConfigClientReviewMapper, never()).updateByExampleSelective(any(), any());
    }

    @Test
    @Transactional
    void mergeCustomerObConfigData_WithPets_UpdatesCustomerAndPets() {
        // Arrange
        CustomerPetMergeRelationDTO mergeRelation = buildCustomerPetMergeRelationDTO(true);
        when(obConfigClientReviewMapper.selectByExample(any())).thenReturn(List.of());

        // Act
        appointmentMergeService.mergeCustomerObConfigData(mergeRelation);

        // Verify
        verify(obConfigClientReviewMapper, atLeast(2)).selectByExample(any());
    }

    private CustomerPetMergeRelationDTO buildCustomerPetMergeRelationDTO(boolean withPet) {
        CustomerPetMergeRelationDTO mergeRelation = new CustomerPetMergeRelationDTO();
        mergeRelation.setCompanyId(1L);
        mergeRelation.setTargetCustomerId(1);
        mergeRelation.setAllLocationIds(List.of(1L, 2L, 3L));
        mergeRelation.setSourceCustomerIds(List.of(2, 3));
        if (withPet) {
            var petDto = new PetMergeRelationDTO();
            petDto.setTargetPetId(1);
            petDto.setSourcePetIds(List.of(2, 3));
            mergeRelation.setPetMergeRelations(List.of(petDto));
        }
        return mergeRelation;
    }
}
