package com.moego.server.grooming.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.server.business.client.IBusinessServiceAreaClient;
import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.params.BatchGetAreasByLocationParams;
import com.moego.server.business.params.GetAreasByLocationParams;
import com.moego.server.customer.dto.GroomingCalenderCustomerInfo;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.WaitListStatusEnum;
import com.moego.server.grooming.mapper.MoeWaitListMapper;
import com.moego.server.grooming.mapper.po.DatePreferencePO;
import com.moego.server.grooming.mapper.po.StaffPreferencePO;
import com.moego.server.grooming.mapper.po.TimePreferencePO;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeWaitList;
import com.moego.server.grooming.params.appointment.PetParams;
import com.moego.server.grooming.service.params.GetAvailableSlotParams;
import com.moego.server.grooming.service.params.GetFreeSlotParams;
import com.moego.server.grooming.service.storage.AppointmentStorageService;
import com.moego.server.grooming.web.params.waitlist.AddWaitListParam;
import com.moego.server.grooming.web.params.waitlist.WaitListPetServiceParam;
import com.moego.server.grooming.web.params.waitlist.WaitListServiceAndOperationParams;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class WaitListServiceTest {
    Long companyId = 1L;
    Long businessId = 10L;
    Long staffId = 100L;

    @Mock
    MoeWaitListMapper moeWaitListMapper;

    @Mock
    MoePetDetailService moePetDetailService;

    @Mock
    AppointmentStorageService appointmentStorageService;

    @Mock
    MoeGroomingAppointmentService moeGroomingAppointmentService;

    @Mock
    MoeGroomingNoteService moeGroomingNoteService;

    @Mock
    AppointmentServiceV2 appointmentServiceV2;

    @Mock
    SmartScheduleV3Service smartScheduleService;

    @Mock
    IBusinessServiceAreaClient iBusinessServiceAreaClient;

    @InjectMocks
    WaitListService service;

    @Test
    void addWaitList() {
        WaitListService spyService = spy(service);
        doReturn(List.of()).when(spyService).waitListPetParams2PetParams(any());
        doReturn("Asia/Shanghai").when(spyService).getBusinessTimeZone(any());
        when(moePetDetailService.buildPetDetailList(any(), any(), any(), any(), any()))
                .thenReturn(new ArrayList<>());
        doNothing()
                .when(appointmentStorageService)
                .insertAppointment(
                        argThat(argument -> {
                            argument.setId(10000);
                            return true;
                        }),
                        any(),
                        any());
        when(moeWaitListMapper.insertSelective(any())).thenReturn(1);
        AddWaitListParam param = AddWaitListParam.builder()
                .businessId(businessId)
                .companyId(companyId)
                .tokenStaffId(staffId)
                .validFrom(LocalDate.parse("2020-10-10"))
                .build();

        spyService.addWaitList(param);

        ArgumentCaptor<MoeGroomingAppointment> appointmentCaptor =
                ArgumentCaptor.forClass(MoeGroomingAppointment.class);
        verify(appointmentStorageService).insertAppointment(appointmentCaptor.capture(), any(), any());
        MoeGroomingAppointment appointment = appointmentCaptor.getValue();
        assertEquals(AppointmentStatusEnum.UNCONFIRMED.getValue(), appointment.getStatus());
        assertEquals(WaitListStatusEnum.WAITLISTONLY, appointment.getWaitListStatus());

        ArgumentCaptor<MoeWaitList> waitListArgumentCaptor = ArgumentCaptor.forClass(MoeWaitList.class);
        verify(moeWaitListMapper).insertSelective(waitListArgumentCaptor.capture());
        MoeWaitList waitList = waitListArgumentCaptor.getValue();
        assertEquals("2020-10-10", waitList.getValidFrom().toString());
    }

    @Test
    void waitListPetParams2PetParams() {
        Integer petId_1 = 1000;
        Integer serviceId_1_1 = 1100;
        Integer serviceId_1_2 = 1200;
        Integer petId_2 = 2000;
        Integer serviceId_2_1 = 2100;
        Integer serviceId_2_2 = 2200;

        List<WaitListPetServiceParam> wPetParams = List.of(
                new WaitListPetServiceParam(
                        petId_1,
                        List.of(
                                new WaitListServiceAndOperationParams(serviceId_1_1),
                                new WaitListServiceAndOperationParams(serviceId_1_2))),
                new WaitListPetServiceParam(
                        petId_2,
                        List.of(
                                new WaitListServiceAndOperationParams(serviceId_2_1),
                                new WaitListServiceAndOperationParams(serviceId_2_2))));
        List<PetParams> result = service.waitListPetParams2PetParams(wPetParams);
        assertEquals(2, result.size());
        assertEquals(petId_1, result.get(0).petId());
        assertEquals(serviceId_1_1, result.get(0).serviceList().get(0).serviceId());
        assertEquals(serviceId_1_2, result.get(0).serviceList().get(1).serviceId());
        assertEquals(petId_2, result.get(1).petId());
        assertEquals(serviceId_2_1, result.get(1).serviceList().get(0).serviceId());
        assertEquals(serviceId_2_2, result.get(1).serviceList().get(1).serviceId());
    }

    @Test
    void checkDate() {
        DatePreferencePO datePreference = new DatePreferencePO();
        datePreference.setDayOfWeek(List.of(1, 3, 5));
        assertTrue(service.checkDate(datePreference, null, null));
        assertTrue(service.checkDate(datePreference, null, List.of("2023-12-08")));
        assertTrue(service.checkDate(datePreference, null, List.of("2023-12-07", "2023-12-08")));
        assertFalse(service.checkDate(datePreference, null, List.of("2023-12-07")));
        assertFalse(service.checkDate(datePreference, null, List.of("2023-12-05", "2023-12-07")));
        assertFalse(service.checkDate(datePreference, true, null));

        datePreference.setDayOfWeek(null);
        datePreference.setExactDate(List.of("2023-12-07", "2023-12-08"));
        assertTrue(service.checkDate(datePreference, null, List.of("2023-12-07")));
        assertTrue(service.checkDate(datePreference, null, List.of("2023-12-05", "2023-12-07")));
        assertFalse(service.checkDate(datePreference, null, List.of("2023-12-05")));
        assertFalse(service.checkDate(datePreference, null, List.of("2023-12-05", "2023-12-06")));
        assertFalse(service.checkDate(datePreference, true, null));

        datePreference.setExactDate(null);
        datePreference.setIsAnyDate(true);
        assertTrue(service.checkDate(datePreference, null, List.of("2023-12-05", "2023-12-06")));
        assertTrue(service.checkDate(datePreference, true, null));
    }

    @Test
    void checkTime() {
        TimePreferencePO timePreference = new TimePreferencePO();
        timePreference.setTimeRange(List.of(new TimeRangeDto(0, 720), new TimeRangeDto(1020, 1440)));
        assertTrue(service.checkTime(timePreference, null, null));
        assertTrue(service.checkTime(timePreference, null, List.of(new TimeRangeDto(0, 720))));
        assertFalse(service.checkTime(timePreference, null, List.of(new TimeRangeDto(720, 1020))));
        assertFalse(service.checkTime(timePreference, true, null));

        timePreference.setTimeRange(null);
        timePreference.setIsAnyTime(true);
        assertTrue(service.checkTime(timePreference, null, List.of(new TimeRangeDto(0, 720))));
        assertTrue(service.checkTime(timePreference, true, null));

        timePreference.setIsAnyTime(null);
        timePreference.setExactStartTime(List.of(540, 1050));
        assertTrue(service.checkTime(timePreference, null, List.of(new TimeRangeDto(0, 720))));
        assertFalse(service.checkTime(timePreference, null, List.of(new TimeRangeDto(720, 1020))));
        assertTrue(service.checkTime(timePreference, null, List.of(new TimeRangeDto(1020, 1440))));
        assertFalse(service.checkTime(timePreference, true, null));

        timePreference.setExactStartTime(List.of(570, 580));
        assertTrue(service.checkTime(
                timePreference, null, List.of(new TimeRangeDto(0, 720), new TimeRangeDto(720, 1020))));
        assertFalse(service.checkTime(
                timePreference, null, List.of(new TimeRangeDto(720, 1020), new TimeRangeDto(1020, 1440))));
    }

    @Test
    void checkStaff() {
        StaffPreferencePO staffPreference = new StaffPreferencePO();
        staffPreference.setStaffIds(List.of(4, 6));
        assertFalse(service.checkStaff(staffPreference, List.of(), null));
        assertFalse(service.checkStaff(staffPreference, List.of(1, 2, 3), null));
        assertFalse(service.checkStaff(staffPreference, List.of(1, 2, 3), List.of()));
        assertTrue(service.checkStaff(staffPreference, List.of(1, 2, 3), List.of(4, 6, 8)));
        assertFalse(service.checkStaff(staffPreference, List.of(1, 2, 3), List.of(4, 8)));
        assertTrue(service.checkStaff(staffPreference, List.of(6), List.of(4, 8)));

        staffPreference.setStaffIds(List.of(2, 4, 6));
        assertTrue(service.checkStaff(staffPreference, List.of(1, 2, 3), null));

        staffPreference.setStaffIds(null);
        staffPreference.setIsAnyone(true);
        assertTrue(service.checkStaff(staffPreference, List.of(1, 3, 5), null));
    }

    @Test
    void serviceAreaFilter() {
        List<MoeWaitList> records = List.of(
                new MoeWaitList() {
                    {
                        setId(100L);
                    }
                },
                new MoeWaitList() {
                    {
                        setId(200L);
                    }
                },
                new MoeWaitList() {
                    {
                        setId(300L);
                    }
                },
                new MoeWaitList() {
                    {
                        setId(400L);
                    }
                },
                new MoeWaitList() {
                    {
                        setId(500L);
                    }
                });
        Map<Long, List<CertainAreaDTO>> waitListServiceAreas = Map.of(
                100L, List.of(new CertainAreaDTO(10L, "", "")),
                200L, List.of(new CertainAreaDTO(10L, "", ""), new CertainAreaDTO(20L, "", "")),
                300L, List.of(new CertainAreaDTO(20L, "", ""), new CertainAreaDTO(30L, "", "")),
                400L, List.of());

        List<MoeWaitList> result = service.serviceAreaFilter(null, null, waitListServiceAreas, records);
        assertEquals(5, result.size());

        result = service.serviceAreaFilter(List.of(10), null, waitListServiceAreas, records);
        assertEquals(2, result.size());
        assertEquals(100L, result.get(0).getId());
        assertEquals(200L, result.get(1).getId());

        result = service.serviceAreaFilter(List.of(10, 30), null, waitListServiceAreas, records);
        assertEquals(3, result.size());
        assertEquals(100L, result.get(0).getId());
        assertEquals(200L, result.get(1).getId());
        assertEquals(300L, result.get(2).getId());

        result = service.serviceAreaFilter(List.of(40), null, waitListServiceAreas, records);
        assertEquals(0, result.size());

        result = service.serviceAreaFilter(null, true, waitListServiceAreas, records);
        assertEquals(2, result.size());
    }

    @Test
    void batchGetAppointment() {
        Long waitList_1 = 10L;
        Long waitList_2 = 20L;
        Long appointmentId_1 = 100L;
        Long appointmentId_2 = 200L;
        Map<Long, MoeGroomingAppointment> result = service.batchGetAppointment(10L, List.of());
        assertEquals(0, result.size());
        when(moeGroomingAppointmentService.getAppointmentByIds(any(), any()))
                .thenReturn(List.of(
                        new MoeGroomingAppointment() {
                            {
                                setId(appointmentId_1.intValue());
                            }
                        },
                        new MoeGroomingAppointment() {
                            {
                                setId(appointmentId_2.intValue());
                            }
                        }));
        result = service.batchGetAppointment(
                10L,
                List.of(
                        new MoeWaitList() {
                            {
                                setId(waitList_1);
                                setAppointmentId(appointmentId_1);
                            }
                        },
                        new MoeWaitList() {
                            {
                                setId(waitList_2);
                                setAppointmentId(appointmentId_2);
                            }
                        }));
        assertEquals(2, result.size());
        assertEquals(appointmentId_1, result.get(waitList_1).getId().longValue());
        assertEquals(appointmentId_2, result.get(waitList_2).getId().longValue());
    }

    @Test
    void batchGetCustomerInfo() {
        Long waitList_1 = 10L;
        Long waitList_2 = 20L;
        Integer customerId_1 = 100;
        Integer customerId_2 = 200;
        Integer appointId_1 = 1000;
        Integer appointId_2 = 2000;
        Map<Long, GroomingCalenderCustomerInfo> result = service.batchGetCustomerInfo(10L, Map.of());
        assertEquals(0, result.size());
        when(appointmentServiceV2.batchGetCustomerInfo(any(), any()))
                .thenReturn(Map.of(
                        appointId_1,
                        new GroomingCalenderCustomerInfo() {
                            {
                                setCustomerId(customerId_1);
                            }
                        },
                        appointId_2,
                        new GroomingCalenderCustomerInfo() {
                            {
                                setCustomerId(customerId_2);
                            }
                        }));
        result = service.batchGetCustomerInfo(
                10L,
                Map.of(
                        waitList_1,
                        new MoeGroomingAppointment() {
                            {
                                setId(appointId_1);
                            }
                        },
                        waitList_2,
                        new MoeGroomingAppointment() {
                            {
                                setId(appointId_2);
                            }
                        }));
        assertEquals(2, result.size());
        assertEquals(customerId_1, result.get(waitList_1).getCustomerId());
        assertEquals(customerId_2, result.get(waitList_2).getCustomerId());
    }

    @Test
    void batchGetPetService() {
        Long waitList_1 = 10L;
        Long waitList_2 = 20L;
        Integer petId_1 = 100;
        Integer petId_2 = 200;
        Integer petId_3 = 300;
        Integer appointId_1 = 1000;
        Integer appointId_2 = 2000;
        Map<Long, Map<Integer, List<MoeGroomingPetDetail>>> result = service.batchGetPetService(0L, Map.of());
        assertEquals(0, result.size());
        when(moePetDetailService.queryMoePetDetailByGroomingIds(any()))
                .thenReturn(List.of(
                        new MoeGroomingPetDetail() {
                            {
                                setGroomingId(appointId_1);
                                setPetId(petId_1);
                            }
                        },
                        new MoeGroomingPetDetail() {
                            {
                                setGroomingId(appointId_1);
                                setPetId(petId_2);
                            }
                        },
                        new MoeGroomingPetDetail() {
                            {
                                setGroomingId(appointId_2);
                                setPetId(petId_3);
                            }
                        }));
        result = service.batchGetPetService(
                0L,
                Map.of(
                        waitList_1,
                        new MoeGroomingAppointment() {
                            {
                                setId(appointId_1);
                                setWaitListStatus(WaitListStatusEnum.APPTANDWAITLIST);
                            }
                        },
                        waitList_2,
                        new MoeGroomingAppointment() {
                            {
                                setId(appointId_2);
                                setWaitListStatus(WaitListStatusEnum.APPTANDWAITLIST);
                            }
                        }));
        assertEquals(2, result.size());
        assertEquals(2, result.get(waitList_1).size());
        assertEquals(1, result.get(waitList_1).get(petId_1).size());
        assertEquals(1, result.get(waitList_1).get(petId_2).size());
        assertEquals(1, result.get(waitList_2).size());
        assertEquals(1, result.get(waitList_2).get(petId_3).size());
    }

    @Test
    void batchGetTicketComment() {
        Long waitList_1 = 10L;
        Long waitList_2 = 20L;
        Long waitList_3 = 30L;
        Integer appointId_1 = 1000;
        Integer appointId_2 = 2000;
        Integer appointId_3 = 3000;
        String comment_1 = "comment_1";
        String comment_2 = "comment_2";
        Map<Long, MoeGroomingNote> result = service.batchGetTicketComment(List.of());
        assertEquals(0, result.size());
        when(moeGroomingNoteService.getNoteListByGroomingIdListAndType(any(), any()))
                .thenReturn(List.of(
                        new MoeGroomingNote() {
                            {
                                setGroomingId(appointId_1);
                                setNote(comment_1);
                            }
                        },
                        new MoeGroomingNote() {
                            {
                                setGroomingId(appointId_2);
                                setNote(comment_2);
                            }
                        }));
        result = service.batchGetTicketComment(List.of(
                new MoeWaitList() {
                    {
                        setId(waitList_1);
                        setAppointmentId(appointId_1.longValue());
                    }
                },
                new MoeWaitList() {
                    {
                        setId(waitList_2);
                        setAppointmentId(appointId_2.longValue());
                    }
                },
                new MoeWaitList() {
                    {
                        setId(waitList_3);
                        setAppointmentId(appointId_3.longValue());
                    }
                }));
        assertEquals(2, result.size());
        assertEquals("comment_1", result.get(waitList_1).getNote());
        assertEquals("comment_2", result.get(waitList_2).getNote());
    }

    @Test
    void batchGetWaitListServiceAreas() {
        Long waitListId_1 = 100L;
        Long waitListId_2 = 200L;
        Long waitListId_3 = 300L;
        Long waitListId_4 = 400L;

        GroomingCalenderCustomerInfo address1 = new GroomingCalenderCustomerInfo();
        address1.setLat("10.0");
        address1.setLng("20.0");
        address1.setZipcode("zipcode");
        GroomingCalenderCustomerInfo address2 = new GroomingCalenderCustomerInfo();
        address2.setLat("30.0");
        address2.setLng("");
        GroomingCalenderCustomerInfo address3 = new GroomingCalenderCustomerInfo();
        address3.setLng("40.0");

        Map<Long, List<CertainAreaDTO>> result = service.batchGetWaitListServiceAreas(1L, Map.of());
        assertEquals(0, result.size());

        Map<Long, GroomingCalenderCustomerInfo> customerInfoMap = new HashMap<>();
        customerInfoMap.put(waitListId_1, address1);
        customerInfoMap.put(waitListId_2, address2);
        customerInfoMap.put(waitListId_3, address3);
        customerInfoMap.put(waitListId_4, null);

        service.batchGetWaitListServiceAreas(1L, customerInfoMap);
        ArgumentCaptor<BatchGetAreasByLocationParams> locationParamsArgumentCaptor =
                ArgumentCaptor.forClass(BatchGetAreasByLocationParams.class);

        verify(iBusinessServiceAreaClient).getAreasByLocation(locationParamsArgumentCaptor.capture());
        assertEquals(1, locationParamsArgumentCaptor.getValue().getLocations().size());
        GetAreasByLocationParams params =
                locationParamsArgumentCaptor.getValue().getLocations().get(0);
        assertEquals(waitListId_1, params.getId());
        assertEquals(10.0, params.getLat());
        assertEquals(20.0, params.getLng());
        assertEquals("zipcode", params.getZipcode());
    }

    @Test
    void getReportDatePreference() {
        assertEquals("", service.getReportDatePreference(null));
        DatePreferencePO po = new DatePreferencePO();
        assertEquals("", service.getReportDatePreference(po));
        po.setDayOfWeek(List.of(0, 1, 3));
        assertEquals("Sunday,Monday,Wednesday", service.getReportDatePreference(po));
        po.setIsAnyDate(true);
        po.setDayOfWeek(null);
        assertEquals("ASAP", service.getReportDatePreference(po));
        po.setExactDate(List.of("2020-10-10"));
        po.setIsAnyDate(null);
        assertEquals("2020-10-10", service.getReportDatePreference(po));
    }

    @Test
    void getReportTimePreference() {
        assertEquals("", service.getReportTimePreference(null));
        TimePreferencePO po = new TimePreferencePO();
        assertEquals("", service.getReportTimePreference(po));
        po.setExactStartTime(List.of(600));
        assertEquals("600", service.getReportTimePreference(po));
        po.setTimeRange(List.of(new TimeRangeDto(0, 720), new TimeRangeDto(1020, 1440)));
        po.setExactStartTime(null);
        assertEquals("Morning,Evening", service.getReportTimePreference(po));
        po.setIsAnyTime(true);
        po.setTimeRange(null);
        assertEquals("Anytime", service.getReportTimePreference(po));
    }

    @Test
    void getReportStaffPreference() {
        assertEquals("", service.getReportStaffPreference(null, new HashMap<>()));
        StaffPreferencePO po = new StaffPreferencePO();
        assertEquals("", service.getReportStaffPreference(po, new HashMap<>()));
        po.setStaffIds(List.of(1, 2, 3));
        Map<Integer, MoeStaffDto> staffMap = Map.of(
                1,
                new MoeStaffDto() {
                    {
                        setFirstName("first_1");
                        setLastName("last_1");
                    }
                },
                2,
                new MoeStaffDto() {
                    {
                        setFirstName("first_2");
                        setLastName("last_2");
                    }
                },
                3,
                new MoeStaffDto() {
                    {
                        setFirstName("first_3");
                        setLastName("last_3");
                    }
                });
        assertEquals("first_1 last_1,first_2 last_2,first_3 last_3", service.getReportStaffPreference(po, staffMap));
        po.setIsAnyone(true);
        po.setStaffIds(null);
        assertEquals("Anyone", service.getReportStaffPreference(po, staffMap));
    }

    @Test
    void getSlotPerWaitList() {
        WaitListService spyService = spy(service);
        MoeWaitList record = new MoeWaitList() {
            {
                setValidFrom(LocalDate.parse("2024-01-13"));
                setValidTill(LocalDate.parse("2024-01-14"));
                setStaffPreference(new StaffPreferencePO() {
                    {
                        setStaffIds(List.of(1));
                    }
                });
            }
        };
        LocalDateTime availableStartTime = LocalDateTime.of(LocalDate.parse("2024-01-12"), LocalTime.MIN);
        LocalDateTime availableEndTime = LocalDateTime.of(LocalDate.parse("2024-01-15"), LocalTime.MAX);
        Map<Integer, List<MoeGroomingPetDetail>> waitListPetDetailMap = Map.of(
                1,
                        List.of(new MoeGroomingPetDetail() {
                            {
                                setPetId(1);
                                setServiceId(1);
                            }
                        }),
                2,
                        List.of(new MoeGroomingPetDetail() {
                            {
                                setPetId(2);
                                setServiceId(2);
                            }
                        }));
        assertEquals(
                0,
                spyService
                        .getSlotPerWaitList(
                                record,
                                null,
                                availableStartTime,
                                availableEndTime,
                                List.of(),
                                null,
                                null,
                                waitListPetDetailMap)
                        .size());
        assertEquals(
                0,
                spyService
                        .getSlotPerWaitList(
                                record,
                                null,
                                availableStartTime,
                                availableEndTime,
                                List.of(1, 2),
                                null,
                                null,
                                new HashMap<>())
                        .size());
        assertEquals(
                0,
                spyService
                        .getSlotPerWaitList(
                                record,
                                null,
                                availableStartTime,
                                availableEndTime,
                                List.of(1, 2),
                                null,
                                null,
                                Map.of(1, List.of(new MoeGroomingPetDetail() {
                                    {
                                        setPetId(1);
                                        setServiceId(0);
                                    }
                                })))
                        .size());
        when(moePetDetailService.calculateServiceDuration(any())).thenReturn(10);
        doReturn(new GetFreeSlotParams()).when(spyService).buildGetFreeSlotParams(any(), any(), any(), any(), any());
        when(smartScheduleService.getStaffPerDayFreeTimeSlot(any(), any())).thenReturn(new HashMap<>());
        doReturn(new GetAvailableSlotParams())
                .when(spyService)
                .buildGetAvailableSlotParams(any(), any(), any(), any(), any());
        when(smartScheduleService.getAvailableTimeSlot(any(), any()))
                .thenReturn(List.of(TimeSlot.builder().start(10).end(20).build()));
        assertEquals(
                2,
                spyService
                        .getSlotPerWaitList(
                                record,
                                null,
                                availableStartTime,
                                availableEndTime,
                                List.of(1, 2),
                                null,
                                null,
                                waitListPetDetailMap)
                        .size());
    }

    @Test
    void buildGetFreeSlotParams() {
        List<String> preferenceDate = new ArrayList<>();
        preferenceDate.add("2024-01-12");
        preferenceDate.add("2024-01-13");
        List<TimeRangeDto> preferenceTimeRange = new ArrayList<>();
        preferenceTimeRange.add(new TimeRangeDto(10, 20));
        preferenceTimeRange.add(new TimeRangeDto(50, 60));
        MoeWaitList record = new MoeWaitList() {
            {
                setId(1L);
                setAppointmentId(10L);
                setDatePreference(new DatePreferencePO() {
                    {
                        setExactDate(preferenceDate);
                    }
                });
                setTimePreference(new TimePreferencePO() {
                    {
                        setTimeRange(preferenceTimeRange);
                    }
                });
            }
        };
        GetFreeSlotParams freeSlotParams = service.buildGetFreeSlotParams(record, null, null, null, null);
        assertEquals(2, freeSlotParams.getExpectDateList().size());
        assertEquals(2, freeSlotParams.getExpectTimeRangeList().size());
        assertEquals(1L, freeSlotParams.getLocationId());
        assertEquals(1, freeSlotParams.getFilterAppointmentIdList().size());
        freeSlotParams =
                service.buildGetFreeSlotParams(record, null, null, List.of(LocalDate.parse("2024-01-11")), null);
        assertNull(freeSlotParams);
        freeSlotParams = service.buildGetFreeSlotParams(
                record, null, null, List.of(LocalDate.parse("2024-01-12"), LocalDate.parse("2024-01-15")), null);
        assertEquals(1, freeSlotParams.getExpectDateList().size());
        freeSlotParams = service.buildGetFreeSlotParams(record, null, null, null, List.of(new TimeRangeDto(25, 35)));
        assertNull(freeSlotParams);
        freeSlotParams = service.buildGetFreeSlotParams(
                record, null, null, null, List.of(new TimeRangeDto(15, 25), new TimeRangeDto(55, 65)));
        assertEquals(2, freeSlotParams.getExpectTimeRangeList().size());
        assertEquals(15, freeSlotParams.getExpectTimeRangeList().get(0).getStartTime());
        assertEquals(20, freeSlotParams.getExpectTimeRangeList().get(0).getEndTime());
        assertEquals(55, freeSlotParams.getExpectTimeRangeList().get(1).getStartTime());
        assertEquals(60, freeSlotParams.getExpectTimeRangeList().get(1).getEndTime());
    }
}
