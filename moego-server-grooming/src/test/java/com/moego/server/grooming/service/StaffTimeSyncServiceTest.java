package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.google.type.DayOfWeek;
import com.moego.common.enums.DeleteStatusEnum;
import com.moego.idl.models.organization.v1.ScheduleType;
import com.moego.idl.models.organization.v1.StaffAvailability;
import com.moego.idl.models.organization.v1.TimeAvailabilityDay;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.idl.service.online_booking.v1.UpdateStaffAvailabilityResponse;
import com.moego.server.grooming.mapper.MoeBookOnlinePetLimitBreedBindingMapper;
import com.moego.server.grooming.mapper.MoeBookOnlinePetLimitMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineStaffTimeMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlinePetLimit;
import com.moego.server.grooming.mapperbean.MoeBookOnlinePetLimitBreedBinding;
import com.moego.server.grooming.mapperbean.MoeBookOnlineStaffTime;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class StaffTimeSyncServiceTest {
    @Mock
    MoeBookOnlineStaffTimeMapper bookOnlineStaffTimeMapper;

    @Mock
    OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceBlockingStub obStaffAvailabilityService;

    @Mock
    MoeBookOnlinePetLimitMapper moeBookOnlinePetLimitMapper;

    @Mock
    StaffAvailableSyncService staffAvailableSyncService;

    @Mock
    MoeBookOnlinePetLimitBreedBindingMapper moeBookOnlinePetLimitBreedBindingMapper;

    @InjectMocks
    StaffTimeSyncService staffTimeSyncService;

    @Test
    public void asyncStaffTime() {
        // test null
        staffTimeSyncService.asyncStaffTime(1);
        // main
        when(bookOnlineStaffTimeMapper.selectByPrimaryKey(1)).thenReturn(buildTestStaffTime());
        when(obStaffAvailabilityService.updateStaffAvailability(any()))
                .thenReturn(UpdateStaffAvailabilityResponse.getDefaultInstance());
        staffTimeSyncService.asyncStaffTime(1);
    }

    @Test
    public void limitIdsConvertToLimitationDef() {
        // test empty
        staffTimeSyncService.limitIdsConvertToLimitationDef(Collections.singletonList(1L));
        // main
        when(moeBookOnlinePetLimitMapper.selectByIdList(any(), any())).thenReturn(buildPetLimits());
        when(moeBookOnlinePetLimitBreedBindingMapper.selectByIdList(any())).thenReturn(buildPetBreedBindingLimits());
        staffTimeSyncService.limitIdsConvertToLimitationDef(Collections.singletonList(1L));
    }

    private MoeBookOnlineStaffTime buildTestStaffTime() {
        var staffTime = new MoeBookOnlineStaffTime();
        staffTime.setBusinessId(1);
        staffTime.setCompanyId(1L);
        staffTime.setStaffId(1);
        staffTime.setStaffTimes(MoeGroomingBookOnlineService.DEFAULT_STAFF_TIME);
        staffTime.setStatus(DeleteStatusEnum.STATUS_NORMAL);
        return staffTime;
    }

    private List<MoeBookOnlinePetLimit> buildPetLimits() {
        var petLimit1 = new MoeBookOnlinePetLimit();
        petLimit1.setId(1L);
        petLimit1.setBusinessId(1);
        petLimit1.setType((byte) 1);
        petLimit1.setFindId(1L);
        petLimit1.setMaxNumber(1);
        petLimit1.setStatus((byte) 1);
        petLimit1.setCompanyId(1L);
        var petLimit2 = new MoeBookOnlinePetLimit();
        petLimit2.setId(2L);
        petLimit2.setBusinessId(1);
        petLimit2.setType((byte) 2);
        petLimit2.setFindId(1L);
        petLimit2.setMaxNumber(2);
        petLimit2.setStatus((byte) 1);
        petLimit2.setCompanyId(1L);
        var petLimit3 = new MoeBookOnlinePetLimit();
        petLimit3.setId(3L);
        petLimit3.setBusinessId(1);
        petLimit3.setType((byte) 2);
        petLimit3.setFindId(2L);
        petLimit3.setMaxNumber(3);
        petLimit3.setStatus((byte) 1);
        petLimit3.setCompanyId(1L);
        return List.of(petLimit1, petLimit2, petLimit3);
    }

    private List<MoeBookOnlinePetLimitBreedBinding> buildPetBreedBindingLimits() {
        var breedBinding1 = new MoeBookOnlinePetLimitBreedBinding();
        breedBinding1.setId(1L);
        breedBinding1.setBusinessId(1);
        breedBinding1.setPetTypeId(1);
        breedBinding1.setAllBreed((byte) 1);
        breedBinding1.setStatus((byte) 1);
        breedBinding1.setCompanyId(1L);
        breedBinding1.setBreedIdList("[]");
        var breedBinding2 = new MoeBookOnlinePetLimitBreedBinding();
        breedBinding2.setId(2L);
        breedBinding2.setBusinessId(1);
        breedBinding2.setPetTypeId(1);
        breedBinding2.setAllBreed((byte) 0);
        breedBinding2.setStatus((byte) 1);
        breedBinding2.setCompanyId(1L);
        breedBinding2.setBreedIdList("[1,2,3]");
        return List.of(breedBinding1, breedBinding2);
    }

    @Nested
    @DisplayName("getStaffSlotTimeDataByCurrentDate Tests")
    class BuildBusinessClosedDateCheckResultTests {

        @Test
        void testGetStaffSlotTimeDataByCurrentDate_OneWeekSchedule_Monday() {
            // 准备测试数据 - ONE_WEEK 调度类型，周一
            StaffAvailability availability = createStaffAvailability(
                    ScheduleType.ONE_WEEK,
                    "2025-01-05", // 2025-01-05 是周日
                    createTimeAvailabilityDay(DayOfWeek.MONDAY, ScheduleType.ONE_WEEK),
                    createTimeAvailabilityDay(DayOfWeek.TUESDAY, ScheduleType.ONE_WEEK));

            LocalDate currentDate = LocalDate.of(2025, 1, 6); // 周一

            // 执行测试
            TimeAvailabilityDay result =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, currentDate);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result.getDayOfWeek()).isEqualTo(DayOfWeek.MONDAY);
            assertThat(result.getScheduleType()).isEqualTo(ScheduleType.ONE_WEEK);
        }

        @Test
        void testGetStaffSlotTimeDataByCurrentDate_OneWeekSchedule_NotFound() {
            // 准备测试数据 - ONE_WEEK 调度类型，但没有周三的数据
            StaffAvailability availability = createStaffAvailability(
                    ScheduleType.ONE_WEEK,
                    "2025-01-05",
                    createTimeAvailabilityDay(DayOfWeek.MONDAY, ScheduleType.ONE_WEEK),
                    createTimeAvailabilityDay(DayOfWeek.TUESDAY, ScheduleType.ONE_WEEK));

            LocalDate currentDate = LocalDate.of(2025, 1, 8); // 周三

            // 执行测试
            TimeAvailabilityDay result =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, currentDate);

            // 验证结果
            assertThat(result).isNull();
        }

        @Test
        void testGetStaffSlotTimeDataByCurrentDate_TwoWeekSchedule_FirstWeek() {
            // 准备测试数据 - TWO_WEEK 调度类型
            StaffAvailability availability = createStaffAvailability(
                    ScheduleType.TWO_WEEK,
                    "2025-01-05", // 2025-01-05 是周日
                    createTimeAvailabilityDay(DayOfWeek.MONDAY, ScheduleType.ONE_WEEK), // 第1周
                    createTimeAvailabilityDay(DayOfWeek.MONDAY, ScheduleType.TWO_WEEK) // 第2周
                    );

            LocalDate currentDate = LocalDate.of(2025, 1, 6); // 第1周的周一

            // 执行测试
            TimeAvailabilityDay result =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, currentDate);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result.getDayOfWeek()).isEqualTo(DayOfWeek.MONDAY);
            assertThat(result.getScheduleType()).isEqualTo(ScheduleType.ONE_WEEK); // 第1周对应 ONE_WEEK
        }

        @Test
        void testGetStaffSlotTimeDataByCurrentDate_TwoWeekSchedule_SecondWeek() {
            // 准备测试数据 - TWO_WEEK 调度类型
            StaffAvailability availability = createStaffAvailability(
                    ScheduleType.TWO_WEEK,
                    "2025-01-05", // 2025-01-05 是周日
                    createTimeAvailabilityDay(DayOfWeek.MONDAY, ScheduleType.ONE_WEEK), // 第1周
                    createTimeAvailabilityDay(DayOfWeek.MONDAY, ScheduleType.TWO_WEEK) // 第2周
                    );

            LocalDate currentDate = LocalDate.of(2025, 1, 13); // 第2周的周一

            // 执行测试
            TimeAvailabilityDay result =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, currentDate);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result.getDayOfWeek()).isEqualTo(DayOfWeek.MONDAY);
            assertThat(result.getScheduleType()).isEqualTo(ScheduleType.TWO_WEEK); // 第2周对应 TWO_WEEK
        }

        @Test
        void testGetStaffSlotTimeDataByCurrentDate_ThreeWeekSchedule_CycleTest() {
            // 准备测试数据 - THREE_WEEK 调度类型
            StaffAvailability availability = createStaffAvailability(
                    ScheduleType.THREE_WEEK,
                    "2025-01-05", // 2025-01-05 是周日
                    createTimeAvailabilityDay(DayOfWeek.MONDAY, ScheduleType.ONE_WEEK), // 第1周
                    createTimeAvailabilityDay(DayOfWeek.MONDAY, ScheduleType.TWO_WEEK), // 第2周
                    createTimeAvailabilityDay(DayOfWeek.MONDAY, ScheduleType.THREE_WEEK) // 第3周
                    );

            // 测试第1周
            LocalDate firstWeek = LocalDate.of(2025, 1, 6); // 第1周的周一
            TimeAvailabilityDay result1 =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, firstWeek);
            assertThat(result1).isNotNull();
            assertThat(result1.getScheduleType()).isEqualTo(ScheduleType.ONE_WEEK);

            // 测试第2周
            LocalDate secondWeek = LocalDate.of(2025, 1, 13); // 第2周的周一
            TimeAvailabilityDay result2 =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, secondWeek);
            assertThat(result2).isNotNull();
            assertThat(result2.getScheduleType()).isEqualTo(ScheduleType.TWO_WEEK);

            // 测试第3周
            LocalDate thirdWeek = LocalDate.of(2025, 1, 20); // 第3周的周一
            TimeAvailabilityDay result3 =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, thirdWeek);
            assertThat(result3).isNotNull();
            assertThat(result3.getScheduleType()).isEqualTo(ScheduleType.THREE_WEEK);

            // 测试第4周（应该循环回第1周）
            LocalDate fourthWeek = LocalDate.of(2025, 1, 27); // 第4周的周一
            TimeAvailabilityDay result4 =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, fourthWeek);
            assertThat(result4).isNotNull();
            assertThat(result4.getScheduleType()).isEqualTo(ScheduleType.ONE_WEEK); // 循环回第1周
        }

        @Test
        void testGetStaffSlotTimeDataByCurrentDate_FourWeekSchedule_CycleTest() {
            // 准备测试数据 - FOUR_WEEK 调度类型
            StaffAvailability availability = createStaffAvailability(
                    ScheduleType.FOUR_WEEK,
                    "2025-01-05", // 2025-01-05 是周日
                    createTimeAvailabilityDay(DayOfWeek.TUESDAY, ScheduleType.ONE_WEEK), // 第1周
                    createTimeAvailabilityDay(DayOfWeek.TUESDAY, ScheduleType.TWO_WEEK), // 第2周
                    createTimeAvailabilityDay(DayOfWeek.TUESDAY, ScheduleType.THREE_WEEK), // 第3周
                    createTimeAvailabilityDay(DayOfWeek.TUESDAY, ScheduleType.FOUR_WEEK) // 第4周
                    );

            // 测试第5周（应该循环回第1周）
            LocalDate fifthWeek = LocalDate.of(2025, 2, 4); // 第5周的周二
            TimeAvailabilityDay result =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, fifthWeek);
            assertThat(result).isNotNull();
            assertThat(result.getScheduleType()).isEqualTo(ScheduleType.ONE_WEEK); // 循环回第1周
        }

        @Test
        void testGetStaffSlotTimeDataByCurrentDate_DifferentDaysOfWeek() {
            // 测试不同的星期几
            StaffAvailability availability = createStaffAvailability(
                    ScheduleType.ONE_WEEK,
                    "2025-01-05", // 2025-01-05 是周日
                    createTimeAvailabilityDay(DayOfWeek.SUNDAY, ScheduleType.ONE_WEEK),
                    createTimeAvailabilityDay(DayOfWeek.WEDNESDAY, ScheduleType.ONE_WEEK),
                    createTimeAvailabilityDay(DayOfWeek.SATURDAY, ScheduleType.ONE_WEEK));

            // 测试周日
            LocalDate sunday = LocalDate.of(2025, 1, 5);
            TimeAvailabilityDay sundayResult =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, sunday);
            assertThat(sundayResult).isNotNull();
            assertThat(sundayResult.getDayOfWeek()).isEqualTo(DayOfWeek.SUNDAY);

            // 测试周三
            LocalDate wednesday = LocalDate.of(2025, 1, 8);
            TimeAvailabilityDay wednesdayResult =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, wednesday);
            assertThat(wednesdayResult).isNotNull();
            assertThat(wednesdayResult.getDayOfWeek()).isEqualTo(DayOfWeek.WEDNESDAY);

            // 测试周六
            LocalDate saturday = LocalDate.of(2025, 1, 11);
            TimeAvailabilityDay saturdayResult =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, saturday);
            assertThat(saturdayResult).isNotNull();
            assertThat(saturdayResult.getDayOfWeek()).isEqualTo(DayOfWeek.SATURDAY);
        }

        @Test
        void testGetStaffSlotTimeDataByCurrentDate_EmptyAvailabilityList() {
            // 测试空的可用性列表
            StaffAvailability availability = StaffAvailability.newBuilder()
                    .setTimeScheduleType(ScheduleType.ONE_WEEK)
                    .setTimeStartSunday("2025-01-05")
                    .build(); // 没有添加任何 TimeAvailabilityDay

            LocalDate currentDate = LocalDate.of(2025, 1, 6); // 周一

            // 执行测试
            TimeAvailabilityDay result =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, currentDate);

            // 验证结果
            assertThat(result).isNull();
        }

        @Test
        void testGetStaffSlotTimeDataByCurrentDate_MultiWeekSchedule_NotFound() {
            // 测试多周调度但找不到匹配数据的情况
            StaffAvailability availability = createStaffAvailability(
                    ScheduleType.TWO_WEEK,
                    "2025-01-05",
                    createTimeAvailabilityDay(DayOfWeek.MONDAY, ScheduleType.ONE_WEEK), // 只有第1周的周一
                    createTimeAvailabilityDay(DayOfWeek.TUESDAY, ScheduleType.TWO_WEEK) // 只有第2周的周二
                    );

            LocalDate currentDate = LocalDate.of(2025, 1, 14); // 第2周的周二，但查找周一
            currentDate = LocalDate.of(2025, 1, 13); // 第2周的周一

            // 执行测试
            TimeAvailabilityDay result =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, currentDate);

            // 验证结果 - 应该找不到第2周的周一数据
            assertThat(result).isNull();
        }

        @Test
        void testGetStaffSlotTimeDataByCurrentDate_EdgeCaseDates() {
            // 测试边界日期情况
            StaffAvailability availability = createStaffAvailability(
                    ScheduleType.TWO_WEEK,
                    "2025-01-05", // 开始日期是周日
                    createTimeAvailabilityDay(DayOfWeek.SUNDAY, ScheduleType.ONE_WEEK),
                    createTimeAvailabilityDay(DayOfWeek.SUNDAY, ScheduleType.TWO_WEEK));

            // 测试开始日期当天
            LocalDate startDate = LocalDate.of(2025, 1, 5); // 开始日期周日
            TimeAvailabilityDay result1 =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, startDate);
            assertThat(result1).isNotNull();
            assertThat(result1.getScheduleType()).isEqualTo(ScheduleType.ONE_WEEK); // 第1周

            // 测试第2周的同一天
            LocalDate secondWeekSunday = LocalDate.of(2025, 1, 12); // 第2周周日
            TimeAvailabilityDay result2 =
                    StaffTimeSyncService.getStaffSlotTimeDataByCurrentDate(availability, secondWeekSunday);
            assertThat(result2).isNotNull();
            assertThat(result2.getScheduleType()).isEqualTo(ScheduleType.TWO_WEEK); // 第2周
        }

        /**
         * 创建 StaffAvailability 测试对象
         */
        private StaffAvailability createStaffAvailability(
                ScheduleType scheduleType, String startSunday, TimeAvailabilityDay... days) {
            StaffAvailability.Builder builder = StaffAvailability.newBuilder()
                    .setTimeScheduleType(scheduleType)
                    .setTimeStartSunday(startSunday);

            for (TimeAvailabilityDay day : days) {
                builder.addTimeAvailabilityDayList(day);
            }

            return builder.build();
        }

        /**
         * 创建 TimeAvailabilityDay 测试对象
         */
        private TimeAvailabilityDay createTimeAvailabilityDay(DayOfWeek dayOfWeek, ScheduleType scheduleType) {
            return TimeAvailabilityDay.newBuilder()
                    .setDayOfWeek(dayOfWeek)
                    .setScheduleType(scheduleType)
                    .setIsAvailable(true)
                    .build();
        }
    }
}
