package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.common.enums.QuestionConst;
import com.moego.server.grooming.mapper.MoeBookOnlineQuestionMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MoeGroomingQuestionServiceTest {

    @Mock
    private MoeBookOnlineQuestionMapper moeBookOnlineQuestionMapper;

    @InjectMocks
    private MoeGroomingQuestionService questionService;

    @Captor
    private ArgumentCaptor<MoeBookOnlineQuestion> questionCaptor;

    @Test
    void initBookingQuestionForBusiness_WhenNotInitialized_ShouldCreateQuestions() {
        // Arrange
        Integer businessId = 1;
        Long companyId = 1L;
        when(moeBookOnlineQuestionMapper.getListByBusinessId(businessId, QuestionConst.TYPE_PET_QUESTION.intValue()))
                .thenReturn(new ArrayList<>());
        when(moeBookOnlineQuestionMapper.insertSelective(any(MoeBookOnlineQuestion.class)))
                .thenAnswer(invocation -> {
                    MoeBookOnlineQuestion question = invocation.getArgument(0);
                    question.setId(1);
                    return 1;
                });

        // Act
        questionService.initBookingQuestionForBusiness(businessId, companyId);

        // Assert
        verify(moeBookOnlineQuestionMapper, times(33)).insertSelective(questionCaptor.capture());
        List<MoeBookOnlineQuestion> capturedQuestions = questionCaptor.getAllValues();

        // Verify pet questions
        assertPetQuestions(capturedQuestions.subList(0, 16), businessId, companyId);
        // Verify owner questions
        assertOwnerQuestions(capturedQuestions.subList(16, 29), businessId, companyId);
        // Verify service questions
        assertServiceQuestions(capturedQuestions.subList(29, 33), businessId, companyId);
    }

    @Test
    void initBookingQuestionForBusiness_WhenAlreadyInitialized_ShouldNotCreateQuestions() {
        // Arrange
        Integer businessId = 1;
        Long companyId = 1L;
        when(moeBookOnlineQuestionMapper.getListByBusinessId(businessId, QuestionConst.TYPE_PET_QUESTION.intValue()))
                .thenReturn(List.of(new MoeBookOnlineQuestion()));

        // Act
        questionService.initBookingQuestionForBusiness(businessId, companyId);

        // Assert
        verify(moeBookOnlineQuestionMapper, never()).insertSelective(any());
    }

    @Test
    void createAdditionalPetOwnerQuestion_ShouldCreateMissingQuestions() {
        // Arrange
        Integer businessId = 1;
        Long companyId = 1L;
        List<String> existingQuestions = Arrays.asList("Email", "Address");
        when(moeBookOnlineQuestionMapper.insertSelective(any(MoeBookOnlineQuestion.class)))
                .thenAnswer(invocation -> {
                    MoeBookOnlineQuestion question = invocation.getArgument(0);
                    question.setId(1);
                    return 1;
                });

        // Act
        questionService.createAdditionalPetOwnerQuestion(businessId, companyId, existingQuestions);

        // Assert
        verify(moeBookOnlineQuestionMapper, times(11)).insertSelective(questionCaptor.capture());
        List<MoeBookOnlineQuestion> capturedQuestions = questionCaptor.getAllValues();
        assertAdditionalOwnerQuestions(capturedQuestions, businessId, companyId);
    }

    private void assertPetQuestions(List<MoeBookOnlineQuestion> questions, Integer businessId, Long companyId) {
        for (MoeBookOnlineQuestion question : questions) {
            assertThat(question).satisfies(q -> {
                assertThat(q.getBusinessId()).isEqualTo(businessId);
                assertThat(q.getCompanyId()).isEqualTo(companyId);
                assertThat(q.getType()).isEqualTo(QuestionConst.TYPE_PET_QUESTION);
            });
        }
    }

    private void assertOwnerQuestions(List<MoeBookOnlineQuestion> questions, Integer businessId, Long companyId) {
        for (MoeBookOnlineQuestion question : questions) {
            assertThat(question).satisfies(q -> {
                assertThat(q.getBusinessId()).isEqualTo(businessId);
                assertThat(q.getCompanyId()).isEqualTo(companyId);
                assertThat(q.getType()).isEqualTo(QuestionConst.TYPE_PET_OWNER_QUESTION);
            });
        }
    }

    private void assertServiceQuestions(List<MoeBookOnlineQuestion> questions, Integer businessId, Long companyId) {
        for (MoeBookOnlineQuestion question : questions) {
            assertThat(question).satisfies(q -> {
                assertThat(q.getBusinessId()).isEqualTo(businessId);
                assertThat(q.getCompanyId()).isEqualTo(companyId);
                assertThat(q.getIsShow()).isEqualTo(QuestionConst.IS_SHOW_TURE);
                assertThat(q.getIsRequired()).isEqualTo(QuestionConst.IS_REQUIRED_FALSE);
            });
        }
    }

    private void assertAdditionalOwnerQuestions(
            List<MoeBookOnlineQuestion> questions, Integer businessId, Long companyId) {
        for (MoeBookOnlineQuestion question : questions) {
            assertThat(question).satisfies(q -> {
                assertThat(q.getBusinessId()).isEqualTo(businessId);
                assertThat(q.getCompanyId()).isEqualTo(companyId);
                assertThat(q.getType()).isEqualTo(QuestionConst.TYPE_PET_OWNER_QUESTION);
            });
        }
    }
}
