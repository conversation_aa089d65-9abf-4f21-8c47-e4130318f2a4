package com.moego.server.grooming.service;

import com.moego.server.grooming.dto.GroomingQuestionDTO;
import com.moego.server.grooming.util.CustomQuestionUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2020/12/29 4:35 PM
 */
public class UtilTest {

    @Test
    public void testNote() {
        List<GroomingQuestionDTO> questions = new ArrayList<>();
        GroomingQuestionDTO groomingQuestionDTO = new GroomingQuestionDTO();
        groomingQuestionDTO.setKey("customer_1");
        groomingQuestionDTO.setQuestion("you answer is ?");
        groomingQuestionDTO.setQuestionType(Byte.valueOf("1"));

        questions.add(groomingQuestionDTO);
        GroomingQuestionDTO groomingQuestionDTO2 = new GroomingQuestionDTO();
        groomingQuestionDTO2.setKey("customer_2");
        groomingQuestionDTO2.setQuestion("you answer 2 is ?");
        groomingQuestionDTO2.setQuestionType(Byte.valueOf("3"));

        questions.add(groomingQuestionDTO2);
        GroomingQuestionDTO groomingQuestionDTO3 = new GroomingQuestionDTO();
        groomingQuestionDTO3.setKey("customer_3");
        groomingQuestionDTO3.setQuestion("you answer 3 is ?");
        groomingQuestionDTO3.setQuestionType(Byte.valueOf("5"));
        questions.add(groomingQuestionDTO3);

        Map<String, Object> map = new HashMap<>();
        map.put("customer_1", "answer1");
        map.put("customer_2", "answer2");
        map.put("customer_3", "[\"qq\",\"tt\"]");

        String note = CustomQuestionUtil.generateNote(questions, map);
        System.out.println(note);
    }

    @Test
    public void testDecimal() {
        BigDecimal a = BigDecimal.valueOf(0.00);
        System.out.println(a.compareTo(BigDecimal.ZERO));
        System.out.println(a.add(BigDecimal.ONE));
        System.out.println(a);
    }
}
