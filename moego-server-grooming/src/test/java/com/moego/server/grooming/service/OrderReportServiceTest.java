package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.server.grooming.dto.CustomerGrooming;
import com.moego.server.grooming.dto.ServiceChargeDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.helper.NewOrderHelper;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGroomingServiceOperationMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation;
import com.moego.server.grooming.service.dto.ReportAppointmentDAO;
import com.moego.server.grooming.service.report.ReportOrderService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class OrderReportServiceTest {
    @Mock
    private AppointmentMapperProxy appointmentMapper;

    @Mock
    private MoeGroomingServiceOperationMapper serviceOperationMapper;

    @Mock
    private OrderService orderService;

    @Mock
    private MoeGroomingAppointmentService moeGroomingAppointmentService;

    @Mock
    private NewOrderHelper newOrderHelper;

    @InjectMocks
    private ReportOrderService reportOrderService;

    private static final Integer BUSINESS_ID = 12345678;
    private static final String START_DATE = "2022-07-01";
    private static final String END_DATE = "2022-07-31";

    private MoeGroomingInvoice invoice;
    private MoeGroomingServiceOperation operation;
    private ServiceChargeDTO serviceCharge;
    private CustomerGrooming customerGrooming;

    @BeforeEach
    void setUp() {
        // Setup invoice
        invoice = new MoeGroomingInvoice();
        invoice.setId(1);
        invoice.setOrderType(OrderModel.OrderType.ORIGIN.name());
        invoice.setType(InvoiceStatusEnum.TYPE_APPOINTMENT);
        invoice.setBusinessId(BUSINESS_ID);
        invoice.setGroomingId(1);
        invoice.setTotalAmount(new BigDecimal("100.00"));
        invoice.setPaidAmount(new BigDecimal("50.00"));
        invoice.setRemainAmount(new BigDecimal("50.00"));

        // Setup operation
        operation = new MoeGroomingServiceOperation();
        operation.setId(1L);
        operation.setGroomingServiceId(1);
        operation.setStaffId(1);

        // Setup service charge
        serviceCharge = new ServiceChargeDTO();
        serviceCharge.setId(1L);
        serviceCharge.setName("Test Service");
        serviceCharge.setPrice(new BigDecimal("50.00"));

        // Setup customer grooming
        customerGrooming = new CustomerGrooming();
        customerGrooming.setId(1);
        customerGrooming.setBusinessId(BUSINESS_ID);
    }

    @Test
    void queryGroomingCustomerAppointment_WhenValidInput_ShouldReturnAppointments() {
        // Arrange
        // Setup customer grooming with petServiceList
        CustomerGrooming customerGrooming = new CustomerGrooming();
        customerGrooming.setId(1);
        customerGrooming.setBusinessId(BUSINESS_ID);
        customerGrooming.setPetServiceList(new ArrayList<>()); // 初始化空列表而不是 null
        customerGrooming.setServiceTypeInclude(1); // 添加服务类型
        customerGrooming.setStatus(AppointmentStatusEnum.UNCONFIRMED.getValue().intValue()); // 设置状态
        customerGrooming.setCheckInTime(null); // 设置 check-in 时间

        when(appointmentMapper.queryGroomingCustomerAppointment(anyList(), anyInt()))
                .thenReturn(List.of(customerGrooming));
        when(serviceOperationMapper.selectByGroomingIdList(anyList())).thenReturn(List.of(operation));
        when(orderService.getInvoicesByGroomingIds(anyInt(), anyList())).thenReturn(List.of(invoice));

        // Mock moeGroomingAppointmentService
        when(moeGroomingAppointmentService.getCompatibleStatus(AppointmentStatusEnum.UNCONFIRMED.getValue()))
                .thenReturn(1);
        when(moeGroomingAppointmentService.getCompatibleAppointmentStatus(
                        eq(AppointmentStatusEnum.UNCONFIRMED.getValue()), any()))
                .thenReturn(AppointmentStatusEnum.UNCONFIRMED);
        when(newOrderHelper.listNewOrder(anyList())).thenReturn(Map.of(1L, false));

        // Act
        List<CustomerGrooming> result = reportOrderService.queryGroomingCustomerAppointment(BUSINESS_ID, List.of(1), 1);

        // Assert
        assertThat(result).isNotNull().hasSize(1).first().satisfies(grooming -> {
            assertThat(grooming.getBusinessId()).isEqualTo(BUSINESS_ID);
            assertThat(grooming.getAppointmentStatus()).isEqualTo(AppointmentStatusEnum.UNCONFIRMED);
        });
    }

    @Test
    void queryUnpaidApptsWithAmount_WhenValidInput_ShouldReturnAppointments() {
        // Arrange
        ReportAppointmentDAO appointment = new ReportAppointmentDAO();
        appointment.setId(1);

        // 创建可变列表
        List<ReportAppointmentDAO> appointments = new ArrayList<>();
        appointments.add(appointment);

        // Mock invoice with remain amount
        MoeGroomingInvoice unpaidInvoice = new MoeGroomingInvoice();
        unpaidInvoice.setId(1);
        unpaidInvoice.setType(InvoiceStatusEnum.TYPE_APPOINTMENT);
        unpaidInvoice.setOrderType(OrderModel.OrderType.ORIGIN.name());
        unpaidInvoice.setGroomingId(1); // 必须和 appointment.getId() 匹配
        unpaidInvoice.setRemainAmount(new BigDecimal("50.00")); // 确保有未支付金额
        unpaidInvoice.setTotalAmount(new BigDecimal("100.00")); // 添加总金额
        unpaidInvoice.setPaidAmount(new BigDecimal("50.00")); // 添加已支付金额
        unpaidInvoice.setBusinessId(BUSINESS_ID); // 添加业务ID

        when(appointmentMapper.queryUnpaidApptsWithAmount(eq(BUSINESS_ID), eq(START_DATE), eq(END_DATE)))
                .thenReturn(appointments);

        when(orderService.getListByGroomingIds(eq(BUSINESS_ID), anyList(), any()))
                .thenReturn(List.of(unpaidInvoice));

        // Act
        List<ReportAppointmentDAO> result =
                reportOrderService.queryUnpaidApptsWithAmount(BUSINESS_ID, START_DATE, END_DATE);

        // Assert
        assertThat(result).isNotNull().hasSize(1).first().satisfies(appt -> assertThat(appt.getRemainAmount())
                .isEqualTo(new BigDecimal("50.00")));
    }

    @Test
    void getServiceChargeName_WhenValidInput_ShouldReturnServiceNames() {
        // Arrange
        MoeGroomingInvoiceItem item = new MoeGroomingInvoiceItem();
        item.setServiceId(1);
        item.setServiceName("Test Service");

        Map<Long, ServiceChargeDTO> serviceChargeMap = Map.of(1L, serviceCharge);

        // Act
        List<String> result = reportOrderService.getServiceChargeName(List.of(item), serviceChargeMap);

        // Assert
        assertThat(result).isNotNull().hasSize(1).containsExactly("Test Service");
    }

    @Test
    void queryUnpaidApptsCountWithAmount_ShouldReturnCorrectCount() {
        // Arrange
        ReportAppointmentDAO appointment = new ReportAppointmentDAO();
        appointment.setId(1);
        List<ReportAppointmentDAO> appointments = new ArrayList<>();
        appointments.add(appointment);

        when(appointmentMapper.queryUnpaidApptsWithAmount(eq(BUSINESS_ID), eq(START_DATE), eq(END_DATE)))
                .thenReturn(appointments);

        when(orderService.getListByGroomingIds(eq(BUSINESS_ID), anyList(), any()))
                .thenReturn(List.of(invoice));

        // Act
        Integer result = reportOrderService.queryUnpaidApptsCountWithAmount(BUSINESS_ID, START_DATE, END_DATE);

        // Assert
        assertThat(result).isEqualTo(1);
    }

    @Test
    @DisplayName("新订单流程 - 应该正确计算并设置paidAmount和refundAmount")
    void queryGroomingCustomerAppointment_WhenNewOrderFlow_ShouldSetCorrectAmounts() {
        // Arrange - 准备测试数据
        CustomerGrooming customerGrooming = new CustomerGrooming();
        customerGrooming.setId(1);
        customerGrooming.setBusinessId(BUSINESS_ID);
        customerGrooming.setPetServiceList(new ArrayList<>());
        customerGrooming.setServiceTypeInclude(1);
        customerGrooming.setStatus(AppointmentStatusEnum.UNCONFIRMED.getValue().intValue());
        customerGrooming.setCheckInTime(null);

        // 创建多个发票用于金额计算
        MoeGroomingInvoice invoice1 = new MoeGroomingInvoice();
        invoice1.setId(1);
        invoice1.setPaidAmount(BigDecimal.valueOf(100.00));
        invoice1.setRefundedAmount(BigDecimal.valueOf(10.00));
        invoice1.setGroomingId(1);

        MoeGroomingInvoice invoice2 = new MoeGroomingInvoice();
        invoice2.setId(2);
        invoice2.setPaidAmount(BigDecimal.valueOf(200.00));
        invoice2.setRefundedAmount(BigDecimal.valueOf(20.00));
        invoice2.setGroomingId(1);

        when(appointmentMapper.queryGroomingCustomerAppointment(anyList(), anyInt()))
                .thenReturn(List.of(customerGrooming));
        when(serviceOperationMapper.selectByGroomingIdList(anyList())).thenReturn(List.of());
        when(orderService.getInvoicesByGroomingIds(anyInt(), anyList())).thenReturn(List.of(invoice1, invoice2));
        when(moeGroomingAppointmentService.getCompatibleStatus(AppointmentStatusEnum.UNCONFIRMED.getValue()))
                .thenReturn(1);
        when(moeGroomingAppointmentService.getCompatibleAppointmentStatus(
                        eq(AppointmentStatusEnum.UNCONFIRMED.getValue()), any()))
                .thenReturn(AppointmentStatusEnum.UNCONFIRMED);
        when(newOrderHelper.listNewOrder(anyList())).thenReturn(Map.of(1L, true)); // 设置为新订单流程

        // Act - 执行被测试方法
        List<CustomerGrooming> result = reportOrderService.queryGroomingCustomerAppointment(BUSINESS_ID, List.of(1), 1);

        // Assert - 验证金额字段
        assertThat(result).isNotNull().hasSize(1).first().satisfies(grooming -> {
            assertThat(grooming.getPaidAmount()).isEqualTo(BigDecimal.valueOf(300.00)); // 100 + 200
            assertThat(grooming.getRefundAmount()).isEqualTo(BigDecimal.valueOf(30.00)); // 10 + 20
        });
    }

    @Test
    @DisplayName("旧订单流程 - 应该从ORIGIN类型发票设置paidAmount和refundAmount")
    void queryGroomingCustomerAppointment_WhenOldOrderFlow_ShouldSetAmountsFromOriginInvoice() {
        // Arrange - 准备测试数据
        CustomerGrooming customerGrooming = new CustomerGrooming();
        customerGrooming.setId(1);
        customerGrooming.setBusinessId(BUSINESS_ID);
        customerGrooming.setPetServiceList(new ArrayList<>());
        customerGrooming.setServiceTypeInclude(1);
        customerGrooming.setStatus(AppointmentStatusEnum.UNCONFIRMED.getValue().intValue());
        customerGrooming.setCheckInTime(null);

        // 创建ORIGIN类型发票
        MoeGroomingInvoice originInvoice = new MoeGroomingInvoice();
        originInvoice.setId(1);
        originInvoice.setOrderType(OrderModel.OrderType.ORIGIN.name());
        originInvoice.setPaidAmount(BigDecimal.valueOf(150.00));
        originInvoice.setRefundedAmount(BigDecimal.valueOf(15.00));
        originInvoice.setPaymentStatus("PAID");
        originInvoice.setGroomingId(1);

        // 创建其他类型发票（应该被忽略）
        MoeGroomingInvoice otherInvoice = new MoeGroomingInvoice();
        otherInvoice.setId(2);
        otherInvoice.setOrderType(OrderModel.OrderType.DEPOSIT.name());
        otherInvoice.setPaidAmount(BigDecimal.valueOf(10.00));
        otherInvoice.setRefundedAmount(BigDecimal.valueOf(0.00));
        otherInvoice.setGroomingId(1);

        when(appointmentMapper.queryGroomingCustomerAppointment(anyList(), anyInt()))
                .thenReturn(List.of(customerGrooming));
        when(serviceOperationMapper.selectByGroomingIdList(anyList())).thenReturn(List.of());
        when(orderService.getInvoicesByGroomingIds(anyInt(), anyList()))
                .thenReturn(List.of(originInvoice, otherInvoice));
        when(moeGroomingAppointmentService.getCompatibleStatus(AppointmentStatusEnum.UNCONFIRMED.getValue()))
                .thenReturn(1);
        when(moeGroomingAppointmentService.getCompatibleAppointmentStatus(
                        eq(AppointmentStatusEnum.UNCONFIRMED.getValue()), any()))
                .thenReturn(AppointmentStatusEnum.UNCONFIRMED);
        when(newOrderHelper.listNewOrder(anyList())).thenReturn(Map.of(1L, false)); // 设置为旧订单流程

        // Act - 执行被测试方法
        List<CustomerGrooming> result = reportOrderService.queryGroomingCustomerAppointment(BUSINESS_ID, List.of(1), 1);

        // Assert - 验证金额字段
        assertThat(result).isNotNull().hasSize(1).first().satisfies(grooming -> {
            assertThat(grooming.getPaidAmount()).isEqualTo(BigDecimal.valueOf(150.00)); // 只取ORIGIN发票的金额
            assertThat(grooming.getRefundAmount()).isEqualTo(BigDecimal.valueOf(15.00)); // 只取ORIGIN发票的金额
            assertThat(grooming.getAppointmentOrderId()).isEqualTo(1L); // 验证订单ID也被设置
            assertThat(grooming.getPaymentStatus()).isEqualTo("PAID"); // 验证支付状态也被设置
        });
    }

    @Test
    @DisplayName("旧订单流程且无ORIGIN发票 - paidAmount和refundAmount应该为null")
    void queryGroomingCustomerAppointment_WhenOldOrderFlowWithoutOriginInvoice_ShouldKeepAmountsNull() {
        // Arrange - 准备测试数据
        CustomerGrooming customerGrooming = new CustomerGrooming();
        customerGrooming.setId(1);
        customerGrooming.setBusinessId(BUSINESS_ID);
        customerGrooming.setPetServiceList(new ArrayList<>());
        customerGrooming.setServiceTypeInclude(1);
        customerGrooming.setStatus(AppointmentStatusEnum.UNCONFIRMED.getValue().intValue());
        customerGrooming.setCheckInTime(null);

        // 只创建非ORIGIN类型发票
        MoeGroomingInvoice otherInvoice = new MoeGroomingInvoice();
        otherInvoice.setId(1);
        otherInvoice.setOrderType(OrderModel.OrderType.DEPOSIT.name());
        otherInvoice.setPaidAmount(BigDecimal.valueOf(100.00));
        otherInvoice.setRefundedAmount(BigDecimal.valueOf(10.00));
        otherInvoice.setGroomingId(1);

        when(appointmentMapper.queryGroomingCustomerAppointment(anyList(), anyInt()))
                .thenReturn(List.of(customerGrooming));
        when(serviceOperationMapper.selectByGroomingIdList(anyList())).thenReturn(List.of());
        when(orderService.getInvoicesByGroomingIds(anyInt(), anyList())).thenReturn(List.of(otherInvoice));
        when(moeGroomingAppointmentService.getCompatibleStatus(AppointmentStatusEnum.UNCONFIRMED.getValue()))
                .thenReturn(1);
        when(moeGroomingAppointmentService.getCompatibleAppointmentStatus(
                        eq(AppointmentStatusEnum.UNCONFIRMED.getValue()), any()))
                .thenReturn(AppointmentStatusEnum.UNCONFIRMED);
        when(newOrderHelper.listNewOrder(anyList())).thenReturn(Map.of(1L, false)); // 设置为旧订单流程

        // Act - 执行被测试方法
        List<CustomerGrooming> result = reportOrderService.queryGroomingCustomerAppointment(BUSINESS_ID, List.of(1), 1);

        // Assert - 验证金额字段保持为null
        assertThat(result).isNotNull().hasSize(1).first().satisfies(grooming -> {
            assertThat(grooming.getPaidAmount()).isNull();
            assertThat(grooming.getRefundAmount()).isNull();
            assertThat(grooming.getAppointmentOrderId()).isNull();
            assertThat(grooming.getPaymentStatus()).isNull();
        });
    }

    @Test
    @DisplayName("新订单流程且无发票 - paidAmount和refundAmount应该为零")
    void queryGroomingCustomerAppointment_WhenNewOrderFlowWithoutInvoices_ShouldSetAmountsToZero() {
        // Arrange - 准备测试数据
        CustomerGrooming customerGrooming = new CustomerGrooming();
        customerGrooming.setId(1);
        customerGrooming.setBusinessId(BUSINESS_ID);
        customerGrooming.setPetServiceList(new ArrayList<>());
        customerGrooming.setServiceTypeInclude(1);
        customerGrooming.setStatus(AppointmentStatusEnum.UNCONFIRMED.getValue().intValue());
        customerGrooming.setCheckInTime(null);

        when(appointmentMapper.queryGroomingCustomerAppointment(anyList(), anyInt()))
                .thenReturn(List.of(customerGrooming));
        when(serviceOperationMapper.selectByGroomingIdList(anyList())).thenReturn(List.of());
        when(orderService.getInvoicesByGroomingIds(anyInt(), anyList())).thenReturn(List.of()); // 无发票
        when(moeGroomingAppointmentService.getCompatibleStatus(AppointmentStatusEnum.UNCONFIRMED.getValue()))
                .thenReturn(1);
        when(moeGroomingAppointmentService.getCompatibleAppointmentStatus(
                        eq(AppointmentStatusEnum.UNCONFIRMED.getValue()), any()))
                .thenReturn(AppointmentStatusEnum.UNCONFIRMED);
        when(newOrderHelper.listNewOrder(anyList())).thenReturn(Map.of(1L, true)); // 设置为新订单流程

        // Act - 执行被测试方法
        List<CustomerGrooming> result = reportOrderService.queryGroomingCustomerAppointment(BUSINESS_ID, List.of(1), 1);

        // Assert - 验证金额字段为零
        assertThat(result).isNotNull().hasSize(1).first().satisfies(grooming -> {
            assertThat(grooming.getPaidAmount()).isEqualTo(BigDecimal.ZERO);
            assertThat(grooming.getRefundAmount()).isEqualTo(BigDecimal.ZERO);
        });
    }
}
