package com.moego.server.grooming.service;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MoeGroomingBookOnlineServiceTest {

    @Mock
    MoeBusinessBookOnlineMapper moeBusinessBookOnlineMapper;

    @Mock
    ServiceAreaPicCacheService serviceAreaPicCacheService;

    @InjectMocks
    MoeGroomingBookOnlineService service;

    @Disabled
    @Test
    void testFilterTime() {
        MoeGroomingBookOnlineService moeGroomingBookOnlineService = new MoeGroomingBookOnlineService();
        List<TimeRangeDto> workTimeRanges = new ArrayList<>();
        workTimeRanges.add(new TimeRangeDto(0, 200));
        workTimeRanges.add(new TimeRangeDto(400, 600));
        List<TimeRangeDto> staffTimeRanges = new ArrayList<>();
        staffTimeRanges.add(new TimeRangeDto(50, 900));
        List<TimeRangeDto> appointTimeRanges = new ArrayList<>();
        appointTimeRanges.add(new TimeRangeDto(100, 400));
        appointTimeRanges.add(new TimeRangeDto(500, 850));

        System.out.println(moeGroomingBookOnlineService.filterOutNotAvailableTimeForDay(
                workTimeRanges, staffTimeRanges, appointTimeRanges));
    }

    @Test
    @Disabled
    void test_checkAvailableDist() {
        MoeGroomingBookOnlineService bookOnlineService = new MoeGroomingBookOnlineService();
        Integer mockBusinessId = 100000;
        String mockZipCode = "zipcode";
        String mockLocation = "location";

        MoeBusinessBookOnlineMapper mockBusinessBookOnlineMapper = Mockito.mock(MoeBusinessBookOnlineMapper.class);
        MoeBusinessBookOnline obSettings = new MoeBusinessBookOnline();
        // 0. 不开启校验
        obSettings.setIsByZipcode((byte) 0);
        obSettings.setIsByRadius((byte) 0);
        doReturn(obSettings).when(mockBusinessBookOnlineMapper).selectByBusinessId(mockBusinessId);

        // 准备 pricingCheckService
        GroomingFeaturePricingCheckService pricingCheckService = Mockito.mock(GroomingFeaturePricingCheckService.class);
        bookOnlineService.setPricingCheckService(pricingCheckService);

        bookOnlineService.setMoeBusinessBookOnlineMapper(mockBusinessBookOnlineMapper);
        assertTrue(bookOnlineService.checkAvailableDist(mockBusinessId, null, null, null));

        // 1. 开启 zipcode 校验, 商家没有设置 zipcode（obSetting.getZipCodes() == null）
        obSettings.setIsByZipcode((byte) 1);
        doReturn(obSettings).when(mockBusinessBookOnlineMapper).selectByBusinessId(mockBusinessId);
        assertTrue(bookOnlineService.checkAvailableDist(mockBusinessId, null, null, null));

        // 2. 开启 zipcode 校验，商家设置了 zipcode, 且顾客 zipcode 在商家服务范围内
        obSettings.setZipCodes(mockZipCode);
        doReturn(obSettings).when(mockBusinessBookOnlineMapper).selectByBusinessId(mockBusinessId);
        assertTrue(bookOnlineService.checkAvailableDist(mockBusinessId, null, null, mockZipCode));

        // 3. 开启 zipcode 校验，商家设置了 zipcode, 传入的 zipcode 为 null, 同时 lat lng 也为 null
        obSettings.setZipCodes(mockZipCode);
        doReturn(obSettings).when(mockBusinessBookOnlineMapper).selectByBusinessId(mockBusinessId);
        assertFalse(bookOnlineService.checkAvailableDist(mockBusinessId, null, null, null));

        // 4. 开启 zipcode 校验，商家设置了 zipcode, 传入的 zipcode 为 null, 同时 lat lng 不为 null
        SmartScheduleService mockSmartScheduleService = Mockito.mock(SmartScheduleService.class);
        doReturn(mockZipCode)
                .when(mockSmartScheduleService)
                .getZipcodeByLatLng(Mockito.anyString(), Mockito.anyString());
        bookOnlineService.setSmartScheduleService(mockSmartScheduleService);
        assertTrue(bookOnlineService.checkAvailableDist(mockBusinessId, "lat", "lng", null));

        // 5. 开启 radius 校验, 商家未设置 location
        obSettings.setIsByZipcode((byte) 0);
        obSettings.setIsByRadius((byte) 1);
        doReturn(obSettings).when(mockBusinessBookOnlineMapper).selectByBusinessId(mockBusinessId);
        assertTrue(bookOnlineService.checkAvailableDist(mockBusinessId, null, null, null));

        // 6. 开启 radius 校验, 且商家设置了 location，同时 lat lng 为 null
        obSettings.setSettingLocation(mockLocation);
        doReturn(obSettings).when(mockBusinessBookOnlineMapper).selectByBusinessId(mockBusinessId);
        assertFalse(bookOnlineService.checkAvailableDist(mockBusinessId, null, null, null));
    }

    @Test
    void deleteServiceArea() {
        when(moeBusinessBookOnlineMapper.selectByBusinessId(any())).thenReturn(new MoeBusinessBookOnline() {
            {
                setServiceAreas(List.of(1, 2, 3));
            }
        });
        service.deleteServiceArea(1000, 4);
        when(moeBusinessBookOnlineMapper.updateInfoByPrimaryIdOrBusinessId(any()))
                .thenReturn(1);
        service.deleteServiceArea(1000, 3);
    }

    @Test
    void testisNotConflict() {
        // 1. compareStart或compareEnd为null的情况
        assertTrue(
                MoeGroomingBookOnlineService.isNotConflict(null, 100, 200, 300, 0),
                "When compareStart is null, should return true");
        assertTrue(
                MoeGroomingBookOnlineService.isNotConflict(100, null, 200, 300, 0),
                "When compareEnd is null, should return true");

        // 2. bufferTime = 0 的情况
        // 2.1 无交集：compareEnd <= start
        assertTrue(
                MoeGroomingBookOnlineService.isNotConflict(100, 200, 200, 300, 0),
                "When compareEnd equals start, should not conflict");
        assertTrue(
                MoeGroomingBookOnlineService.isNotConflict(100, 150, 200, 300, 0),
                "When compareEnd less than start, should not conflict");

        // 2.2 无交集：compareStart >= end
        assertTrue(
                MoeGroomingBookOnlineService.isNotConflict(300, 400, 100, 300, 0),
                "When compareStart equals end, should not conflict");
        assertTrue(
                MoeGroomingBookOnlineService.isNotConflict(350, 400, 100, 300, 0),
                "When compareStart greater than end, should not conflict");

        // 2.3 有交集的情况
        assertFalse(
                MoeGroomingBookOnlineService.isNotConflict(150, 250, 200, 300, 0),
                "When time ranges overlap, should conflict");
        assertFalse(
                MoeGroomingBookOnlineService.isNotConflict(250, 350, 200, 300, 0),
                "When time ranges overlap, should conflict");
        assertFalse(
                MoeGroomingBookOnlineService.isNotConflict(150, 350, 200, 300, 0),
                "When one range contains another, should conflict");

        // 3. bufferTime < 0 的情况（允许重叠）
        int negativeBuffer = -30;

        // 3.1 compareEnd > start，但重叠未超过buffer绝对值
        assertTrue(
                MoeGroomingBookOnlineService.isNotConflict(290, 350, 200, 300, negativeBuffer),
                "When overlap within negative buffer range, should not conflict");

        // 3.2 compareEnd > start，但重叠超过buffer绝对值
        assertFalse(
                MoeGroomingBookOnlineService.isNotConflict(260, 350, 200, 300, negativeBuffer),
                "When overlap exceeds negative buffer range, should conflict");

        // 3.3 compareEnd > start, 重叠超过buffer绝对值
        assertFalse(
                MoeGroomingBookOnlineService.isNotConflict(250, 350, 200, 300, negativeBuffer),
                "When compareEnd >= start, negative buffer should not affect the result");

        // 3.4 compareEnd > start, 重叠超过buffer绝对值
        assertFalse(
                MoeGroomingBookOnlineService.isNotConflict(150, 250, 200, 300, negativeBuffer),
                "When compareEnd >= start, negative buffer should not affect the result");

        // 4. bufferTime > 0 的情况（与 bufferTime = 0 行为一致）
        int positiveBuffer = 30;

        // 4.1 无交集 和下一个预约的开始时间间隔大于一个buffer time
        assertTrue(
                MoeGroomingBookOnlineService.isNotConflict(100, 200, 250, 300, positiveBuffer),
                "With positive buffer, when no overlap, should not conflict");

        // 4.2 有交集
        assertFalse(
                MoeGroomingBookOnlineService.isNotConflict(150, 250, 200, 300, positiveBuffer),
                "With positive buffer, when overlap exists, should conflict");

        // 4.3 无交集 和下一个预约的开始时间间隔小于一个buffer time
        assertFalse(
                MoeGroomingBookOnlineService.isNotConflict(150, 250, 270, 300, positiveBuffer),
                "With positive buffer, when overlap exists, should conflict");
    }
}
