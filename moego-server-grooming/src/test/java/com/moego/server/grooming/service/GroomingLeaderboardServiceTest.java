package com.moego.server.grooming.service;

import static com.moego.server.grooming.service.utils.ReportUtil.calculateDiscount;
import static com.moego.server.grooming.service.utils.ReportUtil.calculateTax;
import static com.moego.server.grooming.service.utils.ReportUtil.calculateTips;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.common.response.ResponseResult;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.StaffWorkingRangeDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.GroomingCalenderPetInfo;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.grooming.dto.LeaderboardRankInfoDTO;
import com.moego.server.grooming.dto.PetBreedInfoDTO;
import com.moego.server.grooming.dto.StaffBlockInfoDTO;
import com.moego.server.grooming.dto.report.LeaderboardStaffReportDTO;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import com.moego.server.grooming.service.dto.GroomingReportWebAppointment;
import com.moego.server.grooming.service.dto.ReportWebApptPetDetail;
import com.moego.server.grooming.service.dto.report.CollectedPriceDTO;
import com.moego.server.grooming.service.report.ReportCalculateService;
import com.moego.server.grooming.service.report.ReportOrderService;
import com.moego.server.grooming.service.utils.ReportUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GroomingLeaderboardServiceTest {

    @Mock
    private AppointmentMapperProxy appointmentMapper;

    @Mock
    private ReportOrderService reportOrderService;

    @Mock
    private ReportCalculateService reportCalculateService;

    @Mock
    private IBusinessStaffClient iBusinessStaffClient;

    @Mock
    private ICustomerCustomerClient iCustomerClient;

    @Mock
    private IPetClient iPetClient;

    @InjectMocks
    private GroomingLeaderboardService groomingLeaderboardService;

    private List<GroomingReportWebAppointment> buildAppointments() {
        ReportWebApptPetDetail reportWebApptPetDetail = new ReportWebApptPetDetail();
        reportWebApptPetDetail.setStaffId(10001);
        reportWebApptPetDetail.setPetId(20001);
        reportWebApptPetDetail.setServiceId(300001);
        reportWebApptPetDetail.setServiceName("Service Name");
        reportWebApptPetDetail.setServicePrice(BigDecimal.TEN);

        MoeGroomingInvoiceItem moeGroomingInvoiceItem = new MoeGroomingInvoiceItem();
        moeGroomingInvoiceItem.setInvoiceId(900001);

        GroomingReportWebAppointment groomingReportWebAppointment = new GroomingReportWebAppointment();
        groomingReportWebAppointment.setId(12345678);
        groomingReportWebAppointment.setCustomerId(400001);
        groomingReportWebAppointment.setIsPaid((byte) 1);
        groomingReportWebAppointment.setTipsAmount(BigDecimal.ONE);
        groomingReportWebAppointment.setTaxAmount(BigDecimal.ONE);
        groomingReportWebAppointment.setServiceTaxAmount(BigDecimal.ONE);
        groomingReportWebAppointment.setProductTaxAmount(BigDecimal.ZERO);
        groomingReportWebAppointment.setDiscountAmount(BigDecimal.ONE);
        groomingReportWebAppointment.setServiceDiscountAmount(BigDecimal.ONE);
        groomingReportWebAppointment.setProductDiscountAmount(BigDecimal.ZERO);
        groomingReportWebAppointment.setPaidAmount(BigDecimal.TEN);
        groomingReportWebAppointment.setRefundedAmount(BigDecimal.ONE);
        groomingReportWebAppointment.setInvoiceId(900001);

        groomingReportWebAppointment.setPetDetails(Collections.singletonList(reportWebApptPetDetail));
        groomingReportWebAppointment.setInvoiceItems(Collections.singletonList(moeGroomingInvoiceItem));

        return Collections.singletonList(groomingReportWebAppointment);
    }

    private ResponseResult<List<StaffWorkingRangeDto>> buildStaffWorkingRange() {
        TimeRangeDto timeRangeDto = new TimeRangeDto();
        timeRangeDto.setStartTime(540);
        timeRangeDto.setEndTime(1020);

        Map<String, List<TimeRangeDto>> timeRange = new HashMap<>();
        timeRange.put("2022-07-10", Collections.singletonList(timeRangeDto));

        StaffWorkingRangeDto staffWorkingRangeDto = new StaffWorkingRangeDto();
        staffWorkingRangeDto.setStaffId(10001);
        staffWorkingRangeDto.setFirstName("First");
        staffWorkingRangeDto.setLastName("Last");
        staffWorkingRangeDto.setTimeRange(timeRange);

        return ResponseResult.success(Collections.singletonList(staffWorkingRangeDto));
    }

    private List<StaffBlockInfoDTO> buildStaffBlockInfo() {
        StaffBlockInfoDTO staffBlockInfoDTO = new StaffBlockInfoDTO();
        staffBlockInfoDTO.setStaffId(10001);
        staffBlockInfoDTO.setDate("2022-07-10");
        staffBlockInfoDTO.setStartTime(600);
        staffBlockInfoDTO.setEndTime(660);

        return Collections.singletonList(staffBlockInfoDTO);
    }

    private List<LeaderboardStaffReportDTO> buildLeaderboardStaffReport() {
        return Collections.singletonList(LeaderboardStaffReportDTO.builder()
                .staffId(10001)
                .collectedRev(BigDecimal.ZERO)
                .collectedTips(BigDecimal.ZERO)
                .totalAppts(1)
                .totalApptsAll(1)
                .finishedApptNum(0)
                .paidApptNum(1)
                .servicedPetNum(1)
                .totalServiceMinute(0L)
                .totalWorkingMinute(480)
                .totalBlockMinute(60)
                .build());
    }

    private List<LeaderboardRankInfoDTO> buildLeaderboardStaffRankInfo() {
        return Collections.singletonList(LeaderboardRankInfoDTO.builder()
                .id(300001)
                .key("Service Name")
                .value(BigDecimal.valueOf(9))
                .build());
    }

    private List<LeaderboardRankInfoDTO> buildLeaderboardClientRankInfo() {
        return Collections.singletonList(LeaderboardRankInfoDTO.builder()
                .id(400001)
                .key("First Last")
                .value(BigDecimal.TEN.subtract(BigDecimal.ONE))
                .build());
    }

    private List<LeaderboardRankInfoDTO> buildLeaderboardBreedRankInfo() {
        return Collections.singletonList(LeaderboardRankInfoDTO.builder()
                .key("Breed")
                .value(BigDecimal.TEN)
                .build());
    }

    @Test
    void getStaffForLeaderboard() {
        when(reportOrderService.queryWebReportApptEmployee(any(), any(), any(), any()))
                .thenAnswer(invocationOnMock -> buildAppointments());
        when(iBusinessStaffClient.queryRange(any(), any(), any()))
                .thenAnswer(invocationOnMock -> buildStaffWorkingRange());
        when(appointmentMapper.selectBlockByDateRange(any(), any(), any()))
                .thenAnswer(invocationOnMock -> buildStaffBlockInfo());

        List<LeaderboardStaffReportDTO> staffForLeaderboard = groomingLeaderboardService.getStaffForLeaderboard(
                12345678, "2022-07-01", "2022-07-31", new ArrayList<>());
        assertThat(staffForLeaderboard).isEqualTo(buildLeaderboardStaffReport());

        verify(reportCalculateService, Mockito.only()).processMoney(any(), any(), Mockito.anyList(), any());
    }

    @Test
    void getServiceRankForLeaderboard() {
        var appointment = buildAppointments();
        try (MockedStatic<ReportUtil> mockedStatic = mockStatic(ReportUtil.class)) {
            when(reportOrderService.queryWebReportApptEmployee(any(), any(), any(), any()))
                    .thenAnswer(invocationOnMock -> appointment);
            mockedStatic
                    .when(() -> calculateTips(Map.of(), appointment.get(0).getPetDetails()))
                    .thenReturn(Collections.singletonMap(900001, BigDecimal.ONE));
            mockedStatic
                    .when(() -> calculateDiscount(appointment.get(0).getPetDetails(), List.of()))
                    .thenReturn(Collections.singletonMap(900001, BigDecimal.ONE));
            mockedStatic
                    .when(() -> calculateTax(appointment.get(0).getPetDetails(), List.of()))
                    .thenReturn(Collections.emptyMap());

            when(reportOrderService.getInvoiceItemMap(any())).thenAnswer(invocationOnMock -> Collections.EMPTY_MAP);
            List<LeaderboardRankInfoDTO> rankForLeaderboard =
                    groomingLeaderboardService.getServiceRankForLeaderboard(12345678, "2022-07-01", "2022-07-31");
            assertThat(rankForLeaderboard).isEqualTo(buildLeaderboardStaffRankInfo());
        }
    }

    @Test
    void getClientRankForLeaderboard() {
        when(reportOrderService.queryWebReportApptEmployee(any(), any(), any(), any()))
                .thenAnswer(invocationOnMock -> buildAppointments());
        when(reportOrderService.getRefundMap(any()))
                .thenAnswer(invocationOnMock -> Collections.singletonMap(900001, BigDecimal.ONE));
        when(iCustomerClient.queryCustomerListWithDeleted(any())).thenAnswer(invocationOnMock -> {
            MoeBusinessCustomerDTO moeBusinessCustomerDTO = new MoeBusinessCustomerDTO();
            moeBusinessCustomerDTO.setId(400001);
            moeBusinessCustomerDTO.setFirstName("First");
            moeBusinessCustomerDTO.setLastName("Last");
            return Collections.singletonList(moeBusinessCustomerDTO);
        });

        when(reportCalculateService.calculateCollectedPrice(any())).thenAnswer(invocationOnMock -> {
            CollectedPriceDTO collectedPriceDTO = new CollectedPriceDTO();
            collectedPriceDTO.setCollectedServicePrice(BigDecimal.TEN.subtract(BigDecimal.ONE));
            return collectedPriceDTO;
        });

        List<LeaderboardRankInfoDTO> rankForLeaderboard =
                groomingLeaderboardService.getClientRankForLeaderboard(12345678, "2022-07-01", "2022-07-31");
        assertThat(rankForLeaderboard).isEqualTo(buildLeaderboardClientRankInfo());
    }

    @Test
    void getPetBreedRankForLeaderboard() {
        when(appointmentMapper.queryPetBreed(any(), any(), any()))
                .thenAnswer(invocationOnMock -> Collections.singletonList(
                        PetBreedInfoDTO.builder().petId(70001).amount(10).build()));
        when(iPetClient.getGroomingCalenderPetInfo(any())).thenAnswer(invocationOnMock -> {
            GroomingCalenderPetInfo petInfo = new GroomingCalenderPetInfo();
            petInfo.setPetId(70001);
            petInfo.setPetBreed("Breed");
            petInfo.setPetName("Pet Name");
            return Collections.singletonList(petInfo);
        });

        List<LeaderboardRankInfoDTO> rankForLeaderboard =
                groomingLeaderboardService.getPetBreedRankForLeaderboard(12345678, "2022-07-01", "2022-07-31");
        assertThat(rankForLeaderboard).isEqualTo(buildLeaderboardBreedRankInfo());
    }
}
