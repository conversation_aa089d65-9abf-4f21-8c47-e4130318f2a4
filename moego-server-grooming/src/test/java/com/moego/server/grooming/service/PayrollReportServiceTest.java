package com.moego.server.grooming.service;

import static java.math.RoundingMode.HALF_UP;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;

import com.moego.common.enums.PayrollConst;
import com.moego.server.business.client.IPayrollCalculationClient;
import com.moego.server.business.client.IPayrollSettingClient;
import com.moego.server.business.dto.BusinessPayrollSettingDTO;
import com.moego.server.business.dto.PayrollExceptionDTO;
import com.moego.server.business.dto.PayrollTierConfig;
import com.moego.server.business.dto.StaffPayrollSettingDTO;
import com.moego.server.grooming.service.dto.report.StaffRevenueDetail;
import com.moego.server.grooming.service.report.PayrollCalculateService;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PayrollReportServiceTest {

    @InjectMocks
    private PayrollCalculateService payrollCalculateService;

    @Mock
    private IPayrollSettingClient iPayrollSettingClient;

    @Mock
    private IPayrollCalculationClient iPayrollCalculationClient;

    @Test
    public void testProcessStaffCommissionByFixedRate() {
        var staffPayrollSetting = getStaffPayrollSetting();
        Mockito.when(iPayrollSettingClient.getBusinessPayrollSetting(any())).thenReturn(getBusinessPayrollSetting());
        Mockito.when(iPayrollSettingClient.getPayrollExceptionList(any())).thenReturn(getPayrollExceptionList());
        Mockito.when(iPayrollSettingClient.getStaffPayrollSettingListByStaffIds(10000L, List.of(100)))
                .thenReturn(Collections.singletonList(staffPayrollSetting));

        StaffRevenueDetail employee = getReportWebEmployeeForException();
        employee.setStaffId(100);
        employee.setCollectedTips(BigDecimal.valueOf(24));

        // by fixed rate: collectedService=220, serviceRate=60%, collectedAddon=70, addon=30%
        // exceptionCollectedServicePrice=100, exceptionServiceRate=20%, exceptionCollectedAddon=50,
        // exceptionAddonRate=50%
        // collectedTips=24, tipsRate=95%
        staffPayrollSetting.setServiceCommissionType(PayrollConst.SERVICE_COMMISSION_TYPE_FIXED_RATE);
        payrollCalculateService.processStaffCommission(10000, Collections.singletonList(employee));
        // serviceCommission = (220-100) * 60% + 100 * 20% = 72 + 20 = 92
        assertThat(employee.getServiceCommission()).isEqualByComparingTo(BigDecimal.valueOf(92));
        // addonCommission = (70-50) * 30% + 50 * 50% = 6 + 25 = 31
        assertThat(employee.getAddonCommission()).isEqualByComparingTo(BigDecimal.valueOf(31));
        // tipsCommission = 24 * 95% = 22.8
        assertThat(employee.getTipsCommission()).isEqualByComparingTo(BigDecimal.valueOf(22.8));
        assertThat(employee.getTotalCommission()).isEqualByComparingTo(BigDecimal.valueOf(92 + 31 + 22.8));
    }

    @Test
    public void testProcessStaffCommissionByTier() {
        var staffPayrollSetting = getStaffPayrollSetting();
        Mockito.when(iPayrollSettingClient.getBusinessPayrollSetting(any())).thenReturn(getBusinessPayrollSetting());
        Mockito.when(iPayrollSettingClient.getPayrollExceptionList(any())).thenReturn(getPayrollExceptionList());
        Mockito.when(iPayrollSettingClient.getStaffPayrollSettingListByStaffIds(10000L, List.of(100)))
                .thenReturn(Collections.singletonList(staffPayrollSetting));
        Mockito.when(iPayrollCalculationClient.calculateCommissionByTierRate(any()))
                .thenReturn(BigDecimal.TEN);

        StaffRevenueDetail employee = getReportWebEmployeeForException();
        employee.setStaffId(100);
        employee.setCollectedTips(BigDecimal.valueOf(24));
        // by tier rate
        staffPayrollSetting.setServiceCommissionType(PayrollConst.SERVICE_COMMISSION_TYPE_TIER_RATE);
        staffPayrollSetting.setTierType(PayrollConst.TIER_TYPE_PROGRESSIVE);
        payrollCalculateService.processStaffCommission(10000, Collections.singletonList(employee));
        // serviceCommission = [(220-100) - 100] * tierRate3 + (100 - 10) * tierRate2 + 10 * tierRate1 + 100 *
        // exceptionRate = 20 * 30% + 90 * 20% + 10 * 10% + 100 * 20% = 6+18+1+20 = 45
        assertThat(employee.getServiceCommission()).isEqualByComparingTo(BigDecimal.valueOf(30));
        // addonCommission = [(70-50) - 10] * tierRate2 + 10 * tierRate1 + 50 * exceptionRate = 10 * 20% + 10 * 10% + 50
        // * 50% = 2 + 1 + 25 = 13
        assertThat(employee.getAddonCommission()).isEqualByComparingTo(BigDecimal.valueOf(35));
        // tipsCommission = 24 * 95% = 22.8
        assertThat(employee.getTipsCommission()).isEqualByComparingTo(BigDecimal.valueOf(22.8));
        assertThat(employee.getTotalCommission()).isEqualByComparingTo(BigDecimal.valueOf(45 + 20 + 22.8));
    }

    @Test
    public void testCalculateCommissionByFixedRate() {
        BigDecimal collectedNull = null;
        BigDecimal rateNull = null;
        BigDecimal collectedNormal = BigDecimal.valueOf(100);
        BigDecimal rateNormal = BigDecimal.valueOf(50);

        BigDecimal commission1 = payrollCalculateService.calculateCommissionByRate(collectedNull, rateNull);
        assertThat(commission1).isEqualByComparingTo(BigDecimal.ZERO);

        BigDecimal commission2 = payrollCalculateService.calculateCommissionByRate(collectedNormal, rateNormal);
        assertThat(commission2).isEqualByComparingTo(BigDecimal.valueOf(50));
    }

    @Test
    public void testCalculateServiceExceptions() {
        StaffRevenueDetail employeeReport = getReportWebEmployeeForException();
        List<PayrollExceptionDTO> payrollExceptions = getPayrollExceptionList();

        payrollCalculateService.calculateServiceExceptions(employeeReport, payrollExceptions, true);
        assertThat(employeeReport.getExceptionServiceBase()).isEqualByComparingTo(BigDecimal.valueOf(100));
        assertThat(employeeReport.getServiceCommission())
                .isEqualByComparingTo(BigDecimal.valueOf(100)
                        .multiply(payrollExceptions.get(0).getRate().scaleByPowerOfTen(-2)));
        assertThat(employeeReport.getExceptionAddonBase()).isEqualByComparingTo(BigDecimal.valueOf(50));
        assertThat(employeeReport.getAddonCommission())
                .isEqualByComparingTo(BigDecimal.valueOf(50)
                        .multiply(payrollExceptions.get(1).getRate().scaleByPowerOfTen(-2)));
    }

    @Test
    public void testCalculateTipsCommission() {
        StaffPayrollSettingDTO payrollSetting = getStaffPayrollSetting();

        StaffRevenueDetail employeeReport =
                StaffRevenueDetail.builder().collectedTips(null).build();
        payrollCalculateService.calculateTipsCommission(employeeReport, payrollSetting.getTipsPayRate(), true);
        assertThat(employeeReport.getTipsCommission()).isEqualByComparingTo(BigDecimal.ZERO);

        StaffRevenueDetail employeeReport2 = StaffRevenueDetail.builder()
                .collectedTips(BigDecimal.valueOf(23))
                .build();
        payrollCalculateService.calculateTipsCommission(employeeReport2, payrollSetting.getTipsPayRate(), true);
        assertThat(employeeReport2.getTipsCommission())
                .isEqualByComparingTo(employeeReport2
                        .getCollectedTips()
                        .multiply(payrollSetting.getTipsPayRate().scaleByPowerOfTen(-2))
                        .setScale(2, HALF_UP));
    }

    @Test
    public void testCalculateStaffServiceCommissionBasedOnExpected() {
        StaffRevenueDetail employee = getReportWebEmployeeForException();
        StaffPayrollSettingDTO payrollSetting = getStaffPayrollSetting();
        // case1：没有设置 finishExpected 时，计算金额为 0
        payrollCalculateService.calculateStaffServiceCommission(employee, payrollSetting, false);
        assertThat(employee.getServiceCommission()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(employee.getAddonCommission()).isEqualByComparingTo(BigDecimal.ZERO);

        // case2：finishExpectedService = 100, finishExpectedAddonPrice = 20，commissionType = by fixed rate
        StaffRevenueDetail employee2 = getReportWebEmployeeForException();
        employee2.setFinishExpectedServicePrice(BigDecimal.valueOf(100));
        employee2.setFinishExpectedAddonPrice(BigDecimal.valueOf(20));
        payrollSetting.setServiceCommissionType(PayrollConst.SERVICE_COMMISSION_TYPE_FIXED_RATE);
        payrollCalculateService.calculateStaffServiceCommission(employee2, payrollSetting, false);
        // 预期：Commission 等于 expected金额 乘以对应 payRate
        assertThat(employee2.getServiceCommission())
                .isEqualByComparingTo(employee2
                        .getFinishExpectedServicePrice()
                        .multiply(payrollSetting.getServicePayRate().scaleByPowerOfTen(-2)));
        assertThat(employee2.getAddonCommission())
                .isEqualByComparingTo(employee2
                        .getFinishExpectedAddonPrice()
                        .multiply(payrollSetting.getAddonPayRate().scaleByPowerOfTen(-2)));

        // case3：finishExpectedService = 120, finishExpectedAddonPrice = 20，commissionType = by tier rate, progressive
        StaffRevenueDetail employee3 = getReportWebEmployeeForException();
        employee3.setFinishExpectedServicePrice(BigDecimal.valueOf(120));
        employee3.setFinishExpectedAddonPrice(BigDecimal.valueOf(20));
        payrollSetting.setServiceCommissionType(PayrollConst.SERVICE_COMMISSION_TYPE_TIER_RATE);
        payrollSetting.setTierType(PayrollConst.TIER_TYPE_PROGRESSIVE);
        payrollCalculateService.calculateStaffServiceCommission(employee3, payrollSetting, false);

        assertThat(employee3.getServiceCommission()).isEqualByComparingTo(BigDecimal.valueOf(0));
        assertThat(employee3.getAddonCommission()).isEqualByComparingTo(BigDecimal.valueOf(0));
    }

    @Test
    public void testCalculateServiceExceptionsBasedOnExpected() {
        StaffRevenueDetail employee = getReportWebEmployeeForException();
        List<PayrollExceptionDTO> payrollExceptions = getPayrollExceptionList();

        payrollCalculateService.calculateServiceExceptions(employee, payrollExceptions, false);
        assertThat(employee.getExceptionServiceBase()).isZero();
        assertThat(employee.getServiceCommission()).isZero();
        assertThat(employee.getExceptionAddonBase()).isEqualByComparingTo(BigDecimal.valueOf(20));
        assertThat(employee.getAddonCommission())
                .isEqualByComparingTo(BigDecimal.valueOf(20)
                        .multiply(payrollExceptions.get(1).getRate().scaleByPowerOfTen(-2)));
    }

    @Test
    public void testCalculateTipsCommissionBasedOnExpected() {
        StaffPayrollSettingDTO payrollSetting = getStaffPayrollSetting();

        // finishExpectedTips = 10, tipsCommission = 10 * 95% = 9.5
        StaffRevenueDetail employee = StaffRevenueDetail.builder()
                .collectedTips(BigDecimal.valueOf(23))
                .finishExpectedTips(BigDecimal.valueOf(10))
                .build();
        payrollCalculateService.calculateTipsCommission(employee, payrollSetting.getTipsPayRate(), false);
        assertThat(employee.getTipsCommission())
                .isEqualByComparingTo(employee.getFinishExpectedTips()
                        .multiply(payrollSetting.getTipsPayRate().scaleByPowerOfTen(-2))
                        .setScale(2, HALF_UP));

        // finishExpectedTips = null, tipsCommission = 0
        StaffRevenueDetail employee2 = StaffRevenueDetail.builder().build();
        payrollCalculateService.calculateTipsCommission(employee2, payrollSetting.getTipsPayRate(), false);
        assertThat(employee2.getTipsCommission()).isEqualByComparingTo(BigDecimal.ZERO);
    }

    private StaffRevenueDetail getReportWebEmployeeForException() {
        StaffRevenueDetail employee = StaffRevenueDetail.builder()
                .staffId(100)
                .collectedServicePrice(BigDecimal.valueOf(220))
                .collectedAddonPrice(BigDecimal.valueOf(70))
                .build();

        Map<Integer, BigDecimal> serviceCollectedMap = new HashMap<>();
        serviceCollectedMap.put(1, BigDecimal.valueOf(100));
        serviceCollectedMap.put(2, BigDecimal.valueOf(120));
        Map<Integer, BigDecimal> addonCollectedMap = new HashMap<>();
        addonCollectedMap.put(3, BigDecimal.valueOf(50));
        addonCollectedMap.put(4, BigDecimal.valueOf(20));
        employee.getServiceCollectedMap().putAll(serviceCollectedMap);
        employee.getAddonCollectedMap().putAll(addonCollectedMap);

        Map<Integer, BigDecimal> serviceExpectedMap = new HashMap<>();
        //        serviceExpectedMap.put(1, BigDecimal.valueOf(0));
        serviceExpectedMap.put(2, BigDecimal.valueOf(120));
        Map<Integer, BigDecimal> addonExpectedMap = new HashMap<>();
        addonExpectedMap.put(3, BigDecimal.valueOf(20));
        addonExpectedMap.put(4, BigDecimal.valueOf(20));
        employee.getServiceExpectedMap().putAll(serviceExpectedMap);
        employee.getAddonExpectedMap().putAll(addonExpectedMap);

        return employee;
    }

    private BusinessPayrollSettingDTO getBusinessPayrollSetting() {
        BusinessPayrollSettingDTO businessPayrollSetting = new BusinessPayrollSettingDTO();
        businessPayrollSetting.setBusinessId(10000);
        businessPayrollSetting.setCompanyId(10000L);
        businessPayrollSetting.setServiceCommissionBased(PayrollConst.COMMISSION_BASED_ACTUAL_PAYMENT);
        return businessPayrollSetting;
    }

    private StaffPayrollSettingDTO getStaffPayrollSetting() {
        StaffPayrollSettingDTO staffSetting = new StaffPayrollSettingDTO();
        staffSetting.setStaffId(100);
        // fixed rate config
        staffSetting.setServiceCommissionEnable(true);
        staffSetting.setServicePayRate(BigDecimal.valueOf(60));
        staffSetting.setAddonPayRate(BigDecimal.valueOf(30));

        // tier rate config
        PayrollTierConfig tier1 = new PayrollTierConfig();
        tier1.setStart(BigDecimal.ZERO);
        tier1.setEnd(BigDecimal.valueOf(10));
        tier1.setRate(BigDecimal.valueOf(10));
        PayrollTierConfig tier2 = new PayrollTierConfig();
        tier2.setStart(BigDecimal.valueOf(10.01));
        tier2.setEnd(BigDecimal.valueOf(100));
        tier2.setRate(BigDecimal.valueOf(20));
        PayrollTierConfig tier3 = new PayrollTierConfig();
        tier3.setStart(BigDecimal.valueOf(100.01));
        tier3.setEnd(BigDecimal.valueOf(-1));
        tier3.setRate(BigDecimal.valueOf(30));
        staffSetting.setTierConfig(Arrays.asList(tier1, tier2, tier3));

        // tip config
        staffSetting.setTipsCommissionEnable(true);
        staffSetting.setTipsPayRate(BigDecimal.valueOf(95));
        return staffSetting;
    }

    private List<PayrollExceptionDTO> getPayrollExceptionList() {
        // 命中 service 和 staff
        PayrollExceptionDTO payrollException1 = new PayrollExceptionDTO();
        payrollException1.setServiceId(1);
        payrollException1.setRate(BigDecimal.valueOf(20));
        payrollException1.setIsAllStaff(false);
        payrollException1.setStaffIdList(Collections.singletonList(100));
        // 命中 addon 和 staff
        PayrollExceptionDTO payrollException2 = new PayrollExceptionDTO();
        payrollException2.setServiceId(3);
        payrollException2.setRate(BigDecimal.valueOf(50));
        payrollException2.setIsAllStaff(true);
        payrollException2.setStaffIdList(Collections.singletonList(101));
        // 没命中 staff
        PayrollExceptionDTO payrollException3 = new PayrollExceptionDTO();
        payrollException3.setServiceId(4);
        payrollException3.setRate(BigDecimal.valueOf(50));
        payrollException3.setIsAllStaff(false);
        payrollException3.setStaffIdList(Collections.singletonList(101));
        return Arrays.asList(payrollException1, payrollException2, payrollException3);
    }
}
