package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.google.type.LatLng;
import com.moego.common.dto.FeatureQuotaDto;
import com.moego.common.enums.FeatureConst;
import com.moego.common.utils.RedisUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.BizException;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.service.dto.RouteOptimizationRequest;
import com.moego.server.grooming.service.dto.RouteOptimizationResponse;
import com.moego.server.grooming.service.dto.RouteResult;
import com.moego.server.grooming.service.ob.OBAddressService;
import com.moego.server.payment.api.IPaymentPlanService;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class RouteOptimizationServiceTest {

    @Mock
    private PetDetailMapperProxy petDetailMapper;

    @Mock
    private GoogleMapService googleMapService;

    @Mock
    private IBusinessBusinessClient businessClient;

    @Mock
    private OBAddressService obAddressService;

    @Mock
    private IBusinessStaffClient staffClient;

    @Mock
    private IPaymentPlanService paymentPlanService;

    @Mock
    private RedisUtil redisUtil;

    @InjectMocks
    private RouteOptimizationService routeOptimizationService;

    private static final Long COMPANY_ID = 1L;
    private static final Integer BUSINESS_ID = 1;
    private static final Integer STAFF_ID = 1;
    private static final LocalDate TEST_DATE = LocalDate.now();

    private RouteOptimizationRequest request;
    private SmartScheduleGroomingDetailsDTO appointment1;
    private SmartScheduleGroomingDetailsDTO appointment2;
    private CustomerAddressDto address1;
    private CustomerAddressDto address2;
    private FeatureQuotaDto featureQuota;

    @BeforeEach
    void setUp() {
        request = new RouteOptimizationRequest();
        request.setDate(TEST_DATE);
        request.setStaffId(STAFF_ID);
        request.setStartLocationLat("37.7749");
        request.setStartLocationLnt("-122.4194");
        request.setEndLocationLat("37.7750");
        request.setEndLocationLnt("-122.4195");

        // Setup test appointments
        appointment1 = new SmartScheduleGroomingDetailsDTO();
        appointment1.setGroomingId(1);
        appointment1.setCustomerId(1);
        appointment1.setStartTime(540L); // 9:00
        appointment1.setEndTime(600L); // 10:00

        appointment2 = new SmartScheduleGroomingDetailsDTO();
        appointment2.setGroomingId(2);
        appointment2.setCustomerId(2);
        appointment2.setStartTime(660L); // 11:00
        appointment2.setEndTime(720L); // 12:00

        // Setup addresses
        address1 = new CustomerAddressDto();
        address1.setLat("37.7749");
        address1.setLng("-122.4194");

        address2 = new CustomerAddressDto();
        address2.setLat("37.7750");
        address2.setLng("-122.4195");

        // Setup feature quota
        featureQuota = new FeatureQuotaDto();
        featureQuota.setQuota(20);
        featureQuota.setEnable(true);
    }

    @Test
    void getRouteOptimizationResult_WhenFeatureNotAvailable_ShouldThrowException() {
        // Arrange
        FeatureQuotaDto disablFeatureQuotaDto = new FeatureQuotaDto();
        disablFeatureQuotaDto.setEnable(false);
        when(paymentPlanService.queryCompanyPlanFeatureByCidCode(anyInt(), eq(FeatureConst.FC_ROUTE_OPTIMIZATION)))
                .thenReturn(disablFeatureQuotaDto);

        // Act & Assert
        assertThatThrownBy(() -> routeOptimizationService.getRouteOptimizationResult(COMPANY_ID, BUSINESS_ID, request))
                .isInstanceOf(BizException.class)
                .hasFieldOrPropertyWithValue("code", Code.CODE_TOO_MANY_REQUESTS.getNumber());
    }

    @Test
    void getRouteOptimizationResult_WhenNoAppointments_ShouldThrowException() {
        // Arrange
        when(paymentPlanService.queryCompanyPlanFeatureByCidCode(anyInt(), eq(FeatureConst.FC_ROUTE_OPTIMIZATION)))
                .thenReturn(featureQuota);
        when(petDetailMapper.queryInProgressByBusinessIdBetweenDates(anyInt(), anyString(), anyString(), anyList()))
                .thenReturn(List.of());

        // Act & Assert
        assertThatThrownBy(() -> routeOptimizationService.getRouteOptimizationResult(COMPANY_ID, BUSINESS_ID, request))
                .isInstanceOf(BizException.class)
                .hasFieldOrPropertyWithValue("code", Code.CODE_PARAMS_ERROR.getNumber());
    }

    @Test
    void getRouteOptimizationResult_WhenValidRequest_ShouldReturnOptimizedRoute() {
        // Arrange
        when(paymentPlanService.queryCompanyPlanFeatureByCidCode(anyInt(), eq(FeatureConst.FC_ROUTE_OPTIMIZATION)))
                .thenReturn(featureQuota);
        when(petDetailMapper.queryInProgressByBusinessIdBetweenDates(anyInt(), anyString(), anyString(), anyList()))
                .thenReturn(List.of(appointment1, appointment2));
        when(obAddressService.batchGetPrimaryAddress(anyList())).thenReturn(Map.of(1, address1, 2, address2));
        when(staffClient.getStaffWorkingHourWithOverrideDate(anyInt(), anyList(), anyString(), anyString()))
                .thenReturn(Map.of(STAFF_ID, Map.of(TEST_DATE, List.of(new TimeRangeDto(480, 1080)))));
        when(businessClient.getBusinessInfo(any())).thenReturn(new MoeBusinessDto());

        // Mock GoogleMapService
        RouteResult mockRouteResult = new RouteResult(
                List.of(0, 1), // waypointIndexList
                "mock_polyline", // polyline
                new BigDecimal("5.5"), // drivingMiles
                30, // drivingMinutes
                List.of(10, 10, 10), // drivingMinuteList (需要 n+1 个元素)
                List.of( // drivingMileList (需要 n+1 个元素)
                        new BigDecimal("1.5"), new BigDecimal("2.0"), new BigDecimal("2.0")));

        when(googleMapService.queryAndCacheRoutes(any(LatLng.class), any(LatLng.class), anyList(), anyBoolean()))
                .thenReturn(mockRouteResult);

        // Act
        RouteOptimizationResponse response =
                routeOptimizationService.getRouteOptimizationResult(COMPANY_ID, BUSINESS_ID, request);

        // Assert
        assertThat(response).isNotNull().satisfies(r -> {
            assertThat(r.getWayPointCount()).isEqualTo(2);
            assertThat(r.getOptimizedDrivingMinutes()).isEqualTo(30);
            assertThat(r.getOptimizedDrivingMiles()).isEqualTo(new BigDecimal("5.5"));
            assertThat(r.getOptimizedPolyline()).containsExactly("mock_polyline");
        });
    }

    @Test
    void checkRouteOptimization_WhenUnlimitedQuota_ShouldReturnTrue() {
        // Arrange
        featureQuota.setQuota(FeatureConst.MAX_QUOTA);
        when(paymentPlanService.queryCompanyPlanFeatureByCidCode(anyInt(), eq(FeatureConst.FC_ROUTE_OPTIMIZATION)))
                .thenReturn(featureQuota);

        // Act
        boolean result = routeOptimizationService.checkRouteOptimization(COMPANY_ID);

        // Assert
        assertThat(result).isTrue();
    }

    @Test
    void checkRouteOptimization_WhenQuotaAvailable_ShouldReturnTrue() {
        // Arrange
        featureQuota.setQuota(20);
        when(paymentPlanService.queryCompanyPlanFeatureByCidCode(anyInt(), eq(FeatureConst.FC_ROUTE_OPTIMIZATION)))
                .thenReturn(featureQuota);
        when(redisUtil.get(anyString())).thenReturn("10");

        // Act
        boolean result = routeOptimizationService.checkRouteOptimization(COMPANY_ID);

        // Assert
        assertThat(result).isTrue();
    }

    @Test
    void checkRouteOptimization_WhenNoQuotaLeft_ShouldReturnFalse() {
        // Arrange
        featureQuota.setQuota(20);
        when(paymentPlanService.queryCompanyPlanFeatureByCidCode(anyInt(), eq(FeatureConst.FC_ROUTE_OPTIMIZATION)))
                .thenReturn(featureQuota);
        when(redisUtil.get(anyString())).thenReturn("20");

        // Act
        boolean result = routeOptimizationService.checkRouteOptimization(COMPANY_ID);

        // Assert
        assertThat(result).isFalse();
    }
}
