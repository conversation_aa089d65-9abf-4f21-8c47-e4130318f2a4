package com.moego.server.grooming.util;

import com.moego.server.grooming.utils.ExcelUtil;
import com.moego.server.grooming.web.vo.ob.ExportAbandonedClientVO;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

/**
 * {@link ExcelUtil} tester.
 */
class ExcelUtilTest {

    /**
     * {@link ExcelUtil#writeExcel(OutputStream, String, Class, List)}
     */
    @Test
    @Disabled("This test will write a file to the project root directory, no assert needed.")
    void writeExcel() throws IOException {
        OutputStream os = new FileOutputStream("test.xlsx");

        ExportAbandonedClientVO vo = new ExportAbandonedClientVO();
        vo.setStatus("test");
        vo.setType("test");
        vo.setEmail("<EMAIL>");
        vo.setAddress("test");
        vo.setContact("1234567890");
        vo.setPetName("test");

        ExcelUtil.writeExcel(os, "test", ExportAbandonedClientVO.class, List.of(vo));
    }
}
