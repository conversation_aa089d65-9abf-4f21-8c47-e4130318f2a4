package com.moego.server.grooming.service.printcard;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

class PrintCardServiceTest {

    @Test
    @DisplayName("Should sort by check-in time when both have valid check-in times")
    void sortByCheckInTimeWhenBothHaveValidCheckInTimes() {
        Map<Integer, MoeGroomingAppointment> appointmentMap = new HashMap<>();
        MoeGroomingAppointment appointment1 = mock(MoeGroomingAppointment.class);
        MoeGroomingAppointment appointment2 = mock(MoeGroomingAppointment.class);
        when(appointment1.getCheckInTime()).thenReturn(1L);
        when(appointment2.getCheckInTime()).thenReturn(2L);
        appointmentMap.put(1, appointment1);
        appointmentMap.put(2, appointment2);

        MoeGroomingPetDetail pet1 = new MoeGroomingPetDetail();
        pet1.setGroomingId(1);
        pet1.setServiceId(1);

        MoeGroomingPetDetail pet2 = new MoeGroomingPetDetail();
        pet2.setGroomingId(2);
        pet2.setServiceId(2);

        var serviceNameMap = Map.of(1, "Service A", 2, "Service B");

        Comparator<MoeGroomingPetDetail> comparator =
                PrintCardService.sortByCheckInTimeAndServiceName(appointmentMap, serviceNameMap);
        assertEquals(-1, comparator.compare(pet1, pet2));
    }

    @Test
    @DisplayName("Should sort by service name when both have invalid check-in times")
    void sortByServiceNameWhenBothHaveInvalidCheckInTimes() {
        Map<Integer, MoeGroomingAppointment> appointmentMap = new HashMap<>();
        MoeGroomingAppointment appointment1 = mock(MoeGroomingAppointment.class);
        MoeGroomingAppointment appointment2 = mock(MoeGroomingAppointment.class);
        when(appointment1.getCheckInTime()).thenReturn(0L);
        when(appointment2.getCheckInTime()).thenReturn(0L);
        appointmentMap.put(1, appointment1);
        appointmentMap.put(2, appointment2);

        MoeGroomingPetDetail pet1 = new MoeGroomingPetDetail();
        pet1.setGroomingId(1);
        pet1.setServiceId(1);

        MoeGroomingPetDetail pet2 = new MoeGroomingPetDetail();
        pet2.setGroomingId(2);
        pet2.setServiceId(2);

        var serviceNameMap = Map.of(1, "Service B", 2, "Service A");

        Comparator<MoeGroomingPetDetail> comparator =
                PrintCardService.sortByCheckInTimeAndServiceName(appointmentMap, serviceNameMap);
        assertEquals(1, comparator.compare(pet1, pet2));
    }

    @Test
    @DisplayName("Should sort by service name when check-in times are equal")
    void sortByServiceNameWhenCheckInTimesAreEqual() {
        Map<Integer, MoeGroomingAppointment> appointmentMap = new HashMap<>();
        MoeGroomingAppointment appointment1 = mock(MoeGroomingAppointment.class);
        MoeGroomingAppointment appointment2 = mock(MoeGroomingAppointment.class);
        when(appointment1.getCheckInTime()).thenReturn(1L);
        when(appointment2.getCheckInTime()).thenReturn(1L);
        appointmentMap.put(1, appointment1);
        appointmentMap.put(2, appointment2);

        MoeGroomingPetDetail pet1 = new MoeGroomingPetDetail();
        pet1.setGroomingId(1);
        pet1.setServiceId(1);

        MoeGroomingPetDetail pet2 = new MoeGroomingPetDetail();
        pet2.setGroomingId(2);
        pet2.setServiceId(2);

        var serviceNameMap = Map.of(1, "Service B", 2, "Service A");

        Comparator<MoeGroomingPetDetail> comparator =
                PrintCardService.sortByCheckInTimeAndServiceName(appointmentMap, serviceNameMap);
        assertEquals(1, comparator.compare(pet1, pet2));
    }

    @Test
    @DisplayName("Should sort by valid check-in time when one has invalid check-in time")
    void sortByValidCheckInTimeWhenOneHasInvalidCheckInTime() {
        Map<Integer, MoeGroomingAppointment> appointmentMap = new HashMap<>();
        MoeGroomingAppointment appointment1 = mock(MoeGroomingAppointment.class);
        MoeGroomingAppointment appointment2 = mock(MoeGroomingAppointment.class);
        when(appointment1.getCheckInTime()).thenReturn(0L);
        when(appointment2.getCheckInTime()).thenReturn(1L);
        appointmentMap.put(1, appointment1);
        appointmentMap.put(2, appointment2);

        MoeGroomingPetDetail pet1 = new MoeGroomingPetDetail();
        pet1.setGroomingId(1);
        pet1.setGroomingId(1);

        MoeGroomingPetDetail pet2 = new MoeGroomingPetDetail();
        pet2.setGroomingId(2);
        pet2.setGroomingId(2);

        var serviceNameMap = Map.of(1, "Service A", 2, "Service B");

        Comparator<MoeGroomingPetDetail> comparator =
                PrintCardService.sortByCheckInTimeAndServiceName(appointmentMap, serviceNameMap);
        assertEquals(1, comparator.compare(pet1, pet2));
    }

    @Test
    @DisplayName("Should return Long.MAX_VALUE when appointment is not found")
    void returnMaxValueWhenAppointmentIsNotFound() {
        Map<Integer, MoeGroomingAppointment> appointmentMap = new HashMap<>();

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setGroomingId(1);

        assertEquals(Long.MAX_VALUE, PrintCardService.getCheckInTime(appointmentMap, petDetail));
    }
}
