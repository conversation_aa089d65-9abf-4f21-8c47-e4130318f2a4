package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.CustomerPetEnum;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentDTO;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentPetDetailDTO;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MoeAppointmentQueryServiceTest {

    @Mock
    private IPetClient iPetClient;

    @InjectMocks
    private MoeAppointmentQueryService moeAppointmentQueryService;

    @DisplayName("Should return appointments when pets are not passed away")
    @Test
    void filterPassAwayPetAppointment_WhenNoPetsPassedAway_ReturnAppointments() {
        // Given
        CustomerGroomingAppointmentDTO customerGroomingAppointmentDTO1 = new CustomerGroomingAppointmentDTO();
        customerGroomingAppointmentDTO1.setId(1);
        CustomerGroomingAppointmentPetDetailDTO petDetailDTO11 = new CustomerGroomingAppointmentPetDetailDTO();
        petDetailDTO11.setGroomingId(1);
        petDetailDTO11.setPetId(11);

        CustomerGroomingAppointmentPetDetailDTO petDetailDTO12 = new CustomerGroomingAppointmentPetDetailDTO();
        petDetailDTO12.setGroomingId(1);
        petDetailDTO12.setPetId(12);

        customerGroomingAppointmentDTO1.setPetDetails(List.of());
        List<CustomerGroomingAppointmentDTO> customerAppointmentDTOS = List.of(customerGroomingAppointmentDTO1);

        CustomerPetDetailDTO pet11 = new CustomerPetDetailDTO();
        pet11.setLifeStatus(CustomerPetEnum.LIFE_STATUS_ALIVE.intValue());
        CustomerPetDetailDTO pet12 = new CustomerPetDetailDTO();
        pet11.setLifeStatus(CustomerPetEnum.LIFE_STATUS_ALIVE.intValue());
        when(iPetClient.getCustomerPetListByIdList(anyList())).thenReturn(List.of(pet11, pet12));

        // When
        List<CustomerGroomingAppointmentDTO> result =
                moeAppointmentQueryService.filterPassAwayPetAppointment(customerAppointmentDTOS);

        // Then
        assertThat(result).isNotEmpty();
    }

    @DisplayName("Should return empty list when all pets are passed away")
    @Test
    void filterPassAwayPetAppointment_WhenAllPetsPassedAway_ReturnAppointments() {
        // Given
        CustomerGroomingAppointmentDTO customerGroomingAppointmentDTO1 = new CustomerGroomingAppointmentDTO();
        customerGroomingAppointmentDTO1.setId(1);
        CustomerGroomingAppointmentPetDetailDTO petDetailDTO11 = new CustomerGroomingAppointmentPetDetailDTO();
        petDetailDTO11.setGroomingId(1);
        petDetailDTO11.setPetId(11);

        CustomerGroomingAppointmentPetDetailDTO petDetailDTO12 = new CustomerGroomingAppointmentPetDetailDTO();
        petDetailDTO12.setGroomingId(1);
        petDetailDTO12.setPetId(12);

        customerGroomingAppointmentDTO1.setPetDetails(List.of());
        List<CustomerGroomingAppointmentDTO> customerAppointmentDTOS = List.of(customerGroomingAppointmentDTO1);

        CustomerPetDetailDTO pet11 = new CustomerPetDetailDTO();
        pet11.setLifeStatus(CustomerPetEnum.LIFE_STATUS_DIE.intValue());
        CustomerPetDetailDTO pet12 = new CustomerPetDetailDTO();
        pet11.setLifeStatus(CustomerPetEnum.LIFE_STATUS_DIE.intValue());
        when(iPetClient.getCustomerPetListByIdList(anyList())).thenReturn(List.of(pet11, pet12));

        // When
        List<CustomerGroomingAppointmentDTO> result =
                moeAppointmentQueryService.filterPassAwayPetAppointment(customerAppointmentDTOS);

        // Then
        assertThat(result).isNotEmpty();
    }

    @DisplayName("Should return empty list when same pets are passed away")
    @Test
    void filterPassAwayPetAppointment_WhenSamePetsPassedAway_ReturnAppointments() {
        // Given
        CustomerGroomingAppointmentDTO customerGroomingAppointmentDTO1 = new CustomerGroomingAppointmentDTO();
        customerGroomingAppointmentDTO1.setId(1);
        CustomerGroomingAppointmentPetDetailDTO petDetailDTO11 = new CustomerGroomingAppointmentPetDetailDTO();
        petDetailDTO11.setGroomingId(1);
        petDetailDTO11.setPetId(11);

        CustomerGroomingAppointmentPetDetailDTO petDetailDTO12 = new CustomerGroomingAppointmentPetDetailDTO();
        petDetailDTO12.setGroomingId(1);
        petDetailDTO12.setPetId(12);

        customerGroomingAppointmentDTO1.setPetDetails(List.of());
        List<CustomerGroomingAppointmentDTO> customerAppointmentDTOS = List.of(customerGroomingAppointmentDTO1);

        CustomerPetDetailDTO pet11 = new CustomerPetDetailDTO();
        pet11.setLifeStatus(CustomerPetEnum.LIFE_STATUS_ALIVE.intValue());
        CustomerPetDetailDTO pet12 = new CustomerPetDetailDTO();
        pet11.setLifeStatus(CustomerPetEnum.LIFE_STATUS_DIE.intValue());
        when(iPetClient.getCustomerPetListByIdList(anyList())).thenReturn(List.of(pet11, pet12));

        // When
        List<CustomerGroomingAppointmentDTO> result =
                moeAppointmentQueryService.filterPassAwayPetAppointment(customerAppointmentDTOS);

        // Then
        assertThat(result.size()).isEqualTo(1);
    }

    @DisplayName("Should return appointments when cycle days match")
    @Test
    void filterSpecificSupportRebookReminderSendListByCustomer_WhenCycleDaysMatch_ReturnAppointments() {
        // Given
        String businessCurDate = "2022-11-27";
        CustomerGroomingAppointmentDTO lastServiceAppt = new CustomerGroomingAppointmentDTO();
        lastServiceAppt.setAppointmentDate("2022-11-20");
        lastServiceAppt.setCustomerId(1);
        List<CustomerGroomingAppointmentDTO> lastServiceAppts = List.of(lastServiceAppt);

        MoeBusinessCustomerDTO customerDTO = new MoeBusinessCustomerDTO();
        customerDTO.setCustomerId(1);
        Map<Integer, MoeBusinessCustomerDTO> customerDTOMap = Map.of(customerDTO.getCustomerId(), customerDTO);
        Integer cycleDays = 7;

        // When
        List<CustomerGroomingAppointmentDTO> result =
                MoeAppointmentQueryService.filterSpecificSupportRebookReminderSendListByCustomer(
                        businessCurDate, lastServiceAppts, customerDTOMap, cycleDays);

        // Then
        assertThat(result.size()).isEqualTo(1);
    }

    @DisplayName("Should return empty list when cycle days do not match")
    @Test
    void filterSpecificSupportRebookReminderSendListByCustomer_WhenCycleDaysDoNotMatch_ReturnEmptyList() {
        // Given
        CustomerGroomingAppointmentDTO lastServiceAppt = new CustomerGroomingAppointmentDTO();
        lastServiceAppt.setAppointmentDate("2022-11-20");
        lastServiceAppt.setCustomerId(1);
        List<CustomerGroomingAppointmentDTO> lastServiceAppts = List.of(lastServiceAppt);

        MoeBusinessCustomerDTO customerDTO = new MoeBusinessCustomerDTO();
        customerDTO.setCustomerId(1);
        Map<Integer, MoeBusinessCustomerDTO> customerDTOMap = Map.of(customerDTO.getCustomerId(), customerDTO);

        // When
        List<CustomerGroomingAppointmentDTO> result =
                MoeAppointmentQueryService.filterSpecificSupportRebookReminderSendListByCustomer(
                        "2022-11-27", lastServiceAppts, customerDTOMap, 8);

        // Then
        assertThat(result).isEmpty();

        // When
        result = MoeAppointmentQueryService.filterSpecificSupportRebookReminderSendListByCustomer(
                "2022-11-20", lastServiceAppts, customerDTOMap, 8);

        // Then
        assertThat(result).isEmpty();
    }

    @DisplayName("Should return appointments when date is within the expected service date range")
    @Test
    void filterRebookReminderPageByCustomer_WhenDateIsWithinExpectedServiceDateRange_ReturnAppointments() {
        // Given
        MoeBusinessCustomerDTO customerDTO1 = new MoeBusinessCustomerDTO();
        customerDTO1.setCustomerId(1);
        customerDTO1.setInactive(BooleanEnum.INACTIVE_FALSE);
        customerDTO1.setPreferredFrequencyDay(30);

        MoeBusinessCustomerDTO customerDTO2 = new MoeBusinessCustomerDTO();
        customerDTO2.setCustomerId(2);
        customerDTO2.setInactive(BooleanEnum.INACTIVE_FALSE);
        customerDTO2.setPreferredFrequencyDay(40);

        Map<Integer, MoeBusinessCustomerDTO> customerDTOMap =
                Map.of(customerDTO1.getCustomerId(), customerDTO1, customerDTO2.getCustomerId(), customerDTO2);

        CustomerGroomingAppointmentDTO lastServiceAppt1 = new CustomerGroomingAppointmentDTO();
        lastServiceAppt1.setAppointmentDate("2022-11-03");
        lastServiceAppt1.setCustomerId(1);

        CustomerGroomingAppointmentDTO lastServiceAppt2 = new CustomerGroomingAppointmentDTO();
        lastServiceAppt2.setAppointmentDate("2022-11-03");
        lastServiceAppt2.setCustomerId(2);

        List<CustomerGroomingAppointmentDTO> lastServiceAppts = List.of(lastServiceAppt1, lastServiceAppt2);

        String businessCurDate = "2022-11-12";
        Integer isBefore = 1;

        // When
        List<CustomerGroomingAppointmentDTO> result = MoeAppointmentQueryService.filterRebookReminderPageByCustomer(
                businessCurDate, lastServiceAppts, customerDTOMap, isBefore);

        // Then
        assertThat(result.size()).isEqualTo(2);
    }

    @DisplayName("Should return empty list when date is not within the expected service date range")
    @Test
    void filterRebookReminderPageByCustomer_WhenDateIsNotWithinExpectedServiceDateRange_ReturnEmptyList() {
        // Given
        MoeBusinessCustomerDTO customerDTO1 = new MoeBusinessCustomerDTO();
        customerDTO1.setCustomerId(1);
        customerDTO1.setInactive(BooleanEnum.INACTIVE_FALSE);
        customerDTO1.setPreferredFrequencyDay(30);

        MoeBusinessCustomerDTO customerDTO2 = new MoeBusinessCustomerDTO();
        customerDTO2.setCustomerId(2);
        customerDTO2.setInactive(BooleanEnum.INACTIVE_FALSE);
        customerDTO2.setPreferredFrequencyDay(40);

        Map<Integer, MoeBusinessCustomerDTO> customerDTOMap =
                Map.of(customerDTO1.getCustomerId(), customerDTO1, customerDTO2.getCustomerId(), customerDTO2);

        CustomerGroomingAppointmentDTO lastServiceAppt1 = new CustomerGroomingAppointmentDTO();
        lastServiceAppt1.setAppointmentDate("2022-11-13");
        lastServiceAppt1.setCustomerId(1);

        CustomerGroomingAppointmentDTO lastServiceAppt2 = new CustomerGroomingAppointmentDTO();
        lastServiceAppt2.setAppointmentDate("2022-11-13");
        lastServiceAppt2.setCustomerId(2);

        List<CustomerGroomingAppointmentDTO> lastServiceAppts = List.of(lastServiceAppt1, lastServiceAppt2);

        String businessCurDate = "2022-11-12";
        Integer isBefore = 0;

        // When
        List<CustomerGroomingAppointmentDTO> result = MoeAppointmentQueryService.filterRebookReminderPageByCustomer(
                businessCurDate, lastServiceAppts, customerDTOMap, isBefore);

        // Then
        assertThat(result).isEmpty();
    }

    @DisplayName("Should return appointments when date is within the expected service date range")
    @Test
    void filterRebookReminderSendListByCustomer_WhenDateIsWithinExpectedServiceDateRange_ReturnAppointments() {
        // Given
        MoeBusinessCustomerDTO customerDTO1 = new MoeBusinessCustomerDTO();
        customerDTO1.setCustomerId(1);
        customerDTO1.setInactive(BooleanEnum.INACTIVE_FALSE);
        customerDTO1.setPreferredFrequencyDay(30);

        Map<Integer, MoeBusinessCustomerDTO> customerDTOMap = Map.of(customerDTO1.getCustomerId(), customerDTO1);

        CustomerGroomingAppointmentDTO lastServiceAppt1 = new CustomerGroomingAppointmentDTO();
        lastServiceAppt1.setAppointmentDate("2022-11-03");
        lastServiceAppt1.setCustomerId(1);

        List<CustomerGroomingAppointmentDTO> lastServiceAppts = List.of(lastServiceAppt1);

        String businessCurDate = "2022-12-02";
        Integer isBefore = 1;

        // When
        List<CustomerGroomingAppointmentDTO> result = MoeAppointmentQueryService.filterRebookReminderSendListByCustomer(
                businessCurDate, lastServiceAppts, customerDTOMap, isBefore);

        // Then
        assertEquals(1, result.size());
    }

    @DisplayName("Should return empty list when date is not within the expected service date range")
    @Test
    void filterRebookReminderSendListByCustomer_WhenDateIsNotWithinExpectedServiceDateRange_ReturnEmptyList() {
        // Given
        MoeBusinessCustomerDTO customerDTO1 = new MoeBusinessCustomerDTO();
        customerDTO1.setCustomerId(1);
        customerDTO1.setInactive(BooleanEnum.INACTIVE_FALSE);
        customerDTO1.setPreferredFrequencyDay(30);

        Map<Integer, MoeBusinessCustomerDTO> customerDTOMap = Map.of(customerDTO1.getCustomerId(), customerDTO1);

        CustomerGroomingAppointmentDTO lastServiceAppt1 = new CustomerGroomingAppointmentDTO();
        lastServiceAppt1.setAppointmentDate("2022-11-03");
        lastServiceAppt1.setCustomerId(1);

        List<CustomerGroomingAppointmentDTO> lastServiceAppts = List.of(lastServiceAppt1);

        String businessCurDate = "2022-12-02";
        Integer isBefore = 2;

        // When
        List<CustomerGroomingAppointmentDTO> result = MoeAppointmentQueryService.filterRebookReminderSendListByCustomer(
                businessCurDate, lastServiceAppts, customerDTOMap, isBefore);

        // Then
        assertThat(result).isEmpty();
    }
}
