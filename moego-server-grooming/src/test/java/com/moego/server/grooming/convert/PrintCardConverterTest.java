package com.moego.server.grooming.convert;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.agreement.v1.AgreementWithRecentRecordsView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.customer.dto.CustomerPetPetCodeDTO;
import com.moego.server.customer.dto.MoePetCodeInfoDTO;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.printcard.PrintCardAppointment;
import com.moego.server.grooming.dto.printcard.PrintCardCustomer;
import com.moego.server.grooming.dto.printcard.PrintCardHistory;
import com.moego.server.grooming.dto.printcard.PrintCardOperation;
import com.moego.server.grooming.dto.printcard.PrintCardPet;
import com.moego.server.grooming.dto.printcard.PrintCardServiceInfo;
import com.moego.server.grooming.dto.printcard.PrintPetCodeBinding;
import com.moego.server.grooming.dto.printcard.StayCardAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import java.math.BigDecimal;
import java.util.AbstractMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class PrintCardConverterTest {

    @Test
    void toPrintCardPet_withValidData() {
        CustomerPetPetCodeDTO pet = new CustomerPetPetCodeDTO();
        pet.setPetId(1);
        pet.setPetName("Buddy");
        pet.setPetTypeId(1);
        pet.setBreed("Golden Retriever");
        pet.setGender(1);
        pet.setAvatarPath("/images/buddy.png");
        pet.setWeight("30.5");
        pet.setVetName("Dr. Smith");
        pet.setVetPhone("************");
        pet.setHealthIssues("None");
        pet.setPetAppearanceColor("color");
        pet.setPetAppearanceNotes("notes");
        pet.setFixed("Spayed");
        pet.setBirthday("2025-04-17");

        MoePetCodeInfoDTO petCodeInfo1 = new MoePetCodeInfoDTO();
        petCodeInfo1.setDescription("Friendly");
        MoePetCodeInfoDTO petCodeInfo2 = new MoePetCodeInfoDTO();
        petCodeInfo2.setDescription("Kindly");
        pet.setMoePetCodeInfos(List.of(petCodeInfo1, petCodeInfo2));

        VaccineBindingRecordDto vaccineRecord1 = new VaccineBindingRecordDto();
        vaccineRecord1.setVaccineName("Rabies1");
        vaccineRecord1.setExpirationDate("2024-12-31");
        VaccineBindingRecordDto vaccineRecord2 = new VaccineBindingRecordDto();
        vaccineRecord2.setVaccineName("Rabies2");
        vaccineRecord2.setExpirationDate("2025-12-31");
        pet.setVaccineList(List.of(vaccineRecord1, vaccineRecord2));

        Map<Long, List<String>> petNotes = Map.of(1L, List.of("Note 1", "Note 2"));
        List<PrintPetCodeBinding> petCodeBindings = List.of(
                PrintPetCodeBinding.builder()
                        .description("Leash required")
                        .color("#FFFFFF")
                        .abbreviation("Lea")
                        .uniqueComment("test comment1")
                        .build(),
                PrintPetCodeBinding.builder()
                        .description("Energetic")
                        .color("#BBBBBB")
                        .abbreviation("Ene")
                        .uniqueComment("test comment2")
                        .build());

        var result = PrintCardConverter.INSTANCE.toPrintCardPet(pet, petNotes, petCodeBindings);
        var expect = new PrintCardPet(
                "Buddy",
                1,
                "Golden Retriever",
                "Male",
                "/images/buddy.png",
                "30.5",
                List.of("Note 1", "Note 2"),
                List.of("Friendly", "Kindly"),
                Map.of("Rabies2", "2025-12-31", "Rabies1", "2024-12-31"),
                "Dr. Smith",
                "************",
                "None",
                "color",
                "notes",
                "Spayed",
                "2025-04-17",
                List.of(
                        PrintPetCodeBinding.builder()
                                .description("Leash required")
                                .color("#FFFFFF")
                                .abbreviation("Lea")
                                .uniqueComment("test comment1")
                                .build(),
                        PrintPetCodeBinding.builder()
                                .description("Energetic")
                                .color("#BBBBBB")
                                .abbreviation("Ene")
                                .uniqueComment("test comment2")
                                .build()));
        // 忽略顺序比较以及对象引用比较
        assertThat(result).usingRecursiveComparison().ignoringCollectionOrder().isEqualTo(expect);
    }

    @Test
    void toPrintCardCustomer_withValidData() {
        var customer = BusinessCustomerInfoModel.newBuilder()
                .setId(1L)
                .setFirstName("John")
                .setLastName("Doe")
                .setPhoneNumber("************")
                .setEmail("<EMAIL>")
                .addAllTagIds(List.of(1L, 2L))
                .build();

        var tagMap = Map.of(1L, "VIP", 2L, "Regular");
        var customerNotes = Map.of(1L, List.of("Note 1", "Note 2"));
        var agreement1 = AgreementWithRecentRecordsView.newBuilder()
                .setAgreementTitle("Agreement 1")
                .setRecentSignedTime(1625097600000L)
                .build();
        var agreement2 = AgreementWithRecentRecordsView.newBuilder()
                .setAgreementTitle("Agreement 2")
                .setRecentSignedTime(1625184000000L)
                .build();
        var customerAgreements = Map.of(1L, List.of(agreement1, agreement2));

        var result = PrintCardConverter.INSTANCE.toPrintCardCustomer(
                customer, tagMap, customerNotes, customerAgreements, "UTC");
        var expect = new PrintCardCustomer(
                "John Doe",
                "Doe",
                "************",
                "<EMAIL>",
                List.of("VIP", "Regular"),
                List.of("Note 1", "Note 2"),
                Map.of("Agreement 2", "2021-07-02", "Agreement 1", "2021-07-01"));
        assertThat(result).usingRecursiveComparison().ignoringCollectionOrder().isEqualTo(expect);
    }

    @Test
    void toPrintCardAppointment_withValidData() {
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setAppointmentDate("2023-10-01");
        appointment.setAppointmentStartTime(600);

        MoeGroomingNote commentNote = new MoeGroomingNote();
        commentNote.setType(GroomingAppointmentEnum.NOTE_COMMENT);
        commentNote.setNote("This is a comment");

        MoeGroomingNote alertNote = new MoeGroomingNote();
        alertNote.setType(GroomingAppointmentEnum.NOTE_ALERT);
        alertNote.setNote("This is an alert");

        Map<Integer, List<MoeGroomingNote>> appointmentNotes =
                Map.of(appointment.getId(), List.of(commentNote, alertNote));

        var result = PrintCardConverter.INSTANCE.toPrintCardAppointment(appointment, appointmentNotes);
        var expect = new PrintCardAppointment("2023-10-01", 600, "This is a comment", "This is an alert");
        assertThat(result).usingRecursiveComparison().ignoringCollectionOrder().isEqualTo(expect);
    }

    @Test
    void toPrintCardAppointment_withNoNotes() {
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setAppointmentDate("2023-10-01");
        appointment.setAppointmentStartTime(600);

        Map<Integer, List<MoeGroomingNote>> appointmentNotes = Map.of();

        PrintCardAppointment result = PrintCardConverter.INSTANCE.toPrintCardAppointment(appointment, appointmentNotes);
        var expect = new PrintCardAppointment("2023-10-01", 600, "", "");
        assertThat(result).usingRecursiveComparison().ignoringCollectionOrder().isEqualTo(expect);
    }

    @Test
    void toPrintCardServiceInfo_withValidData() {
        GroomingPetDetailDTO petDetail1 = new GroomingPetDetailDTO();
        petDetail1.setId(1);
        petDetail1.setLodgingId(0L);
        petDetail1.setServiceName("Grooming");
        petDetail1.setStartTime(600L);
        petDetail1.setServiceTime(120);
        petDetail1.setServicePrice(new BigDecimal("50.00"));
        petDetail1.setStaffId(1);
        petDetail1.setEnableOperation(true);
        petDetail1.setWorkMode(1);
        petDetail1.setServiceItemType(1);
        petDetail1.setServiceType(1);
        petDetail1.setPriceUnit(1);

        GroomingServiceOperationDTO operation11 = new GroomingServiceOperationDTO();
        operation11.setGroomingServiceId(1);
        operation11.setStaffId(1);
        operation11.setStartTime(600);
        operation11.setDuration(60);
        operation11.setPrice(new BigDecimal("25.00"));
        operation11.setOperationName("Grooming-operation-1");
        GroomingServiceOperationDTO operation12 = new GroomingServiceOperationDTO();
        operation12.setGroomingServiceId(1);
        operation12.setStaffId(2);
        operation12.setStartTime(660);
        operation12.setDuration(60);
        operation12.setPrice(new BigDecimal("25.00"));
        operation12.setOperationName("Grooming-operation-2");

        Map<Integer, List<GroomingServiceOperationDTO>> serviceOperationMap =
                Map.of(1, List.of(operation11, operation12));

        MoeStaffDto staff1 = new MoeStaffDto();
        staff1.setId(1);
        staff1.setFirstName("John");
        staff1.setLastName("Doe");

        MoeStaffDto staff2 = new MoeStaffDto();
        staff2.setId(2);
        staff2.setFirstName("Jane");
        staff2.setLastName("Smith");

        Map<Integer, MoeStaffDto> staffMap = Map.of(1, staff1, 2, staff2);

        GroomingPetDetailDTO petDetail2 = new GroomingPetDetailDTO();
        petDetail2.setId(2);
        petDetail2.setLodgingId(2L);
        petDetail2.setServiceName("Daycare");
        petDetail2.setStartTime(720L);
        petDetail2.setServiceTime(180);
        petDetail2.setServicePrice(new BigDecimal("30.00"));
        petDetail2.setStaffId(0);
        petDetail2.setEnableOperation(false);
        petDetail2.setServiceItemType(3);
        petDetail2.setServiceType(1);
        petDetail2.setPriceUnit(1);

        Map<Long, LodgingUnitModel> lodgingUnitMap = Map.of(
                1L,
                LodgingUnitModel.newBuilder()
                        .setId(1L)
                        .setLodgingTypeId(1L)
                        .setName("Unit 1")
                        .build(),
                2L,
                LodgingUnitModel.newBuilder()
                        .setId(2L)
                        .setLodgingTypeId(2L)
                        .setName("Unit 2")
                        .build());
        Map<Long, LodgingTypeModel> lodgingTypeMap = Map.of(
                1L,
                LodgingTypeModel.newBuilder().setId(1L).setName("Type 1").build(),
                2L,
                LodgingTypeModel.newBuilder().setId(2L).setName("Type 2").build());
        AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>> lodgingMap =
                new AbstractMap.SimpleEntry<>(lodgingUnitMap, lodgingTypeMap);

        List<PrintCardServiceInfo> result = PrintCardConverter.INSTANCE.toPrintCardServiceInfo(
                List.of(petDetail1, petDetail2), staffMap, serviceOperationMap, lodgingMap, List.of());
        var expect = List.of(
                new PrintCardServiceInfo(
                        "Grooming",
                        600,
                        120,
                        "50.00",
                        1,
                        "John Doe",
                        true,
                        List.of(
                                new PrintCardOperation(1, "John Doe", 600, 60, "25.00", "Grooming-operation-1"),
                                new PrintCardOperation(2, "Jane Smith", 660, 60, "25.00", "Grooming-operation-2")),
                        ServiceItemEnum.GROOMING,
                        ServiceItemType.GROOMING,
                        "Unit 2",
                        1,
                        1),
                new PrintCardServiceInfo(
                        "Daycare",
                        720,
                        180,
                        "30.00",
                        0,
                        "",
                        false,
                        List.of(),
                        ServiceItemEnum.DAYCARE,
                        ServiceItemType.DAYCARE,
                        "Unit 2",
                        1,
                        1));
        assertThat(result).usingRecursiveComparison().ignoringCollectionOrder().isEqualTo(expect);
    }

    @Test
    void toStayCardAppointment_withValidData() {
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setAppointmentDate("2023-10-01");
        appointment.setAppointmentEndDate("2023-10-02");
        appointment.setAppointmentStartTime(600);
        appointment.setAppointmentEndTime(1080);
        appointment.setServiceTypeInclude(ServiceItemEnum.BOARDING.getBitValue());

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setLodgingId(1L);
        petDetail.setServiceId(1);
        petDetail.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail.setServiceItemType(ServiceItemEnum.BOARDING.getBitValue());

        MoeGroomingNote commentNote = new MoeGroomingNote();
        commentNote.setType(GroomingAppointmentEnum.NOTE_COMMENT);
        commentNote.setNote("This is a comment");

        MoeGroomingNote alertNote = new MoeGroomingNote();
        alertNote.setType(GroomingAppointmentEnum.NOTE_ALERT);
        alertNote.setNote("This is an alert");

        Map<Integer, List<MoeGroomingNote>> appointmentNotes =
                Map.of(appointment.getId(), List.of(commentNote, alertNote));

        Map<Long, LodgingUnitModel> lodgingUnitMap = Map.of(
                1L,
                LodgingUnitModel.newBuilder()
                        .setId(1L)
                        .setLodgingTypeId(1L)
                        .setName("Unit 1")
                        .build());
        Map<Long, LodgingTypeModel> lodgingTypeMap = Map.of(
                1L, LodgingTypeModel.newBuilder().setId(1L).setName("Type 1").build());
        AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>> lodgingMap =
                new AbstractMap.SimpleEntry<>(lodgingUnitMap, lodgingTypeMap);

        Map<Integer, String> serviceNameMap = Map.of(1, "Standard boarding");

        var result = PrintCardConverter.INSTANCE.toStayCardAppointment(
                appointment, List.of(petDetail), lodgingMap, appointmentNotes, List.of(), serviceNameMap);
        var expect = new StayCardAppointment(
                "2023-10-01",
                "2023-10-02",
                600,
                1080,
                "Type 1",
                "Unit 1",
                "This is a comment",
                "This is an alert",
                "Standard boarding",
                ServiceItemEnum.BOARDING,
                List.of());
        assertThat(result).usingRecursiveComparison().ignoringCollectionOrder().isEqualTo(expect);
    }

    @Test
    void toStayCardAppointment_withNoNotes() {
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setAppointmentDate("2023-10-01");
        appointment.setAppointmentEndDate("2023-10-01");
        appointment.setAppointmentStartTime(600);
        appointment.setAppointmentEndTime(1080);
        appointment.setServiceTypeInclude(ServiceItemEnum.DAYCARE.getBitValue());

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setLodgingId(0L);
        petDetail.setServiceItemType(ServiceItemEnum.DAYCARE.getBitValue());

        Map<Integer, List<MoeGroomingNote>> appointmentNotes = Map.of();

        Map<Long, LodgingUnitModel> lodgingUnitMap = Map.of();
        Map<Long, LodgingTypeModel> lodgingTypeMap = Map.of();
        AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>> lodgingMap =
                new AbstractMap.SimpleEntry<>(lodgingUnitMap, lodgingTypeMap);

        Map<Integer, String> serviceNameMap = Map.of();

        StayCardAppointment result = PrintCardConverter.INSTANCE.toStayCardAppointment(
                appointment, List.of(petDetail), lodgingMap, appointmentNotes, List.of(), serviceNameMap);
        var expect = new StayCardAppointment(
                "2023-10-01", "2023-10-01", 600, 1080, "", "", "", "", "", ServiceItemEnum.DAYCARE, List.of());
        assertThat(result).usingRecursiveComparison().ignoringCollectionOrder().isEqualTo(expect);
    }

    @Test
    void toPrintCardHistory_withValidData() {
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setAppointmentDate("2023-10-01");
        appointment.setAppointmentStartTime(600);

        GroomingPetDetailDTO petDetail1 = new GroomingPetDetailDTO();
        petDetail1.setPetId(1);
        petDetail1.setServiceName("Grooming1");
        petDetail1.setId(1);
        petDetail1.setStaffId(1);
        GroomingPetDetailDTO petDetail2 = new GroomingPetDetailDTO();
        petDetail2.setPetId(1);
        petDetail2.setServiceName("Grooming2");
        petDetail2.setId(2);
        petDetail2.setStaffId(2);

        MoeStaffDto staff1 = new MoeStaffDto();
        staff1.setId(1);
        staff1.setFirstName("John");
        staff1.setLastName("Doe");

        MoeStaffDto staff2 = new MoeStaffDto();
        staff2.setId(2);
        staff2.setFirstName("Jane");
        staff2.setLastName("Smith");

        MoeGroomingNote commentNote = new MoeGroomingNote();
        commentNote.setType(GroomingAppointmentEnum.NOTE_COMMENT);
        commentNote.setNote("This is a comment");

        Map<Integer, List<MoeGroomingNote>> appointmentComments = Map.of(1, List.of(commentNote));
        Map<Integer, List<GroomingPetDetailDTO>> appointmentPetDetails = Map.of(1, List.of(petDetail1, petDetail2));
        Map<Integer, List<GroomingServiceOperationDTO>> serviceOperations = Map.of();
        Map<Integer, MoeStaffDto> staffMap = Map.of(1, staff1, 2, staff2);

        var result = PrintCardConverter.INSTANCE.toPrintCardHistory(
                1, appointment, appointmentComments, appointmentPetDetails, serviceOperations, staffMap);
        var expect = new PrintCardHistory(
                "2023-10-01",
                600,
                List.of("Grooming1", "Grooming2"),
                List.of("John Doe", "Jane Smith"),
                "This is a comment");
        assertThat(result).usingRecursiveComparison().ignoringCollectionOrder().isEqualTo(expect);
    }

    @Test
    void toPrintCardHistory_withNoPetDetails() {
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setId(1);

        Map<Integer, List<MoeGroomingNote>> appointmentComments = Map.of();
        Map<Integer, List<GroomingPetDetailDTO>> appointmentPetDetails = Map.of();
        Map<Integer, List<GroomingServiceOperationDTO>> serviceOperations = Map.of();
        Map<Integer, MoeStaffDto> staffMap = Map.of();

        PrintCardHistory result = PrintCardConverter.INSTANCE.toPrintCardHistory(
                1, appointment, appointmentComments, appointmentPetDetails, serviceOperations, staffMap);

        assertThat(result).isNull();
    }
}
