package com.moego.server.grooming.service;

import com.moego.common.utils.GsonUtil;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.grooming.service.dto.OBAvailableTimeStaffAvailableTimeDto;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;

public class StaffAvailableTimeTest {

    MoeGroomingBookOnlineService obService = new MoeGroomingBookOnlineService();

    @Test
    public void testTimeRangeInterval() {
        System.out.println("从availableTime  生成 满足条件的时间点数组");
        List<TimeRangeDto> origin = new ArrayList<>();
        TimeRangeDto dto = new TimeRangeDto();
        dto.setStartTime(100);
        dto.setEndTime(200);
        origin.add(dto);

        TimeRangeDto dto2 = new TimeRangeDto();
        dto2.setStartTime(300);
        dto2.setEndTime(333);
        origin.add(dto2);

        int interval = 30;
        int totalDuration = 60;
        OBAvailableTimeStaffAvailableTimeDto timeList = new OBAvailableTimeStaffAvailableTimeDto();
        timeList.setAm(new ArrayList<>());
        timeList.setPm(new ArrayList<>());
        obService.generateAvailableTimePoint(origin, interval, totalDuration, timeList);
        System.out.println(GsonUtil.toJson(timeList, true)); // am: 100, 130 pm : null
    }
}
