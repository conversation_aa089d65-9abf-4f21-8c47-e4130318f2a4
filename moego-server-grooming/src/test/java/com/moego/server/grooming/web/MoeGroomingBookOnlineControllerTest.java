package com.moego.server.grooming.web;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.AcceptCustomerType;
import com.moego.idl.service.online_booking.v1.ListAcceptedCustomerSettingResponse;
import com.moego.idl.service.online_booking.v1.OBAvailabilitySettingServiceGrpc;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * {@link MoeGroomingBookOnlineController}
 */
@ExtendWith(MockitoExtension.class)
class MoeGroomingBookOnlineControllerTest {

    @Mock
    OBAvailabilitySettingServiceGrpc.OBAvailabilitySettingServiceBlockingStub obAvailabilitySettingService;

    @InjectMocks
    MoeGroomingBookOnlineController controller;

    /**
     * {@link MoeGroomingBookOnlineController#getAcceptCustomerTypes(long, long)}
     */
    @Test
    void testGetAcceptCustomerTypes() {
        // Mock
        when(obAvailabilitySettingService.listAcceptedCustomerSetting(any()))
                .thenReturn(ListAcceptedCustomerSettingResponse.newBuilder()
                        .addAcceptCustomerTypes(ListAcceptedCustomerSettingResponse.AcceptCustomerType.newBuilder()
                                .setServiceItemType(ServiceItemType.GROOMING)
                                .setAcceptCustomerType(AcceptCustomerType.NEW_CUSTOMER) // 1
                                .build())
                        .addAcceptCustomerTypes(ListAcceptedCustomerSettingResponse.AcceptCustomerType.newBuilder()
                                .setServiceItemType(ServiceItemType.BOARDING)
                                .setAcceptCustomerType(AcceptCustomerType.EXISTING_CUSTOMER) // 2
                                .build())
                        .addAcceptCustomerTypes(ListAcceptedCustomerSettingResponse.AcceptCustomerType.newBuilder()
                                .setServiceItemType(ServiceItemType.DAYCARE)
                                .setAcceptCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER) // 3
                                .build())
                        .build());

        // Act
        var result = controller.getAcceptCustomerTypes(1L, 1L);

        // Assert
        assertThat(result.getGrooming()).isEqualTo(AcceptCustomerType.NEW_CUSTOMER_VALUE); // 1
        assertThat(result.getBoarding()).isEqualTo(AcceptCustomerType.EXISTING_CUSTOMER_VALUE); // 2
        assertThat(result.getDaycare()).isEqualTo(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE); // 3
    }
}
