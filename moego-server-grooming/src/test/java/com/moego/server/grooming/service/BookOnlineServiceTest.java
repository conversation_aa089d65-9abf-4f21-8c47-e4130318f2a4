package com.moego.server.grooming.service;

import com.google.protobuf.Duration;
import com.google.rpc.Code;
import com.google.rpc.Status;
import com.moego.common.constant.Dictionary;
import com.moego.idl.models.map.v1.RouteMatrixElement;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 * @since 2022/8/5
 */
@ExtendWith(MockitoExtension.class)
public class BookOnlineServiceTest {

    @InjectMocks
    private MoeGroomingBookOnlineService bookOnlineService;

    @Mock
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Mock
    private SmartScheduleService smartScheduleService;

    // 深圳北 - 深圳西 26.5km 32min
    private final String startLat = "22.527816";
    private final String startLng = "113.907297";
    private final String endLat = "22.560951";
    private final String endLng = "114.116892";

    private RouteMatrixElement matrixElement;

    @BeforeEach
    public void initialize() {
        matrixElement = RouteMatrixElement.newBuilder()
                .setStatus(Status.newBuilder()
                        .setCode(Code.OK.getNumber())
                        .setMessage("OK")
                        .build())
                .setDistance(26500)
                .setDuration(Duration.newBuilder().setSeconds(32 * 60).build())
                .build();
    }

    @Test
    public void checkZipcodeByLatLngAvailableTime() {
        Integer businessId = 10000;
        MoeBusinessBookOnline bookOnline = new MoeBusinessBookOnline();
        bookOnline.setBusinessId(10000);
        // 10 km
        bookOnline.setMaxAvailableDist(10);
        bookOnline.setMaxAvailableTime(40);
        bookOnline.setSettingLat(endLat);
        bookOnline.setSettingLng(endLng);

        MoeBusinessDto businessDto = new MoeBusinessDto();
        businessDto.setUnitOfDistanceType(Dictionary.UNITED_DISTANCE_TYPE_2.byteValue());
        Mockito.doReturn(businessDto)
                .when(iBusinessBusinessClient)
                .getBusinessInfo(InfoIdParams.builder().infoId(businessId).build());
        Mockito.doReturn(matrixElement)
                .when(smartScheduleService)
                .callGoogleAPI(startLat, startLng, bookOnline.getSettingLat(), bookOnline.getSettingLng());

        Boolean result = bookOnlineService.checkZipcodeByLatLng(startLat, startLng, bookOnline);
        Assertions.assertThat(result).as("It's in the max time").isTrue();
    }

    @Test
    public void checkZipcodeByLatLngAvailableDistance() {
        Integer businessId = 10000;
        MoeBusinessBookOnline bookOnline = new MoeBusinessBookOnline();
        bookOnline.setBusinessId(10000);
        // 30 mile
        bookOnline.setMaxAvailableDist(17);
        bookOnline.setMaxAvailableTime(30);
        bookOnline.setSettingLat(endLat);
        bookOnline.setSettingLng(endLng);

        MoeBusinessDto businessDto = new MoeBusinessDto();
        businessDto.setUnitOfDistanceType(Dictionary.UNITED_DISTANCE_TYPE_1.byteValue());
        Mockito.doReturn(businessDto)
                .when(iBusinessBusinessClient)
                .getBusinessInfo(InfoIdParams.builder().infoId(businessId).build());
        Mockito.doReturn(matrixElement)
                .when(smartScheduleService)
                .callGoogleAPI(startLat, startLng, bookOnline.getSettingLat(), bookOnline.getSettingLng());

        Boolean result = bookOnlineService.checkZipcodeByLatLng(startLat, startLng, bookOnline);
        Assertions.assertThat(result).as("It's in the max distance").isTrue();
    }

    @Test
    public void checkZipcodeByLatLngNotAvailable() {
        Integer businessId = 10000;
        MoeBusinessBookOnline bookOnline = new MoeBusinessBookOnline();
        bookOnline.setBusinessId(10000);
        // 10 mile
        bookOnline.setMaxAvailableDist(25);
        bookOnline.setMaxAvailableTime(30);
        bookOnline.setSettingLat(endLat);
        bookOnline.setSettingLng(endLng);

        MoeBusinessDto businessDto = new MoeBusinessDto();
        businessDto.setUnitOfDistanceType(Dictionary.UNITED_DISTANCE_TYPE_2.byteValue());
        Mockito.doReturn(businessDto)
                .when(iBusinessBusinessClient)
                .getBusinessInfo(InfoIdParams.builder().infoId(businessId).build());
        Mockito.doReturn(matrixElement)
                .when(smartScheduleService)
                .callGoogleAPI(startLat, startLng, bookOnline.getSettingLat(), bookOnline.getSettingLng());

        Boolean result = bookOnlineService.checkZipcodeByLatLng(startLat, startLng, bookOnline);
        Assertions.assertThat(result)
                .as("It's out the max distance and out the max time")
                .isFalse();
    }
}
