package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

import com.moego.common.enums.ConflictTypeEnum;
import com.moego.common.response.ResponseResult;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.StaffWorkingRangeDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.grooming.dto.RepeatPreviewInfoDTO;
import com.moego.server.grooming.dto.StaffConflictDTO;
import com.moego.server.grooming.dto.StaffConflictInfoDTO;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.params.PreviewRepeatParams;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MoeAppointmentConflictCheckServiceTest {

    @Mock
    private PetDetailMapperProxy moeGroomingPetDetailMapper;

    @Mock
    private IBusinessStaffClient iBusinessStaffClient;

    @InjectMocks
    private MoeAppointmentConflictCheckService conflictCheckService;

    private PreviewRepeatParams previewParams;
    private List<RepeatPreviewInfoDTO> previewInfoList;
    private StaffWorkingRangeDto staffWorkingRange;

    @BeforeEach
    void setUp() {
        // 设置基本的预览参数
        previewParams = new PreviewRepeatParams();
        previewParams.setBusinessId(1);
        previewParams.setExistAppointmentId(null);
        previewParams.setRepeatId(null);

        // 创建预览信息列表
        previewInfoList = new ArrayList<>();
        RepeatPreviewInfoDTO previewInfo = new RepeatPreviewInfoDTO();
        previewInfo.setDate("2024-01-01");
        previewInfo.setStartTime(900); // 9:00
        previewInfo.setDuration(60); // 1小时
        previewInfo.setStaffIdList(List.of(1, 2));
        previewInfo.setIsNotConflict(true);

        List<StaffConflictInfoDTO> staffConflicts = new ArrayList<>();
        staffConflicts.add(createStaffConflictInfo(1, 900, 60));
        staffConflicts.add(createStaffConflictInfo(2, 900, 60));
        previewInfo.setStaffConflictInfoList(staffConflicts);

        previewInfoList.add(previewInfo);

        // 创建员工工作时间范围
        staffWorkingRange = new StaffWorkingRangeDto();
        staffWorkingRange.setStaffId(1);
        Map<String, List<TimeRangeDto>> timeRange = new HashMap<>();
        timeRange.put("2024-01-01", List.of(new TimeRangeDto(800, 1700))); // 8:00-17:00
        staffWorkingRange.setTimeRange(timeRange);
    }

    @Test
    void checkConflictForRepeatV2_WhenNoConflict_ShouldReturnTrue() {
        // Arrange
        // 为所有 staff 设置工作时间
        StaffWorkingRangeDto staff1WorkingRange = new StaffWorkingRangeDto();
        staff1WorkingRange.setStaffId(1);
        Map<String, List<TimeRangeDto>> timeRange1 = new HashMap<>();
        timeRange1.put("2024-01-01", List.of(new TimeRangeDto(800, 1700))); // 8:00-17:00
        staff1WorkingRange.setTimeRange(timeRange1);

        StaffWorkingRangeDto staff2WorkingRange = new StaffWorkingRangeDto();
        staff2WorkingRange.setStaffId(2);
        Map<String, List<TimeRangeDto>> timeRange2 = new HashMap<>();
        timeRange2.put("2024-01-01", List.of(new TimeRangeDto(800, 1700))); // 8:00-17:00
        staff2WorkingRange.setTimeRange(timeRange2);

        ResponseResult<List<StaffWorkingRangeDto>> response =
                ResponseResult.success(List.of(staff1WorkingRange, staff2WorkingRange));
        when(iBusinessStaffClient.queryRange(anyInt(), any(), any())).thenReturn(response);

        when(moeGroomingPetDetailMapper.queryPetDetailByAppointmentDatesAndStaffIds(
                        anyInt(), anyList(), anyList(), anyList(), any()))
                .thenReturn(List.of());

        // Act
        conflictCheckService.checkConflictForRepeatV2(previewInfoList, previewParams);

        // Assert
        assertThat(previewInfoList.get(0).getIsNotConflict()).isTrue();
        assertThat(previewInfoList.get(0).getStaffConflictInfoList()).allMatch(StaffConflictInfoDTO::getIsNotConflict);
    }

    @Test
    void checkConflictForRepeatV2_WhenOutsideWorkingHours_ShouldReturnFalse() {
        // Arrange
        StaffWorkingRangeDto laterWorkingRange = new StaffWorkingRangeDto();
        laterWorkingRange.setStaffId(1);
        Map<String, List<TimeRangeDto>> timeRange = new HashMap<>();
        timeRange.put("2024-01-01", List.of(new TimeRangeDto(1000, 1700)));
        laterWorkingRange.setTimeRange(timeRange);

        ResponseResult<List<StaffWorkingRangeDto>> response = ResponseResult.success(List.of(laterWorkingRange));
        when(iBusinessStaffClient.queryRange(anyInt(), any(), any())).thenReturn(response);

        when(moeGroomingPetDetailMapper.queryPetDetailByAppointmentDatesAndStaffIds(
                        anyInt(), anyList(), anyList(), anyList(), any()))
                .thenReturn(List.of());

        // Act
        conflictCheckService.checkConflictForRepeatV2(previewInfoList, previewParams);

        // Assert
        assertThat(previewInfoList.get(0).getIsNotConflict()).isFalse();
        assertThat(previewInfoList.get(0).getStaffConflictInfoList())
                .anyMatch(staff -> staff.getConflictType().equals(ConflictTypeEnum.WORK_TIME.getValue()));
    }

    @Test
    void checkConflictForRepeatV2_WhenAppointmentConflict_ShouldReturnFalse() {
        // Arrange
        ResponseResult<List<StaffWorkingRangeDto>> response = ResponseResult.success(List.of(staffWorkingRange));
        when(iBusinessStaffClient.queryRange(anyInt(), any(), any())).thenReturn(response);

        // 创建一个冲突的预约
        StaffConflictDTO conflictingAppointment = new StaffConflictDTO();
        conflictingAppointment.setStaffId(1);
        conflictingAppointment.setAppointmentDate("2024-01-01");
        conflictingAppointment.setStartTime(900L);
        conflictingAppointment.setServiceTime(60);
        conflictingAppointment.setIsBlock(0);

        when(moeGroomingPetDetailMapper.queryPetDetailByAppointmentDatesAndStaffIds(
                        anyInt(), anyList(), anyList(), anyList(), any()))
                .thenReturn(List.of(conflictingAppointment));

        // Act
        conflictCheckService.checkConflictForRepeatV2(previewInfoList, previewParams);

        // Assert
        assertThat(previewInfoList.get(0).getIsNotConflict()).isFalse();
        assertThat(previewInfoList.get(0).getStaffConflictInfoList())
                .anyMatch(staff -> staff.getConflictType().equals(ConflictTypeEnum.APPOINTMENT.getValue()));
    }

    private StaffConflictInfoDTO createStaffConflictInfo(int staffId, int startTime, int duration) {
        StaffConflictInfoDTO staffConflict = new StaffConflictInfoDTO();
        staffConflict.setStaffId(staffId);
        staffConflict.setStartTime(startTime);
        staffConflict.setDuration(duration);
        staffConflict.setIsNotConflict(true);
        return staffConflict;
    }
}
