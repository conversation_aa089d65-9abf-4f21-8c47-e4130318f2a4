package com.moego.server.grooming.service;

import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.order.v1.CustomizedTipConfig;
import com.moego.server.grooming.dto.AmountPercentagePair;
import com.moego.server.grooming.dto.EvaluationServiceDetailDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class SplitTipsServiceTest {

    @InjectMocks
    private SplitTipsService splitTipsService;

    @Spy
    private AppointmentServiceDetailService appointmentServiceDetailService;

    @Test
    public void getTipsSplitByServiceTest() {
        BigDecimal tipsAmount = BigDecimal.valueOf(100);
        List<GroomingPetDetailDTO> petDetails = new ArrayList<>();

        // test case 1: 一个staff，分配到所有tips
        GroomingPetDetailDTO pd1 = new GroomingPetDetailDTO();
        pd1.setStaffId(100001);
        pd1.setServicePrice(BigDecimal.valueOf(200));
        petDetails.add(pd1);
        Map<Integer, AmountPercentagePair> tipsMap1 = splitTipsService.getTipsSplitByService(tipsAmount, petDetails);
        // expected: staff分配到全部tips
        Assertions.assertEquals(tipsMap1.get(100001).getAmount(), BigDecimal.valueOf(100));
        Assertions.assertEquals(tipsMap1.get(100001).getPercentage(), 100);

        // test case 2: 金额比例2:1，分配2/3，1/3
        GroomingPetDetailDTO pd2 = new GroomingPetDetailDTO();
        pd2.setStaffId(100002);
        pd2.setServicePrice(BigDecimal.valueOf(100));
        petDetails.add(pd2);
        // expected: 按比例分配，staff1 分到2/3，66.67，staff2 分到1/3，33.33
        Map<Integer, AmountPercentagePair> tipsMap2 = splitTipsService.getTipsSplitByService(tipsAmount, petDetails);
        Assertions.assertEquals(tipsMap2.get(100001).getAmount(), BigDecimal.valueOf(66.67));
        Assertions.assertEquals(tipsMap2.get(100001).getPercentage(), 67);
        Assertions.assertEquals(tipsMap2.get(100002).getAmount(), BigDecimal.valueOf(33.33));
        Assertions.assertEquals(tipsMap2.get(100002).getPercentage(), 33);

        // test case 3: 金额比例0:1，分配0，100%
        pd1.setServicePrice(BigDecimal.valueOf(0));
        pd2.setServicePrice(BigDecimal.valueOf(100));
        // expected: 按比例分配，staff1分到0，staff2分到100%
        Map<Integer, AmountPercentagePair> tipsMap3 = splitTipsService.getTipsSplitByService(tipsAmount, petDetails);
        Assertions.assertEquals(
                tipsMap3.get(100001).getAmount(), BigDecimal.valueOf(0).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(tipsMap3.get(100001).getPercentage(), 0);
        Assertions.assertEquals(
                tipsMap3.get(100002).getAmount(), BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(tipsMap3.get(100002).getPercentage(), 100);

        // test case4: 两个服务的金额都是0，总服务金额=0，平均分配
        pd1.setServicePrice(BigDecimal.valueOf(0));
        pd2.setServicePrice(BigDecimal.valueOf(0));
        Map<Integer, AmountPercentagePair> tipsMap4 = splitTipsService.getTipsSplitByService(tipsAmount, petDetails);
        Assertions.assertEquals(
                tipsMap4.get(100001).getAmount(), BigDecimal.valueOf(50).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(tipsMap4.get(100001).getPercentage(), 50);
        Assertions.assertEquals(
                tipsMap4.get(100002).getAmount(), BigDecimal.valueOf(50).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(tipsMap4.get(100002).getPercentage(), 50);
    }

    @Test
    public void getTipsSplitByEquallyTest() {
        BigDecimal tipsAmount = BigDecimal.valueOf(100);
        List<GroomingPetDetailDTO> petDetails = new ArrayList<>();

        // test case 1: 只有一个staff
        GroomingPetDetailDTO pd1 = new GroomingPetDetailDTO();
        pd1.setStaffId(100001);
        pd1.setServicePrice(BigDecimal.valueOf(200));
        petDetails.add(pd1);
        // expected: staff分配到所有tips
        Map<Integer, AmountPercentagePair> tipsMap1 = splitTipsService.getTipSplitByEqually(tipsAmount, petDetails);
        Assertions.assertEquals(
                tipsMap1.get(100001).getAmount(), BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(tipsMap1.get(100001).getPercentage(), 100);

        // test case 2: 两个staff都只有一个service
        GroomingPetDetailDTO pd2 = new GroomingPetDetailDTO();
        pd2.setStaffId(100002);
        pd2.setServicePrice(BigDecimal.valueOf(100));
        petDetails.add(pd2);
        // expected: 两个staff平分tips
        Map<Integer, AmountPercentagePair> tipsMap2 = splitTipsService.getTipSplitByEqually(tipsAmount, petDetails);
        Assertions.assertEquals(
                tipsMap2.get(100001).getAmount(), BigDecimal.valueOf(50).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(tipsMap2.get(100001).getPercentage(), 50);
        Assertions.assertEquals(
                tipsMap2.get(100002).getAmount(), BigDecimal.valueOf(50).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(tipsMap2.get(100002).getPercentage(), 50);

        // test case 3: 两个staff其中一个有两个service
        GroomingPetDetailDTO pd3 = new GroomingPetDetailDTO();
        pd3.setStaffId(100002);
        pd3.setServicePrice(BigDecimal.valueOf(200));
        // expected: 两个staff平分tips
        Map<Integer, AmountPercentagePair> tipsMap3 = splitTipsService.getTipSplitByEqually(tipsAmount, petDetails);
        Assertions.assertEquals(
                tipsMap3.get(100001).getAmount(), BigDecimal.valueOf(50).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(tipsMap3.get(100001).getPercentage(), 50);
        Assertions.assertEquals(
                tipsMap3.get(100002).getAmount(), BigDecimal.valueOf(50).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(tipsMap3.get(100002).getPercentage(), 50);
    }

    @Test
    public void getTipsSplitCustomizedTest() {
        List<CustomizedTipConfig> customizedTipList = new ArrayList<>();

        customizedTipList.add(CustomizedTipConfig.newBuilder()
                .setStaffId(100001)
                .setAmount(20)
                .setPercentage(20)
                .build());
        customizedTipList.add(CustomizedTipConfig.newBuilder()
                .setStaffId(100002)
                .setAmount(80)
                .setPercentage(80)
                .build());
        Map<Integer, AmountPercentagePair> tipsMap = splitTipsService.getTipSplitCustomized(customizedTipList);

        Assertions.assertEquals(
                tipsMap.get(100001).getAmount(), BigDecimal.valueOf(20).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(tipsMap.get(100001).getPercentage(), 20);
        Assertions.assertEquals(
                tipsMap.get(100002).getAmount(), BigDecimal.valueOf(80).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(tipsMap.get(100002).getPercentage(), 80);
    }

    @Test
    public void testSplitTipsBetweenBusinessAndStaffs_Default() {
        BigDecimal tipsAmount = new BigDecimal("100.00");
        List<GroomingPetDetailDTO> petDetails = List.of(
                createGroomingService(1, new BigDecimal("100.00")),
                createGroomingService(1, new BigDecimal("100.00")),
                createGroomingService(2, new BigDecimal("50.00")));

        List<EvaluationServiceDetailDTO> evaluations = new ArrayList<>();

        BigDecimal tipsBaseAmount =
                petDetails.stream().map(GroomingPetDetailDTO::getServicePrice).reduce(BigDecimal.ZERO, BigDecimal::add);

        Assertions.assertEquals(new BigDecimal("250.00"), tipsBaseAmount);

        Pair<BigDecimal, BigDecimal> businessStaffTips =
                splitTipsService.splitTipsBetweenBusinessAndStaff(tipsAmount, tipsBaseAmount, petDetails, evaluations);

        Assertions.assertEquals(new BigDecimal("0.00"), businessStaffTips.getLeft());
        Assertions.assertEquals(new BigDecimal("100.00"), businessStaffTips.getRight());
    }

    @Test
    public void testSplitTipsBetweenBusinessAndStaffs_GroomingOnly() {
        BigDecimal tipsAmount = new BigDecimal("100.00");

        List<GroomingPetDetailDTO> petDetails = List.of(
                createGroomingService(1, new BigDecimal("100.00")),
                createGroomingService(2, new BigDecimal("100.00")),
                createGroomingService(3, new BigDecimal("50.00")));
        List<EvaluationServiceDetailDTO> evaluations = new ArrayList<>();

        BigDecimal tipsBaseAmount =
                petDetails.stream().map(GroomingPetDetailDTO::getServicePrice).reduce(BigDecimal.ZERO, BigDecimal::add);

        Assertions.assertEquals(new BigDecimal("250.00"), tipsBaseAmount);

        Pair<BigDecimal, BigDecimal> businessStaffTips =
                splitTipsService.splitTipsBetweenBusinessAndStaff(tipsAmount, tipsBaseAmount, petDetails, evaluations);

        Assertions.assertEquals(new BigDecimal("0.00"), businessStaffTips.getLeft());
        Assertions.assertEquals(new BigDecimal("100.00"), businessStaffTips.getRight());
    }

    @Test
    public void testSplitTipsBetweenBusinessAndStaffs_GroomingOnly_WithUnassignedStaff() {
        BigDecimal tipsAmount = new BigDecimal("100.00");

        List<GroomingPetDetailDTO> petDetails = List.of(
                createGroomingService(0, new BigDecimal("100.00")), // unassigned staff.
                createGroomingService(2, new BigDecimal("100.00")),
                createGroomingService(3, new BigDecimal("50.00")));
        List<EvaluationServiceDetailDTO> evaluations = new ArrayList<>();

        BigDecimal tipsBaseAmount =
                petDetails.stream().map(GroomingPetDetailDTO::getServicePrice).reduce(BigDecimal.ZERO, BigDecimal::add);

        Assertions.assertEquals(new BigDecimal("250.00"), tipsBaseAmount);

        Pair<BigDecimal, BigDecimal> businessStaffTips =
                splitTipsService.splitTipsBetweenBusinessAndStaff(tipsAmount, tipsBaseAmount, petDetails, evaluations);

        Assertions.assertEquals(new BigDecimal("40.00"), businessStaffTips.getLeft());
        Assertions.assertEquals(new BigDecimal("60.00"), businessStaffTips.getRight());
    }

    @Test
    public void testSplitTipsBetweenBusinessAndStaffs_EvaluationOnly() {
        BigDecimal tipsAmount = new BigDecimal("100.00");

        List<GroomingPetDetailDTO> petDetails = new ArrayList<>();
        List<EvaluationServiceDetailDTO> evaluations = List.of(
                createEvaluationService(1L, new BigDecimal("100.00")),
                createEvaluationService(2L, new BigDecimal("100.00")),
                createEvaluationService(3L, new BigDecimal("50.00")));

        BigDecimal tipsBaseAmount = evaluations.stream()
                .map(EvaluationServiceDetailDTO::getServicePrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        Assertions.assertEquals(new BigDecimal("250.00"), tipsBaseAmount);

        Pair<BigDecimal, BigDecimal> businessStaffTips =
                splitTipsService.splitTipsBetweenBusinessAndStaff(tipsAmount, tipsBaseAmount, petDetails, evaluations);

        Assertions.assertEquals(new BigDecimal("100.00"), businessStaffTips.getLeft());
        Assertions.assertEquals(new BigDecimal("0.00"), businessStaffTips.getRight());
    }

    @Test
    public void testSplitTipsBetweenBusinessAndStaffs_GroomingAndEvaluation_FiftyFifty() {
        BigDecimal tipsAmount = new BigDecimal("100.00");

        List<GroomingPetDetailDTO> petDetails = List.of(
                createGroomingService(1, new BigDecimal("100.00")),
                createGroomingService(2, new BigDecimal("100.00")),
                createGroomingService(3, new BigDecimal("50.00")));
        List<EvaluationServiceDetailDTO> evaluations = List.of(
                createEvaluationService(null, new BigDecimal("100.00")),
                createEvaluationService(5L, new BigDecimal("100.00")),
                createEvaluationService(6L, new BigDecimal("50.00")));

        BigDecimal tipsBaseAmount = Stream.of(
                        petDetails.stream()
                                .map(GroomingPetDetailDTO::getServicePrice)
                                .reduce(BigDecimal.ZERO, BigDecimal::add),
                        evaluations.stream()
                                .map(EvaluationServiceDetailDTO::getServicePrice)
                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        Assertions.assertEquals(new BigDecimal("500.00"), tipsBaseAmount);

        Pair<BigDecimal, BigDecimal> businessStaffTips =
                splitTipsService.splitTipsBetweenBusinessAndStaff(tipsAmount, tipsBaseAmount, petDetails, evaluations);

        Assertions.assertEquals(new BigDecimal("50.00"), businessStaffTips.getLeft());
        Assertions.assertEquals(new BigDecimal("50.00"), businessStaffTips.getRight());
    }

    @Test
    public void testSplitTipsBetweenBusinessAndStaffs_GroomingAndEvaluation_ThirtySeventy() {
        BigDecimal tipsAmount = new BigDecimal("100.00");

        List<GroomingPetDetailDTO> petDetails = List.of(
                // 0 means no staff assigned.
                createGroomingService(0, new BigDecimal("100.00")),
                createGroomingService(2, new BigDecimal("100.00")),
                createGroomingService(3, new BigDecimal("50.00")));
        List<EvaluationServiceDetailDTO> evaluations = List.of(
                createEvaluationService(null, new BigDecimal("100.00")),
                createEvaluationService(5L, new BigDecimal("100.00")),
                createEvaluationService(6L, new BigDecimal("50.00")));

        BigDecimal tipsBaseAmount = Stream.of(
                        petDetails.stream()
                                .map(GroomingPetDetailDTO::getServicePrice)
                                .reduce(BigDecimal.ZERO, BigDecimal::add),
                        evaluations.stream()
                                .map(EvaluationServiceDetailDTO::getServicePrice)
                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        Assertions.assertEquals(new BigDecimal("500.00"), tipsBaseAmount);

        Pair<BigDecimal, BigDecimal> businessStaffTips =
                splitTipsService.splitTipsBetweenBusinessAndStaff(tipsAmount, tipsBaseAmount, petDetails, evaluations);

        Assertions.assertEquals(new BigDecimal("70.00"), businessStaffTips.getLeft());
        Assertions.assertEquals(new BigDecimal("30.00"), businessStaffTips.getRight());
    }

    @Test
    public void testSplitTipsBetweenBusinessAndStaffs_SmallTipBasedAmount() {
        BigDecimal tipsAmount = new BigDecimal("100.00");

        List<GroomingPetDetailDTO> petDetails = List.of(
                // 0 means no staff assigned.
                createGroomingService(0, new BigDecimal("100.00")),
                createGroomingService(2, new BigDecimal("100.00")),
                createGroomingService(3, new BigDecimal("50.00")));
        List<EvaluationServiceDetailDTO> evaluations = List.of(
                createEvaluationService(null, new BigDecimal("100.00")),
                createEvaluationService(5L, new BigDecimal("100.00")),
                createEvaluationService(6L, new BigDecimal("50.00")));

        // Less than the actual total amount.
        BigDecimal tipsBaseAmount = new BigDecimal("1.23");
        Assertions.assertTrue(tipsBaseAmount.compareTo(Stream.of(
                                petDetails.stream()
                                        .map(GroomingPetDetailDTO::getServicePrice)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add),
                                evaluations.stream()
                                        .map(EvaluationServiceDetailDTO::getServicePrice)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                < 0);

        Pair<BigDecimal, BigDecimal> businessStaffTips =
                splitTipsService.splitTipsBetweenBusinessAndStaff(tipsAmount, tipsBaseAmount, petDetails, evaluations);

        Assertions.assertEquals(new BigDecimal("100.00"), businessStaffTips.getLeft());
        Assertions.assertEquals(new BigDecimal("0.00"), businessStaffTips.getRight());
    }

    @Test
    public void testVerifyCustomizedByPercentageParams() {
        BigDecimal tipsAmount = new BigDecimal("100.00");
        Map<Integer, AmountPercentagePair> staffAmountMap = new HashMap<>();

        // 测试正常情况: 百分比之和为100%
        AmountPercentagePair pair1 = new AmountPercentagePair();
        pair1.setPercentage(60);
        staffAmountMap.put(1, pair1);

        AmountPercentagePair pair2 = new AmountPercentagePair();
        pair2.setPercentage(40);
        staffAmountMap.put(2, pair2);

        splitTipsService.verifyCustomizedByPercentageParams(tipsAmount, staffAmountMap);

        // 验证金额计算是否正确
        Assertions.assertEquals(new BigDecimal("60.00"), staffAmountMap.get(1).getAmount());
        Assertions.assertEquals(60, staffAmountMap.get(1).getPercentage());
        Assertions.assertEquals(new BigDecimal("40.00"), staffAmountMap.get(2).getAmount());
        Assertions.assertEquals(40, staffAmountMap.get(2).getPercentage());

        // 测试异常情况: 百分比之和不为100%
        AmountPercentagePair pair3 = new AmountPercentagePair();
        pair3.setPercentage(50);
        staffAmountMap.put(3, pair3);

        Assertions.assertThrows(Exception.class, () -> {
            splitTipsService.verifyCustomizedByPercentageParams(tipsAmount, staffAmountMap);
        });
    }

    @Test
    public void testVerifyCustomizedByAmountParams() {
        BigDecimal tipsAmount = new BigDecimal("100.00");
        Map<Integer, AmountPercentagePair> staffAmountMap = new HashMap<>();

        // 测试正常情况: 金额之和等于总小费
        AmountPercentagePair pair1 = new AmountPercentagePair();
        pair1.setAmount(new BigDecimal("60.00"));
        staffAmountMap.put(1, pair1);

        AmountPercentagePair pair2 = new AmountPercentagePair();
        pair2.setAmount(new BigDecimal("40.00"));
        staffAmountMap.put(2, pair2);

        splitTipsService.verifyCustomizedByAmountParams(tipsAmount, staffAmountMap);

        // 验证百分比计算是否正确
        Assertions.assertEquals(60, staffAmountMap.get(1).getPercentage());
        Assertions.assertEquals(new BigDecimal("60.00"), staffAmountMap.get(1).getAmount());
        Assertions.assertEquals(40, staffAmountMap.get(2).getPercentage());
        Assertions.assertEquals(new BigDecimal("40.00"), staffAmountMap.get(2).getAmount());

        // 测试异常情况: 金额之和不等于总小费
        staffAmountMap.clear();
        AmountPercentagePair pair3 = new AmountPercentagePair();
        pair3.setAmount(new BigDecimal("50.00"));
        staffAmountMap.put(3, pair3);

        Assertions.assertThrows(Exception.class, () -> {
            splitTipsService.verifyCustomizedByAmountParams(tipsAmount, staffAmountMap);
        });

        // 测试边界情况: 总金额为0
        staffAmountMap.clear();
        AmountPercentagePair pair4 = new AmountPercentagePair();
        pair4.setAmount(BigDecimal.ZERO);
        staffAmountMap.put(1, pair4);

        AmountPercentagePair pair5 = new AmountPercentagePair();
        pair5.setAmount(BigDecimal.ZERO);
        staffAmountMap.put(2, pair5);

        splitTipsService.verifyCustomizedByAmountParams(new BigDecimal("0.00"), staffAmountMap);

        // 测试边界情况: 总金额为0, 不同精度的 0
        staffAmountMap.clear();
        AmountPercentagePair pair6 = new AmountPercentagePair();
        pair6.setAmount(BigDecimal.ZERO);
        staffAmountMap.put(1, pair6);

        AmountPercentagePair pair7 = new AmountPercentagePair();
        pair7.setAmount(BigDecimal.ZERO);
        staffAmountMap.put(2, pair7);

        splitTipsService.verifyCustomizedByAmountParams(BigDecimal.ZERO, staffAmountMap);

        // 验证百分比计算是否正确
        Assertions.assertEquals(0, staffAmountMap.get(1).getPercentage());
        Assertions.assertEquals(BigDecimal.ZERO, staffAmountMap.get(1).getAmount());
        Assertions.assertEquals(100, staffAmountMap.get(2).getPercentage());
        Assertions.assertEquals(BigDecimal.ZERO, staffAmountMap.get(2).getAmount());

        // 测试边界情况: 总金额为0,但单个金额不为0
        staffAmountMap.clear();
        AmountPercentagePair pair8 = new AmountPercentagePair();
        pair8.setAmount(new BigDecimal("50.00"));
        staffAmountMap.put(1, pair8);

        AmountPercentagePair pair9 = new AmountPercentagePair();
        pair9.setAmount(new BigDecimal("50.00"));
        staffAmountMap.put(2, pair9);

        Assertions.assertThrows(Exception.class, () -> {
            splitTipsService.verifyCustomizedByAmountParams(BigDecimal.ZERO, staffAmountMap);
        });
    }

    private GroomingPetDetailDTO createGroomingService(int staffId, BigDecimal servicePrice) {
        GroomingPetDetailDTO petDetail = new GroomingPetDetailDTO();
        petDetail.setStaffId(staffId);
        petDetail.setServicePrice(servicePrice);
        petDetail.setServiceType(ServiceType.SERVICE_VALUE);
        // 下面都是为了跑通计算流程设置的固定值
        petDetail.setPriceUnit(ServicePriceUnit.PER_HOUR_VALUE);
        petDetail.setStartDate("2024-01-01");
        petDetail.setEndDate("2024-01-01");
        petDetail.setStartTime(0L);
        petDetail.setEndTime(60L); // 60 min
        petDetail.setQuantity(1);
        petDetail.setPetId(1);

        return petDetail;
    }

    private EvaluationServiceDetailDTO createEvaluationService(Long staffId, BigDecimal servicePrice) {
        EvaluationServiceDetailDTO evaluationService = new EvaluationServiceDetailDTO();
        evaluationService.setStaffId(staffId);
        evaluationService.setServicePrice(servicePrice);

        return evaluationService;
    }
}
