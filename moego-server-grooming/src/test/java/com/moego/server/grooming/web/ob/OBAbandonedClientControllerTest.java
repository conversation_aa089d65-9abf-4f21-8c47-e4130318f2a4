package com.moego.server.grooming.web.ob;

import static com.moego.server.grooming.web.ob.OBAbandonedClientController.getLeadTypeOrder;
import static org.assertj.core.api.Assertions.assertThat;

import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord;
import com.moego.server.grooming.web.params.SearchAbandonedClientParam;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;

class OBAbandonedClientControllerTest {

    @Test
    void testSort() {
        MoeBookOnlineAbandonRecord r1 = new MoeBookOnlineAbandonRecord();
        r1.setAbandonStep(OBStepEnum.personal_info.name());
        r1.setLeadType(SearchAbandonedClientParam.LeadType.NEW_VISITOR.getValue());
        MoeBookOnlineAbandonRecord r2 = new MoeBookOnlineAbandonRecord();
        r2.setAbandonStep(OBStepEnum.prepay.name());
        r2.setLeadType(SearchAbandonedClientParam.LeadType.EXISTING_CLIENT.getValue());
        MoeBookOnlineAbandonRecord r3 = new MoeBookOnlineAbandonRecord();
        r3.setAbandonStep(OBStepEnum.prepay.name());
        r3.setLeadType(SearchAbandonedClientParam.LeadType.NEW_VISITOR.getValue());
        MoeBookOnlineAbandonRecord r4 = new MoeBookOnlineAbandonRecord();
        r4.setAbandonStep(OBStepEnum.personal_info.name());
        r4.setLeadType(SearchAbandonedClientParam.LeadType.EXISTING_CLIENT.getValue());
        List<MoeBookOnlineAbandonRecord> records = Stream.of(r1, r2, r3, r4)
                .sorted((o1, o2) -> {
                    if (o1.getAbandonStep().equals(o2.getAbandonStep())) {
                        return getLeadTypeOrder(o1.getLeadType()) - getLeadTypeOrder(o2.getLeadType());
                    }
                    return (OBStepEnum.valueOf(o2.getAbandonStep()).getOrder()
                            - OBStepEnum.valueOf(o1.getAbandonStep()).getOrder());
                })
                .toList();

        assertThat(records).containsExactly(r3, r2, r1, r4);
    }
}
