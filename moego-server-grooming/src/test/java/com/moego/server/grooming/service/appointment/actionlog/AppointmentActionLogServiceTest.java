package com.moego.server.grooming.service.appointment.actionlog;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.google.protobuf.Struct;
import com.google.protobuf.Value;
import com.moego.idl.models.activity_log.v1.ActivityLogModel;
import com.moego.idl.models.activity_log.v1.ActivityLogModelSimpleView;
import com.moego.idl.service.activity_log.v1.ActivityLogServiceGrpc.ActivityLogServiceBlockingStub;
import com.moego.idl.service.activity_log.v1.ListActivityLogDetailsResponse;
import com.moego.idl.service.activity_log.v1.SearchActivityLogPageOutput;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub;
import com.moego.server.grooming.dto.appointment.history.ActionHistoryDTO;
import com.moego.server.grooming.dto.appointment.history.AppointmentActionHistory;
import com.moego.server.grooming.enums.ActionTypeEnum;
import com.moego.server.grooming.service.AutoAssignService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AppointmentActionLogServiceTest {

    @Mock
    private ActivityLogServiceBlockingStub activityLogServiceBlockingStub;

    @Mock
    private AutoAssignService autoAssignService;

    @Mock
    private ServiceManagementServiceBlockingStub serviceStub;

    @InjectMocks
    private AppointmentActionLogService appointmentActionLogService;

    private ActivityLogModelSimpleView sampleActivityLog;
    private ActivityLogModel sampleActivityLogDetail;

    @BeforeEach
    void setUp() {
        // Setup sample activity log
        sampleActivityLog = ActivityLogModelSimpleView.newBuilder()
                .setId("1")
                .setAction("CREATE")
                .setOperator(com.moego.idl.models.activity_log.v1.Operator.newBuilder()
                        .setId("123")
                        .build())
                .setTime(com.google.protobuf.Timestamp.newBuilder()
                        .setSeconds(1000)
                        .build())
                .build();

        // Setup sample activity log detail
        Struct detailStruct = Struct.newBuilder()
                .putFields("isAutoAccept", Value.newBuilder().setBoolValue(true).build())
                .putFields(
                        "bookOnlineStatus", Value.newBuilder().setNumberValue(1).build())
                .build();

        sampleActivityLogDetail = ActivityLogModel.newBuilder()
                .setId("1")
                .setDetails(Value.newBuilder().setStructValue(detailStruct).build())
                .build();
    }

    @Test
    void getActionHistory_WhenFound_ReturnsActionHistory() {
        // Arrange
        Integer businessId = 1;
        Integer appointmentId = 100;

        SearchActivityLogPageOutput searchOutput = SearchActivityLogPageOutput.newBuilder()
                .addActivityLogs(sampleActivityLog)
                .build();

        ListActivityLogDetailsResponse detailsResponse = ListActivityLogDetailsResponse.newBuilder()
                .addActivityLogs(sampleActivityLogDetail)
                .build();

        when(activityLogServiceBlockingStub.searchActivityLogPage(any())).thenReturn(searchOutput);
        when(activityLogServiceBlockingStub.listActivityLogDetails(any())).thenReturn(detailsResponse);

        // Act
        ActionHistoryDTO result = appointmentActionLogService.getActionHistory(businessId, appointmentId);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getActions()).isNotEmpty();

        AppointmentActionHistory action = result.getActions().get(0);
        assertThat(action.getOperatorId()).isEqualTo(123);
        assertThat(action.getActionType()).isEqualTo(ActionTypeEnum.CREATE);
        assertThat(action.getOperateTime()).isEqualTo(1000);
    }
}
