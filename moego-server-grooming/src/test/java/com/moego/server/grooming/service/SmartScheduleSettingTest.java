package com.moego.server.grooming.service;

import com.moego.common.constant.CommonConstant;
import com.moego.common.constant.Dictionary;
import com.moego.server.business.dto.SmartScheduleSettingDTO;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 * @since 2022/8/3
 */
@ExtendWith(MockitoExtension.class)
public class SmartScheduleSettingTest {

    @InjectMocks
    private SmartScheduleSettingDTO smartScheduleSettingDTO;

    @Test
    public void getMaxDistanceMileType1() {
        Integer maxDistance = 50;
        smartScheduleSettingDTO.setMaxDistance(maxDistance);
        smartScheduleSettingDTO.setUnitOfDistanceType(Dictionary.UNITED_DISTANCE_TYPE_1.byteValue());
        Integer maxDistanceMile = smartScheduleSettingDTO.getMaxDistanceMile();
        Assertions.assertThat(maxDistanceMile)
                .as("maxDistanceMile not equals 50")
                .isEqualTo(maxDistance);
    }

    @Test
    public void getMaxDistanceMileType2() {
        int maxDistance = 50;
        smartScheduleSettingDTO.setMaxDistance(maxDistance);
        smartScheduleSettingDTO.setUnitOfDistanceType(Dictionary.UNITED_DISTANCE_TYPE_2.byteValue());
        Integer maxDistanceMile = smartScheduleSettingDTO.getMaxDistanceMile();
        Assertions.assertThat(maxDistanceMile)
                .as("maxDistanceMile not equals 50")
                .isEqualTo(Double.valueOf(maxDistance / CommonConstant.MILE_TO_KILOMETER)
                        .intValue());
    }
}
