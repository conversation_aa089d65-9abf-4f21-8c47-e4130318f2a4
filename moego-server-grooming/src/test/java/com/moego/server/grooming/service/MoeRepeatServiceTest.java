package com.moego.server.grooming.service;

import static org.junit.jupiter.api.Assertions.assertTrue;

import com.moego.server.grooming.params.RepeatParams;
import com.moego.server.grooming.service.utils.RepeatUtil;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.Test;

class MoeRepeatServiceTest {

    @Test
    void repeatMonthTest() {
        RepeatParams param = new RepeatParams();
        param.setRepeatType(3);
        param.setStartsOn("2021-01-31");
        param.setSetEndOn("2121-06-29");
        param.setTimes(10);
        param.setRepeatEveryType(2);
        param.setMonthDay(29);
        param.setType("2");
        List<LocalDate> list = RepeatUtil.checkAndComputeDate(param);
        for (LocalDate date : list) {
            System.out.println(date.toString());
        }
        assertTrue(list.size() <= 100);
    }
}
