package com.moego.server.grooming.utils;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class PetDetailDTOUtilTest {

    @Test
    void getServiceDates_withPerNightBoarding_EverydayBoardingAddOn() {
        // Arrange
        GroomingPetDetailDTO boarding = new GroomingPetDetailDTO();
        boarding.setPetId(1);
        boarding.setServiceId(1127763);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-11-20");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-11-22");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        GroomingPetDetailDTO addon = new GroomingPetDetailDTO();
        addon.setPetId(1);
        addon.setServiceId(1049595);
        addon.setAssociatedServiceId(1127763L);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        addon.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        addon.setQuantityPerDay(1);
        addon.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);
        addon.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap =
                PetDetailDTOUtil.getPetServiceMap(List.of(boarding, addon));

        // Act
        var result = PetDetailDTOUtil.getServiceDates(addon, petServiceMap);
        var expected = List.of("2024-11-20", "2024-11-21");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withPerNightBoarding_CertainDayBoardingAddOn() {
        // Arrange
        GroomingPetDetailDTO boarding = new GroomingPetDetailDTO();
        boarding.setPetId(1);
        boarding.setServiceId(1127763);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-11-20");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-11-22");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        GroomingPetDetailDTO addon = new GroomingPetDetailDTO();
        addon.setPetId(1);
        addon.setServiceId(1049595);
        addon.setAssociatedServiceId(1127763L);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        addon.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        addon.setSpecificDates("[\"2024-11-20\", \"2024-11-22\"]");
        addon.setQuantityPerDay(1);
        addon.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap =
                PetDetailDTOUtil.getPetServiceMap(List.of(boarding, addon));

        // Act
        var result = PetDetailDTOUtil.getServiceDates(addon, petServiceMap);
        var expected = List.of("2024-11-20", "2024-11-22");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withPerDayBoarding_EverydayBoardingAddOn() {
        // Arrange
        GroomingPetDetailDTO boarding = new GroomingPetDetailDTO();
        boarding.setPetId(1);
        boarding.setServiceId(1127763);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-11-20");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-11-22");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_DAY_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        GroomingPetDetailDTO addon = new GroomingPetDetailDTO();
        addon.setPetId(1);
        addon.setServiceId(1049595);
        addon.setAssociatedServiceId(1127763L);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        addon.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        addon.setQuantityPerDay(1);
        addon.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap =
                PetDetailDTOUtil.getPetServiceMap(List.of(boarding, addon));

        // Act
        var result = PetDetailDTOUtil.getServiceDates(addon, petServiceMap);
        var expected = List.of("2024-11-20", "2024-11-21");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withPerDayBoarding_CertainDayBoardingAddOn() {
        // Arrange
        GroomingPetDetailDTO boarding = new GroomingPetDetailDTO();
        boarding.setPetId(1);
        boarding.setServiceId(1127763);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-11-20");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-11-22");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_DAY_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        GroomingPetDetailDTO addon = new GroomingPetDetailDTO();
        addon.setPetId(1);
        addon.setServiceId(1049595);
        addon.setAssociatedServiceId(1127763L);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        addon.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        addon.setSpecificDates("[\"2024-11-20\", \"2024-11-22\"]");
        addon.setQuantityPerDay(1);
        addon.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap =
                PetDetailDTOUtil.getPetServiceMap(List.of(boarding, addon));

        // Act
        var result = PetDetailDTOUtil.getServiceDates(addon, petServiceMap);
        var expected = List.of("2024-11-20", "2024-11-22");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withPerNightBoarding_EverydayGroomingAddOn() {
        // Arrange
        GroomingPetDetailDTO boarding = new GroomingPetDetailDTO();
        boarding.setPetId(1);
        boarding.setServiceId(1127763);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-11-20");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-11-22");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        GroomingPetDetailDTO addon = new GroomingPetDetailDTO();
        addon.setPetId(1);
        addon.setServiceId(1049595);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.GROOMING_VALUE);
        addon.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        addon.setQuantityPerDay(1);
        addon.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap =
                PetDetailDTOUtil.getPetServiceMap(List.of(boarding, addon));

        // Act
        var result = PetDetailDTOUtil.getServiceDates(addon, petServiceMap);
        var expected = List.of("2024-11-20", "2024-11-21");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withPerDayBoarding_EverydayGroomingAddOn() {
        // Arrange
        GroomingPetDetailDTO boarding = new GroomingPetDetailDTO();
        boarding.setPetId(1);
        boarding.setServiceId(1127763);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-11-20");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-11-22");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_DAY_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        GroomingPetDetailDTO addon = new GroomingPetDetailDTO();
        addon.setPetId(1);
        addon.setServiceId(1049595);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.GROOMING_VALUE);
        addon.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        addon.setQuantityPerDay(1);
        addon.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap =
                PetDetailDTOUtil.getPetServiceMap(List.of(boarding, addon));

        // Act
        var result = PetDetailDTOUtil.getServiceDates(addon, petServiceMap);
        var expected = List.of("2024-11-20", "2024-11-21");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withPerNightBoardingService_EverydayDaycareService_EverydayDaycareAddOn() {
        // Arrange
        GroomingPetDetailDTO boarding = new GroomingPetDetailDTO();
        boarding.setPetId(1);
        boarding.setServiceId(1127763);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-11-20");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-11-22");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        GroomingPetDetailDTO daycare = new GroomingPetDetailDTO();
        daycare.setPetId(1);
        daycare.setServiceId(1091908);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setStartTime(0L);
        daycare.setEndTime(0L);
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        daycare.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        GroomingPetDetailDTO addon = new GroomingPetDetailDTO();
        addon.setPetId(1);
        addon.setServiceId(1049595);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        addon.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        addon.setAssociatedServiceId(1091908L);
        addon.setQuantityPerDay(1);
        addon.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap =
                PetDetailDTOUtil.getPetServiceMap(List.of(boarding, daycare, addon));

        // Act
        var daycareResult = PetDetailDTOUtil.getServiceDates(daycare, petServiceMap);
        var daycareExpected = List.of("2024-11-20", "2024-11-21");
        var addOnResult = PetDetailDTOUtil.getServiceDates(addon, petServiceMap);
        var addOnExpected = List.of("2024-11-20", "2024-11-21");

        // Assert
        assertThat(daycareResult).isEqualTo(daycareExpected);
        assertThat(addOnResult).isEqualTo(addOnExpected);
    }

    @Test
    void getServiceDates_withPerNightBoardingService_MultiDayDaycareService_EverydayDaycareAddOn() {
        // Arrange
        GroomingPetDetailDTO boarding = new GroomingPetDetailDTO();
        boarding.setPetId(1);
        boarding.setServiceId(1127763);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-11-20");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-11-22");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        GroomingPetDetailDTO daycare = new GroomingPetDetailDTO();
        daycare.setPetId(1);
        daycare.setServiceId(1091908);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setStartTime(0L);
        daycare.setEndTime(0L);
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        daycare.setSpecificDates("[\"2024-11-20\", \"2024-11-22\"]");
        daycare.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE);

        GroomingPetDetailDTO addon = new GroomingPetDetailDTO();
        addon.setPetId(1);
        addon.setServiceId(1049595);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        addon.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        addon.setAssociatedServiceId(1091908L);
        addon.setQuantityPerDay(1);
        addon.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap =
                PetDetailDTOUtil.getPetServiceMap(List.of(boarding, daycare, addon));

        // Act
        var daycareResult = PetDetailDTOUtil.getServiceDates(daycare, petServiceMap);
        var daycareExpected = List.of("2024-11-20", "2024-11-22");
        var addOnResult = PetDetailDTOUtil.getServiceDates(addon, petServiceMap);
        var addOnExpected = List.of("2024-11-20", "2024-11-22");

        // Assert
        assertThat(daycareResult).isEqualTo(daycareExpected);
        assertThat(addOnResult).isEqualTo(addOnExpected);
    }

    @Test
    void getServiceDates_withPerNightBoardingService_MultiDayDaycareService_EverydayGroomingAddOn() {
        // Arrange
        GroomingPetDetailDTO boarding = new GroomingPetDetailDTO();
        boarding.setPetId(1);
        boarding.setServiceId(1127763);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-11-20");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-11-22");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        GroomingPetDetailDTO daycare = new GroomingPetDetailDTO();
        daycare.setPetId(1);
        daycare.setServiceId(1091908);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setStartTime(0L);
        daycare.setEndTime(0L);
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        daycare.setSpecificDates("[\"2024-11-20\", \"2024-11-22\"]");
        daycare.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE);

        GroomingPetDetailDTO addon = new GroomingPetDetailDTO();
        addon.setPetId(1);
        addon.setServiceId(1049595);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.GROOMING_VALUE);
        addon.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        addon.setQuantityPerDay(1);
        addon.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap =
                PetDetailDTOUtil.getPetServiceMap(List.of(boarding, daycare, addon));

        // Act
        var daycareResult = PetDetailDTOUtil.getServiceDates(daycare, petServiceMap);
        var daycareExpected = List.of("2024-11-20", "2024-11-22");
        var addOnResult = PetDetailDTOUtil.getServiceDates(addon, petServiceMap);
        var addOnExpected = List.of("2024-11-20", "2024-11-21");

        // Assert
        assertThat(daycareResult).isEqualTo(daycareExpected);
        assertThat(addOnResult).isEqualTo(addOnExpected);
    }

    @Test
    void getServiceDates_withPerDayBoardingService_MultiDayDaycareService_EverydayGroomingAddOn() {
        // Arrange
        GroomingPetDetailDTO boarding = new GroomingPetDetailDTO();
        boarding.setPetId(1);
        boarding.setServiceId(1127763);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-11-20");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-11-22");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_DAY_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        GroomingPetDetailDTO daycare = new GroomingPetDetailDTO();
        daycare.setPetId(1);
        daycare.setServiceId(1091908);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setStartTime(0L);
        daycare.setEndTime(0L);
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        daycare.setSpecificDates("[\"2024-11-20\", \"2024-11-22\"]");
        daycare.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE);

        GroomingPetDetailDTO addon = new GroomingPetDetailDTO();
        addon.setPetId(1);
        addon.setServiceId(1049595);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.GROOMING_VALUE);
        addon.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        addon.setQuantityPerDay(1);
        addon.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap =
                PetDetailDTOUtil.getPetServiceMap(List.of(boarding, daycare, addon));

        // Act
        var daycareResult = PetDetailDTOUtil.getServiceDates(daycare, petServiceMap);
        var daycareExpected = List.of("2024-11-20", "2024-11-22");
        var addOnResult = PetDetailDTOUtil.getServiceDates(addon, petServiceMap);
        var addOnExpected = List.of("2024-11-20", "2024-11-21");

        // Assert
        assertThat(daycareResult).isEqualTo(daycareExpected);
        assertThat(addOnResult).isEqualTo(addOnExpected);
    }

    @Test
    void getServiceDates_withPerDayBoardingService_EveryDaycareService_EverydayGroomingAddOn() {
        // Arrange
        GroomingPetDetailDTO boarding = new GroomingPetDetailDTO();
        boarding.setPetId(1);
        boarding.setServiceId(1127763);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-11-20");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-11-22");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_DAY_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        GroomingPetDetailDTO daycare = new GroomingPetDetailDTO();
        daycare.setPetId(1);
        daycare.setServiceId(1091908);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setStartTime(0L);
        daycare.setEndTime(0L);
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        daycare.setQuantityPerDay(1);
        daycare.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        GroomingPetDetailDTO addon = new GroomingPetDetailDTO();
        addon.setPetId(1);
        addon.setServiceId(1049595);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.GROOMING_VALUE);
        addon.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        addon.setQuantityPerDay(1);
        addon.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap =
                PetDetailDTOUtil.getPetServiceMap(List.of(boarding, daycare, addon));

        // Act
        var daycareResult = PetDetailDTOUtil.getServiceDates(daycare, petServiceMap);
        var daycareExpected = List.of("2024-11-20", "2024-11-21");
        var addOnResult = PetDetailDTOUtil.getServiceDates(addon, petServiceMap);
        var addOnExpected = List.of("2024-11-20", "2024-11-21");

        // Assert
        assertThat(daycareResult).isEqualTo(daycareExpected);
        assertThat(addOnResult).isEqualTo(addOnExpected);
    }

    @Test
    void getServiceDates_withDaycare_DaycareAddOn_GroomingAddOn() {
        // Arrange
        GroomingPetDetailDTO daycare = new GroomingPetDetailDTO();
        daycare.setPetId(1);
        daycare.setServiceId(1096338);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setStartDate("2024-11-20");
        daycare.setStartTime(600L);
        daycare.setEndDate("2024-11-20");
        daycare.setEndTime(600L);
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        daycare.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        GroomingPetDetailDTO daycareAddOn = new GroomingPetDetailDTO();
        daycareAddOn.setPetId(1);
        daycareAddOn.setServiceId(1049595);
        daycareAddOn.setServiceType(ServiceType.ADDON_VALUE);
        daycareAddOn.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycareAddOn.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        daycareAddOn.setAssociatedServiceId(1096338L);
        daycareAddOn.setQuantityPerDay(1);
        daycare.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        GroomingPetDetailDTO groomingAddOn = new GroomingPetDetailDTO();
        groomingAddOn.setPetId(1);
        groomingAddOn.setServiceId(1119327);
        groomingAddOn.setServiceType(ServiceType.ADDON_VALUE);
        groomingAddOn.setServiceItemType(ServiceItemType.GROOMING_VALUE);
        groomingAddOn.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        groomingAddOn.setQuantityPerDay(1);
        groomingAddOn.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap =
                PetDetailDTOUtil.getPetServiceMap(List.of(daycare, daycareAddOn, groomingAddOn));

        // Act
        var daycareAddOnResult = PetDetailDTOUtil.getServiceDates(daycareAddOn, petServiceMap);
        var daycareAddOnExpected = List.of("2024-11-20");
        var groomingAddOnResult = PetDetailDTOUtil.getServiceDates(groomingAddOn, petServiceMap);
        var groomingAddOnExpected = List.of("2024-11-20");

        // Assert
        assertThat(daycareAddOnResult).isEqualTo(daycareAddOnExpected);
        assertThat(groomingAddOnResult).isEqualTo(groomingAddOnExpected);
    }
}
