package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.common.enums.order.OrderItemType;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.service.membership.v1.GetRecommendBenefitUsageResponse;
import com.moego.idl.service.membership.v1.MembershipServiceGrpc;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.grooming.dto.GroomingPackageServiceInfoDTO;
import com.moego.server.grooming.mapper.MoeGroomingInvoiceApplyPackageMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackage;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import com.moego.server.grooming.mapperbean.MoeGroomingPackage;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageService;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OrderServiceTest {

    @Mock
    OrderServiceGrpc.OrderServiceBlockingStub orderClient;

    @Mock
    MoeGroomingInvoiceApplyPackageMapper invoiceApplyPackageMapper;

    @Mock
    ICustomerCustomerClient iCustomerCustomerClient;

    @Mock
    MembershipServiceGrpc.MembershipServiceBlockingStub membershipService;

    @InjectMocks
    private OrderService orderService;

    @Test
    public void testJoinAvailablePackageService() {
        List<MoeGroomingInvoiceItem> invoiceItems;
        Map<Integer, List<GroomingPackageServiceInfoDTO>> servicePackageMap = new HashMap<>();
        Map<Integer, Integer> processingServiceQuantityMap = new HashMap<>();

        // case1: 2个 service item, 两个 item 各有一个可用 package, 无 processing 占用数量
        // expect: 得到两个可用 package, item1 和 item2 各一个
        MoeGroomingInvoiceItem item1 = buildServiceInvoiceItem(1, 2);
        MoeGroomingInvoiceItem item2 = buildServiceInvoiceItem(2, 1);
        invoiceItems = Arrays.asList(item1, item2);

        GroomingPackageServiceInfoDTO servicePackage1 = buildServicePackage(10, 1000, 5);
        servicePackageMap.put(item1.getServiceId(), Collections.singletonList(servicePackage1));
        GroomingPackageServiceInfoDTO servicePackage2 = buildServicePackage(11, 1001, 5);
        servicePackageMap.put(item2.getServiceId(), Collections.singletonList(servicePackage2));

        List<MoeGroomingInvoiceApplyPackage> result1 =
                orderService.joinAvailablePackageService(invoiceItems, servicePackageMap, processingServiceQuantityMap);
        assertThat(result1.size()).isEqualTo(2);
        assertThat(result1.get(0).getServiceId()).isEqualTo(item1.getServiceId());
        assertThat(result1.get(0).getQuantity()).isEqualTo((item1.getQuantity()));
        assertThat(result1.get(1).getServiceId()).isEqualTo(item2.getServiceId());
        assertThat(result1.get(1).getQuantity()).isEqualTo((item2.getQuantity()));

        // case2: 2个 service item, item1 有两个可用 package(package1 remainingQuantity > item1.quantity), item2 无 package,  无
        // processing 占用数量
        // expect: 返回一个 item1 的可用 package，数量为 item1.quantity
        item1 = buildServiceInvoiceItem(1, 2);
        item2 = buildServiceInvoiceItem(2, 1);
        invoiceItems = Arrays.asList(item1, item2);

        servicePackageMap.clear();
        servicePackage1 = buildServicePackage(10, 1000, 5);
        servicePackage2 = buildServicePackage(11, 1001, 5);
        servicePackageMap.put(item1.getServiceId(), Arrays.asList(servicePackage1, servicePackage2));

        List<MoeGroomingInvoiceApplyPackage> result2 =
                orderService.joinAvailablePackageService(invoiceItems, servicePackageMap, processingServiceQuantityMap);
        assertThat(result2.size()).isEqualTo(1);
        assertThat(result2.get(0).getServiceId()).isEqualTo(item1.getServiceId());
        assertThat(result2.get(0).getQuantity()).isEqualTo((item1.getQuantity()));

        // case3: 2个 service item, item1 有一个 package(package1 remainingQuantity < item1.quantity), item2 无 package,  无
        // processing 占用数量
        // expect: 返回一个 item1 的可用 package，数量为 package1.remainingQuantity
        item1 = buildServiceInvoiceItem(1, 2);
        item2 = buildServiceInvoiceItem(2, 1);
        invoiceItems = Arrays.asList(item1, item2);

        servicePackageMap.clear();
        servicePackage1 = buildServicePackage(10, 1000, 1);
        servicePackageMap.put(item1.getServiceId(), Collections.singletonList(servicePackage1));

        List<MoeGroomingInvoiceApplyPackage> result3 =
                orderService.joinAvailablePackageService(invoiceItems, servicePackageMap, processingServiceQuantityMap);
        assertThat(result3.size()).isEqualTo(1);
        assertThat(result3.get(0).getServiceId()).isEqualTo(item1.getServiceId());
        assertThat(result3.get(0).getQuantity()).isEqualTo(1);

        // case4: 2个 service item, item1 有两个不可用 package, 无 processing 占用数量
        // expect: 空
        item1 = buildServiceInvoiceItem(1, 2);
        item2 = buildServiceInvoiceItem(2, 1);
        invoiceItems = Arrays.asList(item1, item2);

        servicePackageMap.clear();
        servicePackage1 = buildServicePackage(10, 1000, 0);
        servicePackage2 = buildServicePackage(11, 1001, 0);
        servicePackageMap.put(item1.getServiceId(), Arrays.asList(servicePackage1, servicePackage2));
        List<MoeGroomingInvoiceApplyPackage> result4 =
                orderService.joinAvailablePackageService(invoiceItems, servicePackageMap, processingServiceQuantityMap);
        assertThat(result4.size()).isEqualTo(0);

        // case5: 2个 service item, item1 有两个可用 package, package1 processing 数量小于 package1.remainingQuantity，package2
        // processing 数量等于 package2.remainingQuantity
        // expect: 返回一个 item1 的可用 package，数量 = Min(item.quantity, package1.remainQuantity - package1.processingQuantity)
        item1 = buildServiceInvoiceItem(1, 2);
        item2 = buildServiceInvoiceItem(2, 1);
        invoiceItems = Arrays.asList(item1, item2);

        servicePackageMap.clear();
        servicePackage1 = buildServicePackage(10, 1000, 5);
        servicePackage2 = buildServicePackage(11, 1001, 5);
        servicePackageMap.put(item1.getServiceId(), Arrays.asList(servicePackage1, servicePackage2));

        processingServiceQuantityMap.clear();
        processingServiceQuantityMap.put(1000, 2);
        processingServiceQuantityMap.put(1001, 5);

        List<MoeGroomingInvoiceApplyPackage> result5 =
                orderService.joinAvailablePackageService(invoiceItems, servicePackageMap, processingServiceQuantityMap);
        assertThat(result5.size()).isEqualTo(1);
        assertThat(result5.get(0).getServiceId()).isEqualTo(item1.getServiceId());
        assertThat(result5.get(0).getQuantity()).isEqualTo(2);
    }

    private MoeGroomingInvoiceItem buildServiceInvoiceItem(Integer serviceId, Integer quantity) {
        MoeGroomingInvoiceItem item = new MoeGroomingInvoiceItem();
        item.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
        item.setServiceId(serviceId);
        item.setQuantity(quantity);
        return item;
    }

    private GroomingPackageServiceInfoDTO buildServicePackage(
            Integer packageId, Integer packageServiceId, Integer remainQuantity) {
        GroomingPackageServiceInfoDTO servicePackage = new GroomingPackageServiceInfoDTO();
        servicePackage.setPackageId(packageId);
        servicePackage.setPackageServiceId(packageServiceId);
        servicePackage.setRemainingQuantity(remainQuantity);
        return servicePackage;
    }

    /**
     * {@link OrderService#sortPackageServicesByExpirationDate(List, Map)}
     */
    @Test
    void testSortPackageServicesByExpirationDate() {

        // Mock
        var pkg1 = new MoeGroomingPackage();
        pkg1.setId(1);
        pkg1.setExpirationDate("2023-12-01");
        var pkg3 = new MoeGroomingPackage();
        pkg3.setId(3);
        pkg3.setExpirationDate("2023-12-03");

        var ps1 = new MoeGroomingPackageService();
        ps1.setId(1);
        ps1.setPackageId(1);
        var ps2 = new MoeGroomingPackageService();
        ps2.setId(2);
        ps2.setPackageId(2); // no package, should be last
        var ps3 = new MoeGroomingPackageService();
        ps3.setId(3);
        ps3.setPackageId(3);

        var packageServices = Arrays.asList(ps1, ps2, ps3);
        var packageMap = Map.of(pkg1.getId(), pkg1, pkg3.getId(), pkg3);

        // Act
        var result = OrderService.sortPackageServicesByExpirationDate(packageServices, packageMap);

        // Assert
        assertThat(result).containsExactly(ps1, ps3, ps2);
    }

    /**
     * {@link OrderService#applyPackage(Integer, Integer, Integer, Boolean)}
     */
    @Test
    void applyPackage_whenNoAvailablePackage_thenShouldAlsoDeleteAppliedRecord() {
        // Arrange
        when(orderClient.getOrderDetail(any()))
                .thenReturn(OrderDetailModel.newBuilder()
                        .setOrder(OrderModel.newBuilder()
                                .setId(1)
                                .setBusinessId(1)
                                .build())
                        .build());
        when(iCustomerCustomerClient.getCustomerWithDeleted(anyInt())).thenReturn(new MoeBusinessCustomerDTO());
        when(membershipService.getRecommendBenefitUsage(any()))
                .thenReturn(GetRecommendBenefitUsageResponse.getDefaultInstance());

        // Act
        orderService.applyPackage(1, 1, 1, false);

        // Assert
        // 先删除 applied record 再查询可用 package，无论如何都会删除 applied record
        verify(invoiceApplyPackageMapper, times(1)).updateByExampleSelective(any(), any());
    }
}
