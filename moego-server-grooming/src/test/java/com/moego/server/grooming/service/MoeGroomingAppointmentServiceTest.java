package com.moego.server.grooming.service;

import static java.util.Arrays.asList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.StaffTimeslotPetCountDTO;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * {@link MoeGroomingAppointmentService} tester.
 */
@ExtendWith(MockitoExtension.class)
class MoeGroomingAppointmentServiceTest {

    @InjectMocks
    private MoeGroomingAppointmentService moeGroomingAppointmentService;

    @Mock
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Mock
    private MoePetDetailService moePetDetailService;

    @Mock
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Mock
    private IBusinessStaffClient iBusinessStaffClient;

    /**
     * {@link MoeGroomingAppointmentService#getRemainPetCountWithin(Integer, Integer, String, String, boolean)}
     */
    @Test
    void getRemainPetCountWithin() {
        MoeBusinessDto business = new MoeBusinessDto();
        business.setId(100);
        business.setTimezoneName("America/Los_Angeles");
        when(iBusinessBusinessClient.getBusinessInfo(any())).thenReturn(business);

        MoeGroomingAppointment appt1 = new MoeGroomingAppointment();
        appt1.setId(10000);
        appt1.setStatus((byte) 1);
        MoeGroomingAppointment appt2 = new MoeGroomingAppointment();
        appt2.setId(10001);
        appt2.setStatus((byte) 3);
        when(moeGroomingAppointmentMapper.getAppointmentsWithin(any(), any(), any(), any(), any()))
                .thenReturn(asList(appt1, appt2));

        when(iBusinessStaffClient.getOwnerStaffId(1)).thenReturn(666);
        when(iBusinessStaffClient.getOwnerStaffId(2)).thenReturn(888);

        when(moePetDetailService.queryPetDetailByAppointmentIds(any())).thenReturn(generateServices());

        // 当前 business 的 owner staff 为传入 staffId, 返回 business 所有剩余的 pet 数量
        int result = moeGroomingAppointmentService.getRemainPetCountWithin(1, 666, null, "2022-11-23", false);
        assertThat(result).isEqualTo(3); // 一个 appt 里一个 pet 有多个 service，只算一个

        // 当前 business 的 owner staff 不为传入 staffId, 返回该 staff 剩余的 pet 数量
        result = moeGroomingAppointmentService.getRemainPetCountWithin(2, 666, null, "2022-11-23", false);
        assertThat(result).isEqualTo(1);
    }

    private static List<MoeGroomingPetDetail> generateServices() {
        MoeGroomingPetDetail service1 = new MoeGroomingPetDetail();
        service1.setId(1);
        service1.setGroomingId(100);
        service1.setPetId(1);
        service1.setStaffId(666);
        MoeGroomingPetDetail service2 = new MoeGroomingPetDetail();
        service2.setId(2);
        service2.setGroomingId(100);
        service2.setPetId(2);
        service2.setStaffId(888);

        // 一个 appt 里有一个 pet 有多个 service
        MoeGroomingPetDetail service3 = new MoeGroomingPetDetail();
        service3.setId(3);
        service3.setGroomingId(101);
        service3.setPetId(2);
        service3.setStaffId(888);
        MoeGroomingPetDetail service4 = new MoeGroomingPetDetail();
        service4.setId(4);
        service4.setGroomingId(101);
        service4.setPetId(2);
        service4.setStaffId(888);
        return asList(service1, service2, service3, service4);
    }

    @Test
    void getStaffTimeslotPetCountByPetDetails_boundaryTest() {
        // 空列表测试
        List<SmartScheduleGroomingDetailsDTO> emptyList = List.of();
        List<StaffTimeslotPetCountDTO> emptyResult =
                MoeGroomingAppointmentService.getStaffTimeslotPetCountByPetDetails(emptyList);
        assertThat(emptyResult).isEmpty();

        // 无效数据测试
        List<SmartScheduleGroomingDetailsDTO> invalidList = new ArrayList<>();

        // PetId 为 0 的数据应被过滤
        SmartScheduleGroomingDetailsDTO invalidPetId = new SmartScheduleGroomingDetailsDTO();
        invalidPetId.setPetId(0);
        invalidPetId.setStartTime(600L);
        invalidPetId.setEndTime(660L);
        invalidPetId.setStaffId(100);
        invalidPetId.setStartDate("2023-10-01");
        invalidPetId.setGroomingId(1001);
        invalidList.add(invalidPetId);

        // StartTime 为 null 的数据应被过滤
        SmartScheduleGroomingDetailsDTO invalidStartTime = new SmartScheduleGroomingDetailsDTO();
        invalidStartTime.setPetId(1);
        invalidStartTime.setStartTime(null);
        invalidStartTime.setEndTime(660L);
        invalidStartTime.setStaffId(100);
        invalidStartTime.setStartDate("2023-10-01");
        invalidStartTime.setGroomingId(1001);
        invalidList.add(invalidStartTime);

        // EndTime 为 null 的数据应被过滤
        SmartScheduleGroomingDetailsDTO invalidEndTime = new SmartScheduleGroomingDetailsDTO();
        invalidEndTime.setPetId(1);
        invalidEndTime.setStartTime(600L);
        invalidEndTime.setEndTime(null);
        invalidEndTime.setStaffId(100);
        invalidEndTime.setStartDate("2023-10-01");
        invalidEndTime.setGroomingId(1001);
        invalidList.add(invalidEndTime);

        // 有效数据不应被过滤
        SmartScheduleGroomingDetailsDTO validDetail = new SmartScheduleGroomingDetailsDTO();
        validDetail.setPetId(1);
        validDetail.setStartTime(600L);
        validDetail.setEndTime(660L);
        validDetail.setStaffId(100);
        validDetail.setStartDate("2023-10-01");
        validDetail.setGroomingId(1001);
        invalidList.add(validDetail);

        List<StaffTimeslotPetCountDTO> invalidResult =
                MoeGroomingAppointmentService.getStaffTimeslotPetCountByPetDetails(invalidList);

        // 只有1个有效数据
        assertThat(invalidResult).hasSize(1);
        assertThat(invalidResult.get(0).getPetCount()).isEqualTo(1);
        assertThat(invalidResult.get(0).getAppointmentCount()).isEqualTo(1);
        assertThat(invalidResult.get(0).getPetDetails()).hasSize(1);
    }

    @Test
    void getStaffTimeslotPetCountByPetDetails_complexScenario() {
        // 创建测试数据
        List<SmartScheduleGroomingDetailsDTO> petDetails = new ArrayList<>();

        // 添加所有测试场景的详情数据
        // 场景1: 预约1, pet1, 连续pet_detail
        SmartScheduleGroomingDetailsDTO petDetail1 = createPetDetail(1001, 1, 100, "2023-10-01", 600L, 660L);
        SmartScheduleGroomingDetailsDTO petDetail2 = createPetDetail(1001, 1, 100, "2023-10-01", 660L, 720L);
        petDetails.add(petDetail1);
        petDetails.add(petDetail2);

        // 场景2: 预约1, pet2, 不连续pet_detail
        SmartScheduleGroomingDetailsDTO petDetail3 = createPetDetail(1001, 2, 100, "2023-10-01", 600L, 660L);
        SmartScheduleGroomingDetailsDTO petDetail4 = createPetDetail(1001, 2, 100, "2023-10-01", 780L, 840L);
        petDetails.add(petDetail3);
        petDetails.add(petDetail4);

        // 场景3: 预约2, pet3
        SmartScheduleGroomingDetailsDTO petDetail5 = createPetDetail(1002, 3, 101, "2023-10-01", 600L, 660L);
        SmartScheduleGroomingDetailsDTO petDetail6 = createPetDetail(1002, 3, 101, "2023-10-01", 660L, 720L);
        SmartScheduleGroomingDetailsDTO petDetail7 = createPetDetail(1002, 3, 100, "2023-10-01", 660L, 720L);
        petDetails.add(petDetail5);
        petDetails.add(petDetail6);
        petDetails.add(petDetail7);
        // 场景4: 跨天pet_detail, 预约3, pet4
        SmartScheduleGroomingDetailsDTO petDetail8 = createPetDetail(1003, 4, 102, "2023-10-01", 600L, 660L);
        SmartScheduleGroomingDetailsDTO petDetail9 = createPetDetail(1003, 4, 102, "2023-10-02", 660L, 720L);
        petDetails.add(petDetail8);
        petDetails.add(petDetail9);

        // 场景5: 同一预约由不同staff处理的pet
        SmartScheduleGroomingDetailsDTO petDetail10 = createPetDetail(1001, 5, 103, "2023-10-01", 600L, 660L);
        petDetails.add(petDetail10);

        // 场景6: 同一预约同 staff，不同 pet
        SmartScheduleGroomingDetailsDTO petDetail11 = createPetDetail(1004, 6, 100, "2023-10-01", 600L, 660L);
        SmartScheduleGroomingDetailsDTO petDetail12 = createPetDetail(1004, 7, 100, "2023-10-01", 600L, 660L);
        petDetails.add(petDetail11);
        petDetails.add(petDetail12);

        // 构建预期结果集
        List<StaffTimeslotPetCountDTO> expectedResults = Arrays.asList(
                // 1: 2023-10-01, 600, staffId=100
                createExpectedResult(
                        100, "2023-10-01", 600, 4, 2, petDetail12, petDetail11, petDetail1, petDetail2, petDetail3),

                // 2: 2023-10-01, 780, staffId=100
                createExpectedResult(100, "2023-10-01", 780, 1, 1, petDetail4),

                // 3: 2023-10-01, 600, staffId=101
                createExpectedResult(101, "2023-10-01", 600, 1, 1, petDetail5, petDetail6),

                // 1: 2023-10-01, 660, staffId=100
                createExpectedResult(100, "2023-10-01", 660, 1, 1, petDetail7),

                // 4: 2023-10-01, 600, staffId=102
                createExpectedResult(102, "2023-10-01", 600, 1, 1, petDetail8),

                // 5: 2023-10-02, 660, staffId=102
                createExpectedResult(102, "2023-10-02", 660, 1, 1, petDetail9),

                // 6: 2023-10-01, 600, staffId=103
                createExpectedResult(103, "2023-10-01", 600, 1, 1, petDetail10));

        // 执行测试
        List<StaffTimeslotPetCountDTO> actualResults =
                MoeGroomingAppointmentService.getStaffTimeslotPetCountByPetDetails(petDetails);

        // 直接比较整个集合（忽略顺序）
        assertThat(actualResults)
                .usingRecursiveFieldByFieldElementComparator()
                .containsExactlyInAnyOrderElementsOf(expectedResults);
    }

    /**
     * 创建宠物预约详情对象
     */
    private SmartScheduleGroomingDetailsDTO createPetDetail(
            int groomingId, int petId, int staffId, String startDate, Long startTime, Long endTime) {
        SmartScheduleGroomingDetailsDTO detail = new SmartScheduleGroomingDetailsDTO();
        detail.setGroomingId(groomingId);
        detail.setPetId(petId);
        detail.setStaffId(staffId);
        detail.setStartDate(startDate);
        detail.setStartTime(startTime);
        detail.setEndTime(endTime);
        return detail;
    }

    /**
     * 创建预期结果对象
     */
    private StaffTimeslotPetCountDTO createExpectedResult(
            int staffId,
            String slotDate,
            int slotTime,
            int petCount,
            int appointmentCount,
            SmartScheduleGroomingDetailsDTO... petDetails) {
        StaffTimeslotPetCountDTO result = new StaffTimeslotPetCountDTO();
        result.setSlotDate(slotDate);
        result.setSlotTime(slotTime);
        result.setStaffId(staffId);
        result.setPetCount(petCount);
        result.setAppointmentCount(appointmentCount);
        result.setPetDetails(Arrays.asList(petDetails));
        return result;
    }
}
