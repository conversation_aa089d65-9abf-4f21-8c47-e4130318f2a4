package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.moego.server.grooming.mapper.AutoAssignMapper;
import com.moego.server.grooming.mapperbean.AutoAssign;
import com.moego.server.grooming.mapperbean.AutoAssignExample;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AutoAssignServiceTest {

    @Mock
    private AutoAssignMapper autoAssignMapper;

    @InjectMocks
    private AutoAssignService autoAssignService;

    private AutoAssign sampleAutoAssign;

    @BeforeEach
    void setUp() {
        sampleAutoAssign = new AutoAssign();
        sampleAutoAssign.setId(1);
        sampleAutoAssign.setAppointmentId(100);
    }

    @Test
    void getAutoAssign_WhenFound_ReturnsAutoAssign() {
        // Arrange
        when(autoAssignMapper.selectByExample(any(AutoAssignExample.class))).thenReturn(List.of(sampleAutoAssign));

        // Act
        AutoAssign result = autoAssignService.getAutoAssign(100);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1);
        assertThat(result.getAppointmentId()).isEqualTo(100);
    }

    @Test
    void getAutoAssign_WhenNotFound_ReturnsNull() {
        // Arrange
        when(autoAssignMapper.selectByExample(any(AutoAssignExample.class))).thenReturn(List.of());

        // Act
        AutoAssign result = autoAssignService.getAutoAssign(999);

        // Assert
        assertThat(result).isNull();
    }

    @Test
    void listAutoAssign_WithValidIds_ReturnsAutoAssigns() {
        // Arrange
        List<Integer> appointmentIds = List.of(100, 101);
        List<AutoAssign> expectedAutoAssigns = List.of(sampleAutoAssign);
        when(autoAssignMapper.selectByExample(any(AutoAssignExample.class))).thenReturn(expectedAutoAssigns);

        // Act
        List<AutoAssign> result = autoAssignService.listAutoAssign(appointmentIds);

        // Assert
        assertThat(result).isNotEmpty();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getId()).isEqualTo(1);
    }

    @Test
    void listAutoAssign_WithEmptyIds_ReturnsEmptyList() {
        // Act
        List<AutoAssign> result = autoAssignService.listAutoAssign(List.of());

        // Assert
        assertThat(result).isEmpty();
    }
}
