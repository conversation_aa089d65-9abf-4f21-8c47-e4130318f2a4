package com.moego.server.grooming.service.utils;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;

class ServiceUtilTest {

    @Test
    void getCustomizedService_shouldReturnServiceWhenAllConditionsMatch() {
        // Arrange
        Long petId = 101L;
        Long serviceId = 1L;
        Long staffId = 201L;
        var service = createService(serviceId, petId, staffId, "Matched Service");
        var services = List.of(service);

        // Act
        var result =
                ServiceUtil.getCustomizedService(services, petId.intValue(), serviceId.intValue(), staffId.intValue());

        // Assert
        var expected = service.getCustomizedService();
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getCustomizedService_shouldReturnNullWhenServiceIdDoesNotMatch() {
        // Arrange
        Long petId = 101L;
        Long serviceId = 1L;
        Long staffId = 201L;
        var service = createService(serviceId, petId, staffId, "Matched with Nulls");
        var serviceList = List.of(service);

        // Act
        var result = ServiceUtil.getCustomizedService(serviceList, petId.intValue(), 2, staffId.intValue());

        // Assert
        assertThat(result).isNull();
    }

    @Test
    void getCustomizedService_shouldReturnNullWhenPetIdDoesNotMatch() {
        // Arrange
        Long petId = 101L;
        Long serviceId = 1L;
        Long staffId = 201L;
        var service = createService(serviceId, petId, staffId, "Matched with Nulls");
        var serviceList = List.of(service);

        // Act
        var result = ServiceUtil.getCustomizedService(serviceList, 102, serviceId.intValue(), staffId.intValue());

        // Assert
        assertThat(result).isNull();
    }

    @Test
    void getCustomizedService_shouldReturnNullWhenStaffIdDoesNotMatch() {
        // Arrange
        Long petId = 101L;
        Long serviceId = 1L;
        Long staffId = 201L;
        var service = createService(serviceId, petId, staffId, "Matched with Nulls");
        var serviceList = List.of(service);

        // Act
        var result = ServiceUtil.getCustomizedService(serviceList, petId.intValue(), serviceId.intValue(), 202);

        // Assert
        assertThat(result).isNull();
    }

    @Test
    void getCustomizedService_shouldReturnServiceWhenPetIdAndStaffIdAreNullAndConditionsMatch() {
        // Arrange
        Long serviceId = 1L;
        var service = createService(serviceId, null, null, "Matched service");
        var serviceList = List.of(service);

        // Act
        var result = ServiceUtil.getCustomizedService(serviceList, null, serviceId.intValue(), null);

        // Assert
        var expected = service.getCustomizedService();
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getCustomizedService_shouldReturnNullWhenServiceListIsEmpty() {
        // Arrange
        var serviceList = new ArrayList<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo>();
        Integer petId = 101;
        Integer serviceId = 1;
        Integer staffId = 201;

        // Act
        var result = ServiceUtil.getCustomizedService(serviceList, petId, serviceId, staffId);

        // Assert
        assertThat(result).isNull();
    }

    private BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo createService(
            Long serviceId, Long petId, Long staffId, String serviceName) {
        var condition = CustomizedServiceQueryCondition.newBuilder().setServiceId(serviceId);
        if (petId != null) {
            condition.setPetId(petId);
        }
        if (staffId != null) {
            condition.setStaffId(staffId);
        }
        return BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo.newBuilder()
                .setQueryCondition(condition)
                .setCustomizedService(CustomizedServiceView.newBuilder()
                        .setId(serviceId)
                        .setName(serviceName)
                        .build())
                .build();
    }
}
