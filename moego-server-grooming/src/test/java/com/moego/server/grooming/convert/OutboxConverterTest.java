package com.moego.server.grooming.convert;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.event_bus.v1.AppointmentCanceledEvent;
import com.moego.idl.models.event_bus.v1.EventData;
import com.moego.idl.models.event_bus.v1.EventType;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.lib.event_bus.event.EventRecord;
import com.moego.server.grooming.mapperbean.AppointmentOutbox;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import org.junit.jupiter.api.Test;

class OutboxConverterTest {

    @Test
    void toAppointmentOutbox() {
        var detail = EventData.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(1L).setBusinessId(2).build())
                .setAppointmentCanceledEvent(AppointmentCanceledEvent.newBuilder()
                        .setId(111)
                        .setCancelledType(AppointmentCanceledEvent.CancelledType.CANCELLED_BY_SYSTEM)
                        .setCancelledReason("")
                        .setOperatorId(123)
                        .addAllServiceItemTypes(List.of(ServiceItemType.BOARDING, ServiceItemType.DAYCARE))
                        .setCustomerId(456)
                        .build())
                .build();

        var eventRecord = EventRecord.<EventData>builder()
                .id("event123")
                .detail(detail)
                .key("key1")
                .time(LocalDateTime.of(2023, 1, 1, 12, 0, 0)
                        .atZone(ZoneId.systemDefault())
                        .toInstant())
                .type(EventType.APPOINTMENT_CANCELED)
                .build();

        var result = OutboxConverter.INSTANCE.toAppointmentOutbox("topicName", eventRecord);
        //        System.out.println(result.getEventDetail());

        var expect = new AppointmentOutbox();
        expect.setTopic("topicName");
        expect.setEventId("event123");
        expect.setEventType(EventType.APPOINTMENT_CANCELED);
        expect.setEventKey("key1");
        expect.setEventTime(LocalDateTime.of(2023, 1, 1, 12, 0, 0));
        expect.setEventDetail(
                "{\"tenant\":{\"companyId\":\"1\",\"businessId\":\"2\"},\"appointmentCanceledEvent\":{\"id\":\"111\",\"cancelledType\":\"CANCELLED_BY_SYSTEM\",\"cancelledReason\":\"\",\"operatorId\":\"123\",\"serviceItemTypes\":[\"BOARDING\",\"DAYCARE\"],\"customerId\":\"456\",\"autoRefundOrder\":false,\"refundDeposit\":false}}");

        assertThat(result).usingRecursiveComparison().isEqualTo(expect);
    }
}
