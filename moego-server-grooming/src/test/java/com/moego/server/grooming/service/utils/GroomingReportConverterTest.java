package com.moego.server.grooming.service.utils;

import static com.moego.common.enums.groomingreport.GroomingReportCategoryEnum.CATEGORY_CUSTOMIZED_FEEDBACK;
import static org.assertj.core.api.Assertions.assertThat;

import com.moego.common.enums.groomingreport.GroomingReportQuestionTypeEnum;
import com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;

class GroomingReportConverterTest {

    /**
     * 测试 initChoiceQuestionChoice 方法的所有分支
     * 这是一个公开的静态方法，可以直接调用
     */
    @Test
    void initChoiceQuestionChoice_shouldInitializeChoicesWhenNull() {
        // Arrange
        GroomingReportInfoDTO.GroomingReportQuestion question = new GroomingReportInfoDTO.GroomingReportQuestion();
        question.setType(GroomingReportQuestionTypeEnum.single_choice.name());
        question.setCategory((byte) 1); // CATEGORY_FEEDBACK
        question.setOptions(List.of("Option1", "Option2"));
        question.setChoices(null); // choices 为 null

        // Act
        GroomingReportConverter.initChoiceQuestionChoice(question);

        // Assert
        assertThat(question.getChoices()).isNotNull();
        assertThat(question.getChoices()).hasSize(1);
        assertThat(question.getChoices()).contains("Option1");
    }

    @Test
    void initChoiceQuestionChoice_shouldNotModifyNonChoiceQuestion() {
        // Arrange
        GroomingReportInfoDTO.GroomingReportQuestion question = new GroomingReportInfoDTO.GroomingReportQuestion();
        question.setType(GroomingReportQuestionTypeEnum.text_input.name());
        question.setCategory((byte) 1);
        question.setOptions(List.of("Option1", "Option2"));
        question.setChoices(null);

        // Act
        GroomingReportConverter.initChoiceQuestionChoice(question);

        // Assert
        assertThat(question.getChoices()).isNotNull();
        assertThat(question.getChoices()).isEmpty(); // 只初始化空数组，不添加选项
    }

    @Test
    void initChoiceQuestionChoice_shouldNotModifyCustomizedFeedbackQuestion() {
        // Arrange
        GroomingReportInfoDTO.GroomingReportQuestion question = new GroomingReportInfoDTO.GroomingReportQuestion();
        question.setType(GroomingReportQuestionTypeEnum.single_choice.name());
        question.setCategory(CATEGORY_CUSTOMIZED_FEEDBACK.getType());
        question.setOptions(List.of("Option1", "Option2"));
        question.setChoices(null);

        // Act
        GroomingReportConverter.initChoiceQuestionChoice(question);

        // Assert
        assertThat(question.getChoices()).isNotNull();
        assertThat(question.getChoices()).isEmpty(); // 只初始化空数组，不添加选项
    }

    @Test
    void initChoiceQuestionChoice_shouldNotAddChoiceWhenChoicesNotEmpty() {
        // Arrange
        GroomingReportInfoDTO.GroomingReportQuestion question = new GroomingReportInfoDTO.GroomingReportQuestion();
        question.setType(GroomingReportQuestionTypeEnum.single_choice.name());
        question.setCategory((byte) 1);
        question.setOptions(List.of("Option1", "Option2"));
        question.setChoices(new ArrayList<>(List.of("Option2")));

        // Act
        GroomingReportConverter.initChoiceQuestionChoice(question);

        // Assert
        assertThat(question.getChoices()).hasSize(1);
        assertThat(question.getChoices()).contains("Option2");
        assertThat(question.getChoices()).doesNotContain("Option1");
    }

    @Test
    void initChoiceQuestionChoice_shouldNotAddChoiceWhenCustomOptionsExist() {
        // Arrange
        GroomingReportInfoDTO.GroomingReportQuestion question = new GroomingReportInfoDTO.GroomingReportQuestion();
        question.setType(GroomingReportQuestionTypeEnum.single_choice.name());
        question.setCategory((byte) 1);
        question.setOptions(List.of("Option1", "Option2"));
        question.setChoices(new ArrayList<>());
        question.setCustomOptions(List.of("CustomOption"));

        // Act
        GroomingReportConverter.initChoiceQuestionChoice(question);

        // Assert
        assertThat(question.getChoices()).isEmpty(); // 有 customOptions 时不自动添加选项
    }

    @Test
    void initChoiceQuestionChoice_shouldNotAddChoiceWhenOptionsEmpty() {
        // Arrange
        GroomingReportInfoDTO.GroomingReportQuestion question = new GroomingReportInfoDTO.GroomingReportQuestion();
        question.setType(GroomingReportQuestionTypeEnum.single_choice.name());
        question.setCategory((byte) 1);
        question.setOptions(new ArrayList<>());
        question.setChoices(new ArrayList<>());

        // Act
        GroomingReportConverter.initChoiceQuestionChoice(question);

        // Assert
        assertThat(question.getChoices()).isEmpty();
    }

    @Test
    void initChoiceQuestionChoice_shouldRemoveInvalidChoices() {
        // Arrange
        GroomingReportInfoDTO.GroomingReportQuestion question = new GroomingReportInfoDTO.GroomingReportQuestion();
        question.setType(GroomingReportQuestionTypeEnum.multi_choice.name());
        question.setCategory((byte) 1);
        question.setOptions(List.of("Option1", "Option2"));
        question.setChoices(new ArrayList<>(List.of("Option1", "InvalidOption", "Option2")));

        // Act
        GroomingReportConverter.initChoiceQuestionChoice(question);

        // Assert
        assertThat(question.getChoices()).hasSize(2);
        assertThat(question.getChoices()).containsExactlyInAnyOrder("Option1", "Option2");
        assertThat(question.getChoices()).doesNotContain("InvalidOption");
    }

    @Test
    void initChoiceQuestion_shouldKeepValidChoicesWithCustomOptions() throws Exception {
        // Arrange
        GroomingReportInfoDTO.GroomingReportQuestion question = new GroomingReportInfoDTO.GroomingReportQuestion();
        question.setType(GroomingReportQuestionTypeEnum.multi_choice.name());
        question.setCategory((byte) 1);
        question.setOptions(List.of("Option1", "Option2"));
        question.setCustomOptions(List.of("CustomOption1", "CustomOption2"));
        question.setChoices(new ArrayList<>(List.of("Option1", "CustomOption1", "InvalidOption")));

        // Act
        GroomingReportConverter.initChoiceQuestionChoice(question);

        // Assert
        assertThat(question.getChoices()).hasSize(2);
        assertThat(question.getChoices()).containsExactlyInAnyOrder("Option1", "CustomOption1");
        assertThat(question.getChoices()).doesNotContain("InvalidOption");
    }

    @Test
    void initChoiceQuestion_shouldHandleTagChoiceType() throws Exception {
        // Arrange
        GroomingReportInfoDTO.GroomingReportQuestion question = new GroomingReportInfoDTO.GroomingReportQuestion();
        question.setType(GroomingReportQuestionTypeEnum.tag_choice.name());
        question.setCategory((byte) 1);
        question.setOptions(List.of("Tag1", "Tag2"));
        question.setChoices(null);

        // Act
        GroomingReportConverter.initChoiceQuestionChoice(question);

        // Assert
        assertThat(question.getChoices()).isNotNull();
        assertThat(question.getChoices()).hasSize(1);
        assertThat(question.getChoices()).contains("Tag1");
    }

    @Test
    void initChoiceQuestion_shouldHandleNullOptions() throws Exception {
        // Arrange
        GroomingReportInfoDTO.GroomingReportQuestion question = new GroomingReportInfoDTO.GroomingReportQuestion();
        question.setType(GroomingReportQuestionTypeEnum.single_choice.name());
        question.setCategory((byte) 1);
        question.setOptions(null);
        question.setChoices(null);

        // Act
        GroomingReportConverter.initChoiceQuestionChoice(question);

        // Assert
        assertThat(question.getChoices()).isNotNull();
        assertThat(question.getChoices()).isEmpty();
    }
}
