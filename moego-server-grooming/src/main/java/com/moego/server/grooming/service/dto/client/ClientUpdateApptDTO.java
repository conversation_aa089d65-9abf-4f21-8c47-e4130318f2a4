package com.moego.server.grooming.service.dto.client;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/9
 */
@Data
@Accessors(chain = true)
public class ClientUpdateApptDTO {

    @NotEmpty
    @Schema(description = "Ticket unique identifier")
    private String bookingId;

    @NotEmpty
    @Pattern(message = "Invalid date format, valid example: 2020-02-08", regexp = "^(\\d{4}-\\d{2}-\\d{2})?$")
    @Schema(description = "Appointment date")
    private String apptDate;

    @NotNull
    @Max(1440)
    @Schema(description = "Appointment start time")
    private Integer apptStartTime;

    @NotNull
    @Schema(description = "Staff id")
    private Integer staffId;

    @JsonIgnore
    @Hidden
    List<BaseBusinessCustomerIdDTO> customerIdDTOList;

    @JsonIgnore
    @Hidden
    private Integer clientId;
}
