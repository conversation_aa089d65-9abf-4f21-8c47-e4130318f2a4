package com.moego.server.grooming.service.dto;

import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ReportWebApptPetDetail {

    private Integer id;
    private Integer groomingId;
    private Integer petId;
    private Integer staffId;
    private Integer serviceId;
    private String serviceName;
    private Integer serviceType;
    private Integer serviceTime;
    private BigDecimal servicePrice;
    private Long startTime;
    private Long endTime;
    private Integer status;
    private Long updateTime;
    private Integer taxId;
    private List<GroomingServiceOperationDTO> operationList = new ArrayList<>();

    // 内部计算用。目前不用对外暴露
    private String startDate;
    private String endDate;
    private Integer priceUnit;
    private String specificDates;
    private Long associatedServiceId;
    private Integer serviceItemType;
    private Integer quantityPerDay;
    // MoeGroomingPetDetail 的其它字段
    private Integer scopeTypePrice;
    private Integer scopeTypeTime;
    private Integer starStaffId;
    private Integer packageServiceId;
    private Boolean enableOperation;
    private Integer workMode;
    private String serviceColorCode;
    private Long lodgingId;
    private ServiceOverrideType priceOverrideType;
    private ServiceOverrideType durationOverrideType;
    private Date createdAt;
    private Date updatedAt;
    private Integer dateType;
    private BigDecimal totalPrice;
    private Integer quantity;
    private Long orderLineItemId;
}
