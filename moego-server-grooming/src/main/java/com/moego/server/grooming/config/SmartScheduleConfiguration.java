package com.moego.server.grooming.config;

import com.moego.lib.common.util.ThreadPoolUtil;
import java.time.Duration;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/5/16
 */
@Configuration(proxyBeanMethods = false)
@RequiredArgsConstructor
public class SmartScheduleConfiguration {

    private final SmartScheduleProperties smartScheduleProperties;

    public static final String SMART_SCHEDULE_EXECUTOR_SERVICE = "smartScheduleExecutorService";

    @Bean(name = SMART_SCHEDULE_EXECUTOR_SERVICE)
    public ExecutorService smartScheduleExecutorService() {
        return ThreadPoolUtil.newExecutorService(
                smartScheduleProperties.getCorePoolSize(),
                smartScheduleProperties.getMaximumPoolSize(),
                Duration.ofSeconds(smartScheduleProperties.getAliveTimeSeconds()),
                smartScheduleProperties.getQueueCapacity(),
                "moego-smart-schedule-",
                new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
