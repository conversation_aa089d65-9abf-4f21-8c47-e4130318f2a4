package com.moego.server.grooming.server;

import com.moego.common.utils.DateUtil;
import com.moego.server.grooming.api.IQuickBooksServiceBase;
import com.moego.server.grooming.dto.quickbooks.ListQuickBookInvoiceDTO;
import com.moego.server.grooming.params.SyncAppointmentParams;
import com.moego.server.grooming.params.quickbook.ListQuickBookInvoiceParams;
import com.moego.server.grooming.service.QuickBooksService;
import com.moego.server.grooming.service.QuickBooksSyncService;
import com.moego.server.payment.dto.ListQuickBookSettingDTO;
import com.moego.server.payment.params.ListQuickBookSettingParams;
import com.moego.server.payment.params.UpdateQuickBookSettingParams;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class QuickBooksServer extends IQuickBooksServiceBase {

    @Autowired
    QuickBooksService quickBooksService;

    @Autowired
    QuickBooksSyncService quickBooksSyncService;

    @Override
    public boolean syncGroomingAppointment(Integer groomingId) {
        return quickBooksSyncService.testSyncInvoiceByGroomingId(groomingId);
    }

    @Override
    public String syncTaskBegin() {
        List<String> taskResult = quickBooksService.beginTask();
        StringBuilder stringBuilder = new StringBuilder();
        for (String result : taskResult) {
            stringBuilder.append(result).append("\n");
        }
        return stringBuilder.toString();
    }

    @Override
    public void invoiceCheckQbSyncStatus(Integer tokenBusinessId, @RequestParam("invoiceId") Integer invoiceId) {
        quickBooksService.addSyncJobByInvoiceId(tokenBusinessId, invoiceId);
    }

    @Override
    public Boolean addRedisSyncGroomingData(SyncAppointmentParams params) {
        quickBooksService.addRedisSyncGroomingData(
                params.getBusinessId(), params.getAppointmentId(), params.getAppointmentDate());
        return Boolean.TRUE;
    }

    @Override
    public Long addNeedSyncPaymentToQB(Integer businessId, Integer paymentId) {
        return quickBooksService.addRedisNeedSyncPaymentToListRight(businessId, paymentId);
    }

    @Override
    public ListQuickBookSettingDTO listQuickBookSetting(ListQuickBookSettingParams params) {
        return quickBooksService.listQuickBookSetting(params);
    }

    @Override
    public Integer updateQuickBookSetting(UpdateQuickBookSettingParams params) {
        return quickBooksService.updateQuickBookSetting(params);
    }

    @Override
    public Integer compensationTimeRange(Integer businessId, Long startTime, Long endTime) {
        return quickBooksService.compensationByDateRange(businessId, startTime, endTime);
    }

    @Override
    public List<String> compensationInvoice(Integer businessId, List<Integer> invoiceIds) {
        if (invoiceIds == null || invoiceIds.isEmpty()) return null;
        final String SUCCESS = "success";
        return invoiceIds.stream()
                .map(id -> {
                    String sync =
                            quickBooksSyncService.syncInvoiceByInvoiceId(businessId, id, DateUtil.get10Timestamp());
                    return SUCCESS.equals(sync)
                            ?
                            // 成功
                            String.format("%s sync %s", id, SUCCESS)
                            :
                            // 失败
                            String.format("%s sync failed, reason: %s", id, sync);
                })
                .toList();
    }

    @Override
    public ListQuickBookInvoiceDTO listQuickBookInvoice(ListQuickBookInvoiceParams params) {
        return quickBooksSyncService.listQuickBookInvoice(params);
    }
}
