package com.moego.server.grooming.web;

import com.moego.common.response.ResponseResult;
import com.moego.common.utils.PermissionUtil;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.server.grooming.dto.AppointmentWithPetDetailsDto;
import com.moego.server.grooming.dto.BusinessUpcomingDTO;
import com.moego.server.grooming.dto.CustomerUpcomingBusinessDTO;
import com.moego.server.grooming.dto.GroomingTicketWindowDetailDTO;
import com.moego.server.grooming.dto.PetLastServiceDTO;
import com.moego.server.grooming.service.DrivingInfoService;
import com.moego.server.grooming.service.MoeAppointmentQueryService;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.server.grooming.service.dto.BatchDrivingDisplayInfo;
import com.moego.server.grooming.service.dto.DrivingDisplayInfo;
import com.moego.server.grooming.web.params.BatchDrivingInfoParams;
import java.time.LocalDate;
import java.util.List;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grooming")
@Slf4j
@RequiredArgsConstructor
public class MoeGroomingAppointmentQueryController {

    private final MoeAppointmentQueryService appointmentQueryService;
    private final MoeGroomingAppointmentService appointmentService;
    private final DrivingInfoService drivingInfoService;
    private final MigrateHelper migrateHelper;

    /**
     * 窗口预约详情
     *
     * @param context
     * @param id
     * @return
     */
    @GetMapping("/appointment/detail")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<GroomingTicketWindowDetailDTO> queryTicketDetailWithWindow(
            AuthContext context, @RequestParam Integer id) {
        return appointmentQueryService.queryTicketDetailWithWindow(
                context.companyId(), context.getBusinessId(), id, migrateHelper.isMigrate(context));
    }

    /**
     * 获取发送给用户的 upcoming url
     * DONE(Frank): customerId->businessId
     * AuthType应该是为business
     *
     * @param customerId
     * @return
     */
    @GetMapping("/appointment/customer/upcoming/url")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<String> getCustomerUpComingUrl(AuthContext context, @RequestParam Integer customerId) {
        appointmentService.checkBusinessCustomer(context.getBusinessId(), customerId);
        return appointmentQueryService.getCustomerUpComingUrl(customerId);
    }

    @PostMapping("/appointment/business/upcoming")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<BusinessUpcomingDTO> queryBusinessUpComingAppoint(AuthContext context, String date) {
        return appointmentQueryService.queryBusinessUpComingAppoint(context.getBusinessId(), date);
    }

    /**
     * DONE(account structure): 迁移后的用户查询 company 维度的 last service
     */
    @GetMapping("/pet/last/service")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<PetLastServiceDTO> getPetLastService(AuthContext context, @RequestParam Integer petId) {
        var companyId = context.companyId();
        var businessId = context.getBusinessId();
        var migrated = migrateHelper.isMigrate(context);
        return appointmentQueryService.getPetLastService(migrated, companyId, businessId, petId);
    }

    /**
     * customer端 查询customer upcoming 预约
     *
     * @param id
     * @return
     */
    @GetMapping("/appointment/customer/upcoming")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<CustomerUpcomingBusinessDTO> queryCustomerUpComingAppoint(@RequestParam String id) {
        var result = appointmentQueryService.queryCustomerUpComingAppointForClientShare(id);

        result.getData().getUpComingAppoint().forEach(dto -> {
            dto.setClientPhoneNumber(PermissionUtil.phoneNumberConfusion(dto.getClientPhoneNumber()));
            dto.setEmail(PermissionUtil.emailConfusion(dto.getEmail()));
        });

        return result;
    }

    @GetMapping("/appointment")
    @Auth(AuthType.BUSINESS)
    public AppointmentWithPetDetailsDto getAppointmentWithPetDetails(AuthContext context, Integer appointmentId) {
        AppointmentWithPetDetailsDto appointment =
                appointmentQueryService.getAppointmentWithPetDetails(appointmentId, false);
        if (appointment != null && context.getBusinessId().equals(appointment.getBusinessId())) {
            return appointment;
        }

        return null;
    }

    @GetMapping("/appointment/driving/info")
    @Auth(AuthType.BUSINESS)
    public List<DrivingDisplayInfo> getDrivingInfo(
            AuthContext context,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam Integer staffId) {
        return drivingInfoService.getDrivingInfo(date, staffId, context.getBusinessId());
    }

    @PostMapping("appointment/driving/info/batch")
    @Auth(AuthType.BUSINESS)
    public BatchDrivingDisplayInfo getDrivingInfoBatch(@Valid @RequestBody BatchDrivingInfoParams params) {
        params =
                params.toBuilder().businessId(AuthContext.get().getBusinessId()).build();
        return drivingInfoService.getDrivingInfoBatch(params);
    }
}
