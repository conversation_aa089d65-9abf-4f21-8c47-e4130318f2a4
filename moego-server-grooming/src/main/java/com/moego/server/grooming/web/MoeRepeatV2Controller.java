package com.moego.server.grooming.web;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.dto.AddResultDTO;
import com.moego.server.grooming.dto.ConflictInfoDTO;
import com.moego.server.grooming.dto.MoeRepeatInfoDTO;
import com.moego.server.grooming.dto.RepeatAppointmentDto;
import com.moego.server.grooming.dto.RepeatPreviewSummaryDTO;
import com.moego.server.grooming.dto.SaveRepeatAppointmentListResultDTO;
import com.moego.server.grooming.params.PreviewRepeatParams;
import com.moego.server.grooming.params.SaveRepeatAppointmentListParams;
import com.moego.server.grooming.params.SaveRepeatParams;
import com.moego.server.grooming.params.appointment.conflict.BatchConflictCheckParams;
import com.moego.server.grooming.service.MoeAppointmentConflictCheckService;
import com.moego.server.grooming.service.MoeAppointmentQueryService;
import com.moego.server.grooming.service.MoeRepeatV2Service;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grooming/repeat/v2")
@RequiredArgsConstructor
public class MoeRepeatV2Controller {

    private final MoeRepeatV2Service moeRepeatV2Service;
    private final MoeAppointmentQueryService appointmentQueryService;
    private final MoeAppointmentConflictCheckService appointmentConflictCheckService;

    /**
     * 创建 repeat rule
     *
     * @param repeatParams 请求参数
     * @return 创建的 repeatId
     */
    @PostMapping("/rule")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.CREATE,
            resourceType = ResourceType.REPEAT_RULE,
            resourceId = "#result.data.id",
            details = "#repeatParams")
    public AddResultDTO addRepeatRule(@Valid @RequestBody SaveRepeatParams repeatParams) {
        repeatParams.setCompanyId(AuthContext.get().getCompanyId());
        repeatParams.setBusinessId(AuthContext.get().getBusinessId());
        repeatParams.setStaffId(AuthContext.get().getStaffId());
        return moeRepeatV2Service.addRepeatRule(repeatParams);
    }

    /**
     * 修改 repeat rule
     *
     * @param editRepeatParams 请求参数
     * @return
     */
    @PutMapping("/rule")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.REPEAT_RULE,
            resourceId = "#editRepeatParams.repeatId",
            details = "#editRepeatParams")
    public Boolean modifyRepeatRule(@Valid @RequestBody SaveRepeatParams editRepeatParams) {
        editRepeatParams.setCompanyId(AuthContext.get().getCompanyId());
        editRepeatParams.setBusinessId(AuthContext.get().getBusinessId());
        editRepeatParams.setStaffId(AuthContext.get().getStaffId());
        return moeRepeatV2Service.modifyRepeatRule(editRepeatParams);
    }

    /**
     * 查询 repeat rule
     *
     * @param repeatId repeat id
     * @return repeat rule record
     */
    @GetMapping("/rule")
    @Auth(AuthType.BUSINESS)
    public MoeRepeatInfoDTO queryRepeatRule(@RequestParam Integer repeatId) {
        return moeRepeatV2Service.queryRepeatRule(AuthContext.get().companyId(), repeatId);
    }

    /**
     * 日期规则生成校验
     *
     * @param repeatParams repeat params
     * @return conflict info list
     */
    @PostMapping("/preview")
    @Auth(AuthType.BUSINESS)
    public RepeatPreviewSummaryDTO previewRepeatDay(@Valid @RequestBody PreviewRepeatParams repeatParams) {
        if (repeatParams == null || CollectionUtils.isEmpty(repeatParams.getRepeatStaffInfoParams())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "staff info is not empty");
        }

        repeatParams.setCompanyId(AuthContext.get().getCompanyId());
        repeatParams.setBusinessId(AuthContext.get().getBusinessId());
        repeatParams.setStaffId(AuthContext.get().getStaffId());
        return moeRepeatV2Service.previewRepeatDay(repeatParams);
    }

    /**
     * 查询 repeat appointment list
     *
     * @param repeatId      要查询的 repeatId
     * @param type          查询类型：1-history，2-upcoming，3-all
     * @param includeFinish 是否包含 finish 的预约
     * @return appointment list
     */
    @GetMapping("/appointment/list")
    @Auth(AuthType.BUSINESS)
    public List<RepeatAppointmentDto> getRepeatAppointmentList(
            @RequestParam Integer repeatId,
            @RequestParam Byte type,
            @RequestParam(required = false) Boolean includeFinish) {
        return appointmentQueryService.getRepeatAppointmentList(
                AuthContext.get().getBusinessId(), repeatId, type, includeFinish);
    }

    /**
     * 批量保存 repeat appointment list
     * 包含新增、更新、删除操作
     *
     * @param params save params
     * @return updated row count
     */
    @PutMapping("/appointment/list")
    @Auth(AuthType.BUSINESS)
    public SaveRepeatAppointmentListResultDTO saveRepeatAppointmentList(
            @Valid @RequestBody SaveRepeatAppointmentListParams params) {
        params.setCompanyId(AuthContext.get().getCompanyId());
        params.setBusinessId(AuthContext.get().getBusinessId());
        params.setStaffId(AuthContext.get().getStaffId());
        if (params.getSaveType() == SaveRepeatAppointmentListParams.SAVED_TYPE_SELECTED_DATES) {
            return moeRepeatV2Service.saveMultipleAppointmentList(params);
        } else {
            return moeRepeatV2Service.saveRepeatAppointmentList(params);
        }
    }

    @PostMapping("/conflict/check")
    @Auth(AuthType.BUSINESS)
    public List<ConflictInfoDTO> checkConflict(@Valid @RequestBody BatchConflictCheckParams params) {
        return appointmentConflictCheckService.checkConflictBatch(
                AuthContext.get().getBusinessId(), params.conflictCheckParamsList());
    }
}
