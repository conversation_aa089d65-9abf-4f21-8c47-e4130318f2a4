package com.moego.server.grooming.service.ob.metrics;

import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineDepositMapper;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import java.math.BigDecimal;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Service
@RequiredArgsConstructor
public class PrepaidRevenueMetricService implements IOBMetricsService {

    private final MoeBookOnlineDepositMapper depositMapper;

    @Override
    public Object sumMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        BigDecimal sum = depositMapper.countPrepaidRevenue(
                timeRangeDTO.businessId(), timeRangeDTO.startTime(), timeRangeDTO.endTime());
        return Objects.nonNull(sum) ? sum : BigDecimal.ZERO;
    }

    @Override
    public Object proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return "";
    }

    @Override
    public OBMetricsEnum getMetricsName() {
        return OBMetricsEnum.prepaid_revenue;
    }
}
