package com.moego.server.grooming.web.vo.ob.component;

import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Storefront amenities component
 *
 * <AUTHOR>
 * @since 2023/2/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AmenitiesComponentVO extends BaseComponentVO {

    @Schema(description = "Amenity, 8 features and 4 payment options")
    private OBLandingPageConfigVO.LandingPageAmenitiesVO amenities;

    @Override
    public String getComponent() {
        return LandingPageComponentEnum.AMENITIES.getComponent();
    }
}
