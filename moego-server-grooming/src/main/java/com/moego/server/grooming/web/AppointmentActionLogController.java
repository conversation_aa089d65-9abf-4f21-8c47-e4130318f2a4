package com.moego.server.grooming.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.dto.appointment.history.ActionHistoryDTO;
import com.moego.server.grooming.service.appointment.actionlog.AppointmentActionLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(path = "/grooming/appointment/actionLog")
@RequiredArgsConstructor
public class AppointmentActionLogController {
    private final AppointmentActionLogService appointmentActionLogService;

    @GetMapping("")
    @Auth(AuthType.BUSINESS)
    public ActionHistoryDTO getActionLog(AuthContext context, @RequestParam Integer appointmentId) {
        return appointmentActionLogService.getActionHistory(context.getBusinessId(), appointmentId);
    }
}
