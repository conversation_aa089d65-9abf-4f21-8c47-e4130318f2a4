package com.moego.server.grooming.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.service.StaffAvailableSyncService;
import com.moego.server.grooming.service.StaffTimeSyncService;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/grooming/timeslot")
@RestController
@RequiredArgsConstructor
@Hidden
public class StaffTimeAvailableSyncController {

    private final StaffAvailableSyncService staffAvailabilityService;
    private final StaffTimeSyncService staffTimeSyncService;

    /**
     * 手动触发的一次性任务，同步数据到新表内
     */
    @GetMapping("/taskSyncAllAvailableRecord")
    @Auth(AuthType.ANONYMOUS)
    public void taskSyncAllAvailableRecord() {
        staffAvailabilityService.taskSyncAllAvailableRecord();
    }

    /**
     * 手动触发的一次性任务，同步单个 bid 数据到新表内
     */
    @GetMapping("/syncOneBid")
    @Auth(AuthType.ANONYMOUS)
    public void syncOneBidAvailable(Integer bid) {
        staffAvailabilityService.syncOneBidAvailable(bid);
    }

    /**
     * 手动触发的一次性任务，同步数据到新表内
     */
    @GetMapping("/taskSyncAllTimeRecord")
    @Auth(AuthType.ANONYMOUS)
    public void taskSyncAllTimeRecord() {
        staffTimeSyncService.taskSyncAllTimeRecord();
    }

    /**
     * 手动触发的一次性任务，同步单个 bid 数据到新表内
     * @param staffTimeId
     */
    @GetMapping("/syncOneStaffTimesRecord")
    @Auth(AuthType.ANONYMOUS)
    public void syncOneStaffTimesRecord(Integer staffTimeId) {
        staffTimeSyncService.syncOneStaffTimesRecord(staffTimeId);
    }
}
