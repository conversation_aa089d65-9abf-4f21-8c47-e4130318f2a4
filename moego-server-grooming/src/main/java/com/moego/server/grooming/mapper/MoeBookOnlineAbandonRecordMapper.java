package com.moego.server.grooming.mapper;

import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.mapper.param.SearchAbandonedRecordParam;
import com.moego.server.grooming.mapper.param.UpdateAbandonedRecordByCustomerIdParam;
import com.moego.server.grooming.mapper.po.CountByAbandonedStepPO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordExample;
import com.moego.server.grooming.service.dto.ob.OBAbandonRecordDTO;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

public interface MoeBookOnlineAbandonRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    long countByExample(MoeBookOnlineAbandonRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    int deleteByExample(MoeBookOnlineAbandonRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlineAbandonRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlineAbandonRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    List<MoeBookOnlineAbandonRecord> selectByExampleWithBLOBs(MoeBookOnlineAbandonRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    List<MoeBookOnlineAbandonRecord> selectByExample(MoeBookOnlineAbandonRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    MoeBookOnlineAbandonRecord selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeBookOnlineAbandonRecord record,
            @Param("example") MoeBookOnlineAbandonRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("record") MoeBookOnlineAbandonRecord record,
            @Param("example") MoeBookOnlineAbandonRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeBookOnlineAbandonRecord record,
            @Param("example") MoeBookOnlineAbandonRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlineAbandonRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeBookOnlineAbandonRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlineAbandonRecord record);

    MoeBookOnlineAbandonRecord selectUndeletedAbandonRecordByBookingFlowId(Integer businessId, String bookingFlowId);

    MoeBookOnlineAbandonRecord selectAbandonRecordByBookingFlowId(Integer businessId, String bookingFlowId);

    int deleteByBookingFlowId(MoeBookOnlineAbandonRecord abandonRecord);

    Set<String> selectBookingFlowIdsByKeyword(Integer businessId, String keyword);

    List<MoeBookOnlineAbandonRecord> searchByFilterPram(@Param("param") SearchAbandonedRecordParam param);

    int countStartedClients(
            @Param("businessId") Integer businessId,
            @Param("startTime") long startTime,
            @Param("endTime") long endTime,
            @Param("recoverableSteps") List<OBStepEnum> recoverableSteps);

    int countStartedNotRecoveredRecords(
            @Param("businessId") Integer businessId,
            @Param("startTime") long startTime,
            @Param("endTime") long endTime);

    int countNotRecoveredClients(
            @Param("businessId") Integer businessId,
            @Param("startTime") long startTime,
            @Param("endTime") long endTime,
            @Param("recoverableSteps") List<OBStepEnum> recoverableSteps);

    int countAbandonedClients(
            @Param("businessId") Integer businessId,
            @Param("startTime") long startTime,
            @Param("endTime") long endTime,
            @Param("recoverableSteps") List<OBStepEnum> recoverableSteps);

    int countRecoverableClients(
            @Param("businessId") Integer businessId,
            @Param("startTime") long startTime,
            @Param("endTime") long endTime,
            @Param("recoverableSteps") List<OBStepEnum> recoverableSteps);

    int countRecoveredClients(
            @Param("businessId") Integer businessId,
            @Param("startTime") long startTime,
            @Param("endTime") long endTime);

    int countOnlyAbandonedClients(
            @Param("businessId") Integer businessId,
            @Param("startTime") long startTime,
            @Param("endTime") long endTime,
            @Param("recoverableSteps") List<OBStepEnum> recoverableSteps);

    int countRecoverableAbandonedClients(
            @Param("businessId") Integer businessId,
            @Param("startTime") long startTime,
            @Param("endTime") long endTime,
            @Param("recoverableSteps") List<OBStepEnum> recoverableSteps);

    int countRecoveredRecords(
            @Param("businessId") Integer businessId,
            @Param("startTime") long startTime,
            @Param("endTime") long endTime,
            @Param("recoverableSteps") List<OBStepEnum> recoverableSteps);

    List<Integer> listRecoveredApptId(
            @Param("businessId") Integer businessId,
            @Param("startTime") long startTime,
            @Param("endTime") long endTime);

    int updateAbandonedRecordByCustomerId(
            @Param("businessId") int businessId,
            @Param("customerId") int customerId,
            @Param("param") UpdateAbandonedRecordByCustomerIdParam param);

    int removeAbandonRecords(
            @Param("businessId") int businessId,
            @Param("deleteType") byte deleteType,
            @Param("customerId") Integer customerId,
            @Param("phoneNumber") String phoneNumber);

    int deleteNotRecoveredAbandonedRecords(
            @Param("businessId") int businessId,
            @Param("deleteType") byte deleteType,
            @Param("abandonTime") long abandonTime,
            @Param("customerId") Integer customerId,
            @Param("phoneNumber") String phoneNumber);

    List<MoeBookOnlineAbandonRecord> listByCustomerId(
            @Param("businessId") int businessId, @Param("customerId") int customerId);

    List<CountByAbandonedStepPO> countExistingClientRecordByAbandonedStep(
            @Param("businessId") int businessId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("abandonSteps") List<OBStepEnum> abandonSteps);

    List<CountByAbandonedStepPO> countNewClientRecordByAbandonedStep(
            @Param("businessId") int businessId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("abandonSteps") List<OBStepEnum> abandonSteps);

    /**
     * Count existing clients, distinct by customer.
     *
     * @param businessId businessId
     * @param startTime  nullable, if null, then no lower bound, in seconds
     * @param endTime    nullable, if null, then no upper bound, in seconds
     * @param abandonSteps      if null or empty, then no step filter
     * @return count of existing client
     */
    int countExistingClient(
            @Param("businessId") int businessId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("abandonSteps") List<OBStepEnum> abandonSteps);

    /**
     * Count new clients, distinct by phone number.
     *
     * @param businessId businessId
     * @param startTime  nullable, if null, then no lower bound, in seconds
     * @param endTime    nullable, if null, then no upper bound, in seconds
     * @param abandonSteps      if null or empty, then no step filter
     * @return count of new client
     */
    int countNewClient(
            @Param("businessId") int businessId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("abandonSteps") List<OBStepEnum> abandonSteps);

    Set<Integer> getCustomerIds(
            @Param("businessId") Integer businessId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("abandonSteps") List<OBStepEnum> abandonSteps);

    Set<Integer> getAddressIds(
            @Param("businessId") Integer businessId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("abandonSteps") List<OBStepEnum> abandonSteps);

    void updateLeadTypeByCustomerId(
            @Param("businessId") Integer businessId,
            @Param("customerId") Integer customerId,
            @Param("leadType") String leadType);

    int resetCustomerIdById(@Param("id") Integer id);

    Set<Integer> listCustomerIdByFilter(
            @Param("clientsFilter") ClientsFilterDTO clientsFilter,
            @Param("abandonSteps") List<OBStepEnum> abandonSteps);

    List<OBAbandonRecordDTO> getLatestAbandonedRecordByCustomerIds(
            @Param("businessId") Integer businessId, @Param("customerIds") List<Integer> customerIds);

    /**
     * 获取最新产生的一条 abandon 记录
     */
    MoeBookOnlineAbandonRecord getLatestAbandonedRecord();

    List<Integer> selectByAbandonTimeRange(@Param("startTime") Long startTime, @Param("endTime") Long endTime);
}
