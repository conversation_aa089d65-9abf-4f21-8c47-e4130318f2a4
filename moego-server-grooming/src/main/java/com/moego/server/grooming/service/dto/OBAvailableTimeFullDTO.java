package com.moego.server.grooming.service.dto;

import com.moego.server.grooming.dto.OBAvailableTimeWithNonAvailableDTO;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OBAvailableTimeFullDTO {

    private Map<String /* date */, OBAvailableTimeWithNonAvailableDTO> availableDateTimesWithNonAvailable;

    private Map<String /* date */, OBAvailableTimeDto> availableDateTimes;

    Map<String /* date */, Map<Integer /* staff id */, OneDayTimeslotsDTO>> timeslotMapByDate;
}
