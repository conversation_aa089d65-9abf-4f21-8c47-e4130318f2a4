package com.moego.server.grooming.utils;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.util.StringUtils;

public class PetDetailDTOUtil {

    // 过滤出 service 类型的 petDetail，并按 petId -> serviceId -> serviceDetail 的形式返回
    public static Map<Integer, Map<Integer, GroomingPetDetailDTO>> getPetServiceMap(
            List<GroomingPetDetailDTO> appointmentPetDetails) {
        return appointmentPetDetails.stream()
                .filter(petDetail -> Objects.equals(petDetail.getServiceType(), ServiceType.SERVICE_VALUE))
                .collect(Collectors.groupingBy(
                        GroomingPetDetailDTO::getPetId,
                        Collectors.toMap(GroomingPetDetailDTO::getServiceId, Function.identity(), (p1, p2) -> p1)));
    }

    private static boolean datePoint(GroomingPetDetailDTO petDetail) {
        return StringUtils.hasText(petDetail.getStartDate()) && Objects.nonNull(petDetail.getStartTime());
    }

    private static boolean specificDates(GroomingPetDetailDTO petDetail) {
        return !getSpecificDates(petDetail).isEmpty();
    }

    private static List<String> getSpecificDates(GroomingPetDetailDTO petDetail) {
        List<String> result = new ArrayList<>();
        if (StringUtils.hasText(petDetail.getSpecificDates())) {
            result = JsonUtil.toList(petDetail.getSpecificDates(), String.class);
        }
        return result;
    }

    private static GroomingPetDetailDTO getAssociatedService(
            GroomingPetDetailDTO petDetail, Map<Integer, GroomingPetDetailDTO> petServiceMap) {
        if (!PetDetailUtil.isServiceTypeNormal(petDetail.getServiceType())) {
            return null;
        }

        if (petDetail.getAssociatedServiceId() != null && petDetail.getAssociatedServiceId() > 0) {
            return petServiceMap.get(petDetail.getAssociatedServiceId().intValue());
        }
        var mainPetDetail = getMainService(petServiceMap.values());
        if (mainPetDetail == null) {
            return null;
        }

        // for addOn
        if (petDetail.getServiceType() == ServiceType.ADDON_VALUE) {
            return mainPetDetail;
        }

        // for service
        if (petDetail.getServiceType() == ServiceType.SERVICE_VALUE
                && petDetail.getServiceItemType() == ServiceItemType.DAYCARE_VALUE
                && mainPetDetail.getServiceItemType() == ServiceItemType.BOARDING_VALUE) {
            return mainPetDetail;
        }
        return null;
    }

    /**
     * Retrieves the main service from the given petDetails.
     *
     * @param petDetails A collection of petDetails for one pet.
     * @return main service
     */
    private static GroomingPetDetailDTO getMainService(Collection<GroomingPetDetailDTO> petDetails) {
        var mainServiceItemType = ServiceItemEnum.getMainServiceItemType(petDetails.stream()
                .filter(v -> Objects.equals(v.getServiceType(), ServiceType.SERVICE_VALUE))
                .map(GroomingPetDetailDTO::getServiceItemType)
                .toList());
        return petDetails.stream()
                .filter(v -> Objects.equals(v.getServiceType(), ServiceType.SERVICE_VALUE))
                .filter(v -> Objects.equals(v.getServiceItemType(), mainServiceItemType.getServiceItem()))
                .findFirst()
                .orElse(null);
    }

    private static List<String> getBoardingDates(
            GroomingPetDetailDTO petDetail, Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap) {
        return switch (Objects.requireNonNull(ServiceType.forNumber(petDetail.getServiceType()))) {
            case SERVICE -> getBoardingServiceDates(petDetail, petServiceMap);
            case ADDON -> getBoardingAddOnDates(petDetail, petServiceMap);
            default -> List.of();
        };
    }

    private static List<String> getBoardingServiceDates(
            GroomingPetDetailDTO petDetail, Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap) {
        if (Objects.equals(ServicePriceUnit.PER_NIGHT.getNumber(), petDetail.getPriceUnit())) {
            return PetDetailUtil.generateAllDatesBetweenByNight(petDetail.getStartDate(), petDetail.getEndDate());
        }
        return DateUtil.generateAllDatesBetween(petDetail.getStartDate(), petDetail.getEndDate());
    }

    private static List<String> getBoardingAddOnDates(
            GroomingPetDetailDTO petDetail, Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap) {
        if (datePoint(petDetail)) {
            return List.of(petDetail.getStartDate());
        }
        if (specificDates(petDetail)) {
            return JsonUtil.toList(petDetail.getSpecificDates(), String.class);
        }

        GroomingPetDetailDTO associatedService =
                getAssociatedService(petDetail, petServiceMap.get(petDetail.getPetId()));
        if (associatedService == null) {
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "Associated service not found");
        }

        // every day not include the checkout day
        if (Objects.equals(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE, petDetail.getDateType())) {
            return PetDetailUtil.generateAllDatesBetweenByNight(
                    associatedService.getStartDate(), associatedService.getEndDate());
        }
        // every day include the checkout day
        return DateUtil.generateAllDatesBetween(associatedService.getStartDate(), associatedService.getEndDate());
    }

    private static List<String> getDaycareDates(
            GroomingPetDetailDTO petDetail, Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap) {
        if (datePoint(petDetail)) {
            return List.of(petDetail.getStartDate());
        }
        if (specificDates(petDetail)) {
            return JsonUtil.toList(petDetail.getSpecificDates(), String.class);
        }

        GroomingPetDetailDTO associatedService =
                getAssociatedService(petDetail, petServiceMap.get(petDetail.getPetId()));
        if (associatedService == null) {
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "Associated service not found");
        }
        if (Objects.equals(ServiceItemType.BOARDING_VALUE, associatedService.getServiceItemType())) {
            // every day not include the checkout day
            if (Objects.equals(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE, petDetail.getDateType())) {
                return PetDetailUtil.generateAllDatesBetweenByNight(
                        associatedService.getStartDate(), associatedService.getEndDate());
            }
            // every day include the checkout day
            return DateUtil.generateAllDatesBetween(associatedService.getStartDate(), associatedService.getEndDate());
        }
        return getDaycareDates(associatedService, petServiceMap);
    }

    private static List<String> getGroomingDates(
            GroomingPetDetailDTO petDetail, Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap) {
        if (datePoint(petDetail)) {
            return List.of(petDetail.getStartDate());
        }
        if (specificDates(petDetail)) {
            return JsonUtil.toList(petDetail.getSpecificDates(), String.class);
        }

        GroomingPetDetailDTO associatedService =
                getAssociatedService(petDetail, petServiceMap.get(petDetail.getPetId()));
        if (associatedService == null) {
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "Associated service not found");
        }
        if (Objects.equals(ServiceItemType.BOARDING_VALUE, associatedService.getServiceItemType())) {
            // every day not include the checkout day
            if (Objects.equals(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE, petDetail.getDateType())) {
                return PetDetailUtil.generateAllDatesBetweenByNight(
                        associatedService.getStartDate(), associatedService.getEndDate());
            }
            // every day include the checkout day
            return DateUtil.generateAllDatesBetween(associatedService.getStartDate(), associatedService.getEndDate());
        }
        return getDaycareDates(associatedService, petServiceMap);
    }

    public static List<String> getServiceDates(
            GroomingPetDetailDTO petDetail, Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap) {
        return switch (Objects.requireNonNull(mapServiceItemType(petDetail.getServiceItemType()))) {
            case BOARDING -> getBoardingDates(petDetail, petServiceMap);
            case DAYCARE -> getDaycareDates(petDetail, petServiceMap);
            case GROOMING -> getGroomingDates(petDetail, petServiceMap);
            case DOG_WALKING -> getGroomingDates(petDetail, petServiceMap); // same as grooming
            default -> List.of();
        };
    }

    public static ServiceItemType mapServiceItemType(Integer value) {
        if (value == null) {
            return ServiceItemType.UNRECOGNIZED;
        }
        var serviceItemType = ServiceItemType.forNumber(value);
        if (serviceItemType == null) {
            return ServiceItemType.UNRECOGNIZED;
        }
        return serviceItemType;
    }
}
