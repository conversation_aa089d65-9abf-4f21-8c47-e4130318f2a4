package com.moego.server.grooming.service.dto.report;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Builder
@Setter
@Getter
@Accessors(chain = true)
public class StaffRevenueDetail {
    // staff/appointment/pet/service/product 相关字段
    private Integer staffId;
    private Integer invoiceId;
    private Integer appointmentId;
    private String appointmentStatus;
    private String date;
    private Integer startTime;
    private String paymentStatus;
    private Integer customerId;

    @Builder.Default
    private List<String> services = new ArrayList<>();

    @Builder.Default
    private List<String> addOns = new ArrayList<>();

    @Builder.Default
    private List<String> products = new ArrayList<>();

    @Builder.Default
    private List<Integer> petIds = new ArrayList<>();

    // 金额相关字段
    @Builder.Default
    private BigDecimal expectedRevenue = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal collectedRevenue = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal netSaleRevenue =
            BigDecimal.ZERO; // netSaleRevenue = collectedRevenue - convenienceFee - tax - tips - refund

    @Builder.Default
    private BigDecimal unpaidRevenue = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal totalPayment = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal totalRefund = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal totalTips = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal totalTax = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal discount = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal servicePrice = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal addonPrice = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal productPrice = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal collectedServicePrice = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal collectedAddonPrice = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal collectedProductPrice = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal collectedTips = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal collectedTax = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal serviceCommissionBase = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal serviceCommission = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal addonCommissionBase = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal addonCommission = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal tipsCommissionBase = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal tipsCommission = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal totalCommission = BigDecimal.ZERO; // serviceComm + addonComm + tipsComm

    @Builder.Default
    private BigDecimal orderTotalTips = BigDecimal.ZERO;

    // 计算用到的中间字段
    @Builder.Default
    private Map<String, BigDecimal> paymentMap = new HashMap<>();

    @Builder.Default
    private Map<Integer, BigDecimal> serviceCollectedMap = new HashMap<>();

    @Builder.Default
    private Map<Integer, BigDecimal> addonCollectedMap = new HashMap<>();

    @Builder.Default
    private Map<Integer, BigDecimal> serviceExpectedMap = new HashMap<>();

    @Builder.Default
    private Map<Integer, BigDecimal> addonExpectedMap = new HashMap<>();

    @Builder.Default
    private BigDecimal finishExpectedServicePrice = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal finishExpectedAddonPrice = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal finishExpectedTips = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal exceptionServiceBase = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal exceptionAddonBase = BigDecimal.ZERO;
}
