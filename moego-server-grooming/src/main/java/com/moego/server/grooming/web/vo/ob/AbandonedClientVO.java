package com.moego.server.grooming.web.vo.ob;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AbandonedClientVO {

    /**
     * Abandon record id.
     */
    private Integer id;

    private String bookingFlowId;
    private String abandonStatus;
    private String abandonStep;
    private Long abandonTime;
    private String avatarPath;
    private String firstName;
    private String lastName;
    private List<Pet> pets;
    private String phoneNumber;
    private Appointment appointment;
    private Integer customerId;
    private boolean hasRequestUpdate;
    private Long lastContactTime;
    private String careType;

    @Data
    public static class Pet {

        private Integer petId;
        private String breed;
        private String petName;
        private Integer petTypeId;
    }

    @Data
    @Accessors(chain = true)
    public static class Appointment {

        private Integer id;
        /**
         * OB request status.
         *
         * <p> 0: not OB request
         * <p> 1: OB request
         */
        private Byte status = 0;
    }
}
