package com.moego.server.grooming.server;

import com.moego.server.grooming.api.IGoogleAnalyticsServiceBase;
import com.moego.server.grooming.service.google.GoogleAnalyticsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class GoogleAnalyticsServer extends IGoogleAnalyticsServiceBase {

    private final GoogleAnalyticsService googleAnalyticsService;

    @Override
    public Boolean syncTotalUsersMetricsToDB() {
        return googleAnalyticsService.syncTotalUsersMetricsToDB();
    }
}
