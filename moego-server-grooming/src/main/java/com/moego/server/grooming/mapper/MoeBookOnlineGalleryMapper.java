package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlineGallery;
import com.moego.server.grooming.params.MoeBookOnlineGalleryBatchParams;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeBookOnlineGalleryMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_gallery
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_gallery
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlineGallery record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_gallery
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlineGallery record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_gallery
     *
     * @mbg.generated
     */
    MoeBookOnlineGallery selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_gallery
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlineGallery record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_gallery
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlineGallery record);

    /**
     * get Record by businessId
     * @param businessId 商户主键
     * @return
     */
    List<MoeBookOnlineGallery> selectByBusinessId(Integer businessId);

    /**
     * 批量删除
     *
     * @param batchParams 需要删除的id列表
     * @return
     */
    int batchDeleteGallery(@Param("batchParams") MoeBookOnlineGalleryBatchParams batchParams);

    /**
     * 批量标记
     * @param galleries 需要更新的列表
     * @return
     */
    int batchStarGallery(@Param("galleries") List<MoeBookOnlineGallery> galleries);

    List<MoeBookOnlineGallery> listGalleryMaxSortImageByBusinessId(
            @Param("businessIdList") List<Integer> businessIdList);
}
