package com.moego.server.grooming.service.dto.client;

import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/9
 */
@Data
@Accessors(chain = true)
public class UpdateApptConditionDTO {

    private Integer apptId;

    private Byte isDeprecate;

    private Byte bookOnlineStatus;

    private Integer source;

    private Integer createdById;

    private Byte status;

    List<BaseBusinessCustomerIdDTO> customerIdDTOList;
}
