package com.moego.server.grooming.server;

import static java.util.stream.Collectors.groupingBy;

import com.moego.common.constant.CommonConstant;
import com.moego.common.response.ResponseResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.BizException;
import com.moego.server.grooming.api.IGroomingServiceServiceBase;
import com.moego.server.grooming.dto.GroomingServiceBreedBindingDTO;
import com.moego.server.grooming.dto.GroomingServiceDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.mapper.MoeGroomingServiceBreedBindingMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceBreedBinding;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceExample;
import com.moego.server.grooming.mapstruct.GroomingServiceMapper;
import com.moego.server.grooming.params.CommonIdsParams;
import com.moego.server.grooming.params.CreateCustomerPackageResult;
import com.moego.server.grooming.params.PurchasedPackage;
import com.moego.server.grooming.params.QueryServiceByTagIdParams;
import com.moego.server.grooming.params.QueryServiceByTypeParams;
import com.moego.server.grooming.params.UpdateServiceByCareTypeParams;
import com.moego.server.grooming.service.CompanyGroomingServiceQueryService;
import com.moego.server.grooming.service.GroomingServiceService;
import com.moego.server.grooming.service.MoePackageService;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class GroomingServiceServer extends IGroomingServiceServiceBase {

    private final GroomingServiceService groomingServiceService;
    private final CompanyGroomingServiceQueryService companyGroomingServiceQueryService;
    private final MoePackageService packageService;
    private final MoeGroomingServiceMapper groomingServiceMapper;
    private final BusinessInfoHelper businessInfoHelper;
    private final MoeGroomingServiceBreedBindingMapper serviceBreedBindingMapper;

    @Override
    public List<GroomingServiceDTO> list(Collection<Integer> serviceIds) {
        if (ObjectUtils.isEmpty(serviceIds)) {
            return List.of();
        }
        MoeGroomingServiceExample example = new MoeGroomingServiceExample();
        example.createCriteria().andIdIn(List.copyOf(serviceIds)).andStatusEqualTo(CommonConstant.NORMAL);
        return groomingServiceMapper.selectByExample(example).stream()
                .map(GroomingServiceMapper.INSTANCE::entity2DTO)
                .toList();
    }

    @Override
    public ResponseResult<List<MoeGroomingServiceDTO>> getServicesByServiceIds(
            @RequestBody CommonIdsParams commonIdsParams) {
        return ResponseResult.success(groomingServiceService.getServicesByServiceIds(
                commonIdsParams.getBusinessId(), commonIdsParams.getIds()));
    }

    @Override
    public List<Integer> getServiceIdByServiceIdsLocationIdFilter(CommonIdsParams commonIdsParams) {
        var serviceList = groomingServiceMapper.getServicesByBusinessIdServiceIdsNew(
                commonIdsParams.getCompanyId(), commonIdsParams.getBusinessId(), commonIdsParams.getIds());
        return serviceList.stream().map(MoeGroomingService::getId).toList();
    }

    @Override
    public Boolean selectServiceByTagId(@RequestBody QueryServiceByTagIdParams params) {
        return groomingServiceService.selectServiceByTagId(params.getCompanyId(), params.getTagId());
    }

    @Override
    public CreateCustomerPackageResult createPurchasedPackageAndReturnResult(
            @RequestBody List<PurchasedPackage> purchasedPackages) {
        List<Integer> businessIds = purchasedPackages.stream()
                .filter(p -> p.getCompanyId() == null || p.getCompanyId().equals(0L))
                .filter(p -> p.getBusinessId() != null && p.getBusinessId() > 0)
                .map(PurchasedPackage::getBusinessId)
                .distinct()
                .toList();
        if (!businessIds.isEmpty()) {
            if (businessIds.size() > 1) {
                throw new BizException(Code.CODE_PARAMS_ERROR_VALUE, "package's business id must be the same");
            }
            var companyId = businessInfoHelper.getCompanyIdByBusinessId(businessIds.get(0));
            purchasedPackages.forEach(p -> {
                if ((p.getCompanyId() == null || p.getCompanyId().equals(0L))) {
                    p.setCompanyId(companyId);
                }
            });
        }
        return packageService.createPurchasedPackage(purchasedPackages);
    }

    @Override
    public ResponseResult<Integer> createPurchasedPackage(@RequestBody List<PurchasedPackage> purchasedPackages) {
        var result = createPurchasedPackageAndReturnResult(purchasedPackages);
        return ResponseResult.success(
                result.getPackageIdToCustomerPackageIdMap().size());
    }

    @Override
    public GroomingServiceDTO getGroomingServiceById(int id) {
        MoeGroomingService entity = groomingServiceMapper.selectByPrimaryKey(id); // 用在 log 和 type 校验
        return GroomingServiceMapper.INSTANCE.entity2DTO(entity);
    }

    @Override
    public Boolean updateCoatBinding(int businessId, int coatId) {
        return groomingServiceService.updateCoatBindingAfterDeleteCoat(businessId, coatId);
    }

    @Override
    public Boolean updateBreedBinding(int businessId, String breedName) {
        return groomingServiceService.updateBreedBindingAfterDeleteBreed(businessId, breedName);
    }

    @Override
    public Map<Integer, List<GroomingServiceDTO>> getServices(Collection<Integer> businessIds) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return Map.of();
        }
        return companyGroomingServiceQueryService.queryServiceWithObSettingByBidList(new ArrayList<>(businessIds));
    }

    @Override
    public Map<Long, List<GroomingServiceDTO>> getServicesByCompanyIds(Collection<Long> companyIds) {
        if (CollectionUtils.isEmpty(companyIds)) {
            return Map.of();
        }
        return groomingServiceMapper.selectByCompanyIds(companyIds).stream()
                .map(GroomingServiceMapper.INSTANCE::entity2DTO)
                .collect(groupingBy(GroomingServiceDTO::getCompanyId));
    }

    @Override
    public List<GroomingServiceBreedBindingDTO> getBreedBindings(CommonIdsParams commonIdsParams) {
        List<Integer> businessIds =
                commonIdsParams.getBusinessId() != null ? List.of(commonIdsParams.getBusinessId()) : List.of();
        List<MoeGroomingServiceBreedBinding> models = serviceBreedBindingMapper.selectByCidBidsAndServiceId(
                commonIdsParams.getCompanyId(), businessIds, null);
        return models.stream()
                .map(model -> new GroomingServiceBreedBindingDTO()
                        .setId(model.getId())
                        .setBusinessId(model.getBusinessId())
                        .setServiceId(model.getServiceId())
                        .setPetTypeId(model.getPetTypeId())
                        .setIsAll(model.getIsAll())
                        .setBreedNameList(model.getBreedNameList())
                        .setCompanyId(model.getCompanyId()))
                .toList();
    }

    @Override
    public Integer updateBreedBindings(List<GroomingServiceBreedBindingDTO> dtos) {
        AtomicInteger affectedRows = new AtomicInteger();
        dtos.forEach(dto -> {
            MoeGroomingServiceBreedBinding model = new MoeGroomingServiceBreedBinding();
            model.setId(dto.getId());
            model.setBreedNameList(dto.getBreedNameList());
            serviceBreedBindingMapper.updateByPrimaryKeySelective(model);
            affectedRows.incrementAndGet();
        });
        return affectedRows.get();
    }

    @Override
    public List<Long> getObAvailableServiceIds(QueryServiceByTypeParams queryServiceByTypeParams) {
        return groomingServiceService.getObAvailableServiceIds(queryServiceByTypeParams);
    }

    @Override
    public Boolean updateBookOnlineAvailable(UpdateServiceByCareTypeParams updateServiceByCareTypeParams) {
        return groomingServiceService.updateBookOnlineAvailableByIds(updateServiceByCareTypeParams);
    }
}
