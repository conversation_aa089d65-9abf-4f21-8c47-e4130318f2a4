package com.moego.server.grooming.web.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/14
 */
@Data
@Accessors(chain = true)
public class OBConfigVO {

    @Schema(description = "Online booking enable, 0-false, 1-true")
    private Byte enableOB;

    @Schema(description = "Smart scheduling enable")
    private Boolean querySmartScheduling;

    @Schema(description = "How far can your client book")
    private Integer farthestDay;

    @Schema(description = "Availability type, 0-By working hours, 1-By slots, 2-Disable select time")
    private Byte availableTimeType;

    @Schema(description = "By slots time slot format")
    private Byte bySlotTimeslotFormat;

    @Schema(description = "By slots time slot interval")
    private Integer bySlotTimeslotMins;

    @Schema(description = "By working hours time slot format, Exact times, Arrival windows")
    private Byte timeslotFormat;

    @Schema(description = "By working hours time slot interval")
    private Integer timeslotMins;

    @Schema(description = "Book online name")
    private String bookOnlineName;

    @Schema(description = "OB version")
    private Byte useVersion;

    private Boolean displayStaffSelectionPage;
    private Integer arrivalWindowBeforeMin;
    private Integer arrivalWindowAfterMin;
    private Integer bookingRangeStartOffset;
    private Byte bookingRangeEndType;
    private Integer bookingRangeEndOffset;
    private String bookingRangeEndDate;
    private Boolean isNeedSendRenewNotification;
}
