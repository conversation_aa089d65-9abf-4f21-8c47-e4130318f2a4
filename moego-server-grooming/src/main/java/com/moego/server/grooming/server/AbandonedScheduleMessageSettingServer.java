package com.moego.server.grooming.server;

import com.moego.server.grooming.api.IAbandonedScheduleMessageSettingServiceBase;
import com.moego.server.grooming.dto.AbandonedScheduleMessageSettingDTO;
import com.moego.server.grooming.mapper.AbandonedScheduleMessageSettingMapper;
import com.moego.server.grooming.mapperbean.AbandonedScheduleMessageSettingExample;
import com.moego.server.grooming.mapstruct.AbandonedScheduleMessageSettingConverter;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class AbandonedScheduleMessageSettingServer extends IAbandonedScheduleMessageSettingServiceBase {

    private final AbandonedScheduleMessageSettingMapper abandonedScheduleMessageSettingMapper;

    @Override
    public List<AbandonedScheduleMessageSettingDTO> listByCondition(ListByCondition condition) {
        AbandonedScheduleMessageSettingExample example = new AbandonedScheduleMessageSettingExample();
        AbandonedScheduleMessageSettingExample.Criteria criteria = example.createCriteria();
        Optional.ofNullable(condition.businessIdIn())
                .filter(e -> !e.isEmpty())
                .ifPresent(e -> criteria.andBusinessIdIn(List.copyOf(e)));
        Optional.ofNullable(condition.isEnabledIn())
                .filter(e -> !e.isEmpty())
                .ifPresent(e -> criteria.andIsEnabledIn(List.copyOf(e)));
        return abandonedScheduleMessageSettingMapper.selectByExample(example).stream()
                .map(AbandonedScheduleMessageSettingConverter.INSTANCE::entityToDTO)
                .toList();
    }
}
