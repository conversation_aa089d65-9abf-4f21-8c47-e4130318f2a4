package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.AppointmentPetScheduleSetting;
import com.moego.server.grooming.mapperbean.AppointmentPetScheduleSettingExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AppointmentPetScheduleSettingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_schedule_setting
     *
     * @mbg.generated
     */
    long countByExample(AppointmentPetScheduleSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_schedule_setting
     *
     * @mbg.generated
     */
    int deleteByExample(AppointmentPetScheduleSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_schedule_setting
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_schedule_setting
     *
     * @mbg.generated
     */
    int insert(AppointmentPetScheduleSetting record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_schedule_setting
     *
     * @mbg.generated
     */
    int insertSelective(AppointmentPetScheduleSetting record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_schedule_setting
     *
     * @mbg.generated
     */
    List<AppointmentPetScheduleSetting> selectByExample(AppointmentPetScheduleSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_schedule_setting
     *
     * @mbg.generated
     */
    AppointmentPetScheduleSetting selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_schedule_setting
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") AppointmentPetScheduleSetting record,
            @Param("example") AppointmentPetScheduleSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_schedule_setting
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") AppointmentPetScheduleSetting record,
            @Param("example") AppointmentPetScheduleSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_schedule_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(AppointmentPetScheduleSetting record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_schedule_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(AppointmentPetScheduleSetting record);
}
