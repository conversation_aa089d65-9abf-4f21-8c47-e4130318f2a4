package com.moego.server.grooming.config;

import com.moego.lib.common.util.ThreadPoolUtil;
import java.time.Duration;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@RequiredArgsConstructor
public class DevelopScriptConfiguration {

    private final DevelopScriptProperties developScriptProperties;

    public static final String DEVELOP_SCRIPT_EXECUTOR_SERVICE = "developScriptExecutorService";

    @Bean(name = DEVELOP_SCRIPT_EXECUTOR_SERVICE)
    public ExecutorService developScriptExecutorService() {
        return ThreadPoolUtil.newExecutorService(
                developScriptProperties.getCorePoolSize(),
                developScriptProperties.getMaximumPoolSize(),
                Duration.ofSeconds(developScriptProperties.getAliveTimeSeconds()),
                developScriptProperties.getQueueCapacity(),
                "moego-develop-script-",
                new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
