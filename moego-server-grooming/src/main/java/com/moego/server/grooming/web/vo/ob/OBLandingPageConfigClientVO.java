package com.moego.server.grooming.web.vo.ob;

import com.moego.server.grooming.web.vo.ob.component.BaseComponentVO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/2/22
 */
@Data
@Accessors(chain = true)
public class OBLandingPageConfigClientVO {

    @Schema(description = "Theme color, Inherited from moe_book_online_profile.button_color")
    private String themeColor;

    @Schema(description = "GA4 MEASUREMENT ID")
    private String gaMeasurementId;

    @Schema(description = "Landing page components")
    private List<BaseComponentVO> pageComponents;

    @Schema(description = "thank you page url")
    private String thankYouPageUrl;
}
