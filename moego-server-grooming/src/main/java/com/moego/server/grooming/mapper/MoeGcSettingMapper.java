package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGcSetting;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGcSettingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_setting
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_setting
     *
     * @mbg.generated
     */
    int insert(MoeGcSetting record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_setting
     *
     * @mbg.generated
     */
    int insertSelective(MoeGcSetting record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_setting
     *
     * @mbg.generated
     */
    MoeGcSetting selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGcSetting record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGcSetting record);

    MoeGcSetting selectByStaffIdBusinessId(@Param("businessId") Integer businessId, @Param("staffId") Integer staffId);

    List<MoeGcSetting> selectByGoogleAuthId(@Param("googleAuthId") Integer googleAuthId);

    List<MoeGcSetting> selectByBusinessId(@Param("businessId") Integer businessId);

    List<MoeGcSetting> selectNeedImportSetting();

    List<MoeGcSetting> selectNeedExportSetting();
}
