package com.moego.server.grooming.mapper;

import com.moego.server.grooming.dto.GroomingPackageDTO;
import com.moego.server.grooming.dto.GroomingPackageServiceInfoDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingPackage;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingPackageMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package
     *
     * @mbg.generated
     */
    long countByExample(MoeGroomingPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package
     *
     * @mbg.generated
     */
    int insert(MoeGroomingPackage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingPackage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package
     *
     * @mbg.generated
     */
    List<MoeGroomingPackage> selectByExample(MoeGroomingPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package
     *
     * @mbg.generated
     */
    MoeGroomingPackage selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeGroomingPackage record, @Param("example") MoeGroomingPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeGroomingPackage record, @Param("example") MoeGroomingPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingPackage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingPackage record);

    List<GroomingPackageDTO> queryCustomerPackageListByBusinessIds(
            @Param("businessIds") List<Integer> businessIds, @Param("customerId") Integer customerId);

    List<GroomingPackageDTO> queryCustomerPackageListByCompany(
            @Param("companyId") Long companyId, @Param("customerId") Integer customerId);

    /**
     * @deprecated by Freeman, 方法的返回值已经不符合现在 package 模型定义
     */
    @Deprecated(since = "2024/10/28")
    List<GroomingPackageServiceInfoDTO> queryCustomerPackageByServiceIds(
            @Param("businessId") Integer businessId,
            @Param("serviceIds") List<Integer> serviceIds,
            @Param("customerId") Integer customerId,
            @Param("date") String date);

    List<GroomingPackageDTO> queryCustomerPackageListByFilter(
            @Param("companyId") Long companyId,
            @Param("customerIds") List<Integer> customerIds,
            @Param("businessId") Integer businessId,
            @Param("date") String date);
}
