package com.moego.server.grooming.web.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/5
 */
@Data
@Accessors(chain = true)
public class StaffDetailVO {

    @Schema(description = "Staff id")
    private Integer staffId;

    @Schema(description = "Staff first name")
    private String firstName;

    @Schema(description = "Staff last name")
    private String lastName;

    @Schema(description = "Staff avatar path")
    private String avatarPath;
}
