package com.moego.server.grooming.web.vo.ob.component;

import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.web.vo.client.AddressVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Address component.
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddressComponentVO extends BaseComponentVO {

    private String address;

    private AddressVO addressDetails;

    @Override
    public String getComponent() {
        return LandingPageComponentEnum.ADDRESS.getComponent();
    }
}
