package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.ObConfigClientReview;
import com.moego.server.grooming.mapperbean.ObConfigClientReviewExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ObConfigClientReviewMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_client_review
     *
     * @mbg.generated
     */
    long countByExample(ObConfigClientReviewExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_client_review
     *
     * @mbg.generated
     */
    int deleteByExample(ObConfigClientReviewExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_client_review
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_client_review
     *
     * @mbg.generated
     */
    int insert(ObConfigClientReview record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_client_review
     *
     * @mbg.generated
     */
    int insertSelective(ObConfigClientReview record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_client_review
     *
     * @mbg.generated
     */
    List<ObConfigClientReview> selectByExample(ObConfigClientReviewExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_client_review
     *
     * @mbg.generated
     */
    ObConfigClientReview selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_client_review
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") ObConfigClientReview record, @Param("example") ObConfigClientReviewExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_client_review
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") ObConfigClientReview record, @Param("example") ObConfigClientReviewExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_client_review
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(ObConfigClientReview record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_client_review
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(ObConfigClientReview record);

    /**
     * Batch insert, use default value if the property is null.
     *
     * @param entities entities
     * @return insert count
     */
    int batchInsertSelective(@Param("entities") List<ObConfigClientReview> entities);
}
