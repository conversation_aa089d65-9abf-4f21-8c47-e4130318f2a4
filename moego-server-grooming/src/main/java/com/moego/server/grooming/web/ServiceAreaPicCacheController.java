package com.moego.server.grooming.web;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.mapper.po.BusinessCompanyPO;
import com.moego.server.grooming.mapperbean.ServiceAreaPicCache;
import com.moego.server.grooming.mapstruct.ServiceAreaPicCacheConverter;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.grooming.service.ServiceAreaPicCacheService;
import com.moego.server.grooming.service.ob.OBLandingPageConfigService;
import com.moego.server.grooming.service.params.ServiceAreaPicCacheOperationParams;
import com.moego.server.grooming.web.vo.DeleteResult;
import com.moego.server.grooming.web.vo.ServiceAreaPicCacheVO;
import jakarta.validation.Valid;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/grooming/service-area-pic-cache")
public class ServiceAreaPicCacheController {

    private final ServiceAreaPicCacheService serviceAreaPicCacheService;
    private final OBLandingPageConfigService landingPageConfigService;

    /**
     * B 端删除 {@link ServiceAreaPicCache}
     */
    @DeleteMapping
    @Auth(AuthType.BUSINESS)
    public DeleteResult delete() {
        Integer businessId = AuthContext.get().getBusinessId();
        int affectedCount = serviceAreaPicCacheService.deleteByBusinessId(businessId);
        return new DeleteResult().setAffectedCount(affectedCount);
    }

    /**
     * C 端接口，获取 {@link ServiceAreaPicCache}
     */
    @GetMapping
    @Auth(AuthType.ANONYMOUS)
    public ServiceAreaPicCacheVO getFromClient(OBAnonymousParams params) {
        return getByBusinessId(getBusinessId(params));
    }

    /**
     * C 端接口，创建或更新 {@link ServiceAreaPicCache}
     */
    @PostMapping
    @Auth(AuthType.ANONYMOUS)
    public ServiceAreaPicCacheVO createOrUpdateFromClient(
            @RequestBody @Valid ServiceAreaPicCacheOperationParams param, OBAnonymousParams anonymousParams) {
        Integer businessId = getBusinessId(anonymousParams);
        ServiceAreaPicCache entity = ServiceAreaPicCacheConverter.INSTANCE.paramToEntity(param);
        entity.setBusinessId(businessId);
        if (getByBusinessId(businessId) == null) {
            insert(entity);
        } else {
            serviceAreaPicCacheService.updateByBusinessId(entity);
        }
        return getByBusinessId(businessId);
    }

    private void insert(ServiceAreaPicCache entity) {
        try {
            serviceAreaPicCacheService.insert(entity);
        } catch (DuplicateKeyException e) {
            log.warn("service area pic cache already exists!");
        }
    }

    private Integer getBusinessId(OBAnonymousParams params) {
        var businessId = Optional.ofNullable(landingPageConfigService.getBusinessIdAndCompanyIdByAnonymous(params))
                .map(BusinessCompanyPO::getBusinessId)
                .orElse(null);
        if (businessId == null) {
            throw ExceptionUtil.bizException(Code.CODE_BOOK_ONLINE_SITE_NOT_FOUND);
        }
        return businessId;
    }

    private ServiceAreaPicCacheVO getByBusinessId(Integer businessId) {
        ServiceAreaPicCache entity = serviceAreaPicCacheService.getByBusinessId(businessId);
        return ServiceAreaPicCacheConverter.INSTANCE.entityToVO(entity);
    }
}
