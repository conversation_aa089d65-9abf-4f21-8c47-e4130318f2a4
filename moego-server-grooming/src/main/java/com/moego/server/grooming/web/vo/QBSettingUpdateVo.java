package com.moego.server.grooming.web.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "设置更新实体类")
public class QBSettingUpdateVo {
    @Schema(description = "指定更新的 business", example = "100001")
    private Integer businessId;

    @Schema(description = "是否开启同步  1 开启  0 关闭(默认)")
    private Byte enableSync;

    @Schema(description = "预约开始同步日期(默认创建时间) 年-月-日")
    private String syncBeginDate;

    @Schema(description = "账本名")
    private String accountName;

    @Schema(description = "账本id")
    private String accountId;
}
