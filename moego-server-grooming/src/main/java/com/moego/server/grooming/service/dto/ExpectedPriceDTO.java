package com.moego.server.grooming.service.dto;

import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ExpectedPriceDTO {

    private BigDecimal expectedTips;
    private BigDecimal expectedTax;
    private BigDecimal discountAmount;
    private Integer serviceType;
    private BigDecimal servicePrice;
    private BigDecimal serviceRevenue;
}
