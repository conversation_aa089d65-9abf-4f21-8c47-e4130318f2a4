package com.moego.server.grooming.web;

import com.moego.common.utils.DateUtil;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.mapper.MoeGroomingNoteMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingNoteExample;
import com.moego.server.grooming.web.vo.DeleteResult;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/grooming/grooming-note")
@RequiredArgsConstructor
public class GroomingNoteController {

    private final MoeGroomingNoteMapper groomingNoteMapper;

    @DeleteMapping("/{id}")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(action = Action.DELETE, resourceType = ResourceType.GROOMING_NOTE, resourceId = "#id")
    public DeleteResult deleteById(@PathVariable("id") int id) {
        MoeGroomingNote updateBean = new MoeGroomingNote();
        updateBean.setUpdateBy(AuthContext.get().getStaffId());
        updateBean.setUpdateTime(DateUtil.get10Timestamp());
        updateBean.setIsDeleted(true);
        MoeGroomingNoteExample example = new MoeGroomingNoteExample();
        example.createCriteria()
                .andIdEqualTo(id)
                .andBusinessIdEqualTo(AuthContext.get().getBusinessId())
                .andIsDeletedEqualTo(false);
        int affected = groomingNoteMapper.updateByExampleSelective(updateBean, example);
        return new DeleteResult().setAffectedCount(affected);
    }
}
