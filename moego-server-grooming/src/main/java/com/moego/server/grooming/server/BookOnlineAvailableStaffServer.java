package com.moego.server.grooming.server;

import com.moego.common.constant.CommonConstant;
import com.moego.idl.service.online_booking.v1.GetStaffAvailabilityStatusRequest;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.grooming.api.IBookOnlineAvailableStaffServiceBase;
import com.moego.server.grooming.dto.BookOnlineAvailableStaffDTO;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.service.StaffAvailableSyncService;
import com.moego.server.grooming.service.ob.OBBusinessStaffService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class BookOnlineAvailableStaffServer extends IBookOnlineAvailableStaffServiceBase {

    private final OBBusinessStaffService obBusinessStaffService;
    private final MoeBusinessBookOnlineMapper bookOnlineMapper;
    private final StaffAvailableSyncService staffAvailableSyncService;
    private final OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceBlockingStub
            obStaffAvailabilityServiceBlockingStub;

    @Override
    public List<BookOnlineAvailableStaffDTO> listBookOnlineAvailableStaffByBusinessId(Integer businessId) {
        // available time sync
        var availableTimeSync = staffAvailableSyncService.queryAvailableTimeSync(businessId);
        // staff available
        var response = obStaffAvailabilityServiceBlockingStub.getStaffAvailabilityStatus(
                GetStaffAvailabilityStatusRequest.newBuilder()
                        .setBusinessId(businessId)
                        .build());
        List<BookOnlineAvailableStaffDTO> dtoList = new ArrayList<>();
        Map<Long, Boolean> staffAvailabilityMap = response.getStaffAvailabilityMap();
        for (Map.Entry<Long, Boolean> available : staffAvailabilityMap.entrySet()) {
            dtoList.add(BookOnlineAvailableStaffDTO.builder()
                    .businessId(businessId)
                    .staffId(available.getKey().intValue())
                    .bySlotEnable(available.getValue() ? CommonConstant.ENABLE : CommonConstant.DISABLE)
                    .byWorkingHourEnable(available.getValue() ? CommonConstant.ENABLE : CommonConstant.DISABLE)
                    .syncWithWorkingHour(availableTimeSync ? CommonConstant.ENABLE : CommonConstant.DISABLE)
                    .build());
        }
        return dtoList;
    }

    @Override
    public Boolean bookOnlineAvailableTimeSync(Integer businessId) {
        return staffAvailableSyncService.queryAvailableTimeSync(businessId);
    }

    @Override
    public List<MoeStaffDto> getAvailableStaffListInAvailabilityType(Integer businessId) {
        MoeBusinessBookOnline bookOnline = bookOnlineMapper.selectByBusinessId(businessId);
        return obBusinessStaffService.getOBAvailableStaffList(businessId, bookOnline.getAvailableTimeType());
    }
}
