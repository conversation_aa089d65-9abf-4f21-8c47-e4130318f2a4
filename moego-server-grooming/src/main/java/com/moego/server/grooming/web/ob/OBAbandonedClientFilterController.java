package com.moego.server.grooming.web.ob;

import static com.moego.server.grooming.web.params.SearchAbandonedClientParam.AbandonStatus;
import static com.moego.server.grooming.web.params.SearchAbandonedClientParam.LeadType;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordExample;
import com.moego.server.grooming.web.vo.ob.AbandonedClientFilterVO;
import com.moego.server.grooming.web.vo.ob.AbandonedClientFilterVO.Option;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/grooming/ob/v2/abandoned-client-filters")
public class OBAbandonedClientFilterController {

    private final MoeBookOnlineAbandonRecordMapper abandonRecordMapper;

    @GetMapping
    @Auth(AuthType.BUSINESS)
    public List<AbandonedClientFilterVO> findAll(
            @RequestParam(value = "startTimeSec", required = false) Long startTimeSec,
            @RequestParam(value = "endTimeSec", required = false) Long endTimeSec) {
        int businessId = Objects.requireNonNull(AuthContext.get().getBusinessId());

        // 默认查询最近一个小时以前的数据
        endTimeSec = endTimeSec != null ? endTimeSec : System.currentTimeMillis() / 1000 - 60 * 60;
        // 默认查询最近一个月的数据
        startTimeSec = startTimeSec != null ? startTimeSec : endTimeSec - 60 * 60 * 24 * 30;

        // lead_type filter
        AbandonedClientFilterVO leadTypeFilter = new AbandonedClientFilterVO();
        leadTypeFilter.setFilter("leadType");
        leadTypeFilter.setOptions(List.of(
                new Option()
                        .setValue(LeadType.NEW_VISITOR.getValue())
                        .setCount(abandonRecordMapper.countNewClient(
                                businessId,
                                startTimeSec,
                                endTimeSec,
                                new ArrayList<>(OBStepEnum.listRecoverableSteps()))),
                new Option()
                        .setValue(LeadType.EXISTING_CLIENT.getValue())
                        .setCount(abandonRecordMapper.countExistingClient(
                                businessId,
                                startTimeSec,
                                endTimeSec,
                                new ArrayList<>(OBStepEnum.listRecoverableSteps())))));

        // abandoned_step filter
        // 这里可能会出现数据对不上的情况，
        // 比如 new visitor 同一个手机号在不同 step 都出现过，在根据 step 分组计算时，该手机号可能被分在不同的 step 中（两条 records）
        // 而在 abandoned list 里，我们只会选择 new visitor 的最后一条记录，所以会出现数据对不上的情况
        // 所以根据 step 之和总是大于或等于 abandoned list 总数
        AbandonedClientFilterVO abandonedStepFilter = new AbandonedClientFilterVO();
        abandonedStepFilter.setFilter("abandonedStep");
        Map<String, Integer> stepToCount = new HashMap<>();
        abandonRecordMapper
                .countExistingClientRecordByAbandonedStep(
                        businessId, startTimeSec, endTimeSec, new ArrayList<>(OBStepEnum.listRecoverableSteps()))
                .forEach(po -> stepToCount.merge(po.getAbandonedStep(), po.getCount(), Integer::sum));
        abandonRecordMapper
                .countNewClientRecordByAbandonedStep(
                        businessId, startTimeSec, endTimeSec, new ArrayList<>(OBStepEnum.listRecoverableSteps()))
                .forEach(po -> stepToCount.merge(po.getAbandonedStep(), po.getCount(), Integer::sum));

        abandonedStepFilter.setOptions(stepToCount.entrySet().stream()
                .map(entry -> new Option().setValue(entry.getKey()).setCount(entry.getValue()))
                .toList());

        // status filter
        AbandonedClientFilterVO statusFilter = new AbandonedClientFilterVO();
        statusFilter.setFilter("abandonStatus");

        MoeBookOnlineAbandonRecordExample example = new MoeBookOnlineAbandonRecordExample();
        MoeBookOnlineAbandonRecordExample.Criteria criteria = example.createCriteria();
        criteria.andBusinessIdEqualTo(businessId)
                .andAbandonTimeBetween(startTimeSec, endTimeSec)
                .andIsDeletedEqualTo(Boolean.FALSE)
                .andAbandonStepIn(OBStepEnum.listRecoverableStepsString());
        statusFilter.setOptions(Arrays.stream(AbandonStatus.values())
                .map(status -> {
                    criteria.andAbandonStatusEqualTo(status.name().toLowerCase());
                    long count = abandonRecordMapper.countByExample(example);
                    return new Option().setValue(status.name().toLowerCase()).setCount((int) count);
                })
                .toList());

        return List.of(leadTypeFilter, abandonedStepFilter, statusFilter);
    }
}
