package com.moego.server.grooming.web.params.calendar;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

@Builder
public record QueryCardListParams(
        @Schema(description = "business id", hidden = true) Integer businessId,
        @Schema(description = "staff id", hidden = true) Integer staffId,
        @Schema(description = "start date") String startDate,
        @Schema(description = "end date") String endDate,
        @Schema(description = "is waiting list") Boolean isWaitingList,
        @Schema(description = "filter no start time") Boolean filterNoStartTime,
        @Schema(description = "filter no staff") Boolean filterNoStaff) {}
