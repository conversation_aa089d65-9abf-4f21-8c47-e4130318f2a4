package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeQbSyncInvoice;
import com.moego.server.grooming.params.quickbook.ListQuickBookInvoiceParams;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeQbSyncInvoiceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_invoice
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_invoice
     *
     * @mbg.generated
     */
    int insert(MoeQbSyncInvoice record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_invoice
     *
     * @mbg.generated
     */
    int insertSelective(MoeQbSyncInvoice record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_invoice
     *
     * @mbg.generated
     */
    MoeQbSyncInvoice selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_invoice
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeQbSyncInvoice record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_invoice
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeQbSyncInvoice record);

    MoeQbSyncInvoice selectByGroomingId(
            @Param("businessId") Integer businessId,
            @Param("groomingId") Integer groomingId,
            @Param("realmId") String realmId);

    MoeQbSyncInvoice selectByInvoiceId(@Param("businessId") Integer businessId, @Param("invoiceId") Integer invoiceId);

    List<MoeQbSyncInvoice> selectByBusinessId(@Param("businessId") Integer businessId);

    List<MoeQbSyncInvoice> listInvoice(
            ListQuickBookInvoiceParams params, @Param("limit") Integer limit, @Param("offset") Integer offset);

    Integer countInvoice(ListQuickBookInvoiceParams params);
}
