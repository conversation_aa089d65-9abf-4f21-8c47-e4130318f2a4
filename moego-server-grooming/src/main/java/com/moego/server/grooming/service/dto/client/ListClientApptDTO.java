package com.moego.server.grooming.service.dto.client;

import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.common.dto.BusinessDateTimeDTO;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/7
 */
@Data
public class ListClientApptDTO {

    private Byte status;
    private List<BaseBusinessCustomerIdDTO> customerIdDTOList;
    private Map<Integer, BusinessDateTimeDTO> businessDateTimeDTOMap;
    /**
     * 只查询预约，不查询 booking request
     */
    private Boolean isAppointmentOnly;
}
