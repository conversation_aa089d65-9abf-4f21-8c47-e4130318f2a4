package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingReportThemeConfig;
import com.moego.server.grooming.mapperbean.MoeGroomingReportThemeConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingReportThemeConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_theme_config
     *
     * @mbg.generated
     */
    long countByExample(MoeGroomingReportThemeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_theme_config
     *
     * @mbg.generated
     */
    int deleteByExample(MoeGroomingReportThemeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_theme_config
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_theme_config
     *
     * @mbg.generated
     */
    int insert(MoeGroomingReportThemeConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_theme_config
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingReportThemeConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_theme_config
     *
     * @mbg.generated
     */
    List<MoeGroomingReportThemeConfig> selectByExample(MoeGroomingReportThemeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_theme_config
     *
     * @mbg.generated
     */
    MoeGroomingReportThemeConfig selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_theme_config
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeGroomingReportThemeConfig record,
            @Param("example") MoeGroomingReportThemeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_theme_config
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeGroomingReportThemeConfig record,
            @Param("example") MoeGroomingReportThemeConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_theme_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingReportThemeConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_theme_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingReportThemeConfig record);
}
