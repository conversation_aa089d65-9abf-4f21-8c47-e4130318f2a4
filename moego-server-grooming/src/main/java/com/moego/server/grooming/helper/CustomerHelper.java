package com.moego.server.grooming.helper;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerNoteModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerTagModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeModel;
import com.moego.idl.models.business_customer.v1.BusinessPetNoteModel;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.BatchListBindingPetCodeRequest;
import com.moego.idl.service.business_customer.v1.BatchListBindingPetCodeResponse;
import com.moego.idl.service.business_customer.v1.BatchListCustomerNoteRequest;
import com.moego.idl.service.business_customer.v1.BatchListPetNoteRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerNoteServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerTagServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetCodeServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetNoteServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListCustomerTagRequest;
import com.moego.lib.utils.model.Pair;
import com.moego.server.customer.api.IPetBelongingService;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetPetCodeDTO;
import com.moego.server.customer.dto.PetBelongingDTO;
import jakarta.annotation.Nullable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Component
@RequiredArgsConstructor
public class CustomerHelper {
    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub businessCustomerClient;
    private final BusinessCustomerNoteServiceGrpc.BusinessCustomerNoteServiceBlockingStub customerNoteClient;
    private final BusinessCustomerTagServiceGrpc.BusinessCustomerTagServiceBlockingStub customerTagClient;
    private final BusinessPetNoteServiceGrpc.BusinessPetNoteServiceBlockingStub petNoteClient;
    private final BusinessPetCodeServiceGrpc.BusinessPetCodeServiceBlockingStub petCodeStub;
    private final IPetBelongingService petBelongingClient;
    private final IPetClient iPetClient;

    public Map<Long, BusinessCustomerInfoModel> getCustomerInfos(List<Long> customerIds) {
        customerIds =
                customerIds.stream().filter(k -> k != null && k > 0).distinct().toList();
        if (ObjectUtils.isEmpty(customerIds)) {
            return Map.of();
        }
        List<Set<Long>> sets = CommonUtil.splitListByItemNum(customerIds, 1000);
        Map<Long, BusinessCustomerInfoModel> customerMap = new HashMap<>();
        sets.forEach(set -> {
            var result = businessCustomerClient
                    .batchGetCustomerInfo(BatchGetCustomerInfoRequest.newBuilder()
                            .addAllIds(set)
                            .build())
                    .getCustomersList()
                    .stream()
                    .collect(Collectors.toMap(BusinessCustomerInfoModel::getId, Function.identity(), (a, b) -> a));
            customerMap.putAll(result);
        });

        return customerMap;
    }

    @Nullable
    public BusinessCustomerInfoModel getCustomerInfo(Long customerId) {
        return getCustomerInfos(List.of(customerId)).get(customerId);
    }

    Map<Long, List<BusinessPetNoteModel>> getPetNoteDTOs(List<Long> petIds) {
        petIds = petIds.stream().filter(k -> k != null && k > 0).distinct().toList();
        if (CollectionUtils.isEmpty(petIds)) {
            return new HashMap<>();
        }
        List<Set<Long>> sets = CommonUtil.splitListByItemNum(petIds, 100);
        Map<Long, List<BusinessPetNoteModel>> petNoteMap = new HashMap<>();
        sets.forEach(set -> {
            var petNoteResponse = petNoteClient.batchListPetNote(
                    BatchListPetNoteRequest.newBuilder().addAllPetIds(set).build());
            petNoteResponse.getPetNotesMapMap().forEach((k, v) -> petNoteMap.put(k, v.getNotesList()));
        });

        return petNoteMap;
    }

    public Map<Long, List<String>> getPetNotes(List<Long> petIds) {
        var petNotesDtos = getPetNoteDTOs(petIds);
        Map<Long, List<String>> petNoteMap = new HashMap<>();
        petNotesDtos.forEach((k, v) ->
                petNoteMap.put(k, v.stream().map(BusinessPetNoteModel::getNote).toList()));
        return petNoteMap;
    }

    Map<Long, List<BusinessCustomerNoteModel>> getCustomerNoteDTOs(List<Long> customerIds) {
        customerIds =
                customerIds.stream().filter(k -> k != null && k > 0).distinct().toList();
        if (CollectionUtils.isEmpty(customerIds)) {
            return new HashMap<>();
        }
        List<Set<Long>> sets = CommonUtil.splitListByItemNum(customerIds, 100);
        Map<Long, List<BusinessCustomerNoteModel>> customerNoteMap = new HashMap<>();
        sets.forEach(set -> {
            var customerNoteResponse =
                    customerNoteClient.batchListCustomerNote(BatchListCustomerNoteRequest.newBuilder()
                            .addAllCustomerIds(set.stream().toList())
                            .build());
            customerNoteResponse.getCustomerNotesMapMap().forEach((k, v) -> customerNoteMap.put(k, v.getNotesList()));
        });

        return customerNoteMap;
    }

    public Map<Long, List<String>> getCustomerNotes(List<Long> customerIds) {
        var customerNotesDtos = getCustomerNoteDTOs(customerIds);
        Map<Long, List<String>> customerNoteMap = new HashMap<>();
        customerNotesDtos.forEach((k, v) -> customerNoteMap.put(
                k, v.stream().map(BusinessCustomerNoteModel::getNote).toList()));
        return customerNoteMap;
    }

    Map<Long, BusinessCustomerTagModel> getCustomerTag(Long companyId) {
        var listRequest =
                ListCustomerTagRequest.newBuilder().setCompanyId(companyId).build();
        var listResponse = customerTagClient.listCustomerTag(listRequest);

        return listResponse.getTagsList().stream()
                .collect(Collectors.toMap(BusinessCustomerTagModel::getId, Function.identity(), (a, b) -> a));
    }

    public Map<Long, String> getCustomerTagName(Long companyId) {
        return getCustomerTag(companyId).entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, k -> k.getValue().getName()));
    }

    List<PetBelongingDTO> listPetBelongs(List<Integer> appointmentIds) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return List.of();
        }
        return petBelongingClient.getPetBelongs(appointmentIds);
    }

    public Map<Integer, List<PetBelongingDTO>> getAppointmentPetBelongs(List<Integer> appointmentIds) {
        return listPetBelongs(appointmentIds).stream()
                .collect(Collectors.groupingBy(PetBelongingDTO::getAppointmentId));
    }

    List<CustomerPetPetCodeDTO> listPetInfos(List<Integer> petIds) {
        petIds = petIds.stream().filter(k -> k != null && k > 0).distinct().toList();
        if (CollectionUtils.isEmpty(petIds)) {
            return List.of();
        }
        return iPetClient.getCustomerPetInfoListByIdList(petIds);
    }

    public Map<Integer, CustomerPetPetCodeDTO> getPetInfoMap(List<Integer> petIds) {
        return listPetInfos(petIds).stream()
                .collect(Collectors.toMap(CustomerPetPetCodeDTO::getPetId, Function.identity(), (a, b) -> a));
    }

    public Pair<List<BatchListBindingPetCodeResponse.Binding>, List<BusinessPetCodeModel>> listPetCodeBindings(
            Long companyId, List<Long> petIds) {
        if (companyId == null || ObjectUtils.isEmpty(petIds)) {
            return Pair.of(List.of(), List.of());
        }
        var response = petCodeStub.batchListBindingPetCode(BatchListBindingPetCodeRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllPetIds(petIds)
                .build());
        return Pair.of(response.getPetCodeBindingsList(), response.getPetCodesList());
    }
}
