package com.moego.server.grooming.web;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.common.response.ResponseResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.utils.StringUtils;
import com.moego.server.grooming.mapper.MoeZipcodeMapper;
import com.moego.server.grooming.mapper.po.BusinessCompanyPO;
import com.moego.server.grooming.mapperbean.MoeZipcode;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.grooming.service.ob.OBLandingPageConfigService;
import jakarta.validation.constraints.Size;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/grooming/bookOnline/zipcode")
@Validated
public class MoeZipcodeController {

    private final MoeZipcodeMapper moeZipcodeMapper;
    private final OBLandingPageConfigService landingPageConfigService;

    @GetMapping("/search")
    @Auth(AuthType.ACCOUNT)
    public ResponseResult<List<MoeZipcode>> searchByPrefix(@RequestParam String prefix) {
        String utf8mb3Chars = StringUtils.filterNonUtf8mb3Chars(prefix);
        List<MoeZipcode> codes = moeZipcodeMapper.searchByZipcodePrefix(utf8mb3Chars);
        return ResponseResult.success(codes);
    }

    @GetMapping("/details")
    @Auth(AuthType.ACCOUNT)
    public ResponseResult<List<MoeZipcode>> selectByZipCodes(@RequestParam List<String> zipCodes) {
        if (CollectionUtils.isEmpty(zipCodes)) {
            return ResponseResult.success(Collections.emptyList());
        }
        List<MoeZipcode> zipcodes = moeZipcodeMapper.selectByZipcodeList(zipCodes);
        return ResponseResult.success(zipcodes);
    }

    @GetMapping
    @Auth(AuthType.ANONYMOUS)
    public List<MoeZipcode> selectByZipCodes(
            OBAnonymousParams obParam, @RequestParam @Size(max = 500) List<@Length(max = 50) String> zipCodes) {

        checkParam(obParam);

        return !ObjectUtils.isEmpty(zipCodes) ? moeZipcodeMapper.selectByZipcodeList(zipCodes) : List.of();
    }

    private void checkParam(OBAnonymousParams obParam) {
        Integer businessId = Optional.ofNullable(landingPageConfigService.getBusinessIdAndCompanyIdByAnonymous(obParam))
                .map(BusinessCompanyPO::getBusinessId)
                .orElse(null);
        if (businessId == null) {
            throw bizException(Code.CODE_BOOK_ONLINE_SITE_NOT_FOUND);
        }
    }
}
