package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeInvoiceDeposit;

public interface MoeInvoiceDepositMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_invoice_deposit
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_invoice_deposit
     *
     * @mbg.generated
     */
    int insert(MoeInvoiceDeposit record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_invoice_deposit
     *
     * @mbg.generated
     */
    int insertSelective(MoeInvoiceDeposit record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_invoice_deposit
     *
     * @mbg.generated
     */
    MoeInvoiceDeposit selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_invoice_deposit
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeInvoiceDeposit record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_invoice_deposit
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeInvoiceDeposit record);

    MoeInvoiceDeposit selectByDeGuid(String deGuid);

    MoeInvoiceDeposit selectByInvoiceId(Integer invoiceId);
}
