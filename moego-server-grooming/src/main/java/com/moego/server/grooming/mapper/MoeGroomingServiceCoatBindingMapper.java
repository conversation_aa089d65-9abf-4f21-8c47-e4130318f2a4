package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingServiceCoatBinding;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingServiceCoatBindingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_coat_binding
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_coat_binding
     *
     * @mbg.generated
     */
    int insert(MoeGroomingServiceCoatBinding record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_coat_binding
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingServiceCoatBinding record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_coat_binding
     *
     * @mbg.generated
     */
    MoeGroomingServiceCoatBinding selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_coat_binding
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingServiceCoatBinding record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_coat_binding
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingServiceCoatBinding record);

    int insertOrUpdate(MoeGroomingServiceCoatBinding record);

    int deleteByBusinessIdAndServiceId(@Param("businessId") Integer businessId, @Param("serviceId") Integer serviceId);

    List<MoeGroomingServiceCoatBinding> selectByBusinessIdAndServiceId(
            @Param("businessId") Integer businessId, @Param("serviceId") Integer serviceId);

    List<MoeGroomingServiceCoatBinding> selectByBusinessId(@Param("businessId") Integer businessId);

    List<MoeGroomingServiceCoatBinding> selectByCompanyId(@Param("companyId") Long companyId);

    List<MoeGroomingServiceCoatBinding> selectByCompanyIdAndCoatId(
            @Param("companyId") Long companyId, @Param("coatId") Integer coatId);
}
