package com.moego.server.grooming.mapper;

import com.moego.lib.common.auth.AuthContext;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentPetDetailDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.GroomingPetServiceListInfoDTO;
import com.moego.server.grooming.dto.PetDetailInvoiceDTO;
import com.moego.server.grooming.dto.PetDetailServiceDTO;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import com.moego.server.grooming.dto.StaffConflictDTO;
import com.moego.server.grooming.mapper.po.GroomingStaffIdListPO;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetailExample;
import com.moego.server.grooming.params.report.DescribePetDetailReportsParams;
import com.moego.server.grooming.service.dto.ob.OBClientApptDTO;
import com.moego.server.grooming.web.vo.PetDetailMonthlyQueryVo;
import com.moego.server.grooming.web.vo.PetDetailQueryVo;
import java.math.BigDecimal;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * PetDetailMapperProxy - 代理类用于白名单和非白名单商家的分流调用
 * 白名单商家使用 fulfillment 远程调用，非白名单商家使用原有的 mapper 调用
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PetDetailMapperProxy {

    private final MoeGroomingPetDetailMapper petDetailMapper;
    // TODO: 注入 fulfillment 远程服务
    // private final FulfillmentPetDetailService fulfillmentPetDetailService;

    /**
     * 判断是否为白名单商家，使用新的 fulfillment flow
     * 直接从 AuthContext 获取 companyId
     * TODO: 实现具体的白名单判断逻辑
     *
     * @return true if company is in fulfillment flow whitelist
     */
    private boolean isFulfillmentFlow() {
        //        Long companyId = AuthContext.get().companyId();
        //        if (companyId == null) {
        //            log.warn("CompanyId is null in AuthContext, using original mapper");
        //            return false;
        //        }
        // TODO: 实现白名单判断逻辑
        return false;
    }

    // ==================== MyBatis Generator 生成的基础方法 ====================

    public long countByExample(MoeGroomingPetDetailExample example) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for countByExample, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.countByExample(example);
    }

    public int insertSelective(MoeGroomingPetDetail record) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for insertSelective, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.insertSelective(record);
    }

    public List<MoeGroomingPetDetail> selectByExample(MoeGroomingPetDetailExample example) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectByExample, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.selectByExample(example);
    }

    public MoeGroomingPetDetail selectByPrimaryKey(Integer id) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectByPrimaryKey, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.selectByPrimaryKey(id);
    }

    public int updateByExampleSelective(MoeGroomingPetDetail record, MoeGroomingPetDetailExample example) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for updateByExampleSelective, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.updateByExampleSelective(record, example);
    }

    public int updateByPrimaryKeySelective(MoeGroomingPetDetail record) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for updateByPrimaryKeySelective, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.updateByPrimaryKeySelective(record);
    }

    // ==================== 自定义方法 ====================

    public Integer insertSelectiveBatch(List<MoeGroomingPetDetail> moeGroomingPetDetails) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for insertSelectiveBatch, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.insertSelectiveBatch(moeGroomingPetDetails);
    }

    public Integer deleteByAppointmentId(Integer appointmentId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for deleteByAppointmentId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.deleteByAppointmentId(appointmentId);
    }

    public List<GroomingPetDetailDTO> queryPetDetailByGroomingId(Integer groomingId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryPetDetailByGroomingId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryPetDetailByGroomingId(groomingId);
    }

    public List<GroomingPetDetailDTO> queryPetDetailByServiceItems(Integer groomingId, List<Integer> serviceItems) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryPetDetailByServiceItems, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryPetDetailByServiceItems(groomingId, serviceItems);
    }

    public List<GroomingPetServiceListInfoDTO> queryPetDetailListForGroomingOnly(PetDetailQueryVo petDetailQueryVo) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryPetDetailListForGroomingOnly, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryPetDetailListForGroomingOnly(petDetailQueryVo);
    }

    public List<GroomingPetServiceListInfoDTO> queryPetDetailListIncludeHybrid(PetDetailQueryVo petDetailQueryVo) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryPetDetailListIncludeHybrid, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryPetDetailListIncludeHybrid(petDetailQueryVo);
    }

    public List<GroomingPetServiceListInfoDTO> queryPetDetailListMonthlyForGroomingOnly(
            PetDetailMonthlyQueryVo petDetailMonthlyQueryVo) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryPetDetailListMonthlyForGroomingOnly, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryPetDetailListMonthlyForGroomingOnly(petDetailMonthlyQueryVo);
    }

    public List<GroomingPetServiceListInfoDTO> queryPetDetailListMonthlyIncludeHybrid(
            PetDetailMonthlyQueryVo petDetailMonthlyQueryVo) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryPetDetailListMonthlyIncludeHybrid, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryPetDetailListMonthlyIncludeHybrid(petDetailMonthlyQueryVo);
    }

    public List<MoeGroomingPetDetail> queryPetDetailByAppointmentDateAndStaffId(
            Integer businessId, String appointmentDate, Integer staffId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryPetDetailByAppointmentDateAndStaffId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryPetDetailByAppointmentDateAndStaffId(businessId, appointmentDate, staffId);
    }

    public List<StaffConflictDTO> queryPetDetailByAppointmentDatesAndStaffIds(
            Integer businessId,
            List<String> appointmentDates,
            List<Integer> staffIds,
            List<Integer> exceptIds,
            Integer exceptRepeatId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryPetDetailByAppointmentDatesAndStaffIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryPetDetailByAppointmentDatesAndStaffIds(
                businessId, appointmentDates, staffIds, exceptIds, exceptRepeatId);
    }

    public List<MoeGroomingPetDetail> queryPetDetailByAppointmentDateAndStaffIdBlock(
            String appointmentDate, Integer businessId, Integer staffId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryPetDetailByAppointmentDateAndStaffIdBlock, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryPetDetailByAppointmentDateAndStaffIdBlock(appointmentDate, businessId, staffId);
    }

    public List<GroomingPetDetailDTO> queryPetDetailByGroomingIds(List<Integer> groomingIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryPetDetailByGroomingIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryPetDetailByGroomingIds(groomingIds);
    }

    public List<GroomingPetDetailDTO> queryHasStaffPetDetailByGroomingIds(
            List<Integer> groomingIds, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryHasStaffPetDetailByGroomingIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryHasStaffPetDetailByGroomingIds(groomingIds, startDate, endDate);
    }

    public List<GroomingPetDetailDTO> queryAllPetDetailByGroomingIds(List<Integer> groomingIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryAllPetDetailByGroomingIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryAllPetDetailByGroomingIds(groomingIds);
    }

    public Integer deletePetDetail(List<Integer> ids) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for deletePetDetail, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.deletePetDetail(ids);
    }

    public Integer deleteByGroomingIds(List<Integer> groomingIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for deleteByGroomingIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.deleteByGroomingIds(groomingIds);
    }

    public List<PetDetailServiceDTO> queryPetDetailServiceByGroomingId(Integer groomingId, Integer petId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryPetDetailServiceByGroomingId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryPetDetailServiceByGroomingId(groomingId, petId);
    }

    public List<PetDetailInvoiceDTO> queryPetDetailInvoiceByGroomingId(Integer groomingId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryPetDetailInvoiceByGroomingId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryPetDetailInvoiceByGroomingId(groomingId);
    }

    public List<PetDetailInvoiceDTO> queryPetDetailInvoiceByCidBidGid(
            Long companyId, Integer businessId, Integer groomingId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryPetDetailInvoiceByCidBidGid, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryPetDetailInvoiceByCidBidGid(companyId, businessId, groomingId);
    }

    public List<MoeGroomingPetDetail> queryPetDetailCountByGroomingId(Integer groomingId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryPetDetailCountByGroomingId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryPetDetailCountByGroomingId(groomingId);
    }

    public List<Integer> queryTransferAppointment(
            Integer businessId, Integer sourceStaffId, String nowDate, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryTransferAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryTransferAppointment(businessId, sourceStaffId, nowDate, endTimes);
    }

    public int transferAppointment(
            Integer sourceStaffId, Integer targetStaffId, Long updateTime, List<Integer> groomingIdList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for transferAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.transferAppointment(sourceStaffId, targetStaffId, updateTime, groomingIdList);
    }

    public List<CustomerGroomingAppointmentPetDetailDTO> queryCustomerAppointmentPetDetail(Integer groomingId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryCustomerAppointmentPetDetail, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryCustomerAppointmentPetDetail(groomingId);
    }

    public List<OBClientApptDTO.OBClientApptPetDetailDTO> getApptPetDetail(Integer groomingId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getApptPetDetail, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.getApptPetDetail(groomingId);
    }

    public List<MoeGroomingAppointment> selectUpcomingByCustomerId(
            Long companyId, Integer customerId, String appointmentDate, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectUpcomingByCustomerId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.selectUpcomingByCustomerId(companyId, customerId, appointmentDate, endTimes);
    }

    public List<MoeGroomingPetDetail> selectPetDetailByGroomingIdList(List<Integer> groomingIdList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectPetDetailByGroomingIdList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.selectPetDetailByGroomingIdList(groomingIdList);
    }

    public List<MoeGroomingPetDetail> selectByAppointmentIdList(List<Integer> appointmentIdList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectByAppointmentIdList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.selectByAppointmentIdList(appointmentIdList);
    }

    public List<MoeGroomingPetDetail> queryByBusinessIdAndDateOrderByTime(Integer businessId, String date) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryByBusinessIdAndDateOrderByTime, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryByBusinessIdAndDateOrderByTime(businessId, date);
    }

    public List<SmartScheduleGroomingDetailsDTO> queryInProgressByBusinessIdBetweenDates(
            Integer businessId, String startDate, String endDate, List<Integer> staffIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryInProgressByBusinessIdBetweenDates, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryInProgressByBusinessIdBetweenDates(businessId, startDate, endDate, staffIds);
    }

    public List<Integer> queryStaffIdByGroomingId(Integer groomingId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryStaffIdByGroomingId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryStaffIdByGroomingId(groomingId);
    }

    public List<GroomingStaffIdListPO> queryStaffIdByGroomingIds(List<Integer> groomingIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryStaffIdByGroomingIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryStaffIdByGroomingIds(groomingIds);
    }

    public List<Integer> queryStaffIdListByDateRange(Integer businessId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryStaffIdListByDateRange, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryStaffIdListByDateRange(businessId, startDate, endDate);
    }

    public List<GroomingPetDetailDTO> queryUpcomingApptsByPetId(
            Integer businessId, String startDate, Integer petId, Integer serviceId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryUpcomingApptsByPetId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryUpcomingApptsByPetId(businessId, startDate, petId, serviceId);
    }

    public List<Integer> queryUpcomingApptsByServiceId(
            Integer businessId, String appointmentDate, Integer endTimes, Integer serviceId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryUpcomingApptsByServiceId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryUpcomingApptsByServiceId(businessId, appointmentDate, endTimes, serviceId);
    }

    public BigDecimal countApptRevenue(List<Integer> groomingIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for countApptRevenue, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.countApptRevenue(groomingIds);
    }

    public BigDecimal countATVRecentlyDays(Integer businessId, Long startTime, Long endTime) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for countATVRecentlyDays, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.countATVRecentlyDays(businessId, startTime, endTime);
    }

    public List<GroomingPetServiceListInfoDTO> queryFinishedPetDetail(
            List<Integer> businessIdList, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryFinishedPetDetail, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryFinishedPetDetail(businessIdList, startDate, endDate);
    }

    public List<MoeGroomingPetDetail> describePetDetailReports(DescribePetDetailReportsParams params) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for describePetDetailReports, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.describePetDetailReports(params);
    }

    public List<GroomingPetDetailDTO> queryNormalPetDetailDTOByIds(List<Long> petDetailIdList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryNormalPetDetailDTOByIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.queryNormalPetDetailDTOByIds(petDetailIdList);
    }

    public List<MoeGroomingPetDetail> selectNormalPetDetailByIdList(List<Long> petDetailIdList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectNormalPetDetailByIdList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.selectNormalPetDetailByIdList(petDetailIdList);
    }

    public Integer batchInsertBlockPetDetail(List<MoeGroomingPetDetail> moeGroomingPetDetails) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for batchInsertBlockPetDetail, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailMapper.batchInsertBlockPetDetail(moeGroomingPetDetails);
    }
}
