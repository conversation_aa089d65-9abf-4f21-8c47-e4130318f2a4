package com.moego.server.grooming.server;

import com.google.common.collect.ImmutableMap;
import com.moego.server.grooming.api.IGroomingLeaderboardServiceBase;
import com.moego.server.grooming.dto.LeaderboardRankInfoDTO;
import com.moego.server.grooming.dto.report.LeaderboardStaffReportDTO;
import com.moego.server.grooming.service.GroomingLeaderboardService;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
public class GroomingLeaderboardServer extends IGroomingLeaderboardServiceBase {

    @Autowired
    private GroomingLeaderboardService groomingLeaderboardService;

    @FunctionalInterface
    interface TrFunction<T, U, V, R> {
        R apply(T t, U u, V v);
    }

    private final Map<String, TrFunction<Integer, String, String, List<LeaderboardRankInfoDTO>>> functionMap =
            ImmutableMap.of(
                    "service",
                    (businessId, startDate, endDate) ->
                            groomingLeaderboardService.getServiceRankForLeaderboard(businessId, startDate, endDate),
                    "client",
                    (businessId, startDate, endDate) ->
                            groomingLeaderboardService.getClientRankForLeaderboard(businessId, startDate, endDate),
                    "petBreed",
                    (businessId, startDate, endDate) ->
                            groomingLeaderboardService.getPetBreedRankForLeaderboard(businessId, startDate, endDate));

    @Override
    public List<LeaderboardStaffReportDTO> getStaffForLeaderboard(
            Integer businessId, String startDate, String endDate, List<Integer> staffIdList) {
        return groomingLeaderboardService.getStaffForLeaderboard(businessId, startDate, endDate, staffIdList);
    }

    @Override
    public List<LeaderboardRankInfoDTO> getRankForLeaderboard(
            Integer businessId, String startDate, String endDate, String type) {
        return functionMap.get(type).apply(businessId, startDate, endDate);
    }
}
