package com.moego.server.grooming.web.vo.ob;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.server.customer.dto.AdditionalContactDTO;
import com.moego.server.grooming.dto.AutoAssignDTO;
import com.moego.server.grooming.dto.DiscountCodeDTO;
import com.moego.server.grooming.web.vo.client.StaffDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 * @since 2023/6/30
 */
@Builder(toBuilder = true)
public record OBRequestDetailVO(
        @Schema(description = "appointment id") Integer apptId,
        Long createTime,
        String appointmentDate,
        Integer appointmentStartTime,
        Boolean noStartTime,
        @Schema(description = "paid status, 1: fully paid, 2: not paid, 3: partially paid") Byte isPaid,
        Byte status,
        Byte bookOnlineStatus,
        Byte isWaitingList,
        String additionalNote,
        @Schema(description = "has customer profile request update") boolean hasRequestUpdate,
        StaffDetailVO staff,
        OBClientDetailVO customer,
        AddressDetailVO address,
        List<OBPetDetailVO> pets,
        List<OBServiceDetailVO> services,
        OBPrepayDetailVO prepay,
        String sourcePlatform,
        Boolean isAutoAccept,
        AutoAssignDTO autoAssign,
        DiscountCodeDTO discountCode,
        List<Integer> serviceItemTypes,
        AdditionalContactDTO emergencyContact) {
    public record OBPrepayDetailVO(
            BigDecimal paidAmount,
            BigDecimal prepaidAmount,
            BigDecimal refundAmount,
            Double prepayRate,
            Byte prepayStatus) {}

    public record AddressDetailVO(
            Integer addressId,
            String address1,
            String address2,
            String city,
            String state,
            String zipcode,
            String country,
            String lat,
            String lng) {}

    public record OBServiceDetailVO(
            Integer serviceId,
            String serviceName,
            Byte serviceType,
            Integer duration,
            BigDecimal price,
            @Schema(type = "integer", format = "int32") @JsonFormat(shape = JsonFormat.Shape.NUMBER)
                    ServiceOverrideType priceOverrideType,
            @Schema(type = "integer", format = "int32") @JsonFormat(shape = JsonFormat.Shape.NUMBER)
                    ServiceOverrideType durationOverrideType) {}

    @Builder
    public record QuestionAnswerVO(String key, String question, String answer) {}
}
