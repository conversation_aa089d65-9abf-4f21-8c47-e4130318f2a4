package com.moego.server.grooming.server;

import com.moego.server.grooming.api.IAutoAssignServiceBase;
import com.moego.server.grooming.dto.AutoAssignDTO;
import com.moego.server.grooming.mapstruct.AutoAssignConverter;
import com.moego.server.grooming.service.AutoAssignService;
import java.util.Collection;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class AutoAssignServer extends IAutoAssignServiceBase {

    private final AutoAssignService autoAssignService;

    @Override
    public AutoAssignDTO getAutoAssign(Integer appointmentId) {
        return AutoAssignConverter.INSTANCE.entityToDTO(autoAssignService.getAutoAssign(appointmentId));
    }

    @Override
    public List<AutoAssignDTO> listAutoAssign(Collection<Integer> appointmentIds) {
        return autoAssignService.listAutoAssign(appointmentIds).stream()
                .map(AutoAssignConverter.INSTANCE::entityToDTO)
                .toList();
    }
}
