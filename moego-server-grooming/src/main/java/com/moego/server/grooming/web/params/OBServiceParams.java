package com.moego.server.grooming.web.params;

import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class OBServiceParams {

    @Schema(description = "existing client id")
    private Integer customerId;

    @Deprecated // todo: remove businessId
    @Schema(description = "business id")
    private Integer businessId;

    // @NotBlank todo: open this after removing businessId
    @Deprecated
    @Schema(description = "business name")
    private String businessName;

    @NotEmpty
    @Schema(description = "filter service by pet info")
    private List<@NotNull OBPetDataDTO> petDataList;
}
