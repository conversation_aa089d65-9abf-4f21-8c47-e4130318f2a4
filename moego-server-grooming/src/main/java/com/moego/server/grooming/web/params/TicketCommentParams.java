package com.moego.server.grooming.web.params;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

@Builder
public record TicketCommentParams(
        @Schema(description = "business id", hidden = true) Integer businessId,
        @Schema(description = "staff id", hidden = true) Integer staffId,
        @Schema(description = "pet id, 传空时表示 general comment") Integer appointmentId,
        Long petId,
        String content) {}
