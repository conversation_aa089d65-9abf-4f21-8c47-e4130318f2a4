package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageGallery;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeBookOnlineLandingPageGalleryMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_gallery
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_gallery
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlineLandingPageGallery record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_gallery
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlineLandingPageGallery record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_gallery
     *
     * @mbg.generated
     */
    MoeBookOnlineLandingPageGallery selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_gallery
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlineLandingPageGallery record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_gallery
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlineLandingPageGallery record);

    List<MoeBookOnlineLandingPageGallery> listGalleryByCondition(MoeBookOnlineLandingPageGallery gallery);

    int batchInsert(List<MoeBookOnlineLandingPageGallery> galleryList);

    int deleteByBusinessId(Integer businessId);

    List<MoeBookOnlineLandingPageGallery> listGalleryMinSortImageByBusinessId(
            @Param("businessIdList") List<Integer> businessIdList);
}
