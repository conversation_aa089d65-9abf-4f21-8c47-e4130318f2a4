package com.moego.server.grooming.service.dto;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class AppointmentWithInvoice {

    private Integer id;

    private String orderId;

    private Integer businessId;

    private Integer customerId;

    private String appointmentDate;

    private Integer appointmentStartTime;

    private Integer appointmentEndTime;

    private Byte isWaitingList;

    private Integer moveWaitingBy;

    private Long confirmedTime;

    private Long checkInTime;

    private Long checkOutTime;

    private Long canceledTime;

    private Byte status;

    private Byte bookOnlineStatus;

    private Integer repeatId;

    private Byte isPaid;

    private String colorCode;

    private Byte noShow;

    private Byte isPustNotification;

    private Byte cancelByType;

    private Integer cancelBy;

    private Byte confirmByType;

    private Integer confirmBy;

    private Integer createdById;

    private Byte outOfArea;

    private Long createTime;

    private Long updateTime;

    private Integer source;

    private Integer isBlock;

    private Integer isDeprecate;

    private BigDecimal noShowFee;

    private Integer invoiceId;

    private String type;

    private BigDecimal subTotalAmount;

    private BigDecimal discountAmount;

    private BigDecimal discountRate;

    private String discountType;

    private BigDecimal discountedSubTotalAmount;

    private BigDecimal tipsAmount;

    private BigDecimal tipsRate;

    private String tipsType;

    private BigDecimal taxAmount;

    private BigDecimal totalAmount;

    private BigDecimal paymentAmount;

    private BigDecimal paidAmount;

    private BigDecimal remainAmount;

    private BigDecimal refundedAmount;

    private Integer invoiceStatus;

    private Integer createBy;

    private Integer updateBy;
}
