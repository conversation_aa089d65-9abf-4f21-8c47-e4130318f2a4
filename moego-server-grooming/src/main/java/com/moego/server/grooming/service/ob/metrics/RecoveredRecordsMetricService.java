package com.moego.server.grooming.service.ob.metrics;

import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Service
@RequiredArgsConstructor
public class RecoveredRecordsMetricService implements IOBMetricsService {

    private final RecoverableRecordsMetricService recoverableRecordsMetricService;
    private final MoeBookOnlineAbandonRecordMapper abandonRecordMapper;

    @Override
    public Integer sumMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return abandonRecordMapper.countRecoveredRecords(
                timeRangeDTO.businessId(),
                timeRangeDTO.startTime(),
                timeRangeDTO.endTime(),
                OBStepEnum.listRecoverableSteps());
    }

    @Override
    public Object proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        // recovered records / recoverable records
        Integer recoveredRecords = this.sumMetrics(timeRangeDTO);
        Integer recoverableRecords = recoverableRecordsMetricService.sumMetrics(timeRangeDTO);
        if (recoverableRecords == 0) {
            return "";
        }
        return BigDecimal.valueOf(recoveredRecords)
                .divide(BigDecimal.valueOf(recoverableRecords), 2, RoundingMode.HALF_UP);
    }

    @Override
    public OBMetricsEnum getMetricsName() {
        return OBMetricsEnum.recovered_records;
    }
}
