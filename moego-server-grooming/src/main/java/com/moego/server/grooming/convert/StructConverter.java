/*
 * Copied from svc-appointment.
 * @since 2023-05-29 15:23:19
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.grooming.convert;

import com.moego.common.constant.CommonConstant;
import com.moego.idl.utils.v1.Int64ListValue;
import com.moego.idl.utils.v1.StringListValue;
import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.util.JsonUtil;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.springframework.util.StringUtils;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface StructConverter {
    default List<Long> toList(Int64ListValue value) {
        if (value == null) {
            return null;
        }
        return value.getValuesList();
    }

    default Int64ListValue toInt64ListValue(List<Long> value) {
        if (value == null) {
            return null;
        }
        return Int64ListValue.newBuilder().addAllValues(value).build();
    }

    default Int64ListValue toInt64ListValue(Long... value) {
        return toInt64ListValue(Arrays.asList(value));
    }

    default List<String> toList(StringListValue value) {
        if (value == null) {
            return null;
        }
        return value.getValuesList();
    }

    default StringListValue toStringListValue(List<String> value) {
        if (value == null) {
            return null;
        }
        return StringListValue.newBuilder().addAllValues(value).build();
    }

    default StringListValue toStringListValue(String... value) {
        return toStringListValue(Arrays.asList(value));
    }

    default boolean toBoolean(Byte value) {
        if (value == null) {
            return false;
        }
        return value.equals(CommonConstant.ENABLE);
    }

    default Map<String, String> toMap(String str) {
        if (!StringUtils.hasText(str)) {
            return Map.of();
        }
        return JsonUtil.toBean(str, new TypeRef<>() {});
    }

    default String toString(List<String> list) {
        if (list == null) {
            return null;
        }
        return JsonUtil.toJson(list);
    }
}
