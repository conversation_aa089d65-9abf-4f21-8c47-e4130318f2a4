package com.moego.server.grooming.service.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.moego.server.grooming.dto.OBAvailableTimeSlotDetailDTO;
import com.moego.server.grooming.dto.ss.ScheduleTimeSlot;
import com.moego.server.grooming.service.dto.ob.PetAvailableDTO;
import io.swagger.v3.oas.annotations.Hidden;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class OBAvailableTimeStaffDto {

    private Integer id;
    private String firstName;
    private String lastName;
    private OBAvailableTimeStaffAvailableTimeDto availableTime;

    @Hidden
    private List<ScheduleTimeSlot> availableRange;

    @Hidden
    @JsonIgnore
    private Map<Integer /* start time */, PetAvailableDTO /* pet available */> timeToAvailablePet;

    @Hidden
    @JsonIgnore
    private PetAvailableDTO dailyAvailablePet;

    @Hidden
    @JsonIgnore
    private List<OBAvailableTimeSlotDetailDTO> slotDetails;
}
