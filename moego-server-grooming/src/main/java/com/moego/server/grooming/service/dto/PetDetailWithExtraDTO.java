package com.moego.server.grooming.service.dto;

import com.moego.server.grooming.mapperbean.AppointmentPetFeeding;
import com.moego.server.grooming.mapperbean.AppointmentPetMedication;
import com.moego.server.grooming.mapperbean.AppointmentPetScheduleSetting;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import java.util.List;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/3/24
 */
@Builder
@Getter
public class PetDetailWithExtraDTO {
    private MoeGroomingPetDetail petDetail;

    private List<FeedingScheduleDTO> feedings;

    private List<MedicationScheduleDTO> medications;

    @Builder
    @Getter
    public static class FeedingScheduleDTO {
        private AppointmentPetFeeding feeding;
        private List<AppointmentPetScheduleSetting> schedules;
    }

    @Builder
    @Getter
    public static class MedicationScheduleDTO {
        private AppointmentPetMedication medication;
        private List<AppointmentPetScheduleSetting> schedules;
    }
}
