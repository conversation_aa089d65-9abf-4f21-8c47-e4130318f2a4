package com.moego.server.grooming.service.dto;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class UsedPackageWithPrice {

    private Integer id;

    private Integer invoiceId;

    private Integer invoiceItemId;

    private Integer packageId;

    private Integer serviceId;

    private String packageName;

    private String serviceName;

    private Integer quantity;

    private Integer businessId;

    private Integer categoryId;

    private BigDecimal price;
}
