package com.moego.server.grooming.mapper;

import com.moego.common.enums.groomingreport.GroomingReportStatusEnum;
import com.moego.server.grooming.mapperbean.MoeGroomingReport;
import com.moego.server.grooming.mapperbean.MoeGroomingReportExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MoeGroomingReportMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    long countByExample(MoeGroomingReportExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    int deleteByExample(MoeGroomingReportExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    int insert(MoeGroomingReport record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingReport record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    List<MoeGroomingReport> selectByExample(MoeGroomingReportExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    MoeGroomingReport selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeGroomingReport record, @Param("example") MoeGroomingReportExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MoeGroomingReport record, @Param("example") MoeGroomingReportExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingReport record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingReport record);

    int initGroomingReports(@Param("groomingReportList") List<MoeGroomingReport> groomingReportList);

    MoeGroomingReport selectBusinessDefaultReport(Integer businessId);

    MoeGroomingReport selectByGroomingIdAndPetId(
            @Param("businessId") Integer businessId,
            @Param("groomingId") Integer groomingId,
            @Param("petId") Integer petId);

    List<MoeGroomingReport> selectByGroomingId(
            @Param("businessId") Integer businessId, @Param("groomingId") Integer groomingId);

    List<MoeGroomingReport> queryList(
            @Param("businessId") Integer businessId,
            @Param("groomingIdList") List<Integer> groomingIdList,
            @Param("statuses") List<GroomingReportStatusEnum> statuses);

    MoeGroomingReport selectByUuid(@Param("uuid") String uuid);

    List<MoeGroomingReport> selectByBusinessIdAndIds(
            @Param("businessId") Integer businessId, @Param("ids") List<Integer> ids);

    int batchUpdateGroomingReportStatus(
            @Param("businessId") Integer businessId,
            @Param("ids") List<Integer> ids,
            @Param("status") String status,
            @Param("updateBy") Integer updateBy);

    int addUpGroomingReportOpenedCount(@Param("uuid") String uuid);
}
