package com.moego.server.grooming.service.dto.ob;

import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.server.business.dto.AppointmentPetIdDTO;
import com.moego.server.business.dto.PetSizeDTO;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.MoePetBreedDTO;
import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OBPetLimitFilterDTO {

    /**
     * 当前预约所有 pet 的 weight 及对应数量
     */
    private Map<BigDecimal, Long> currentSizeMap = new HashMap<>();

    /**
     * 当前预约所有 pet 的 type 及对应数量
     */
    private Map<Integer, Long> currentTypeMap = new HashMap<>();

    /**
     * 当前预约所有 pet 的 breed 及对应数量
     */
    private Map<String, Long> currentBreedMap = new HashMap<>();

    /**
     * 当前预约所有 service 及对应数量
     */
    private Map<Long, Long> currentServiceMap = new HashMap<>();

    /**
     * 当前预约每个 staff 负责的 service 及对应数量
     */
    private Map<Integer, Map<Long, Long>> currentStaffServiceMap = new HashMap<>();

    /**
     * 日期及对应的预约数据
     */
    private Map<String, List<AppointmentPetIdDTO>> petAppointmentMap = new HashMap<>();

    /**
     * （当前时间段已有预约的）petId 及对应的 pet 数据
     */
    private Map<Integer, CustomerPetDetailDTO> petIdDetailMap = new HashMap<>();

    /**
     * 当前商家所有 breedId 对应的数据
     */
    private Map<Integer, MoePetBreedDTO> petBreedMap = new HashMap<>();

    /**
     * 当前商家所有的 pet size 对应的数据
     */
    private Map<Long, PetSizeDTO> petSizeMap = new HashMap<>();

    /*
     * 当前商家所有的 service 对应的数据
     */
    private Map<Long, ServiceModel> serviceMap = new HashMap<>();

    /**
     * pet index sub list
     */
    private List<Set<Integer>> petIndexSubList = new ArrayList<>();

    /**
     * pet index map
     */
    private Map<Integer, OBPetDataDTO> petIndexMap = new HashMap<>();
}
