package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeQbConnect;
import com.moego.server.grooming.mapperbean.MoeQbConnectExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeQbConnectMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_connect
     *
     * @mbg.generated
     */
    long countByExample(MoeQbConnectExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_connect
     *
     * @mbg.generated
     */
    int deleteByExample(MoeQbConnectExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_connect
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_connect
     *
     * @mbg.generated
     */
    int insert(MoeQbConnect record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_connect
     *
     * @mbg.generated
     */
    int insertSelective(MoeQbConnect record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_connect
     *
     * @mbg.generated
     */
    List<MoeQbConnect> selectByExample(MoeQbConnectExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_connect
     *
     * @mbg.generated
     */
    MoeQbConnect selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_connect
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") MoeQbConnect record, @Param("example") MoeQbConnectExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_connect
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MoeQbConnect record, @Param("example") MoeQbConnectExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_connect
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeQbConnect record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_connect
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeQbConnect record);

    @Deprecated
    List<MoeQbConnect> selectByBusinessId(Integer businessId);

    List<MoeQbConnect> selectByRealmId(@Param("realmId") String realmId, @Param("connectId") Integer connectId);

    int updateCancelByRealmId(@Param("realmId") String realmId, @Param("updateTime") Long updateTime);
}
