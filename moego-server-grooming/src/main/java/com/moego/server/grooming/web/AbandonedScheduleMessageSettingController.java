package com.moego.server.grooming.web;

import static com.moego.lib.common.auth.AuthType.BUSINESS;
import static com.moego.server.grooming.dto.AbandonedScheduleMessageSettingDTO.ClientType;
import static com.moego.server.grooming.dto.AbandonedScheduleMessageSettingDTO.SendOutType;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.mapperbean.AbandonedScheduleMessageSetting;
import com.moego.server.grooming.mapstruct.AbandonedScheduleMessageSettingConverter;
import com.moego.server.grooming.service.AbandonedScheduleMessageSettingService;
import com.moego.server.grooming.web.vo.AbandonedScheduleMessageSettingVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import java.time.DayOfWeek;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/grooming/abandoned-schedule-message-setting")
public class AbandonedScheduleMessageSettingController {

    private final AbandonedScheduleMessageSettingService abandonedScheduleMessageSettingService;

    @GetMapping
    @Auth(BUSINESS)
    public AbandonedScheduleMessageSettingVO getAbandonedScheduleMessageSetting() {
        Integer businessId = AuthContext.get().getBusinessId();
        AbandonedScheduleMessageSetting entity = abandonedScheduleMessageSettingService.getOrInit(businessId);
        return AbandonedScheduleMessageSettingConverter.INSTANCE.entityToVO(entity);
    }

    @PutMapping
    @Auth(BUSINESS)
    public void updateAbandonedScheduleMessageSetting(
            @RequestBody @Valid UpdateAbandonedScheduleMessageSettingParam param) {
        if (Objects.equals(param.sendOutType, SendOutType.WAIT_FOR)) {
            boolean invalidHour = param.waitForTypeHour == null || param.waitForTypeHour <= 0;
            boolean invalidMinute = param.waitForTypeMinute == null || param.waitForTypeMinute <= 0;
            if (invalidHour && invalidMinute) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR,
                        "Either waitForTypeHour or waitForTypeMinute must be provided and must be greater than 0");
            }
        }
        Integer businessId = AuthContext.get().getBusinessId();

        AbandonedScheduleMessageSetting setting =
                AbandonedScheduleMessageSettingConverter.INSTANCE.updateParamToEntity(param);
        setting.setBusinessId(businessId);
        abandonedScheduleMessageSettingService.updateByBusinessId(setting);
    }

    public record UpdateAbandonedScheduleMessageSettingParam(
            List<ClientType> clientTypes,
            List<OBStepEnum> abandonedSteps,
            SendOutType sendOutType,
            @Size(max = 7) List<DayOfWeek> onTypeDays,
            Integer onTypeMinute,
            Integer waitForTypeHour,
            Integer waitForTypeMinute,
            @Size(max = 2000) String message,
            Boolean isEnabled) {}
}
