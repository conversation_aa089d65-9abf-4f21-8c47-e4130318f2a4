package com.moego.server.grooming.service.ob.metrics;

import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Service
@RequiredArgsConstructor
public class AbandonedRecordsMetricService implements IOBMetricsService {

    private final MoeBookOnlineAbandonRecordMapper abandonRecordMapper;
    private final StartedRecordsMetricService startedRecordsMetricService;

    @Override
    public Integer sumMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        // count(distinct abandoned) + count(recovered)
        int notRecoveredClient = abandonRecordMapper.countNotRecoveredClients(
                timeRangeDTO.businessId(),
                timeRangeDTO.startTime(),
                timeRangeDTO.endTime(),
                OBStepEnum.listRecoverableSteps());
        int recoveredRecords = abandonRecordMapper.countRecoveredRecords(
                timeRangeDTO.businessId(),
                timeRangeDTO.startTime(),
                timeRangeDTO.endTime(),
                OBStepEnum.listRecoverableSteps());
        return notRecoveredClient + recoveredRecords;
    }

    @Override
    public Object proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        // abandoned records / started records
        Integer abandonedRecords = this.sumMetrics(timeRangeDTO);
        int startedRecords = startedRecordsMetricService.sumMetrics(timeRangeDTO);
        if (startedRecords == 0) {
            return "";
        }
        return BigDecimal.valueOf(abandonedRecords).divide(BigDecimal.valueOf(startedRecords), 2, RoundingMode.HALF_UP);
    }

    @Override
    public OBMetricsEnum getMetricsName() {
        return OBMetricsEnum.abandoned_records;
    }
}
