package com.moego.server.grooming.service.ob;

import static com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub;

import com.google.type.LatLng;
import com.moego.common.enums.BooleanEnum;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressCreateDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressUpdateDef;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerPrimaryAddressRequest;
import com.moego.idl.service.business_customer.v1.CreateCustomerAddressRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerAddressRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerPrimaryAddressRequest;
import com.moego.idl.service.business_customer.v1.ListCustomerAddressRequest;
import com.moego.idl.service.business_customer.v1.UpdateCustomerAddressRequest;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.grooming.params.BookOnlineCustomerParams;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class OBAddressService {

    private final BusinessCustomerAddressServiceBlockingStub customerAddressServiceBlockingStub;

    public void createCustomerAddress(BookOnlineCustomerParams params) {
        if (params.getAddressId() != null
                || params.getCustomerId() == null
                || !StringUtils.hasText(params.getLat())
                || !StringUtils.hasText(params.getLng())) {
            return;
        }

        var coordinate = LatLng.newBuilder()
                .setLatitude(Double.parseDouble(params.getLat()))
                .setLongitude(Double.parseDouble(params.getLng()))
                .build();

        var address = BusinessCustomerAddressCreateDef.newBuilder()
                .setCustomerId(params.getCustomerId())
                .setCoordinate(coordinate)
                .setAddress1(Optional.ofNullable(params.getAddress1()).orElse(""))
                .setAddress2(Optional.ofNullable(params.getAddress2()).orElse(""))
                .setCity(Optional.ofNullable(params.getCity()).orElse(""))
                .setCountry(Optional.ofNullable(params.getCountry()).orElse(""))
                .setState(Optional.ofNullable(params.getState()).orElse(""))
                .setZipcode(Optional.ofNullable(params.getZipcode()).orElse(""))
                .build();

        var request = CreateCustomerAddressRequest.newBuilder()
                .setCustomerId(params.getCustomerId())
                .setAddress(address)
                .build();

        customerAddressServiceBlockingStub.createCustomerAddress(request);
    }

    public void createCustomerAddress(CustomerAddressDto address) {

        LatLng coordinate = null;
        if (StringUtils.hasText(address.getLat()) && StringUtils.hasText(address.getLng())) {
            try {
                coordinate = LatLng.newBuilder()
                        .setLatitude(Double.parseDouble(address.getLat()))
                        .setLongitude(Double.parseDouble(address.getLng()))
                        .build();
            } catch (NumberFormatException e) {
                log.error("Failed to parse lat/lng, lat: {}, lng: {}", address.getLat(), address.getLng());
            }
        }

        var builder = BusinessCustomerAddressCreateDef.newBuilder()
                .setCustomerId(address.getCustomerId())
                .setAddress1(Optional.ofNullable(address.getAddress1()).orElse(""))
                .setAddress2(Optional.ofNullable(address.getAddress2()).orElse(""))
                .setCity(Optional.ofNullable(address.getCity()).orElse(""))
                .setCountry(Optional.ofNullable(address.getCountry()).orElse(""))
                .setState(Optional.ofNullable(address.getState()).orElse(""))
                .setZipcode(Optional.ofNullable(address.getZipcode()).orElse(""));

        if (coordinate != null) {
            builder.setCoordinate(coordinate);
        }

        if (BooleanEnum.VALUE_TRUE.equals(address.getIsPrimary())) {
            builder.setIsPrimary(true);
        }

        var request = CreateCustomerAddressRequest.newBuilder()
                .setCustomerId(address.getCustomerId())
                .setAddress(builder.build())
                .build();

        customerAddressServiceBlockingStub.createCustomerAddress(request);
    }

    public void updateCustomerAddress(CustomerAddressDto address) {
        var updateDefBuilder = BusinessCustomerAddressUpdateDef.newBuilder();
        // null 表示不更新，空字符串表示清空
        if (address.getAddress1() != null) {
            updateDefBuilder.setAddress1(address.getAddress1());
        }
        if (address.getAddress2() != null) {
            updateDefBuilder.setAddress2(address.getAddress2());
        }
        if (address.getCity() != null) {
            updateDefBuilder.setCity(address.getCity());
        }
        if (address.getState() != null) {
            updateDefBuilder.setState(address.getState());
        }
        if (address.getCountry() != null) {
            updateDefBuilder.setCountry(address.getCountry());
        }
        if (address.getZipcode() != null) {
            updateDefBuilder.setZipcode(address.getZipcode());
        }
        if (address.getLat() != null && address.getLng() != null) {
            try {
                var latLng = LatLng.newBuilder()
                        .setLatitude(Double.parseDouble(address.getLat()))
                        .setLongitude(Double.parseDouble(address.getLng()))
                        .build();
                updateDefBuilder.setCoordinate(latLng);
            } catch (NumberFormatException e) {
                log.error("Invalid lat or lng: {}, {}", address.getLat(), address.getLng());
                // ignore invalid lat or lng
            }
        }
        if (address.getIsPrimary() != null) {
            updateDefBuilder.setIsPrimary(BooleanEnum.VALUE_TRUE.equals(address.getIsPrimary()));
        }

        var updateRequest = UpdateCustomerAddressRequest.newBuilder()
                .setId(address.getCustomerAddressId())
                .setAddress(updateDefBuilder)
                .build();

        customerAddressServiceBlockingStub.updateCustomerAddress(updateRequest);
    }

    public CustomerAddressDto getCustomerAddress(Integer addressId) {
        try {
            var request =
                    GetCustomerAddressRequest.newBuilder().setId(addressId).build();
            var address = customerAddressServiceBlockingStub
                    .getCustomerAddress(request)
                    .getAddress();
            return toCustomerAddressDto(address);
        } catch (Exception e) {
            log.error("Failed to get customer address, id {}", addressId, e);
        }
        return null;
    }

    public CustomerAddressDto getPrimaryAddress(Integer customerId) {
        try {
            var request = GetCustomerPrimaryAddressRequest.newBuilder()
                    .setCustomerId(customerId)
                    .build();
            var response = customerAddressServiceBlockingStub.getCustomerPrimaryAddress(request);
            if (response.hasAddress()) {
                return toCustomerAddressDto(response.getAddress());
            }
        } catch (Exception e) {
            log.error("Failed to get primary address, customerId {}", customerId, e);
        }
        return null;
    }

    public Map<Integer, CustomerAddressDto> batchGetPrimaryAddress(List<Integer> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return Map.of();
        }

        var longCustomerIds = customerIds.stream()
                .filter(id -> id != null && id > 0)
                .map(Integer::longValue)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(longCustomerIds)) {
            return Map.of();
        }

        var request = BatchGetCustomerPrimaryAddressRequest.newBuilder()
                .addAllCustomerIds(longCustomerIds)
                .build();

        var addressMap = customerAddressServiceBlockingStub
                .batchGetCustomerPrimaryAddress(request)
                .getAddressesMap();

        var result = new HashMap<Integer, CustomerAddressDto>();
        addressMap.forEach((customerId, address) -> result.put(customerId.intValue(), toCustomerAddressDto(address)));
        return result;
    }

    public List<CustomerAddressDto> listCustomerAddress(Integer customerId) {
        var request = ListCustomerAddressRequest.newBuilder()
                .setCustomerId(customerId)
                .build();
        return customerAddressServiceBlockingStub.listCustomerAddress(request).getAddressesList().stream()
                .map(this::toCustomerAddressDto)
                .toList();
    }

    private CustomerAddressDto toCustomerAddressDto(BusinessCustomerAddressModel address) {
        var dto = new CustomerAddressDto();
        dto.setCustomerAddressId((int) address.getId());
        dto.setCustomerId((int) address.getCustomerId());
        dto.setIsPrimary(address.getIsPrimary() ? BooleanEnum.VALUE_TRUE : BooleanEnum.VALUE_FALSE);
        dto.setIsProfileRequestAddress(false);

        dto.setAddress1(address.getAddress1());
        dto.setAddress2(address.getAddress2());
        dto.setCountry(address.getCountry());
        dto.setState(address.getState());
        dto.setCity(address.getCity());
        dto.setZipcode(address.getZipcode());
        if (address.hasCoordinate()) {
            dto.setLat(String.valueOf(address.getCoordinate().getLatitude()));
            dto.setLng(String.valueOf(address.getCoordinate().getLongitude()));
        } else {
            dto.setLat("");
            dto.setLng("");
        }
        return dto;
    }

    public void setPrimaryAddress(Integer addressId) {
        if (addressId == null) {
            return;
        }
        customerAddressServiceBlockingStub.updateCustomerAddress(UpdateCustomerAddressRequest.newBuilder()
                .setId(addressId)
                .setAddress(BusinessCustomerAddressUpdateDef.newBuilder()
                        .setIsPrimary(true)
                        .build())
                .build());
    }
}
