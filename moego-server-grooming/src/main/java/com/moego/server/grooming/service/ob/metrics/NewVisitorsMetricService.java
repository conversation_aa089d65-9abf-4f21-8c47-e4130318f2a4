package com.moego.server.grooming.service.ob.metrics;

import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/5/22
 */
@Service
@RequiredArgsConstructor
public class NewVisitorsMetricService implements IOBMetricsService {

    private final UniqueVisitorsMetricService uniqueVisitorsMetricService;

    @Override
    public Object sumMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        Integer uniqueVisitors = uniqueVisitorsMetricService.sumMetrics(timeRangeDTO);
        long diffTime = timeRangeDTO.endTime() - timeRangeDTO.startTime();
        OBMetricTimeRangeDTO lastTimeRangeDTO = OBMetricTimeRangeDTO.builder()
                .companyId(timeRangeDTO.companyId())
                .businessId(timeRangeDTO.businessId())
                .startTime(timeRangeDTO.startTime() - diffTime)
                .endTime(timeRangeDTO.endTime() - diffTime)
                .build();
        Integer lastUniqueVisitors = uniqueVisitorsMetricService.sumMetrics(lastTimeRangeDTO);
        return uniqueVisitors > lastUniqueVisitors ? uniqueVisitors - lastUniqueVisitors : 0;
    }

    @Override
    public Object proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return "";
    }

    @Override
    public OBMetricsEnum getMetricsName() {
        return OBMetricsEnum.new_visitors;
    }
}
