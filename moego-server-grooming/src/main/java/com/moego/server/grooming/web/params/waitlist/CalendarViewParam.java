package com.moego.server.grooming.web.params.waitlist;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class CalendarViewParam {
    @Schema(description = "business id", hidden = true)
    Long businessId;

    @Schema(description = "token staff id", hidden = true)
    Long tokenStaffId;

    @Schema(description = "company id", hidden = true)
    Long companyId;

    private LocalDate startDate;
    private LocalDate endDate;
}
