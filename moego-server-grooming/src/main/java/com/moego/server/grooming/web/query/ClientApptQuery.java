package com.moego.server.grooming.web.query;

import com.moego.common.enums.ClientApptConst;
import com.moego.common.params.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2022/12/5
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientApptQuery extends PageQuery {

    /**
     * @see com.moego.common.enums.ClientApptConst
     */
    @Schema(description = "0-Requests, 1-Upcoming, 2-Completed")
    private Byte apptType;

    @Override
    public PageQuery setSortList(List<SortQuery> sortList) {
        if (CollectionUtils.isEmpty(sortList)) {
            return super.setSortList(sortList);
        }
        return super.setSortList(sortList.stream()
                .filter(sortQuery -> ClientApptConst.SORT_BY_MAP.containsKey(sortQuery.getSortBy()))
                .map(sortQuery -> sortQuery.setSortBy(ClientApptConst.SORT_BY_MAP.get(sortQuery.getSortBy())))
                .collect(Collectors.toList()));
    }
}
