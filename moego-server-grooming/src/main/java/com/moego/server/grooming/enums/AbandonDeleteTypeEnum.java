package com.moego.server.grooming.enums;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.lang.Nullable;

/**
 * <AUTHOR>
 * @since 2023/5/15
 */
@Getter
@RequiredArgsConstructor
public enum AbandonDeleteTypeEnum {
    TEST_CASE((byte) 1, "test case data"),
    MANUAL((byte) 2, "manual delete"),
    BLOCK_CLIENT((byte) 3, "block client"),
    SUBMITTED((byte) 4, "submitted"),
    @JsonProperty("out_of_service_area")
    OUT_OF_SERVICE_AREA((byte) 5, "out of service area"),
    @JsonProperty("no_applicable_pet")
    NO_APPLICABLE_PET((byte) 6, "no applicable pet"),
    @JsonProperty("no_applicable_service")
    NO_APPLICABLE_SERVICE((byte) 7, "no applicable service"),
    @JsonProperty("no_available_service")
    NO_AVAILABLE_SERVICE((byte) 8, "no available service"),
    HISTORICAL_DATA_OVERWRITTEN((byte) 9, "historical data overwritten");

    private final Byte type;

    private final String desc;

    /**
     * Get enum by type.
     *
     * @param type type
     */
    @Nullable
    public static AbandonDeleteTypeEnum of(Byte type) {
        for (AbandonDeleteTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
