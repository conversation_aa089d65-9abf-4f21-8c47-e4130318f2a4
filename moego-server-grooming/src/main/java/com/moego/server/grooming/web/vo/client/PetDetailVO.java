package com.moego.server.grooming.web.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/5
 */
@Data
@Accessors(chain = true)
public class PetDetailVO {

    @Schema(description = "Pet id")
    private Integer petId;

    @Schema(description = "Pet name")
    private String petName;

    @Schema(description = "Pet type")
    private Integer petTypeId;

    @Schema(description = "Pet breed")
    private String breed;

    @Schema(description = "Pet avatar path")
    private String avatarPath;

    @Schema(description = "Pet weight")
    private String weight;
}
