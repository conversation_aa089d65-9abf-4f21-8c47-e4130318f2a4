package com.moego.server.grooming.web.params;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.DateUtil;
import com.moego.server.grooming.enums.AbandonDeleteTypeEnum;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.params.BookOnlineCustomerAdditionalParams;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Builder;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @since 2023/5/10
 */
@Builder(toBuilder = true)
public record OBAbandonParams(
        @Schema(description = "business id", hidden = true) Integer businessId,
        @Schema(description = "company id", hidden = true) Long companyId,
        @Schema(description = "online booking name", hidden = true) String obName,
        @Schema(description = "unique ID for each booking flow'") @NotEmpty String bookingFlowId,
        @Schema(description = "http header referer") @Size(max = 1024) String referrer,
        @Schema(description = "unrecoverable type") AbandonDeleteTypeEnum deleteType,
        @Schema(description = "skip abandon step") Boolean skipAbandon,
        @Schema(description = "new ob step unique ID") @NotNull OBStepEnum nextStep,
        @Schema(description = "selected staff id") @Min(0) Integer staffId,
        @Schema(description = "selected appointment date, yyyy-MM-dd") @DateTimeFormat(pattern = DateUtil.STANDARD_DATE)
                String appointmentDate,
        @Schema(description = "selected appointment minutes") @Range(min = 0, max = 1440) Integer appointmentStartTime,
        @Schema(description = "additional note, alert note") String note,
        @Schema(description = "signed agreement info") @Valid List<OBAbandonAgreementParams> agreements,
        @Schema(description = "abandon client info") OBAbandonClientParams customerData,
        @Schema(description = "personal additional info")
                BookOnlineCustomerAdditionalParams bookOnlineCustomerAdditionalParams,
        @Schema(description = "payment ob seg") Boolean usePaymentSegSetting,
        @Schema(description = "payment ob seg rule") String paymentSegSettingRule,
        @Schema(description = "selected pet info") @Valid List<OBAbandonPetParams> petData,
        @Schema(description = "selected care type") ServiceItemEnum careType,
        @Schema(description = "boarding start date, yyyy-MM-dd") @DateTimeFormat(pattern = DateUtil.STANDARD_DATE)
                String boardingStartDate,
        @Schema(description = "boarding end date, yyyy-MM-dd") @DateTimeFormat(pattern = DateUtil.STANDARD_DATE)
                String boardingEndDate,
        @Schema(description = "daycare dates, yyyy-MM-dd") List<String> daycareDates,
        @Schema(description = "boarding/daycare service pet arrival time") @Range(min = 0, max = 1440)
                Integer arrivalTime,
        @Schema(description = "boarding/daycare service pet pickup time") @Range(min = 0, max = 1440)
                Integer pickupTime) {
    public record OBAbandonAgreementParams(
            @Schema(description = "agreement id") @NotNull Integer agreementId,
            @Schema(description = "agreement signature base64") @NotEmpty String signature) {}
}
