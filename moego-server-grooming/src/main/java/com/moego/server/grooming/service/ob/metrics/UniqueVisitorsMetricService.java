package com.moego.server.grooming.service.ob.metrics;

import com.google.analytics.data.v1beta.DateRange;
import com.google.analytics.data.v1beta.Dimension;
import com.google.analytics.data.v1beta.Filter;
import com.google.analytics.data.v1beta.FilterExpression;
import com.google.analytics.data.v1beta.Metric;
import com.google.analytics.data.v1beta.MetricValue;
import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineMetricsMapper;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineMetrics;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.service.dto.ob.GAMetricsDTO;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import com.moego.server.grooming.service.google.GoogleAnalyticsService;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/5/22
 */
@Service
@RequiredArgsConstructor
public class UniqueVisitorsMetricService implements IOBMetricsService {

    private final GoogleAnalyticsService googleAnalyticsService;
    private final MoeBusinessBookOnlineMapper businessBookOnlineMapper;
    private final MoeBookOnlineMetricsMapper metricsMapper;

    private static final String DIMENSION_NAME = "customEvent:businessName";
    private static final String METRIC_NAME = "totalUsers";

    private List<Dimension> buildDimensions() {
        return List.of(Dimension.newBuilder().setName(DIMENSION_NAME).build());
    }

    private FilterExpression buildDimensionFilter(String bookOnlineName) {
        return FilterExpression.newBuilder()
                .setFilter(Filter.newBuilder()
                        .setFieldName(DIMENSION_NAME)
                        .setStringFilter(Filter.StringFilter.newBuilder()
                                .setCaseSensitive(true)
                                .setMatchType(Filter.StringFilter.MatchType.EXACT)
                                .setValue(bookOnlineName)
                                .build())
                        .build())
                .build();
    }

    private List<Metric> buildMetrics() {
        return List.of(Metric.newBuilder().setName(METRIC_NAME).build());
    }

    private DateRange buildDateRange(OBMetricTimeRangeDTO timeRangeDTO) {
        return DateRange.newBuilder()
                .setStartDate(
                        LocalDate.ofInstant(Instant.ofEpochSecond(timeRangeDTO.startTime()), ZoneOffset.systemDefault())
                                .format(DateTimeFormatter.ISO_DATE))
                .setEndDate(
                        LocalDate.ofInstant(Instant.ofEpochSecond(timeRangeDTO.endTime()), ZoneOffset.systemDefault())
                                .format(DateTimeFormatter.ISO_DATE))
                .build();
    }

    @Override
    public Integer sumMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        DateRange dateRange = buildDateRange(timeRangeDTO);
        // 1. get metrics from DB
        MoeBookOnlineMetrics metrics = metricsMapper.selectByCondition(
                timeRangeDTO.businessId(), METRIC_NAME, dateRange.getStartDate(), dateRange.getEndDate());
        if (Objects.nonNull(metrics) && StringUtils.isNumeric(metrics.getMetricValue())) {
            return Integer.valueOf(metrics.getMetricValue());
        }
        // 2. get metrics from GA
        MoeBusinessBookOnline businessBookOnline =
                businessBookOnlineMapper.selectByBusinessId(timeRangeDTO.businessId());
        GAMetricsDTO gaMetricsDTO = GAMetricsDTO.builder()
                .dimensions(buildDimensions())
                .dimensionFilter(buildDimensionFilter(businessBookOnline.getBookOnlineName()))
                .metrics(buildMetrics())
                .dateRanges(List.of(dateRange))
                .build();
        MetricValue metricValue = googleAnalyticsService.getMetricBySingleDimension(gaMetricsDTO);
        if (StringUtils.isNumeric(metricValue.getValue())) {
            return Integer.valueOf(metricValue.getValue());
        }
        return 0;
    }

    @Override
    public Object proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return "";
    }

    @Override
    public OBMetricsEnum getMetricsName() {
        return OBMetricsEnum.unique_visitors;
    }
}
