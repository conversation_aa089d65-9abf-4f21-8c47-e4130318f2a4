package com.moego.server.grooming.service.ob;

import com.moego.common.enums.CustomerContactEnum;
import com.moego.idl.models.metadata.v1.OwnerType;
import com.moego.idl.service.metadata.v1.ExtractValuesRequest;
import com.moego.idl.service.metadata.v1.ExtractValuesResponse;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.grooming.constant.MetadataConstant;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/9/1
 */
@Service
@RequiredArgsConstructor
public class OBWhitelistService {

    private final IBusinessBusinessClient businessClient;
    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;

    /**
     * 强制规则：
     * 只开 new -> 都不接
     * 只开 existing -> 只接 existing
     * 开了 new 和 existing -> 只接 existing
     */
    public static final Map<Byte, Byte> CLIENT_TYPE_FORCE_MAP = Map.of(
            CustomerContactEnum.ONLY_ACCEPT_NEW, (byte) 0,
            CustomerContactEnum.ONLY_ACCEPT_OLD, CustomerContactEnum.ONLY_ACCEPT_OLD,
            CustomerContactEnum.ACCEPT_BOTH, CustomerContactEnum.ONLY_ACCEPT_OLD);

    public boolean isWhitelistFranchise(Integer businessId) {
        Integer franchisorAccountId = businessClient.getFranchisorAccountId(businessId);
        if (Objects.isNull(franchisorAccountId)) {
            return false;
        }
        ExtractValuesResponse valuesResponse =
                metadataServiceBlockingStub.extractValues(ExtractValuesRequest.newBuilder()
                        .putOwners(OwnerType.OWNER_TYPE_ACCOUNT_VALUE, franchisorAccountId)
                        .setKeyName(MetadataConstant.ONLINE_BOOKING_LIMIT_EXISTING_CLIENT)
                        .build());
        return Optional.ofNullable(
                        valuesResponse.getValuesMap().get(MetadataConstant.ONLINE_BOOKING_LIMIT_EXISTING_CLIENT))
                .map(Boolean::parseBoolean)
                .orElse(false);
    }
}
