package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGcWatchEvent;
import org.apache.ibatis.annotations.Param;

public interface MoeGcWatchEventMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_watch_event
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_watch_event
     *
     * @mbg.generated
     */
    int insert(MoeGcWatchEvent record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_watch_event
     *
     * @mbg.generated
     */
    int insertSelective(MoeGcWatchEvent record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_watch_event
     *
     * @mbg.generated
     */
    MoeGcWatchEvent selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_watch_event
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGcWatchEvent record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_watch_event
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGcWatchEvent record);

    MoeGcWatchEvent selectByChannelUuid(@Param("channelUuid") String channelUuid);

    MoeGcWatchEvent selectByGcCalendarId(@Param("gcCalendarId") Integer gcCalendarId);
}
