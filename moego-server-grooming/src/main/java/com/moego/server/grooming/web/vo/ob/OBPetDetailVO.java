package com.moego.server.grooming.web.vo.ob;

import java.util.List;
import lombok.Builder;

@Builder(toBuilder = true)
public record OBPetDetailVO(
        Integer petId,
        String petName,
        Integer petTypeId,
        String breed,
        String weight,
        String avatarPath,
        String fixed,
        Byte gender,
        String hairLength,
        String behavior,
        String birthday,
        List<OBVaccineDetailVO> vaccineList,
        Integer serviceId,
        List<Integer> addOnIds,
        String vetAddress,
        String vetName,
        String vetPhoneNumber,
        String emergencyContactName,
        String emergencyContactPhone,
        String healthIssues,
        List<OBRequestDetailVO.QuestionAnswerVO> questionAnswerList) {
    @Builder
    public record OBVaccineDetailVO(
            Integer vaccineBindingId,
            Integer vaccineId,
            String vaccineName,
            String expirationDate,
            List<String> documentUrls) {}
}
