package com.moego.server.grooming.server;

import com.moego.common.dto.PageDTO;
import com.moego.server.grooming.api.IGroomingReportServiceBase;
import com.moego.server.grooming.dto.ReportServiceProduct;
import com.moego.server.grooming.dto.ReportWebSale;
import com.moego.server.grooming.dto.report.DescribeAppointmentReportsDTO;
import com.moego.server.grooming.dto.report.DescribeOperationReportsDTO;
import com.moego.server.grooming.dto.report.DescribePetDetailReportsDTO;
import com.moego.server.grooming.dto.report.EmployeeContribute;
import com.moego.server.grooming.dto.report.GroomingMobileOverviewDTO;
import com.moego.server.grooming.dto.report.MobileSummaryDTO;
import com.moego.server.grooming.dto.report.PayrollReportCountDTO;
import com.moego.server.grooming.dto.report.PayrollReportCountV2DTO;
import com.moego.server.grooming.dto.report.ReportApptsNumberDTO;
import com.moego.server.grooming.dto.report.ReportCollectedRevenueDTO;
import com.moego.server.grooming.dto.report.ReportExpectedRevenueDTO;
import com.moego.server.grooming.dto.report.ReportPaymentSummaryDto;
import com.moego.server.grooming.dto.report.ReportRecurringCustomerDTO;
import com.moego.server.grooming.dto.report.ReportTrendDTO;
import com.moego.server.grooming.dto.report.ReportTrendDataDTO;
import com.moego.server.grooming.dto.report.ReportWebAppointment;
import com.moego.server.grooming.dto.report.ReportWebEmployee;
import com.moego.server.grooming.dto.report.ReportWebSaleService;
import com.moego.server.grooming.dto.report.ScanAppointmentReportsDTO;
import com.moego.server.grooming.mapstruct.GroomingReportMapper;
import com.moego.server.grooming.params.ReportWebApptsRequest;
import com.moego.server.grooming.params.report.DescribeAppointmentReportsParams;
import com.moego.server.grooming.params.report.DescribeOperationReportsParams;
import com.moego.server.grooming.params.report.DescribePetDetailReportsParams;
import com.moego.server.grooming.params.report.GetDashboardOverviewParams;
import com.moego.server.grooming.params.report.GetReportParams;
import com.moego.server.grooming.params.report.GetReportTrendParams;
import com.moego.server.grooming.params.report.QueryPayrollReportByPageParams;
import com.moego.server.grooming.params.report.ScanAppointmentReportsParams;
import com.moego.server.grooming.service.report.GroomingReportService;
import com.moego.server.grooming.service.report.PayrollReportService;
import com.moego.server.grooming.service.report.ReportAppointmentService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
public class GroomingReportServer extends IGroomingReportServiceBase {

    private final GroomingReportService reportService;
    private final PayrollReportService payrollReportService;
    private final ReportAppointmentService reportAppointmentService;

    @Override
    public MobileSummaryDTO getMobileDashboardSummary(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {
        try {
            return reportService.getMobileDashboardSummary(tokenBusinessId, startDate, endDate);
        } catch (RuntimeException e) {
            log.error("Failed getting mobile dashboard summary: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public GroomingMobileOverviewDTO getMobileDashboardOverview(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate,
            @RequestBody List<EmployeeContribute> employees) {
        try {
            return reportService.getMobileDashboardOverview(tokenBusinessId, startDate, endDate, employees);
        } catch (RuntimeException e) {
            log.error("Failed getting mobile dashboard summary: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public GroomingMobileOverviewDTO getMobileDashboardOverviewV2(@RequestBody GetDashboardOverviewParams params) {
        return reportService.getMobileDashboardOverviewV2(params);
    }

    @Override
    public ReportApptsNumberDTO getReportApptsNumber(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {
        try {
            return reportService.getReportApptsNumber(tokenBusinessId, startDate, endDate);
        } catch (RuntimeException e) {
            log.error("Failed getting mobile dashboard summary: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<ReportWebAppointment> queryReportWebAppts(@RequestBody ReportWebApptsRequest request) {
        return reportAppointmentService.getReportWebAppts(request);
    }

    @Override
    public List<ReportWebEmployee> queryReportWebEmployee(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("reportId") Integer reportId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {
        return payrollReportService.queryReportWebEmployee(businessId, reportId, startDate, endDate);
    }

    @Override
    public List<ReportWebEmployee> queryPayrollReport(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {
        return payrollReportService.getStaffPayrollSummary(businessId, startDate, endDate);
    }

    @Override
    public PageDTO<ReportWebEmployee> queryPayrollReportByPage(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("staffId") Integer staffId,
            @RequestParam("type") Byte type,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate,
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize) {
        return payrollReportService.queryPayrollReportByPage(
                businessId, staffId, type, startDate, endDate, pageNum, pageSize);
    }

    @Override
    public PageDTO<ReportWebEmployee> queryPayrollReportByPageV2(
            @Valid @RequestBody QueryPayrollReportByPageParams params) {
        return payrollReportService.queryStaffPayrollByPage(params);
    }

    @Override
    public List<PayrollReportCountDTO> getStaffPayrollReportCounts(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate,
            @RequestBody List<Integer> staffIds) {
        return payrollReportService.getStaffPayrollReportCounts(businessId, startDate, endDate, staffIds);
    }

    @Override
    public List<PayrollReportCountV2DTO> getStaffServicePayrollReportCounts(@RequestBody GetReportParams params) {
        return payrollReportService.getStaffServicePayrollReportCounts(params);
    }

    @Override
    public List<PayrollReportCountV2DTO> getStaffTipsPayrollReportCounts(@RequestBody GetReportParams params) {
        return payrollReportService.getStaffTipsPayrollReportCounts(params);
    }

    @Override
    public List<ReportWebEmployee> queryPayrollReportAll(
            Integer businessId, Integer staffId, Byte type, String startDate, String endDate) {
        return payrollReportService.queryPayrollReportAll(businessId, staffId, type, startDate, endDate);
    }

    @Override
    public List<ReportWebSale> queryReportWebSale(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("reportId") Integer reportId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {
        return reportService.queryReportWebSale(businessId, reportId, startDate, endDate);
    }

    @Override
    public List<ReportWebAppointment> queryReportWebSaleAndAppt(@RequestBody ReportWebApptsRequest requestParam) {
        return reportService.queryReportApptDetail(requestParam);
    }

    @Override
    public List<ReportWebSaleService> queryReportWebByService(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {
        return reportService.queryReportWebByService(businessId, startDate, endDate);
    }

    @Override
    public List<ReportServiceProduct> queryReportWebServiceProduct(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {
        return reportService.queryReportWebServiceProduct(businessId, startDate, endDate);
    }

    @Override
    public ReportTrendDTO getReportTrend(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {
        return reportService.getReportTrend(businessId, startDate, endDate);
    }

    @Override
    public ReportTrendDataDTO getReportTrendData(@RequestBody GetReportTrendParams params) {
        return reportService.getReportTrendData(params);
    }

    @Override
    public List<ReportExpectedRevenueDTO> getExpectedRevReport(
            @RequestParam("businessId") Integer businessId,
            @RequestParam(value = "staffId", required = false) Integer staffId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {
        return reportService.getExpectedRevReport(businessId, staffId, startDate, endDate);
    }

    @Override
    public List<ReportCollectedRevenueDTO> getCollectedRevReport(
            @RequestParam("businessId") Integer businessId,
            @RequestParam(value = "staffId", required = false) Integer staffId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {
        return reportService.getCollectedRevReport(businessId, staffId, startDate, endDate);
    }

    @Override
    public List<ReportPaymentSummaryDto> getPaymentReport(
            @RequestParam("businessId") Integer businessId,
            @RequestParam(value = "staffId", required = false) Integer staffId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {
        return reportService.paymentMethodReport(businessId, startDate, endDate, staffId);
    }

    @Override
    public List<ReportRecurringCustomerDTO> getRecurringClientReport(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {
        return reportService.getRecurringClientReport(businessId, startDate, endDate);
    }

    @Override
    public DescribeAppointmentReportsDTO describeAppointmentReports(DescribeAppointmentReportsParams params) {
        var data = reportAppointmentService.describeAppointmentReports(params);
        return new DescribeAppointmentReportsDTO(
                data.getFirst().stream()
                        .map(GroomingReportMapper.INSTANCE::toAppointmentReporting)
                        .toList(),
                data.getSecond());
    }

    @Override
    public DescribePetDetailReportsDTO describePetDetailReports(DescribePetDetailReportsParams params) {
        var data = reportAppointmentService.describePetDetailReports(params);
        return new DescribePetDetailReportsDTO(
                data.getFirst().stream()
                        .map(GroomingReportMapper.INSTANCE::toPetDetailReporting)
                        .toList(),
                data.getSecond());
    }

    @Override
    public DescribeOperationReportsDTO describeOperationReports(DescribeOperationReportsParams params) {
        var data = reportAppointmentService.describeOperationReports(params);
        return new DescribeOperationReportsDTO(
                data.getFirst().stream()
                        .map(GroomingReportMapper.INSTANCE::toOperationReporting)
                        .toList(),
                data.getSecond());
    }

    @Override
    public ScanAppointmentReportsDTO scanAppointmentReports(ScanAppointmentReportsParams params) {
        var reports = reportAppointmentService.scanAppointmentReports(params);
        return new ScanAppointmentReportsDTO(reports.stream()
                .map(GroomingReportMapper.INSTANCE::toAppointmentReporting)
                .toList());
    }
}
