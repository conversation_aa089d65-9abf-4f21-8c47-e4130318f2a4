package com.moego.server.grooming.web.vo.ob;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Set;
import lombok.Builder;

/**
 * <AUTHOR>
 * @since 2023/8/11
 */
@Builder
public record BulkAddClientVO(
        @Schema(description = "Total number of clients") int total,
        @Schema(description = "Number of successful add clients") int success,
        @Schema(description = "Number of failed add clients") int fail,
        @Schema(description = "failed add client abandoned record ids") List<String> failIds,
        @Schema(description = "从 filter 中正选的 abandoned record 映射出的 customer id") Set<Integer> includeCustomerIds,
        @Schema(description = "从 filter 中反选的 abandoned record 映射出的 customer id") Set<Integer> excludeCustomerIds) {}
