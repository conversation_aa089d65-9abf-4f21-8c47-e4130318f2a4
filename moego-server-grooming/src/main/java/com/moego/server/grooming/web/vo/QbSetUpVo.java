package com.moego.server.grooming.web.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/3/11
 */
@Data
public class QbSetUpVo {
    @JsonIgnore
    private Integer companyId;

    @Schema(description = "连接id")
    @NotNull
    private Integer connectId;

    @Schema(description = "预约开始同步日期(默认创建时间) 年-月-日")
    private String syncBeginDate;

    @Schema(description = "Activate时忽略的商家id列表")
    private List<Integer> dismissedBusinessIds;
}
