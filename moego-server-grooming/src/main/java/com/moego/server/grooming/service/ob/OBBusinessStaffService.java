package com.moego.server.grooming.service.ob;

import com.moego.common.constant.CommonConstant;
import com.moego.common.distributed.LockManager;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.OnlineBookingConst;
import com.moego.common.enums.StaffEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.service.online_booking.v1.GetStaffAvailabilityStatusRequest;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.grooming.dto.ob.AvailableStaffDTO;
import com.moego.server.grooming.dto.ob.PetApplicableDTO;
import com.moego.server.grooming.dto.ob.SelectedPetServiceDTO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAvailableStaff;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.params.ob.AvailableStaffParams;
import com.moego.server.grooming.service.CompanyGroomingServiceQueryService;
import com.moego.server.grooming.service.GroomingServiceService;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.StaffAvailableSyncService;
import com.moego.server.grooming.service.dto.ob.OBClientApptDTO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2022/10/18
 */
@Slf4j
@Service
public class OBBusinessStaffService {

    @Autowired
    private StaffAvailableSyncService staffAvailableSyncService;

    @Autowired
    private GroomingServiceService groomingServiceService;

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private LockManager lockManager;

    @Autowired
    private MoeGroomingBookOnlineService moeGroomingBookOnlineService;

    @Autowired
    private CompanyGroomingServiceQueryService companyGroomingServiceQueryService;

    @Autowired
    private OBBusinessService businessService;

    @Autowired
    private OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceBlockingStub
            obStaffAvailabilityServiceBlockingStub;

    /**
     * initialize moe_book_online_available_staff
     *
     * @param staffs staff info
     * @return initialize result
     */
    public List<MoeBookOnlineAvailableStaff> initStaffAvailability(Integer businessId, List<MoeStaffDto> staffs) {
        byte syncWithWorkingHour;
        if (!CollectionUtils.isEmpty(staffs)) {
            Boolean syncWithWorkingHourStatus = moeGroomingBookOnlineService.getSyncWithWorkingHourStatus(
                    staffs.get(0).getBusinessId());
            syncWithWorkingHour = Boolean.TRUE.equals(syncWithWorkingHourStatus)
                    ? OnlineBookingConst.SYNC_WITH_WORKING_HOUR_ENABLE
                    : OnlineBookingConst.SYNC_WITH_WORKING_HOUR_DISABLE;
        } else {
            syncWithWorkingHour = OnlineBookingConst.SYNC_WITH_WORKING_HOUR_DISABLE;
        }

        List<MoeBookOnlineAvailableStaff> availableStaffs = new ArrayList<>();
        staffs.forEach(staff -> {
            Integer initBusinessId = businessId == null ? staff.getBusinessId() : businessId;
            MoeBookOnlineAvailableStaff availableStaff = new MoeBookOnlineAvailableStaff();
            availableStaff.setBusinessId(initBusinessId);
            availableStaff.setStaffId(staff.getId());
            availableStaff.setCompanyId(staff.getCompanyId());
            availableStaff.setByWorkingHourEnable(
                    staff.getBookOnlineAvailable()); // 旧字段bookOnlineAvailable同步到byWorkingHourEnable
            availableStaff.setBySlotEnable(OnlineBookingConst.STAFF_AVAILABLE_ENABLE); // bySlotEnable默认打开
            availableStaff.setSyncWithWorkingHour(syncWithWorkingHour);
            long now = DateUtil.get10Timestamp();
            availableStaff.setCreateTime(now);
            availableStaff.setUpdateTime(now);
            availableStaffs.add(availableStaff);
            // fix: Initialization concurrency issues
            String resourceKey = lockManager.getResourceKey(LockManager.OB_AVAILABLE_STAFF, staff.getId());
            String uuid = CommonUtil.getUuid();
            try {
                if (!lockManager.lock(resourceKey, uuid)) {
                    return;
                }
                staffAvailableSyncService.insert(availableStaff);
            } finally {
                lockManager.unlock(resourceKey, uuid);
            }
        });
        return availableStaffs;
    }

    private boolean isStaffAvailable(MoeBookOnlineAvailableStaff staffAvailable, byte availabilityType) {
        boolean isAvailable = false;
        if (availabilityType == OnlineBookingConst.AVAILABLE_TIME_TYPE_WORKING_HOUR) {
            isAvailable =
                    Objects.equals(staffAvailable.getByWorkingHourEnable(), OnlineBookingConst.STAFF_AVAILABLE_ENABLE);
        } else if (availabilityType == OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT) {
            isAvailable = Objects.equals(staffAvailable.getBySlotEnable(), OnlineBookingConst.STAFF_AVAILABLE_ENABLE);
        } else if (availabilityType == OnlineBookingConst.AVAILABLE_TIME_TYPE_DISABLE) {
            isAvailable = true;
        }
        return isAvailable;
    }

    /**
     * DONE: 返回 working location 是 businessId 的 staff
     * Get OB available staff info list
     *
     * @param businessId       business id
     * @param availabilityType ob availability type
     * @return available staff info list
     */
    public List<MoeStaffDto> getOBAvailableStaffList(Integer businessId, byte availabilityType) {
        // 查询商家所有的staff
        List<MoeStaffDto> staffInfoList = iBusinessStaffClient.getStaffListByBusinessId(businessId, false);

        // 去掉删除状态的 staff 以及 show on calendar = false 的 staff
        staffInfoList.removeIf(s -> !Objects.equals(s.getStatus(), StaffEnum.STATUS_NORMAL)
                || !Objects.equals(s.getShowOnCalendar(), StaffEnum.SHOW_ON_CALENDAR_TRUE));

        var response = obStaffAvailabilityServiceBlockingStub.getStaffAvailabilityStatus(
                GetStaffAvailabilityStatusRequest.newBuilder()
                        .setBusinessId(businessId)
                        .addAllStaffIdList(staffInfoList.stream()
                                .map(MoeStaffDto::getId)
                                .map(Integer::longValue)
                                .toList())
                        .build());

        // 根据商家设置的 availabilityType, 保留 available 的 staff
        return staffInfoList.stream()
                .filter(staff -> response.getStaffAvailabilityMap()
                        .getOrDefault(staff.getId().longValue(), false))
                .collect(Collectors.toList());
    }

    public List<MoeStaffDto> getOBAvailableStaffList(Integer businessId) {
        // 查询商家所有的staff
        List<MoeStaffDto> staffInfoList = iBusinessStaffClient.getStaffListByBusinessId(businessId, false);

        // 去掉删除状态的 staff 以及 show on calendar = false 的 staff
        staffInfoList.removeIf(s -> !Objects.equals(s.getStatus(), StaffEnum.STATUS_NORMAL)
                || !Objects.equals(s.getShowOnCalendar(), StaffEnum.SHOW_ON_CALENDAR_TRUE));

        var response = obStaffAvailabilityServiceBlockingStub.getStaffAvailabilityStatus(
                GetStaffAvailabilityStatusRequest.newBuilder()
                        .setBusinessId(businessId)
                        .addAllStaffIdList(staffInfoList.stream()
                                .map(MoeStaffDto::getId)
                                .map(Integer::longValue)
                                .toList())
                        .build());

        // 根据商家设置的 availabilityType, 保留 available 的 staff
        return staffInfoList.stream()
                .filter(staff -> response.getStaffAvailabilityMap()
                        .getOrDefault(staff.getId().longValue(), false))
                .toList();
    }

    /**
     * service 和 staff 的绑定关系
     *
     * @param businessId
     * @param allServiceIdList
     * @param allStaffIdList
     * @return Map: serviceId -> staffId list
     */
    public Map<Integer, List<Integer>> getServiceToStaffMap(
            Integer businessId, List<Integer> allServiceIdList, List<Integer> allStaffIdList) {
        List<MoeGroomingService> serviceList =
                companyGroomingServiceQueryService.groomingServiceSelectByBusinessIdServiceIds(
                        businessId, allServiceIdList);

        // serviceId -> staffId list (from database)
        Map<Integer, List<Integer>> serviceToStaffRelationMap =
                groomingServiceService.getServiceIdToStaffMap(businessId);

        // serviceId -> staffId list (full mapping result)
        Map<Integer, List<Integer>> serviceToStaffMap = new HashMap<>();
        for (MoeGroomingService service : serviceList) {
            // service 可能是 all staff 可用, 也可能是指定 staff 可用
            List<Integer> staffIdList = BooleanEnum.VALUE_TRUE.equals(service.getIsAllStaff())
                    ? allStaffIdList
                    : serviceToStaffRelationMap.getOrDefault(service.getId(), new ArrayList<>());

            serviceToStaffMap.put(service.getId(), staffIdList);
        }
        return serviceToStaffMap;
    }

    /**
     * 每个 staff 对于每个 pet 能否进行服务
     * 对于某个 staff，如果一个 pet 选中的所有 service 都可以由他做，那么这个 pet 对这个 staff 而言是 applicable 的
     *
     * @param allStaffIdList         给定的 staff
     * @param selectedPetServiceList 给定的 pet 以及 pet 所选的 service
     * @param serviceToStaffMap      service 和 staff 的绑定关系 (serviceId -> staffId list)
     * @return Map: staffId -> list of applicable for each pet
     */
    public static Map<Integer, List<PetApplicableDTO>> buildApplicableMap(
            List<Integer> allStaffIdList,
            List<SelectedPetServiceDTO> selectedPetServiceList,
            Map<Integer, List<Integer>> serviceToStaffMap) {
        // staffId -> list of applicable for each pet
        Map<Integer, List<PetApplicableDTO>> applicableMap = new HashMap<>();
        for (Integer staffId : allStaffIdList) {
            List<PetApplicableDTO> applicableList = selectedPetServiceList.stream()
                    .map(selectedPetServiceDTO -> {
                        // 当前 pet 选择的所有 service 可以由当前 staff 提供
                        boolean applicable = selectedPetServiceDTO.getServiceIdList().stream()
                                .allMatch(serviceId -> serviceToStaffMap
                                        .getOrDefault(serviceId, new ArrayList<>())
                                        .contains(staffId));

                        PetApplicableDTO applicableDTO = new PetApplicableDTO();
                        applicableDTO.setPetId(
                                selectedPetServiceDTO.getPetDataDTO().getPetId());
                        applicableDTO.setApplicable(applicable);
                        return applicableDTO;
                    })
                    .collect(Collectors.toList());

            applicableMap.put(staffId, applicableList);
        }
        return applicableMap;
    }

    public List<OBClientApptDTO.OBStaffInfoDTO> getAllStaffList(Integer businessId, List<Integer> staffIdList) {
        StaffIdListParams params = new StaffIdListParams();
        params.setBusinessId(businessId);
        params.setStaffIdList(staffIdList);
        List<MoeStaffDto> staffList = iBusinessStaffClient.getStaffList(params);
        // query staff available
        var response = obStaffAvailabilityServiceBlockingStub.getStaffAvailabilityStatus(
                GetStaffAvailabilityStatusRequest.newBuilder()
                        .setBusinessId(businessId)
                        .addAllStaffIdList(
                                staffIdList.stream().map(Integer::longValue).toList())
                        .build());
        var staffAvailabilityMap = response.getStaffAvailabilityMap();
        return staffList.stream()
                .map(moeStaffDto -> {
                    OBClientApptDTO.OBStaffInfoDTO staffInfoDTO = new OBClientApptDTO.OBStaffInfoDTO();
                    BeanUtils.copyProperties(moeStaffDto, staffInfoDTO);
                    staffInfoDTO.setStaffId(moeStaffDto.getId());
                    staffInfoDTO.setBookOnlineAvailable(
                            staffAvailabilityMap.getOrDefault(
                                            moeStaffDto.getId().longValue(), false)
                                    ? CommonConstant.ENABLE
                                    : CommonConstant.DISABLE);
                    return staffInfoDTO;
                })
                .collect(Collectors.toList());
    }

    public AvailableStaffDTO getAvailableStaffList(AvailableStaffParams params) {
        Integer businessId = params.getBusinessId();
        MoeBusinessBookOnline businessBookOnline = businessService.getSettingInfoByBusinessId(businessId);
        List<MoeStaffDto> staffList =
                this.getOBAvailableStaffList(businessId, businessBookOnline.getAvailableTimeType());
        List<Integer> allStaffIdList =
                staffList.stream().map(MoeStaffDto::getId).toList();
        List<Integer> allServiceIdList = params.getPetServiceList().stream()
                .flatMap(list -> list.getServiceIdList().stream())
                .distinct()
                .toList();
        // service -> staff list
        Map<Integer, List<Integer>> serviceToStaffMap =
                this.getServiceToStaffMap(businessId, allServiceIdList, allStaffIdList);
        // applicable (staffId -> list of applicable for each pet)
        Map<Integer, List<PetApplicableDTO>> applicableMap =
                buildApplicableMap(allStaffIdList, params.getPetServiceList(), serviceToStaffMap);

        AvailableStaffDTO availableStaffDTO = new AvailableStaffDTO();
        availableStaffDTO.setStaffList(staffList);
        availableStaffDTO.setApplicableMap(applicableMap);
        return availableStaffDTO;
    }
}
