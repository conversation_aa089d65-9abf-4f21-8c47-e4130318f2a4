package com.moego.server.grooming.service.ob;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.BookOnlineDepositConst;
import com.moego.common.enums.CustomerContactEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceCategoryModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderLineDiscountModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.service.business_customer.v1.BusinessCustomerContactServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListCustomerContactRequest;
import com.moego.idl.service.marketing.v1.DiscountCodeServiceGrpc;
import com.moego.idl.service.marketing.v1.GetDiscountCodeInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeOutput;
import com.moego.idl.service.offering.v1.GetServiceListRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.StaffIdParams;
import com.moego.server.customer.api.IProfileRequestAddressService;
import com.moego.server.customer.dto.AdditionalContactDTO;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.dto.ProfileRequestAddressDTO;
import com.moego.server.grooming.dto.CustomerHasRequestDTO;
import com.moego.server.grooming.dto.DiscountCodeDTO;
import com.moego.server.grooming.dto.GroomingTicketNotesDTO;
import com.moego.server.grooming.dto.ob.GroomingOnlyBookingRequestDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.helper.NewOrderHelper;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.AutoAssign;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeWaitList;
import com.moego.server.grooming.mapstruct.AppointmentMapper;
import com.moego.server.grooming.mapstruct.AutoAssignConverter;
import com.moego.server.grooming.mapstruct.PrepayMapper;
import com.moego.server.grooming.mapstruct.ProfileRequestMapper;
import com.moego.server.grooming.mapstruct.QuestionMapper;
import com.moego.server.grooming.mapstruct.StaffMapper;
import com.moego.server.grooming.service.AutoAssignService;
import com.moego.server.grooming.service.MoeBookOnlineDepositService;
import com.moego.server.grooming.service.MoeGroomingNoteService;
import com.moego.server.grooming.service.OrderService;
import com.moego.server.grooming.service.WaitListService;
import com.moego.server.grooming.service.dto.ob.OBPrepayDetailDTO;
import com.moego.server.grooming.web.vo.ob.OBRequestDetailVO;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2023/6/30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OBRequestService {

    private final AppointmentMapperProxy appointmentMapper;
    private final PetDetailMapperProxy petDetailMapper;
    private final IBusinessStaffService staffService;
    private final OBQuestionService questionService;
    private final PrepayService prepayService;
    private final OBCustomerService customerService;
    private final OBAddressService obAddressService;
    private final MoeGroomingNoteService groomingNoteService;
    private final AutoAssignService autoAssignService;
    private final OrderService orderService;
    private final DiscountCodeServiceGrpc.DiscountCodeServiceBlockingStub discountCodeClient;
    private final IProfileRequestAddressService profileRequestAddressApi;
    private final MoeBookOnlineDepositService bookOnlineDepositService;
    private final WaitListService waitListService;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;
    private final BusinessCustomerContactServiceGrpc.BusinessCustomerContactServiceBlockingStub contactServiceStub;
    private final CompanyHelper companyHelper;
    private final NewOrderHelper newOrderHelper;

    /**
     * Get new ob booking request detail
     *
     * @param apptId appointment id
     * @return OBRequestDetailVO
     */
    public OBRequestDetailVO getOBRequestDetail(Integer apptId, Integer businessId) {
        MoeGroomingAppointment appointment = appointmentMapper.selectByPrimaryKeyAndBusinessId(apptId, businessId);
        if (Objects.isNull(appointment)
                || Objects.equals(
                        appointment.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_NOT_OB)) {
            throw bizException(Code.CODE_APPOINTMENT_NOT_FOUND);
        }
        Integer customerId = appointment.getCustomerId();
        OBRequestDetailVO obRequestDetailVO = AppointmentMapper.INSTANCE.entity2OBRequestDetailVO(appointment);
        List<MoeGroomingPetDetail> allPetDetailList =
                petDetailMapper.selectPetDetailByGroomingIdList(List.of(appointment.getId()));
        if (CollectionUtils.isEmpty(allPetDetailList)) {
            throw bizException(Code.CODE_APPOINTMENT_PET_NOT_EXIST);
        }
        Set<Integer> petIds =
                allPetDetailList.stream().map(MoeGroomingPetDetail::getPetId).collect(Collectors.toSet());
        CustomerHasRequestDTO requestDTO = customerService.getCustomerHasRequestUpdate(businessId, customerId);
        CustomerProfileRequestDTO mergedProfile = requestDTO.mergedProfile();
        if (Objects.isNull(mergedProfile) || Objects.isNull(mergedProfile.getClient())) {
            throw bizException(Code.CODE_CUSTOMER_NOT_FOUND);
        }
        Map<Integer, CustomerProfileRequestDTO.PetProfileDTO> petMap;
        if (CollectionUtils.isEmpty(mergedProfile.getPets())) {
            petMap = Map.of();
        } else {
            petMap = mergedProfile.getPets().stream()
                    .collect(Collectors.toMap(CustomerProfileRequestDTO.PetProfileDTO::getPetId, Function.identity()));
        }
        StaffIdParams staffIdParams = new StaffIdParams();
        staffIdParams.setBusinessId(businessId);
        staffIdParams.setStaffId(allPetDetailList.get(0).getStaffId());
        MoeStaffDto staff = staffService.getStaff(staffIdParams);

        var serviceIds = allPetDetailList.stream()
                .map(MoeGroomingPetDetail::getServiceId)
                .map(Integer::longValue)
                .collect(Collectors.toSet());

        var serviceIdToService = listService(businessId, serviceIds);

        Map<String, MoeBookOnlineQuestion> questionMap = questionService.getCustomQuestionMap(businessId);
        OrderDetailModel orderDetail;
        // DONE new order flow
        if (newOrderHelper.isNewOrder(apptId)) {
            // 新的流程可能会没有订单、或者创建一个 deposit 订单
            orderDetail = orderService.listOrdersByGroomingId(apptId).stream()
                    .filter(order -> Objects.equals(order.getOrder().getOrderType(), OrderModel.OrderType.DEPOSIT))
                    .findFirst()
                    .orElse(OrderDetailModel.getDefaultInstance());
        } else {
            // 老的流程一定会创建一个 appointment 订单
            orderDetail = orderService.mustGetOrderByGroomingId(businessId, apptId);
        }
        OBPrepayDetailDTO prepayDetail = prepayService.getPrepayDetail(businessId, apptId, orderDetail.getOrder());
        // get discount code
        String discountCodeName = getDiscountCode(businessId, orderDetail.getLineDiscountsList());
        // PetId - ServiceType - ServiceId
        Map<Integer, Map<Integer, List<Integer>>> petServiceTypeMap = allPetDetailList.stream()
                .collect(Collectors.groupingBy(
                        MoeGroomingPetDetail::getPetId,
                        Collectors.groupingBy(
                                MoeGroomingPetDetail::getServiceType,
                                Collectors.mapping(MoeGroomingPetDetail::getServiceId, Collectors.toList()))));
        GroomingTicketNotesDTO additionalNote = groomingNoteService.getAdditionalNote(apptId);

        // CRM-3555 emergency contact in mobile app
        var resp = contactServiceStub.listCustomerContact(ListCustomerContactRequest.newBuilder()
                .setCustomerId(customerId)
                .build());
        var contact = resp.getContactsList().stream()
                .filter(Objects::nonNull)
                .filter(c -> Objects.equals(CustomerContactEnum.TYPE_EMERGENCY.intValue(), c.getType()))
                .findFirst()
                .map(c -> {
                    var e = new AdditionalContactDTO();
                    e.setFirstName(c.getFirstName());
                    e.setLastName(c.getLastName());
                    e.setPhone(c.getPhoneNumber());
                    return e;
                })
                .orElse(new AdditionalContactDTO());
        return obRequestDetailVO.toBuilder()
                .hasRequestUpdate(requestDTO.hasRequestUpdate())
                .staff(StaffMapper.INSTANCE.dto2VO(staff))
                .additionalNote(Objects.nonNull(additionalNote) ? additionalNote.getNote() : null)
                .emergencyContact(contact)
                .customer(
                        ProfileRequestMapper.INSTANCE
                                .clientProfileDTO2ClientDetailVO(mergedProfile.getClient())
                                .toBuilder()
                                .businessId(businessId)
                                .customerId(customerId)
                                .questionAnswerList(QuestionMapper.INSTANCE.entity2QuestionAnswerVO(
                                        mergedProfile.getClient().getCustomQuestions(), questionMap))
                                .build())
                .pets(petIds.stream()
                        .map(petId -> {
                            CustomerProfileRequestDTO.PetProfileDTO pet = petMap.get(petId);
                            if (Objects.isNull(pet)) {
                                throw bizException(Code.CODE_PET_NOT_FOUND);
                            }
                            Map<Integer, List<Integer>> serviceTypeMap = petServiceTypeMap.get(petId);
                            List<Integer> serviceIdList = serviceTypeMap.get(Integer.valueOf(ServiceEnum.TYPE_SERVICE));
                            return ProfileRequestMapper.INSTANCE.petProfileDTO2PetDetailVO(pet).toBuilder()
                                    .serviceId(CollectionUtils.isEmpty(serviceIdList) ? null : serviceIdList.get(0))
                                    .addOnIds(serviceTypeMap.getOrDefault(
                                            Integer.valueOf(ServiceEnum.TYPE_ADD_ONS), List.of()))
                                    .questionAnswerList(QuestionMapper.INSTANCE.entity2QuestionAnswerVO(
                                            pet.getCustomQuestions(), questionMap))
                                    .build();
                        })
                        .toList())
                .address(ProfileRequestMapper.INSTANCE.addressDTO2AddressDetailVO(getPrimaryAddress(customerId)))
                .services(toOBServiceVOList(allPetDetailList, serviceIdToService))
                .prepay(PrepayMapper.INSTANCE.dto2vo(prepayDetail))
                .autoAssign(AutoAssignConverter.INSTANCE.entityToDTO(autoAssignService.getAutoAssign(apptId)))
                .discountCode(new DiscountCodeDTO(discountCodeName))
                .serviceItemTypes(getServiceItemTypes(serviceIdToService.values()))
                .build();
    }

    private static List<Integer> getServiceItemTypes(Collection<ServiceModel> services) {
        if (CollectionUtils.isEmpty(services)) {
            return List.of();
        }
        return services.stream()
                .map(ServiceModel::getServiceItemType)
                .distinct()
                .map(ServiceItemType::getNumber)
                .toList();
    }

    private static List<OBRequestDetailVO.OBServiceDetailVO> toOBServiceVOList(
            List<MoeGroomingPetDetail> allPetDetailList, Map<Long, ServiceModel> serviceIdToService) {
        return allPetDetailList.stream()
                .map(petDetail -> petDetailToOBServiceDetailVO(
                        petDetail,
                        serviceIdToService.get(petDetail.getServiceId().longValue())))
                .toList();
    }

    private Map<Long, ServiceModel> listService(Integer businessId, Set<Long> serviceIds) {
        return serviceStub
                .getServiceList(GetServiceListRequest.newBuilder()
                        .setTokenCompanyId(companyHelper.mustGetCompanyId(businessId))
                        .addBusinessIds(businessId)
                        .addAllServiceIds(serviceIds)
                        .build())
                .getCategoryListList()
                .stream()
                .map(ServiceCategoryModel::getServicesList)
                .flatMap(List::stream)
                .collect(Collectors.toMap(ServiceModel::getServiceId, Function.identity(), (o, n) -> o));
    }

    private static OBRequestDetailVO.OBServiceDetailVO petDetailToOBServiceDetailVO(
            MoeGroomingPetDetail petDetail, @Nullable ServiceModel service) {
        service = service != null ? service : ServiceModel.getDefaultInstance();
        return new OBRequestDetailVO.OBServiceDetailVO(
                petDetail.getServiceId(),
                service.getName(),
                (byte) service.getType().getNumber(),
                petDetail.getServiceTime(),
                petDetail.getServicePrice(),
                petDetail.getPriceOverrideType(),
                petDetail.getDurationOverrideType());
    }

    private CustomerAddressDto getPrimaryAddress(Integer customerId) {
        ProfileRequestAddressDTO cPrimaryAddress =
                profileRequestAddressApi
                        .listByCustomerIds(List.of(customerId))
                        .getOrDefault(customerId, List.of())
                        .stream()
                        .filter(ProfileRequestAddressDTO::getIsPrimary)
                        .findFirst()
                        .orElse(null);
        return cPrimaryAddress != null
                ? ProfileRequestAddressDTO.toCustomerAddressDTO(cPrimaryAddress)
                : obAddressService.getPrimaryAddress(customerId);
    }

    private String getDiscountCode(Integer businessId, List<OrderLineDiscountModel> lineDiscountsList) {
        if (CollectionUtils.isEmpty(lineDiscountsList)) {
            return null;
        }

        List<Long> discountCodeIdList = lineDiscountsList.stream()
                .map(OrderLineDiscountModel::getDiscountCodeId)
                .filter(id -> id > 0)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(discountCodeIdList) || discountCodeIdList.size() > 1) {
            return null;
        }

        GetDiscountCodeOutput discountCodeOutput;
        try {
            discountCodeOutput = discountCodeClient.getDiscountCode(GetDiscountCodeInput.newBuilder()
                    .setBusinessId(businessId)
                    .setId(discountCodeIdList.get(0))
                    .build());
        } catch (Exception e) {
            log.error("get discount code error", e);
            return null;
        }

        return discountCodeOutput.getDiscountCodeModel().getDiscountCode();
    }

    public GroomingOnlyBookingRequestDTO getBookingRequest(Integer appointmentId) {
        MoeGroomingAppointment appointment = appointmentMapper.selectByPrimaryKey(appointmentId);
        if (appointment == null) {
            return null;
        }
        GroomingOnlyBookingRequestDTO dto = new GroomingOnlyBookingRequestDTO();
        // no start time 标记为 false 时，才会返回 start time 和 end time
        if (appointment.getNoStartTime() == null || !appointment.getNoStartTime()) {
            dto.setStartTime(appointment.getAppointmentStartTime()).setEndTime(appointment.getAppointmentEndTime());
        }
        return dto.setCompanyId(appointment.getCompanyId())
                .setBusinessId(appointment.getBusinessId())
                .setCustomerId(appointment.getCustomerId())
                .setAppointmentId(appointment.getId())
                .setStartDate(appointment.getAppointmentDate())
                .setEndDate(appointment.getAppointmentEndDate())
                .setStatus(buildStatus(appointment))
                .setIsPrepaid(buildPrepaid(appointment))
                .setAdditionalNote(buildAdditionalNote(appointment))
                .setSourcePlatform(appointment.getSourcePlatform())
                .setCreatedAt(appointment.getCreateTime())
                .setUpdatedAt(appointment.getUpdateTime())
                .setDeletedAt(appointment.getCanceledTime())
                .setGroomingServiceDetails(buildGroomingServiceDetails(appointment))
                .setAutoAssign(buildAutoAssign(appointment))
                .setServiceTypeInclude(appointment.getServiceTypeInclude());
    }

    private GroomingOnlyBookingRequestDTO.GroomingAutoAssignDTO buildAutoAssign(MoeGroomingAppointment appointment) {
        AutoAssign autoAssign = autoAssignService.getAutoAssign(appointment.getId());
        if (autoAssign == null) {
            return null;
        }
        return new GroomingOnlyBookingRequestDTO.GroomingAutoAssignDTO()
                .setStaffId(autoAssign.getStaffId())
                .setStartTime(autoAssign.getAppointmentTime())
                .setCreatedAt(autoAssign.getCreateTime())
                .setUpdatedAt(autoAssign.getUpdateTime());
    }

    private List<GroomingOnlyBookingRequestDTO.GroomingServiceDetailDTO> buildGroomingServiceDetails(
            MoeGroomingAppointment appointment) {
        List<MoeGroomingPetDetail> petDetails = petDetailMapper.queryPetDetailCountByGroomingId(appointment.getId());
        return petDetails.stream()
                .map(petDetail -> {
                    GroomingOnlyBookingRequestDTO.GroomingServiceDetailDTO dto =
                            new GroomingOnlyBookingRequestDTO.GroomingServiceDetailDTO();
                    // no start time 标记为 false 时，才会返回 start time 和 end time
                    if (appointment.getNoStartTime() == null || !appointment.getNoStartTime()) {
                        dto.setStartTime(petDetail.getStartTime().intValue())
                                .setEndTime(petDetail.getEndTime().intValue());
                    }
                    return dto.setPetId(petDetail.getPetId())
                            .setStaffId(petDetail.getStaffId())
                            .setServiceId(petDetail.getServiceId())
                            .setServiceTime(petDetail.getServiceTime())
                            .setServicePrice(petDetail.getServicePrice())
                            .setStartDate(petDetail.getStartDate())
                            .setEndDate(petDetail.getEndDate())
                            .setCreatedAt(petDetail.getUpdateTime())
                            .setUpdatedAt(petDetail.getUpdateTime());
                })
                .toList();
    }

    private Integer buildStatus(MoeGroomingAppointment appointment) {
        if (Objects.equals(appointment.getStatus(), AppointmentStatusEnum.CANCELED.getValue())) {
            return BookingRequestStatus.DECLINED_VALUE;
        }
        MoeWaitList waitList = waitListService.getWaitListByAppointment(
                appointment.getBusinessId().longValue(), appointment.getId().longValue());
        // 无 wait list 记录，有可能被 scheduled
        if (waitList == null) {
            return Objects.equals(appointment.getBookOnlineStatus(), CommonConstant.ENABLE)
                    ? BookingRequestStatus.SUBMITTED_VALUE
                    : BookingRequestStatus.SCHEDULED_VALUE;
        }
        // 有 wait list 记录，有可能被删除
        return Objects.isNull(waitList.getDeletedAt())
                ? BookingRequestStatus.WAIT_LIST_VALUE
                : BookingRequestStatus.DELETED_VALUE;
    }

    private Boolean buildPrepaid(MoeGroomingAppointment appointment) {
        MoeBookOnlineDeposit deposit =
                bookOnlineDepositService.getOBDepositByGroomingId(appointment.getBusinessId(), appointment.getId());
        return deposit != null
                && List.of(
                                BookOnlineDepositConst.REQUIRE_CAPTURE,
                                BookOnlineDepositConst.PROCESSING_CAPTURE,
                                BookOnlineDepositConst.PAID,
                                // Declined
                                BookOnlineDepositConst.REFUNDED,
                                // Expired
                                BookOnlineDepositConst.CANCEL)
                        .contains(deposit.getStatus());
    }

    private String buildAdditionalNote(MoeGroomingAppointment appointment) {
        GroomingTicketNotesDTO additionalNote = groomingNoteService.getAdditionalNote(appointment.getId());
        return additionalNote == null ? null : additionalNote.getNote();
    }
}
