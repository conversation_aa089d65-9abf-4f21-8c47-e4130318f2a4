package com.moego.server.grooming.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
public class RouteOptimizationRequest {

    @Schema(description = "开始地址经度")
    @NotBlank
    String startLocationLat;

    @Schema(description = "开始地址纬度")
    @NotBlank
    String startLocationLnt;

    @Schema(description = "结束地址经度")
    @NotBlank
    String endLocationLat;

    @Schema(description = "结束地址纬度")
    @NotBlank
    String endLocationLnt;

    @Schema(description = "员工编号")
    @NotNull
    @Min(1)
    Integer staffId;

    @Schema(description = "查询日期")
    @NotNull
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    LocalDate date;
}
