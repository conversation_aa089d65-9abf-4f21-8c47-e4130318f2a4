package com.moego.server.grooming.web;

import com.moego.common.dto.CommonResultDto;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.Env;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import com.moego.server.grooming.service.QuickBooksService;
import com.moego.server.grooming.service.QuickBooksSyncService;
import com.moego.server.grooming.service.dto.QBAccountReturnDto;
import com.moego.server.grooming.service.dto.QBBusinessSettingDto;
import com.moego.server.grooming.service.dto.QBConnectReturnDto;
import com.moego.server.grooming.service.dto.QbInvoiceStatusDto;
import com.moego.server.grooming.web.vo.QBCreateConnectVo;
import com.moego.server.grooming.web.vo.QBSettingUpdateVo;
import com.moego.server.grooming.web.vo.QbSetUpVo;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * <AUTHOR>
 */
@RestController
@RequestMapping("/grooming/qb")
@Slf4j
public class QuickBooksController {
    @Autowired
    private Environment environment;

    @Autowired
    QuickBooksService quickBooksService;

    @Autowired
    QuickBooksSyncService quickBooksSyncService;

    /**
     * @param context
     * @return
     * @deprecated 2024-04-01 forRemoval = true 后续需要显示指定 businessId
     */
    @GetMapping("/setting")
    @Operation(summary = "获取商家设置信息", description = "用于设置界面和qb oauth回调界面的配置信息获取")
    @Auth(AuthType.BUSINESS)
    @Deprecated(since = "2024-04-01", forRemoval = true)
    public QBBusinessSettingDto getQbConnectSetting(AuthContext context) {
        return quickBooksService.getQbConnectSetting(context.getBusinessId());
    }

    @GetMapping("/setting/business")
    @Operation(summary = "获取指定商家设置信息", description = "用于设置界面和qb oauth回调界面的配置信息获取")
    @Auth(AuthType.COMPANY)
    public QBBusinessSettingDto getQbConnectSettingByBusinessId(AuthContext context, @RequestParam Integer businessId) {
        quickBooksSyncService.checkBusinessIdAndCompanyId(businessId, context.companyId());
        return quickBooksService.getQbConnectSetting(businessId);
    }

    @GetMapping("/setting/company")
    @Operation(summary = "获取company下所有business设置信息", description = "用于设置界面和qb oauth回调界面的配置信息获取")
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_QUICKBOOK_SYNC})
    public List<QBBusinessSettingDto> getQbConnectSettingByCompanyId(AuthContext context) {
        return quickBooksService.getQbConnectSettingByCompanyId(context.getCompanyId());
    }

    /**
     * @return
     * @deprecated 2024-04-01 forRemoval = true 后续无需指定账本
     */
    @GetMapping("/account")
    @Operation(summary = "获取商家当前关联的qb账号，有那些bank类型的account(账本)")
    @Auth(AuthType.BUSINESS)
    @Deprecated(since = "2024-04-01", forRemoval = true)
    public QBAccountReturnDto getBusinessConnectAccount(AuthContext context) {
        try {
            return quickBooksService.getBusinessConnectAccount(context.getBusinessId());
        } catch (CommonException e) {
            QBAccountReturnDto returnDto = new QBAccountReturnDto();
            returnDto.setAccountList(Collections.emptyList());
            return returnDto;
        }
    }

    @PostMapping("/connect/oauth/url")
    @Operation(summary = "qb oauth链接", description = "获取qb oauth链接")
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_QUICKBOOK_SYNC})
    public String getConnectOAuthUrl(AuthContext context, @RequestBody List<Integer> businessIds) {
        return quickBooksService.getConnectOAuthUrl(businessIds);
    }

    /**
     * 创建business setting
     *
     * @param context
     * @param vo
     * @return
     */
    @PostMapping("/connect/create")
    @Operation(summary = "quickbooks回调后调用，保存code等信息", description = "这里返回的connectId，一会需要回传")
    @Auth(AuthType.BUSINESS)
    public QBConnectReturnDto createQbSetting(AuthContext context, @Valid @RequestBody QBCreateConnectVo vo) {
        vo.setCompanyId(context.getCompanyId());
        vo.setBusinessId(context.getBusinessId());
        return quickBooksService.qbCreateConnect(vo);
    }

    /**
     * 创建qb connect
     *
     * @param context
     * @param connectId
     * @return
     */
    @PostMapping("/setting/create")
    @Operation(summary = "基于connectId，创建商家设置信息", description = "quickbooks回调界面，商家点击确定协议后，最后调用此接口")
    @Auth(AuthType.BUSINESS)
    @Deprecated(since = "2024-04-01", forRemoval = true)
    public CommonResultDto qbCreateSetting(AuthContext context, @RequestParam Integer connectId) {
        CommonResultDto returnDto = new CommonResultDto();
        returnDto.setResult(quickBooksService.qbCreateSetting(context.getBusinessId(), context.companyId(), connectId));
        return returnDto;
    }

    @PostMapping("/setUp")
    @Operation(summary = "基于connectId，创建商家设置信息", description = "quickbooks回调界面，商家点击确定协议后，最后调用此接口")
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_QUICKBOOK_SYNC})
    public String qbSetup(AuthContext context, @Valid @RequestBody QbSetUpVo vo) {
        // create setting in batch
        vo.setCompanyId(context.getCompanyId());
        return quickBooksService.qbSetUp(vo);
    }

    /**
     * 设置更新
     *
     * @param context
     * @param updateVo
     * @return
     */
    @PutMapping("/setting")
    @Operation(summary = "setting更新接口", description = "所有参数都可以不传，建议不更新的参数不要传递")
    @Auth(AuthType.BUSINESS)
    public CommonResultDto createBusinessQbProfile(AuthContext context, @RequestBody QBSettingUpdateVo updateVo) {
        CommonResultDto returnDto = new CommonResultDto();
        if (PrimitiveTypeUtil.isNullOrZeroOrNegative(updateVo.getBusinessId())) {
            // 新版 qb setting 时要指定 businessId， 如果没有指定， 取默认session 中的 business id
            updateVo.setBusinessId(context.getBusinessId());
        } else {
            // check business id is valid
            quickBooksSyncService.checkBusinessIdAndCompanyId(updateVo.getBusinessId(), context.companyId());
        }
        returnDto.setResult(quickBooksService.updateBusinessSetting(updateVo));
        return returnDto;
    }

    @GetMapping("/invoice/status")
    @Operation(summary = "获取invoice状态")
    @Auth(AuthType.BUSINESS)
    public QbInvoiceStatusDto getInvoiceSyncStatus(AuthContext context, Integer invoiceId) {
        return quickBooksSyncService.getInvoiceSyncStatus(context.getBusinessId(), invoiceId);
    }

    @PostMapping("/sync/one")
    @Operation(summary = "同步单个预约")
    @Auth(AuthType.BUSINESS)
    public boolean syncGroomingAppointment(AuthContext context, Integer groomingId) {
        return quickBooksSyncService.testSyncInvoiceByGroomingIdBusinessId(context.getBusinessId(), groomingId);
    }

    @PostMapping("/sync/one/invoice")
    @Operation(summary = "同步单个 invoice")
    @Auth(AuthType.BUSINESS)
    public String syncInvoice(AuthContext context, Integer invoiceId) {
        return quickBooksSyncService.testSyncInvoiceByInvoiceId(context.getBusinessId(), invoiceId);
    }

    @DeleteMapping("/sync/one/invoice")
    @Operation(summary = "删除单个 invoice")
    @Auth(AuthType.BUSINESS)
    public String deleteInvoice(AuthContext context, Integer invoiceId) {
        return quickBooksSyncService.deleteInvoice(context.getBusinessId(), invoiceId);
    }

    @DeleteMapping("/sync/all/invoice")
    @Operation(summary = "删除 business 所有 invoice")
    @Auth(AuthType.BUSINESS)
    public String deleteAllInvoice(AuthContext context, Integer businessId) {
        return quickBooksSyncService.deleteAllInvoice(context.getBusinessId(), businessId);
    }

    @PostMapping("/sync/deleted/all")
    @Operation(summary = "删除指定biz下所有的qb sync数据")
    @Auth(AuthType.ANONYMOUS)
    public String syncDeletedCustomerTask(Integer businessId, Long startTime, Long endTime) {
        if (startTime == null || endTime == null) {
            if (!isTestEnv())
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "startTime and endTime can not be null");
            // 2024-04-11 10:00:00 - 2024-04-11 15:00:00 (UTC时间)
            startTime = 1712834700L;
            endTime = 1712846100L;
        }
        quickBooksSyncService.deleteAll(businessId, startTime, endTime);
        return "success";
    }

    @PostMapping("/sync/task/test")
    @Operation(summary = "触发定时任务")
    @Auth(AuthType.ANONYMOUS)
    public String syncTask() {
        if (!isTestEnv()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Environment error");
        }
        List<String> taskResult = quickBooksService.beginTask();
        StringBuilder stringBuilder = new StringBuilder();
        for (String result : taskResult) {
            stringBuilder.append(result).append("\n");
        }
        return stringBuilder.toString();
    }

    @PostMapping("/sync/refresh/metadata")
    @Operation(summary = "刷新metadata")
    @Auth(AuthType.BUSINESS)
    public String refreshMetadata(AuthContext context, @RequestParam(required = false, defaultValue = "2") Byte mode) {
        // 开放正式环境使用, pre onboarding 前会刷新用户数据
        return quickBooksService.refreshMetadata(context.getBusinessId(), mode);
    }

    @PostMapping("/sync/migration")
    @Operation(summary = "迁移数据, from qb 1.0 to qb 2.0")
    @Auth(AuthType.BUSINESS)
    public String migrationQBData(AuthContext context, Integer businessId, String endDate) {
        if (!isTestEnv()) {
            // 正式环境一定要指定endDate
            if (!StringUtils.hasText(endDate)) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "endDate can not be null");
            }
        }
        log.info(
                "migration qb data, operator business id: {}. migration business id:{}, endDate: {}",
                context.getBusinessId(),
                businessId,
                endDate);
        return quickBooksService.migrationQBDataByBusinessId(businessId, endDate);
    }

    private boolean isTestEnv() {
        return Arrays.stream(environment.getActiveProfiles())
                .anyMatch(profile -> Env.TEST2.getValue().equalsIgnoreCase(profile)
                        || Env.STAGING.getValue().equalsIgnoreCase(profile)
                        || Env.LOCAL.getValue().equalsIgnoreCase(profile));
    }
}
