package com.moego.server.grooming.web.vo.ob;

import com.moego.server.business.dto.BusinessWorkingHourDayDetailDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Landing page merge profile vo
 *
 * <AUTHOR>
 * @since 2023/2/23
 */
@Data
@Accessors(chain = true)
public class OBLandingPageMergeVO {

    @Schema(description = "OB business profile")
    private OBProfileMergeVO obProfile;

    @Schema(description = "Business settings profile")
    private BizProfileMergeVO bizProfile;

    @Schema(description = "OB business working hours")
    private BusinessWorkingHourDayDetailDTO obWorkingHours;

    @Schema(description = "Business settings working hours")
    private BusinessWorkingHourDayDetailDTO bizWorkingHours;

    @Data
    public static class OBProfileMergeVO {

        @Schema(description = "Business profile image path")
        private String avatarPath;

        @Schema(description = "Business info name")
        private String businessName;

        @Schema(description = "Business phone")
        private String phoneNumber;

        @Schema(description = "Business address")
        private String address;

        @Schema(description = "Facebook")
        private String facebook;

        @Schema(description = "Instagram")
        private String instagram;

        @Schema(description = "Google")
        private String google;

        @Schema(description = "Yelp")
        private String yelp;

        @Schema(description = "Website")
        private String website;
    }

    @Data
    public static class BizProfileMergeVO {

        @Schema(description = "Business info logo path")
        private String avatarPath;

        @Schema(description = "Business info name")
        private String businessName;

        @Schema(description = "Business info phone number")
        private String phoneNumber;

        @Schema(description = "Full address")
        private String address;

        @Schema(description = "Address 1")
        private String address1;

        @Schema(description = "Address 2")
        private String address2;

        @Schema(description = "Address city")
        private String addressCity;

        @Schema(description = "Address state")
        private String addressState;

        @Schema(description = "Address zipcode")
        private String addressZipcode;

        @Schema(description = "Address country")
        private String addressCountry;

        @Schema(description = "Address latitude")
        private String addressLat;

        @Schema(description = "Address longitude")
        private String addressLng;

        @Schema(description = "Facebook")
        private String facebook;

        @Schema(description = "Instagram")
        private String instagram;

        @Schema(description = "Google")
        private String google;

        @Schema(description = "Yelp")
        private String yelp;

        @Schema(description = "Website")
        private String website;
    }
}
