package com.moego.server.grooming.service.ob.component;

import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.service.ob.OBLandingPageGalleryService;
import com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO;
import com.moego.server.grooming.web.vo.ob.component.BaseComponentVO;
import com.moego.server.grooming.web.vo.ob.component.GalleryComponentVO;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@Service(value = LandingPageComponentEnum.COMPONENT_GALLERY)
@AllArgsConstructor
public class GalleryComponentService implements ILandingPageComponentService {

    private final OBLandingPageGalleryService landingPageGalleryService;

    @Override
    public BaseComponentVO getPageComponent(MoeBookOnlineLandingPageConfig landingPageConfig) {
        List<OBLandingPageConfigVO.OBLandingPageGalleryVO> galleryList =
                landingPageGalleryService.listAvailableLandingPageGallery(landingPageConfig.getBusinessId());
        return new GalleryComponentVO().setGalleryList(galleryList);
    }
}
