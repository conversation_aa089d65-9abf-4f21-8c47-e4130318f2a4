package com.moego.server.grooming.web;

import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.BindingErrorUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.server.grooming.dto.ApplicableServiceByCategoryDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.dto.PetServiceDTO;
import com.moego.server.grooming.dto.ServiceCategoryDTO;
import com.moego.server.grooming.dto.ob.ServiceAreaResultDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.params.PetServicePageParams;
import com.moego.server.grooming.params.QueryServiceParams;
import com.moego.server.grooming.params.ob.ServiceAreaParams;
import com.moego.server.grooming.service.CompanyGroomingServiceQueryService;
import com.moego.server.grooming.service.CompanyGroomingServiceService;
import com.moego.server.grooming.service.GroomingServiceService;
import com.moego.server.grooming.service.ParamAuthService;
import com.moego.server.grooming.service.dto.CompanyServiceOBSettingUpdateDto;
import com.moego.server.grooming.service.dto.CompanyServiceUpdateDto;
import com.moego.server.grooming.service.dto.ServiceSaveDto;
import com.moego.server.grooming.service.dto.ServiceTaxUpdateForApptDto;
import com.moego.server.grooming.service.dto.ServiceUpdateDto;
import com.moego.server.grooming.service.ob.OBBusinessService;
import com.moego.server.grooming.utils.PetDetailDTOUtil;
import com.moego.server.grooming.web.dto.ob.IsAvailableDto;
import com.moego.server.grooming.web.dto.service.AddServiceResultDto;
import com.moego.server.grooming.web.dto.service.ServiceCategoryGroupedResultDto;
import com.moego.server.grooming.web.vo.DeleteIdVo;
import com.moego.server.grooming.web.vo.PetServicePageVO;
import com.moego.server.grooming.web.vo.SortIdListVo;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
public class MoeGroomingServiceController {

    @Autowired
    private GroomingServiceService groomingServiceService;

    @Autowired
    private CompanyGroomingServiceService companyGroomingServiceService;

    @Autowired
    private CompanyGroomingServiceQueryService companyGroomingServiceQueryService;

    @Autowired
    private OBBusinessService businessService;

    @Autowired
    private ParamAuthService paramAuthService;

    @Autowired
    private MigrateHelper migrateHelper;

    /**
     * setting 查询Service
     *
     * @param type    1: main service   2:addons
     * @param context
     * @return rr
     */
    @GetMapping("/grooming/serviceCategory/service")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<ServiceCategoryGroupedResultDto> getEditServiceWithCategory(
            AuthContext context,
            @RequestParam Byte type,
            @RequestParam(required = false) Byte inactive,
            @RequestParam(required = false, defaultValue = "1") Integer serviceItemType) {
        ServiceCategoryGroupedResultDto returnMap = new ServiceCategoryGroupedResultDto();
        returnMap.setCategoryList(groomingServiceService.getEditServiceWithCategory(
                context.companyId(),
                context.getBusinessId(),
                type,
                inactive,
                PetDetailDTOUtil.mapServiceItemType(serviceItemType)));
        return ResponseResult.success(returnMap);
    }

    @GetMapping("/grooming/serviceCategory/careTypeService")
    @Auth(AuthType.BUSINESS)
    public ServiceCategoryGroupedResultDto getCareTypeServiceWithCategory(
            AuthContext context,
            @RequestParam(required = false, defaultValue = "false") Boolean inactive,
            @RequestParam Long careTypeId) {
        ServiceCategoryGroupedResultDto returnMap = new ServiceCategoryGroupedResultDto();
        returnMap.setCategoryList(groomingServiceService.getCareTypeServiceWithCategory(
                context.companyId(), context.getBusinessId(), inactive, careTypeId));
        return returnMap;
    }

    @PostMapping("/grooming/service/page")
    @Auth(AuthType.BUSINESS)
    public PetServicePageVO getServiceByPage(AuthContext context, @RequestBody @Valid PetServicePageParams params) {
        Integer businessId = migrateHelper.isMigrate(context) ? null : context.getBusinessId();

        return groomingServiceService.getServiceByPage(context.companyId(), businessId, params);
    }

    @PostMapping("/grooming/company/service/page")
    @Auth(AuthType.BUSINESS)
    public PetServicePageVO getCompanyServiceByPage(
            AuthContext context, @RequestBody @Valid PetServicePageParams params) {
        return companyGroomingServiceService.getCompanyServiceByPage(context.companyId(), params);
    }

    @PostMapping("/grooming/service/info")
    @Auth(AuthType.BUSINESS)
    @Operation(summary = "根据 serviceId 查询 service 信息，包括被删除、inactive的")
    public List<MoeGroomingServiceDTO> getServicesByIds(AuthContext context, @RequestBody List<Integer> ids) {
        var bid = context.getBusinessId();
        if (companyGroomingServiceQueryService.companyIsMigrate(context.companyId())) {
            bid = null;
        }
        var serviceList = groomingServiceService.getServicesByServiceIds(bid, ids);
        for (MoeGroomingServiceDTO service : serviceList) {
            if (!service.getCompanyId().equals(context.companyId())) {
                return Collections.emptyList();
            }
        }

        return serviceList;
    }

    /**
     * setting 新建Service
     *
     * @param context        tokenBusinessId
     * @param serviceSaveDto serviceSaveDto
     * @param bindingResult  bindingResult
     * @return rr
     * DONE(Frank): categoryId -> businessId
     * taxId -> businessId
     */
    @PostMapping("/grooming/service")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.CREATE,
            resourceType = ResourceType.GROOMING_SERVICE,
            resourceId = "#result.data.serviceId",
            details = "#serviceSaveDto")
    public ResponseResult<AddServiceResultDto> createService(
            AuthContext context, @Validated @RequestBody ServiceSaveDto serviceSaveDto, BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        paramAuthService.authServiceCategoryWithCid(serviceSaveDto.getCategoryId(), context.companyId());
        // 在 business 内校验 serviceName 是否存在
        if (groomingServiceService.checkServiceNameIsExist(
                context.getBusinessId(), serviceSaveDto.getName(), serviceSaveDto.getType())) {
            throw ExceptionUtil.bizException(Code.CODE_SERVICE_NAME_IS_EXIST);
        }
        // weight, breed filter参数校验
        groomingServiceService.checkServiceFilterParams(serviceSaveDto);
        Integer serviceId =
                groomingServiceService.createService(serviceSaveDto, context.getBusinessId(), context.companyId());
        AddServiceResultDto returnMap = new AddServiceResultDto();
        returnMap.setServiceId(serviceId);
        returnMap.setResult(serviceId != null);
        return ResponseResult.success(returnMap);
    }

    @PostMapping("/grooming/company/service")
    @Auth(AuthType.COMPANY)
    @ActivityLog(
            action = Action.CREATE,
            resourceType = ResourceType.GROOMING_SERVICE,
            resourceId = "#result.data.serviceId",
            details = "#serviceSaveDto")
    public AddServiceResultDto companyCreateService(
            AuthContext context, @Valid @RequestBody ServiceSaveDto serviceSaveDto) {
        paramAuthService.authServiceCategoryWithCid(serviceSaveDto.getCategoryId(), context.companyId());

        // 在 company 内校验 serviceName 是否存在
        if (companyGroomingServiceService.checkServiceNameIsExistInCompanyId(
                context.companyId(), serviceSaveDto.getName(), serviceSaveDto.getType())) {
            throw ExceptionUtil.bizException(Code.CODE_SERVICE_NAME_IS_EXIST);
        }
        // weight, breed filter参数校验
        groomingServiceService.checkServiceFilterParams(serviceSaveDto);
        Integer serviceId = companyGroomingServiceService.createService(serviceSaveDto, context.companyId());
        AddServiceResultDto returnMap = new AddServiceResultDto();
        returnMap.setServiceId(serviceId);
        returnMap.setResult(serviceId != null);
        return returnMap;
    }

    /**
     * setting 更新Service
     *
     * @param context          tokenBusinessId
     * @param serviceUpdateDto serviceUpdateDto
     * @param bindingResult    bindingResult
     * @return rb
     */
    @PutMapping("/grooming/service")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.GROOMING_SERVICE,
            resourceId = "#serviceUpdateDto.serviceId",
            details = "#serviceUpdateDto",
            beforeInvocation = true)
    public ResponseResult<Boolean> updateService(
            AuthContext context,
            @Validated @RequestBody ServiceUpdateDto serviceUpdateDto,
            BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        // bid 参数校验
        groomingServiceService.checkServiceIdWithBidAndNameIsValid(
                context.companyId(),
                context.getBusinessId(),
                serviceUpdateDto.getServiceId(),
                serviceUpdateDto.getName());

        // weight, breed filter参数校验
        groomingServiceService.checkServiceFilterParams(serviceUpdateDto);
        return ResponseResult.success(groomingServiceService.updateService(
                serviceUpdateDto, context.getBusinessId(), context.companyId(), context.getStaffId()));
    }

    @PutMapping("/grooming/company/service")
    @Auth(AuthType.COMPANY)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.GROOMING_SERVICE,
            resourceId = "#serviceUpdateDto.serviceId",
            details = "#serviceUpdateDto",
            beforeInvocation = true)
    public void updateUpdateService(
            AuthContext context,
            @Validated @RequestBody CompanyServiceUpdateDto serviceUpdateDto,
            BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);

        // cid 越权参数校验
        // name 唯一检查
        companyGroomingServiceService.checkServiceIdWithCidAndNameIsValid(
                context.companyId(), serviceUpdateDto.getServiceId(), serviceUpdateDto.getName());

        companyGroomingServiceService.updateCompanyService(context.companyId(), serviceUpdateDto, context.getStaffId());
    }

    @PutMapping("/grooming/service/location/ob/setting")
    @Auth(AuthType.COMPANY)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.GROOMING_SERVICE,
            resourceId = "#serviceUpdateDto.serviceId",
            details = "#serviceUpdateDto",
            beforeInvocation = true)
    public void updateUpdateService(
            AuthContext context, @Validated @RequestBody CompanyServiceOBSettingUpdateDto serviceUpdateDto) {
        companyGroomingServiceService.updateServiceObSetting(
                context.companyId(), serviceUpdateDto.getLocationId(), serviceUpdateDto);
    }

    /**
     * appt invoice 更新Service tax
     *
     * @param context          tokenBusinessId
     * @param serviceUpdateDto serviceUpdateDto
     * @param bindingResult    bindingResult
     * @return rb
     * DONE(Frank): serviceId -> businessId
     * taxId -> businessId
     */
    @PutMapping("/grooming/service/tax")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.UPDATE_TAX,
            resourceType = ResourceType.GROOMING_SERVICE,
            resourceId = "#serviceUpdateDto.serviceId",
            details = "#serviceUpdateDto",
            beforeInvocation = true)
    public ResponseResult<Boolean> updateServiceForApptInvoice(
            AuthContext context,
            @Validated @RequestBody ServiceTaxUpdateForApptDto serviceUpdateDto,
            BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);

        if (serviceUpdateDto.getServiceId() != null
                && !companyGroomingServiceQueryService.checkCompanyIdServiceId(
                        context.companyId(), serviceUpdateDto.getServiceId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "service mismatch");
        }

        if (serviceUpdateDto.getTaxId() != null) {
            if (migrateHelper.isMigrate(context)) {
                if (!groomingServiceService.checkCompanyTaxId(context.companyId(), serviceUpdateDto.getTaxId())) {
                    throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "tax mismatch");
                }
            } else {
                if (!groomingServiceService.checkBusinessTaxId(context.getBusinessId(), serviceUpdateDto.getTaxId())) {
                    throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "tax mismatch");
                }
            }
        }

        return ResponseResult.success(
                groomingServiceService.updateServiceForApptInvoice(context.getBusinessId(), serviceUpdateDto));
    }

    /**
     * setting 更新Service 排序
     *
     * @param context
     * @param idListVo      idListVo
     * @param bindingResult bindingResult
     * @return rb
     */
    @PutMapping("/grooming/service/sort")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Boolean> serviceSort(
            AuthContext context, @Validated @RequestBody SortIdListVo idListVo, BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        return ResponseResult.success(groomingServiceService.sortService(context.companyId(), idListVo.getIdList()));
    }

    /**
     * setting 删除Service
     *
     * @param context       tokenBusinessId
     * @param idVo          idVo
     * @param bindingResult bindingResult
     * @return rr
     */
    @DeleteMapping("/grooming/service")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.DELETE,
            resourceType = ResourceType.GROOMING_SERVICE,
            resourceId = "#idVo.id",
            details = "#idVo",
            beforeInvocation = true)
    public ResponseResult<Boolean> deleteService(
            AuthContext context, @Validated @RequestBody DeleteIdVo idVo, BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        return ResponseResult.success(groomingServiceService.deleteService(
                idVo.getId(), context.companyId(), context.getBusinessId(), context.getStaffId()));
    }

    /**
     * @param context
     * @param queryServiceParams
     * @return
     */
    @GetMapping("/grooming/services")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<ServiceCategoryDTO>> queryPetServices(
            AuthContext context, QueryServiceParams queryServiceParams) {
        queryServiceParams.setBusinessId(context.getBusinessId());
        return ResponseResult.success(groomingServiceService.queryPetServices(context.companyId(), queryServiceParams));
    }

    /**
     * 查询可用的服务
     *
     * @param context
     * @param customerId
     * @param petId
     * @param type       1-主服务(service) 2-额外服务(addons)
     * @return
     */
    @GetMapping("/grooming/applicable/service")
    @Auth(AuthType.BUSINESS)
    public List<PetServiceDTO> queryApplicableService(
            AuthContext context,
            @RequestParam("customerId") Integer customerId,
            @RequestParam("petId") Integer petId,
            @RequestParam(value = "type", required = false) Integer type) {
        QueryServiceParams params = new QueryServiceParams();
        params.setBusinessId(context.getBusinessId());
        params.setCustomerId(customerId);
        params.setPetId(petId);
        params.setType(type);
        return groomingServiceService.queryApplicableService(context.companyId(), params);
    }

    @GetMapping("/grooming/applicable/sorted/service")
    @Auth(AuthType.BUSINESS)
    public List<ApplicableServiceByCategoryDTO> queryApplicableServiceV2(
            AuthContext context,
            @RequestParam("customerId") Integer customerId,
            @RequestParam("petId") Integer petId,
            @RequestParam(value = "type", required = false) Integer type) {
        QueryServiceParams params = new QueryServiceParams();
        params.setBusinessId(context.getBusinessId());
        params.setCustomerId(customerId);
        params.setPetId(petId);
        params.setType(type);
        return groomingServiceService.queryApplicableServiceV2(context.companyId(), params);
    }

    /**
     * 前端用于判断用户地址是否属于 obsetting 的 zipcode、radius
     * @return
     */
    @GetMapping("/grooming/distance")
    @Auth(AuthType.BUSINESS)
    public IsAvailableDto checkAvailableDist(
            AuthContext context, @RequestParam String lat, @RequestParam String lng, @RequestParam String zipcode) {

        ServiceAreaParams.ClientAddressParams clientAddressParams = new ServiceAreaParams.ClientAddressParams();
        clientAddressParams.setLat(lat);
        clientAddressParams.setLng(lng);
        clientAddressParams.setZipcode(zipcode);
        ServiceAreaParams serviceAreaParams = new ServiceAreaParams();
        serviceAreaParams.setBusinessId(context.getBusinessId());
        serviceAreaParams.setAddressParamsList(Collections.singletonList(clientAddressParams));
        List<ServiceAreaResultDTO> resultList = businessService.getOBServiceAreaResultList(serviceAreaParams);
        // result
        IsAvailableDto result = new IsAvailableDto();
        result.setIsAvailable(BooleanUtils.isNotTrue(resultList.stream()
                .findFirst()
                .orElse(new ServiceAreaResultDTO())
                .getOutOfArea()));
        return result;
    }
}
