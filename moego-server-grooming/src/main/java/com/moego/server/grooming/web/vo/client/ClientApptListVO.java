package com.moego.server.grooming.web.vo.client;

import com.moego.common.dto.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/2
 */
@Data
@Accessors(chain = true)
public class ClientApptListVO {

    @Schema(description = "Online booking profile")
    private List<OBProfileVO> obProfileList;

    @Schema(description = "Business info")
    private List<BusinessInfoVO> businessInfoList;

    @Schema(description = "Appointment basic info")
    private PageDTO<ClientApptVO> apptPage;

    @Schema(description = "Online booking config")
    private List<OBConfigVO> obConfigList;
}
