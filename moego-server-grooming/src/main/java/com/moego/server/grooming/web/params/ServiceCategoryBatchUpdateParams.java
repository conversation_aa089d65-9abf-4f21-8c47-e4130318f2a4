package com.moego.server.grooming.web.params;

import com.moego.server.grooming.service.dto.ServiceCategoryUpdateDto;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;
import org.apache.pulsar.shade.io.swagger.annotations.ApiModelProperty;

@Data
public class ServiceCategoryBatchUpdateParams {
    @NotNull
    @ApiModelProperty("根据 categorySaveList 进行全量更新" + "categoryId is null 是创建"
            + "categoryId is not null 是更新"
            + "某个 category 不在这个 list 内，会被执行删除 ")
    private List<ServiceCategoryUpdateDto> categorySaveList;

    @NotNull
    private Byte type;

    private Integer serviceItemType;
}
