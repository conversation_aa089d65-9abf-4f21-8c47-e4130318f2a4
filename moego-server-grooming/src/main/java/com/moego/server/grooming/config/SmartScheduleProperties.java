package com.moego.server.grooming.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/3/18
 */
@Data
@Configuration(proxyBeanMethods = false)
@ConfigurationProperties(prefix = "smart-scheduling")
public class SmartScheduleProperties {

    private int timeout;

    private int corePoolSize;

    private int maximumPoolSize;

    private int aliveTimeSeconds;

    private int queueCapacity;
}
