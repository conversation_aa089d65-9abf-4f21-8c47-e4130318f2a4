package com.moego.server.grooming.service.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CompanyServiceUpdateDto extends ServiceFilterDTO {

    @NotNull
    private Integer serviceId;

    private Integer categoryId;

    @Length(max = 150)
    private String name;

    private String description;

    @NotNull
    private Integer taxId;

    @NotNull
    private BigDecimal price;

    @NotNull
    private Integer duration;

    private Byte inactive; // 新增inactive字段，0-正常，1-inactive
    private String colorCode;

    @NotNull
    private Byte type;

    private Byte showBasePrice;
    private Byte bookOnlineAvailable;
    private Byte isAllStaff;

    private Boolean applyUpcomingAppt;
}
