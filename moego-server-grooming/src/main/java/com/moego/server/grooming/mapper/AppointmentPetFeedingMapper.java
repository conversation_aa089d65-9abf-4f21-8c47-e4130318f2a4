package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.AppointmentPetFeeding;
import com.moego.server.grooming.mapperbean.AppointmentPetFeedingExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AppointmentPetFeedingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    long countByExample(AppointmentPetFeedingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    int deleteByExample(AppointmentPetFeedingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    int insert(AppointmentPetFeeding record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    int insertSelective(AppointmentPetFeeding record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    List<AppointmentPetFeeding> selectByExample(AppointmentPetFeedingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    AppointmentPetFeeding selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") AppointmentPetFeeding record, @Param("example") AppointmentPetFeedingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") AppointmentPetFeeding record, @Param("example") AppointmentPetFeedingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(AppointmentPetFeeding record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_feeding
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(AppointmentPetFeeding record);
}
