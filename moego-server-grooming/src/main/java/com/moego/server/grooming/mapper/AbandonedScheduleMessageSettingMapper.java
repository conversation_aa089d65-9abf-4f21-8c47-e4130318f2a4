package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.AbandonedScheduleMessageSetting;
import com.moego.server.grooming.mapperbean.AbandonedScheduleMessageSettingExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AbandonedScheduleMessageSettingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    long countByExample(AbandonedScheduleMessageSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    int deleteByExample(AbandonedScheduleMessageSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    int insert(AbandonedScheduleMessageSetting record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    int insertSelective(AbandonedScheduleMessageSetting record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    List<AbandonedScheduleMessageSetting> selectByExample(AbandonedScheduleMessageSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    AbandonedScheduleMessageSetting selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") AbandonedScheduleMessageSetting record,
            @Param("example") AbandonedScheduleMessageSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") AbandonedScheduleMessageSetting record,
            @Param("example") AbandonedScheduleMessageSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(AbandonedScheduleMessageSetting record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abandoned_schedule_message_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(AbandonedScheduleMessageSetting record);
}
