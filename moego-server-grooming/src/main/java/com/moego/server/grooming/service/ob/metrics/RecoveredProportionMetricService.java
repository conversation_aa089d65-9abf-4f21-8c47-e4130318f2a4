package com.moego.server.grooming.service.ob.metrics;

import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/6/5
 */
@Service
@RequiredArgsConstructor
public class RecoveredProportionMetricService implements IOBMetricsService {

    private final RecoveredRecordsMetricService recoveredRecordsMetricService;

    @Override
    public Object sumMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return recoveredRecordsMetricService.proportionMetrics(timeRangeDTO);
    }

    @Override
    public String proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return "";
    }

    @Override
    public OBMetricsEnum getMetricsName() {
        return OBMetricsEnum.recovered_proportion;
    }
}
