package com.moego.server.grooming.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.server.grooming.dto.appointment.comment.TicketCommentsDTO;
import com.moego.server.grooming.service.TicketCommentService;
import jakarta.validation.constraints.Min;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(path = "/grooming/appointment/ticketComment")
@RequiredArgsConstructor
@Validated
public class TicketCommentController {
    private final TicketCommentService ticketCommentService;
    private final MigrateHelper migrateHelper;

    @Deprecated
    @GetMapping("")
    @Auth(AuthType.BUSINESS)
    public TicketCommentsDTO getTicketComment(
            AuthContext context, @RequestParam Integer appointmentId, @RequestParam(required = false) Long petId) {
        if (Objects.isNull(petId)) petId = 0L;
        return ticketCommentService.getTicketComment(context.getBusinessId(), appointmentId, petId);
    }

    @GetMapping("/v2")
    @Auth(AuthType.BUSINESS)
    public TicketCommentsDTO getTicketCommentV2(
            AuthContext context,
            @RequestParam(required = false) Integer appointmentId,
            @RequestParam @Min(1) Integer customerId) {
        if (Objects.isNull(appointmentId)) appointmentId = 0;
        Long companyId = null;
        if (migrateHelper.isMigrate(context)) {
            companyId = context.companyId();
        }
        return ticketCommentService.getTicketCommentV2(context.getBusinessId(), customerId, appointmentId, companyId);
    }
}
