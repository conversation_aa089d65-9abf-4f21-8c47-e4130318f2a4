package com.moego.server.grooming.service.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public record DrivingDisplayInfo(
        Integer groomingId,
        Integer drivingMinutes,
        Double drivingMiles,
        Byte unitOfDistanceType,
        Integer intervalTime,
        @Schema(description = "最后一个预约到 endLocation 的时间") Integer drivingOutMinutes,
        @Schema(description = "最后一个预约到 endLocation 的距离") Double drivingOutMiles) {}
