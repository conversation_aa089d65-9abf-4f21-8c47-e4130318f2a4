package com.moego.server.grooming.service.dto;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ServiceSaveDto extends ServiceFilterDTO {

    @NotNull
    private Integer categoryId;

    @NotNull
    @Length(max = 150)
    private String name;

    private String description;

    @NotNull
    private Integer taxId;

    @NotNull
    private BigDecimal price;

    @NotNull
    private Byte type;

    @NotNull
    private Integer duration;

    @NotNull
    private String colorCode;

    private Byte inactive; // 新增inactive字段，0-正常，1-inactive
}
