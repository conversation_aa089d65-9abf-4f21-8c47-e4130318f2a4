package com.moego.server.grooming.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.service.BookOnlinePetLimitService;
import com.moego.server.grooming.web.vo.BookOnlinePetLimitRequest;
import com.moego.server.grooming.web.vo.BookOnlinePetLimitResponse;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(path = "/grooming/bookOnline/pet")
@RequiredArgsConstructor
public class BookOnlinePetLimitController {

    private final BookOnlinePetLimitService bookOnlinePetLimitService;

    @GetMapping("/limit")
    @Auth(AuthType.BUSINESS)
    public BookOnlinePetLimitResponse getPetLimit(AuthContext context, @RequestParam List<Long> limitIdList) {
        BookOnlinePetLimitResponse response = new BookOnlinePetLimitResponse();
        response.setPetLimitDTOList(
                bookOnlinePetLimitService.getPetLimit(context.companyId(), context.getBusinessId(), limitIdList));
        return response;
    }

    @PostMapping("/limit")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.CREATE,
            resourceType = ResourceType.ONLINE_BOOKING_PET_LIMIT,
            resourceId = "#result",
            details = "#request")
    public Long savePetLimit(AuthContext context, @RequestBody BookOnlinePetLimitRequest request) {
        return bookOnlinePetLimitService.savePetLimit(context.getBusinessId(), context.companyId(), request);
    }
}
