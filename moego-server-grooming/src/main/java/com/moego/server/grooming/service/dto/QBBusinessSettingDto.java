package com.moego.server.grooming.service.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.moego.server.grooming.mapperbean.MoeQbConnect;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "qb business setting 信息")
public class QBBusinessSettingDto {

    private Integer businessId;

    @Schema(description = "连接id")
    private Integer connectId;

    @JsonIgnore
    private MoeQbConnect qbConnect;

    @Schema(description = "设置id")
    private Integer setttingId;

    @Schema(description = "连接状态 0 未连接  1正常  2用户取消  3授权异常")
    private Byte connectStatus;

    @Schema(description = "是否开启同步  1 开启  0 关闭(默认)")
    private Byte enableSync;

    @Schema(description = "预约开始同步日期(默认创建时间) 年-月-日")
    private String syncBeginDate;

    @Schema(description = "当前连接已同步过的内容，最小同步日期")
    private String minSyncDate;

    @Schema(description = "当前qb连接的company name")
    private String connectCompanyName;

    @Schema(description = "当前qb连接的登录email")
    private String connectEmail;

    @Schema(description = "账本名")
    @Deprecated
    private String accountName;

    @Schema(description = "账本id")
    @Deprecated
    private String accountId;

    @Schema(description = "上次qb connect连接的email")
    @Deprecated
    private String lastConnectEmail;

    @Schema(description = "授权跳转url")
    @Deprecated
    private String oAuthUrl;

    @Schema(description = "qb sync 版本, 决定了走新旧同步逻辑")
    private Integer userVersion;

    private String lastDisconnectedTime;
}
