package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.ServiceAreaPicCache;
import com.moego.server.grooming.mapperbean.ServiceAreaPicCacheExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ServiceAreaPicCacheMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_area_pic_cache
     *
     * @mbg.generated
     */
    long countByExample(ServiceAreaPicCacheExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_area_pic_cache
     *
     * @mbg.generated
     */
    int deleteByExample(ServiceAreaPicCacheExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_area_pic_cache
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_area_pic_cache
     *
     * @mbg.generated
     */
    int insert(ServiceAreaPicCache record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_area_pic_cache
     *
     * @mbg.generated
     */
    int insertSelective(ServiceAreaPicCache record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_area_pic_cache
     *
     * @mbg.generated
     */
    List<ServiceAreaPicCache> selectByExample(ServiceAreaPicCacheExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_area_pic_cache
     *
     * @mbg.generated
     */
    ServiceAreaPicCache selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_area_pic_cache
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") ServiceAreaPicCache record, @Param("example") ServiceAreaPicCacheExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_area_pic_cache
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") ServiceAreaPicCache record, @Param("example") ServiceAreaPicCacheExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_area_pic_cache
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(ServiceAreaPicCache record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_area_pic_cache
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(ServiceAreaPicCache record);
}
