package com.moego.server.grooming.web.params;

import com.moego.server.grooming.params.BookOnlineAgreementParams;
import com.moego.server.grooming.params.BookOnlineCustomerAdditionalParams;
import com.moego.server.grooming.params.BookOnlineCustomerParams;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.OBRequestSourceType;
import com.moego.server.grooming.params.ob.DiscountCodeParams;
import com.moego.server.grooming.params.ob.MembershipParams;
import com.moego.server.grooming.params.ob.PreAuthDetailParams;
import com.moego.server.grooming.params.ob.PrepayDetailParams;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/12
 */
@Data
public class OBClientInfoParams {

    // todo: remove businessId
    @Deprecated
    private Integer businessId;

    // @NotBlank todo: open this after removing businessId
    @Deprecated
    private String businessName;

    /**
     * 对于嵌套的字段，需要添加@Valid注解使内部的Validation注解生效
     * `@Valid` 必须紧挨着类型名
     */
    @NotNull
    @Valid
    private BookOnlineCustomerParams customerData;

    @NotNull
    @Valid
    private List<@NotNull @Valid BookOnlinePetParams> petData;

    /**
     * 快速提交  simplify submit
     * 没有appointment date，没有appointment start time
     */
    @Pattern(message = "Invalid date format, valid example: 2020-02-08", regexp = "^(\\d{4}-\\d{2}-\\d{2})?$")
    @Schema(description = "日期格式： 2020-02-08")
    private String appointmentDate;

    @Max(1440)
    private Integer appointmentStartTime;

    /**
     * for mobile grooming
     */
    private Byte outOfArea;

    private Integer staffId;

    private String orderId;

    private Byte isAgreePolicy;

    @NotNull
    List<BookOnlineAgreementParams> agreements;

    private String note;

    private BookOnlineCustomerAdditionalParams bookOnlineCustomerAdditionalParams;

    @Schema(description = "prepay guid，用于找到支付记录，绑定到新创建的订单，无支付时不需传")
    private String prepayGuid;

    @Schema(description = "Prepay detail")
    private PrepayDetailParams prepayDetail;

    private OBRequestSourceType sourceType;

    @Schema(description = "PreAuth detail")
    private PreAuthDetailParams preAuthDetail;

    @Schema(description = "Discount code")
    private DiscountCodeParams discountCodeParams;

    @Schema(description = "Membership apply params")
    private MembershipParams membershipParams;
}
