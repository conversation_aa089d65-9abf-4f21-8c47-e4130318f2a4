package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.AutoAssign;
import com.moego.server.grooming.mapperbean.AutoAssignExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AutoAssignMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table auto_assign
     *
     * @mbg.generated
     */
    long countByExample(AutoAssignExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table auto_assign
     *
     * @mbg.generated
     */
    int deleteByExample(AutoAssignExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table auto_assign
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table auto_assign
     *
     * @mbg.generated
     */
    int insert(AutoAssign record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table auto_assign
     *
     * @mbg.generated
     */
    int insertSelective(AutoAssign record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table auto_assign
     *
     * @mbg.generated
     */
    List<AutoAssign> selectByExample(AutoAssignExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table auto_assign
     *
     * @mbg.generated
     */
    AutoAssign selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table auto_assign
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") AutoAssign record, @Param("example") AutoAssignExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table auto_assign
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") AutoAssign record, @Param("example") AutoAssignExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table auto_assign
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(AutoAssign record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table auto_assign
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(AutoAssign record);
}
