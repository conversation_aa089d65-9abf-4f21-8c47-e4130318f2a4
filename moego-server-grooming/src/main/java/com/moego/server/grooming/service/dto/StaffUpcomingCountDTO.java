package com.moego.server.grooming.service.dto;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class StaffUpcomingCountDTO {

    private Integer staffId;
    private List<StaffLocationUpcomingCount> locationUpcomingCountList;

    @Data
    public static class StaffLocationUpcomingCount {
        private Integer locationId;
        private Integer upcomingCount;
    }
}
