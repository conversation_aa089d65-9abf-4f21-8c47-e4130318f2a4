package com.moego.server.grooming.service.ob.component;

import static com.moego.common.enums.groomingreport.GroomingReportConst.PRESET_TAGS;
import static com.moego.server.grooming.service.ob.OBLandingPageConfigService.getFilteredContent;
import static com.moego.server.grooming.service.ob.OBLandingPageConfigService.getShowcase;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

import com.moego.common.enums.PetTypeEnum;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.api.IPetService;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerPetPetCodeDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.params.CustomerIdListParams;
import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.mapper.MoeGroomingReportMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.mapperbean.MoeGroomingReport;
import com.moego.server.grooming.mapperbean.MoeGroomingReportExample;
import com.moego.server.grooming.mapperbean.ObConfigClientReview;
import com.moego.server.grooming.service.OBConfigClientReviewService;
import com.moego.server.grooming.web.vo.ob.component.BaseComponentVO;
import com.moego.server.message.api.IReviewRecordService;
import com.moego.server.message.dto.ReviewBoosterRecordDTO;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Service(value = LandingPageComponentEnum.COMPONENT_OB_CONFIG_CLIENT_REVIEWS)
@RequiredArgsConstructor
public class ClientReviewsComponentService implements ILandingPageComponentService {

    private final IReviewRecordService reviewRecordApi;
    private final ICustomerCustomerService customerApi;
    private final OBConfigClientReviewService obConfigClientReviewService;
    private final IPetService petApi;
    private final MoeGroomingReportMapper groomingReportMapper;

    @Override
    public LandingPageClientReviewListVO getPageComponent(MoeBookOnlineLandingPageConfig landingPageConfig) {
        Integer businessId = landingPageConfig.getBusinessId();

        List<ObConfigClientReview> reviews = obConfigClientReviewService.list(businessId);

        return new LandingPageClientReviewListVO()
                .setReviews(toVOs(reviews, landingPageConfig))
                .setIsDisplayShowcasePhoto(landingPageConfig.getIsDisplayClientReviewShowcasePhoto());
    }

    private List<LandingPageClientReviewVO> toVOs(
            List<ObConfigClientReview> clientReviews, MoeBookOnlineLandingPageConfig landingPageConfig) {
        List<Integer> reviewIds = clientReviews.stream()
                .map(ObConfigClientReview::getReviewId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        Map<Integer, ReviewBoosterRecordDTO> reviewIdToReview =
                reviewRecordApi.listByIds(reviewIds).stream().collect(toMap(ReviewBoosterRecordDTO::getId, identity()));

        List<Integer> customerIds = clientReviews.stream()
                .map(ObConfigClientReview::getCustomerId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        Map<Integer, MoeBusinessCustomerDTO> customerIdToCustomer =
                customerApi.queryCustomerListWithDeleted(new CustomerIdListParams(customerIds)).stream()
                        .collect(toMap(MoeBusinessCustomerDTO::getId, identity()));

        List<Integer> petIds = clientReviews.stream()
                .map(ObConfigClientReview::getPetId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        Map<Integer, CustomerPetPetCodeDTO> petIdToPet = petApi.getCustomerPetInfoListByIdList(petIds).stream()
                .collect(toMap(CustomerPetDetailDTO::getPetId, identity()));

        List<Integer> appointmentIds = reviewIdToReview.values().stream()
                .map(ReviewBoosterRecordDTO::getAppointmentId)
                .filter(Objects::nonNull)
                .filter(id -> id > 0)
                .distinct()
                .toList();
        List<MoeGroomingReport> groomingReports = getGroomingReports(landingPageConfig.getBusinessId(), appointmentIds);

        return clientReviews.stream()
                .map(review -> toClientReviewVO(
                        review, landingPageConfig, reviewIdToReview, customerIdToCustomer, petIdToPet, groomingReports))
                .toList();
    }

    private List<MoeGroomingReport> getGroomingReports(Integer businessId, List<Integer> appointmentIds) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return List.of();
        }
        MoeGroomingReportExample example = new MoeGroomingReportExample();
        example.createCriteria().andBusinessIdEqualTo(businessId).andGroomingIdIn(appointmentIds);
        return groomingReportMapper.selectByExample(example);
    }

    private static LandingPageClientReviewVO toClientReviewVO(
            ObConfigClientReview clientReviews,
            MoeBookOnlineLandingPageConfig landingPageConfig,
            Map<Integer, ReviewBoosterRecordDTO> reviewIdToReview,
            Map<Integer, MoeBusinessCustomerDTO> customerIdToCustomer,
            Map<Integer, CustomerPetPetCodeDTO> petIdToPet,
            List<MoeGroomingReport> groomingReports) {
        return new LandingPageClientReviewVO()
                .setReviewId(clientReviews.getReviewId())
                .setScore(Optional.ofNullable(clientReviews.getReviewId())
                        .map(reviewIdToReview::get)
                        .map(ReviewBoosterRecordDTO::getPositiveScore)
                        .orElse(null))
                .setReviewContent(Optional.ofNullable(clientReviews.getReviewId())
                        .map(reviewIdToReview::get)
                        .map(ReviewBoosterRecordDTO::getReviewContent)
                        .map(content -> getFilteredContent(content, PRESET_TAGS))
                        .orElse(""))
                .setCustomerName(Optional.ofNullable(clientReviews.getCustomerId())
                        .map(customerIdToCustomer::get)
                        .map(MoeBusinessCustomerDTO::getFirstName)
                        .orElse(""))
                .setShowcasePhotoUrl(
                        Boolean.TRUE.equals(landingPageConfig.getIsDisplayClientReviewShowcasePhoto())
                                ? getShowcase(
                                        groomingReports,
                                        Optional.ofNullable(reviewIdToReview.get(clientReviews.getReviewId()))
                                                .map(ReviewBoosterRecordDTO::getAppointmentId)
                                                .orElse(null),
                                        clientReviews.getPetId())
                                : "")
                .setPetTypeId(Optional.ofNullable(clientReviews.getPetId())
                        .map(petIdToPet::get)
                        .map(CustomerPetPetCodeDTO::getPetTypeId)
                        .orElse(PetTypeEnum.DOG.getType()));
    }

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper = true)
    public static class LandingPageClientReviewListVO extends BaseComponentVO {

        private List<LandingPageClientReviewVO> reviews;
        private Boolean isDisplayShowcasePhoto;

        @Override
        public String getComponent() {
            return LandingPageComponentEnum.OB_CONFIG_CLIENT_REVIEWS.getComponent();
        }
    }

    /**
     * @see <a href="https://www.figma.com/file/qGCiknaG6wZh7XrzOuzwsq/OB---master-file?type=design&node-id=6289-10941&mode=design&t=q1nLeUx4HBhDR6K1-0">Figma</a>
     */
    @Data
    @Accessors(chain = true)
    public static class LandingPageClientReviewVO {
        private Integer reviewId;
        private String reviewContent;
        private Integer score;
        private String customerName;
        /**
         * 前端会根据 pet type 展示不同的 icon
         */
        private Integer petTypeId;

        private String showcasePhotoUrl;
    }
}
