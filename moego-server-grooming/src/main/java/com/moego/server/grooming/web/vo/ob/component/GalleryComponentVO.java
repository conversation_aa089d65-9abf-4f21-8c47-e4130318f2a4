package com.moego.server.grooming.web.vo.ob.component;

import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Storefront gallery component
 *
 * <AUTHOR>
 * @since 2023/2/21
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class GalleryComponentVO extends BaseComponentVO {

    @Schema(description = "Landing page gallery list")
    private List<OBLandingPageConfigVO.OBLandingPageGalleryVO> galleryList;

    @Override
    public String getComponent() {
        return LandingPageComponentEnum.GALLERY.getComponent();
    }
}
