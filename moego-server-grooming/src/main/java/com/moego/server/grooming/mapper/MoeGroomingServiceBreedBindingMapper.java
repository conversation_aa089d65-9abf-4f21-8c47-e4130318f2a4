package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingServiceBreedBinding;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingServiceBreedBindingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_breed_binding
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_breed_binding
     *
     * @mbg.generated
     */
    int insert(MoeGroomingServiceBreedBinding record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_breed_binding
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingServiceBreedBinding record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_breed_binding
     *
     * @mbg.generated
     */
    MoeGroomingServiceBreedBinding selectByPrimary<PERSON>ey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_breed_binding
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingServiceBreedBinding record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_breed_binding
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeGroomingServiceBreedBinding record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_breed_binding
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingServiceBreedBinding record);

    List<MoeGroomingServiceBreedBinding> selectByBusinessIdAndServiceId(
            @Param("businessId") Integer businessId, @Param("serviceId") Integer serviceId);

    List<MoeGroomingServiceBreedBinding> selectByCompanyIdAndServiceId(
            @Param("companyId") Long companyId, @Param("serviceId") Integer serviceId);

    List<MoeGroomingServiceBreedBinding> selectByCidBidsAndServiceId(
            @Param("companyId") Long companyId,
            @Param("businessIds") List<Integer> businessIds,
            @Param("serviceId") Integer serviceId);

    int batchInsertRecords(List<MoeGroomingServiceBreedBinding> records);

    int batchInsertRecordsWithCid(List<MoeGroomingServiceBreedBinding> records);

    List<MoeGroomingServiceBreedBinding> selectByBusinessIdAndBreedName(
            @Param("businessId") Integer businessId, @Param("breedName") String breedName);

    int batchUpdateRecords(@Param("list") List<MoeGroomingServiceBreedBinding> updateList);
}
