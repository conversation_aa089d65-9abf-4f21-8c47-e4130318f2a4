package com.moego.server.grooming.server;

import com.moego.server.grooming.api.ITimeSlotsServiceBase;
import com.moego.server.grooming.dto.OBAvailableTimeWithNonAvailableDTO;
import com.moego.server.grooming.dto.ob.OBTimeSlotQueryDTO;
import com.moego.server.grooming.service.ob.OBClientTimeSlotService;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class TimeSlotsServer extends ITimeSlotsServiceBase {

    private final OBClientTimeSlotService obClientTimeSlotService;

    @Override
    public Map<String, OBAvailableTimeWithNonAvailableDTO> getAvailableTimeBySlot(
            OBTimeSlotQueryDTO obTimeSlotQueryDTO) {
        return obClientTimeSlotService.getAvailableTimeBySlotCapacityForBusiness(
                obTimeSlotQueryDTO.getTimeSlotDTO(),
                obTimeSlotQueryDTO.getNeedQueryDays(),
                obTimeSlotQueryDTO.getAvailableStaffIds());
    }
}
