package com.moego.server.grooming.server;

import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.grooming.api.IGoogleCalendarServiceBase;
import com.moego.server.grooming.params.SyncAppointmentParams;
import com.moego.server.grooming.service.CalendarSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class GoogleCalendarServer extends IGoogleCalendarServiceBase {

    @Autowired
    private CalendarSyncService calendarSyncService;

    /**
     * 从google calendar导入预约
     */
    @Override
    public void taskBeginImport() {
        ThreadPool.execute(() -> calendarSyncService.taskBeginImport());
    }

    /**
     * 导出 google calendar 预约
     */
    @Override
    public void taskBeginExport() {
        ThreadPool.execute(() -> calendarSyncService.taskBeginExport());
    }

    @Override
    public Boolean testSyncOneGrooming(@RequestParam("groomingId") Integer groomingId) {
        return calendarSyncService.testSyncOneAppointment(groomingId);
    }

    @Override
    public void testBatchSyncEvent(@RequestParam("gcCalendarId") Integer gcCalendarId) {
        calendarSyncService.testBatchSyncEvent(gcCalendarId);
    }

    @Override
    public void deleteStaffCalendar(
            @RequestParam("businessId") Integer businessId, @RequestParam("staffId") Integer staffId) {
        calendarSyncService.deleteStaffCalendar(businessId, staffId);
    }

    @Override
    public Boolean checkBusinessHaveGoogleCalendarSync(SyncAppointmentParams params) {
        calendarSyncService.checkBusinessHaveGoogleCalendarSync(
                params.getBusinessId(), params.getAppointmentId(), params.getAppointmentDate(), params.getIsDelay());
        return Boolean.TRUE;
    }
}
