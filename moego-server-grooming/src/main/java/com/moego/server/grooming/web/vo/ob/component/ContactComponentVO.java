package com.moego.server.grooming.web.vo.ob.component;

import com.moego.server.grooming.enums.LandingPageComponentEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Contact component.
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ContactComponentVO extends BaseComponentVO {

    private String phone;

    @Override
    public String getComponent() {
        return LandingPageComponentEnum.CONTACT.getComponent();
    }
}
