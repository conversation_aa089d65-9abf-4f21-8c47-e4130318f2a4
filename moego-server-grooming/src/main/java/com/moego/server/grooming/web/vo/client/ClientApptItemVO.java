package com.moego.server.grooming.web.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/5
 */
@Data
@Accessors(chain = true)
public class ClientApptItemVO {

    @Schema(description = "Pet detail")
    private PetDetailVO petDetail;

    @Schema(description = "Pet detail")
    private ServiceDetailVO serviceDetail;

    @Schema(description = "Staff detail")
    private StaffDetailVO staffDetail;
}
