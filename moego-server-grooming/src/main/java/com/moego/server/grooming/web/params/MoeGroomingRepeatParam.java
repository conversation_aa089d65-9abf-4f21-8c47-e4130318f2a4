package com.moego.server.grooming.web.params;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.Date;
import lombok.Data;

@Data
public class MoeGroomingRepeatParam {

    @NotNull
    private Integer id;

    private Integer staffId;
    private Byte repeatType;
    private Integer repeatEvery;

    @Size(max = 50)
    private String repeatBy;

    private Date startsOn;
    private Integer times;
    private Long createTime;
    private Long updateTime;
    private Byte status;
    private String endOn;
    private Byte isNotice;
    private Date setEndOn;
    private Byte repeatEveryType;
    private Byte monthDay;
    private Byte monthWeekTimes;
    private Byte monthWeekDay;
    private String type;
    private Byte ssFlag;
    private Integer ssBeforeDays;
    private Integer ssAfterDays;
}
