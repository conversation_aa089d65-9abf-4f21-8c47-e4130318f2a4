package com.moego.server.grooming.config;

import com.moego.common.utils.DateUtil;
import com.moego.server.grooming.mapper.MoeGroomingAppointmentMapper;
import com.moego.server.grooming.mapper.MoeGroomingPetDetailMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import java.lang.reflect.Method;
import java.util.Objects;
import javax.annotation.Nonnull;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.springframework.aop.ClassFilter;
import org.springframework.aop.MethodMatcher;
import org.springframework.aop.Pointcut;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.aop.support.StaticMethodMatcher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.Nullable;

@Configuration(proxyBeanMethods = false)
public class SetUpdateTimeCfg {

    @Bean
    static DefaultPointcutAdvisor setUpdateTimeAdvisor() {
        var ad = new DefaultPointcutAdvisor();
        ad.setPointcut(new Pointcut() {
            @Nonnull
            @Override
            public ClassFilter getClassFilter() {
                return clazz -> MoeGroomingAppointmentMapper.class.isAssignableFrom(clazz)
                        || MoeGroomingPetDetailMapper.class.isAssignableFrom(clazz)
                        || MoeGroomingServiceMapper.class.isAssignableFrom(clazz);
            }

            @Nonnull
            @Override
            public MethodMatcher getMethodMatcher() {
                return new StaticMethodMatcher() {
                    @Override
                    public boolean matches(@Nonnull Method method, @Nonnull Class<?> targetClass) {
                        return "updateByPrimaryKeySelective".equals(method.getName());
                    }
                };
            }
        });
        ad.setAdvice(new MethodInterceptor() {
            @Nullable
            @Override
            public Object invoke(@Nonnull MethodInvocation invocation) throws Throwable {
                var arg = invocation.getArguments()[0];

                if (arg instanceof MoeGroomingAppointment) {
                    if (Objects.isNull(((MoeGroomingAppointment) arg).getUpdateTime())) {
                        ((MoeGroomingAppointment) arg).setUpdateTime(DateUtil.get10Timestamp());
                    }
                } else if (arg instanceof MoeGroomingPetDetail) {
                    if (Objects.isNull(((MoeGroomingPetDetail) arg).getUpdateTime())) {
                        ((MoeGroomingPetDetail) arg).setUpdateTime(DateUtil.get10Timestamp());
                    }
                } else if (arg instanceof MoeGroomingService) {
                    if (Objects.isNull(((MoeGroomingService) arg).getUpdateTime())) {
                        ((MoeGroomingService) arg).setUpdateTime(DateUtil.get10Timestamp());
                    }
                }

                return invocation.proceed();
            }
        });
        return ad;
    }
}
