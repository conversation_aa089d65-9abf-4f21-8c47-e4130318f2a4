package com.moego.server.grooming.server;

import com.moego.common.constant.CommonConstant;
import com.moego.common.dto.CommonResultDto;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.response.ResponseResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.grooming.api.IGroomingOnlineBookingServiceBase;
import com.moego.server.grooming.dto.BookOnlineAutoMoveAppointmentDTO;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.BookOnlineQuestionSaveDto;
import com.moego.server.grooming.dto.BookOnlineRequestCountDto;
import com.moego.server.grooming.dto.BookOnlineStaffAvailabilityDTO;
import com.moego.server.grooming.dto.CompanyBusinessIdDTO;
import com.moego.server.grooming.dto.MoeBookOnlineNotificationDto;
import com.moego.server.grooming.dto.ob.BookOnlineConfigDTO;
import com.moego.server.grooming.dto.ob.BookOnlineGalleryDTO;
import com.moego.server.grooming.dto.ob.BookOnlinePaymentGroupSettingDTO;
import com.moego.server.grooming.dto.ob.BookOnlineProfileDTO;
import com.moego.server.grooming.dto.ob.GroomingOnlyBookingRequestDTO;
import com.moego.server.grooming.dto.ob.MoeZipCodeDTO;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.dto.ob.ServiceAreaResultDTO;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapper.MoeZipcodeMapper;
import com.moego.server.grooming.mapper.po.BusinessCompanyPO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAvailableStaff;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnlineExample;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointmentExample;
import com.moego.server.grooming.mapperbean.MoeZipcode;
import com.moego.server.grooming.mapstruct.OBSettingMapper;
import com.moego.server.grooming.mapstruct.ZipCodeMapper;
import com.moego.server.grooming.params.BatchUpdateOBSettingParam;
import com.moego.server.grooming.params.CustomerIdWithPetIdsParams;
import com.moego.server.grooming.params.MoeBookOnlineStaffTimeParams;
import com.moego.server.grooming.params.MoeBusinessBookOnlineDto;
import com.moego.server.grooming.params.UpdateStaffTimeParam;
import com.moego.server.grooming.params.ob.BookingRequestEventParams;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.grooming.params.ob.ServiceAreaParams;
import com.moego.server.grooming.service.ActiveMQService;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.ob.OBBusinessService;
import com.moego.server.grooming.service.ob.OBBusinessStaffService;
import com.moego.server.grooming.service.ob.OBGroomingService;
import com.moego.server.grooming.service.ob.OBLandingPageConfigService;
import com.moego.server.grooming.service.ob.OBRequestService;
import com.moego.server.grooming.web.dto.ob.SettingInfoDto;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
public class GroomingOnlineBookingServer extends IGroomingOnlineBookingServiceBase {

    private final MoeGroomingAppointmentService appointmentService;
    private final MoeGroomingBookOnlineService bookOnlineService;
    private final OBBusinessService businessService;
    private final OBBusinessStaffService businessStaffService;
    private final OBLandingPageConfigService landingPageConfigService;
    private final OBGroomingService obGroomingService;
    private final MoeBusinessBookOnlineMapper bookOnlineMapper;
    private final OBRequestService obRequestService;
    private final AppointmentMapperProxy appointmentMapper;
    private final ActiveMQService activeMQService;
    private final MoeZipcodeMapper moeZipcodeMapper;

    @Override
    public Integer getBusinessId(@RequestParam("obName") String obName) {
        return bookOnlineService.getBusinessIdByBookOnlineName(obName);
    }

    @Override
    public CompanyBusinessIdDTO getCompanyBusinessId(@RequestParam("obName") String obName) {
        var po = bookOnlineService.getBusinessCompanyByBookOnlineName(obName);
        if (Objects.isNull(po) || Objects.isNull(po.getBusinessId()) || Objects.equals(po.getBusinessId(), 0)) {
            throw ExceptionUtil.bizException(Code.CODE_BOOK_ONLINE_NAME_INVALID);
        }
        return new CompanyBusinessIdDTO(po.getCompanyId(), po.getBusinessId());
    }

    @Override
    public String getObNameByBusinessId(@RequestParam("tokenBusinessId") Integer tokenBusinessId) {
        return bookOnlineService.getObNameByBusinessId(tokenBusinessId);
    }

    @Override
    public List<BookOnlineQuestionSaveDto> getQuestionSaveByCustomerInfo(
            @RequestBody CustomerIdWithPetIdsParams petIdsParams) {
        return bookOnlineService.getQuestionSaveByCustomerInfo(petIdsParams);
    }

    @Override
    public MoeBookOnlineNotificationDto getOnlineBookingSendTemplate(@RequestParam("businessId") Integer businessId) {
        return bookOnlineService.getOnlineBookingSendTemplate(businessId);
    }

    @Override
    public void updateBusinessBookOnlineSetting(@RequestParam("businessId") Integer businessId) {
        bookOnlineService.updateBusinessBookOnlineSetting(businessId);
    }

    @Override
    public Boolean checkAvailableDistForCustomerLogin(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("lat") String lat,
            @RequestParam("lng") String lng,
            @RequestParam("zipcode") String zipcode) {
        return bookOnlineService.checkAvailableDist(businessId, lat, lng, zipcode);
    }

    @Override
    public ResponseResult saveAvailableStaffTime(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam("tokenCompanyId") Long tokenCompanyId,
            @RequestBody MoeBookOnlineStaffTimeParams staffTimeParams) {
        staffTimeParams.setBusinessId(tokenBusinessId);
        staffTimeParams.setCompanyId(tokenCompanyId);
        bookOnlineService.initOneStaffTime(staffTimeParams);
        return ResponseResult.success(new CommonResultDto(true));
    }

    @Override
    public Boolean updateStaffAvailableTimeByWorkingTime(@RequestBody UpdateStaffTimeParam updateStaffTimeParam) {
        return bookOnlineService.updateStaffAvailableTimeByWorkingTime(updateStaffTimeParam);
    }

    @Override
    public MoeBusinessBookOnlineDto getBusinessBookOnlineSetting(@RequestParam("businessId") Integer businessId) {
        return bookOnlineService.getBusinessBookOnlineSetting(businessId);
    }

    @Override
    public ResponseResult getSettingInfo(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam("tokenCompanyId") Long tokenCompanyId) {
        SettingInfoDto result = new SettingInfoDto();
        MoeBusinessBookOnline obInfo = bookOnlineService.getSettingInfoByBusinessId(tokenBusinessId);
        result.setBookOnlineInfo(obInfo);
        result.setBookOnlineNotification(bookOnlineService.getNotificationByBusinessId(tokenBusinessId, false));
        return ResponseResult.success(result);
    }

    @Override
    public List<BookOnlineAutoMoveAppointmentDTO> queryGroomingAllOBAppointment() {
        return appointmentService.queryAllBookingAppointmentForAutoMove();
    }

    @Override
    public Boolean autoMoveWaitlist() {
        return obGroomingService.autoMoveWaitlist();
    }

    @Override
    public BookOnlineRequestCountDto getBookOnlineRequestCountForNotification(
            @RequestParam("businessId") Integer businessId) {
        return appointmentService.getBookOnlineRequestCountForNotification(businessId);
    }

    @Override
    public void updateStaffOBAvailability(
            @RequestParam("businessId") Integer businessId, @RequestBody BookOnlineStaffAvailabilityDTO params) {
        bookOnlineService.modifyStaffOBAvailabilityInternal(businessId, params);
    }

    @Override
    public List<ServiceAreaResultDTO> getServiceAreaResultList(@RequestBody ServiceAreaParams serviceAreaParams) {
        return businessService.getServiceAreaResultList(serviceAreaParams);
    }

    @Override
    public List<BookOnlineProfileDTO> listBookOnlineProfile(List<Integer> businessIdList) {
        return bookOnlineService.listBookOnlineProfile(businessIdList);
    }

    @Override
    public List<BookOnlineGalleryDTO> listGalleryMaxSortImageByBusinessId(List<Integer> businessIdList) {
        return bookOnlineService.listGalleryMaxSortImageByBusinessId(businessIdList);
    }

    @Override
    public List<BookOnlineConfigDTO> listBookOnlineConfig(List<Integer> businessIdList) {
        return bookOnlineService.listBookOnlineConfig(businessIdList);
    }

    @Override
    @Deprecated // use initializeAvailableStaffV2 instead
    public boolean initializeAvailableStaff(List<MoeStaffDto> staffDtoList) {

        List<MoeBookOnlineAvailableStaff> initStaffList =
                businessStaffService.initStaffAvailability(null, staffDtoList);
        return Objects.equals(initStaffList.size(), staffDtoList.size());
    }

    // AS 之后，一个 Staff 会在多个 business 下初始化 OB 配置，因此需要指定 businessId
    @Override
    public boolean initializeAvailableStaffV2(
            @RequestParam("businessId") Integer businessId, @RequestBody List<MoeStaffDto> staffDtoList) {
        businessStaffService.initStaffAvailability(businessId, staffDtoList);
        return true;
    }

    @Override
    public Integer getBusinessIdByOBNameOrDomain(OBAnonymousParams anonymousParams) {
        BusinessCompanyPO businessCompanyPO =
                landingPageConfigService.getBusinessIdAndCompanyIdByAnonymous(anonymousParams);
        if (Objects.isNull(businessCompanyPO)) return null;
        return businessCompanyPO.getBusinessId();
    }

    @Override
    public OBBusinessDTO getBusinessDTOByOBNameOrDomain(OBAnonymousParams anonymousParams) {
        BusinessCompanyPO businessCompanyPO =
                landingPageConfigService.getBusinessIdAndCompanyIdByAnonymous(anonymousParams);
        if (Objects.isNull(businessCompanyPO)) return null;
        var res = new OBBusinessDTO();
        res.setBusinessId(businessCompanyPO.getBusinessId());
        res.setCompanyId(businessCompanyPO.getCompanyId());
        return res;
    }

    @Override
    public OBBusinessDTO mustGetBusinessDTOByOBNameOrDomain(OBAnonymousParams anonymousParams) {
        return Optional.ofNullable(this.getBusinessDTOByOBNameOrDomain(anonymousParams))
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_BOOK_ONLINE_SITE_NOT_FOUND));
    }

    @Override
    public List<BookOnlineDTO> listOBSetting(List<Integer> businessIds) {
        if (ObjectUtils.isEmpty(businessIds)) {
            return List.of();
        }
        MoeBusinessBookOnlineExample example = new MoeBusinessBookOnlineExample();
        example.createCriteria().andBusinessIdIn(businessIds);
        return bookOnlineMapper.selectByExample(example).stream()
                .map(OBSettingMapper.INSTANCE::entity2DTO)
                .toList();
    }

    @Override
    public BookOnlineDTO getOBSetting(Integer businessId) {
        MoeBusinessBookOnline bookOnline = bookOnlineMapper.selectByBusinessId(businessId);
        return OBSettingMapper.INSTANCE.entity2DTO(bookOnline);
    }

    @Override
    public int batchUpdateOBSetting(BatchUpdateOBSettingParam param) {
        if (ObjectUtils.isEmpty(param.getBusinessIds())) {
            return 0;
        }
        MoeBusinessBookOnlineExample example = new MoeBusinessBookOnlineExample();
        example.createCriteria().andBusinessIdIn(param.getBusinessIds());
        MoeBusinessBookOnline bookOnline = OBSettingMapper.INSTANCE.dto2Entity(param.getUpdateValue());
        log.info(
                "syncStatusChanged in batchUpdateOBSetting, businessId:{}, syncStatus:{}",
                param.getBusinessIds(),
                bookOnline.getAvailableTimeSync());
        return bookOnlineMapper.updateByExampleSelective(bookOnline, example);
    }

    @Override
    public void deleteOBGroupPaymentSetting(Integer businessId) {
        bookOnlineMapper.deletePaymentGroupSetting(businessId);
    }

    @Override
    public void deleteServiceArea(@RequestParam Integer businessId, @RequestParam Integer serviceAreaId) {
        bookOnlineService.deleteServiceArea(businessId, serviceAreaId);
    }

    @Override
    public BookOnlinePaymentGroupSettingDTO getOBClientPaymentSetting(Integer businessId, Integer customerId) {
        MoeBusinessBookOnline info = bookOnlineService.getSettingInfoByBusinessId(businessId);
        try {
            return bookOnlineService.checkGroupPaymentTypeForClient(info, customerId);
        } catch (Exception e) {
            log.error("getOBClientPaymentSetting error bizId:{},customerId:{}", businessId, customerId, e);
            return null;
        }
    }

    @Override
    public GroomingOnlyBookingRequestDTO getBookingRequest(Integer appointmentId) {
        return obRequestService.getBookingRequest(appointmentId);
    }

    @Override
    public Boolean sendBookingRequestValidateEvent(
            @RequestParam(required = false, name = "businessId") Integer businessId,
            @RequestParam(required = false, name = "startDate") String startDate) {
        if (businessId != null) {
            sendBookingRequestValidateEventByBusiness(businessId, startDate);
            return true;
        }

        List<BookOnlineConfigDTO> configs = bookOnlineMapper.listAllBookOnlineName();
        configs.forEach(config -> {
            if (Objects.equals(config.getIsEnable(), CommonConstant.DISABLE)) {
                return;
            }
            sendBookingRequestValidateEventByBusiness(config.getBusinessId(), startDate);
        });
        return true;
    }

    private void sendBookingRequestValidateEventByBusiness(Integer businessId, String startDate) {
        MoeGroomingAppointmentExample example = new MoeGroomingAppointmentExample();
        String start =
                startDate != null ? startDate : LocalDate.now().minusDays(7).toString();
        String end = LocalDate.parse(start).plusYears(1).toString();
        example.createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andAppointmentDateGreaterThanOrEqualTo(start)
                .andAppointmentDateLessThanOrEqualTo(end)
                .andIsDeprecateEqualTo(CommonConstant.NO)
                .andIsAutoAcceptEqualTo(Boolean.FALSE)
                .andSourceEqualTo(GroomingAppointmentEnum.SOURCE_OB);
        List<MoeGroomingAppointment> appointments = appointmentMapper.selectByExample(example);
        log.info("sendBookingRequestValidateEvent businessId: {}, count: {}", businessId, appointments.size());
        appointments.forEach(appointment -> activeMQService.publishBookingRequestEvent(new BookingRequestEventParams()
                .setBusinessId(appointment.getBusinessId())
                .setAppointmentId(appointment.getId())
                .setEvent(BookingRequestEventParams.BookingRequestEvent.VALIDATE)));
    }

    @Override
    public List<MoeZipCodeDTO> searchByPrefix(@RequestParam String prefix) {
        List<MoeZipcode> codes = moeZipcodeMapper.searchByZipcodePrefix(prefix);
        return ZipCodeMapper.INSTANCE.toMoeZipCodeDTOList(codes);
    }

    @Override
    public List<MoeZipCodeDTO> selectByZipCodes(@RequestParam List<String> zipCodes) {
        if (CollectionUtils.isEmpty(zipCodes)) {
            return Collections.emptyList();
        }
        List<MoeZipcode> zipcodes = moeZipcodeMapper.selectByZipcodeList(zipCodes);
        return ZipCodeMapper.INSTANCE.toMoeZipCodeDTOList(zipcodes);
    }

    @Override
    public List<MoeZipCodeDTO> selectByZipCodesPost(@RequestBody List<String> zipCodes) {
        if (CollectionUtils.isEmpty(zipCodes)) {
            return Collections.emptyList();
        }
        List<MoeZipcode> zipcodes = moeZipcodeMapper.selectByZipcodeList(zipCodes);
        return ZipCodeMapper.INSTANCE.toMoeZipCodeDTOList(zipcodes);
    }
}
