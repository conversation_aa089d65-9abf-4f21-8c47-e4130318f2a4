package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.OrderDecouplingFlowMarker;
import com.moego.server.grooming.mapperbean.OrderDecouplingFlowMarkerExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OrderDecouplingFlowMarkerMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_decoupling_flow_marker
     *
     * @mbg.generated
     */
    long countByExample(OrderDecouplingFlowMarkerExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_decoupling_flow_marker
     *
     * @mbg.generated
     */
    int deleteByExample(OrderDecouplingFlowMarkerExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_decoupling_flow_marker
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_decoupling_flow_marker
     *
     * @mbg.generated
     */
    int insert(OrderDecouplingFlowMarker record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_decoupling_flow_marker
     *
     * @mbg.generated
     */
    int insertSelective(OrderDecouplingFlowMarker record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_decoupling_flow_marker
     *
     * @mbg.generated
     */
    List<OrderDecouplingFlowMarker> selectByExample(OrderDecouplingFlowMarkerExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_decoupling_flow_marker
     *
     * @mbg.generated
     */
    OrderDecouplingFlowMarker selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_decoupling_flow_marker
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") OrderDecouplingFlowMarker record,
            @Param("example") OrderDecouplingFlowMarkerExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_decoupling_flow_marker
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") OrderDecouplingFlowMarker record,
            @Param("example") OrderDecouplingFlowMarkerExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_decoupling_flow_marker
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(OrderDecouplingFlowMarker record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_decoupling_flow_marker
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(OrderDecouplingFlowMarker record);
}
