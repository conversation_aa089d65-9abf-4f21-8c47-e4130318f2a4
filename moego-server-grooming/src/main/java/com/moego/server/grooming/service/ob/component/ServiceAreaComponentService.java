package com.moego.server.grooming.service.ob.component;

import com.moego.server.business.api.IGeoAreaService;
import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.web.vo.ob.component.ServiceAreaComponentVO;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service(value = LandingPageComponentEnum.COMPONENT_SERVICE_AREA)
@RequiredArgsConstructor
public class ServiceAreaComponentService implements ILandingPageComponentService {

    private final MoeBusinessBookOnlineMapper bookOnlineMapper;
    private final IGeoAreaService geoAreaApi;

    @Override
    public ServiceAreaComponentVO getPageComponent(MoeBookOnlineLandingPageConfig landingPageConfig) {
        ServiceAreaComponentVO vo = new ServiceAreaComponentVO();
        vo.setResult(geoAreaApi.listGeoArea(getServiceAreaIdsForOB(landingPageConfig.getBusinessId())).stream()
                .map(ServiceAreaComponentVO.Area::from)
                .toList());
        return vo;
    }

    private List<Integer> getServiceAreaIdsForOB(Integer businessId) {
        MoeBusinessBookOnline bookOnline = bookOnlineMapper.selectByBusinessId(businessId);
        return bookOnline.getServiceAreas();
    }
}
