package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingReportResource;
import com.moego.server.grooming.mapperbean.MoeGroomingReportResourceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingReportResourceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_resource
     *
     * @mbg.generated
     */
    long countByExample(MoeGroomingReportResourceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_resource
     *
     * @mbg.generated
     */
    int deleteByExample(MoeGroomingReportResourceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_resource
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_resource
     *
     * @mbg.generated
     */
    int insert(MoeGroomingReportResource record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_resource
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingReportResource record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_resource
     *
     * @mbg.generated
     */
    List<MoeGroomingReportResource> selectByExample(MoeGroomingReportResourceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_resource
     *
     * @mbg.generated
     */
    MoeGroomingReportResource selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_resource
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeGroomingReportResource record,
            @Param("example") MoeGroomingReportResourceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_resource
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeGroomingReportResource record,
            @Param("example") MoeGroomingReportResourceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_resource
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingReportResource record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_resource
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingReportResource record);
}
