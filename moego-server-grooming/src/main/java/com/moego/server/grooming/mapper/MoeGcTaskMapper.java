package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGcTask;
import org.apache.ibatis.annotations.Param;

public interface MoeGcTaskMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_task
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_task
     *
     * @mbg.generated
     */
    int insert(MoeGcTask record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_task
     *
     * @mbg.generated
     */
    int insertSelective(MoeGcTask record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_task
     *
     * @mbg.generated
     */
    MoeGcTask selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_task
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGcTask record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_task
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGcTask record);

    MoeGcTask selectByGcCalendarId(@Param("gcCalendarId") Integer gcCalendarId);

    int updateFailTask(@Param("nowTime") Long nowTime, @Param("nowFailTime") Long nowFailTime);
}
