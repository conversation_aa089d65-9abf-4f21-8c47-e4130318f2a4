package com.moego.server.grooming.utils;

import com.moego.server.grooming.enums.AppointmentStatusEnum;

public class StatusUtil {
    public static String getApptStatusForReport(AppointmentStatusEnum status) {
        return switch (status) {
            case UNCONFIRMED -> "Unconfirmed";
            case CONFIRMED, READY, CHECK_IN -> "Confirmed";
            case FINISHED -> "Finished";
            case CANCELED -> "Cancelled";
            default -> "Unknown";
        };
    }

    public static String getApptStatusForReport(Byte status) {
        return getApptStatusForReport(AppointmentStatusEnum.fromValue(status));
    }
}
