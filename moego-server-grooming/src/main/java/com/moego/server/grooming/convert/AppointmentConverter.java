package com.moego.server.grooming.convert;

import com.google.protobuf.ProtocolMessageEnum;
import com.google.protobuf.Timestamp;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentPaymentStatus;
import com.moego.idl.models.appointment.v1.AppointmentSource;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.AppointmentTracking;
import com.moego.server.grooming.dto.AppointmentTrackingViewDTO;
import com.moego.server.grooming.dto.GroomingAppointmentDto;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE,
        uses = {TimestampConverter.class, StructConverter.class})
public interface AppointmentConverter {
    AppointmentConverter INSTANCE = Mappers.getMapper(AppointmentConverter.class);

    @Mapping(target = "readyTimestamp", source = "readyTime")
    AppointmentModel toModel(MoeGroomingAppointment appointment);

    default AppointmentStatus mapAppointmentStatus(Integer status) {
        return AppointmentStatus.forNumber(status);
    }

    default AppointmentStatus mapAppointmentStatusEnum(AppointmentStatusEnum status) {
        return AppointmentStatus.forNumber(status.getValue());
    }

    default AppointmentPaymentStatus mapAppointmentPaymentStatus(Integer isPaid) {
        return AppointmentPaymentStatus.forNumber(isPaid);
    }

    default AppointmentSource mapAppointmentSource(Integer source) {
        return AppointmentSource.forNumber(source);
    }

    List<GroomingAppointmentDto> toDtoList(List<MoeGroomingAppointment> appointmentList);

    AppointmentTrackingViewDTO toTrackingViewDto(AppointmentTracking tracking);

    default int protoEnumToInteger(ProtocolMessageEnum value) {
        return value.getNumber();
    }

    default Long toUnixSeconds(Timestamp value) {
        return value == null ? null : value.getSeconds();
    }
}
