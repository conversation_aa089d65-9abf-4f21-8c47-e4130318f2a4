package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingAddonApplicableService;
import com.moego.server.grooming.mapperbean.MoeGroomingAddonApplicableServiceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingAddonApplicableServiceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    long countByExample(MoeGroomingAddonApplicableServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    int deleteByExample(MoeGroomingAddonApplicableServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    int insert(MoeGroomingAddonApplicableService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingAddonApplicableService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    List<MoeGroomingAddonApplicableService> selectByExample(MoeGroomingAddonApplicableServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    MoeGroomingAddonApplicableService selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeGroomingAddonApplicableService record,
            @Param("example") MoeGroomingAddonApplicableServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeGroomingAddonApplicableService record,
            @Param("example") MoeGroomingAddonApplicableServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingAddonApplicableService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_addon_applicable_service
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingAddonApplicableService record);
}
