package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGcAuthStaff;

public interface MoeGcAuthStaffMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_auth_staff
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_auth_staff
     *
     * @mbg.generated
     */
    int insert(MoeGcAuthStaff record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_auth_staff
     *
     * @mbg.generated
     */
    int insertSelective(MoeGcAuthStaff record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_auth_staff
     *
     * @mbg.generated
     */
    MoeGcAuthStaff selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_auth_staff
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGcAuthStaff record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_auth_staff
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGcAuthStaff record);
}
