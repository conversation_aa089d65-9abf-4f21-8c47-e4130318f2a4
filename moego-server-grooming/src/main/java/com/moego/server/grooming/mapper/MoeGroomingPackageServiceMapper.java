package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingPackageService;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageServiceExample;
import com.moego.server.grooming.service.params.UpdatePackageServiceQuantityParams;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingPackageServiceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    long countByExample(MoeGroomingPackageServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    int insert(MoeGroomingPackageService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingPackageService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    List<MoeGroomingPackageService> selectByExample(MoeGroomingPackageServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    MoeGroomingPackageService selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeGroomingPackageService record,
            @Param("example") MoeGroomingPackageServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeGroomingPackageService record,
            @Param("example") MoeGroomingPackageServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingPackageService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingPackageService record);

    MoeGroomingPackageService queryPackageServiceByProp(
            @Param("businessId") Integer businessId,
            @Param("customerId") Integer customerId,
            @Param("packageId") Integer packageId,
            @Param("packageServiceId") Integer packageServiceId);

    Integer batchUpdateMoeGroomingPackageService(List<MoeGroomingPackageService> moeGroomingPackageServices);

    Integer updateQuantityById(@Param("id") Integer id, @Param("quantity") Integer quantity);

    Integer batchUpdateQuantityByIds(@Param("updateList") List<UpdatePackageServiceQuantityParams> updateList);
}
