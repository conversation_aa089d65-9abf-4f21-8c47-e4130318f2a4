package com.moego.server.grooming.service.dto;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class StaffPaymentForReportDTO {

    // apt + no show总实收金额、总收款金额、总退款金额
    private BigDecimal totalPayment;
    private BigDecimal totalRefund;
    // apt type 总实收金额、总收款金额、总退款金额
    private BigDecimal aptPayment;
    private BigDecimal aptRefund;
    // no show fee 总实收金额、总收款金额、总退款金额
    private BigDecimal noShowPayment;
    private BigDecimal noShowRefund;

    // 净实收收入 = payment - convenienceFee - tax - tips - refund
    private BigDecimal netSaleRevenue;
    private BigDecimal collectedServicePrice;
    private BigDecimal collectedProductPrice;
    private BigDecimal collectedTips;
    private BigDecimal collectedTax;
    private BigDecimal discount;

    /**
     * 构建赋初始值的对象
     *
     * @return
     */
    public static StaffPaymentForReportDTO buildInitializedBean() {
        StaffPaymentForReportDTO dto = new StaffPaymentForReportDTO();
        dto.setTotalPayment(BigDecimal.ZERO);
        dto.setTotalRefund(BigDecimal.ZERO);
        dto.setAptPayment(BigDecimal.ZERO);
        dto.setAptRefund(BigDecimal.ZERO);
        dto.setNoShowPayment(BigDecimal.ZERO);
        dto.setNoShowRefund(BigDecimal.ZERO);
        dto.setNetSaleRevenue(BigDecimal.ZERO);
        dto.setCollectedServicePrice(BigDecimal.ZERO);
        dto.setCollectedProductPrice(BigDecimal.ZERO);
        dto.setCollectedTax(BigDecimal.ZERO);
        dto.setCollectedTips(BigDecimal.ZERO);
        dto.setDiscount(BigDecimal.ZERO);
        return dto;
    }
}
