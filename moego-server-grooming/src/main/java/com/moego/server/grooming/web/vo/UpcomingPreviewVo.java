package com.moego.server.grooming.web.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class UpcomingPreviewVo {

    @NotNull
    private Integer customerId;

    @JsonIgnore
    private Boolean isPreview;

    @NotNull
    @Schema(description = "share appt 状态  0 all 1 unconfirm 2confirm 3 finished")
    private Byte shareApptStatus;

    @NotNull
    @Schema(description = "0 all  1 in x days  2 next x appointment  3 manually apptids")
    private Byte shareRangeType;

    @Schema(description = "不同type时的value")
    private Integer shareRangeValue;

    //    private String shareApptJson;
    @Schema(description = "当share_range_type为3时，记录的所有apptIds，仅shareRangeType为3时生效")
    private List<Integer> shareApptIds;
}
