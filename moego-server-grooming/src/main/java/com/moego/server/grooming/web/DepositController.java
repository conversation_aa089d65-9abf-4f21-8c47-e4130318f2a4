package com.moego.server.grooming.web;

import com.moego.common.dto.GuidDto;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.dto.DepositDto;
import com.moego.server.grooming.mapperbean.MoeInvoiceDeposit;
import com.moego.server.grooming.params.DepositVo;
import com.moego.server.grooming.service.DepositService;
import com.moego.server.grooming.service.InvoiceService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2021/10/21 11:09 AM
 */
@RestController
@Slf4j
@RequestMapping("/grooming/deposit")
public class DepositController {

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private DepositService depositService;

    // DONE(Frank, P0): 检查 invoiceId 属于 businessId
    // 已在 DepositService::createDepositId 函数中添加校验
    @PostMapping("/create")
    @Operation(summary = "生成depositGuid， 用于pay online 支付定金; 如果已存在，就更新amount")
    @Auth(AuthType.BUSINESS)
    public GuidDto checkInvoiceGuid(AuthContext context, @RequestBody DepositVo depositVo) {
        depositVo.setBusinessId(context.getBusinessId());
        depositVo.setStaffId(context.getStaffId());
        GuidDto result = new GuidDto();
        // 新建 or update deposit guid，并记录关联关系
        result.setGuid(depositService.createDepositId(depositVo, Boolean.TRUE));
        return result;
    }

    // DONE(Frank, P0): 此处不应该校验, 这个接口应该是要匿名访问的，使用 guid 的查询接口基本上都是产品上要求匿名访问的
    // DONE(Frank, P0): 考虑换一下函数名, 和下面个名字一样了, 这个没什么意义
    // FIXME(Ritchie, P2): 可以考虑优化一下 guid 的格式, 避免对 guid 加索引
    @GetMapping("/client/detail")
    @Auth(AuthType.ANONYMOUS)
    public DepositDto getDepositAndInvoiceDetailByGUID(@RequestParam String depositGuid) {
        DepositDto result = new DepositDto();
        MoeInvoiceDeposit deposit = depositService.getDepositByGuid(depositGuid);
        if (deposit == null) {
            return null;
        }
        BeanUtils.copyProperties(deposit, result);
        return result;
    }

    @GetMapping("/detail")
    @Auth(AuthType.BUSINESS)
    public DepositDto getDepositAndInvoiceDetailByInvoiceId(AuthContext context, @RequestParam Integer invoiceId) {
        DepositDto result = new DepositDto();
        MoeInvoiceDeposit deposit = depositService.getDepositByInvoiceId(invoiceId);
        if (deposit == null) {
            return null;
        }
        if (!deposit.getBusinessId().equals(context.getBusinessId())) {
            return null;
        }
        BeanUtils.copyProperties(deposit, result);
        return result;
    }
}
