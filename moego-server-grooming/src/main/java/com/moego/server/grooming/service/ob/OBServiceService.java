package com.moego.server.grooming.service.ob;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.common.constant.CommonConstant;
import com.moego.common.dto.CompanyFunctionControlDto;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.CompanyFunctionControlConst;
import com.moego.common.enums.CustomerPetEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCoatTypeModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizedServiceCategoryView;
import com.moego.idl.models.offering.v1.ServiceApplicableFilter;
import com.moego.idl.models.offering.v1.ServiceFilterByPet;
import com.moego.idl.models.offering.v1.ServiceFilterByService;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.service.business_customer.v1.BusinessPetCoatTypeServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetSizeServiceGrpc;
import com.moego.idl.service.offering.v1.GetApplicableServiceListRequest;
import com.moego.idl.service.offering.v1.GetApplicableServiceListResponse;
import com.moego.idl.service.offering.v1.GetServiceDetailRequest;
import com.moego.idl.service.offering.v1.GetServiceDetailResponse;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.migrate.MigrateInfo;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.customer.api.ICustomerProfileRequestService;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.grooming.dto.PetApplicableServiceDTO;
import com.moego.server.grooming.dto.PetServiceDTO;
import com.moego.server.grooming.dto.ServiceCategoryDTO;
import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import com.moego.server.grooming.dto.ob.OBServiceDTO;
import com.moego.server.grooming.dto.ob.OBServiceListDto;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAcceptPetType;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices;
import com.moego.server.grooming.params.PetDataForServiceParams;
import com.moego.server.grooming.service.BookOnlineAcceptPetTypeService;
import com.moego.server.grooming.service.CompanyGroomingServiceQueryService;
import com.moego.server.grooming.service.GroomingServiceService;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.web.dto.ob.OBPetServiceDTO;
import com.moego.server.grooming.web.vo.ob.OBLandingPageServiceCategoryVO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2022/11/11
 */
@Slf4j
@Service
@AllArgsConstructor
public class OBServiceService {

    private final GroomingServiceService serviceService;
    private final CompanyGroomingServiceQueryService companyGroomingServiceQueryService;

    private final IBusinessBusinessClient iBusinessBusinessClient;
    private final MoeGroomingBookOnlineService bookOnlineService;
    private final GroomingServiceService groomingServiceService;
    private MigrateHelper migrateHelper;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementService;
    private final BusinessPetSizeServiceGrpc.BusinessPetSizeServiceBlockingStub businessPetSizeServiceClient;
    private final BusinessPetCoatTypeServiceGrpc.BusinessPetCoatTypeServiceBlockingStub
            businessPetCoatTypeServiceClient;
    private final BookOnlineAcceptPetTypeService bookOnlineAcceptPetTypeService;
    private final ICustomerProfileRequestService customerProfileRequestService;
    private final CompanyHelper companyHelper;

    public List<ServiceCategoryDTO> getServiceCategoryListByBusinessId(Integer businessId) {
        return companyGroomingServiceQueryService.groomingServiceCategorySelectCategoryByCidOrBid(
                null, businessId, null);
    }

    public Map<Integer, Map<Byte, List<OBPetServiceDTO>>> getServiceMapByBusinessId(Integer businessId) {
        List<OBPetServiceDTO> petServiceDTOList = queryObService(businessId, null);
        return petServiceDTOList.stream()
                .collect(Collectors.groupingBy(
                        OBPetServiceDTO::getCategoryId, Collectors.groupingBy(OBPetServiceDTO::getType)));
    }

    public Map<Integer, Map<Integer, Map<Byte, MoeGroomingCustomerServices>>> getCustomerServiceMap(
            Integer businessId, Integer customerId, List<Integer> petIdList) {
        List<MoeGroomingCustomerServices> moeGroomingCustomerServices =
                companyGroomingServiceQueryService.getCustomizeServices(businessId, customerId, petIdList, null, null);
        // petId - serviceId - saveType - customerService
        return moeGroomingCustomerServices.stream()
                .collect(Collectors.groupingBy(
                        MoeGroomingCustomerServices::getPetId,
                        Collectors.groupingBy(
                                MoeGroomingCustomerServices::getServiceId,
                                Collectors.toMap(
                                        MoeGroomingCustomerServices::getSaveType,
                                        Function.identity(),
                                        // get latest in case of conflict https://moego.atlassian.net/browse/ERP-4122
                                        (s1, s2) -> s1.getUpdateTime() - s2.getUpdateTime() >= 0 ? s1 : s2))));
    }

    public void addDefaultServiceCategory(Integer businessId, List<ServiceCategoryDTO> serviceCategories) {
        ServiceCategoryDTO serviceCategory0DTOSrv = new ServiceCategoryDTO();
        serviceCategory0DTOSrv.setSort(Integer.MAX_VALUE);
        serviceCategory0DTOSrv.setId(0);
        serviceCategory0DTOSrv.setBusinessId(businessId);
        serviceCategory0DTOSrv.setType(ServiceEnum.TYPE_SERVICE);
        serviceCategories.add(serviceCategory0DTOSrv);

        ServiceCategoryDTO serviceCategory0DTOAddon = new ServiceCategoryDTO();
        serviceCategory0DTOAddon.setSort(Integer.MAX_VALUE);
        serviceCategory0DTOAddon.setId(0);
        serviceCategory0DTOAddon.setBusinessId(businessId);
        serviceCategory0DTOAddon.setType(ServiceEnum.TYPE_ADD_ONS);
        serviceCategories.add(serviceCategory0DTOAddon);
    }

    public OBServiceListDto getPetServiceList(OBServiceDTO obServiceDTO) {
        Integer businessId = obServiceDTO.getBusinessId();
        // existing client id
        Integer customerId = obServiceDTO.getCustomerId();
        // existing pet id
        List<Integer> petIdList = obServiceDTO.getPetDataList().stream()
                .map(OBPetDataDTO::getPetId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 查询所有的service和category
        List<ServiceCategoryDTO> serviceCategories = getServiceCategoryListByBusinessId(businessId);
        Map<Integer, Map<Byte, List<OBPetServiceDTO>>> categoryServiceMap = getServiceMapByBusinessId(businessId);
        // 添加没有category的service组
        addDefaultServiceCategory(businessId, serviceCategories);
        // 对服务分组
        List<ServiceCategoryDTO> serviceList = new ArrayList<>();
        List<ServiceCategoryDTO> addonsList = new ArrayList<>();
        for (ServiceCategoryDTO serviceCategory : serviceCategories) {
            if (serviceCategory.getPetServices() == null) {
                serviceCategory.setPetServices(new ArrayList<>());
            }
            Map<Byte, List<OBPetServiceDTO>> typePetServiceDTOMap = categoryServiceMap.get(serviceCategory.getId());
            if (MapUtils.isEmpty(typePetServiceDTOMap)) {
                continue;
            }
            if (ServiceEnum.TYPE_SERVICE.equals(serviceCategory.getType())) {
                List<OBPetServiceDTO> serviceDTOList = typePetServiceDTOMap.get(ServiceEnum.TYPE_SERVICE);
                if (CollectionUtils.isEmpty(serviceDTOList)) {
                    continue;
                }
                serviceCategory.getPetServices().addAll(serviceDTOList);
                serviceList.add(serviceCategory);
            } else {
                List<OBPetServiceDTO> addonDTOList = typePetServiceDTOMap.get(ServiceEnum.TYPE_ADD_ONS);
                if (CollectionUtils.isEmpty(addonDTOList)) {
                    continue;
                }
                serviceCategory.getPetServices().addAll(addonDTOList);
                addonsList.add(serviceCategory);
            }
        }
        sortServiceAndCategory(addonsList);
        sortServiceAndCategory(serviceList);

        OBServiceListDto resultMap = new OBServiceListDto();
        resultMap.setServiceList(serviceList);
        resultMap.setAddonsList(addonsList);

        // 如果petId 或者 customerId为空，肯定没有save price和time
        if (Objects.nonNull(customerId) && !CollectionUtils.isEmpty(petIdList)) {
            Map<Integer, List<ServiceCategoryDTO>> petServiceList = new HashMap<>();
            Map<Integer, List<ServiceCategoryDTO>> petAddonsList = new HashMap<>();
            // add customize service to result
            resultMap.setPetServiceList(petServiceList);
            resultMap.setPetAddonsList(petAddonsList);

            Map<Integer, Map<Integer, Map<Byte, MoeGroomingCustomerServices>>> customerServiceMap =
                    getCustomerServiceMap(businessId, customerId, petIdList);
            petIdList.forEach(petId -> {
                List<ServiceCategoryDTO> tmpServiceList = new ArrayList<>();
                List<ServiceCategoryDTO> tmpAddonsList = new ArrayList<>();
                petServiceList.put(petId, tmpServiceList);
                petAddonsList.put(petId, tmpAddonsList);
                // clone origin service for this pet; collect all srv and over write price and time if needes.
                List<PetServiceDTO> currentPetAllSrv = new ArrayList<>();
                serviceList.forEach(srv -> {
                    ServiceCategoryDTO currentSrv = srv.clone();
                    tmpServiceList.add(currentSrv);
                    currentPetAllSrv.addAll(currentSrv.getPetServices());
                });
                addonsList.forEach(srv -> {
                    ServiceCategoryDTO currentSrv = srv.clone();
                    tmpAddonsList.add(currentSrv);
                    currentPetAllSrv.addAll(currentSrv.getPetServices());
                });
                // serviceId - saveType - customerService
                Map<Integer, Map<Byte, MoeGroomingCustomerServices>> serviceTypeMap = customerServiceMap.get(petId);
                if (MapUtils.isEmpty(serviceTypeMap)) {
                    return;
                }
                // saved pet service
                for (PetServiceDTO petServiceDTO : currentPetAllSrv) {
                    petServiceDTO.setIsSavePrice(false);
                    petServiceDTO.setIsSaveTime(false);
                    Map<Byte, MoeGroomingCustomerServices> saveTypeMap = serviceTypeMap.get(petServiceDTO.getId());
                    if (MapUtils.isEmpty(saveTypeMap)) {
                        continue;
                    }
                    MoeGroomingCustomerServices priceService = saveTypeMap.get(CustomerPetEnum.SERVICE_SAVE_TYPE_PRICE);
                    if (Objects.nonNull(priceService)) {
                        petServiceDTO.setIsSavePrice(true);
                        petServiceDTO.setPrice(priceService.getServiceFee());
                    }
                    MoeGroomingCustomerServices timeService = saveTypeMap.get(CustomerPetEnum.SERVICE_SAVE_TYPE_TIME);
                    if (Objects.nonNull(timeService)) {
                        petServiceDTO.setIsSaveTime(true);
                        petServiceDTO.setDuration(timeService.getServiceTime());
                    }
                }
            });
        }

        // 查询applicable service，权限控制
        MoeBusinessBookOnline setting = bookOnlineService.getSettingInfoByBusinessId(businessId);
        if (Objects.equals(setting.getServiceFilter(), CommonConstant.ENABLE) && isServiceFilterAllowed(businessId)) {
            List<PetServiceDTO> petServices = Stream.concat(serviceList.stream(), addonsList.stream())
                    .flatMap(serviceCategoryDTO -> serviceCategoryDTO.getPetServices().stream())
                    .collect(Collectors.toList());
            List<PetApplicableServiceDTO> applicableServiceList =
                    getApplicableServiceList(businessId, petServices, obServiceDTO.getPetDataList());
            resultMap.setApplicableServiceList(applicableServiceList);
            // fill all service
            if (CollectionUtils.isEmpty(applicableServiceList)) {
                resultMap.setApplicableServiceList(fillAllServices(
                        obServiceDTO.getPetDataList(), resultMap.getServiceList(), resultMap.getAddonsList()));
            }
        } else {
            // https://moego.atlassian.net/browse/ERP-8799 前端拿掉 service filter 的判断，统一后端返回可用列表
            resultMap.setApplicableServiceList(fillAllServices(
                    obServiceDTO.getPetDataList(), resultMap.getServiceList(), resultMap.getAddonsList()));
        }

        return resultMap;
    }

    public void checkApplicableServiceByPetId(
            Map<Integer /* pet id */, Pair<List<Integer> /* service ids */, List<Integer> /* addon ids */>>
                    petServiceMap,
            Integer businessId,
            Long companyId,
            Integer customerId) {
        if (CollectionUtils.isEmpty(petServiceMap)) {
            return;
        }
        // petId -> pet
        Map<Integer, ServiceFilterByPet> petFilterMap =
                getPetFilters(companyId, businessId, new ArrayList<>(petServiceMap.keySet()), customerId);

        Map<ServiceFilterByPet, Pair<List<Integer>, List<Integer>>> petMap = petServiceMap.entrySet().stream()
                .filter(entry -> petFilterMap.containsKey(entry.getKey()))
                .map(entry -> Map.entry(petFilterMap.get(entry.getKey()), entry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> {
                    // merge service ids and add-on ids
                    List<Integer> serviceIds = Stream.of(a.getFirst(), b.getFirst())
                            .flatMap(List::stream)
                            .distinct()
                            .toList();
                    List<Integer> addOnIds = Stream.of(a.getSecond(), b.getSecond())
                            .flatMap(List::stream)
                            .distinct()
                            .toList();
                    return Pair.of(serviceIds, addOnIds);
                }));
        checkApplicableServiceByPet(petMap, businessId, companyId);
    }

    public void checkApplicableServiceByPet(
            Map<ServiceFilterByPet, Pair<List<Integer> /* service ids */, List<Integer> /* addon ids */>> petServiceMap,
            Integer businessId,
            Long companyId) {
        if (CollectionUtils.isEmpty(petServiceMap)) {
            return;
        }
        // 1. check service is applicable for online booking
        List<Integer> allServiceIds = petServiceMap.values().stream()
                .map(pair -> Stream.concat(pair.getFirst().stream(), pair.getSecond().stream())
                        .toList())
                .flatMap(List::stream)
                .distinct()
                .toList();
        companyGroomingServiceQueryService.obServiceQueryByIds(companyId, businessId, allServiceIds).entrySet().stream()
                .filter(entry -> Objects.equals(entry.getValue().getBookOnlineAvailable(), BooleanEnum.VALUE_FALSE))
                .findFirst()
                .ifPresent(entry -> {
                    throw bizException(
                            Code.CODE_PARAMS_ERROR,
                            "Service not applicable: "
                                    + getServiceName(entry.getValue().getServiceId()));
                });

        // 2. check only show available service
        boolean onlyAvailable = false;
        MoeBusinessBookOnline setting = bookOnlineService.getSettingInfoByBusinessId(businessId);
        if (Objects.equals(setting.getServiceFilter(), CommonConstant.ENABLE) && isServiceFilterAllowed(businessId)) {
            onlyAvailable = true;
        }

        // get acceptable pet type
        Map<Integer, Boolean> acceptPetTypeMap =
                bookOnlineAcceptPetTypeService.getAcceptPetTypeList(companyId, businessId).stream()
                        .collect(Collectors.toMap(
                                MoeBookOnlineAcceptPetType::getPetTypeId, rec -> Objects.equals(rec.getAccepted(), 1)));

        // 3. check service is applicable for each pet
        for (Map.Entry<ServiceFilterByPet, Pair<List<Integer>, List<Integer>>> entry : petServiceMap.entrySet()) {

            // check acceptable pet type
            if (Objects.equals(
                    acceptPetTypeMap.getOrDefault(entry.getKey().getPetType().getNumber(), Boolean.FALSE),
                    Boolean.FALSE)) {
                throw bizException(Code.CODE_PARAMS_ERROR, "Pet type is not accepted.");
            }

            // check service
            GetApplicableServiceListResponse applicableServiceResponse =
                    serviceManagementService.getApplicableServiceList(GetApplicableServiceListRequest.newBuilder()
                            .setCompanyId(companyId)
                            .setBusinessId(businessId)
                            .setOnlyAvailable(onlyAvailable)
                            .setServiceType(ServiceType.SERVICE)
                            .setFilter(ServiceApplicableFilter.newBuilder()
                                    .setFilterByPet(entry.getKey())
                                    .build())
                            .setInactive(false)
                            .build());
            List<Integer> applicableServices = applicableServiceResponse.getCategoryListList().stream()
                    .map(CustomizedServiceCategoryView::getServicesList)
                    .flatMap(Collection::stream)
                    .map(service -> Math.toIntExact(service.getId()))
                    .toList();
            entry.getValue().getFirst().stream()
                    .filter(Predicate.not(applicableServices::contains))
                    .findFirst()
                    .ifPresent(serviceId -> {
                        throw bizException(
                                Code.CODE_PARAMS_ERROR, "Service not applicable: " + getServiceName(serviceId));
                    });

            // check add-on
            GetApplicableServiceListResponse applicableAddOnResponse =
                    serviceManagementService.getApplicableServiceList(GetApplicableServiceListRequest.newBuilder()
                            .setCompanyId(companyId)
                            .setBusinessId(businessId)
                            .setOnlyAvailable(onlyAvailable)
                            .setServiceType(ServiceType.ADDON)
                            .setFilter(ServiceApplicableFilter.newBuilder()
                                    .setFilterByPet(entry.getKey())
                                    .setFilterByService(ServiceFilterByService.newBuilder()
                                            .addAllServiceIds(entry.getValue().getFirst().stream()
                                                    .map(Long::valueOf)
                                                    .toList())
                                            .build())
                                    .build())
                            .setInactive(false)
                            .build());
            List<Integer> applicableAddOns = applicableAddOnResponse.getCategoryListList().stream()
                    .map(CustomizedServiceCategoryView::getServicesList)
                    .flatMap(Collection::stream)
                    .map(service -> Math.toIntExact(service.getId()))
                    .toList();
            entry.getValue().getSecond().stream()
                    .filter(Predicate.not(applicableAddOns::contains))
                    .findFirst()
                    .ifPresent(addOnId -> {
                        throw bizException(
                                Code.CODE_PARAMS_ERROR, "Service not applicable: " + getServiceName(addOnId));
                    });
        }
    }

    private String getServiceName(Integer serviceId) {
        GetServiceDetailResponse serviceDetail = serviceManagementService.getServiceDetail(
                GetServiceDetailRequest.newBuilder().setServiceId(serviceId).build());
        return Optional.of(serviceDetail.getService())
                .map(ServiceModel::getName)
                .orElse(serviceId.toString());
    }

    private List<PetApplicableServiceDTO> fillAllServices(
            List<OBPetDataDTO> petDataList,
            List<ServiceCategoryDTO> serviceCategoryDTOList,
            List<ServiceCategoryDTO> addonCategoryDTOList) {
        if (CollectionUtils.isEmpty(serviceCategoryDTOList) && CollectionUtils.isEmpty(addonCategoryDTOList)) {
            return Collections.emptyList();
        }
        List<PetApplicableServiceDTO> allApplicableServiceList = new ArrayList<>();
        List<Integer> allServiceIds = serviceCategoryDTOList.stream()
                .map(ServiceCategoryDTO::getPetServices)
                .flatMap(Collection::stream)
                .map(PetServiceDTO::getId)
                .toList();
        List<Integer> allAddonIds = addonCategoryDTOList.stream()
                .map(ServiceCategoryDTO::getPetServices)
                .flatMap(Collection::stream)
                .map(PetServiceDTO::getId)
                .toList();
        petDataList.forEach(petDataDTO -> {
            PetApplicableServiceDTO serviceDTO = new PetApplicableServiceDTO();
            serviceDTO.setPetId(petDataDTO.getPetId());
            serviceDTO.setApplicableService(allServiceIds);
            serviceDTO.setApplicableAddon(allAddonIds);
            allApplicableServiceList.add(serviceDTO);
        });
        return allApplicableServiceList;
    }

    /**
     * 对类别和服务列表排序
     *
     * @param serviceCategories
     */
    private void sortServiceAndCategory(List<ServiceCategoryDTO> serviceCategories) {
        // 排序
        serviceCategories.sort((o1, o2) -> o2.getSort().compareTo(o1.getSort()));
        for (ServiceCategoryDTO serviceCategory : serviceCategories) {
            serviceCategory.getPetServices().sort((o1, o2) -> o2.getSort().compareTo(o1.getSort()));
        }
    }

    /**
     * service by breed权限控制：仅69以上用户可以使用
     *
     * @param businessId
     * @return
     */
    public boolean isServiceFilterAllowed(Integer businessId) {
        CompanyFunctionControlDto controlDto = iBusinessBusinessClient.queryCompanyPermissionByBusinessId(businessId);
        return (controlDto != null
                && Objects.equals(controlDto.getPremiumType(), CompanyFunctionControlConst.PREMIUM_TYPE_69));
    }

    private List<PetApplicableServiceDTO> getApplicableServiceList(
            Integer businessId, List<PetServiceDTO> petServices, List<OBPetDataDTO> obPetDataList) {
        if (CollectionUtils.isEmpty(obPetDataList)) {
            return Collections.emptyList();
        }

        List<PetDataForServiceParams> petDataList = obPetDataList.stream()
                .map(petData -> {
                    PetDataForServiceParams params = new PetDataForServiceParams();
                    params.setPetId(petData.getPetId());
                    params.setWeight(petData.getWeight());
                    params.setPetTypeId(petData.getPetTypeId());
                    params.setBreed(petData.getBreed());
                    params.setCoat(petData.getCoat());

                    return params;
                })
                .toList();

        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        if (migrateInfo != null && migrateInfo.isMigrate()) {
            serviceService.setPetSizeId(migrateInfo.companyId(), petDataList);
        }
        return serviceService.getApplicableServiceList(businessId, petServices, petDataList);
    }

    public List<OBPetServiceDTO> queryObService(Integer businessId, Integer type) {
        var companyDto = iBusinessBusinessClient.getCompanyByBusinessId(businessId);
        if (companyDto == null || companyDto.getId() == null) {
            return Collections.emptyList();
        }
        return companyGroomingServiceQueryService.groomingServiceSelectObServiceByBusinessId(
                companyDto.getId().longValue(), businessId, type);
    }

    public List<OBLandingPageServiceCategoryVO> listLandingPageServiceCategoryVO(Integer businessId, Byte type) {
        // Select all service categories and services with ob enabled
        List<ServiceCategoryDTO> serviceCategoryDTOList =
                companyGroomingServiceQueryService.groomingServiceCategorySelectCategoryByCidOrBid(
                        null, businessId, type.intValue());
        // Add default category
        this.addDefaultServiceCategory(businessId, serviceCategoryDTOList);
        List<OBPetServiceDTO> obPetServiceDTOList = queryObService(businessId, type.intValue());
        Map<Integer, List<OBPetServiceDTO>> categoryServicesMap =
                obPetServiceDTOList.stream().collect(Collectors.groupingBy(OBPetServiceDTO::getCategoryId));

        // 查询 bundle service id list
        long companyId = companyHelper.mustGetCompanyId(businessId);
        Map<Long, List<Long>> bundleServiceMap = groomingServiceService.getBundleServiceMap(
                companyId,
                obPetServiceDTOList.stream()
                        .map(OBPetServiceDTO::getId)
                        .filter(CommonUtil::isNormal)
                        .map(Integer::longValue)
                        .distinct()
                        .toList());

        return serviceCategoryDTOList.stream()
                .sorted(Comparator.comparing(ServiceCategoryDTO::getSort).reversed())
                .filter(serviceCategoryDTO -> categoryServicesMap.containsKey(serviceCategoryDTO.getId())
                        && Objects.equals(type, serviceCategoryDTO.getType()))
                .map(serviceCategoryDTO -> new OBLandingPageServiceCategoryVO()
                        .setCategoryId(serviceCategoryDTO.getId())
                        .setCategoryName(serviceCategoryDTO.getName())
                        .setSort(serviceCategoryDTO.getSort())
                        .setCategoryType(serviceCategoryDTO.getType())
                        .setServiceList(categoryServicesMap.get(serviceCategoryDTO.getId()).stream()
                                .map(obPetServiceDTO -> new OBLandingPageServiceCategoryVO.OBLandingPageServiceVO()
                                        .setServiceId(obPetServiceDTO.getId())
                                        .setServiceName(obPetServiceDTO.getName())
                                        .setDescription(obPetServiceDTO.getDescription())
                                        .setServiceType(obPetServiceDTO.getType())
                                        .setColorCode(obPetServiceDTO.getColorCode())
                                        .setDuration(obPetServiceDTO.getDuration())
                                        .setPrice(obPetServiceDTO.getPrice())
                                        .setShowBasePrice(obPetServiceDTO.getShowBasePrice())
                                        .setSort(obPetServiceDTO.getSort())
                                        .setBundleServiceIds(bundleServiceMap.getOrDefault(
                                                obPetServiceDTO.getId().longValue(), List.of())))
                                .toList()))
                .toList();
    }

    public List<BusinessPetCoatTypeModel> getPetCoatTypeList(long companyId) {
        return businessPetCoatTypeServiceClient
                .listPetCoatType(com.moego.idl.service.business_customer.v1.ListPetCoatTypeRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getCoatTypesList();
    }

    public List<BusinessPetSizeModel> getPetSizeList(long companyId) {
        return businessPetSizeServiceClient
                .listPetSize(com.moego.idl.service.business_customer.v1.ListPetSizeRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getSizesList();
    }

    public Map<Integer, ServiceFilterByPet> getPetFilters(
            long companyId, Integer businessId, List<Integer> petIds, Integer customerId) {
        List<CustomerProfileRequestDTO.PetProfileDTO> petsProfileWithRequest =
                customerProfileRequestService.getPetsProfileWithRequest(businessId, customerId);
        if (CollectionUtils.isEmpty(petsProfileWithRequest)) {
            return Map.of();
        }

        var petSizeList = getPetSizeList(companyId);
        var petCoatList = getPetCoatTypeList(companyId);

        return petsProfileWithRequest.stream()
                .filter(petProfile -> petIds.contains(petProfile.getPetId()))
                .map(petProfile -> BusinessCustomerPetModel.newBuilder()
                        .setId(petProfile.getPetId())
                        .setPetType(PetType.forNumber(petProfile.getPetTypeId()))
                        .setBreed(petProfile.getBreed())
                        .setCoatType(petProfile.getHairLength())
                        .setWeight(petProfile.getWeight())
                        .build())
                .collect(Collectors.toMap(
                        pet -> Math.toIntExact(pet.getId()), pet -> getPetFilter(pet, petSizeList, petCoatList)));
    }

    public ServiceFilterByPet getPetFilter(
            BusinessCustomerPetModel pet,
            List<BusinessPetSizeModel> petSizeList,
            List<BusinessPetCoatTypeModel> petCoatList) {
        var builder = ServiceFilterByPet.newBuilder().setPetType(pet.getPetType());

        if (Strings.isNotEmpty(pet.getBreed())) {
            builder.setPetBreed(pet.getBreed());
        }

        if (Strings.isNotEmpty(pet.getCoatType())) {
            petCoatList.stream()
                    .filter(coatType -> coatType.getName().equals(pet.getCoatType()))
                    .findFirst()
                    .ifPresent(coatType -> builder.setPetCoatTypeId(coatType.getId()));
        }

        if (Strings.isNotEmpty(pet.getWeight())) {
            var weight = new BigDecimal(pet.getWeight());
            builder.setPetWeight(weight.doubleValue());

            var roundedWeight = weight.setScale(0, RoundingMode.HALF_UP).intValue();
            petSizeList.stream()
                    .filter(size -> size.getWeightLow() <= roundedWeight && size.getWeightHigh() >= roundedWeight)
                    .findFirst()
                    .ifPresentOrElse(
                            petSizeModel -> builder.setPetSizeId(petSizeModel.getId()),
                            () -> builder.setPetSizeId(-1)); // 如果未命中任何一个 Pet size，则传 -1，一定不会满足 Pet size filter 的条件
        }
        return builder.build();
    }
}
