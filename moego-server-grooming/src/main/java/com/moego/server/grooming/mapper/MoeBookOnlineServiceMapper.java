package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlineService;
import com.moego.server.grooming.mapperbean.MoeBookOnlineServiceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeBookOnlineServiceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    long countByExample(MoeBookOnlineServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    int deleteByExample(MoeBookOnlineServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlineService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlineService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    List<MoeBookOnlineService> selectByExample(MoeBookOnlineServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    MoeBookOnlineService selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeBookOnlineService record, @Param("example") MoeBookOnlineServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeBookOnlineService record, @Param("example") MoeBookOnlineServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlineService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_service
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlineService record);

    MoeBookOnlineService selectByUniqueIndexBidSid(
            @Param("companyId") Long companyId,
            @Param("businessId") Integer businessId,
            @Param("serviceId") Integer serviceId);

    List<MoeBookOnlineService> selectByUniqueIndexBidSids(
            @Param("companyId") Long companyId,
            @Param("businessId") Integer businessId,
            @Param("serviceIds") List<Integer> serviceIds);
}
