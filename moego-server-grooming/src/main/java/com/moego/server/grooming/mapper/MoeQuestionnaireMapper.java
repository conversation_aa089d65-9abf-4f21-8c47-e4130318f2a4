package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeQuestionnaire;
import org.apache.ibatis.annotations.Param;

public interface MoeQuestionnaireMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_questionnaire
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_questionnaire
     *
     * @mbg.generated
     */
    int insert(MoeQuestionnaire record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_questionnaire
     *
     * @mbg.generated
     */
    int insertSelective(MoeQuestionnaire record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_questionnaire
     *
     * @mbg.generated
     */
    MoeQuestionnaire selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_questionnaire
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeQuestionnaire record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_questionnaire
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeQuestionnaire record);

    MoeQuestionnaire queryOne(@Param("businessId") Long businessId, @Param("createdBy") Long createdBy);
}
