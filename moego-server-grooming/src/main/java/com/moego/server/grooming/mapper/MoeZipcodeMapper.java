package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeZipcode;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeZipcodeMapper {
    /**
     * 根据主键id查询
     * @param id
     * @return
     */
    MoeZipcode selectByPrimaryKey(Integer id);

    /**
     * 根据zipcode前缀查询
     * @param prefix
     * @return
     */
    List<MoeZipcode> searchByZipcodePrefix(@Param("prefix") String prefix);

    /**
     * 根据zipcode列表查询
     * @param zipcodeList
     * @return
     */
    List<MoeZipcode> selectByZipcodeList(@Param("zipcodeList") List<String> zipcodeList);
}
