package com.moego.server.grooming.web.ob;

import static com.moego.common.utils.PermissionUtil.CAN_ACCESS_ONLINE_BOOKING_REQUEST_AND_WAIT_LIST;
import static com.moego.common.utils.PermissionUtil.VIEW_CLIENT_EMAIL;
import static com.moego.common.utils.PermissionUtil.VIEW_CLIENT_PHONE;
import static com.moego.common.utils.PermissionUtil.checkStaffPermissionsInfo;
import static com.moego.common.utils.PermissionUtil.emailConfusion;
import static com.moego.common.utils.PermissionUtil.phoneNumberConfusion;

import com.moego.common.dto.StaffPermissions;
import com.moego.common.utils.PermissionUtil;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.grooming.enums.AbandonedBookingAction;
import com.moego.server.grooming.service.ob.OBAbandonRecordService;
import com.moego.server.grooming.service.ob.OBLandingPageConfigService;
import com.moego.server.grooming.service.ob.OBLandingPageUpgradeService;
import com.moego.server.grooming.service.ob.OBMetricService;
import com.moego.server.grooming.web.params.OBLandingPageConfigParams;
import com.moego.server.grooming.web.params.OBLandingPageMergeParams;
import com.moego.server.grooming.web.params.OBMetricsParams;
import com.moego.server.grooming.web.result.ob.BriefOBProfileResult;
import com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO;
import com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO;
import com.moego.server.grooming.web.vo.ob.OBLandingPageMergeVO;
import com.moego.server.grooming.web.vo.ob.OBMetricVO;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/2/20
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/grooming/ob/v2/business")
public class BookOnlineV2BusinessController {

    private final OBLandingPageConfigService landingPageConfigService;
    private final OBLandingPageUpgradeService landingPageUpgradeService;
    private final OBMetricService metricService;
    private final OBAbandonRecordService abandonRecordService;
    private final IBusinessStaffService businessStaffService;

    @GetMapping("/config")
    @Auth(AuthType.BUSINESS)
    public OBLandingPageConfigVO getLandingPageConfig(AuthContext context) {
        return landingPageConfigService.getLandingPageConfigVOByBusinessId(context.getBusinessId());
    }

    @PostMapping("/config")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.LANDING_PAGE_SETTING,
            details = "#landingPageConfigParams")
    public void updateLandingPageConfig(
            AuthContext context, @Valid @RequestBody OBLandingPageConfigParams landingPageConfigParams) {
        landingPageConfigService.updateLandingPageConfig(
                context.getBusinessId(), context.companyId(), landingPageConfigParams);
    }

    @GetMapping("/migration")
    @Auth(AuthType.BUSINESS)
    public OBLandingPageMergeVO getLandingPageMergeProfile(AuthContext context) {
        return landingPageUpgradeService.getLandingPageMergeProfile(context.getBusinessId());
    }

    @PutMapping("/migration")
    @Auth(AuthType.BUSINESS)
    public void updateLandingPageMergeProfile(
            AuthContext context, @Valid @RequestBody OBLandingPageMergeParams landingPageMergeParams) {
        landingPageUpgradeService.upgradeLandingPage(
                context.getBusinessId(), context.companyId(), landingPageMergeParams);
    }

    @PostMapping("/metrics")
    @Auth(AuthType.BUSINESS)
    public List<OBMetricVO> listMetrics(@RequestBody OBMetricsParams metricParams) {
        return metricService.listMetrics(metricParams);
    }

    private static final String CREATE_CLIENT_ACTION = "CREATE_CLIENT";

    @GetMapping("/abandon-client/record")
    @Auth(AuthType.BUSINESS)
    public AbandonClientRecordVO getAbandonClientRecord(
            @RequestParam("id") String bookingFlowId, @RequestParam(name = "action", required = false) String action) {
        StaffPermissions staffPermissions =
                businessStaffService.getBusinessRoleByStaffId(AuthContext.get().getStaffId());
        PermissionUtil.checkStaffPermissionsInfo(staffPermissions, CAN_ACCESS_ONLINE_BOOKING_REQUEST_AND_WAIT_LIST);
        AbandonClientRecordVO recordVO = abandonRecordService.getAbandonClientRecord(bookingFlowId);
        if (CREATE_CLIENT_ACTION.equals(action)) {
            return recordVO;
        }
        boolean isHidePhoneNumber = !checkStaffPermissionsInfo(staffPermissions, VIEW_CLIENT_PHONE);
        boolean isHideEmail = !checkStaffPermissionsInfo(staffPermissions, VIEW_CLIENT_EMAIL);
        if (!isHidePhoneNumber && !isHideEmail) {
            return recordVO;
        }
        return recordVO.toBuilder()
                .customer(recordVO.customer().toBuilder()
                        .phoneNumber(
                                isHidePhoneNumber
                                        ? phoneNumberConfusion(
                                                recordVO.customer().phoneNumber())
                                        : recordVO.customer().phoneNumber())
                        .email(
                                isHideEmail
                                        ? emailConfusion(recordVO.customer().email())
                                        : recordVO.customer().email())
                        .build())
                .build();
    }

    @DeleteMapping("/abandon-client/record")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AbandonedBookingAction.DELETE,
            resourceType = ResourceType.ABANDONED_BOOKING,
            resourceId = "#bookingFlowId")
    public void deleteAbandonClientRecord(@RequestParam("id") String bookingFlowId) {
        abandonRecordService.deleteAbandonClientRecord(bookingFlowId);
    }

    @PutMapping("/abandon-client/record-pets")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AbandonedBookingAction.ADD_CLIENT,
            resourceType = ResourceType.ABANDONED_BOOKING,
            resourceId = "#bookingFlowId")
    public AbandonClientRecordVO updateAbandonClientRecordAddPets(@RequestParam("id") String bookingFlowId) {
        abandonRecordService.batchAddNewPet(bookingFlowId);
        return abandonRecordService.getAbandonClientRecord(bookingFlowId);
    }

    @GetMapping("/brief/profile/list")
    @Auth(AuthType.COMPANY)
    public List<BriefOBProfileResult.BriefOBProfileDTO> getBriefOBProfileList(AuthContext context) {
        return landingPageConfigService.getBriefOBProfileList(context.companyId());
    }
}
