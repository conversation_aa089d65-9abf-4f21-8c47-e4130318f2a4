package com.moego.server.grooming.web.params.calendar;

import com.moego.common.enums.RepeatModifyTypeEnum;
import com.moego.server.grooming.enums.calendar.CardTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Builder;

@Builder(toBuilder = true)
public record CardRescheduleParams(
        @Schema(description = "business id", hidden = true) Integer businessId,
        @Schema(description = "staff id", hidden = true) Integer tokenStaffId,
        @Schema(description = "appointment id") Integer appointmentId,
        @Schema(description = "card id") Long id,
        @Schema(description = "repeat type: 1-only this, 2-apply to upcoming, 3-apply to all")
                RepeatModifyTypeEnum repeatType,
        @Schema(description = "staff id") Integer staffId,
        @Schema(description = "start date") String startDate,
        @Schema(description = "start time") Integer startTime,
        @Schema(description = "end time") Integer endTime,
        @Schema(description = "card type, 1 - appointment, 2 - service, 3 - operation, 4 - block")
                CardTypeEnum cardType,
        @Schema(description = "pet detail ids") List<Integer> petDetailIds) {}
