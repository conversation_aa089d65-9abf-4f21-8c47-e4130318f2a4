package com.moego.server.grooming.web.params.waitlist;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.LocalDate;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class AddWaitListParam {
    @Schema(description = "business id", hidden = true)
    Long businessId;

    @Schema(description = "company id", hidden = true)
    Long companyId;

    @Schema(description = "token staff id", hidden = true)
    Long tokenStaffId;

    @NotNull
    @Positive
    private Integer customerId;

    private List<@Valid WaitListPetServiceParam> petServices;

    private Boolean allPetsStartAtSameTime;

    @Valid
    private DatePreference datePreference;

    @Valid
    private TimePreference timePreference;

    @Valid
    private StaffPreference staffPreference;

    @Schema(description = "valid from, YYYY-MM-DD")
    private LocalDate validFrom;

    @Schema(description = "valid till, YYYY-MM-DD")
    private LocalDate validTill;

    private String ticketComment;
}
