package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingNoteHistory;

public interface MoeGroomingNoteHistoryMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note_history
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note_history
     *
     * @mbg.generated
     */
    int insert(MoeGroomingNoteHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note_history
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingNoteHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note_history
     *
     * @mbg.generated
     */
    MoeGroomingNoteHistory selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note_history
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingNoteHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note_history
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeGroomingNoteHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note_history
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingNoteHistory record);
}
