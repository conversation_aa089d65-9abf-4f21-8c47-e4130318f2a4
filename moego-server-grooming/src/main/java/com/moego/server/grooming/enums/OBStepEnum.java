package com.moego.server.grooming.enums;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * An enum of each step of the new online booking
 *
 * <p> 这个类应该放到 grooming api 模块，因为 DTO 也会用到。
 *
 * <AUTHOR>
 * @since 2023/5/10
 */
@Getter
@RequiredArgsConstructor
public enum OBStepEnum {
    welcome_page(1, true),
    basic_info(2, true),
    select_care_type(3, false),
    select_address(4, false),
    select_pet(5, true),
    select_date(6, false),
    select_service(7, true),
    select_groomer(8, false),
    select_time(9, false),
    additional_pet_info(10, false),
    personal_info(11, false),
    card_on_file(12, false),
    prepay(13, false),
    pre_auth(14, false),
    submit_appt(99, true);

    /**
     * The order of the current step, the smaller the value, the higher the order
     */
    private final int order;

    /**
     * Whether the current step is required
     */
    private final boolean required;

    /**
     * Get all steps before the current step
     *
     * @param currentStep current step
     * @return next step list
     */
    public static List<OBStepEnum> listAllStepsBeforeStep(OBStepEnum currentStep) {
        List<OBStepEnum> nextSteps = new ArrayList<>();
        for (OBStepEnum step : OBStepEnum.values()) {
            if (step.getOrder() < currentStep.getOrder()) {
                nextSteps.add(step);
            }
        }
        return nextSteps;
    }

    public static List<OBStepEnum> listRecoverableSteps() {
        return List.of(
                select_address,
                select_pet,
                select_service,
                select_date,
                select_groomer,
                select_time,
                additional_pet_info,
                personal_info,
                card_on_file,
                prepay,
                pre_auth);
    }

    public static List<String> listRecoverableStepsString() {
        return List.of(
                select_address.name().toLowerCase(),
                select_pet.name().toLowerCase(),
                select_service.name().toLowerCase(),
                select_date.name().toLowerCase(),
                select_groomer.name().toLowerCase(),
                select_time.name().toLowerCase(),
                additional_pet_info.name().toLowerCase(),
                personal_info.name().toLowerCase(),
                card_on_file.name().toLowerCase(),
                prepay.name().toLowerCase(),
                pre_auth.name().toLowerCase());
    }
}
