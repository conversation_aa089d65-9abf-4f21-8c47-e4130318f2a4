package com.moego.server.grooming.server;

import static com.moego.common.utils.CommonUtil.isNormal;

import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.server.grooming.api.IBookOnlineDepositServiceBase;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.dto.BookOnlineDepositDetailDTO;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import com.moego.server.grooming.mapstruct.BookOnlineDepositMapper;
import com.moego.server.grooming.params.MoeBookOnlineDepositBatchQueryByCompanyVO;
import com.moego.server.grooming.params.MoeBookOnlineDepositBatchQueryVO;
import com.moego.server.grooming.params.MoeBookOnlineDepositVO;
import com.moego.server.grooming.service.MoeBookOnlineDepositService;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.dto.PaymentDTO;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequiredArgsConstructor
public class BookOnlineDepositServer extends IBookOnlineDepositServiceBase {

    private final MoeBookOnlineDepositService bookOnlineDepositService;
    private final IPaymentPaymentClient paymentClient;
    private final BusinessInfoHelper businessInfoHelper;
    private final CompanyHelper companyHelper;

    @Override
    public Boolean createOrUpdateOBDeposit(@RequestBody MoeBookOnlineDepositVO depositVo) {
        if ((depositVo.getCompanyId() == null || depositVo.getCompanyId().equals(0L))
                && depositVo.getBusinessId() != null
                && depositVo.getBusinessId() > 0) {
            depositVo.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(depositVo.getBusinessId()));
        }
        return bookOnlineDepositService.createOrUpdateDepositByGuid(depositVo) > 0;
    }

    @Override
    public Boolean updateOBDepositByPaymentId(@RequestBody MoeBookOnlineDepositVO depositVo) {
        return bookOnlineDepositService.updateDepositPaidByPaymentId(depositVo) > 0;
    }

    @Override
    public BookOnlineDepositDTO getOBDepositByPaymentId(
            @RequestParam("businessId") Integer businessId, @RequestParam("paymentId") Integer paymentId) {
        return bookOnlineDepositService.getOBDepositByPaymentId(businessId, paymentId);
    }

    @Override
    public List<BookOnlineDepositDTO> getOBDepositByPaymentIds(Integer businessId, Set<Integer> paymentIds) {
        return bookOnlineDepositService.getOBDepositByPaymentIds(businessId, paymentIds);
    }

    @Override
    public List<BookOnlineDepositDTO> getOBDepositByPaymentIdsV2(MoeBookOnlineDepositBatchQueryByCompanyVO req) {
        return bookOnlineDepositService.getOBDepositByPaymentIdsV2(req.getCompanyId(), req.getPaymentIds());
    }

    @Override
    public List<BookOnlineDepositDTO> getOBDepositByPaymentIds(MoeBookOnlineDepositBatchQueryVO req) {
        return bookOnlineDepositService.getOBDepositByPaymentIds(req.getBusinessId(), req.getPaymentIds());
    }

    @Override
    public BookOnlineDepositDTO getOBDepositByGroomingId(Integer businessId, Integer groomingId) {
        MoeBookOnlineDeposit deposit = bookOnlineDepositService.getOBDepositByGroomingId(businessId, groomingId);
        if (deposit == null) {
            return null;
        }
        BookOnlineDepositDTO depositDTO = new BookOnlineDepositDTO();
        BeanUtils.copyProperties(deposit, depositDTO);
        return depositDTO;
    }

    @Override
    public BookOnlineDepositDTO getOBDepositByBookingRequestId(Integer businessId, long bookingRequestId) {
        return CollectionUtils.firstElement(listOBDepositByBookingRequestIds(businessId, List.of(bookingRequestId)));
    }

    @Override
    public List<BookOnlineDepositDTO> listOBDepositByBookingRequestIds(
            Integer businessId, Collection<Long> bookingRequestIds) {
        if (ObjectUtils.isEmpty(bookingRequestIds)) {
            return List.of();
        }
        return bookOnlineDepositService.listOBDepositByBookingRequestIds(businessId, bookingRequestIds).stream()
                .map(BookOnlineDepositMapper.INSTANCE::entityToDto)
                .toList();
    }

    @Override
    public List<BookOnlineDepositDTO> getOBDepositByGroomingIds(Collection<Integer> groomingIds) {
        return bookOnlineDepositService.getOBDepositByGroomingIdsV2(new HashSet<>(groomingIds)).stream()
                .map(BookOnlineDepositMapper.INSTANCE::entityToDto)
                .toList();
    }

    @Override
    public Boolean updateOBDepositByGroomingId(MoeBookOnlineDepositVO depositVo) {
        return bookOnlineDepositService.updateDepositPaidByGroomingId(depositVo) > 0;
    }

    @Override
    public BookOnlineDepositDetailDTO getOBDepositWithPaymentByGroomingId(Integer businessId, Integer groomingId) {
        MoeBookOnlineDeposit deposit = bookOnlineDepositService.getOBDepositByGroomingId(businessId, groomingId);
        if (Objects.isNull(deposit)) {
            return null;
        }
        BookOnlineDepositDTO depositDTO = BookOnlineDepositMapper.INSTANCE.entityToDto(deposit);
        BookOnlineDepositDetailDTO dto = new BookOnlineDepositDetailDTO().setDeposit(depositDTO);
        if (StringUtils.hasText(depositDTO.getGuid())
                && DepositPaymentTypeEnum.PrePay.equals(depositDTO.getDepositType())) {
            PaymentDTO paymentDTO = paymentClient.getPaymentMethodById(depositDTO.getPaymentId());
            dto.setPaymentDTO(paymentDTO);
        }
        return dto;
    }

    @Override
    public Integer migrateDiscountCodeIds(Map<Long, Long> discountCodeIdMap) {
        return bookOnlineDepositService.migrateDiscountCodeIds(discountCodeIdMap);
    }

    @Nullable
    @Override
    public BookOnlineDepositDTO getByGuid(String guid) {
        var deposit = bookOnlineDepositService.getOBDepositByDeGuid(guid);
        return BookOnlineDepositMapper.INSTANCE.entityToDto(deposit);
    }

    @Override
    public int insert(InsertParam param) {

        var insertBean = BookOnlineDepositMapper.INSTANCE.insertParamToEntity(param);

        if (!isNormal(insertBean.getCompanyId())) {
            insertBean.setCompanyId(companyHelper.mustGetCompanyId(insertBean.getBusinessId()));
        }

        return bookOnlineDepositService.insert(insertBean);
    }

    @Override
    public int update(UpdateParam param) {
        var updateBean = BookOnlineDepositMapper.INSTANCE.updateParamToEntity(param);
        return bookOnlineDepositService.update(updateBean);
    }
}
