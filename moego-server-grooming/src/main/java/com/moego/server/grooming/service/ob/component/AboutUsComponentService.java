package com.moego.server.grooming.service.ob.component;

import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.web.vo.ob.component.BaseComponentVO;
import com.moego.server.grooming.web.vo.ob.component.TextComponentVO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@Service(value = LandingPageComponentEnum.COMPONENT_ABOUT_US)
public class AboutUsComponentService implements ILandingPageComponentService {

    @Override
    public BaseComponentVO getPageComponent(MoeBookOnlineLandingPageConfig landingPageConfig) {
        TextComponentVO textComponentVO = new TextComponentVO();
        textComponentVO.setText(landingPageConfig.getAboutUs());
        textComponentVO.setComponent(LandingPageComponentEnum.ABOUT_US.getComponent());
        return textComponentVO;
    }
}
