package com.moego.server.grooming.helper;

import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ListServiceRequest;
import com.moego.idl.service.offering.v1.ListServiceResponse;
import com.moego.idl.service.offering.v1.LodgingUnitServiceGrpc;
import com.moego.idl.service.offering.v1.MGetLodgingUnitRequest;
import com.moego.idl.service.offering.v1.MGetLodgingUnitResponse;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@RequiredArgsConstructor
public class OfferingHelper {
    private final LodgingUnitServiceGrpc.LodgingUnitServiceBlockingStub lodgingUnitClient;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceClient;

    public AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>> getLodgingMap(
            List<Long> lodgingIds) {
        lodgingIds =
                lodgingIds.stream().filter(k -> k != null && k > 0).distinct().toList();
        if (CollectionUtils.isEmpty(lodgingIds)) {
            return new AbstractMap.SimpleEntry<>(Map.of(), Map.of());
        }

        MGetLodgingUnitResponse lodgingUnitResponse = lodgingUnitClient.mGetLodgingUnit(
                MGetLodgingUnitRequest.newBuilder().addAllIdList(lodgingIds).build());
        Map<Long, LodgingUnitModel> lodgingUnitMap = lodgingUnitResponse.getLodgingUnitListList().stream()
                .collect(Collectors.toMap(LodgingUnitModel::getId, Function.identity()));
        Map<Long, LodgingTypeModel> lodgingTypeMap = lodgingUnitResponse.getLodgingTypeListList().stream()
                .collect(Collectors.toMap(LodgingTypeModel::getId, Function.identity()));
        return new AbstractMap.SimpleEntry<>(lodgingUnitMap, lodgingTypeMap);
    }

    public Map<Integer, ServiceBriefView> getServiceMap(List<Integer> serviceIds) {
        List<Long> longServiceIds = serviceIds.stream()
                .filter(k -> k != null && k > 0)
                .distinct()
                .map(Integer::longValue)
                .toList();
        if (CollectionUtils.isEmpty(longServiceIds)) {
            return Map.of();
        }
        return serviceClient
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .addAllServiceIds(longServiceIds)
                        .build())
                .getServicesList()
                .stream()
                .collect(Collectors.toMap(k -> (int) k.getId(), Function.identity(), (k1, k2) -> k1));
    }

    public Map<Integer, String> getServiceNameMap(List<Integer> serviceIds) {
        return getServiceMap(serviceIds).entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, k -> k.getValue().getName()));
    }

    public List<ServiceModel> fetchAllGroomingService(long companyId, long businessId) {
        List<ServiceModel> serviceModels = new ArrayList<>();
        fetchAllGroomingService(companyId, businessId, 1, 1000, serviceModels);
        return serviceModels;
    }

    private void fetchAllGroomingService(
            long companyId, long businessId, int pageNum, int pageSize, List<ServiceModel> serviceModels) {
        ListServiceResponse listServiceResponse = serviceClient.listService(ListServiceRequest.newBuilder()
                .setPagination(PaginationRequest.newBuilder()
                        .setPageNum(pageNum)
                        .setPageSize(pageSize)
                        .build())
                .addServiceItemTypes(ServiceItemType.GROOMING)
                .addBusinessIds(businessId)
                .setTokenCompanyId(companyId)
                .setInactive(false)
                .build());
        if (CollectionUtils.isEmpty(listServiceResponse.getServicesList())) {
            return;
        }
        serviceModels.addAll(listServiceResponse.getServicesList());

        if (listServiceResponse.getPagination().getTotal() > pageNum * pageSize) {
            fetchAllGroomingService(companyId, businessId, pageNum + 1, pageSize, serviceModels);
        }
    }
}
