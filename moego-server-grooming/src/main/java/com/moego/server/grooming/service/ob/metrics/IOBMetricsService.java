package com.moego.server.grooming.service.ob.metrics;

import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;

/**
 * <AUTHOR>
 * @since 2023/5/22
 */
public interface IOBMetricsService {
    /**
     * The total value of the current period
     *
     * @param timeRangeDTO time range
     * @return total value
     */
    Object sumMetrics(OBMetricTimeRangeDTO timeRangeDTO);

    /**
     * Proportion of the current stage to the previous stage
     *
     * @param timeRangeDTO time range
     * @return proportion
     */
    Object proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO);

    /**
     * Get metrics name
     *
     * @return metrics name
     */
    OBMetricsEnum getMetricsName();
}
