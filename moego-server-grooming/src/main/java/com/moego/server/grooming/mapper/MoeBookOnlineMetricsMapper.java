package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlineMetrics;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeBookOnlineMetricsMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_metrics
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_metrics
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlineMetrics record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_metrics
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlineMetrics record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_metrics
     *
     * @mbg.generated
     */
    MoeBookOnlineMetrics selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_metrics
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlineMetrics record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_metrics
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlineMetrics record);

    int batchUpsert(List<MoeBookOnlineMetrics> list);

    MoeBookOnlineMetrics selectByCondition(
            @Param("businessId") int businessId,
            @Param("metricName") String metricName,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);
}
