package com.moego.server.grooming.mapper;

import com.moego.common.dto.clients.BusinessDateClientsDTO;
import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.common.params.PageQuery;
import com.moego.lib.common.auth.AuthContext;
import com.moego.server.business.dto.AppointmentPetIdDTO;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.grooming.dto.AppointmentDateWithServiceDurationDto;
import com.moego.server.grooming.dto.AppointmentReminderSendDTO;
import com.moego.server.grooming.dto.AppointmentWithPetDetailsDto;
import com.moego.server.grooming.dto.BookOnlineAutoMoveAppointmentDTO;
import com.moego.server.grooming.dto.CustomerApptCountDTO;
import com.moego.server.grooming.dto.CustomerApptDateDTO;
import com.moego.server.grooming.dto.CustomerGrooming;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentDTO;
import com.moego.server.grooming.dto.CustomerStatisticsDTO;
import com.moego.server.grooming.dto.CustomerUpComingAppointDTO;
import com.moego.server.grooming.dto.GroomingAppointmentWaitingListDTO;
import com.moego.server.grooming.dto.GroomingBookingDTO;
import com.moego.server.grooming.dto.GroomingIdPetIdDto;
import com.moego.server.grooming.dto.PetBreedInfoDTO;
import com.moego.server.grooming.dto.RepeatAppointmentDto;
import com.moego.server.grooming.dto.RepeatNumDTO;
import com.moego.server.grooming.dto.StaffBlockInfoDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.WaitListStatusEnum;
import com.moego.server.grooming.mapper.po.BusinessCountPO;
import com.moego.server.grooming.mapper.po.CountUpcomingApptByCustomerIdsPO;
import com.moego.server.grooming.mapper.po.GetLatestAppointmentDatePO;
import com.moego.server.grooming.mapper.po.LongestWorkingDayPo;
import com.moego.server.grooming.mapper.po.WebsiteAppointmentSummaryPO;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointmentExample;
import com.moego.server.grooming.params.GetInvoiceIdsAppointmentDateBetweenParam;
import com.moego.server.grooming.params.report.DescribeAppointmentReportsParams;
import com.moego.server.grooming.params.report.ScanAppointmentReportsParams;
import com.moego.server.grooming.service.dto.CustomerIdCountResultDto;
import com.moego.server.grooming.service.dto.GroomingReportApptDetail;
import com.moego.server.grooming.service.dto.GroomingReportWebAppointment;
import com.moego.server.grooming.service.dto.QbQueryGroomingResultDto;
import com.moego.server.grooming.service.dto.ReportAppointmentDAO;
import com.moego.server.grooming.service.dto.client.ListCommonApptDTO;
import com.moego.server.grooming.service.dto.client.ListUpcomingApptDTO;
import com.moego.server.grooming.service.dto.ob.OBClientApptDTO;
import com.moego.server.grooming.web.vo.GroomingBookingQueryVO;
import com.moego.server.grooming.web.vo.GroomingCustomerQueryVO;
import com.moego.server.grooming.web.vo.WaitingPetDetailVO;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * AppointmentMapperProxy - 代理类用于白名单和非白名单商家的分流调用
 * 白名单商家使用 fulfillment 远程调用，非白名单商家使用原有的 mapper 调用
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AppointmentMapperProxy {

    private final MoeGroomingAppointmentMapper appointmentMapper;
    // TODO: 注入 fulfillment 远程服务
    // private final FulfillmentAppointmentService fulfillmentAppointmentService;

    /**
     * 判断是否为白名单商家，使用新的 fulfillment flow
     * 直接从 AuthContext 获取 companyId
     * TODO: 实现具体的白名单判断逻辑
     *
     * @return true if company is in fulfillment flow whitelist
     */
    private boolean isFulfillmentFlow() {
        //        Long companyId = AuthContext.get().companyId();
        //        if (companyId == null) {
        //            log.warn("CompanyId is null in AuthContext, using original mapper");
        //            return false;
        //        }
        // TODO: 实现白名单判断逻辑
        return false;
    }

    // ==================== MyBatis Generator 生成的基础方法 ====================

    public int insertSelective(MoeGroomingAppointment record) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for insertSelective, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.insertSelective(record);
    }

    public List<MoeGroomingAppointment> selectByExample(MoeGroomingAppointmentExample example) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectByExample, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectByExample(example);
    }

    public MoeGroomingAppointment selectByPrimaryKey(Integer id) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectByPrimaryKey, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectByPrimaryKey(id);
    }

    public int updateByExampleSelective(MoeGroomingAppointment record, MoeGroomingAppointmentExample example) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for updateByExampleSelective, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.updateByExampleSelective(record, example);
    }

    public int updateByPrimaryKeySelective(MoeGroomingAppointment record) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for updateByPrimaryKeySelective, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.updateByPrimaryKeySelective(record);
    }

    public int updateByPrimaryKey(MoeGroomingAppointment record) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for updateByPrimaryKey, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.updateByPrimaryKey(record);
    }

    // ==================== 自定义方法 ====================

    public MoeGroomingAppointment selectByPrimaryKeyAndBusinessId(Integer id, Integer businessId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectByPrimaryKeyAndBusinessId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectByPrimaryKeyAndBusinessId(id, businessId);
    }

    public List<MoeGroomingAppointment> selectByIdListAndBusinessId(List<Integer> idList, Integer businessId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectByIdListAndBusinessId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectByIdListAndBusinessId(idList, businessId);
    }

    public List<MoeGroomingAppointment> selectByIdList(List<Integer> idList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectByIdList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectByIdList(idList);
    }

    public List<StaffBlockInfoDTO> selectBlockByApptDates(Integer businessId, Set<String> appointmentDates) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectBlockByApptDates, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectBlockByApptDates(businessId, appointmentDates);
    }

    public List<StaffBlockInfoDTO> selectBlockListByDatesAndStaffIds(
            Integer businessId, Set<String> appointmentDates, Set<Integer> staffIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectBlockListByDatesAndStaffIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectBlockListByDatesAndStaffIds(businessId, appointmentDates, staffIds);
    }

    public List<MoeGroomingAppointment> queryApptsByRepeatId(
            Integer businessId, Integer repeatId, String appointmentDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryApptsByRepeatId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryApptsByRepeatId(businessId, repeatId, appointmentDate);
    }

    public List<MoeGroomingAppointment> queryBlocksByRepeatId(
            Integer tokenBusinessId, Integer repeatId, String appointmentDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryBlocksByRepeatId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryBlocksByRepeatId(tokenBusinessId, repeatId, appointmentDate);
    }

    public List<MoeGroomingAppointment> selectBusinessCustomerIdByApptId(List<Integer> ids, Integer businessId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectBusinessCustomerIdByApptId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectBusinessCustomerIdByApptId(ids, businessId);
    }

    public List<BookOnlineAutoMoveAppointmentDTO> queryAllBookingAutoMoveAppointment(Integer scrollId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryAllBookingAutoMoveAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryAllBookingAutoMoveAppointment(scrollId);
    }

    public Integer deleteAppointments(List<Integer> ids, Integer businessId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for deleteAppointments, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.deleteAppointments(ids, businessId);
    }

    public List<CustomerGrooming> queryGroomingCustomerAppointmentNum(GroomingCustomerQueryVO groomingCustomerQueryVO) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryGroomingCustomerAppointmentNum, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryGroomingCustomerAppointmentNum(groomingCustomerQueryVO);
    }

    public List<GroomingBookingDTO> queryGroomingBookingAppointment(GroomingBookingQueryVO groomingCustomerQueryVO) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryGroomingBookingAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryGroomingBookingAppointment(groomingCustomerQueryVO);
    }

    public List<GroomingBookingDTO> queryGroomingBookingAppointmentJoin(List<Integer> groomingIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryGroomingBookingAppointmentJoin, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryGroomingBookingAppointmentJoin(groomingIds);
    }

    public Integer queryAppointmentCount(Integer businessId, Byte bookOnlineStatus, Integer queryType) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryAppointmentCount, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryAppointmentCount(businessId, bookOnlineStatus, queryType);
    }

    public Integer queryAppointmentCountRange(Integer businessId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryAppointmentCountRange, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryAppointmentCountRange(businessId, startDate, endDate);
    }

    public List<GroomingAppointmentWaitingListDTO> queryAppointmentWaitingList(WaitingPetDetailVO waitingPetDetailVO) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryAppointmentWaitingList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryAppointmentWaitingList(waitingPetDetailVO);
    }

    public CustomerGroomingAppointmentDTO queryCustomerLastOrNextAppointment(
            Long companyId,
            Integer customerId,
            String date,
            Integer endTimes,
            Integer ignoreGroomingId,
            Boolean last,
            boolean excludeBookingRequest) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryCustomerLastOrNextAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryCustomerLastOrNextAppointment(
                companyId, customerId, date, endTimes, ignoreGroomingId, last, excludeBookingRequest);
    }

    public CustomerGroomingAppointmentDTO queryCustomerLastOrNextAppointmentAllServiceType(
            Long companyId,
            Integer customerId,
            String date,
            Integer endTimes,
            Integer ignoreGroomingId,
            Boolean last,
            boolean excludeBookingRequest) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryCustomerLastOrNextAppointmentAllServiceType, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryCustomerLastOrNextAppointmentAllServiceType(
                companyId, customerId, date, endTimes, ignoreGroomingId, last, excludeBookingRequest);
    }

    public OBClientApptDTO getLastFinishedApptByCustomerId(
            Long companyId, Integer customerId, String date, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getLastFinishedApptByCustomerId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getLastFinishedApptByCustomerId(companyId, customerId, date, endTimes);
    }

    public List<CustomerGroomingAppointmentDTO> selectCustomerLastAppt(
            List<Integer> customerIdList, Long companyId, String date, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectCustomerLastAppt, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectCustomerLastAppt(customerIdList, companyId, date, endTimes);
    }

    public List<CustomerGroomingAppointmentDTO> selectCustomerLastFinishedAppt(
            List<Integer> customerIdList, Long companyId, String date, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectCustomerLastFinishedAppt, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectCustomerLastFinishedAppt(customerIdList, companyId, date, endTimes);
    }

    public List<CustomerGroomingAppointmentDTO> selectCustomerLastTwoUncanceledApptDate(
            Integer customerId, Integer businessId, String date, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectCustomerLastTwoUncanceledApptDate, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectCustomerLastTwoUncanceledApptDate(customerId, businessId, date, endTimes);
    }

    public List<Integer> getCustomerIdsWithUncancelledRepeat(Integer businessId, Set<Integer> customerId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getCustomerIdsWithUncancelledRepeat, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getCustomerIdsWithUncancelledRepeat(businessId, customerId);
    }

    public Integer countCustomerIdsWithUncancelledRepeat(Integer businessId, Set<Integer> customerId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for countCustomerIdsWithUncancelledRepeat, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.countCustomerIdsWithUncancelledRepeat(businessId, customerId);
    }

    public Integer countCustomerIdsWithUncancelledRepeatByCompanyId(Long companyId, Set<Integer> customerId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for countCustomerIdsWithUncancelledRepeatByCompanyId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.countCustomerIdsWithUncancelledRepeatByCompanyId(companyId, customerId);
    }

    public List<CustomerGroomingAppointmentDTO> selectLapsedCustomerLastAppt(
            List<Integer> customerIdList, Long companyId, Integer businessId, String date, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectLapsedCustomerLastAppt, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectLapsedCustomerLastAppt(customerIdList, companyId, businessId, date, endTimes);
    }

    public List<CustomerGroomingAppointmentDTO> selectCustomerNextAppt(
            List<Integer> customerIdList, Long companyId, String date, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectCustomerNextAppt, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectCustomerNextAppt(customerIdList, companyId, date, endTimes);
    }

    public CustomerGroomingAppointmentDTO selectCustomerPetNextAppt(
            Long companyId,
            Integer customerId,
            Integer petId,
            Integer ignoredGroomingId,
            String fromDate,
            Integer fromMinutes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectCustomerPetNextAppt, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectCustomerPetNextAppt(
                companyId, customerId, petId, ignoredGroomingId, fromDate, fromMinutes);
    }

    public List<CustomerStatisticsDTO> getCustomerAppointmentNumBatch(
            List<Integer> customerIds, String date, Integer endTimes, Long companyId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getCustomerAppointmentNumBatch, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getCustomerAppointmentNumBatch(customerIds, date, endTimes, companyId);
    }

    public List<Integer> queryCustomerIdsInWaiting(List<Integer> customerIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryCustomerIdsInWaiting, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryCustomerIdsInWaiting(customerIds);
    }

    public List<Integer> queryStaffUpComingAppointCount(
            Integer businessId, Integer staffId, String appointmentDate, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryStaffUpComingAppointCount, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryStaffUpComingAppointCount(businessId, staffId, appointmentDate, endTimes);
    }

    public List<Integer> queryServiceUpComingAppointCount(
            Integer businessId, Integer serviceId, String appointmentDate, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryServiceUpComingAppointCount, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryServiceUpComingAppointCount(businessId, serviceId, appointmentDate, endTimes);
    }

    public List<TimeRangeDto> queryStaffAppointTimeByDate(Integer businessId, String appointmentDate, Integer staffId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryStaffAppointTimeByDate, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryStaffAppointTimeByDate(businessId, appointmentDate, staffId);
    }

    public List<CustomerGrooming> queryGroomingCustomerAppointment(List<Integer> appointmentIds, Integer orderType) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryGroomingCustomerAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryGroomingCustomerAppointment(appointmentIds, orderType);
    }

    public List<CustomerUpComingAppointDTO> queryCustomerUpComingAppoint(
            Integer customerId, Integer businessId, String appointmentDate, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryCustomerUpComingAppoint, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryCustomerUpComingAppoint(customerId, businessId, appointmentDate, endTimes);
    }

    public List<CustomerUpComingAppointDTO> queryCustomerUpComingAppointForClientShare(
            Integer customerId,
            Integer companyId,
            List<Integer> apptIds,
            String startDate,
            Integer startMins,
            Integer status,
            String endDateStr) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryCustomerUpComingAppointForClientShare, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryCustomerUpComingAppointForClientShare(
                customerId, companyId, apptIds, startDate, startMins, status, endDateStr);
    }

    public List<CustomerGrooming> queryGroomingCustomerAppointmentByIds(List<Integer> ids) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryGroomingCustomerAppointmentByIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryGroomingCustomerAppointmentByIds(ids);
    }

    public List<Integer> queryFutureAppointmentCustomerIdList(
            Integer businessId, String appointmentDate, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryFutureAppointmentCustomerIdList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryFutureAppointmentCustomerIdList(businessId, appointmentDate, endTimes);
    }

    public List<Integer> queryFutureAppointmentCustomerIdListByCompany(
            Long companyId, String appointmentDate, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryFutureAppointmentCustomerIdListByCompany, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryFutureAppointmentCustomerIdListByCompany(companyId, appointmentDate, endTimes);
    }

    public List<MoeGroomingAppointment> selectBusinessRepeatAppointmentReminder(
            Integer businessId, List<Integer> dismissIds, List<Integer> repeatIds, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectBusinessRepeatAppointmentReminder, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectBusinessRepeatAppointmentReminder(
                businessId, dismissIds, repeatIds, startDate, endDate);
    }

    public List<MoeGroomingAppointment> selectBusinessAppointmentReminder(
            Integer businessId,
            List<Integer> dismissIds,
            String startDay,
            String endDay,
            Integer startMinute,
            Integer status) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectBusinessAppointmentReminder, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectBusinessAppointmentReminder(
                businessId, dismissIds, startDay, endDay, startMinute, status);
    }

    public Integer customerDelete(Long companyId, Integer customerId, Integer staffId, Long nowTime) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for customerDelete, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.customerDelete(companyId, customerId, staffId, nowTime);
    }

    public List<MoeGroomingAppointment> selectCustomerDeleteAppt(Long companyId, Integer customerId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectCustomerDeleteAppt, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectCustomerDeleteAppt(companyId, customerId);
    }

    public Integer selectHasUpcomingService(
            String appointmentDate, Integer endTimes, Integer businessId, Integer serviceId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectHasUpcomingService, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectHasUpcomingService(appointmentDate, endTimes, businessId, serviceId);
    }

    public Integer countRepeatUpcomingCountForExpiryReminder(
            Integer businessId, Integer repeatId, String currentDate, Integer currentTime) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for countRepeatUpcomingCountForExpiryReminder, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.countRepeatUpcomingCountForExpiryReminder(
                businessId, repeatId, currentDate, currentTime);
    }

    public List<RepeatAppointmentDto> selectRepeatAppointmentList(
            Integer businessId, Integer repeatId, List<AppointmentStatusEnum> statusList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectRepeatAppointmentList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectRepeatAppointmentList(businessId, repeatId, statusList);
    }

    public List<RepeatAppointmentDto> selectRepeatAppointmentListByType(
            Integer businessId,
            Integer repeatId,
            List<AppointmentStatusEnum> statusList,
            String currentDate,
            Integer currentTime,
            Byte type) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectRepeatAppointmentListByType, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectRepeatAppointmentListByType(
                businessId, repeatId, statusList, currentDate, currentTime, type);
    }

    public RepeatAppointmentDto selectRepeatAppointmentById(Integer businessId, Integer appointmentId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectRepeatAppointmentById, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectRepeatAppointmentById(businessId, appointmentId);
    }

    public List<CustomerUpComingAppointDTO> queryBusinessUpComingAppoint(
            Integer customerId, Integer businessId, String appointmentDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryBusinessUpComingAppoint, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryBusinessUpComingAppoint(customerId, businessId, appointmentDate);
    }

    public Integer getLatestClientUpcomingAppointId(
            Set<Integer> businessIds, Set<Integer> customerIds, String appointmentDate, Integer endTime) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getLatestClientUpcomingAppointId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getLatestClientUpcomingAppointId(businessIds, customerIds, appointmentDate, endTime);
    }

    public List<CustomerUpComingAppointDTO> queryCustomerUpcomingAppoint(
            Set<Integer> businessIds, Set<Integer> customerIds, String appointmentDate, Integer endTime) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryCustomerUpcomingAppoint, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryCustomerUpcomingAppoint(businessIds, customerIds, appointmentDate, endTime);
    }

    public CustomerUpComingAppointDTO queryCustomerUpcomingAppointById(Integer appointmentId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryCustomerUpcomingAppointById, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryCustomerUpcomingAppointById(appointmentId);
    }

    public List<Integer> selectCustomerIdWithInAllAppointmentDate(Integer businessId, Integer staffId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectCustomerIdWithInAllAppointmentDate, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectCustomerIdWithInAllAppointmentDate(businessId, staffId);
    }

    public Integer selectCustomerIdIsWithAppointmentDate(
            Integer businessId, Integer staffId, String startDate, String endDate, Integer customerId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectCustomerIdIsWithAppointmentDate, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectCustomerIdIsWithAppointmentDate(
                businessId, staffId, startDate, endDate, customerId);
    }

    public Integer selectCustomerIdIsWithAllAppointmentDate(Integer businessId, Integer staffId, Integer customerId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectCustomerIdIsWithAllAppointmentDate, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectCustomerIdIsWithAllAppointmentDate(businessId, staffId, customerId);
    }

    public List<AppointmentReminderSendDTO> queryAppointmentReminderWill(
            Integer businessId, List<Integer> dismissIds, String appointmentDate, Integer status) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryAppointmentReminderWill, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryAppointmentReminderWill(businessId, dismissIds, appointmentDate, status);
    }

    public AppointmentReminderSendDTO queryAppointmentReminderSendDto(Integer businessId, Integer groomingId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryAppointmentReminderSendDto, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryAppointmentReminderSendDto(businessId, groomingId);
    }

    @Deprecated
    public List<Integer> getBusinessIdsOnSendDaily(String dateTomorrow, String dateYesterday) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getBusinessIdsOnSendDaily, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getBusinessIdsOnSendDaily(dateTomorrow, dateYesterday);
    }

    public List<CustomerGroomingAppointmentDTO> selectCustomerLastServiceTime(
            Integer businessId, String appointmentDate, Integer endTimes, List<Integer> dismissCustomerIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectCustomerLastServiceTime, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectCustomerLastServiceTime(
                businessId, appointmentDate, endTimes, dismissCustomerIds);
    }

    public List<CustomerGroomingAppointmentDTO> selectCustomerLastServiceTimeByCompany(
            Integer companyId, String appointmentDate, Integer endTimes, List<Integer> customerIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectCustomerLastServiceTimeByCompany, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectCustomerLastServiceTimeByCompany(
                companyId, appointmentDate, endTimes, customerIds);
    }

    public CustomerGroomingAppointmentDTO selectOneCustomerLastServiceTime(
            Integer businessId, Integer customerId, String appointmentDate, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectOneCustomerLastServiceTime, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectOneCustomerLastServiceTime(businessId, customerId, appointmentDate, endTimes);
    }

    public String selectLastServiceTime(Integer businessId, Integer customerId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectLastServiceTime, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectLastServiceTime(businessId, customerId);
    }

    public List<Integer> queryRepeatIdOnlyTwoGrooming(Integer businessId, String startDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryRepeatIdOnlyTwoGrooming, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryRepeatIdOnlyTwoGrooming(businessId, startDate);
    }

    public List<RepeatNumDTO> queryRepeatLastestAppointment(Integer businessId, List<Integer> repeatIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryRepeatLastestAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryRepeatLastestAppointment(businessId, repeatIds);
    }

    public List<RepeatNumDTO> queryCustomerLastestAppointment(
            Integer businessId, List<Integer> customerIds, String startDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryCustomerLastestAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryCustomerLastestAppointment(businessId, customerIds, startDate);
    }

    public List<GroomingReportApptDetail> queryBusinessApptsAndInvoiceWithDate(
            List<Integer> businessIds, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryBusinessApptsAndInvoiceWithDate, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryBusinessApptsAndInvoiceWithDate(businessIds, startDate, endDate);
    }

    public List<ReportAppointmentDAO> queryUnpaidApptsWithAmount(Integer businessId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryUnpaidApptsWithAmount, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryUnpaidApptsWithAmount(businessId, startDate, endDate);
    }

    public List<ReportAppointmentDAO> queryUnclosedAppts(
            Integer businessId, String startDate, String endDate, String currentDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryUnclosedAppts, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryUnclosedAppts(businessId, startDate, endDate, currentDate);
    }

    public List<ReportAppointmentDAO> queryCancelledApptsWithNoShowInfo(
            Integer businessId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryCancelledApptsWithNoShowInfo, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryCancelledApptsWithNoShowInfo(businessId, startDate, endDate);
    }

    public List<ReportAppointmentDAO> queryNoShowApptsWithNoShowInfo(
            Integer businessId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryNoShowApptsWithNoShowInfo, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryNoShowApptsWithNoShowInfo(businessId, startDate, endDate);
    }

    public List<ReportAppointmentDAO> queryApptsByOnlineBook(Integer businessId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryApptsByOnlineBook, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryApptsByOnlineBook(businessId, startDate, endDate);
    }

    public List<ReportAppointmentDAO> queryUpcomingAppts(
            Integer businessId, String startDate, String endDate, String currentDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryUpcomingAppts, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryUpcomingAppts(businessId, startDate, endDate, currentDate);
    }

    public List<ReportAppointmentDAO> queryWaitListAppts(Integer businessId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryWaitListAppts, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryWaitListAppts(businessId, startDate, endDate);
    }

    public List<MoeGroomingAppointment> queryByOnlineBook(
            Integer businessId, List<AppointmentStatusEnum> statusList, Byte isWaitingList, String order) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryByOnlineBook, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryByOnlineBook(businessId, statusList, isWaitingList, order);
    }

    public List<MoeGroomingAppointment> queryWaitList(
            Long companyId,
            List<Integer> businessIdList,
            List<AppointmentStatusEnum> statusList,
            List<WaitListStatusEnum> waitListStatusList,
            List<Integer> customerIdList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryWaitList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryWaitList(
                companyId, businessIdList, statusList, waitListStatusList, customerIdList);
    }

    public List<QbQueryGroomingResultDto> queryAppointmentDateByDateRange(
            Integer businessId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryAppointmentDateByDateRange, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryAppointmentDateByDateRange(businessId, startDate, endDate);
    }

    public List<QbQueryGroomingResultDto> queryAppointmentDateByDateRangeWithStaffId(
            Integer businessId, Integer staffId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryAppointmentDateByDateRangeWithStaffId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryAppointmentDateByDateRangeWithStaffId(businessId, staffId, startDate, endDate);
    }

    public List<MoeGroomingAppointment> queryByPrimaryIds(List<Integer> appointmentIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryByPrimaryIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryByPrimaryIds(appointmentIds);
    }

    public Integer getPetLastAppointmentId(
            Long companyId, Integer businessId, Integer petId, String appointmentDate, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getPetLastAppointmentId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getPetLastAppointmentId(companyId, businessId, petId, appointmentDate, endTimes);
    }

    public List<GroomingReportWebAppointment> queryWebReportUnpaidAppts(
            Integer businessId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryWebReportUnpaidAppts, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryWebReportUnpaidAppts(businessId, startDate, endDate);
    }

    public List<GroomingReportWebAppointment> queryWebReportCancelledAppts(
            Integer businessId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryWebReportCancelledAppts, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryWebReportCancelledAppts(businessId, startDate, endDate);
    }

    public List<GroomingReportWebAppointment> queryWebReportNoShowAppts(
            Integer businessId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryWebReportNoShowAppts, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryWebReportNoShowAppts(businessId, startDate, endDate);
    }

    public List<GroomingReportWebAppointment> queryApptStaffAndCustomer(
            Integer businessId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryApptStaffAndCustomer, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryApptStaffAndCustomer(businessId, startDate, endDate);
    }

    public List<Integer> queryApptIdsByPageV2(
            List<Integer> businessIds,
            Integer staffId,
            String startDate,
            String endDate,
            Integer offset,
            Integer size) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryApptIdsByPageV2, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryApptIdsByPageV2(businessIds, staffId, startDate, endDate, offset, size);
    }

    public Integer getPayrollApptsCountV2(
            List<Integer> businessIds, Integer staffId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getPayrollApptsCountV2, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getPayrollApptsCountV2(businessIds, staffId, startDate, endDate);
    }

    public AppointmentWithPetDetailsDto getAppointmentWithPetDetails(Integer appointmentId, Boolean includeCancelled) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getAppointmentWithPetDetails, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getAppointmentWithPetDetails(appointmentId, includeCancelled);
    }

    public MoeGroomingAppointment getAppointmentById(Integer id) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getAppointmentById, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getAppointmentById(id);
    }

    public List<AppointmentWithPetDetailsDto> getAppointmentListWithPetDetails(
            Set<Integer> appointmentIds, Boolean includeCancelled) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getAppointmentListWithPetDetails, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getAppointmentListWithPetDetails(appointmentIds, includeCancelled);
    }

    public List<AppointmentWithPetDetailsDto> getRepeatedAppointmentListWithPetDetails(Set<Integer> appointmentIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getRepeatedAppointmentListWithPetDetails, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getRepeatedAppointmentListWithPetDetails(appointmentIds);
    }

    public Integer getPetCountUtilChristmas(Integer businessId, String appointmentDate, Integer endTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getPetCountUtilChristmas, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getPetCountUtilChristmas(businessId, appointmentDate, endTimes);
    }

    public Integer get2021AnnualPetCountUtilChristmas(Integer businessId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for get2021AnnualPetCountUtilChristmas, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.get2021AnnualPetCountUtilChristmas(businessId);
    }

    public Integer getBusiness2021AllServiceDuration(Integer businessId, Integer staffId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getBusiness2021AllServiceDuration, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getBusiness2021AllServiceDuration(businessId, staffId);
    }

    public AppointmentDateWithServiceDurationDto getBusiness2021LongestWorking(Integer businessId, Integer staffId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getBusiness2021LongestWorking, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getBusiness2021LongestWorking(businessId, staffId);
    }

    public List<GroomingIdPetIdDto> getBusiness2021PetIdList(Integer businessId, Integer staffId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getBusiness2021PetIdList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getBusiness2021PetIdList(businessId, staffId);
    }

    public List<GroomingReportWebAppointment> queryWebReportApptEmployee(
            Integer businessId, String startDate, String endDate, Integer staffId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryWebReportApptEmployee, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryWebReportApptEmployee(businessId, startDate, endDate, staffId);
    }

    public int updateBatchCancelByPrimaryKey(
            Integer businessId,
            Byte noShow,
            Integer cancelBy,
            Byte cancelByType,
            Long updateTime,
            Long canceledTime,
            List<Integer> idList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for updateBatchCancelByPrimaryKey, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.updateBatchCancelByPrimaryKey(
                businessId, noShow, cancelBy, cancelByType, updateTime, canceledTime, idList);
    }

    public int cancelBlockByRepeatType(Integer businessId, Integer repeatId, String appointmentDate, Long updateTime) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for cancelBlockByRepeatType, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.cancelBlockByRepeatType(businessId, repeatId, appointmentDate, updateTime);
    }

    public List<Integer> selectBlockByRepeatType(Integer businessId, Integer repeatId, String appointmentDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectBlockByRepeatType, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectBlockByRepeatType(businessId, repeatId, appointmentDate);
    }

    public List<CustomerGroomingAppointmentDTO> queryExpiryCustomerLastApptListWithOrderLimit(
            Integer businessId,
            String appointmentDate,
            Integer endTimes,
            Set<Integer> customerIdSet,
            Integer pageSize,
            Integer startNum) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryExpiryCustomerLastApptListWithOrderLimit, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryExpiryCustomerLastApptListWithOrderLimit(
                businessId, appointmentDate, endTimes, customerIdSet, pageSize, startNum);
    }

    public Integer queryExpiryCustomerLastApptListCount(
            Integer businessId, String appointmentDate, Integer endTimes, Set<Integer> customerIdSet) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryExpiryCustomerLastApptListCount, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryExpiryCustomerLastApptListCount(
                businessId, appointmentDate, endTimes, customerIdSet);
    }

    public List<CustomerIdCountResultDto> queryExpiryCustomerUpcomingCount(
            Integer businessId,
            String appointmentDate,
            Integer endTimes,
            Integer upcomingCount,
            List<Integer> dismissIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryExpiryCustomerUpcomingCount, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryExpiryCustomerUpcomingCount(
                businessId, appointmentDate, endTimes, upcomingCount, dismissIds);
    }

    public List<GroomingBookingDTO> queryAppointmentListBetweenTwoTime(
            Integer businessId,
            Integer staffId,
            String startAppointmentDate,
            Integer startMins,
            String endAppointmentDate,
            Integer endMins) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryAppointmentListBetweenTwoTime, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryAppointmentListBetweenTwoTime(
                businessId, staffId, startAppointmentDate, startMins, endAppointmentDate, endMins);
    }

    public List<Integer> queryUpcomingApptIdList(
            Integer businessId, String appointmentDate, Integer endTimes, Integer customerId, List<Byte> statusList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryUpcomingApptIdList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryUpcomingApptIdList(businessId, appointmentDate, endTimes, customerId, statusList);
    }

    public List<Integer> queryCustomerUpcomingApptIdList(
            Integer businessId, String appointmentDate, Integer endTimes, Integer customerId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryCustomerUpcomingApptIdList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryCustomerUpcomingApptIdList(businessId, appointmentDate, endTimes, customerId);
    }

    public List<StaffBlockInfoDTO> selectBlockByDateRange(Integer businessId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectBlockByDateRange, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.selectBlockByDateRange(businessId, startDate, endDate);
    }

    public List<PetBreedInfoDTO> queryPetBreed(Integer businessId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryPetBreed, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryPetBreed(businessId, startDate, endDate);
    }

    public List<MoeGroomingAppointment> getAppointmentsWithin(
            Integer businessId, String startDate, Integer startMinutes, String endDate, Integer endMinutes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getAppointmentsWithin, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getAppointmentsWithin(businessId, startDate, startMinutes, endDate, endMinutes);
    }

    public List<Integer> getNotCanceledAppointmentIds(
            Integer businessId, Integer staffId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getNotCanceledAppointmentIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getNotCanceledAppointmentIds(businessId, staffId, startDate, endDate);
    }

    public MoeGroomingAppointment getLastedNotCanceledAppointment(Integer businessId, Integer customerId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getLastedNotCanceledAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getLastedNotCanceledAppointment(businessId, customerId);
    }

    public List<AppointmentPetIdDTO> queryStaffAppointmentPetIdByTime(
            Integer businessId, Set<String> appointmentDates, Set<Integer> startTimes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryStaffAppointmentPetIdByTime, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryStaffAppointmentPetIdByTime(businessId, appointmentDates, startTimes);
    }

    public List<Integer> queryCustomerFinishApptIds(Long companyId, List<Integer> customerIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryCustomerFinishApptIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryCustomerFinishApptIds(companyId, customerIds);
    }

    public Integer getServiceDuration(Integer businessId, Integer staffId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getServiceDuration, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getServiceDuration(businessId, staffId, startDate, endDate);
    }

    public LongestWorkingDayPo getLongestWorkingDay(
            Integer businessId, Integer staffId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getLongestWorkingDay, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getLongestWorkingDay(businessId, staffId, startDate, endDate);
    }

    public int getOnlineBookingCount(Integer businessId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getOnlineBookingCount, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getOnlineBookingCount(businessId, startDate, endDate);
    }

    public List<Integer> getGroomedPetIds(Integer businessId, Integer staffId, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getGroomedPetIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getGroomedPetIds(businessId, staffId, startDate, endDate);
    }

    public List<MoeGroomingAppointment> listUpcomingAppt(
            List<ListUpcomingApptDTO> upcomingApptDTOList, PageQuery pageQuery) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listUpcomingAppt, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.listUpcomingAppt(upcomingApptDTOList, pageQuery);
    }

    public List<MoeGroomingAppointment> listClientAppt(
            List<ListCommonApptDTO> completedApptDTOList, PageQuery pageQuery) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listClientAppt, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.listClientAppt(completedApptDTOList, pageQuery);
    }

    public WebsiteAppointmentSummaryPO countBetween(Integer startId, Integer endId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for countBetween, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.countBetween(startId, endId);
    }

    public MoeGroomingAppointment getCustomerFirstAppt(Integer businessId, Integer customerId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getCustomerFirstAppt, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getCustomerFirstAppt(businessId, customerId);
    }

    public Set<Integer> listCustomerIdByFilter(ClientsFilterDTO clientsFilter) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listCustomerIdByFilter, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.listCustomerIdByFilter(clientsFilter);
    }

    public Set<Integer> listCustomerIdByDateFilter(ClientsFilterDTO clientsFilter) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listCustomerIdByDateFilter, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.listCustomerIdByDateFilter(clientsFilter);
    }

    public Set<Integer> listCustomerIdByGroomerFilter(ClientsFilterDTO clientsFilter) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listCustomerIdByGroomerFilter, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.listCustomerIdByGroomerFilter(clientsFilter);
    }

    public List<CustomerApptCountDTO> listCustomerApptCount(BusinessDateClientsDTO businessDateClients) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listCustomerApptCount, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.listCustomerApptCount(businessDateClients);
    }

    public List<CustomerApptDateDTO> listCustomerApptDate(BusinessDateClientsDTO businessDateClients) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listCustomerApptDate, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.listCustomerApptDate(businessDateClients);
    }

    public List<MoeGroomingAppointment> batchSelectCustomerDeleteAppt(Integer businessId, Set<Integer> customerIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for batchSelectCustomerDeleteAppt, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.batchSelectCustomerDeleteAppt(businessId, customerIds);
    }

    public int batchDeleteApptByCustomerId(
            Integer businessId, Set<Integer> customerIds, Integer staffId, Long nowTime) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for batchDeleteApptByCustomerId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.batchDeleteApptByCustomerId(businessId, customerIds, staffId, nowTime);
    }

    public List<MoeGroomingAppointment> queryApptsCreatedBetween(
            Integer businessId, Long startTime, Long endTime, Set<Integer> customerIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryApptsCreatedBetween, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryApptsCreatedBetween(businessId, startTime, endTime, customerIds);
    }

    public List<Integer> getPetHistoryAppointment(
            Integer businessId, List<Integer> customerIds, List<Integer> petIds, String startDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getPetHistoryAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getPetHistoryAppointment(businessId, customerIds, petIds, startDate);
    }

    public List<MoeGroomingAppointment> getAllApptByStartDateRange(
            Long companyId,
            Integer businessId,
            String startDateGte,
            String startDateLte,
            List<AppointmentStatusEnum> statusList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getAllApptByStartDateRange, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getAllApptByStartDateRange(
                companyId, businessId, startDateGte, startDateLte, statusList);
    }

    public List<MoeGroomingAppointment> listAppointmentsForPayroll(
            Integer businessId, String queryStartDate, String queryEndDate, List<AppointmentStatusEnum> statusList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listAppointmentsForPayroll, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.listAppointmentsForPayroll(businessId, queryStartDate, queryEndDate, statusList);
    }

    public List<MoeGroomingAppointment> getAllGroomingByDateRange(
            Integer businessId,
            String startDate,
            String endDate,
            List<Integer> serviceTypeIncludeList,
            List<AppointmentStatusEnum> statusList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getAllGroomingByDateRange, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getAllGroomingByDateRange(
                businessId, startDate, endDate, serviceTypeIncludeList, statusList);
    }

    public List<MoeGroomingAppointment> getAllGroomingByStartDate(
            Integer businessId,
            String date,
            List<Integer> serviceTypeIncludeList,
            List<AppointmentStatusEnum> statusList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getAllGroomingByStartDate, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getAllGroomingByStartDate(businessId, date, serviceTypeIncludeList, statusList);
    }

    public MoeGroomingAppointment getCustomerNewAppointment(Integer businessId, Integer customerId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getCustomerNewAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getCustomerNewAppointment(businessId, customerId);
    }

    public List<CountUpcomingApptByCustomerIdsPO> countUpcomingApptByCustomerIds(
            Integer businessId, String appointmentDate, Integer endTimes, Collection<Integer> customerIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for countUpcomingApptByCustomerIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.countUpcomingApptByCustomerIds(businessId, appointmentDate, endTimes, customerIds);
    }

    public List<GetLatestAppointmentDatePO> getLatestAppointmentDate(
            Integer businessId, Collection<Integer> customerIds, int latestCount, String date, Integer nowMinutes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getLatestAppointmentDate, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getLatestAppointmentDate(businessId, customerIds, latestCount, date, nowMinutes);
    }

    public BigDecimal countOBRequestsRevenue(Integer businessId, long startTime, long endTime) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for countOBRequestsRevenue, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.countOBRequestsRevenue(businessId, startTime, endTime);
    }

    public BigDecimal countPendingOBRequestsRevenue(Integer businessId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for countPendingOBRequestsRevenue, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.countPendingOBRequestsRevenue(businessId);
    }

    public List<MoeGroomingAppointment> getAppointmentsDateBetween(GetInvoiceIdsAppointmentDateBetweenParam param) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getAppointmentsDateBetween, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getAppointmentsDateBetween(param);
    }

    public Set<Integer> findNotNewCustomerIdList(Integer businessId, List<Integer> customerIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for findNotNewCustomerIdList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.findNotNewCustomerIdList(businessId, customerIds);
    }

    public List<BusinessCountPO> countAppointmentFromSourcePlatform(List<Integer> businessIds, String sourcePlatform) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for countAppointmentFromSourcePlatform, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.countAppointmentFromSourcePlatform(businessIds, sourcePlatform);
    }

    public List<MoeGroomingAppointment> describeAppointmentReports(DescribeAppointmentReportsParams params) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for describeAppointmentReports, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.describeAppointmentReports(params);
    }

    public void batchUpdateByPrimaryKeySelective(List<MoeGroomingAppointment> records) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for batchUpdateByPrimaryKeySelective, companyId: {}",
                    AuthContext.get().companyId());
        }
        appointmentMapper.batchUpdateByPrimaryKeySelective(records);
    }

    public List<MoeGroomingAppointment> scanAppointmentReports(ScanAppointmentReportsParams params) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for scanAppointmentReports, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.scanAppointmentReports(params);
    }

    public List<Integer> listActiveAppointmentIdByCustomerId(Integer businessId, Integer customerId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listActiveAppointmentIdByCustomerId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.listActiveAppointmentIdByCustomerId(businessId, customerId);
    }

    public List<Integer> listActiveAppointmentIdByServiceTypeInclude(
            Integer businessId, List<Integer> serviceTypeIncludeList, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listActiveAppointmentIdByServiceTypeInclude, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.listActiveAppointmentIdByServiceTypeInclude(
                businessId, serviceTypeIncludeList, startDate, endDate);
    }

    public List<Integer> queryDeletableAppts(Integer companyId, List<Integer> apptIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for queryDeletableAppts, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.queryDeletableAppts(companyId, apptIds);
    }

    public Integer batchInsertBlockRepeat(List<MoeGroomingAppointment> moeGroomingAppointments) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for batchInsertBlockRepeat, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.batchInsertBlockRepeat(moeGroomingAppointments);
    }

    public List<MoeGroomingAppointment> getAppointmentByIdsAndDateRange(
            Integer businessId, List<Integer> idList, String startDate, String endDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getAppointmentByIdsAndDateRange, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentMapper.getAppointmentByIdsAndDateRange(businessId, idList, startDate, endDate);
    }
}
