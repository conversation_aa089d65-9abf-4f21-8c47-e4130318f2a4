package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeQbTask;
import com.moego.server.grooming.mapperbean.MoeQbTaskExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeQbTaskMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_task
     *
     * @mbg.generated
     */
    long countByExample(MoeQbTaskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_task
     *
     * @mbg.generated
     */
    int deleteByExample(MoeQbTaskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_task
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_task
     *
     * @mbg.generated
     */
    int insert(MoeQbTask record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_task
     *
     * @mbg.generated
     */
    int insertSelective(MoeQbTask record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_task
     *
     * @mbg.generated
     */
    List<MoeQbTask> selectByExample(MoeQbTaskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_task
     *
     * @mbg.generated
     */
    MoeQbTask selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_task
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") MoeQbTask record, @Param("example") MoeQbTaskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_task
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MoeQbTask record, @Param("example") MoeQbTaskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_task
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeQbTask record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_task
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeQbTask record);

    MoeQbTask selectBusinessIdTask(@Param("businessId") Integer businessId);

    int updateFailTask(@Param("nowTime") Long nowTime, @Param("nowFailTime") Long nowFailTime);

    List<MoeQbTask> selectTaskByBusinessIdAndType(
            @Param("businessId") Integer businessId, @Param("taskType") Byte taskType);
}
