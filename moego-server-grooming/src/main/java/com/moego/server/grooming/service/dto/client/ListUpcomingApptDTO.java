package com.moego.server.grooming.service.dto.client;

import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022/12/6
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ListUpcomingApptDTO extends ListCommonApptDTO {

    /**
     * moe_business_customer.share_range_type
     */
    private Byte shareRangeType;

    /**
     * share_range_type = 3, share_appt_json
     */
    private List<Integer> shareApptIdList;

    /**
     * share_range_type = 1, share_range_value
     */
    private String endDate;

    /**
     * share_range_type = 2, share_range_value
     */
    private Integer limit;
}
