package com.moego.server.grooming.server;

import com.moego.common.enums.FeatureConst;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.server.grooming.api.IGroomingPetDetailServiceBase;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.GroomingPetServiceListInfoDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.PetDetailDTO;
import com.moego.server.grooming.enums.PetDetailStatusEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.EvaluationServiceDetailMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.EvaluationServiceDetail;
import com.moego.server.grooming.mapperbean.EvaluationServiceDetailExample;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetailExample;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceExample;
import com.moego.server.grooming.mapstruct.PetDetailConverter;
import com.moego.server.grooming.params.CommonIdsParams;
import com.moego.server.grooming.params.EditPetDetailStaffCommissionParam;
import com.moego.server.grooming.params.PetPassAwayParams;
import com.moego.server.grooming.service.GroomingServiceOperationService;
import com.moego.server.grooming.service.MoePetDetailService;
import com.moego.server.grooming.service.RouteOptimizationService;
import com.moego.server.payment.api.IPaymentPlanService;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class GroomingPetDetailServer extends IGroomingPetDetailServiceBase {

    private final MoePetDetailService petDetailService;
    private final RouteOptimizationService routeOptimizationService;
    private final IPaymentPlanService paymentPlanService;
    private final GroomingServiceOperationService groomingServiceOperationService;
    private final AppointmentMapperProxy appointmentMapper;
    private final PetDetailMapperProxy petDetailMapper;
    private final EvaluationServiceDetailMapper evaluationServiceDetailMapper;
    private final MoeGroomingServiceMapper groomingServiceMapper;
    private static final String PET_DETAIL_EXTERNAL_ID_PREFIX = "petdetail_";

    @Override
    public List<GroomingPetDetailDTO> queryPetDetailByGroomingId(@RequestBody CommonIdsParams commonIdsParams) {
        return petDetailService.queryPetDetailByGroomingId(commonIdsParams.getIds());
    }

    @Override
    public Boolean petPassAwayOrDelete(@RequestBody PetPassAwayParams params) {
        return petDetailService.petRemove(params);
    }

    @Override
    public List<GroomingPetServiceListInfoDTO> queryFinishedPetDetailByBusinessIdList(
            List<Integer> businessIdList, String startDate, String endDate) {
        return petDetailService.queryFinishedPetDetailByBusinessIdList(businessIdList, startDate, endDate);
    }

    @Override
    public void initRouteOptimizationTimes(Long companyId) {
        var featureCode = paymentPlanService.queryCompanyPlanFeatureByCidCode(
                companyId.intValue(), FeatureConst.FC_ROUTE_OPTIMIZATION);
        routeOptimizationService.initRouteOptimizationTimes(companyId, featureCode);
    }

    @Override
    public List<PetDetailDTO> listBoardingPetDetailByDateRange(ListBoardingPetDetailByDateRangeParam param) {
        List<Integer> appointmentIds = appointmentMapper.listActiveAppointmentIdByServiceTypeInclude(
                param.businessId(),
                ServiceItemEnum.getBitValueListByServiceItem(ServiceItemEnum.BOARDING),
                param.dateRange().lower(),
                param.dateRange().upper());

        if (ObjectUtils.isEmpty(appointmentIds)) {
            return List.of();
        }

        MoeGroomingPetDetailExample e = new MoeGroomingPetDetailExample();
        e.createCriteria()
                .andGroomingIdIn(appointmentIds)
                .andServiceItemTypeEqualTo(ServiceItemEnum.BOARDING.getServiceItem())
                .andStatusEqualTo(PetDetailStatusEnum.NORMAL.getValue().byteValue());

        return petDetailMapper.selectByExample(e).stream()
                .filter(it -> isValid(param, it))
                .map(PetDetailConverter.INSTANCE::entityToDTO)
                .toList();
    }

    @Override
    public List<PetDetailDTO> listPetDetailByDateRange(ListPetDetailByDateRangeParam param) {
        List<Integer> appointmentIds = appointmentMapper.listActiveAppointmentIdByServiceTypeInclude(
                param.businessId(),
                null,
                param.dateRange().lower(),
                param.dateRange().upper());

        if (ObjectUtils.isEmpty(appointmentIds)) {
            return List.of();
        }

        // pet_detail 表只包含了 grooming/boarding/daycare 的数据
        // evaluation 的数据在 evaluation_service_detail 中
        // 这里统一处理，将 evaluation 的数据转换为 PetDetail :)

        var all = new ArrayList<PetDetailDTO>();

        all.addAll(listNonEvaluationPetDetailWithPureService(appointmentIds));
        all.addAll(listEvaluationPetDetail(appointmentIds));

        return all;
    }

    @Override
    public List<GroomingPetDetailDTO> listGroomingPetDetail(
            Long businessId, Long groomingId, List<Long> petDetailIdList) {
        List<GroomingPetDetailDTO> groomingPetDetailDTOS = petDetailService.queryPetDetailDTOByIdList(petDetailIdList);
        fillExtraInfo(businessId, groomingId, groomingPetDetailDTOS);
        return groomingPetDetailDTOS;
    }

    @Override
    public void updatePetDetailStaff(EditPetDetailStaffCommissionParam params) {
        // 更新pet detail，service operation
        petDetailService.updatePetDetailStaff(params);
    }

    @Override
    public List<GroomingPetDetailDTO> queryAllPetDetailByGroomingId(Long businessId, Long groomingId) {
        List<GroomingPetDetailDTO> groomingPetDetailDTOS =
                petDetailService.queryAllPetDetailByGroomingIds(List.of(Math.toIntExact(groomingId)));
        fillExtraInfo(businessId, groomingId, groomingPetDetailDTOS);
        return groomingPetDetailDTOS;
    }

    private void fillExtraInfo(Long businessId, Long groomingId, List<GroomingPetDetailDTO> groomingPetDetailDTOS) {
        Map<Integer, List<GroomingServiceOperationDTO>> operationMapByPetDetailId =
                groomingServiceOperationService.getOperationMapByPetDetailId(
                        Math.toIntExact(businessId), Math.toIntExact(groomingId));
        for (GroomingPetDetailDTO groomingPetDetailDTO : groomingPetDetailDTOS) {
            groomingPetDetailDTO.setOperationList(operationMapByPetDetailId.get(groomingPetDetailDTO.getId()));
            groomingPetDetailDTO.setExternalId(PET_DETAIL_EXTERNAL_ID_PREFIX + groomingPetDetailDTO.getId());
        }
    }

    private List<PetDetailDTO> listEvaluationPetDetail(List<Integer> appointmentIds) {
        if (ObjectUtils.isEmpty(appointmentIds)) {
            return List.of();
        }

        var example = new EvaluationServiceDetailExample();
        example.createCriteria()
                .andAppointmentIdIn(
                        appointmentIds.stream().map(Integer::longValue).toList());
        return evaluationServiceDetailMapper.selectByExample(example).stream()
                .map(GroomingPetDetailServer::evaluationServiceDetailToPetDetailDTO)
                .toList();
    }

    private List<PetDetailDTO> listNonEvaluationPetDetail(List<Integer> appointmentIds) {
        if (ObjectUtils.isEmpty(appointmentIds)) {
            return List.of();
        }

        var e = new MoeGroomingPetDetailExample();
        e.createCriteria()
                .andGroomingIdIn(appointmentIds)
                .andStatusEqualTo(PetDetailStatusEnum.NORMAL.getValue().byteValue());

        return petDetailMapper.selectByExample(e).stream()
                .map(PetDetailConverter.INSTANCE::entityToDTO)
                .toList();
    }

    private List<PetDetailDTO> listNonEvaluationPetDetailWithPureService(List<Integer> appointmentIds) {
        if (ObjectUtils.isEmpty(appointmentIds)) {
            return List.of();
        }

        var petDetailDTOS = listNonEvaluationPetDetail(appointmentIds);
        if (ObjectUtils.isEmpty(petDetailDTOS)) {
            return List.of();
        }

        MoeGroomingServiceExample example = new MoeGroomingServiceExample();
        example.createCriteria()
                .andIdIn(petDetailDTOS.stream()
                        .map(PetDetailDTO::getServiceId)
                        .distinct()
                        .toList());

        var pureServiceIds = groomingServiceMapper.selectByExample(example).stream()
                .filter(service -> Objects.equals(service.getType().intValue(), ServiceType.SERVICE_VALUE))
                .map(MoeGroomingService::getId)
                .collect(Collectors.toSet());

        return petDetailDTOS.stream()
                .filter(dto -> pureServiceIds.contains(dto.getServiceId()))
                .toList();
    }

    private static boolean isValid(ListBoardingPetDetailByDateRangeParam param, MoeGroomingPetDetail petDetail) {
        String startDate = petDetail.getStartDate();
        String endDate = petDetail.getEndDate();
        if (!StringUtils.hasText(startDate) || !StringUtils.hasText(endDate)) {
            return false;
        }
        return param.dateRange().lower().compareTo(endDate) <= 0
                && param.dateRange().upper().compareTo(startDate) >= 0;
    }

    private static PetDetailDTO evaluationServiceDetailToPetDetailDTO(EvaluationServiceDetail evaluationServiceDetail) {
        var dto = new PetDetailDTO();
        dto.setId(Math.toIntExact(evaluationServiceDetail.getId()));
        dto.setGroomingId(Math.toIntExact(evaluationServiceDetail.getAppointmentId()));
        dto.setPetId(Math.toIntExact(evaluationServiceDetail.getPetId()));
        dto.setServiceId(Math.toIntExact(evaluationServiceDetail.getServiceId()));
        dto.setServiceTime(evaluationServiceDetail.getServiceTime());
        dto.setServicePrice(evaluationServiceDetail.getServicePrice().doubleValue());
        dto.setStartTime(Long.valueOf(evaluationServiceDetail.getStartTime()));
        dto.setEndTime(Long.valueOf(evaluationServiceDetail.getEndTime()));
        dto.setStartDate(dateToLocalDate(evaluationServiceDetail.getStartDate()).toString());
        dto.setEndDate(dateToLocalDate(evaluationServiceDetail.getEndDate()).toString());
        dto.setServiceItemType(ServiceItemEnum.EVALUATION.getServiceItem());
        return dto;
    }

    static LocalDate dateToLocalDate(Date date) {
        return LocalDate.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }
}
