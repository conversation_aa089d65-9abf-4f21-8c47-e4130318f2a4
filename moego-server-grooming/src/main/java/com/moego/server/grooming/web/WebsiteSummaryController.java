package com.moego.server.grooming.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.service.WebsiteSummaryService;
import com.moego.server.grooming.service.dto.WebsiteSummaryDTO;
import com.moego.server.grooming.web.vo.WebsiteSummaryVO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/grooming/website_summary")
public class WebsiteSummaryController {

    @Autowired
    private WebsiteSummaryService websiteSummaryService;

    /**
     * Get website summary data.
     */
    @GetMapping
    @Auth(AuthType.ANONYMOUS)
    public WebsiteSummaryVO getSummary() {
        WebsiteSummaryDTO summary = websiteSummaryService.getSummary();
        return new WebsiteSummaryVO(
                toMillion(summary.appointmentCount()),
                toMillion(summary.petCount()),
                toMillion(summary.customerCount()));
    }

    /**
     * int to million, 2 decimal places.
     *
     * @param value int value
     * @return double
     */
    static double toMillion(int value) {
        BigDecimal v = BigDecimal.valueOf(value).divide(BigDecimal.valueOf(1000_000L), 2, RoundingMode.CEILING);
        return v.doubleValue();
    }
}
