package com.moego.server.grooming.server;

import com.moego.server.grooming.api.IGroomingTrackServiceBase;
import com.moego.server.grooming.dto.track.GroomingApptDTO;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/3/9
 */
@RestController
@AllArgsConstructor
public class GroomingTrackServer extends IGroomingTrackServiceBase {

    private final AppointmentMapperProxy groomingAppointmentMapper;

    @Override
    public GroomingApptDTO getCustomerFirstAppt(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId) {
        MoeGroomingAppointment appt = groomingAppointmentMapper.getCustomerFirstAppt(businessId, customerId);
        if (Objects.isNull(appt)) {
            return null;
        }
        return new GroomingApptDTO()
                .setGroomingId(appt.getId())
                .setBusinessId(appt.getBusinessId())
                .setCustomerId(appt.getCustomerId())
                .setSource(appt.getSource())
                .setCreateTime(appt.getCreateTime());
    }
}
