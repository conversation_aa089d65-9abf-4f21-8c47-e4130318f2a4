package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingReportQuestion;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MoeGroomingReportQuestionMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_question
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_question
     *
     * @mbg.generated
     */
    int insert(MoeGroomingReportQuestion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_question
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingReportQuestion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_question
     *
     * @mbg.generated
     */
    MoeGroomingReportQuestion selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_question
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingReportQuestion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_question
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeGroomingReportQuestion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_question
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingReportQuestion record);

    int countByBusinessId(Integer businessId);

    int initGroomingReportQuestions(List<MoeGroomingReportQuestion> questions);

    List<MoeGroomingReportQuestion> selectByBusinessId(Integer businessId);

    List<MoeGroomingReportQuestion> selectByBusinessIdAndCategory(
            @Param("businessId") Integer businessId, @Param("category") Byte category);

    List<MoeGroomingReportQuestion> selectByBusinessIdAndIds(
            @Param("businessId") Integer businessId, @Param("ids") Set<Integer> ids);
}
