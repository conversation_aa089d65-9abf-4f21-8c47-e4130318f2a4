package com.moego.server.grooming.mapper;

import com.moego.common.dto.clients.BusinessDateClientsDTO;
import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.common.params.PageQuery;
import com.moego.server.business.dto.AppointmentPetIdDTO;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.grooming.constant.AppointmentStatusSet;
import com.moego.server.grooming.dto.AppointmentDateWithServiceDurationDto;
import com.moego.server.grooming.dto.AppointmentReminderSendDTO;
import com.moego.server.grooming.dto.AppointmentWithPetDetailsDto;
import com.moego.server.grooming.dto.BookOnlineAutoMoveAppointmentDTO;
import com.moego.server.grooming.dto.CustomerApptCountDTO;
import com.moego.server.grooming.dto.CustomerApptDateDTO;
import com.moego.server.grooming.dto.CustomerGrooming;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentDTO;
import com.moego.server.grooming.dto.CustomerStatisticsDTO;
import com.moego.server.grooming.dto.CustomerUpComingAppointDTO;
import com.moego.server.grooming.dto.GroomingAppointmentWaitingListDTO;
import com.moego.server.grooming.dto.GroomingBookingDTO;
import com.moego.server.grooming.dto.GroomingIdPetIdDto;
import com.moego.server.grooming.dto.PetBreedInfoDTO;
import com.moego.server.grooming.dto.RepeatAppointmentDto;
import com.moego.server.grooming.dto.RepeatNumDTO;
import com.moego.server.grooming.dto.StaffBlockInfoDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.WaitListStatusEnum;
import com.moego.server.grooming.mapper.po.BusinessCountPO;
import com.moego.server.grooming.mapper.po.CountUpcomingApptByCustomerIdsPO;
import com.moego.server.grooming.mapper.po.GetLatestAppointmentDatePO;
import com.moego.server.grooming.mapper.po.LongestWorkingDayPo;
import com.moego.server.grooming.mapper.po.WebsiteAppointmentSummaryPO;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointmentExample;
import com.moego.server.grooming.params.GetInvoiceIdsAppointmentDateBetweenParam;
import com.moego.server.grooming.params.report.DescribeAppointmentReportsParams;
import com.moego.server.grooming.params.report.ScanAppointmentReportsParams;
import com.moego.server.grooming.service.dto.CustomerIdCountResultDto;
import com.moego.server.grooming.service.dto.GroomingReportApptDetail;
import com.moego.server.grooming.service.dto.GroomingReportWebAppointment;
import com.moego.server.grooming.service.dto.QbQueryGroomingResultDto;
import com.moego.server.grooming.service.dto.ReportAppointmentDAO;
import com.moego.server.grooming.service.dto.client.ListCommonApptDTO;
import com.moego.server.grooming.service.dto.client.ListUpcomingApptDTO;
import com.moego.server.grooming.service.dto.ob.OBClientApptDTO;
import com.moego.server.grooming.web.vo.GroomingBookingQueryVO;
import com.moego.server.grooming.web.vo.GroomingCustomerQueryVO;
import com.moego.server.grooming.web.vo.WaitingPetDetailVO;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingAppointmentMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    long countByExample(MoeGroomingAppointmentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    int deleteByExample(MoeGroomingAppointmentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    int insert(MoeGroomingAppointment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingAppointment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    List<MoeGroomingAppointment> selectByExample(MoeGroomingAppointmentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    MoeGroomingAppointment selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeGroomingAppointment record, @Param("example") MoeGroomingAppointmentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeGroomingAppointment record, @Param("example") MoeGroomingAppointmentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingAppointment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_appointment
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingAppointment record);

    MoeGroomingAppointment selectByPrimaryKeyAndBusinessId(
            @Param("id") Integer id, @Param("businessId") Integer businessId);

    List<MoeGroomingAppointment> selectByIdListAndBusinessId(
            @Param("idList") List<Integer> idList, @Param("businessId") Integer businessId);

    List<MoeGroomingAppointment> selectByIdList(@Param("idList") List<Integer> idList);

    List<StaffBlockInfoDTO> selectBlockByApptDates(
            @Param("businessId") Integer businessId, @Param("appointmentDates") Set<String> appointmentDates);

    List<StaffBlockInfoDTO> selectBlockListByDatesAndStaffIds(
            @Param("businessId") Integer businessId,
            @Param("appointmentDates") Set<String> appointmentDates,
            @Param("staffIds") Set<Integer> staffIds);

    List<MoeGroomingAppointment> queryApptsByRepeatId(
            @Param("businessId") Integer businessId,
            @Param("repeatId") Integer repeatId,
            @Param("appointmentDate") String appointmentDate);

    List<MoeGroomingAppointment> queryBlocksByRepeatId(
            @Param("businessId") Integer tokenBusinessId,
            @Param("repeatId") Integer repeatId,
            @Param("appointmentDate") String appointmentDate);

    List<MoeGroomingAppointment> selectBusinessCustomerIdByApptId(
            @Param("list") List<Integer> ids, @Param("businessId") Integer businessId);

    List<BookOnlineAutoMoveAppointmentDTO> queryAllBookingAutoMoveAppointment(@Param("scrollId") Integer scrollId);

    Integer deleteAppointments(@Param("list") List<Integer> ids, @Param("businessId") Integer businessId);

    List<CustomerGrooming> queryGroomingCustomerAppointmentNum(
            @Param("groomingCustomerQuery") GroomingCustomerQueryVO groomingCustomerQueryVO);

    List<GroomingBookingDTO> queryGroomingBookingAppointment(
            @Param("groomingCustomerQuery") GroomingBookingQueryVO groomingCustomerQueryVO);

    List<GroomingBookingDTO> queryGroomingBookingAppointmentJoin(@Param("groomingIds") List<Integer> groomingIds);

    /**
     * 查询ob的request数量
     * queryType=1查询ob request
     * queryType=2查询ob request waiting list
     *
     * @param businessId
     * @param queryType
     * @return
     */
    Integer queryAppointmentCount(
            @Param("businessId") Integer businessId,
            @Param("bookOnlineStatus") Byte bookOnlineStatus,
            @Param("queryType") Integer queryType);

    /**
     * 查询某个范围的预约数量
     *
     * @param businessId
     * @param startDate
     * @param endDate
     * @return
     */
    Integer queryAppointmentCountRange(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<GroomingAppointmentWaitingListDTO> queryAppointmentWaitingList(
            @Param("waitingPetDetail") WaitingPetDetailVO waitingPetDetailVO);

    /**
     * 单个customerId 查询 last/next appointment grooming only 的 last/next appointment
     *
     * @param date 日期yyyy-MM-dd
     * @param last last true next false
     * @return
     */
    CustomerGroomingAppointmentDTO queryCustomerLastOrNextAppointment(
            @Param("companyId") Long companyId,
            @Param("customerId") Integer customerId,
            @Param("appointmentDate") String date,
            @Param("endTimes") Integer endTimes,
            @Param("ignoreGroomingId") Integer ignoreGroomingId,
            @Param("last") Boolean last,
            @Param("excludeBookingRequest") boolean excludeBookingRequest);

    /**
     * 单个customerId 查询 last/next appointment with all service type
     * 获取 grooming only 的 last/next appointment 参考 {@link #queryCustomerLastOrNextAppointment}
     *
     * @param date 日期yyyy-MM-dd
     * @param last last true next false
     * @return
     */
    CustomerGroomingAppointmentDTO queryCustomerLastOrNextAppointmentAllServiceType(
            @Param("companyId") Long companyId,
            @Param("customerId") Integer customerId,
            @Param("appointmentDate") String date,
            @Param("endTimes") Integer endTimes,
            @Param("ignoreGroomingId") Integer ignoreGroomingId,
            @Param("last") Boolean last,
            @Param("excludeBookingRequest") boolean excludeBookingRequest);

    OBClientApptDTO getLastFinishedApptByCustomerId(
            @Param("companyId") Long companyId,
            @Param("customerId") Integer customerId,
            @Param("appointmentDate") String date,
            @Param("endTimes") Integer endTimes);

    /**
     * customerIdList查询last
     *
     * @param date 日期yyyy-MM-dd
     * @return
     */
    List<CustomerGroomingAppointmentDTO> selectCustomerLastAppt(
            @Param("customerIdList") List<Integer> customerIdList,
            @Param("companyId") Long companyId,
            @Param("appointmentDate") String date,
            @Param("endTimes") Integer endTimes);

    List<CustomerGroomingAppointmentDTO> selectCustomerLastFinishedAppt(
            @Param("customerIdList") List<Integer> customerIdList,
            @Param("companyId") Long companyId,
            @Param("appointmentDate") String date,
            @Param("endTimes") Integer endTimes);

    List<CustomerGroomingAppointmentDTO> selectCustomerLastTwoUncanceledApptDate(
            @Param("customerId") Integer customerIdList,
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String date,
            @Param("endTimes") Integer endTimes);

    List<Integer> getCustomerIdsWithUncancelledRepeat(
            @Param("businessId") Integer businessId, @Param("customerIds") Set<Integer> customerId);

    Integer countCustomerIdsWithUncancelledRepeat(
            @Param("businessId") Integer businessId, @Param("customerIds") Set<Integer> customerId);

    Integer countCustomerIdsWithUncancelledRepeatByCompanyId(
            @Param("companyId") Long companyId, @Param("customerIds") Set<Integer> customerId);

    /**
     * customerIdList查询last
     *
     * @param date 日期yyyy-MM-dd
     * @return
     */
    List<CustomerGroomingAppointmentDTO> selectLapsedCustomerLastAppt(
            @Param("customerIdList") List<Integer> customerIdList,
            @Param("companyId") Long companyId,
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String date,
            @Param("endTimes") Integer endTimes);

    /**
     * customerIdList查询next
     *
     * @param date 日期yyyy-MM-dd
     * @return
     */
    List<CustomerGroomingAppointmentDTO> selectCustomerNextAppt(
            @Param("customerIdList") List<Integer> customerIdList,
            @Param("companyId") Long companyId,
            @Param("appointmentDate") String date,
            @Param("endTimes") Integer endTimes);

    CustomerGroomingAppointmentDTO selectCustomerPetNextAppt(
            @Param("companyId") Long companyId,
            @Param("customerId") Integer customerId,
            @Param("petId") Integer petId,
            @Param("ignoredGroomingId") Integer ignoredGroomingId,
            @Param("fromDate") String fromDate,
            @Param("fromMinutes") Integer fromMinutes);

    /**
     * 批量获取apponitment数量
     *
     * @param customerIds
     * @param date
     * @param endTimes
     * @return
     */
    List<CustomerStatisticsDTO> getCustomerAppointmentNumBatch(
            @Param("customerIds") List<Integer> customerIds,
            @Param("appointmentDate") String date,
            @Param("endTimes") Integer endTimes,
            @Param("companyId") Long companyId);

    List<Integer> queryCustomerIdsInWaiting(@Param("customerIds") List<Integer> customerIds);

    List<Integer> queryStaffUpComingAppointCount(
            @Param("businessId") Integer businessId,
            @Param("staffId") Integer staffId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes);

    List<Integer> queryServiceUpComingAppointCount(
            @Param("businessId") Integer businessId,
            @Param("serviceId") Integer serviceId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes);

    /**
     * query all ordered time of the staff on target date
     *
     * @param businessId
     * @param appointmentDate
     * @param staffId
     * @return
     */
    List<TimeRangeDto> queryStaffAppointTimeByDate(
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String appointmentDate,
            @Param("staffId") Integer staffId);

    List<CustomerGrooming> queryGroomingCustomerAppointment(
            @Param("appointmentIds") List<Integer> appointmentIds, @Param("orderType") Integer orderType);

    List<CustomerUpComingAppointDTO> queryCustomerUpComingAppoint(
            @Param("customerId") Integer customerId,
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes);

    List<CustomerUpComingAppointDTO> queryCustomerUpComingAppointForClientShare(
            @Param("customerId") Integer customerId,
            @Param("companyId") Integer companyId,
            @Param("apptIds") List<Integer> apptIds,
            @Param("startDate") String startDate,
            @Param("startMins") Integer startMins,
            @Param("status") Integer status,
            @Param("endDateStr") String endDateStr);

    List<CustomerGrooming> queryGroomingCustomerAppointmentByIds(@Param("ids") List<Integer> ids);

    List<Integer> queryFutureAppointmentCustomerIdList(
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes);

    List<Integer> queryFutureAppointmentCustomerIdListByCompany(
            @Param("companyId") Long companyId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes);

    List<MoeGroomingAppointment> selectBusinessRepeatAppointmentReminder(
            @Param("businessId") Integer businessId,
            @Param("dismissIds") List<Integer> dismissIds,
            @Param("repeatIds") List<Integer> repeatIds,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<MoeGroomingAppointment> selectBusinessAppointmentReminder(
            @Param("businessId") Integer businessId,
            @Param("dismissIds") List<Integer> dismissIds,
            @Param("startDay") String startDay,
            @Param("endDay") String endDay,
            @Param("startMinute") Integer startMinute,
            @Param("status") Integer status);

    Integer customerDelete(
            @Param("companyId") Long companyId,
            @Param("customerId") Integer customerId,
            @Param("staffId") Integer staffId,
            @Param("nowTime") Long nowTime);

    List<MoeGroomingAppointment> selectCustomerDeleteAppt(
            @Param("companyId") Long companyId, @Param("customerId") Integer customerId);

    Integer selectHasUpcomingService(
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes,
            @Param("businessId") Integer businessId,
            @Param("serviceId") Integer serviceId);

    Integer countRepeatUpcomingCountForExpiryReminder(
            @Param("businessId") Integer businessId,
            @Param("repeatId") Integer repeatId,
            @Param("currentDate") String currentDate,
            @Param("currentTime") Integer currentTime);

    List<RepeatAppointmentDto> selectRepeatAppointmentList(
            @Param("businessId") Integer businessId,
            @Param("repeatId") Integer repeatId,
            @Param("statusList") List<AppointmentStatusEnum> statusList);

    List<RepeatAppointmentDto> selectRepeatAppointmentListByType(
            @Param("businessId") Integer businessId,
            @Param("repeatId") Integer repeatId,
            @Param("statusList") List<AppointmentStatusEnum> statusList,
            @Param("currentDate") String currentDate,
            @Param("currentTime") Integer currentTime,
            @Param("type") Byte type);

    RepeatAppointmentDto selectRepeatAppointmentById(
            @Param("businessId") Integer businessId, @Param("appointmentId") Integer appointmentId);

    List<CustomerUpComingAppointDTO> queryBusinessUpComingAppoint(
            @Param("customerId") Integer customerId,
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String appointmentDate);

    Integer getLatestClientUpcomingAppointId(
            @Param("businessIds") Set<Integer> businessIds,
            @Param("customerIds") Set<Integer> customerIds,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTime") Integer endTime);

    List<CustomerUpComingAppointDTO> queryCustomerUpcomingAppoint(
            @Param("businessIds") Set<Integer> businessIds,
            @Param("customerIds") Set<Integer> customerIds,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTime") Integer endTime);

    CustomerUpComingAppointDTO queryCustomerUpcomingAppointById(@Param("appointmentId") Integer appointmentId);

    List<Integer> selectCustomerIdWithInAllAppointmentDate(
            @Param("businessId") Integer businessId, @Param("staffId") Integer staffId);

    Integer selectCustomerIdIsWithAppointmentDate(
            @Param("businessId") Integer businessId,
            @Param("staffId") Integer staffId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("customerId") Integer customerId);

    Integer selectCustomerIdIsWithAllAppointmentDate(
            @Param("businessId") Integer businessId,
            @Param("staffId") Integer staffId,
            @Param("customerId") Integer customerId);

    List<AppointmentReminderSendDTO> queryAppointmentReminderWill(
            @Param("businessId") Integer businessId,
            @Param("dismissIds") List<Integer> dismissIds,
            @Param("appointmentDate") String appointmentDate,
            @Param("status") Integer status);

    AppointmentReminderSendDTO queryAppointmentReminderSendDto(
            @Param("businessId") Integer businessId, @Param("groomingId") Integer groomingId);

    @Deprecated
    List<Integer> getBusinessIdsOnSendDaily(
            @Param("tomorrow") String dateTomorrow, @Param("yesterday") String dateYesterday);

    List<CustomerGroomingAppointmentDTO> selectCustomerLastServiceTime(
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes,
            @Param("dismissCustomerIds") List<Integer> dismissCustomerIds);

    List<CustomerGroomingAppointmentDTO> selectCustomerLastServiceTimeByCompany(
            @Param("companyId") Integer companyId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes,
            @Param("customerIds") List<Integer> customerIds);

    CustomerGroomingAppointmentDTO selectOneCustomerLastServiceTime(
            @Param("businessId") Integer businessId,
            @Param("customerId") Integer customerId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes);

    String selectLastServiceTime(@Param("businessId") Integer businessId, @Param("customerId") Integer customerId);

    List<Integer> queryRepeatIdOnlyTwoGrooming(
            @Param("businessId") Integer businessId, @Param("appointmentDate") String startDate);

    List<RepeatNumDTO> queryRepeatLastestAppointment(
            @Param("businessId") Integer businessId, @Param("list") List<Integer> repeatIds);

    List<RepeatNumDTO> queryCustomerLastestAppointment(
            @Param("businessId") Integer businessId,
            @Param("customerIds") List<Integer> customerIds,
            @Param("appointmentDate") String startDate);

    /**
     * 查询appt 和invoice信息，包括所有状态的appt
     *
     * @param businessIds
     * @param startDate
     * @param endDate
     * @return
     */
    List<GroomingReportApptDetail> queryBusinessApptsAndInvoiceWithDate(
            @Param("businessIds") List<Integer> businessIds,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<ReportAppointmentDAO> queryUnpaidApptsWithAmount(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<ReportAppointmentDAO> queryUnclosedAppts(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("currentDate") String currentDate);

    List<ReportAppointmentDAO> queryCancelledApptsWithNoShowInfo(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<ReportAppointmentDAO> queryNoShowApptsWithNoShowInfo(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<ReportAppointmentDAO> queryApptsByOnlineBook(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<ReportAppointmentDAO> queryUpcomingAppts(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("currentDate") String currentDate);

    List<ReportAppointmentDAO> queryWaitListAppts(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<MoeGroomingAppointment> queryByOnlineBook(
            @Param("businessId") Integer businessId,
            @Param("statusList") List<AppointmentStatusEnum> statusList,
            @Param("isWaitingList") Byte isWaitingList,
            @Param("order") String order);

    List<MoeGroomingAppointment> queryWaitList(
            @Param("companyId") Long companyId,
            @Param("businessIdList") List<Integer> businessIdList,
            @Param("statusList") List<AppointmentStatusEnum> statusList,
            @Param("waitListStatusList") List<WaitListStatusEnum> waitListStatusList,
            @Param("customerIdList") List<Integer> customerIdList);

    List<QbQueryGroomingResultDto> queryAppointmentDateByDateRange(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<QbQueryGroomingResultDto> queryAppointmentDateByDateRangeWithStaffId(
            @Param("businessId") Integer businessId,
            @Param("staffId") Integer staffId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<MoeGroomingAppointment> queryByPrimaryIds(@Param("appointmentIds") List<Integer> appointmentIds);

    Integer getPetLastAppointmentId(
            @Param("companyId") Long companyId,
            @Param("businessId") Integer businessId,
            @Param("petId") Integer petId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes);

    List<GroomingReportWebAppointment> queryWebReportUnpaidAppts(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<GroomingReportWebAppointment> queryWebReportCancelledAppts(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<GroomingReportWebAppointment> queryWebReportNoShowAppts(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<GroomingReportWebAppointment> queryApptStaffAndCustomer(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<Integer> queryApptIdsByPageV2(
            @Param("businessIds") List<Integer> businessIds,
            @Param("staffId") Integer staffId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size);

    Integer getPayrollApptsCountV2(
            @Param("businessIds") List<Integer> businessIds,
            @Param("staffId") Integer staffId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    AppointmentWithPetDetailsDto getAppointmentWithPetDetails(
            @Param("appointmentId") Integer appointmentId, @Param("includeCancelled") Boolean includeCancelled);

    /**
     * 仅查询 Appointment，包含 grooming、bd、evaluation，不查询 block、waitlist。
     * @return
     */
    MoeGroomingAppointment getAppointmentById(@Param("id") Integer id);

    List<AppointmentWithPetDetailsDto> getAppointmentListWithPetDetails(
            @Param("appointmentIds") Set<Integer> appointmentIds, @Param("includeCancelled") Boolean includeCancelled);

    List<AppointmentWithPetDetailsDto> getRepeatedAppointmentListWithPetDetails(
            @Param("appointmentIds") Set<Integer> appointmentIds);

    Integer getPetCountUtilChristmas(
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes);

    Integer get2021AnnualPetCountUtilChristmas(@Param("businessId") Integer businessId);

    Integer getBusiness2021AllServiceDuration(
            @Param("businessId") Integer businessId, @Param("staffId") Integer staffId);

    AppointmentDateWithServiceDurationDto getBusiness2021LongestWorking(
            @Param("businessId") Integer businessId, @Param("staffId") Integer staffId);

    List<GroomingIdPetIdDto> getBusiness2021PetIdList(
            @Param("businessId") Integer businessId, @Param("staffId") Integer staffId);

    List<GroomingReportWebAppointment> queryWebReportApptEmployee(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("staffId") Integer staffId);

    int updateBatchCancelByPrimaryKey(
            @Param("businessId") Integer businessId,
            @Param("noShow") Byte noShow,
            @Param("cancelBy") Integer cancelBy,
            @Param("cancelByType") Byte cancelByType,
            @Param("updateTime") Long updateTime,
            @Param("canceledTime") Long canceledTime,
            @Param("idList") List<Integer> idList);

    /**
     * @param businessId
     * @param repeatId        不能为0或null
     * @param appointmentDate 如果是null，则cancel所有
     * @return
     */
    int cancelBlockByRepeatType(
            @Param("businessId") Integer businessId,
            @Param("repeatId") Integer repeatId,
            @Param("appointmentDate") String appointmentDate,
            @Param("updateTime") Long updateTime);

    List<Integer> selectBlockByRepeatType(
            @Param("businessId") Integer businessId,
            @Param("repeatId") Integer repeatId,
            @Param("appointmentDate") String appointmentDate);

    List<CustomerGroomingAppointmentDTO> queryExpiryCustomerLastApptListWithOrderLimit(
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes,
            @Param("customerIdSet") Set<Integer> customerIdSet,
            @Param("pageSize") Integer pageSize,
            @Param("startNum") Integer startNum);

    Integer queryExpiryCustomerLastApptListCount(
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes,
            @Param("customerIdSet") Set<Integer> customerIdSet);

    List<CustomerIdCountResultDto> queryExpiryCustomerUpcomingCount(
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes,
            @Param("upcomingCount") Integer upcomingCount,
            @Param("dismissIds") List<Integer> dismissIds);

    List<GroomingBookingDTO> queryAppointmentListBetweenTwoTime(
            @Param("businessId") Integer businessId,
            @Param("staffId") Integer staffId,
            @Param("startAppointmentDate") String startAppointmentDate,
            @Param("startMins") Integer startMins,
            @Param("endAppointmentDate") String endAppointmentDate,
            @Param("endMins") Integer endMins);

    List<Integer> queryUpcomingApptIdList(
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes,
            @Param("customerId") Integer customerId,
            @Param("statusList") List<Byte> statusList);

    List<Integer> queryCustomerUpcomingApptIdList(
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes,
            @Param("customerId") Integer customerId);

    List<StaffBlockInfoDTO> selectBlockByDateRange(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<PetBreedInfoDTO> queryPetBreed(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<MoeGroomingAppointment> getAppointmentsWithin(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("startMinutes") Integer startMinutes,
            @Param("endDate") String endDate,
            @Param("endMinutes") Integer endMinutes);

    /**
     * Get not canceled appointment list.
     *
     * @param businessId business id
     * @param staffId    staff id, can be null, if null, return all staff's appointments
     * @param startDate  start date
     * @param endDate    end date
     * @return
     */
    List<Integer> getNotCanceledAppointmentIds(
            @Param("businessId") Integer businessId,
            @Param("staffId") Integer staffId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    MoeGroomingAppointment getLastedNotCanceledAppointment(
            @Param("businessId") Integer businessId, @Param("customerId") Integer customerId);

    List<AppointmentPetIdDTO> queryStaffAppointmentPetIdByTime(
            @Param("businessId") Integer businessId,
            @Param("appointmentDates") Set<String> appointmentDates,
            @Param("startTimes") Set<Integer> startTimes);

    List<Integer> queryCustomerFinishApptIds(
            @Param("companyId") Long companyId, @Param("customerIds") List<Integer> customerIds);

    /**
     * Get total service duration in minutes.
     *
     * @param businessId businessId
     * @param staffId    staffId, can be null, if null, query all business staff
     * @param startDate  startDate
     * @param endDate    endDate
     * @return total service duration in minutes
     */
    Integer getServiceDuration(
            @Param("businessId") Integer businessId,
            @Param("staffId") Integer staffId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    /**
     * Get the longest service duration date.
     *
     * @param businessId businessId
     * @param staffId    staffId, can be null, if null, query all business staff
     * @param startDate  startDate
     * @param endDate    endDate
     * @return {@link LongestWorkingDayPo}
     */
    LongestWorkingDayPo getLongestWorkingDay(
            @Param("businessId") Integer businessId,
            @Param("staffId") Integer staffId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    /**
     * Get book online appointment count (any status).
     *
     * @param businessId businessId
     * @param startDate  startDate
     * @param endDate    endDate
     * @return count
     */
    int getOnlineBookingCount(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    /**
     * Get groomed pet ids.
     *
     * @param businessId businessId
     * @param staffId    staffId, can be null, if null, return all staff groomed pet ids
     * @param startDate  startDate
     * @param endDate    endDate
     * @return pet id list
     */
    List<Integer> getGroomedPetIds(
            @Param("businessId") Integer businessId,
            @Param("staffId") Integer staffId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<MoeGroomingAppointment> listUpcomingAppt(
            @Param("list") List<ListUpcomingApptDTO> upcomingApptDTOList, @Param("pageQuery") PageQuery pageQuery);

    List<MoeGroomingAppointment> listClientAppt(
            @Param("list") List<ListCommonApptDTO> completedApptDTOList, @Param("pageQuery") PageQuery pageQuery);

    /**
     * Count appointment between start id (inclusive) and end id (inclusive).
     *
     * @param startId start id, nullable, means no 'id >= startId' condition
     * @param endId   end id, nullable, means no 'id <= endId' condition
     * @return {@link WebsiteAppointmentSummaryPO}
     */
    WebsiteAppointmentSummaryPO countBetween(@Param("startId") Integer startId, @Param("endId") Integer endId);

    MoeGroomingAppointment getCustomerFirstAppt(
            @Param("businessId") Integer businessId, @Param("customerId") Integer customerId);

    Set<Integer> listCustomerIdByFilter(@Param("clientsFilter") ClientsFilterDTO clientsFilter);

    Set<Integer> listCustomerIdByDateFilter(@Param("clientsFilter") ClientsFilterDTO clientsFilter);

    Set<Integer> listCustomerIdByGroomerFilter(@Param("clientsFilter") ClientsFilterDTO clientsFilter);

    List<CustomerApptCountDTO> listCustomerApptCount(
            @Param("businessDateClients") BusinessDateClientsDTO businessDateClients);

    List<CustomerApptDateDTO> listCustomerApptDate(
            @Param("businessDateClients") BusinessDateClientsDTO businessDateClients);

    List<MoeGroomingAppointment> batchSelectCustomerDeleteAppt(
            @Param("businessId") Integer businessId, @Param("customerIds") Set<Integer> customerIds);

    int batchDeleteApptByCustomerId(
            @Param("businessId") Integer businessId,
            @Param("customerIds") Set<Integer> customerIds,
            @Param("staffId") Integer staffId,
            @Param("nowTime") Long nowTime);

    List<MoeGroomingAppointment> queryApptsCreatedBetween(
            @Param("businessId") Integer businessId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("customerIds") Set<Integer> customerIds);

    /**
     * 查询 pet 历史预约
     * 先查询 distinct appointment+pet 组合。再按日期，对每个 pet 取最新的五个预约。最后返回所有 pet 预约的集合
     */
    List<Integer> getPetHistoryAppointment(
            @Param("businessId") Integer businessId,
            @Param("customerIds") List<Integer> customerIds,
            @Param("petIds") List<Integer> petIds,
            @Param("startDate") String startDate);

    List<MoeGroomingAppointment> getAllApptByStartDateRange(
            @Param("companyId") Long companyId,
            @Param("businessId") Integer businessId,
            @Param("startDateGte") String startDateGte,
            @Param("startDateLte") String startDateLte,
            @Param("statusList") List<AppointmentStatusEnum> statusList);

    List<MoeGroomingAppointment> listAppointmentsForPayroll(
            @Param("businessId") Integer businessId,
            @Param("queryStartDate") String queryStartDate,
            @Param("queryEndDate") String queryEndDate,
            @Param("statusList") List<AppointmentStatusEnum> statusList);

    List<MoeGroomingAppointment> getAllGroomingByDateRange(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("serviceTypeIncludeList") List<Integer> serviceTypeIncludeList,
            @Param("statusList") List<AppointmentStatusEnum> statusList);

    List<MoeGroomingAppointment> getAllGroomingByStartDate(
            @Param("businessId") Integer businessId,
            @Param("date") String date,
            @Param("serviceTypeIncludeList") List<Integer> serviceTypeIncludeList,
            @Param("statusList") List<AppointmentStatusEnum> statusList);

    MoeGroomingAppointment getCustomerNewAppointment(
            @Param("businessId") Integer businessId, @Param("customerId") Integer customerId);

    List<CountUpcomingApptByCustomerIdsPO> countUpcomingApptByCustomerIds(
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes,
            @Param("customerIds") Collection<Integer> customerIds);

    /**
     * Get latest appointments for customers.
     *
     * @param businessId businessId
     * @param customerIds customerIds
     * @param latestCount 获取最近的预约数量
     * @return appointments
     */
    List<GetLatestAppointmentDatePO> getLatestAppointmentDate(
            @Param("businessId") Integer businessId,
            @Param("customerIds") Collection<Integer> customerIds,
            @Param("latestCount") int latestCount,
            @Param("date") String date,
            @Param("nowMinutes") Integer nowMinutes);

    BigDecimal countOBRequestsRevenue(
            @Param("businessId") Integer businessId,
            @Param("startTime") long startTime,
            @Param("endTime") long endTime);

    BigDecimal countPendingOBRequestsRevenue(@Param("businessId") Integer businessId);

    List<MoeGroomingAppointment> getAppointmentsDateBetween(
            @Param("param") GetInvoiceIdsAppointmentDateBetweenParam param);

    Set<Integer> findNotNewCustomerIdList(
            @Param("businessId") Integer businessId, @Param("customerIds") List<Integer> customerIds);

    /**
     * Count appointment from Google for specific business.
     *
     * @param businessIds businessIds
     * @return {@link BusinessCountPO}
     */
    List<BusinessCountPO> countAppointmentFromSourcePlatform(
            @Param("businessIds") List<Integer> businessIds, @Param("sourcePlatform") String sourcePlatform);

    List<MoeGroomingAppointment> describeAppointmentReports(DescribeAppointmentReportsParams params);

    void batchUpdateByPrimaryKeySelective(@Param("records") List<MoeGroomingAppointment> records);

    List<MoeGroomingAppointment> scanAppointmentReports(ScanAppointmentReportsParams params);

    /**
     * List appointment ids by customer id, only include status in {@link AppointmentStatusSet#ACTIVE_STATUS_SET}.
     *
     * @param businessId businessId
     * @param customerId customerId
     * @return appointment ids
     */
    List<Integer> listActiveAppointmentIdByCustomerId(
            @Param("businessId") Integer businessId, @Param("customerId") Integer customerId);

    /**
     * List appointment ids by service type include list,
     * only include status in {@link AppointmentStatusSet#ACTIVE_STATUS_SET}.
     *
     * @param businessId businessId
     * @param serviceTypeIncludeList service type include list
     * @return appointment ids
     */
    List<Integer> listActiveAppointmentIdByServiceTypeInclude(
            @Param("businessId") Integer businessId,
            @Param("serviceTypeIncludeList") @Nullable List<Integer> serviceTypeIncludeList,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    /**
     * 根据预约id列表查询可以被delete的预约id列表，目前只有 在waitlist or cancelled or repeat的预约可以被直接删除
     * @param companyId
     * @param apptIds
     * @return
     */
    List<Integer> queryDeletableAppts(@Param("companyId") Integer companyId, @Param("apptIds") List<Integer> apptIds);

    Integer batchInsertBlockRepeat(
            @Param("moeGroomingAppointments") List<MoeGroomingAppointment> moeGroomingAppointments);

    List<MoeGroomingAppointment> getAppointmentByIdsAndDateRange(
            @Param("businessId") Integer businessId,
            @Param("idList") List<Integer> idList,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);
}
