package com.moego.server.grooming.server;

import com.moego.server.grooming.api.IPetCustomizedServiceServiceBase;
import com.moego.server.grooming.dto.PetCustomizedServiceDTO;
import com.moego.server.grooming.mapper.MoeGroomingCustomerServicesMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices;
import com.moego.server.grooming.mapstruct.PetCustomizedServiceMapper;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2021/11/3 6:23 PM
 */
@RestController
@RequiredArgsConstructor
public class PetCustomizedServiceServer extends IPetCustomizedServiceServiceBase {

    private final MoeGroomingCustomerServicesMapper mapper;

    @Override
    public PetCustomizedServiceDTO getPetCustomizedServiceById(int petCustomizedServiceId) {
        MoeGroomingCustomerServices customerService = mapper.selectByPrimaryKey(petCustomizedServiceId);
        return PetCustomizedServiceMapper.INSTANCE.entity2DTO(customerService);
    }

    @Override
    public List<PetCustomizedServiceDTO> listByPetId(int petId) {
        return mapper.getCustomizeServicesByPetIds(null, List.of(petId), null, null).stream()
                .map(PetCustomizedServiceMapper.INSTANCE::entity2DTO)
                .toList();
    }
}
