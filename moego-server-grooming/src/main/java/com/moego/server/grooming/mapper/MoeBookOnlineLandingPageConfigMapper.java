package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MoeBookOnlineLandingPageConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    long countByExample(MoeBookOnlineLandingPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    int deleteByExample(MoeBookOnlineLandingPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlineLandingPageConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlineLandingPageConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    List<MoeBookOnlineLandingPageConfig> selectByExampleWithBLOBs(MoeBookOnlineLandingPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    List<MoeBookOnlineLandingPageConfig> selectByExample(MoeBookOnlineLandingPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    MoeBookOnlineLandingPageConfig selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeBookOnlineLandingPageConfig record,
            @Param("example") MoeBookOnlineLandingPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("record") MoeBookOnlineLandingPageConfig record,
            @Param("example") MoeBookOnlineLandingPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeBookOnlineLandingPageConfig record,
            @Param("example") MoeBookOnlineLandingPageConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlineLandingPageConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeBookOnlineLandingPageConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_landing_page_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlineLandingPageConfig record);

    MoeBookOnlineLandingPageConfig selectByBusinessId(@Param("businessId") Integer businessId);

    int updateByBusinessIdSelective(MoeBookOnlineLandingPageConfig record);

    MoeBookOnlineLandingPageConfig selectByDomainName(String domain);

    List<MoeBookOnlineLandingPageConfig> listByBusinessId(@Param("businessIdList") List<Integer> businessIdList);
}
