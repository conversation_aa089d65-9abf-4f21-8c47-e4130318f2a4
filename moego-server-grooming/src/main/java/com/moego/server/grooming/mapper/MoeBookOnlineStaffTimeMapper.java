package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlineStaffTime;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 通过 StaffTimeSyncService 已迁移到
 * moego_svc_online_booking staff_availablity等多张表
 */
@Deprecated
public interface MoeBookOnlineStaffTimeMapper {

    /**
     * 使用场景 init staff time
     * @param record
     * @return
     */
    int insertSelective(MoeBookOnlineStaffTime record);

    int updateByPrimaryKeySelective(MoeBookOnlineStaffTime record);
    // 双写 db 用到的逻辑，可忽略
    MoeBookOnlineStaffTime selectByPrimaryKey(Integer id);

    Integer selectMaxId();

    List<MoeBookOnlineStaffTime> selectByBusinessId(Integer businessId);

    // ss
    @Deprecated
    MoeBookOnlineStaffTime selectByStaffId(@Param("businessId") Integer businessId, @Param("staffId") Integer staffId);
}
