package com.moego.server.grooming.service.ob;

import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.params.CommonIdsParams;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineProfileMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.mapperbean.MoeBookOnlineProfile;
import com.moego.server.grooming.service.utils.LandingPageConfigHelper;
import com.moego.server.grooming.web.vo.client.AddressVO;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/3/17
 */
@Service
@AllArgsConstructor
public class OBBusinessProfileService {

    private final MoeBookOnlineProfileMapper bookOnlineProfileMapper;
    private final OBLandingPageConfigService landingPageConfigService;
    private final IBusinessBusinessService businessService;
    private final LandingPageConfigHelper landingPageConfigHelper;

    /**
     * Get book online profile by bid
     * 2.0 use moe_book_online_profile
     * 3.0 use moe_business
     *
     * @param businessId bid
     * @return ob profile
     */
    public MoeBookOnlineProfile getProfileByBusinessId(Integer businessId) {
        MoeBookOnlineProfile bookOnlineProfile = bookOnlineProfileMapper.selectByBusinessId(businessId);
        MoeBookOnlineLandingPageConfig landingPageConfig = landingPageConfigService.selectByBusinessId(businessId);
        return getProfile(businessId, bookOnlineProfile, landingPageConfig);
    }

    public MoeBookOnlineProfile getProfile(
            Integer businessId,
            MoeBookOnlineProfile bookOnlineProfile,
            MoeBookOnlineLandingPageConfig landingPageConfig) {
        // 2.0 use ob profile
        if (Objects.isNull(landingPageConfig)) {
            return bookOnlineProfile;
        }
        if (Objects.isNull(businessId)) {
            return null;
        }
        // 3.0 has landing page config, replace biz profile to ob
        InfoIdParams idParams = new InfoIdParams();
        idParams.setInfoId(businessId);
        OBBusinessInfoDTO obBusinessInfoDTO = businessService.getBusinessInfoForOB(idParams);
        return buildBookOnlineProfile(obBusinessInfoDTO, landingPageConfig);
    }

    /**
     * Depending on whether migrate decides to return ob profile or biz info
     *
     * @param businessIdList biz id
     * @return biz ob profile
     */
    public List<MoeBookOnlineProfile> listProfileByBusinessId(List<Integer> businessIdList) {
        if (CollectionUtils.isEmpty(businessIdList)) {
            return Collections.emptyList();
        }
        // Migrated biz
        List<MoeBookOnlineLandingPageConfig> landingPageConfigList =
                landingPageConfigService.listByBusinessId(businessIdList);
        Map<Integer, MoeBookOnlineLandingPageConfig> landingPageConfigMap = landingPageConfigList.stream()
                .collect(Collectors.toMap(MoeBookOnlineLandingPageConfig::getBusinessId, Function.identity()));
        final Map<Integer, OBBusinessInfoDTO> bizInfoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(landingPageConfigList)) {
            CommonIdsParams commonIdsParams = new CommonIdsParams();
            commonIdsParams.setIds(landingPageConfigList.stream()
                    .map(MoeBookOnlineLandingPageConfig::getBusinessId)
                    .toList());
            Map<Integer, OBBusinessInfoDTO> bizInfo = businessService.getBusinessInfoListForOB(commonIdsParams).stream()
                    .collect(Collectors.toMap(OBBusinessInfoDTO::getId, Function.identity()));
            bizInfoMap.putAll(bizInfo);
        }
        // OB profile
        final Map<Integer, MoeBookOnlineProfile> obProfileMap =
                bookOnlineProfileMapper.getBusinessProfileList(businessIdList).stream()
                        .collect(Collectors.toMap(MoeBookOnlineProfile::getBusinessId, Function.identity()));
        return businessIdList.stream()
                .map(businessId -> {
                    // migrated
                    OBBusinessInfoDTO obBusinessInfoDTO = bizInfoMap.get(businessId);
                    if (Objects.nonNull(obBusinessInfoDTO)) {
                        return buildBookOnlineProfile(obBusinessInfoDTO, landingPageConfigMap.get(businessId));
                    }
                    // no migrate
                    return obProfileMap.get(businessId);
                })
                .filter(Objects::nonNull)
                .toList();
    }

    private MoeBookOnlineProfile buildBookOnlineProfile(
            OBBusinessInfoDTO obBusinessInfoDTO, MoeBookOnlineLandingPageConfig landingPageConfig) {
        MoeBookOnlineProfile bookOnlineProfile = new MoeBookOnlineProfile();
        bookOnlineProfile.setBusinessId(obBusinessInfoDTO.getId());
        bookOnlineProfile.setBusinessName(obBusinessInfoDTO.getBusinessName());
        bookOnlineProfile.setWebsite(obBusinessInfoDTO.getWebsite());
        bookOnlineProfile.setBusinessEmail(obBusinessInfoDTO.getOwnerEmail());
        bookOnlineProfile.setAvatarPath(obBusinessInfoDTO.getAvatarPath());
        bookOnlineProfile.setFacebook(obBusinessInfoDTO.getFacebook());
        bookOnlineProfile.setInstagram(obBusinessInfoDTO.getInstagram());
        bookOnlineProfile.setGoogle(obBusinessInfoDTO.getGoogle());
        bookOnlineProfile.setYelp(obBusinessInfoDTO.getYelp());
        bookOnlineProfile.setTiktok(obBusinessInfoDTO.getTiktok());
        bookOnlineProfile.setButtonColor(landingPageConfig.getThemeColor());
        bookOnlineProfile.setDescription(landingPageConfig.getAboutUs());
        bookOnlineProfile.setPhoneNumber("");
        bookOnlineProfile.setAddress("");
        if (StringUtils.hasText(landingPageConfig.getPageComponents())) {
            Map<String, Boolean> componentToEnabled = landingPageConfigHelper.getComponentEnabledMap(landingPageConfig);

            Optional.ofNullable(componentToEnabled.get(LandingPageComponentEnum.COMPONENT_ADDRESS))
                    .filter(Boolean::booleanValue)
                    .ifPresent(enable -> {
                        bookOnlineProfile.setAddress(obBusinessInfoDTO.getAddress());
                        bookOnlineProfile.setAddressDetails(new AddressVO()
                                .setAddress1(obBusinessInfoDTO.getAddress1())
                                .setAddress2(obBusinessInfoDTO.getAddress2())
                                .setCity(obBusinessInfoDTO.getAddressCity())
                                .setState(obBusinessInfoDTO.getAddressState())
                                .setCountry(obBusinessInfoDTO.getAddressCountry())
                                .setZipcode(obBusinessInfoDTO.getAddressZipcode())
                                .setLat(obBusinessInfoDTO.getAddressLat())
                                .setLng(obBusinessInfoDTO.getAddressLng()));
                    });
            Optional.ofNullable(componentToEnabled.get(LandingPageComponentEnum.COMPONENT_CONTACT))
                    .filter(Boolean::booleanValue)
                    .ifPresent(enable -> bookOnlineProfile.setPhoneNumber(obBusinessInfoDTO.getPhoneNumber()));
        }
        return bookOnlineProfile;
    }
}
