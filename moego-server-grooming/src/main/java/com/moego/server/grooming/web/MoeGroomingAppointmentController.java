package com.moego.server.grooming.web;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.common.distributed.LockManager;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.BindingErrorUtil;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.grooming.activity.ActivityLogHelper;
import com.moego.server.grooming.convert.SmartScheduleRequestConverter;
import com.moego.server.grooming.dto.AddResultDTO;
import com.moego.server.grooming.dto.CalendarConflictDTO;
import com.moego.server.grooming.dto.ConflictCheckDTO;
import com.moego.server.grooming.dto.CustomerAppointmentNumInfoDTO;
import com.moego.server.grooming.dto.GroomingAppointmentWaitingListDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.GroomingTicketDetailDTO;
import com.moego.server.grooming.dto.HistoryCommentsDTO;
import com.moego.server.grooming.dto.MoeRepeatInfoDTO;
import com.moego.server.grooming.dto.StaffPetServiceDuration;
import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import com.moego.server.grooming.dto.ob.PetDataDTO;
import com.moego.server.grooming.dto.ss.SmartScheduleResultDto;
import com.moego.server.grooming.dto.ss.SmartScheduleVO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.enums.AppointmentPermissionEnums;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.params.AppointmentBatchCheckParams;
import com.moego.server.grooming.params.AppointmentBlockParams;
import com.moego.server.grooming.params.AppointmentCheckParams;
import com.moego.server.grooming.params.AppointmentParams;
import com.moego.server.grooming.params.AppointmentRepeatModifyParams;
import com.moego.server.grooming.params.AppointmentRepeatParams;
import com.moego.server.grooming.params.ApptReopenParams;
import com.moego.server.grooming.params.BlockRepeatParams;
import com.moego.server.grooming.params.CalendarCheckParams;
import com.moego.server.grooming.params.CancelParams;
import com.moego.server.grooming.params.CheckParams;
import com.moego.server.grooming.params.ColorEditParams;
import com.moego.server.grooming.params.ConfirmParams;
import com.moego.server.grooming.params.DeleteAppointmentParams;
import com.moego.server.grooming.params.EditCommentsParams;
import com.moego.server.grooming.params.EditContextParams;
import com.moego.server.grooming.params.EditIdParams;
import com.moego.server.grooming.params.IdParams;
import com.moego.server.grooming.params.PetDetailParams;
import com.moego.server.grooming.params.TransferAppointmentParams;
import com.moego.server.grooming.params.ss.SmartScheduleRequest;
import com.moego.server.grooming.params.status.StatusRevertParams;
import com.moego.server.grooming.params.status.StatusUpdateParams;
import com.moego.server.grooming.service.BusinessSummaryService;
import com.moego.server.grooming.service.MoeAppointmentQueryService;
import com.moego.server.grooming.service.MoeBookOnlineDepositService;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.MoePetDetailService;
import com.moego.server.grooming.service.MoeRepeatV2Service;
import com.moego.server.grooming.service.OrderService;
import com.moego.server.grooming.service.SmartScheduleService;
import com.moego.server.grooming.service.dto.CustomerAppointmentListDTO;
import com.moego.server.grooming.service.dto.GroomingServiceUpcomingAppointmentCountDto;
import com.moego.server.grooming.service.dto.GroomingStaffUpcomingAppointmentCountDto;
import com.moego.server.grooming.service.ob.SmartScheduleV2Service;
import com.moego.server.grooming.web.dto.Business2021SummaryDto;
import com.moego.server.grooming.web.dto.SummaryDto;
import com.moego.server.grooming.web.dto.ThanksgivingResultDto;
import com.moego.server.grooming.web.dto.ob.LastApptDto;
import com.moego.server.grooming.web.params.ApptEditSaveCheckParams;
import com.moego.server.grooming.web.params.MoeGroomingAppointmentSpecialParam;
import com.moego.server.grooming.web.vo.Business2023SummaryVo;
import com.moego.server.grooming.web.vo.DistanceMatrixUsageSummaryVo;
import com.moego.server.grooming.web.vo.GroomingCustomerQueryVO;
import com.moego.server.grooming.web.vo.SummaryVo;
import com.moego.server.grooming.web.vo.UpcomingPreviewVo;
import com.moego.server.grooming.web.vo.UpdateStatusResultVO;
import com.moego.server.grooming.web.vo.WaitingPetDetailVO;
import com.moego.server.payment.dto.RefundChannelDTO;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grooming")
@RequiredArgsConstructor
@Slf4j
public class MoeGroomingAppointmentController {

    private final MoeGroomingAppointmentService appointmentService;
    private final BusinessSummaryService businessSummaryService;

    private final MoeAppointmentQueryService appointmentQueryService;
    private final SmartScheduleService smartScheduleService;
    private final SmartScheduleV2Service smartScheduleV2Service;
    private final MoeGroomingBookOnlineService moeGroomingBookOnlineService;
    private final MoeBookOnlineDepositService obDepositService;
    private final LockManager moegoLockManager;
    private final OrderService orderService;
    private final MigrateHelper migrateHelper;
    private final PermissionHelper permissionHelper;
    private final MoeRepeatV2Service moeRepeatV2Service;
    private final IPetClient petClient;
    private final MoePetDetailService moePetDetailService;

    @GetMapping("/2021/thanksgiving/petCount")
    @Auth(AuthType.BUSINESS)
    public ThanksgivingResultDto getPetCountUtilChristmas(AuthContext context) {
        return appointmentService.getPetCountUtilChristmas(context.getBusinessId());
    }

    /**
     * Return remaining pets count between two dates.
     *
     * @param startDate startDate
     * @param endDate   endDate
     * @param queryAll  whether query pets of any status appointment (not include cancelled)
     */
    @GetMapping("/pet/count/within")
    @Auth(AuthType.BUSINESS)
    public int getRemainPetCountWithin(
            @RequestParam(required = false) String startDate,
            @RequestParam String endDate,
            @RequestParam(required = false, defaultValue = "false") boolean queryAll,
            AuthContext context) {
        return appointmentService.getRemainPetCountWithin(
                context.getBusinessId(), context.getStaffId(), startDate, endDate, queryAll);
    }

    @GetMapping("/2021/summary")
    @Auth(AuthType.BUSINESS)
    public Business2021SummaryDto getBusiness2021Summary(AuthContext context) {
        return appointmentService.getBusiness2021Summary(context.getBusinessId(), context.getStaffId());
    }

    @GetMapping("/summary")
    @Auth(AuthType.BUSINESS)
    public SummaryVo getBusinessSummary(
            AuthContext context, @RequestParam String startDate, @RequestParam String endDate) {
        Pattern pattern = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}$");
        if (!pattern.matcher(startDate).matches() || !pattern.matcher(endDate).matches()) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "Invalid date format, should be 'yyyy-MM-dd'");
        }

        SummaryDto dto =
                appointmentService.getSummary(context.getBusinessId(), context.getStaffId(), startDate, endDate);

        SummaryVo vo = new SummaryVo();
        vo.setStaffSummary(SummaryVo.StaffSummary.from(dto.getStaffSummary()));
        vo.setBusinessSummary(SummaryVo.BusinessSummary.from(dto.getBusinessSummary()));
        return vo;
    }

    @GetMapping("/2023/summary")
    @Auth(AuthType.BUSINESS)
    public Business2023SummaryVo getBusiness2023Summary(AuthContext context) {
        return businessSummaryService.getBusiness2023Summary(context.getBusinessId(), context.getStaffId());
    }

    // DONE(Frank): customerId-> businessId
    //          petServices.petId-> customerId(checking pet owner)
    //          petServices.serviceId -> businessId
    @PostMapping("/appointment")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<AddResultDTO> addGroomingAppointment(
            AuthContext context, @Valid @RequestBody AppointmentParams createApptParams, BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        checkPetDetailParams(createApptParams.getPetServices());

        appointmentService.checkPetDetailParams(
                context.companyId(),
                context.getBusinessId(),
                createApptParams.getCustomerId(),
                createApptParams.getPetServices());
        createApptParams.setId(null);
        createApptParams.setBusinessId(context.getBusinessId());
        createApptParams.setCompanyId(context.companyId());
        createApptParams.setCreatedById(context.getStaffId());

        return ResponseResult.success(appointmentService.addGroomingAppointment(createApptParams));
    }

    /**
     * 添加waiting 预约
     * DONE(Frank): 入参相同，字段非空时，同 @PostMapping("/appointment")
     *
     * @param context
     * @param appointment
     * @return
     */
    @PostMapping("/appointment/waiting")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.ADD_WAITING_LIST,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#result.data.id",
            details = "#appointment")
    public ResponseResult<AddResultDTO> addGroomingAppointmentWaiting(
            AuthContext context, @RequestBody AppointmentParams appointment) {
        if (appointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }
        appointmentService.checkPetDetailParams(
                context.companyId(),
                context.getBusinessId(),
                appointment.getCustomerId(),
                appointment.getPetServices());
        appointment.setId(null);
        appointment.setBusinessId(context.getBusinessId());
        appointment.setCompanyId(context.companyId());
        appointment.setCreatedById(context.getStaffId());
        return appointmentService.addGroomingAppointmentWaiting(appointment);
    }

    @PutMapping("/appointment/special")
    @Auth(AuthType.BUSINESS)
    /**
     * 修改预约的一些特殊值
     * @param businessId
     * @param param
     */
    public void modifySpecialAppointment(
            AuthContext context, @Validated @RequestBody MoeGroomingAppointmentSpecialParam param) {
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    param.getId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        appointmentService.modifySpecialAppointment(businessId, param);
    }

    // DONE(Frank): 入参相同，字段非空时，同 @PostMapping("/appointment")
    @PutMapping("/appointment")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.EDIT_PET_AND_SERVICES,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#appointment.id",
            details = "#appointment")
    public ResponseResult<Integer> modifyGroomingAppointment(
            AuthContext context, @Validated @RequestBody AppointmentParams appointment, BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        if (appointment == null || appointment.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    appointment.getId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }

        fillNoStartTime(appointment);

        checkPetDetailParams(appointment.getPetServices());
        appointment.setBusinessId(businessId);

        MoeGroomingAppointment groomingAppointment =
                appointmentQueryService.getAppointmentById(businessId, appointment.getId());
        if (groomingAppointment == null || !businessId.equals(appointment.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found appointment");
        }

        appointmentService.checkPetDetailParams(
                context.companyId(), businessId, groomingAppointment.getCustomerId(), appointment.getPetServices());

        // 同时多个 PUT 请求，串行执行后面的保存逻辑
        String resourceCacheKey = moegoLockManager.getResourceKey(LockManager.APPT_PUT_LOCK, appointment.getId());
        String randomValue = CommonUtil.getUuid();
        boolean locked = moegoLockManager.lockWithRetry(resourceCacheKey, randomValue, 10, 100);
        if (!locked) {
            throw bizException(Code.CODE_TOO_MANY_REQUESTS, "Too many requests. Please wait and try again later.");
        }
        try {
            ResponseResult<Integer> result = appointmentService.modifyGroomingAppointment(
                    groomingAppointment, context.getStaffId(), appointment, true);
            // capture appointment automatically:
            if (ServiceEnum.OB_NOT_CONFIRM.equals(groomingAppointment.getBookOnlineStatus())) {
                obDepositService.capturePaymentIntent(appointment.getBusinessId(), appointment.getId());
            }
            return result;
        } finally {
            moegoLockManager.unlock(resourceCacheKey, randomValue);
        }
    }

    @PutMapping("/v2/appointment/repeat")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.REPEAT_RULE,
            resourceId = "#appointmentRepeatModifyParams.id",
            details = "#appointmentRepeatModifyParams")
    public void modifyGroomingAppointmentRepeatV2(
            AuthContext context, @RequestBody @Valid AppointmentRepeatModifyParams appointmentRepeatModifyParams) {
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    appointmentRepeatModifyParams.getId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        appointmentRepeatModifyParams.setBusinessId(businessId);
        appointmentRepeatModifyParams.setStaffId(context.getStaffId());
        appointmentService.modifyAppointmentRepeatV2(appointmentRepeatModifyParams);
    }

    @PostMapping("/appointment/applyCustomService")
    @Auth(AuthType.BUSINESS)
    public String applyCustomService(AuthContext context, @Valid @RequestBody AppointmentParams appointment) {
        Integer petId = null;
        if (appointment == null || CollectionUtils.isEmpty(appointment.getPetServices())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "pet service should not be empty");
        }
        petId = appointment.getPetServices().get(0).getPetId();
        if (PrimitiveTypeUtil.isNullOrZero(petId)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "pet id is not valid.");
        }
        appointment.setBusinessId(context.getBusinessId());
        appointment.setOperatorId(context.getStaffId());
        String resourceCacheKey = moegoLockManager.getResourceKey(LockManager.PET_DETAIL, petId);
        String randomValue = CommonUtil.getUuid();
        try {
            if (moegoLockManager.lock(resourceCacheKey, randomValue)) {
                ThreadPool.execute(() -> {
                    appointmentService.applyingCustomServiceForUnconfirmedAppt(appointment);
                });
            } else {
                throw new CommonException(ResponseCodeEnum.PARALLEL_ERROR, "get lock failed for " + resourceCacheKey);
            }
        } finally {
            moegoLockManager.unlock(resourceCacheKey, randomValue);
        }

        return "This task has been submitted successfully.";
    }

    private static void checkPetDetailParams(List<PetDetailParams> appointmentServiceParams) {
        BigDecimal total = new BigDecimal(0);
        for (PetDetailParams service : appointmentServiceParams) {
            BigDecimal currentServicePrice = service.getServicePrice();
            if (Objects.isNull(currentServicePrice)) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid service price.");
            }
            total = total.add(currentServicePrice);

            if (Objects.isNull(service.getEnableOperation())
                    || Objects.equals(Boolean.FALSE, service.getEnableOperation())) {
                continue;
            }

            if (Objects.equals(Boolean.TRUE, service.getEnableOperation())
                    && CollectionUtils.isEmpty(service.getOperationList())) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid operation.");
            }

            // check service price
            BigDecimal currentOperationServiceTotal = service.getOperationList().stream()
                    .map(GroomingServiceOperationDTO::getPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (currentOperationServiceTotal.compareTo(currentServicePrice) != 0) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid service price.");
            }

            // check service start time
            int startTime = service.getStartTime();
            service.getOperationList().stream()
                    .filter(operation -> operation.getStartTime() < startTime)
                    .findFirst()
                    .ifPresent(operation -> {
                        throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid operation start time.");
                    });

            // check service time
            int endTime = startTime + service.getServiceTime();
            service.getOperationList().stream()
                    .filter(operation -> operation.getStartTime() + operation.getDuration() > endTime)
                    .findFirst()
                    .ifPresent(operation -> {
                        throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid operation duration.");
                    });
        }

        if (total.compareTo(BigDecimal.ZERO) < 0) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid service price.");
        }
    }

    /**
     * 编辑waiting 预约，仍为waiting
     *
     * @param context
     * @param appointment
     * @return
     */
    @PutMapping("/appointment/waiting/still")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#appointment.id",
            details = "#appointment")
    public ResponseResult<Integer> modifyGroomingAppointmentWaiting(
            AuthContext context, @RequestBody AppointmentParams appointment) {
        if (appointment == null || appointment.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    appointment.getId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        appointment.setBusinessId(businessId);
        return appointmentService.modifyGroomingAppointmentWaiting(context.getStaffId(), appointment);
    }

    /**
     * 真实开始时间
     *
     * @param context
     * @param checkParams
     * @return
     */
    @Deprecated // use PUT /appointment/status instead
    @PutMapping("/appointment/checkin")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Integer> checkInGroomingAppointment(
            AuthContext context, @Validated @RequestBody CheckParams checkParams, BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        if (checkParams == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "checkParams is not null");
        }
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    checkParams.getId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        checkParams.setBusinessId(businessId);
        return appointmentService.checkInOrOutGroomingAppointment(checkParams, true);
    }

    /**
     * 真实结束时间
     *
     * @param context
     * @param checkParams
     * @return
     */
    @Deprecated // use PUT /appointment/status instead
    @PutMapping("/appointment/checkout")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.CHECK_OUT,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#checkParams.id",
            details = "#checkParams")
    public ResponseResult<Integer> checkOutGroomingAppointment(
            AuthContext context, @Validated @RequestBody CheckParams checkParams, BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        if (checkParams == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "checkParams is not null");
        }
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    checkParams.getId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        checkParams.setBusinessId(businessId);
        return appointmentService.checkInOrOutGroomingAppointment(checkParams, false);
    }

    /**
     * 修改消息推送
     *
     * @param context
     * @param editIdParams
     * @return
     */
    @PutMapping("/appointment/notification")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.PUSH_NOTIFICATION,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#editIdParams.id",
            details = "#editIdParams")
    public ResponseResult<Integer> pushNotification(AuthContext context, @RequestBody EditIdParams editIdParams) {
        if (editIdParams == null || editIdParams.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }
        editIdParams.setBusinessId(context.getBusinessId());
        return appointmentService.editAppointmentNotification(editIdParams);
    }

    /**
     * comments修改
     * DONE(Frank): ticketId->businessId
     *
     * @param context
     * @param editCommentsParams
     * @return
     */
    @PutMapping("/appointment/comments")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.UPDATE_COMMENTS,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#editCommentsParams.ticketId",
            details = "#editCommentsParams")
    public ResponseResult<Integer> updateComments(
            AuthContext context, @RequestBody EditCommentsParams editCommentsParams) {
        if (editCommentsParams == null || editCommentsParams.getTicketId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "ticketId is not null");
        }

        editCommentsParams.setBusinessId(context.getBusinessId());
        editCommentsParams.setCompanyId(context.companyId());
        editCommentsParams.setAccountId(context.getStaffId());
        editCommentsParams.setStaffId(context.getStaffId());
        if (Objects.isNull(editCommentsParams.getPetId())) {
            editCommentsParams.setPetId(0L);
        }
        int updateRows =
                appointmentService.editAppointmentComments(editCommentsParams, migrateHelper.isMigrate(context));
        return ResponseResult.success(updateRows);
    }

    /**
     * alertNotes修改
     * DONE(Frank): ticketId->businessId
     *
     * @param context
     * @param editCommentsParams
     * @return
     */
    @PutMapping("/appointment/alertNotes")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.UPDATE_ALERT_NOTES,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#editCommentsParams.ticketId",
            details = "#editCommentsParams")
    public ResponseResult<Integer> updateAlertNotes(
            AuthContext context, @RequestBody EditCommentsParams editCommentsParams) {
        if (editCommentsParams == null || editCommentsParams.getTicketId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "ticketId is not null");
        }
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    editCommentsParams.getTicketId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        editCommentsParams.setBusinessId(businessId);
        editCommentsParams.setAccountId(context.getStaffId());
        return appointmentService.editAppointmentAlertNotes(editCommentsParams);
    }

    /**
     * 批量alertNotes修改
     * DONE(Frank): ticketId->businessId
     *
     * @param context
     * @param editContextParams
     * @param type
     * @return
     */
    @PutMapping("/appointment/alertNotes/repeat")
    @Auth(AuthType.BUSINESS)
    @Operation(summary = "支持alertnotes 基于repeat批量修改")
    public void updateAlertNotesWithRepeat(
            AuthContext context,
            @RequestParam Integer type,
            @Validated @RequestBody EditContextParams editContextParams) {
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    editContextParams.getAppointmentId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        var details = ActivityLogHelper.getAppointmentRepeatParamsLogDTO(null, type, editContextParams);
        ActivityLogRecorder.recordRoot(
                AppointmentAction.UPDATE_ALERT_NOTES,
                ResourceType.APPOINTMENT,
                editContextParams.getAppointmentId(),
                details);

        appointmentService.editAppointmentAlertNotesWithRepeat(editContextParams, type, businessId);
    }

    /**
     * 修改是否支付
     *
     * @param context
     * @param editIdParams
     * @return
     */
    @PutMapping("/appointment/pay")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.UPDATE_PAYMENT_STATUS,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#editIdParams.id",
            details = "#editIdParams")
    public ResponseResult<Integer> paid(AuthContext context, @RequestBody EditIdParams editIdParams) {
        if (editIdParams == null || editIdParams.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    editIdParams.getId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        editIdParams.setBusinessId(businessId);
        return appointmentService.editAppointmentPaid(editIdParams);
    }

    /**
     * 关闭预约
     * DONE(Frank): editIdParams.id(ticketId)->businessId
     *
     * @param context
     * @param editIdParams
     * @return
     */
    @PutMapping("/appointment/cancel")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.CANCEL,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#editIdParams.id",
            details = "#editIdParams")
    public ResponseResult<Integer> cancel(AuthContext context, @RequestBody CancelParams editIdParams) {
        if (editIdParams == null || editIdParams.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }
        Integer businessId = appointmentService.checkPermissionToAppointment(
                context.companyId(),
                context.staffId(),
                editIdParams.getId().longValue(),
                AppointmentPermissionEnums.CANCEL);
        if (editIdParams.getNoShow() == null) {
            editIdParams.setNoShow(GroomingAppointmentEnum.NO_SHOW_FALSE);
        }

        editIdParams.setBusinessId(businessId);
        editIdParams.setAccountId(context.getStaffId());
        return ResponseResult.success(
                appointmentService.cancelAppointmentRepeat(editIdParams.getRepeatType(), businessId, editIdParams));
        //        return appointmentService.editAppointmentCancel(editIdParams);
    }

    /**
     * reopen预约
     * 1、设置 payment 状态为 partial paid
     * 2、设置 appointment 状态为 confirmed
     * 3、清除 check out 记录
     *
     * @param context
     * @param apptReopenParams params
     * @param bindingResult    bingResult
     * @return
     */
    @Deprecated
    @PutMapping("/appointment/reopen")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.REOPEN,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#apptReopenParams.groomingId",
            details = "#apptReopenParams")
    public ResponseResult<Boolean> apptReopen(
            AuthContext context,
            @Validated @RequestBody ApptReopenParams apptReopenParams,
            BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    apptReopenParams.getGroomingId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        return ResponseResult.success(appointmentService.apptReopen(apptReopenParams.getGroomingId(), businessId));
    }

    /**
     * 确认预约
     *
     * @param context
     * @param editIdParams
     * @return
     */
    @PutMapping("/appointment/confirm")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.CONFIRM,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#editIdParams.id",
            details = "#editIdParams")
    public ResponseResult<Integer> confirm(AuthContext context, @RequestBody ConfirmParams editIdParams) {
        if (editIdParams == null || editIdParams.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    editIdParams.getId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        editIdParams.setBusinessId(businessId);
        editIdParams.setAccountId(context.getStaffId());
        return appointmentService.editAppointmentConfirm(editIdParams);
    }

    @PutMapping("/appointment/color")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.UPDATE_COLOR,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#editIdParams.id",
            details = "#editIdParams")
    public ResponseResult<Integer> changeColor(AuthContext context, @RequestBody ColorEditParams editIdParams) {
        if (editIdParams == null || editIdParams.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    editIdParams.getId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        editIdParams.setBusinessId(businessId);
        return appointmentService.editAppointmentColorCode(editIdParams);
    }

    @PutMapping("/appointment/color/repeat")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.UPDATE_COLOR,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#editIdParams.id",
            details = "#editIdParams")
    public void modifyAppointmentColorRepeat(AuthContext context, @RequestBody @Valid ColorEditParams editIdParams) {
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    editIdParams.getId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        editIdParams.setBusinessId(businessId);
        appointmentService.modifyAppointmentColorRepeat(editIdParams);
    }

    /**
     * 变更状态为unconfirm
     *
     * @param context
     * @param editIdParams
     * @return
     */
    @PutMapping("/appointment/unconfirm")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.UNCONFIRM,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#editIdParams.id",
            details = "#editIdParams")
    public ResponseResult<Integer> unconfirm(AuthContext context, @RequestBody EditIdParams editIdParams) {
        if (editIdParams == null || editIdParams.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    editIdParams.getId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        editIdParams.setBusinessId(businessId);
        editIdParams.setAccountId(context.getStaffId());
        return appointmentService.editAppointmentUnConfirm(editIdParams);
    }

    @PutMapping("/appointment/reverse/checkin")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.REVERSE_CHECK_IN,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#editIdParams.id",
            details = "#editIdParams")
    public ResponseResult<Integer> reverseCheckIn(AuthContext context, @RequestBody EditIdParams editIdParams) {
        if (editIdParams == null || editIdParams.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    editIdParams.getId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        editIdParams.setBusinessId(businessId);
        editIdParams.setAccountId(context.getStaffId());
        return appointmentService.editAppointmentUnCheckIn(editIdParams);
    }

    /**
     * 完成预约
     *
     * @param context
     * @param editIdParams
     * @return
     */
    @PutMapping("/appointment/finish")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.FINISH,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#editIdParams.id",
            details = "#editIdParams")
    public ResponseResult<Integer> finish(AuthContext context, @RequestBody EditIdParams editIdParams) {
        if (editIdParams == null || editIdParams.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    editIdParams.getId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        editIdParams.setBusinessId(businessId);
        return appointmentService.editAppointmentFinish(editIdParams);
    }

    /**
     * 加入或移出waiting
     *
     * @param context
     * @param editIdParams
     * @return
     */
    @PutMapping("/appointment/waiting")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.UPDATE_WAITING_STATUS,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#editIdParams.id",
            details = "#editIdParams")
    public ResponseResult<Integer> toWaiting(AuthContext context, @RequestBody EditIdParams editIdParams) {
        if (editIdParams == null || editIdParams.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    editIdParams.getId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        editIdParams.setBusinessId(businessId);
        editIdParams.setAccountId(context.getStaffId());
        return appointmentService.toWaiting(editIdParams);
    }

    /**
     * 弹窗预约详情
     *
     * @param context
     * @param id
     * @return
     */
    @GetMapping("/appointment/detail/pup")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<GroomingTicketDetailDTO> queryTicketDetail(
            AuthContext context,
            @RequestParam Integer id,
            @RequestParam(required = false, defaultValue = "false")
                    @Schema(description = "false only filters grooming, true may include", type = "boolean")
                    boolean filterGroomingOnlyService) {
        List<Integer> serviceItems;
        if (filterGroomingOnlyService) {
            serviceItems = List.of(
                    ServiceItemEnum.GROOMING.getServiceItem(),
                    ServiceItemEnum.BOARDING.getServiceItem(),
                    ServiceItemEnum.DAYCARE.getServiceItem(),
                    ServiceItemEnum.DOG_WALKING.getServiceItem());
        } else {
            serviceItems = List.of(ServiceItemEnum.GROOMING.getServiceItem());
        }

        return ResponseResult.success(appointmentService.queryTicketDetail(
                context.companyId(),
                context.getBusinessId(),
                id,
                context.getStaffId(),
                migrateHelper.isMigrate(context),
                serviceItems));
    }

    /**
     * history comments
     *
     * @param context
     * @param customerId
     * @return
     */
    @GetMapping("/appointment/comments/history")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<HistoryCommentsDTO>> queryCustomerHistoryComments(
            AuthContext context, @RequestParam Integer customerId, @RequestParam(required = false) Long petId) {
        return ResponseResult.success(
                appointmentService.queryCustomerHistoryComments(context.getBusinessId(), customerId, petId));
    }

    /**
     * 校验员工是否与已有服务冲突
     *
     * @param appointmentCheckParams
     * @return
     */
    @PutMapping("/appointment/conflict/check")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<ConflictCheckDTO> checkConflict(
            AuthContext context,
            @Validated @RequestBody AppointmentCheckParams appointmentCheckParams,
            BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        appointmentCheckParams.setBusinessId(context.getBusinessId());
        appointmentCheckParams.setTokenStaffId(context.getStaffId());
        appointmentCheckParams.setAppointmentDate(appointmentCheckParams.getAppointmentTime());
        return ResponseResult.success(appointmentService.checkConflict(appointmentCheckParams));
    }

    /**
     * 校验多个员工是否与已有服务冲突
     */
    @PostMapping("/appointment/conflict/check/batch")
    @Auth(AuthType.BUSINESS)
    public List<ConflictCheckDTO> batchCheckConflict(@RequestBody @Valid AppointmentBatchCheckParams param) {
        return appointmentService.batchCheckConflict(
                param.getAppointmentCheckParamList(),
                AuthContext.get().getBusinessId(),
                AuthContext.get().getStaffId());
    }

    /**
     * 日历页面检查员工是否有时间冲突
     *
     * @param calendarCheckParams
     * @return
     */
    @PutMapping("/appointment/conflict/calendar")
    @Auth(AuthType.BUSINESS)
    public CalendarConflictDTO checkCalendarConflict(
            AuthContext context, @Valid @RequestBody CalendarCheckParams calendarCheckParams) {
        return appointmentService.checkCalendarConflict(context.getBusinessId(), calendarCheckParams);
    }

    /**
     * 添加block
     *
     * @param appointmentBlockParams
     * @return
     */
    @PostMapping("/appointment/block")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.CREATE,
            resourceType = ResourceType.BLOCK,
            resourceId = "#result.data.id",
            details = "#appointmentBlockParams")
    public ResponseResult<AddResultDTO> addAppointmentBlock(
            AuthContext context,
            @RequestBody AppointmentBlockParams appointmentBlockParams,
            BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        if (migrateHelper.isMigrate(context)) {
            permissionHelper.checkPermission(
                    context.companyId(),
                    Set.of(context.businessId()),
                    context.staffId(),
                    PermissionEnums.CREATE_AND_EDIT_BLOCK);
        }
        appointmentBlockParams.setTicketId(null);
        appointmentBlockParams.setRepeatId(null);
        appointmentBlockParams.setAppointmentDate(appointmentBlockParams.getAppointmentTime());
        appointmentBlockParams.setTokenBusinessId(context.getBusinessId());
        appointmentBlockParams.setTokenStaffId(context.getStaffId());
        appointmentBlockParams.setTokenCompanyId(context.companyId());
        return ResponseResult.success(
                appointmentService.addAppointmentBlock(appointmentBlockParams)); // controller block insert
    }

    /**
     * repeat 新增block
     *
     * @param context
     * @param blockRepeatParams
     * @return
     */
    @PostMapping("/appointment/block/repeat")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(action = "Batch Create", resourceType = ResourceType.BLOCK, details = "#blockRepeatParams")
    public ResponseResult<Integer> addAppointmentBlockRepeat(
            AuthContext context, @Valid @RequestBody BlockRepeatParams blockRepeatParams) {
        if (migrateHelper.isMigrate(context)) {
            permissionHelper.checkPermission(
                    context.companyId(),
                    Set.of(context.businessId()),
                    context.staffId(),
                    PermissionEnums.CREATE_AND_EDIT_BLOCK);
        }
        return appointmentService.addAppointmentBlockRepeat(
                context.getBusinessId(), context.companyId(), context.getStaffId(), blockRepeatParams);
    }

    /**
     * 编辑block
     *
     * @param context
     * @param appointmentBlockParams
     * @param bindingResult
     * @return
     */
    @PutMapping("/appointment/block")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.BLOCK,
            resourceId = "#appointmentBlockParams.ticketId",
            details = "#appointmentBlockParams")
    public ResponseResult<Integer> modifyAppointmentBlock(
            AuthContext context,
            @Validated @RequestBody AppointmentBlockParams appointmentBlockParams,
            BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        if (appointmentBlockParams.getTicketId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "ticketId is not null");
        }
        if (migrateHelper.isMigrate(context)) {
            permissionHelper.checkPermission(
                    context.companyId(),
                    Set.of(context.businessId()),
                    context.staffId(),
                    PermissionEnums.CREATE_AND_EDIT_BLOCK);
        }
        appointmentBlockParams.setAppointmentDate(appointmentBlockParams.getAppointmentTime());
        appointmentBlockParams.setRepeatId(null);
        appointmentBlockParams.setTokenBusinessId(context.getBusinessId());
        appointmentBlockParams.setTokenStaffId(context.getStaffId());
        return appointmentService.modifyAppointmentBlock(appointmentBlockParams);
    }

    /**
     * repeat block 修改
     *
     * @param context
     * @param type
     * @param appointmentBlockParams
     * @return
     */
    @PutMapping("/appointment/block/repeat")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Integer> modifyAppointmentBlockRepeat(
            AuthContext context,
            @RequestParam Integer type,
            @Valid @RequestBody AppointmentBlockParams appointmentBlockParams) {
        var details = ActivityLogHelper.getAppointmentRepeatParamsLogDTO(null, type, appointmentBlockParams);
        ActivityLogRecorder.recordRoot(Action.BATCH_UPDATE, ResourceType.BLOCK, null, details);
        if (migrateHelper.isMigrate(context)) {
            permissionHelper.checkPermission(
                    context.companyId(),
                    Set.of(context.businessId()),
                    context.staffId(),
                    PermissionEnums.CREATE_AND_EDIT_BLOCK);
        }
        if (appointmentBlockParams == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "params is not null");
        }

        if (appointmentBlockParams.getTicketId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "ticket is not null");
        }

        if (appointmentBlockParams.getRepeatId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "repeatId is not null");
        }

        appointmentBlockParams.setAppointmentDate(appointmentBlockParams.getAppointmentTime());
        appointmentBlockParams.setTokenBusinessId(context.getBusinessId());
        appointmentBlockParams.setTokenStaffId(context.getStaffId());
        return appointmentService.modifyAppointmentBlockRepeat(type, appointmentBlockParams);
    }

    /**
     * 删除block
     *
     * @param context
     * @param idParams
     * @return
     */
    @DeleteMapping("/appointment/block")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.DELETE,
            resourceType = ResourceType.BLOCK,
            resourceId = "#idParams.id",
            details = "#idParams")
    public ResponseResult<Integer> deleteAppointmentBlock(AuthContext context, @RequestBody IdParams idParams) {
        if (idParams == null || idParams.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }
        if (migrateHelper.isMigrate(context)) {
            permissionHelper.checkPermission(
                    context.companyId(),
                    Set.of(context.businessId()),
                    context.staffId(),
                    PermissionEnums.CREATE_AND_EDIT_BLOCK);
        }
        idParams.setBusinessId(context.getBusinessId());
        return ResponseResult.success(
                appointmentService.deleteAppointmentBlockAndRepeatBlock(idParams, context.getBusinessId()));
    }

    /**
     * repeat 新增appt
     *
     * @param context
     * @param appointmentRepeatParams
     * @param bindingResult
     * @return
     */
    @PostMapping("/appointment/repeat")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<AddResultDTO> addAppointmentRepeat(
            AuthContext context,
            @RequestParam(required = false) Integer appointmentId,
            @Validated @RequestBody List<AppointmentRepeatParams> appointmentRepeatParams,
            BindingResult bindingResult) {
        Integer businessId = context.getBusinessId();
        if (appointmentId != null && migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    appointmentId.longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        var details = ActivityLogHelper.getAppointmentRepeatParamsLogDTO(appointmentId, null, appointmentRepeatParams);
        ActivityLogRecorder.recordRoot(
                AppointmentAction.CREATE_REPEATED, ResourceType.APPOINTMENT, appointmentId, details);

        BindingErrorUtil.handleResult(bindingResult);

        for (AppointmentRepeatParams e : appointmentRepeatParams) {
            if (e.getPetServices() == null || e.getPetServices().size() == 0) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "pet service can't be empty");
            }
            checkPetDetailParams(e.getPetServices());
            if (e.getRepeatId() == null) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "repeatId is not null");
            }

            e.setBusinessId(businessId);
            e.setCompanyId(context.companyId());
            e.setCreatedById(context.getStaffId());
        }

        return ResponseResult.success(
                appointmentService.addAppointmentRepeat(appointmentId, appointmentRepeatParams, businessId));
    }

    /**
     * Multiple days 新增预约
     *
     * @param context
     * @param appointmentRepeatParams
     * @param bindingResult
     * @return
     */
    @PostMapping("/appointment/multiple")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<AddResultDTO> addAppointmentMultiple(
            AuthContext context,
            @Valid @RequestBody @Size(max = 100) List<AppointmentRepeatParams> appointmentRepeatParams,
            BindingResult bindingResult) {
        var details = ActivityLogHelper.getAppointmentRepeatParamsLogDTO(null, null, appointmentRepeatParams);
        ActivityLogRecorder.recordRoot(AppointmentAction.CREATE_REPEATED, ResourceType.APPOINTMENT, null, details);

        BindingErrorUtil.handleResult(bindingResult);
        for (AppointmentRepeatParams e : appointmentRepeatParams) {
            if (e.getPetServices() == null || e.getPetServices().size() == 0) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "pet service can't be empty");
            }
            checkPetDetailParams(e.getPetServices());
            e.setRepeatId(0);
            e.setBusinessId(context.getBusinessId());
            e.setCompanyId(context.companyId());
            e.setCreatedById(context.getStaffId());
        }

        return ResponseResult.success(
                appointmentService.addAppointmentRepeat(null, appointmentRepeatParams, context.getBusinessId()));
    }

    /**
     * repeat 修改
     *
     * @param context
     * @param type
     * @param appointmentParams
     * @param bindingResult
     * @return
     */
    @PutMapping("/appointment/repeat")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Integer> modifyAppointmentRepeat(
            AuthContext context,
            @RequestParam Integer type,
            @Validated @RequestBody AppointmentParams appointmentParams,
            BindingResult bindingResult) {
        Integer businessId = context.getBusinessId();
        if (migrateHelper.isMigrate(context.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    context.companyId(),
                    context.staffId(),
                    appointmentParams.getId().longValue(),
                    AppointmentPermissionEnums.UPDATE);
        }
        var details =
                ActivityLogHelper.getAppointmentRepeatParamsLogDTO(appointmentParams.getId(), type, appointmentParams);
        ActivityLogRecorder.recordRoot(Action.UPDATE, ResourceType.APPOINTMENT, appointmentParams.getId(), details);

        BindingErrorUtil.handleResult(bindingResult);
        if (appointmentParams.getPetServices() == null
                || appointmentParams.getPetServices().size() == 0) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "pet service can't be empty");
        }
        if (appointmentParams.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }

        if (appointmentParams.getRepeatId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "repeatId is not null");
        }

        checkPetDetailParams(appointmentParams.getPetServices());

        // customer不得修改
        appointmentParams.setCustomerId(null);
        appointmentParams.setBusinessId(businessId);
        return appointmentService.modifyAppointmentRepeat(type, businessId, context.getStaffId(), appointmentParams);
    }

    /**
     * 删除repeat预约接口, 普通预约目前仅能删除waiting_list以及canceled状态的预约
     *
     * @param context
     * @param deleteAppointmentParams
     * @return
     */
    @DeleteMapping("/appointment")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.BATCH_DELETE,
            resourceType = ResourceType.APPOINTMENT,
            details = "#deleteAppointmentParams",
            beforeInvocation = true)
    public ResponseResult<Integer> deleteAppointment(
            AuthContext context, @RequestBody DeleteAppointmentParams deleteAppointmentParams) {
        if (migrateHelper.isMigrate(context.companyId())) {
            permissionHelper.checkPermission(
                    context.companyId(),
                    Set.of(context.businessId()),
                    context.staffId(),
                    PermissionEnums.CANCEL_APPOINTMENT);
        }

        // 筛选waiting_list、canceled状态、repeat的预约
        if (!CollectionUtils.isEmpty(deleteAppointmentParams.getIds())) {
            List<Integer> ids = deleteAppointmentParams.getIds();
            deleteAppointmentParams.setIds(appointmentService.filterValidAppointmentIds(context.getCompanyId(), ids));
        }

        return ResponseResult.success(
                appointmentService.deleteAppointment(context.getBusinessId(), deleteAppointmentParams));
    }

    /**
     * business端 查询当前顾客的预约信息
     *
     * @return
     */
    @PostMapping("/customer/appointment/list/v2")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<CustomerAppointmentListDTO> queryGroomingCustomerAppointmentV2(
            AuthContext context, @RequestBody @Valid GroomingCustomerQueryVO queryVo) {
        queryVo.setBusinessId(context.getBusinessId());
        queryVo.setCompanyId(context.companyId());
        var tokenStaffId = context.getStaffId();
        /*
        兼容旧版本，如果查询条件中包含CONFIRMED，则查询CONFIRMED, CHECK_IN, READY
         */
        if (Objects.isNull(queryVo.getAppointmentStatusList()) && !Objects.isNull(queryVo.getAppointmentStatus())) {
            List<AppointmentStatusEnum> statusList = new ArrayList<>();
            queryVo.getAppointmentStatus().forEach(s -> {
                statusList.add(AppointmentStatusEnum.fromValue(s.byteValue()));
                if (s == AppointmentStatusEnum.CONFIRMED.ordinal()) {
                    statusList.add(AppointmentStatusEnum.CHECK_IN);
                    statusList.add(AppointmentStatusEnum.READY);
                }
            });
            queryVo.setAppointmentStatusList(statusList);
        }
        if (CollectionUtils.isEmpty(queryVo.getServiceItems())) {
            queryVo.setServiceItems(List.of(
                    ServiceItemEnum.GROOMING.getServiceItem(),
                    ServiceItemEnum.BOARDING.getServiceItem(),
                    ServiceItemEnum.DAYCARE.getServiceItem(),
                    ServiceItemEnum.EVALUATION.getServiceItem(),
                    ServiceItemEnum.DOG_WALKING.getServiceItem()));
        }
        queryVo.setServiceTypeIncludes(ServiceItemEnum.convertServiceItemListToBitValueList(queryVo.getServiceItems()));
        // -------------------------------
        return appointmentService.queryGroomingCustomerAppointment(queryVo, tokenStaffId);
    }

    /**
     * @param context
     * @param customerId
     * @param type       1 upcoming, 2 history, 3 waiting, 4 cancelled, 5 no show
     * @param orderType  1 正序, 2 倒序 (appointmentDate和appointmentStartTime)
     * @return
     * @link /customer/appointment/list/v2 update: 使用requset body接收参数
     */
    @PostMapping("/customer/appointment/list")
    @Auth(AuthType.BUSINESS)
    @Deprecated
    public ResponseResult<CustomerAppointmentListDTO> queryGroomingCustomerAppointment(
            AuthContext context,
            @RequestParam Integer customerId,
            @RequestParam(required = false, defaultValue = "2") Integer orderType,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            String appointmentTime,
            Integer time,
            Integer type,
            Boolean skipCancel,
            @RequestParam(required = false, defaultValue = "false")
                    @Schema(description = "false only filters grooming, true may include", type = "boolean")
                    boolean filterGroomingOnlyService) {
        List<Integer> serviceItems;
        List<Integer> serviceTypeIncludes;
        if (filterGroomingOnlyService) {
            serviceTypeIncludes = ServiceItemEnum.getBitValueListByServiceItem(ServiceItemEnum.GROOMING);
            serviceItems = List.of(
                    ServiceItemEnum.GROOMING.getServiceItem(),
                    ServiceItemEnum.BOARDING.getServiceItem(),
                    ServiceItemEnum.DAYCARE.getServiceItem(),
                    ServiceItemEnum.DOG_WALKING.getServiceItem());
        } else {
            serviceTypeIncludes = List.of(ServiceItemEnum.GROOMING.getBitValue());
            serviceItems = List.of(ServiceItemEnum.GROOMING.getServiceItem());
        }

        GroomingCustomerQueryVO groomingCustomerQueryVO = GroomingCustomerQueryVO.builder()
                .businessId(context.getBusinessId())
                .customerId(customerId)
                .orderType(orderType)
                .type(type)
                .skipCancel(skipCancel)
                .pageSize(pageSize)
                .pageNum(pageNum)
                .appointmentTime(appointmentTime)
                .time(time)
                .serviceItems(serviceItems)
                .serviceTypeIncludes(serviceTypeIncludes)
                .build();

        return queryGroomingCustomerAppointmentV2(context, groomingCustomerQueryVO);
    }

    @GetMapping("/appointment/waiting/list")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<GroomingAppointmentWaitingListDTO>> queryAppointmentWaitingList(
            AuthContext context,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        WaitingPetDetailVO waitingPetDetailVO = new WaitingPetDetailVO();
        if (StringUtils.isNotBlank(startTime)) {
            waitingPetDetailVO.setStartDate(startTime);
        }
        if (StringUtils.isNotBlank(endTime)) {
            waitingPetDetailVO.setEndDate(endTime);
        }

        waitingPetDetailVO.setBusinessId(context.getBusinessId());

        return appointmentService.queryAppointmentWaitingList(waitingPetDetailVO, context.getStaffId());
    }

    /**
     * staff 特殊操作，可以将某个staff的预约，转移给另一个staff
     * DONE(Frank): targetStaffId->businessId
     *
     * @return
     */
    @PostMapping("/appointment/transfer")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.TRANSFER_UPCOMING_APPOINTMENT,
            resourceType = ResourceType.APPOINTMENT,
            details = "#apptParam",
            beforeInvocation = true)
    public ResponseResult<Boolean> transferAppointment(
            AuthContext context, @RequestBody @Valid TransferAppointmentParams apptParam) {
        appointmentService.checkBusinessStaff(context.getBusinessId(), apptParam.getTargetStaffId());
        return ResponseResult.success(appointmentService.transferAppointment(context.getBusinessId(), apptParam));
    }

    /**
     * todo 查询customer 给ob
     *
     * @param customerId
     * @return
     */
    @PostMapping("/bookOnline/customer/lastAppt")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<LastApptDto> getCustomerLastAppointmentForBookOnline(
            @RequestParam String businessName, @RequestParam Integer customerId) {
        Integer businessId = moeGroomingBookOnlineService.getBusinessIdByBookOnlineName(businessName);
        LastApptDto returnMap = new LastApptDto();
        returnMap.setLastAppt(appointmentService.getCustomerLastAppointmentForOnlineBooking(customerId, businessId));
        return ResponseResult.success(returnMap);
    }

    /**
     * 查询staff 预约统计
     *
     * @param context
     * @return
     */
    @GetMapping("/appointment/staff/num")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<GroomingStaffUpcomingAppointmentCountDto> queryStaffUpComingAppointCount(
            AuthContext context, @RequestParam Integer staffId) {
        return appointmentService.queryStaffUpComingAppointCount(context.getBusinessId(), staffId);
    }

    /**
     * 查询service 预约统计
     *
     * @param serviceId
     * @return
     */
    @Deprecated
    @GetMapping("/appointment/service/num")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<GroomingServiceUpcomingAppointmentCountDto> queryServiceUpComingAppointCount(
            AuthContext context, @RequestParam Integer serviceId) {
        return appointmentService.queryServiceUpComingAppointCount(context.getBusinessId(), serviceId);
    }

    /**
     * 统计 staff upcoming operation 数量
     *
     * @param context
     * @param sourceStaffId
     * @param targetStaffId
     * @return operation quantity
     */
    @Deprecated
    @GetMapping("/appointment/operation/num")
    @Auth(AuthType.BUSINESS)
    public Integer queryStaffUpComingOperationCount(
            AuthContext context, @RequestParam Integer sourceStaffId, @RequestParam Integer targetStaffId) {
        return appointmentService.queryStaffUpComingOperationCount(
                context.getBusinessId(), sourceStaffId, targetStaffId);
    }

    /**
     * @param previewVo
     * @return
     */
    @PostMapping("/upcoming/preview/list")
    @Auth(AuthType.BUSINESS)
    public CustomerAppointmentListDTO queryCustomerUpComingAppointForClientShare(
            AuthContext context, @Valid @RequestBody UpcomingPreviewVo previewVo) {
        previewVo.setIsPreview(true);
        return appointmentService.queryPreviewListForCustomer(context.companyId(), context.getBusinessId(), previewVo);
    }

    @Deprecated
    @PostMapping("/appointment/smartSchedule")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<SmartScheduleVO> smartSchedule(
            AuthContext context, @RequestBody SmartScheduleRequest request) {
        return ResponseResult.success(SmartScheduleVO.builder().build());
    }

    @PostMapping("/v2/appointment/smartSchedule")
    @Auth(AuthType.BUSINESS)
    public SmartScheduleResultDto smartSchedule2(
            AuthContext context, @Valid @RequestBody SmartScheduleRequest request) {
        if (!CollectionUtils.isEmpty(request.getStaffPetServiceDurationList())) {
            // list to map[staffId, duration]
            request.setStaffServiceDuration(request.getStaffPetServiceDurationList().stream()
                    .filter(duration -> duration.getStaffId() != null)
                    .collect(Collectors.toMap(
                            StaffPetServiceDuration::getStaffId,
                            StaffPetServiceDuration::getTotalDuration,
                            (existing, replacement) -> existing)));
        }
        var businessId = context.getBusinessId();
        var smartScheduleV2Request = SmartScheduleRequestConverter.INSTANCE.toSmartScheduleV2Request(request);

        if (CommonUtil.isNormal(request.getFilterGroomingId())) {
            MoeGroomingAppointment groomingAppointment =
                    appointmentQueryService.getAppointmentById(businessId, request.getFilterGroomingId());
            if (Objects.nonNull(groomingAppointment)) {
                var moeGroomingPetDetails =
                        moePetDetailService.queryPetDetailsByAppointmentId(request.getFilterGroomingId());

                var petIds = moeGroomingPetDetails.stream()
                        .map(MoeGroomingPetDetail::getPetId)
                        .distinct()
                        .toList();

                var petMap = petClient.getCustomerPetListByIdList(petIds).stream()
                        .collect(Collectors.toMap(CustomerPetDetailDTO::getPetId, Function.identity(), (a, b) -> b));

                var petToServiceMap = moeGroomingPetDetails.stream()
                        .collect(Collectors.groupingBy(
                                MoeGroomingPetDetail::getPetId,
                                Collectors.mapping(MoeGroomingPetDetail::getServiceId, Collectors.toList())));

                AtomicInteger atomicInteger = new AtomicInteger(0);
                var petDataDTOS = petIds.stream()
                        .map(petId -> {
                            var obPetDataDTO = new OBPetDataDTO();
                            obPetDataDTO.setPetId(petId);
                            var pet = petMap.getOrDefault(petId, new CustomerPetDetailDTO());
                            obPetDataDTO.setWeight(pet.getWeight());
                            obPetDataDTO.setPetTypeId(pet.getPetTypeId());
                            obPetDataDTO.setBreed(pet.getBreed());
                            obPetDataDTO.setCoat(pet.getHairLength());
                            obPetDataDTO.setHairLength(pet.getHairLength());
                            obPetDataDTO.setPetIndex(atomicInteger.getAndAdd(1));
                            obPetDataDTO.setServiceIds(petToServiceMap.getOrDefault(petId, List.of()));
                            return obPetDataDTO;
                        })
                        .toList();
                smartScheduleV2Request.setPetParamList(petDataDTOS);

                var serviceIds = moeGroomingPetDetails.stream()
                        .map(MoeGroomingPetDetail::getServiceId)
                        .toList();
                smartScheduleV2Request.setServiceIds(serviceIds);
            }
        } else if (!CollectionUtils.isEmpty(request.getPetParamListForSS())) {
            var petIds = request.getPetParamListForSS().stream()
                    .map(PetDataDTO::getPetId)
                    .distinct()
                    .toList();

            var petMap = petClient.getCustomerPetListByIdList(petIds).stream()
                    .collect(Collectors.toMap(CustomerPetDetailDTO::getPetId, Function.identity(), (a, b) -> b));

            AtomicInteger atomicInteger = new AtomicInteger(0);
            var obPetDataDTOS = request.getPetParamListForSS().stream()
                    .map(petParam -> {
                        var pet = petMap.get(petParam.getPetId());
                        OBPetDataDTO obPetDataDTO = new OBPetDataDTO();
                        if (pet != null) {
                            obPetDataDTO.setWeight(pet.getWeight());
                            obPetDataDTO.setPetTypeId(pet.getPetTypeId());
                            obPetDataDTO.setBreed(pet.getBreed());
                            obPetDataDTO.setCoat(pet.getHairLength());
                            obPetDataDTO.setHairLength(pet.getHairLength());
                        }
                        obPetDataDTO.setPetIndex(atomicInteger.getAndAdd(1));
                        obPetDataDTO.setServiceIds(petParam.getServiceIds());
                        return obPetDataDTO;
                    })
                    .toList();

            smartScheduleV2Request.setPetParamList(obPetDataDTOS);
            smartScheduleV2Request.setServiceIds(request.getPetParamListForSS().stream()
                    .flatMap(petParam -> petParam.getServiceIds().stream())
                    .toList());
        }

        return smartScheduleV2Service.smartScheduleV2(businessId, smartScheduleV2Request);
    }

    @GetMapping("/debugging/distanceMatrixUsageSummary")
    @Auth(AuthType.ANONYMOUS)
    public DistanceMatrixUsageSummaryVo getDistanceMatrixUsageSummary(
            @RequestParam(defaultValue = "false") Boolean fillData,
            @RequestParam(defaultValue = "false") Boolean cleanData,
            @RequestParam(defaultValue = "false") Boolean dumpFile)
            throws IOException {
        /*
        ConcurrentMap<String, Integer> data = GoogleMapService.distanceExecuteCountMap;
        if (fillData) {
            for (int i = data.size(); i < 1 << 20; i++) {
                data.put(CommonUtil.getRandomOnlyNumberString(100), 1);
            }
        }

        if (cleanData) {
            data.clear();
        }

        File file = new File("distance_execute_count.txt");
        if (dumpFile) {
            try (FileOutputStream stream = new FileOutputStream(file)) {
                for (Map.Entry<String, Integer> entry : data.entrySet()) {
                    String line = entry.getKey() + "=" + entry.getValue() + "\n";
                    stream.write(line.getBytes(StandardCharsets.UTF_8));
                }
            }
        }

        return new DistanceMatrixUsageSummaryVo(
                data.size(), data.values().stream().reduce(0, Integer::sum), file.getAbsolutePath());
        */
        throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "not supported method");
    }

    /**
     * 查询以往repeat预约
     *
     * @param context
     * @param repeatId
     * @return
     */
    @GetMapping("/repeat/appointment")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<MoeRepeatInfoDTO> getRepeatAppointment(AuthContext context, @RequestParam Integer repeatId) {
        return ResponseResult.success(moeRepeatV2Service.getRepeatInfoWithAppointments(context.companyId(), repeatId));
    }

    /*
     * 通过下拉菜单，修改 appointment 状态
     */
    @PutMapping("/appointment/status")
    @Auth(AuthType.BUSINESS)
    public UpdateStatusResultVO updateAppointmentStatus(
            @RequestBody @Valid StatusUpdateParams params, AuthContext context) {
        return appointmentService.updateAppointmentStatus(context.getBusinessId(), context.getStaffId(), params);
    }

    /*
     * revert appointment status
     */
    @PutMapping("/appointment/status/revert")
    @Auth(AuthType.BUSINESS)
    public Boolean revertAppointmentStatus(@RequestBody @Valid StatusRevertParams params, AuthContext context) {
        return appointmentService.revertAppointmentStatus(context.getBusinessId(), params);
    }

    @PostMapping("/appointment/amount/validate")
    @Auth(AuthType.BUSINESS)
    public RefundChannelDTO validateAppointmentEdit(
            AuthContext context, @RequestBody @Valid ApptEditSaveCheckParams appointmentCheckParams) {
        if (CollectionUtils.isEmpty(appointmentCheckParams.getServiceList())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        return orderService.validateRefundAmount(
                context.getBusinessId(),
                appointmentCheckParams.getGroomingId(),
                appointmentCheckParams.getServiceList());
    }

    private static void fillNoStartTime(AppointmentParams appointment) {
        Integer startTime = appointment.getAppointmentStartTime();
        if (startTime == null) {
            return;
        }
        if (startTime != 0) {
            appointment.setNoStartTime(false);
        }
    }

    @GetMapping("/customer/appointment/count")
    @Auth(AuthType.COMPANY)
    public CustomerAppointmentNumInfoDTO appointmentCount(AuthContext context, @RequestParam Integer customerId) {
        var res = appointmentQueryService.getCustomerAppointmentNum(
                true, context.companyId(), context.getBusinessId(), List.of(customerId));
        if (CollectionUtils.isEmpty(res)) {
            return new CustomerAppointmentNumInfoDTO();
        }
        return res.get(0);
    }
}
