package com.moego.server.grooming.service.dto.ob;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/25
 */
@Data
public class OBClientApptDTO {

    private Integer id;

    private Integer customerId;

    private String orderId;

    @Schema(description = "last appt date, contains business time zone")
    private String appointmentDate;

    @Schema(description = "last appt start time, minutes units")
    private Integer appointmentStartTime;

    private Integer appointmentEndTime;

    private List<OBClientApptPetDetailDTO> petDetails;

    private List<OBServiceInfoDTO> serviceInfoList;

    private List<OBPetInfoDTO> petInfoList;

    private List<OBStaffInfoDTO> staffInfoList;

    @Schema(description = "recommend date to book for next appt, grooming report's recommend date")
    private String recommendDate;

    @Data
    public static class OBClientApptPetDetailDTO {

        private Integer petDetailId;

        private Integer groomingId;

        private Integer petId;

        private Integer staffId;

        private Integer serviceId;

        @Schema(description = "pet service time, customize time")
        private Integer serviceTime;

        @Schema(description = "pet service price, customize price")
        private BigDecimal servicePrice;
    }

    @Data
    public static class OBPetInfoDTO {

        private Integer petId;

        @Schema(description = "pet weight")
        private String weight;

        @Schema(description = "pet type id, 1-dog, 2-cat, 11-other")
        private Integer petTypeId;

        private String petName;

        private String breed;

        @Schema(description = "1-normal, 2-deleted")
        private Byte status;

        @Schema(description = "1-normal, 2-passed away")
        private Integer lifeStatus;

        private String avatarPath;
    }

    @Data
    public static class OBServiceInfoDTO {

        @Schema(description = "0-disable, 1-enable")
        private Byte isAllStaff;

        @Schema(description = "0-disable, 1-enable")
        private Byte breedFilter;

        @Schema(description = "0-disable, 1-enable")
        private Byte weightFilter;

        private BigDecimal weightDownLimit;

        private BigDecimal weightUpLimit;

        @Schema(description = "staff can do this service")
        private List<Integer> availableStaffIdList;

        @Schema(
                description =
                        "key: petTypeId, value: service available breed list, **ALL_BREEDS** means all breeds are allowed")
        private Map<Integer, String> typeBreedMap;

        private Integer serviceId;

        @Schema(description = "latest service name")
        private String name;

        @Schema(description = "1-service, 2-add-on")
        private Byte type;

        @Schema(description = "0-active, 1-inactive")
        private Byte inactive;

        @Schema(description = "0-disable, 1-enable")
        private Byte bookOnlineAvailable;

        @Schema(description = "1-normal, 2-deleted")
        private Byte status;
    }

    @Data
    public static class OBStaffInfoDTO {

        private Integer staffId;

        private String avatarPath;

        private String firstName;

        private String lastName;

        @Schema(description = "0-active, 1-inactive")
        private Byte inactive;

        @Schema(description = "0-disable, 1-enable")
        private Byte bookOnlineAvailable;

        @Schema(description = "1-normal, 2-deleted")
        private Byte status;
    }
}
