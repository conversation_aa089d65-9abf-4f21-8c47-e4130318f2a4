package com.moego.server.grooming.utils;

import com.google.api.client.auth.oauth2.AuthorizationCodeRequestUrl;
import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.auth.oauth2.TokenResponse;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets;
import com.google.api.client.googleapis.auth.oauth2.GoogleRefreshTokenRequest;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.client.util.DateTime;
import com.google.api.client.util.store.DataStoreFactory;
import com.google.api.client.util.store.MemoryDataStoreFactory;
import com.google.api.services.calendar.Calendar;
import com.google.api.services.calendar.CalendarScopes;
import com.google.api.services.calendar.model.Event;
import com.google.api.services.calendar.model.Event.Reminders;
import com.google.api.services.calendar.model.EventDateTime;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.server.grooming.dto.GroomingPetInfoDetailDTO;
import com.moego.server.grooming.dto.GroomingPetServiceDTO;
import com.moego.server.grooming.service.dto.CalendarEventParamDto;
import com.moego.server.grooming.service.dto.PetDetailTimeDto;
import java.io.IOException;
import java.io.StringReader;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2021/1/21 3:16 PM
 */
@Slf4j
public class SyncCalendarUtil {

    /**
     * Application name
     */
    private static final String APPLICATION_NAME = "moegoCalendarSync";

    private static final List<String> SCOPES = Collections.singletonList(CalendarScopes.CALENDAR);

    // TODO set multiple credential files
    private static final String CREDENTIALS_FILE_PATH = "classpath:credentials.json";
    // todo 需要保存在credentials.json
    private static final String CREDENTIALS =
            "{\"web\":{\"client_id\":\"************-r6ppk2fpmjv76r3ak7f7jpvio06d86vr.apps.googleusercontent.com\",\"project_id\":\"moegoclient\",\"auth_uri\":\"https://accounts.google.com/o/oauth2/auth\",\"token_uri\":\"https://oauth2.googleapis.com/token\",\"auth_provider_x509_cert_url\":\"https://www.googleapis.com/oauth2/v1/certs\",\"client_secret\":\"_FvzoB7_DMtUiEGA0nk40-T7\",\"redirect_uris\":[\"https://go.t2.moego.pet/calendar/grooming\",\"https://go.moego.pet/calendar/grooming\",\"https://go.s1.moego.pet/calendar/grooming\"],\"javascript_origins\":[\"https://go.t2.moego.pet\",\"https://go.moego.pet\",\"https://go.s1.moego.pet\"]}}";
    private static final String CLIENT_ID = "************-r6ppk2fpmjv76r3ak7f7jpvio06d86vr.apps.googleusercontent.com";
    private static final String CLIENT_SECRET = "_FvzoB7_DMtUiEGA0nk40-T7";
    /**
     * Global instance of the {@link DataStoreFactory}.
     */
    //    private static FileDataStoreFactory dataStoreFactory;
    private static MemoryDataStoreFactory memoryDataStoreFactory;

    /**
     * Global instance of the JSON factory.
     */
    private static final JsonFactory JSON_FACTORY = JacksonFactory.getDefaultInstance();

    /**
     * Global instance of the HTTP transport.
     */
    private static final HttpTransport httpTransport;

    public static final GoogleAuthorizationCodeFlow globalFlow;

    public static final String STAFF_NAME_PLACEHOLDER = "{staffName}";
    public static final String STORE_NAME_PLACEHOLDER = "{storeName}";
    public static final String CUSTOMER_NAME_PLACEHOLDER = "{customerName}";
    public static final String PET_AMOUNT_PLACEHOLDER = "{petAmount}";
    public static final String PET_NAME_PLACEHOLDER = "{petName}";
    public static final String PET_BREED_PLACEHOLDER = "{petBreed}";
    public static final String SERVICE_PLACEHOLDER = "{service}";

    private static final Pattern EVENT_DESCRIPTION_PATTERN = Pattern.compile("\\{[\\s\\S]*\\}");

    static {
        try {
            httpTransport = GoogleNetHttpTransport.newTrustedTransport();
            memoryDataStoreFactory = new MemoryDataStoreFactory();
            GoogleClientSecrets clientSecrets = GoogleClientSecrets.load(JSON_FACTORY, new StringReader(CREDENTIALS));
            globalFlow = new GoogleAuthorizationCodeFlow.Builder(httpTransport, JSON_FACTORY, clientSecrets, SCOPES)
                    .setDataStoreFactory(memoryDataStoreFactory)
                    .build();
        } catch (Throwable t) {
            throw new CommonException(ResponseCodeEnum.SERVER_ERROR, "init google calendar handler failed", t);
        }
    }

    public static TokenResponse getNewTokenResponse(String refreshToken) {
        try {
            return new GoogleRefreshTokenRequest(httpTransport, JSON_FACTORY, refreshToken, CLIENT_ID, CLIENT_SECRET)
                    .setScopes(SCOPES)
                    .setGrantType("refresh_token")
                    .execute();
        } catch (IOException e) {
            log.error("refresh token getNewTokenResponse", e);
            return null;
        }
    }

    public static Credential refreshToken(TokenResponse response, String userId) {
        try {
            Credential credential = globalFlow.createAndStoreCredential(response, userId);
            credential.refreshToken();
            return credential;
        } catch (IOException e) {
            log.error(String.format("refresh token error userId:%s", userId));
            return null;
        }
    }

    public static Credential initCredential(TokenResponse response, String userId) {
        try {
            Credential credential = globalFlow.loadCredential(userId);
            if (credential != null) {
                return credential;
            }
        } catch (IOException e) {
            log.error(String.format("globalFlow.loadCredential %s", userId), e);
        }
        try {
            return globalFlow.createAndStoreCredential(response, userId);
        } catch (IOException e) {
            log.error("create and Store Credential error", e);
            return null;
        }
    }

    public static Calendar initCalendarByResponse(TokenResponse response, String userId) {
        try {
            Credential credential = globalFlow.createAndStoreCredential(response, userId);
            return new Calendar.Builder(httpTransport, JSON_FACTORY, credential)
                    .setApplicationName(APPLICATION_NAME)
                    .build();
        } catch (IOException e) {
            log.error("create and store credential error", e);
            return null;
        }
    }

    public static Calendar initCalendarByCredential(Credential credential) {
        return new Calendar.Builder(httpTransport, JSON_FACTORY, credential)
                .setApplicationName(APPLICATION_NAME)
                .build();
    }

    public Credential getCredentialByUserId(String userId) {
        Credential credential = null;
        try {
            credential = globalFlow.loadCredential(userId);
        } catch (IOException e) {
            throw new CommonException(ResponseCodeEnum.SERVER_ERROR, "internal google flow error", e);
        }
        if (credential != null
                && (credential.getRefreshToken() != null
                        || credential.getExpiresInSeconds() == null
                        || credential.getExpiresInSeconds() > 60)) {
            return credential;
        }
        throw new CommonException(ResponseCodeEnum.SERVER_ERROR, "can't find valid credential by " + userId);
    }

    /**
     * Authorizes the installed application to access user's protected data.
     */
    public static String getRedirectUrlForGoogle(String redirectUrl) {
        AuthorizationCodeRequestUrl authorizationCodeRequestUrl = globalFlow
                .newAuthorizationUrl()
                .setAccessType("offline")
                .setApprovalPrompt("force")
                .setRedirectUri(redirectUrl);
        return authorizationCodeRequestUrl.build();
    }

    protected String getRedirectUri(String url1) {
        GenericUrl url = new GenericUrl(url1);
        url.setRawPath("/oauth2callback");
        return url.build();
    }

    /**
     * Gets the datastore factory used in these samples.
     */
    public static DataStoreFactory getDataStoreFactory() {
        return memoryDataStoreFactory;
    }

    /**
     * Creates a test event.
     */
    public static Event createTestEvent(Calendar client, String summary) throws IOException {
        Date oneHourFromNow = SyncCalendarUtil.getRelativeDate(java.util.Calendar.HOUR, 2);
        Date twoHoursFromNow = SyncCalendarUtil.getRelativeDate(java.util.Calendar.HOUR, 3);
        DateTime start = new DateTime(oneHourFromNow, TimeZone.getTimeZone("UTC"));
        DateTime end = new DateTime(twoHoursFromNow, TimeZone.getTimeZone("UTC"));
        Event event = new Event()
                .setSummary(summary)
                .setReminders(new Reminders().setUseDefault(false))
                .setStart(new EventDateTime().setDateTime(start))
                .setEnd(new EventDateTime().setDateTime(end));
        return client.events().insert("primary", event).execute();
    }

    /**
     * @param apptDate    yyyy-MM-dd
     * @param hourMinutes 540=9*60
     * @param zoneName    America/Los_Angeles
     * @return
     * @throws ParseException
     */
    public static EventDateTime getEventDateTimeFromAppointmentDate(
            String apptDate, Integer hourMinutes, String zoneName) throws ParseException {
        if (StringUtils.isBlank(zoneName)) {
            zoneName = "America/Los_Angeles";
        }
        Integer hours = hourMinutes / 60;
        Integer minutes = hourMinutes % 60;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone(zoneName));
        Date date = simpleDateFormat.parse(String.format("%s %s:%s", apptDate, hours, minutes));
        DateTime start = new DateTime(date, TimeZone.getTimeZone(zoneName));
        return new EventDateTime().setDateTime(start);
    }

    /**
     * Gets a new {@link Date} relative to the current date and time.
     *
     * @param field  the field identifier from {@link java.util.Calendar} to increment
     * @param amount the amount of the field to increment
     * @return the new date
     */
    public static Date getRelativeDate(int field, int amount) {
        Date now = new Date();
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.setTime(now);
        cal.add(field, amount);
        return cal.getTime();
    }

    public static String parseCalendarName(String text, String staffName, String storeName) {
        if (text.contains(STAFF_NAME_PLACEHOLDER) && StringUtils.isNotBlank(staffName)) {
            text = text.replace(STAFF_NAME_PLACEHOLDER, staffName);
        }
        if (text.contains(STORE_NAME_PLACEHOLDER) && StringUtils.isNotBlank(storeName)) {
            text = text.replace(STORE_NAME_PLACEHOLDER, storeName);
        }
        return text;
    }

    public static void parseEventTitle(String text, CalendarEventParamDto settingDto) {
        if (text.contains(STAFF_NAME_PLACEHOLDER) && StringUtils.isNotBlank(settingDto.getStaffName())) {
            text = text.replace(STAFF_NAME_PLACEHOLDER, settingDto.getStaffName());
        }
        if (text.contains(STORE_NAME_PLACEHOLDER) && StringUtils.isNotBlank(settingDto.getStoreName())) {
            text = text.replace(STORE_NAME_PLACEHOLDER, settingDto.getStoreName());
        }
        if (text.contains(CUSTOMER_NAME_PLACEHOLDER) && StringUtils.isNotBlank(settingDto.getCustomerName())) {
            text = text.replace(CUSTOMER_NAME_PLACEHOLDER, settingDto.getCustomerName());
        }
        if (text.contains(PET_AMOUNT_PLACEHOLDER) && StringUtils.isNotBlank(settingDto.getPetAmount())) {
            text = text.replace(PET_AMOUNT_PLACEHOLDER, settingDto.getPetAmount());
        }
        if (text.contains(PET_NAME_PLACEHOLDER) && StringUtils.isNotBlank(settingDto.getPetAmount())) {
            String petName = settingDto.getPetInfoDetails().stream()
                    // 过滤
                    .filter(petInfoDetail -> petInfoDetail.getGroomingPetServiceDTOS().stream()
                            .anyMatch(groomingPetServiceDTO -> settingDto
                                    .getEventTime()
                                    .getPetDetailIdList()
                                    .contains(groomingPetServiceDTO.getPetDetailId())))
                    .map(GroomingPetInfoDetailDTO::getPetName)
                    .distinct()
                    .collect(Collectors.joining(", "));

            text = text.replace(PET_NAME_PLACEHOLDER, petName);
        }
        settingDto.setSummary(text);
    }

    public static void parseEventDescription(String text, CalendarEventParamDto settingDto) {
        Matcher matcher = EVENT_DESCRIPTION_PATTERN.matcher(text);
        if (Boolean.FALSE.equals(matcher.find())) {
            settingDto.setDescription(text);
            return;
        }

        String placeholderText = matcher.group();
        String frontText = text.substring(0, matcher.start());
        String remainderText = text.substring(matcher.end());

        List<GroomingPetInfoDetailDTO> petInfoDetails = settingDto.getPetInfoDetails();
        PetDetailTimeDto eventTime = settingDto.getEventTime();

        StringBuilder detailSB = new StringBuilder();
        if (StringUtils.isNotBlank(frontText)) {
            detailSB.append(frontText).append("\n");
        }
        /**
         * ** Please make changes to this appointment in the MoeGo calendar. Any changes made here will be overwritten during the next sync.
         *
         * {pet name}({pet breed}){service}
         */
        for (GroomingPetInfoDetailDTO petInfoDetail : petInfoDetails) {
            for (GroomingPetServiceDTO groomingPetServiceDTO : petInfoDetail.getGroomingPetServiceDTOS()) {
                if (eventTime.getPetDetailIdList().contains(groomingPetServiceDTO.getPetDetailId())) {
                    detailSB.append(parseEventDescription(
                                    placeholderText,
                                    settingDto.getStoreName(),
                                    petInfoDetail.getPetName(),
                                    petInfoDetail.getBreed(),
                                    groomingPetServiceDTO.getServiceName()))
                            .append("\n");
                }
            }
        }
        detailSB.append(remainderText);
        settingDto.setDescription(detailSB.toString());
    }

    private static String parseEventDescription(
            String text, String storeName, String petName, String petBreed, String service) {
        if (text.contains(STORE_NAME_PLACEHOLDER) && StringUtils.isNotBlank(storeName)) {
            text = text.replace(STORE_NAME_PLACEHOLDER, storeName);
        }
        if (text.contains(PET_NAME_PLACEHOLDER) && StringUtils.isNotBlank(petName)) {
            text = text.replace(PET_NAME_PLACEHOLDER, petName);
        }
        if (text.contains(PET_BREED_PLACEHOLDER) && StringUtils.isNotBlank(petBreed)) {
            text = text.replace(PET_BREED_PLACEHOLDER, petBreed);
        }
        if (text.contains(SERVICE_PLACEHOLDER) && StringUtils.isNotBlank(service)) {
            text = text.replace(SERVICE_PLACEHOLDER, service);
        }
        return text;
    }
}
