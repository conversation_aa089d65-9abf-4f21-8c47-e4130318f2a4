package com.moego.server.grooming.web.dto.ob;

import com.moego.idl.models.online_booking.v1.AcceptCustomerType;
import com.moego.server.grooming.dto.ob.BookOnlinePaymentSettingDTO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineNotification;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SettingInfoDto {

    MoeBusinessBookOnline bookOnlineInfo;

    @Schema(description = "notification for client is not valid, use auto message api instead")
    MoeBookOnlineNotification bookOnlineNotification;

    BookOnlinePaymentSettingDTO paymentSetting;

    @Schema(description = "Whether the merge operation of the landing page has been performed")
    private Boolean isMerged;

    @Schema(description = "New users and those who have never used OB")
    private Boolean isNew;

    private String businessName;

    /**
     * @see <a href="https://moego.atlassian.net/browse/MER-1409">MER-1409</a>
     * @see AcceptCustomerType
     */
    private AcceptCustomerTypes acceptCustomerType;

    @Data
    public static class AcceptCustomerTypes {
        private Integer grooming;
        private Integer boarding;
        private Integer daycare;
    }
}
