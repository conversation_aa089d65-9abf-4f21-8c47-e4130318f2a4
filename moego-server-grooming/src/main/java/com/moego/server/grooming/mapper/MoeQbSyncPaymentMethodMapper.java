package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeQbSyncPaymentMethod;
import org.apache.ibatis.annotations.Param;

public interface MoeQbSyncPaymentMethodMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_payment_method
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_payment_method
     *
     * @mbg.generated
     */
    int insert(MoeQbSyncPaymentMethod record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_payment_method
     *
     * @mbg.generated
     */
    int insertSelective(MoeQbSyncPaymentMethod record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_payment_method
     *
     * @mbg.generated
     */
    MoeQbSyncPaymentMethod selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_payment_method
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeQbSyncPaymentMethod record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_payment_method
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeQbSyncPaymentMethod record);

    MoeQbSyncPaymentMethod selectByBusinessIdRealmIdMethodName(
            @Param("businessId") Integer businessId, @Param("realmId") String realmId, @Param("method") String method);
}
