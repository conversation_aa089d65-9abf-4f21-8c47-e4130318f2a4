package com.moego.server.grooming.web;

import static com.moego.common.utils.PermissionUtil.CAN_ACCESS_ONLINE_BOOKING_REQUEST_AND_WAIT_LIST;
import static com.moego.common.utils.PermissionUtil.VIEW_CLIENT_PHONE;
import static com.moego.common.utils.PermissionUtil.checkStaffPermissionsInfo;
import static com.moego.common.utils.PermissionUtil.phoneNumberConfusion;
import static com.moego.common.utils.PermissionUtil.verifyStaffPermissions;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static java.util.stream.Collectors.toMap;

import com.github.pagehelper.PageInfo;
import com.moego.common.dto.CommonResultDto;
import com.moego.common.dto.StaffPermissions;
import com.moego.common.params.SortIdListParams;
import com.moego.common.response.ResponseResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.online_booking.v1.ListAcceptedCustomerSettingRequest;
import com.moego.idl.service.online_booking.v1.ListAcceptedCustomerSettingResponse;
import com.moego.idl.service.online_booking.v1.OBAvailabilitySettingServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.grooming.dto.AddResultDTO;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.BookOnlineStaffAvailabilityDTO;
import com.moego.server.grooming.dto.GroomingBookingDTO;
import com.moego.server.grooming.dto.GroomingQuestionDTO;
import com.moego.server.grooming.dto.ob.BookOnlinePaymentSettingDTO;
import com.moego.server.grooming.enums.OBRequestSubmittedAutoTypeEnum;
import com.moego.server.grooming.mapperbean.MoeBookOnlineGallery;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.mapperbean.MoeBookOnlineNotification;
import com.moego.server.grooming.mapperbean.MoeBookOnlineProfile;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapstruct.ObPaymentSettingMapper;
import com.moego.server.grooming.params.BookOnlineQuestionListParams;
import com.moego.server.grooming.params.BookOnlineQuestionParams;
import com.moego.server.grooming.params.BusinessBookOnlineParams;
import com.moego.server.grooming.params.MoeBookOnlineGalleryBatchParams;
import com.moego.server.grooming.params.MoeBookOnlineGalleryParams;
import com.moego.server.grooming.params.MoeBookOnlineProfileParams;
import com.moego.server.grooming.params.MoeBookOnlineStaffTimeParams;
import com.moego.server.grooming.params.ObAvailableTimeRequest;
import com.moego.server.grooming.params.ob.BookOnlinePaymentBaseParams;
import com.moego.server.grooming.params.ob.BusinessBookOnlinePaymentParams;
import com.moego.server.grooming.params.ob.MobileGroomingParams;
import com.moego.server.grooming.params.ob.NotificationParams;
import com.moego.server.grooming.params.ob.ServiceParams;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.MoeGroomingQuestionService;
import com.moego.server.grooming.service.dto.OBAvailableTimeDto;
import com.moego.server.grooming.service.ob.OBGroomingService;
import com.moego.server.grooming.service.ob.OBLandingPageConfigService;
import com.moego.server.grooming.web.dto.ob.AvailableStaffTimeListDto;
import com.moego.server.grooming.web.dto.ob.IsAvailableDto;
import com.moego.server.grooming.web.dto.ob.QuestionListDto;
import com.moego.server.grooming.web.dto.ob.SettingInfoDto;
import com.moego.server.grooming.web.vo.GroomingBookingQueryVO;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/grooming/bookOnline")
@RequiredArgsConstructor
public class MoeGroomingBookOnlineController {

    private final MoeGroomingBookOnlineService moeGroomingBookOnlineService;
    private final MoeGroomingQuestionService moeGroomingQuestionService;
    private final OBLandingPageConfigService landingPageConfigService;
    private final IBusinessStaffService businessStaffService;
    private final OBGroomingService obGroomingService;
    private final PermissionHelper permissionHelper;
    private final MigrateHelper migrateHelper;
    private final OBAvailabilitySettingServiceGrpc.OBAvailabilitySettingServiceBlockingStub
            obAvailabilitySettingService;

    @GetMapping("/setting/info")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<SettingInfoDto> getSettingInfo(
            AuthContext context,
            @RequestParam(value = "withoutClientNotification", required = false) Boolean withoutClientNotification) {
        SettingInfoDto result = new SettingInfoDto();
        MoeBusinessBookOnline obInfo = moeGroomingBookOnlineService.getSettingInfoByBusinessId(context.getBusinessId());
        result.setBookOnlineInfo(obInfo);
        result.setPaymentSetting(buildPaymentSetting(obInfo));
        boolean withClientNotification = true;
        if (withoutClientNotification != null && withoutClientNotification) {
            withClientNotification = false;
        }
        result.setBookOnlineNotification(moeGroomingBookOnlineService.getNotificationByBusinessId(
                context.getBusinessId(), withClientNotification));
        MoeBookOnlineLandingPageConfig landingPageConfig =
                landingPageConfigService.selectByBusinessId(context.getBusinessId());
        result.setIsMerged(Objects.nonNull(landingPageConfig));
        // New user = Unpublished site and not modify
        result.setIsNew(Objects.nonNull(landingPageConfig)
                && BooleanUtils.isFalse(landingPageConfig.getIsPublished())
                && Objects.equals(landingPageConfig.getCreateTime(), landingPageConfig.getUpdateTime()));
        result.setAcceptCustomerType(getAcceptCustomerTypes(obInfo.getCompanyId(), obInfo.getBusinessId()));
        return ResponseResult.success(result);
    }

    SettingInfoDto.AcceptCustomerTypes getAcceptCustomerTypes(long companyId, long businessId) {
        var serviceItemTypeToAcceptCustomerType = obAvailabilitySettingService
                .listAcceptedCustomerSetting(ListAcceptedCustomerSettingRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .addAllServiceItemTypes(
                                List.of(ServiceItemType.GROOMING, ServiceItemType.BOARDING, ServiceItemType.DAYCARE))
                        .build())
                .getAcceptCustomerTypesList()
                .stream()
                .collect(toMap(
                        ListAcceptedCustomerSettingResponse.AcceptCustomerType::getServiceItemType,
                        e -> e.getAcceptCustomerType().getNumber(),
                        (o, n) -> o));

        var result = new SettingInfoDto.AcceptCustomerTypes();
        result.setGrooming(serviceItemTypeToAcceptCustomerType.get(ServiceItemType.GROOMING));
        result.setBoarding(serviceItemTypeToAcceptCustomerType.get(ServiceItemType.BOARDING));
        result.setDaycare(serviceItemTypeToAcceptCustomerType.get(ServiceItemType.DAYCARE));
        return result;
    }

    private BookOnlinePaymentSettingDTO buildPaymentSetting(MoeBusinessBookOnline obInfo) {
        BookOnlinePaymentSettingDTO mainSetting = ObPaymentSettingMapper.INSTANCE.toMainSetting(obInfo);
        if (obInfo.getGroupPaymentType() != null) {
            mainSetting.setCertainGroupSetting(ObPaymentSettingMapper.INSTANCE.toCertainGroupSetting(obInfo));
        }
        return mainSetting;
    }

    @DeleteMapping("/payment/group/setting")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(action = Action.DELETE, resourceType = ResourceType.ONLINE_BOOKING_CUSTOMIZED_PAYMENT)
    public void deletePaymentGroupSetting(AuthContext context) {
        Integer businessId = context.getBusinessId();
        moeGroomingBookOnlineService.deletePaymentGroupConfig(businessId);
    }

    @PutMapping("/payment/group/setting")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.ONLINE_BOOKING_CUSTOMIZED_PAYMENT,
            details = "#params")
    public void updatePaymentGroupSetting(
            AuthContext context, @Valid @RequestBody BusinessBookOnlinePaymentParams params) {
        Integer businessId = context.getBusinessId();
        Long companyId = context.companyId();
        if (Objects.isNull(params)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "param empty");
        }
        if (Objects.isNull(params.getPaymentType())
                && !Optional.of(params)
                        .map(BusinessBookOnlinePaymentParams::getPaymentGroupSetting)
                        .map(BookOnlinePaymentBaseParams::getPaymentType)
                        .isPresent()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "paymentType empty");
        }
        moeGroomingBookOnlineService.updatePaymentSetting(companyId, businessId, params);
    }

    @PutMapping("/setting/info")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.ONLINE_BOOKING_SETTING,
            details = "#bookOnlineParams")
    public ResponseResult<Integer> updateSettingInfo(
            AuthContext context, @Valid @RequestBody BusinessBookOnlineParams bookOnlineParams) {
        // DONE(Frank): 这里不再需要判空， 添加business注解后，获取不到直接就抛异常了
        bookOnlineParams.setBusinessId(context.getBusinessId());
        // 1. update notification before update main table
        MoeBookOnlineNotification currentNotification = new MoeBookOnlineNotification();
        BeanUtils.copyProperties(bookOnlineParams, currentNotification);
        moeGroomingBookOnlineService.updateNotification(context.companyId(), currentNotification);
        // 2. then update setting-main-table
        bookOnlineParams.setBookOnlineName(null); // omit ob name from controller
        Optional.ofNullable(bookOnlineParams.getRequestSubmittedAutoType())
                .filter(StringUtils::hasText)
                .ifPresent(OBRequestSubmittedAutoTypeEnum::fromString);
        compatibleWithOB2(bookOnlineParams);
        return ResponseResult.success(moeGroomingBookOnlineService.updateInfoByPrimaryIdOrBusinessId(bookOnlineParams));
    }

    private void compatibleWithOB2(BusinessBookOnlineParams bookOnlineParams) {
        MoeBusinessBookOnline oldSetting =
                moeGroomingBookOnlineService.getSettingInfoByBusinessId(bookOnlineParams.getBusinessId());
        if (!Objects.equals(oldSetting.getUseVersion(), BookOnlineDTO.UseVersion.OB_2_0)) {
            return;
        }

        // OB 2.0 更新时需要更新新字段！
        if (bookOnlineParams.getSoonestAvailable() != null && bookOnlineParams.getBookingRangeStartOffset() == null) {
            bookOnlineParams.setBookingRangeStartOffset(bookOnlineParams.getSoonestAvailable());
        }
        if (bookOnlineParams.getFarestAvailable() != null && bookOnlineParams.getBookingRangeEndOffset() == null) {
            bookOnlineParams.setBookingRangeEndOffset(bookOnlineParams.getFarestAvailable());
        }
        if (bookOnlineParams.getBySlotSoonestAvailable() != null
                && bookOnlineParams.getBookingRangeStartOffset() == null) {
            bookOnlineParams.setBookingRangeStartOffset(bookOnlineParams.getBySlotSoonestAvailable());
        }
        if (bookOnlineParams.getBySlotFarthestAvailable() != null
                && bookOnlineParams.getBookingRangeEndOffset() == null) {
            bookOnlineParams.setBookingRangeEndOffset(bookOnlineParams.getBySlotFarthestAvailable());
        }
        if (bookOnlineParams.getTimeslotMins() != null) {
            // arrival window 模式下，老的 timeslot 表示 arrivalWindowAfterMin
            if (Objects.equals(oldSetting.getTimeslotFormat(), BookOnlineDTO.TimeslotFormat.ARRIVAL_WINDOW)
                    || Objects.equals(
                            oldSetting.getBySlotTimeslotFormat(), BookOnlineDTO.TimeslotFormat.ARRIVAL_WINDOW)) {
                bookOnlineParams.setArrivalWindowAfterMin(bookOnlineParams.getTimeslotMins());
            }
        }
        if (bookOnlineParams.getBySlotTimeslotMins() != null) {
            // arrival window 模式下，老的 bySlotTimeslot 表示 arrivalWindowAfterMin
            if (Objects.equals(oldSetting.getTimeslotFormat(), BookOnlineDTO.TimeslotFormat.ARRIVAL_WINDOW)
                    || Objects.equals(
                            oldSetting.getBySlotTimeslotFormat(), BookOnlineDTO.TimeslotFormat.ARRIVAL_WINDOW)) {
                bookOnlineParams.setArrivalWindowAfterMin(bookOnlineParams.getTimeslotMins());
            }
        }
    }

    /**
     * TODO deprecated
     */
    @PutMapping("/setting/notification")
    @Auth(AuthType.BUSINESS)
    public Integer updateNotificationSetting(
            AuthContext context, @Valid @RequestBody NotificationParams notificationParams) {
        MoeBookOnlineNotification currentNotification = new MoeBookOnlineNotification();
        BeanUtils.copyProperties(notificationParams, currentNotification);
        currentNotification.setBusinessId(context.getBusinessId());
        return moeGroomingBookOnlineService.updateNotification(context.companyId(), currentNotification);
    }

    /**
     * TODO deprecated
     */
    @PutMapping("/setting/mobile")
    @Auth(AuthType.BUSINESS)
    public Integer updateMobileGroomingSetting(
            AuthContext context, @Valid @RequestBody MobileGroomingParams mobileGroomingParams) {
        mobileGroomingParams.setBusinessId(context.getBusinessId());
        return moeGroomingBookOnlineService.updateMobileGrooming(mobileGroomingParams);
    }

    /**
     * TODO deprecated
     */
    @PutMapping("/setting/service")
    @Auth(AuthType.BUSINESS)
    public Integer updateServiceSetting(AuthContext context, @Valid @RequestBody ServiceParams serviceParams) {
        serviceParams.setBusinessId(context.getBusinessId());
        serviceParams.setCompanyId(context.companyId());
        return moeGroomingBookOnlineService.updateService(serviceParams);
    }

    /**
     * TODO OB 2.0 sunset
     */
    @GetMapping("/profile")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<MoeBookOnlineProfile> getProfile(AuthContext context) {
        return ResponseResult.success(moeGroomingBookOnlineService.getProfileByBusinessId(context.getBusinessId()));
    }

    /**
     * TODO OB 2.0 sunset
     */
    @PutMapping("/profile")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Integer> updateProfile(
            AuthContext context, @Valid @RequestBody MoeBookOnlineProfileParams profileParams) {
        if (context.getBusinessId() != null) {
            profileParams.setBusinessId(context.getBusinessId());
        }
        return ResponseResult.success(moeGroomingBookOnlineService.updateProfileByPrimaryIdOrBusinessId(profileParams));
    }

    /**
     * TODO OB 2.0 sunset
     */
    @GetMapping("/gallery")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<MoeBookOnlineGallery>> getGallery(AuthContext context) {
        return ResponseResult.success(moeGroomingBookOnlineService.getGalleryByBusinessId(context.getBusinessId()));
    }

    /**
     * TODO OB 2.0 sunset
     */
    @PostMapping("/gallery")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<AddResultDTO> addGalleryImage(
            AuthContext context, @RequestBody MoeBookOnlineGalleryParams galleryParams) {
        galleryParams.setBusinessId(context.getBusinessId());
        galleryParams.setCompanyId(context.companyId());
        if (context.getStaffId() != null) {
            galleryParams.setStaffId(context.getStaffId());
        }
        AddResultDTO addResultDTO = new AddResultDTO();
        addResultDTO.setResult(true);
        addResultDTO.setId(moeGroomingBookOnlineService.addGalleryImage(galleryParams));
        return ResponseResult.success(addResultDTO);
    }

    /**
     * TODO OB 2.0 sunset
     */
    @DeleteMapping("/gallery")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Integer> deleteGalleryImage(
            AuthContext context, @RequestBody MoeBookOnlineGalleryBatchParams galleryParams) {
        galleryParams.setBusinessId(context.getBusinessId());
        return ResponseResult.success(moeGroomingBookOnlineService.batchDeleteGalleryImage(galleryParams));
    }

    /**
     * TODO OB 2.0 sunset
     */
    @PutMapping("/gallery/star")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Integer> batchStarGalleryImage(
            AuthContext context, @RequestBody MoeBookOnlineGalleryBatchParams galleryParams) {
        galleryParams.setBusinessId(context.getBusinessId());
        return ResponseResult.success(moeGroomingBookOnlineService.batchStarGalleryImage(galleryParams));
    }

    @GetMapping("/staff/availability")
    @Auth(AuthType.BUSINESS)
    @Deprecated
    public List<BookOnlineStaffAvailabilityDTO> getStaffOnlineAvailability(AuthContext context) {
        return moeGroomingBookOnlineService.getStaffOBAvailability(context.getBusinessId(), false);
    }

    @PutMapping("/staff/availability")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.ONLINE_BOOKING_TEAM_SCHEDULE,
            resourceId = "#params.staffId",
            details = "#params")
    @Deprecated
    public Boolean modifyStaffOnlineAvailability(
            AuthContext context, @RequestBody BookOnlineStaffAvailabilityDTO params) {
        moeGroomingBookOnlineService.modifyStaffOBAvailability(context.getBusinessId(), params);
        return true;
    }

    /**
     * TODO deprecated
     */
    @PostMapping("/staff/availability/batch")
    @Auth(AuthType.BUSINESS)
    public Boolean modifyStaffOnlineAvailabilityBatch(
            AuthContext context, @RequestBody List<BookOnlineStaffAvailabilityDTO> paramList) {
        moeGroomingBookOnlineService.modifyStaffOBAvailabilityInternalBatch(context.getBusinessId(), paramList);
        return true;
    }

    @GetMapping("/staff/availability/sync")
    @Auth(AuthType.BUSINESS)
    @Deprecated
    public Boolean getSyncWithWorkingHourStatus(AuthContext context) {
        return moeGroomingBookOnlineService.getSyncWithWorkingHourStatus(context.getBusinessId());
    }

    @PostMapping("/staff/availability/sync")
    @Auth(AuthType.BUSINESS)
    @Deprecated
    public void modifySyncWithWorkingHourStatus(AuthContext context) {
        moeGroomingBookOnlineService.modifySyncWithWorkingHourStatus(context.getBusinessId());
    }

    /**
     * 获取当前商户所有staff的工作时间
     *
     * @param context
     * @return
     */
    @GetMapping("/staff/time")
    @Auth(AuthType.BUSINESS)
    @Deprecated
    public ResponseResult<AvailableStaffTimeListDto> getAvailableStaffTime(AuthContext context) {
        AvailableStaffTimeListDto result = new AvailableStaffTimeListDto();
        result.setStaffList(moeGroomingBookOnlineService.getStaffTimeByBusinessId(context.getBusinessId()));
        result.setAvailabilityList(moeGroomingBookOnlineService.getStaffOBAvailability(context.getBusinessId(), false));
        return ResponseResult.success(result);
    }

    @PostMapping("/staff/time")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.ONLINE_BOOKING_TEAM_SCHEDULE,
            resourceId = "#staffTimeParams.staffId",
            details = "#staffTimeParams")
    @Deprecated
    public ResponseResult<CommonResultDto> saveAvailableStaffTime(
            AuthContext context, @RequestBody MoeBookOnlineStaffTimeParams staffTimeParams) {
        staffTimeParams.setBusinessId(context.getBusinessId());
        staffTimeParams.setCompanyId(context.companyId());
        moeGroomingBookOnlineService.saveStaffTime(staffTimeParams);
        return ResponseResult.success(new CommonResultDto(true));
    }

    @GetMapping("/question")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<QuestionListDto> getQuestion(AuthContext context, @RequestParam Integer type) {
        QuestionListDto result = new QuestionListDto();
        result.setQuestions(moeGroomingBookOnlineService.getQuestionsByBusinessId(context.getBusinessId(), type));
        return ResponseResult.success(result);
    }

    @DeleteMapping("/question")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.DELETE,
            resourceType = ResourceType.ONLINE_BOOKING_QUESTION,
            resourceId = "#questionId",
            details = "#questionId")
    public ResponseResult<Integer> deleteQuestion(AuthContext context, @RequestParam Integer questionId) {
        return ResponseResult.success(moeGroomingQuestionService.deleteQuestion(context.getBusinessId(), questionId));
    }

    @PostMapping("/question")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.CREATE,
            resourceType = ResourceType.ONLINE_BOOKING_QUESTION,
            resourceId = "#result.data.id",
            details = "#record")
    public ResponseResult<AddResultDTO> createQuestion(
            AuthContext context, @Valid @RequestBody BookOnlineQuestionParams record) {
        record.setBusinessId(context.getBusinessId());
        record.setCompanyId(context.companyId());
        AddResultDTO result = new AddResultDTO();
        result.setResult(moeGroomingBookOnlineService.createQuestion(record) == 1);
        result.setId(record.getId());
        return ResponseResult.success(result);
    }

    @PutMapping("/question")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.ONLINE_BOOKING_QUESTION,
            resourceId = "#currentRecord.id",
            details = "#currentRecord")
    public ResponseResult<Integer> updateQuestion(
            AuthContext context, @Valid @RequestBody BookOnlineQuestionParams currentRecord) {
        if (currentRecord.getId() == null) {
            throw bizException(Code.CODE_PRIMARY_ID_EMPTY, "Primary id is empty");
        }
        currentRecord.setBusinessId(context.getBusinessId());
        return ResponseResult.success(moeGroomingBookOnlineService.updateQuestion(currentRecord));
    }

    @GetMapping("/questionList")
    @Auth(AuthType.BUSINESS)
    public QuestionListDto getQuestionList(AuthContext context, @RequestParam Integer type) {
        QuestionListDto result = new QuestionListDto();
        result.setQuestions(moeGroomingBookOnlineService.getQuestionListByBusinessId(
                context.getBusinessId(), context.companyId(), type));
        return result;
    }

    @PutMapping("/questionList")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(action = Action.BATCH_UPDATE, resourceType = ResourceType.ONLINE_BOOKING_QUESTION, details = "#params")
    public void updateQuestionList(AuthContext context, @Valid @RequestBody BookOnlineQuestionListParams params) {
        if (CollectionUtils.isEmpty(params.getQuestions())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Question list is empty");
        }
        params.setBusinessId(context.getBusinessId());
        params.setCompanyId(context.companyId());
        moeGroomingBookOnlineService.updateQuestionList(params);
    }

    @PutMapping("/question/sort")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.BATCH_UPDATE,
            resourceType = ResourceType.ONLINE_BOOKING_QUESTION,
            details = "#questionIdList")
    public ResponseResult<Integer> updateQuestionSort(
            AuthContext context, @RequestBody SortIdListParams questionIdList) {
        // DONE(Frank): 加了@RequestBody注解后，questionIdList就不可能为空了，所以这里可以去掉 questionIdList == null
        if (CollectionUtils.isEmpty(questionIdList.getIdList())) {
            return ResponseResult.success(0);
        }

        List<GroomingQuestionDTO> questions =
                moeGroomingBookOnlineService.getQuestionsByBusinessId(context.getBusinessId(), null);
        if (CollectionUtils.isEmpty(questions)) {
            return ResponseResult.success(0);
        }

        Set<Integer> idSet = new HashSet<>();
        List<Integer> targetIds = new ArrayList<>();
        questions.forEach(question -> idSet.add(question.getId()));
        questionIdList.getIdList().forEach(questionId -> {
            if (idSet.contains(questionId)) {
                targetIds.add(questionId);
            }
        });
        if (CollectionUtils.isEmpty(targetIds)) {
            return ResponseResult.success(0);
        }

        SortIdListParams params = new SortIdListParams();
        params.setIdList(targetIds);
        return ResponseResult.success(moeGroomingBookOnlineService.sortQuestion(params));
    }

    /**
     * 已检查网关日志，该接口已废弃多年
     * @param businessName
     * @param request
     * @return
     */
    @PostMapping("/v2/client/availableTime")
    @Deprecated(forRemoval = true)
    @Auth(AuthType.ANONYMOUS)
    public Map<String, OBAvailableTimeDto> getStaffAvailableTimeV2(
            @RequestParam String businessName, @Valid @RequestBody ObAvailableTimeRequest request) {
        return Map.of();
    }

    /**
     * @param businessName
     * @param lat
     * @param lng
     * @param zipcode
     * @return
     */
    @PostMapping("/checkAvailableDist")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<IsAvailableDto> checkAvailableDist(
            @RequestParam String businessName,
            @RequestParam String lat,
            @RequestParam String lng,
            @RequestParam String zipcode) {
        Integer businessId = moeGroomingBookOnlineService.getBusinessIdByBookOnlineName(businessName);
        boolean isAvailable = moeGroomingBookOnlineService.checkAvailableDist(businessId, lat, lng, zipcode);
        IsAvailableDto tmpReturn = new IsAvailableDto();
        tmpReturn.setIsAvailable(isAvailable);
        return ResponseResult.success(tmpReturn);
    }

    /**
     * book online 查询预约信息
     *
     * @param context
     * @param type
     * @return
     */
    @GetMapping("/appointment")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<PageInfo<GroomingBookingDTO>> queryGroomingBookOnlineAppointment(
            AuthContext context,
            @RequestParam(required = false, defaultValue = "1") Integer type,
            @RequestParam(required = false, defaultValue = "desc") String orderType,
            @RequestParam(required = false, defaultValue = "createTime") String orderBy,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum) {

        var migrated = migrateHelper.isMigrate(context);
        var companyId = context.companyId();
        var staffId = context.staffId();

        if (migrated) {
            permissionHelper.checkPermission(
                    context.companyId(),
                    Set.of(context.businessId()),
                    context.staffId(),
                    PermissionEnums.ACCESS_BOOKING_REQUEST_AND_WAITING_LIST);
        }
        GroomingBookingQueryVO bookingQueryVO = new GroomingBookingQueryVO();
        bookingQueryVO.setBusinessId(context.getBusinessId());
        bookingQueryVO.setOrderType(orderType);
        bookingQueryVO.setType(type);
        bookingQueryVO.setOrderBy(orderBy);

        StaffPermissions staffPermissions = businessStaffService.getBusinessRoleByStaffId(context.getStaffId());
        verifyStaffPermissions(staffPermissions, CAN_ACCESS_ONLINE_BOOKING_REQUEST_AND_WAIT_LIST);
        PageInfo<GroomingBookingDTO> result =
                obGroomingService.queryGroomingBookingAppointment(bookingQueryVO, pageNum, pageSize);

        boolean isHidePhoneNumber = migrated
                ? !permissionHelper.hasPermission(
                        companyId, staffId, PermissionEnums.ACCESS_CLIENT_EMAIL_AND_PHONE_NUMBER)
                : !checkStaffPermissionsInfo(staffPermissions, VIEW_CLIENT_PHONE);
        if (isHidePhoneNumber) {
            result.getList().forEach(appt -> appt.setPhoneNumber(phoneNumberConfusion(appt.getPhoneNumber())));
        }
        return ResponseResult.success(result);
    }

    @GetMapping("/appointment/v2")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<PageInfo<GroomingBookingDTO>> queryGroomingBookOnlineAppointmentV2(
            AuthContext context,
            @RequestParam(required = false, defaultValue = "1") Integer type,
            @RequestParam(required = false, defaultValue = "desc") String orderType,
            @RequestParam(required = false, defaultValue = "createTime") String orderBy,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum) {

        var migrated = migrateHelper.isMigrate(context);
        var companyId = context.companyId();
        var staffId = context.staffId();

        GroomingBookingQueryVO bookingQueryVO = new GroomingBookingQueryVO();
        bookingQueryVO.setBusinessId(context.getBusinessId());
        bookingQueryVO.setOrderType(orderType);
        bookingQueryVO.setType(type);
        bookingQueryVO.setOrderBy(orderBy);

        StaffPermissions staffPermissions = businessStaffService.getBusinessRoleByStaffId(context.getStaffId());
        verifyStaffPermissions(staffPermissions, CAN_ACCESS_ONLINE_BOOKING_REQUEST_AND_WAIT_LIST);
        PageInfo<GroomingBookingDTO> result = obGroomingService.queryGroomingBookingAppointmentV2(
                migrated, companyId, bookingQueryVO, pageNum, pageSize);

        boolean isHidePhoneNumber = migrated
                ? !permissionHelper.hasPermission(
                        companyId, staffId, PermissionEnums.ACCESS_CLIENT_EMAIL_AND_PHONE_NUMBER)
                : !checkStaffPermissionsInfo(staffPermissions, VIEW_CLIENT_PHONE);
        if (isHidePhoneNumber) {
            result.getList().forEach(appt -> appt.setPhoneNumber(phoneNumberConfusion(appt.getPhoneNumber())));
        }
        return ResponseResult.success(result);
    }
}
