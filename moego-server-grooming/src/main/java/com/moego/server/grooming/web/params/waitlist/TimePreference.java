package com.moego.server.grooming.web.params.waitlist;

import com.moego.server.business.dto.TimeRangeDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

@Data
public class TimePreference {
    @Schema(description = "exact time 预约起始时间，value 为当天的分钟数")
    private List<@Range(min = 0, max = 1440) Integer> exactStartTime;

    @Schema(description = "偏好的时间段")
    List<TimeRangeDto> timeRange;

    private Boolean isAnyTime;
}
