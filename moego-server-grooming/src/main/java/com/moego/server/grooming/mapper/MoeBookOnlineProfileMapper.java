package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlineProfile;
import com.moego.server.grooming.params.MoeBookOnlineProfileParams;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface MoeBookOnlineProfileMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_profile
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_profile
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlineProfile record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_profile
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlineProfile record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_profile
     *
     * @mbg.generated
     */
    MoeBookOnlineProfile selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_profile
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlineProfile record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_profile
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeBookOnlineProfile record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_profile
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlineProfile record);

    /**
     * get Record by businessId
     *
     * @param businessId 商户主键
     * @return
     */
    MoeBookOnlineProfile selectByBusinessId(Integer businessId);

    List<MoeBookOnlineProfile> getBusinessProfileList(List<Integer> businessIdList);

    int updateProfileByPrimaryIdOrBusinessId(MoeBookOnlineProfileParams params);
}
