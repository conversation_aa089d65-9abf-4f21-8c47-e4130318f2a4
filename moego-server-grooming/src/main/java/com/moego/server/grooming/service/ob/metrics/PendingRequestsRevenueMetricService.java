package com.moego.server.grooming.service.ob.metrics;

import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import java.math.BigDecimal;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/6/15
 */
@Service
@RequiredArgsConstructor
public class PendingRequestsRevenueMetricService implements IOBMetricsService {

    private final AppointmentMapperProxy appointmentMapper;

    @Override
    public BigDecimal sumMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        BigDecimal sum = appointmentMapper.countPendingOBRequestsRevenue(timeRangeDTO.businessId());
        return Objects.nonNull(sum) ? sum : BigDecimal.ZERO;
    }

    @Override
    public Object proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return "";
    }

    @Override
    public OBMetricsEnum getMetricsName() {
        return OBMetricsEnum.pending_requests_revenue;
    }
}
