package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlinePetLimit;
import java.util.List;
import org.apache.ibatis.annotations.Param;

@Deprecated
public interface MoeBookOnlinePetLimitMapper {
    @Deprecated
    int insertSelective(MoeBookOnlinePetLimit record);

    /**
     * 存量两处代码，一处改前端，一处改后端
     * @param businessId
     * @param idList
     * @return
     */
    @Deprecated
    List<MoeBookOnlinePetLimit> selectByIdList(
            @Param("businessId") Integer businessId, @Param("idList") List<Long> idList);
}
