package com.moego.server.grooming.web;

import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.BindingErrorUtil;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.service.MoeGroomingCustomerServicesService;
import com.moego.server.grooming.service.dto.MoeGroomingServiceDto;
import com.moego.server.grooming.web.vo.CustomerDeleteSaveServiceVo;
import com.moego.server.grooming.web.vo.CustomerSaveServiceVo;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grooming")
public class MoeGroomingCustomerServicesController {

    @Autowired
    private MoeGroomingCustomerServicesService moeGroomingCustomerServicesService;

    /**
     * 保存 price & time
     *
     * @param context
     * @param saveServiceVo
     * @param bindingResult
     * @return
     */
    @PutMapping("/customer/service")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Boolean> modifyCustomerService(
            AuthContext context,
            @Validated @RequestBody CustomerSaveServiceVo saveServiceVo,
            BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        if (saveServiceVo.getDuration() == null && saveServiceVo.getPrice() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "service time & price is null");
        }
        saveServiceVo.setServiceTime(saveServiceVo.getDuration());
        saveServiceVo.setServiceFee(saveServiceVo.getPrice());
        return ResponseResult.success(moeGroomingCustomerServicesService.saveCustomerService(
                context.getBusinessId(), context.companyId(), context.getStaffId(), saveServiceVo));
    }

    @DeleteMapping("/customer/service")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.DELETE,
            resourceType = ResourceType.PET_CUSTOMIZED_SERVICE,
            details = "#deleteVo",
            beforeInvocation = true)
    public ResponseResult<Boolean> deleteCustomerService(
            AuthContext context, @RequestBody CustomerDeleteSaveServiceVo deleteVo, BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        return ResponseResult.success(moeGroomingCustomerServicesService.deleteCustomerService(
                context.companyId(), context.getBusinessId(), deleteVo));
    }

    @GetMapping("/customer/service/info")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<MoeGroomingServiceDto>> queryCustomerService(
            AuthContext context,
            @RequestParam Integer customerId, // 查询 pet customized service 不需要 customer id，表中有些数据没有 customer id
            @RequestParam Integer petId) {
        return ResponseResult.success(moeGroomingCustomerServicesService.queryCustomerService(
                context.companyId(), context.getBusinessId(), petId));
    }
}
