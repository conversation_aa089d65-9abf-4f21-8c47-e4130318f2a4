package com.moego.server.grooming.web.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class Business2021SummaryDto {

    @Schema(description = "全年service duration累加")
    private Integer allServiceDurationHours;

    @Schema(description = "最长工作时间的天")
    private String longestWorkingDay;

    @Schema(description = "最长工作时间的和时长")
    private Integer longestWorkingHours;

    @Schema(description = "照顾过的pet数量")
    private Integer takeCarePetCount;

    @Schema(description = "照顾过最多的pet breed")
    private String petBreedMost;

    @Schema(description = "照顾过最多的pet breed 数量")
    private Integer petBreedMostCount;
}
