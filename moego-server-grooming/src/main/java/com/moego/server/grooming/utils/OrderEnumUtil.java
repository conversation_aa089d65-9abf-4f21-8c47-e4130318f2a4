package com.moego.server.grooming.utils;

import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.idl.models.order.v1.OrderSourceType;
import org.springframework.util.StringUtils;

/**
 * Copied from moego-svc-order.
 */
public class OrderEnumUtil {
    /**
     * 为了兼容common 包中的 OrderSourceType : noshow (不是枚举中定义的 NO_SHOW）
     * @param orderSourceType string name from DB
     * @return OrderSourceType
     */
    public static OrderSourceType convert2EnumSourceType(String orderSourceType) {
        if (!StringUtils.hasLength(orderSourceType)) {
            return OrderSourceType.UNRECOGNIZED;
        }

        if (InvoiceStatusEnum.TYPE_NOSHOW.equalsIgnoreCase(orderSourceType)) {
            return OrderSourceType.NO_SHOW;
        }
        return OrderSourceType.valueOf(orderSourceType.toUpperCase());
    }
}
