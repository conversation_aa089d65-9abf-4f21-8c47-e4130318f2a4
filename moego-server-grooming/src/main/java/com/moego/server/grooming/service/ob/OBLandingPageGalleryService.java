package com.moego.server.grooming.service.ob;

import com.moego.server.grooming.dto.ob.BookOnlineGalleryDTO;
import com.moego.server.grooming.mapper.MoeBookOnlineGalleryMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineLandingPageGalleryMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineGallery;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageGallery;
import com.moego.server.grooming.web.params.OBLandingPageConfigParams;
import com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2023/2/20
 */
@Slf4j
@Service
@AllArgsConstructor
public class OBLandingPageGalleryService {

    private final MoeBookOnlineLandingPageGalleryMapper landingPageGalleryMapper;
    private final MoeBookOnlineGalleryMapper bookOnlineGalleryMapper;

    public List<OBLandingPageConfigVO.OBLandingPageGalleryVO> listAvailableLandingPageGallery(Integer businessId) {
        MoeBookOnlineLandingPageGallery galleryCondition = new MoeBookOnlineLandingPageGallery();
        galleryCondition.setBusinessId(businessId);
        galleryCondition.setIsDeleted(Boolean.FALSE);
        List<MoeBookOnlineLandingPageGallery> galleryList =
                landingPageGalleryMapper.listGalleryByCondition(galleryCondition);
        return galleryList.stream()
                .map(gallery -> {
                    OBLandingPageConfigVO.OBLandingPageGalleryVO galleryVO =
                            new OBLandingPageConfigVO.OBLandingPageGalleryVO();
                    BeanUtils.copyProperties(gallery, galleryVO);
                    return galleryVO;
                })
                .toList();
    }

    @Transactional(rollbackFor = Exception.class)
    public void fullUpdateLandingPageGallery(
            Integer businessId,
            Long companyId,
            List<OBLandingPageConfigParams.OBLandingPageGalleryParams> galleryParamsList) {
        landingPageGalleryMapper.deleteByBusinessId(businessId);
        if (CollectionUtils.isEmpty(galleryParamsList)) {
            return;
        }
        List<MoeBookOnlineLandingPageGallery> galleryList = galleryParamsList.stream()
                .map(galleryParams -> {
                    MoeBookOnlineLandingPageGallery gallery = new MoeBookOnlineLandingPageGallery();
                    BeanUtils.copyProperties(galleryParams, gallery);
                    gallery.setBusinessId(businessId);
                    gallery.setCompanyId(companyId);
                    return gallery;
                })
                .toList();
        landingPageGalleryMapper.batchInsert(galleryList);
    }

    public List<BookOnlineGalleryDTO> listGalleryMinSortImageByBusinessId(List<Integer> businessIdList) {
        return landingPageGalleryMapper.listGalleryMinSortImageByBusinessId(businessIdList).stream()
                .map(gallery -> new BookOnlineGalleryDTO()
                        .setId(gallery.getId())
                        .setBusinessId(gallery.getBusinessId())
                        .setImagePath(gallery.getImagePath())
                        .setSort(gallery.getSort()))
                .collect(
                        // Deduplicate by business id
                        Collectors.collectingAndThen(
                                Collectors.toCollection(
                                        () -> new TreeSet<>(Comparator.comparing(BookOnlineGalleryDTO::getBusinessId))),
                                ArrayList::new));
    }

    public void inheritedOBGallery(Integer businessId, Long companyId) {
        List<MoeBookOnlineGallery> galleryList = bookOnlineGalleryMapper.selectByBusinessId(businessId);
        if (CollectionUtils.isEmpty(galleryList)) {
            log.info("biz: [{}] upgrade landing page 3.0 doesn't have gallery", businessId);
            return;
        }
        log.info("biz: [{}] inherit biz gallery", businessId);
        landingPageGalleryMapper.batchInsert(galleryList.stream()
                .map(gallery -> {
                    MoeBookOnlineLandingPageGallery landingPageGallery = new MoeBookOnlineLandingPageGallery();
                    landingPageGallery.setBusinessId(businessId);
                    landingPageGallery.setCompanyId(companyId);
                    landingPageGallery.setImagePath(gallery.getImagePath());
                    landingPageGallery.setSort(gallery.getSort());
                    return landingPageGallery;
                })
                .toList());
    }
}
