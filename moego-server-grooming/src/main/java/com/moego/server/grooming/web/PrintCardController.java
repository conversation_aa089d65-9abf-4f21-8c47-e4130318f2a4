package com.moego.server.grooming.web;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.constant.AppointmentStatusSet;
import com.moego.server.grooming.dto.printcard.ActivityCardDetail;
import com.moego.server.grooming.dto.printcard.PrintCardDetail;
import com.moego.server.grooming.dto.printcard.StayCardDetail;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.service.MoeAppointmentQueryService;
import com.moego.server.grooming.service.printcard.PrintCardService;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grooming/printcard")
public class PrintCardController {

    @Autowired
    private PrintCardService printCardService;

    @Autowired
    private MoeAppointmentQueryService appointmentQueryService;

    @GetMapping("/list")
    @Auth(AuthType.COMPANY)
    public List<PrintCardDetail> getPrintCardInfoByDate(@RequestParam String date) {
        return printCardService.getPrintCardInfoByDate(
                date, AuthContext.get().businessId(), AuthContext.get().companyId());
    }

    @GetMapping("/one")
    @Auth(AuthType.COMPANY)
    public List<PrintCardDetail> getPrintCardInfoByGroomingId(@RequestParam Long groomingId) {
        long companyId = AuthContext.get().companyId();
        long businessId = AuthContext.get().businessId();
        MoeGroomingAppointment appointment = appointmentQueryService.getAppointmentById(groomingId.intValue());
        if (appointment == null || (!Objects.equals(appointment.getCompanyId(), companyId))) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "grooming is empty");
        }
        return printCardService.getPrintCardInfo(
                businessId, companyId, List.of(appointment), appointment.getAppointmentDate());
    }

    @GetMapping("/stay/list")
    @Auth(AuthType.COMPANY)
    public List<StayCardDetail> getStayCardInfoByDate(
            @RequestParam String date,
            @RequestParam(required = false) List<Integer> serviceItemTypes,
            @RequestParam(required = false) Boolean isPetsCheckedInOnly) {
        List<AppointmentStatusEnum> statusList = List.of(AppointmentStatusEnum.CHECK_IN); // 兼容旧版本，默认是仅查询 check in 的预约
        if (Boolean.FALSE.equals(isPetsCheckedInOnly)) {
            statusList = AppointmentStatusSet.ACTIVE_STATUS_SET;
        }
        return printCardService.getStayCardInfoByDate(
                date,
                serviceItemTypes,
                AuthContext.get().businessId(),
                AuthContext.get().companyId(),
                statusList);
    }

    @GetMapping("/stay/one")
    @Auth(AuthType.COMPANY)
    public List<StayCardDetail> getStayCardInfoByGroomingId(@RequestParam Long groomingId) {
        long companyId = AuthContext.get().companyId();
        long businessId = AuthContext.get().businessId();
        MoeGroomingAppointment appointment = appointmentQueryService.getAppointmentById(groomingId.intValue());
        if (appointment == null || (!Objects.equals(appointment.getCompanyId(), companyId))) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "grooming is empty");
        }
        return printCardService.getStayCardInfo(businessId, companyId, List.of(appointment));
    }

    @GetMapping("/activity")
    @Auth(AuthType.COMPANY)
    public ActivityCardDetail getActivityCardInfoByDate(
            @RequestParam String date,
            @RequestParam(required = false) List<Integer> serviceItemTypes,
            @RequestParam(required = false) Boolean isPetsCheckedInOnly) {
        List<AppointmentStatusEnum> statusList = AppointmentStatusSet.ACTIVE_STATUS_SET;
        if (Boolean.TRUE.equals(isPetsCheckedInOnly)) {
            statusList = List.of(AppointmentStatusEnum.CHECK_IN);
        }
        return printCardService.getActivityCardInfoByDate(
                date,
                serviceItemTypes,
                AuthContext.get().businessId(),
                AuthContext.get().companyId(),
                statusList);
    }
}
