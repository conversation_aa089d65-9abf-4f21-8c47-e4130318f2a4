package com.moego.server.grooming.web.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class QBCreateConnectVo {
    @JsonIgnore
    private Integer companyId;

    @JsonIgnore
    private Integer businessId;

    @NotNull
    @Parameter(required = true)
    private String authCode;

    @NotNull
    @Parameter(required = true)
    private String realmId;

    @NotNull
    @Parameter(required = true)
    @Schema(description = "v1: 单个 business id， v2：逗号隔开的business id 列表", example = "10001, 10002")
    private String state;

    @Schema(description = "旧版本是 0， 新版本是 1")
    private Integer userVersion;
}
