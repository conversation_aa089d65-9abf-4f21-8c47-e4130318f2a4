package com.moego.server.grooming.web.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;
import lombok.Builder;
import org.springframework.format.annotation.DateTimeFormat;

@Builder(toBuilder = true)
public record BatchDrivingInfoParams(
        @Schema(description = "token business id", hidden = true) Integer businessId,
        @Schema(description = "查询 staff id 列表，不能为空") @NotEmpty List<Integer> staffIdList,
        @Schema(description = "查询开始日期，必传") @NotNull @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
        @Schema(description = "查询结束日期，非必传，不传时，只查 startDate 一天") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
                LocalDate endDate) {}
