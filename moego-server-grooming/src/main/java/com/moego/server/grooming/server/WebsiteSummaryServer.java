package com.moego.server.grooming.server;

import com.moego.server.grooming.api.IWebsiteSummaryServiceBase;
import com.moego.server.grooming.service.WebsiteSummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
public class WebsiteSummaryServer extends IWebsiteSummaryServiceBase {

    @Autowired
    private WebsiteSummaryService websiteSummaryService;

    @Override
    public boolean refreshCache() {
        websiteSummaryService.refreshCache();
        return true;
    }
}
