package com.moego.server.grooming.web;

import com.moego.common.response.ResponseResult;
import com.moego.common.utils.BindingErrorUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.dto.AddResultDTO;
import com.moego.server.grooming.dto.ConflictDayInfoDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingRepeat;
import com.moego.server.grooming.params.AddRepeatParams;
import com.moego.server.grooming.params.AppointmentCheckParams;
import com.moego.server.grooming.params.EditRepeatParams;
import com.moego.server.grooming.params.PreviewRepeatParams;
import com.moego.server.grooming.params.RepeatWithSSParams;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.server.grooming.service.MoeRepeatService;
import com.moego.server.grooming.web.params.MoeGroomingRepeatParam;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grooming")
public class MoeRepeatController {

    @Autowired
    private MoeRepeatService moeRepeatService;

    @Autowired
    private MoeGroomingAppointmentService appointmentService;

    /**
     * 日期规则生成校验
     * @param repeatParams
     * @return
     */
    @PostMapping("/repeat/preview")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<ConflictDayInfoDTO>> previewRepeatDay(
            AuthContext context, @Valid @RequestBody PreviewRepeatParams repeatParams) {
        if (repeatParams == null || CollectionUtils.isEmpty(repeatParams.getRepeatStaffInfoParams())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "staff info is not empty");
        }

        repeatParams.setBusinessId(context.getBusinessId());
        return ResponseResult.success(moeRepeatService.previewRepeatDay(repeatParams));
    }

    @PostMapping("/repeat/ss/preview")
    @Auth(AuthType.BUSINESS)
    public List<ConflictDayInfoDTO> previewRepeatDayWithSS(
            AuthContext context, @Valid @RequestBody RepeatWithSSParams repeatParams) {
        repeatParams.setCompanyId(context.getCompanyId());
        repeatParams.setBusinessId(context.getBusinessId());
        repeatParams.setStaffId(context.getStaffId());
        return moeRepeatService.previewRepeatDaySmartScheduling(repeatParams);
    }

    @GetMapping("/repeat/ss/available")
    @Auth(AuthType.BUSINESS)
    public Integer getRepeatWithSSAvailability(AuthContext context) {
        return moeRepeatService.getRepeatWithSSAvailableTimes(context.getBusinessId());
    }

    /**
     * 查询business下所有的 repeat rule
     * @param context
     * @return
     */
    @GetMapping("/repeat/rule/all")
    @Auth(AuthType.BUSINESS)
    public List<MoeGroomingRepeat> queryAllBusinessRepeatRule(AuthContext context) {
        return moeRepeatService.queryAllRepeatRuleByBusinessId(context.getBusinessId());
    }

    /**
     * 添加规则
     * @param context
     * @param repeatParams
     * DONE(Frank): customerId->businessId
     * @return
     */
    @PostMapping("/repeat/rule")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.CREATE,
            resourceType = ResourceType.REPEAT_RULE,
            resourceId = "#result.data.id",
            details = "#repeatParams")
    public ResponseResult<AddResultDTO> addRepeatRule(
            AuthContext context, @Validated @RequestBody AddRepeatParams repeatParams, BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);

        if (repeatParams.getCustomerId() != null) {
            appointmentService.checkBusinessCustomer(context.getBusinessId(), repeatParams.getCustomerId());
        }
        repeatParams.setBusinessId(context.getBusinessId());
        repeatParams.setStaffId(context.getStaffId());
        repeatParams.setCompanyId(context.companyId());
        return ResponseResult.success(moeRepeatService.addRepeatRule(repeatParams));
    }

    /**
     * repeat rule 全量修改(除了 business_id 和 customer_id)
     * @param context
     * @param param
     */
    @PutMapping("/repeat/rule/full")
    @Auth(AuthType.BUSINESS)
    public void modifyFullRepeatRule(AuthContext context, @Validated @RequestBody MoeGroomingRepeatParam param) {
        moeRepeatService.modifyFullRepeatRule(context.getBusinessId(), param);
    }

    /**
     * 修改repeat rule
     * @param editRepeatParams
     * DONE(Frank): repeatId->businessId
     * @return
     */
    @PutMapping("/repeat/rule")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.REPEAT_RULE,
            resourceId = "#editRepeatParams.repeatId",
            details = "#editRepeatParams")
    public ResponseResult<Integer> modifyRepeatRule(
            AuthContext context, @Valid @RequestBody EditRepeatParams editRepeatParams) {
        editRepeatParams.setBusinessId(context.getBusinessId());
        editRepeatParams.setStaffId(context.getStaffId());
        return ResponseResult.success(moeRepeatService.modifyRepeatRule(editRepeatParams));
    }

    @GetMapping("/repeat/rule")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<MoeGroomingRepeat> queryRepeatRule(AuthContext context, @RequestParam Integer repeatId) {
        return ResponseResult.success(moeRepeatService.queryRepeatRule(context.getBusinessId(), repeatId));
    }

    /**
     * 时间冲突校验
     * @param appointmentCheckParams
     * @return
     */
    @PutMapping("/repeat/conflict")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<ConflictDayInfoDTO>> checkConflictDay(
            AuthContext context,
            @Validated @RequestBody List<AppointmentCheckParams> appointmentCheckParams,
            BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        for (AppointmentCheckParams appointmentCheck : appointmentCheckParams) {
            appointmentCheck.setBusinessId(context.getBusinessId());
        }
        return ResponseResult.success(moeRepeatService.checkConflictDay(appointmentCheckParams));
    }
}
