package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackage;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackageExample;
import com.moego.server.grooming.service.dto.UsedPackageWithPrice;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingInvoiceApplyPackageMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_invoice_apply_package
     *
     * @mbg.generated
     */
    long countByExample(MoeGroomingInvoiceApplyPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_invoice_apply_package
     *
     * @mbg.generated
     */
    int deleteByExample(MoeGroomingInvoiceApplyPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_invoice_apply_package
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_invoice_apply_package
     *
     * @mbg.generated
     */
    int insert(MoeGroomingInvoiceApplyPackage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_invoice_apply_package
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingInvoiceApplyPackage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_invoice_apply_package
     *
     * @mbg.generated
     */
    List<MoeGroomingInvoiceApplyPackage> selectByExample(MoeGroomingInvoiceApplyPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_invoice_apply_package
     *
     * @mbg.generated
     */
    MoeGroomingInvoiceApplyPackage selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_invoice_apply_package
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeGroomingInvoiceApplyPackage record,
            @Param("example") MoeGroomingInvoiceApplyPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_invoice_apply_package
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeGroomingInvoiceApplyPackage record,
            @Param("example") MoeGroomingInvoiceApplyPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_invoice_apply_package
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingInvoiceApplyPackage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_invoice_apply_package
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingInvoiceApplyPackage record);

    List<MoeGroomingInvoiceApplyPackage> selectByInvoiceId(@Param("invoiceId") Integer invoiceId);

    List<MoeGroomingInvoiceApplyPackage> selectByInvoiceIds(@Param("invoiceIds") List<Integer> invoiceIds);

    List<UsedPackageWithPrice> selectByInvoiceIdWithPrice(@Param("invoiceIds") List<Integer> invoiceIds);

    int batchUpdateAppliedRecord(@Param("records") List<MoeGroomingInvoiceApplyPackage> records);
}
