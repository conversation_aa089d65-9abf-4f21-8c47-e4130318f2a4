package com.moego.server.grooming.mapper;

import com.moego.server.grooming.dto.ServiceCategoryDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceCategory;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingServiceCategoryMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_category
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_category
     *
     * @mbg.generated
     */
    int insert(MoeGroomingServiceCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_category
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingServiceCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_category
     *
     * @mbg.generated
     */
    MoeGroomingServiceCategory selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_category
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingServiceCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_category
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingServiceCategory record);

    List<MoeGroomingServiceCategory> selectByIdSet(@Param(value = "idSet") Set<Integer> idSet);

    // <<<<<<<<<<<<<<<<<<<<<<< company new interface start

    // 新数据接口
    int updateByPrimaryKeySelectiveWithCid(MoeGroomingServiceCategory record);

    // 已检查新旧数据兼容，对应的新数据查询为 selectByCompanyId
    List<MoeGroomingServiceCategory> selectByBusinessId(
            @Param(value = "businessId") Integer businessId, @Param(value = "type") Byte type);

    // 新旧数据兼容之新接口
    List<MoeGroomingServiceCategory> selectByCompanyId(
            @Param(value = "companyId") Long companyId,
            @Param(value = "type") Byte type,
            @Param(value = "serviceItemType") Integer serviceItemType);

    // 不需要数据兼容的接口
    int batchUpdateSort(@Param(value = "serviceCategoryList") List<MoeGroomingServiceCategory> serviceCategoryList);

    // 新数据接口
    List<MoeGroomingServiceCategory> selectCategoryByCidCategoryIds(Long companyId, List<Integer> categoryIdList);

    // 已检查新旧数据接口，新接口为 getCategoryCountWithNameByCompanyId
    Integer getCategoryCountWithName(
            @Param(value = "businessId") Integer businessId,
            @Param(value = "name") String name,
            @Param(value = "type") Byte type,
            @Param(value = "updateCategoryId") Integer updateCategoryId);
    // 新旧数据兼容之新接口
    Integer getCategoryCountWithNameByCompanyId(
            @Param(value = "companyId") Long companyId,
            @Param(value = "name") String name,
            @Param(value = "type") Byte type,
            @Param(value = "updateCategoryId") Integer updateCategoryId,
            @Param(value = "serviceItemType") Integer serviceItemType);
    // 新旧数据兼容之新接口
    Integer batchCountCategoryName(
            @Param(value = "companyId") Long companyId,
            @Param(value = "type") Byte type,
            @Param(value = "nameList") List<String> nameList,
            @Param(value = "updateCategoryIdList") List<Integer> updateCategoryIdList,
            @Param(value = "serviceItemType") Integer serviceItemType);

    // 已检查新旧数据接口，新接口为 selectCategoryByCompanyId
    List<ServiceCategoryDTO> selectCategoryByBusinessId(
            @Param("businessId") Integer businessId, @Param(value = "type") Integer type);
    // 新旧数据兼容之新接口
    List<ServiceCategoryDTO> selectCategoryByCompanyId(
            @Param("companyId") Long companyId,
            @Param(value = "type") Integer type,
            @Param(value = "serviceItemType") Integer serviceItemType);
    // <<<<<<<<<<<<<<<<<<<<<<< company new interface end

}
