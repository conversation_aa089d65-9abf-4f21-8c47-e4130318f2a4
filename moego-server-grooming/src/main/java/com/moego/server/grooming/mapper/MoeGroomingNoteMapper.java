package com.moego.server.grooming.mapper;

import com.moego.server.grooming.dto.HistoryCommentsDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingNoteExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingNoteMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note
     *
     * @mbg.generated
     */
    long countByExample(MoeGroomingNoteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note
     *
     * @mbg.generated
     */
    int deleteByExample(MoeGroomingNoteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note
     *
     * @mbg.generated
     */
    int insert(MoeGroomingNote record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingNote record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note
     *
     * @mbg.generated
     */
    List<MoeGroomingNote> selectByExampleWithBLOBs(MoeGroomingNoteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note
     *
     * @mbg.generated
     */
    List<MoeGroomingNote> selectByExample(MoeGroomingNoteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note
     *
     * @mbg.generated
     */
    MoeGroomingNote selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeGroomingNote record, @Param("example") MoeGroomingNoteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("record") MoeGroomingNote record, @Param("example") MoeGroomingNoteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MoeGroomingNote record, @Param("example") MoeGroomingNoteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingNote record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeGroomingNote record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_note
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingNote record);

    List<MoeGroomingNote> selectByGroomingId(@Param("groomingId") Integer id, @Param("type") Integer type);

    List<HistoryCommentsDTO> queryCustomerHistoryComments(
            @Param("businessId") Integer tokenBusinessId,
            @Param("customerId") Integer customerId,
            @Param("petId") Long petId);

    int batchInsert(@Param("recordList") List<MoeGroomingNote> recordList);

    List<MoeGroomingNote> selectByGroomingIds(
            @Param("groomingIds") List<Integer> groomingIds, @Param("type") Integer type);

    MoeGroomingNote selectLastNoteByCustomerId(
            @Param("companyId") Long companyId,
            @Param("businessId") Integer businessId,
            @Param("customerId") Integer customerId,
            @Param("type") Byte type);
}
