package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingServiceLocation;
import java.util.List;

public interface MoeGroomingServiceLocationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_location
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_location
     *
     * @mbg.generated
     */
    int insert(MoeGroomingServiceLocation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_location
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingServiceLocation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_location
     *
     * @mbg.generated
     */
    MoeGroomingServiceLocation selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_location
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingServiceLocation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_location
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingServiceLocation record);

    List<MoeGroomingServiceLocation> selectByServiceId(Long companyId, Integer serviceId, Boolean isIncludeDeleted);

    List<MoeGroomingServiceLocation> selectNoDeletedByCompanyId(Long companyId);

    MoeGroomingServiceLocation selectWithDeletedByBidServiceId(Long companyId, Integer businessId, Integer serviceId);

    int updateSetDeletedByUniqueIndex(Long companyId, Integer businessId, Integer serviceId);

    List<MoeGroomingServiceLocation> selectNoDeletedByBusinessId(Long companyId, Integer businessId);

    List<MoeGroomingServiceLocation> selectNoDeletedByBusinessIdSids(
            Long companyId, Integer businessId, List<Integer> serviceIds);
}
