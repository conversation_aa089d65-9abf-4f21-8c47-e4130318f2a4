package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.EvaluationServiceDetail;
import com.moego.server.grooming.mapperbean.EvaluationServiceDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface EvaluationServiceDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluation_service_detail
     *
     * @mbg.generated
     */
    long countByExample(EvaluationServiceDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluation_service_detail
     *
     * @mbg.generated
     */
    int deleteByExample(EvaluationServiceDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluation_service_detail
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluation_service_detail
     *
     * @mbg.generated
     */
    int insert(EvaluationServiceDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluation_service_detail
     *
     * @mbg.generated
     */
    int insertSelective(EvaluationServiceDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluation_service_detail
     *
     * @mbg.generated
     */
    List<EvaluationServiceDetail> selectByExample(EvaluationServiceDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluation_service_detail
     *
     * @mbg.generated
     */
    EvaluationServiceDetail selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluation_service_detail
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") EvaluationServiceDetail record, @Param("example") EvaluationServiceDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluation_service_detail
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") EvaluationServiceDetail record, @Param("example") EvaluationServiceDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluation_service_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(EvaluationServiceDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluation_service_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(EvaluationServiceDetail record);

    List<EvaluationServiceDetail> queryByAppointmentIds(List<Long> appointmentList);

    /**
     * 有性能问题，待 Staff 视角的 appointment arrangement 支持后，切换查询逻辑
     * 与 petDetail 表查询一致 @{link com.moego.server.grooming.mapper.MoeGroomingAppointmentMapper#selectCustomerIdWithInAllAppointmentDate}
     * @param businessId
     * @param staffId
     * @return
     */
    List<Integer> selectCustomerIdWithInAllAppointmentDate(
            @Param("businessId") Integer businessId, @Param("staffId") Integer staffId);
}
