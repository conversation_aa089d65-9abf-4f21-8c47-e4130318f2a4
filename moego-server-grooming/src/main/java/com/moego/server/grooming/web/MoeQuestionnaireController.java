package com.moego.server.grooming.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.mapperbean.MoeQuestionnaire;
import com.moego.server.grooming.service.MoeQuestionnaireService;
import com.moego.server.grooming.web.params.SubmitQuestionnaireParams;
import com.moego.server.grooming.web.vo.QuestionnaireVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/grooming")
@Validated
public class MoeQuestionnaireController {

    @Autowired
    private MoeQuestionnaireService moeQuestionnaireService;

    @PostMapping("/2023/questionnaire")
    @Auth(AuthType.BUSINESS)
    public void submitQuestionnaire(AuthContext context, @Validated @RequestBody SubmitQuestionnaireParams params) {
        moeQuestionnaireService.submitQuestionnaire(params.toBuilder()
                .companyId(context.companyId())
                .businessId(context.businessId())
                .staffId(context.staffId())
                .build());
    }

    @GetMapping("/2023/questionnaire")
    @Auth(AuthType.BUSINESS)
    public QuestionnaireVO searchQuestionnaire(AuthContext context) {
        MoeQuestionnaire record = moeQuestionnaireService.getMoeQuestionnaire(context.businessId(), context.staffId());
        if (record == null) {
            return null;
        }
        return QuestionnaireVO.builder().formDetail(record.getFormDetail()).build();
    }
}
