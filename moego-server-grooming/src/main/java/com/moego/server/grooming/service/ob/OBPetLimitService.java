package com.moego.server.grooming.service.ob;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.smart_scheduler.v1.TimeSlotType;
import com.moego.server.business.dto.AppointmentPetIdDTO;
import com.moego.server.business.dto.PetSizeDTO;
import com.moego.server.business.dto.StaffTimeslotPetCountDTO;
import com.moego.server.customer.client.IPetBreedClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.MoePetBreedDTO;
import com.moego.server.grooming.dto.LimitDto;
import com.moego.server.grooming.dto.LimitGroupDTO;
import com.moego.server.grooming.dto.PetBreedLimitDto;
import com.moego.server.grooming.dto.PetSizeLimitDto;
import com.moego.server.grooming.dto.ServiceLimitDto;
import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import com.moego.server.grooming.service.BookOnlinePetLimitService;
import com.moego.server.grooming.service.dto.ob.LimitationResultDTO;
import com.moego.server.grooming.service.dto.ob.OBPetLimitFilterDTO;
import com.moego.server.grooming.service.dto.ob.PetAvailableDTO;
import jakarta.annotation.Nonnull;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class OBPetLimitService {

    private final BookOnlinePetLimitService bookOnlinePetLimitService;

    private final IPetClient iPetClient;

    private final IPetBreedClient iPetBreedClient;

    private static final Long NO_MORE_QUANTITY = -1L;

    public OBPetLimitFilterDTO generateWithCurrentAppointment(
            Long companyId,
            Integer businessId,
            List<OBPetDataDTO> petParamList,
            final @NotNull List<Integer> serviceIds) {
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();

        if (CollectionUtils.isEmpty(petParamList)) {
            return obPetLimitFilterDTO;
        }

        // 查询所有的 limit 设置项
        // 构造本次预约 pet 的 size 数量、breed 数量、type 数量
        Map<BigDecimal, Long> currentSizeMap = petParamList.stream()
                .map(OBPetDataDTO::getWeight)
                .filter(StringUtils::hasText)
                .map(BigDecimal::new)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        Map<Integer, Long> currentTypeMap = petParamList.stream()
                .map(OBPetDataDTO::getPetTypeId)
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        Map<String, Long> currentBreedMap = petParamList.stream()
                .map(OBPetDataDTO::getBreed)
                .filter(StringUtils::hasText)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        Map<Long, Long> currentServiceMap =
                serviceIds.stream().collect(Collectors.groupingBy(Long::valueOf, Collectors.counting()));

        Map<Integer /* staff id */, Map<Long /* service id */, Long /* count */>> currentStaffServiceMap =
                new HashMap<>();
        var hasStaff = petParamList.stream().allMatch(param -> CommonUtil.isNormal(param.getStaffId()));
        if (hasStaff) {
            for (final var param : petParamList) {
                var staffId = param.getStaffId();
                Optional.ofNullable(param.getServiceIds()).orElse(List.of()).forEach(serviceId -> {
                    currentStaffServiceMap
                            .computeIfAbsent(staffId, k -> new HashMap<>())
                            .merge(serviceId.longValue(), 1L, Long::sum);
                });
            }
        } else {
            petParamList.stream()
                    .map(OBPetDataDTO::getServiceStaffMap)
                    .filter(Predicate.not(CollectionUtils::isEmpty))
                    .forEach(entry -> {
                        entry.forEach((serviceId, staffIds) -> staffIds.forEach(staffId -> {
                            currentStaffServiceMap
                                    .computeIfAbsent(staffId, k -> new HashMap<>())
                                    .merge(serviceId.longValue(), 1L, Long::sum);
                        }));
                    });
        }

        // 查询所有 pet breed 数据
        Map<Integer, MoePetBreedDTO> petBreedMap = iPetBreedClient.getPetBreed(businessId);
        // 查询所有 pet size 数据
        Map<Long, PetSizeDTO> petSizeMap = bookOnlinePetLimitService.getPetSizeList(companyId, businessId);
        // 查询所有 service 数据
        Map<Long, ServiceModel> serviceMap = bookOnlinePetLimitService.getServiceMap(companyId, businessId);

        obPetLimitFilterDTO.setCurrentSizeMap(currentSizeMap);
        obPetLimitFilterDTO.setCurrentTypeMap(currentTypeMap);
        obPetLimitFilterDTO.setCurrentBreedMap(currentBreedMap);
        obPetLimitFilterDTO.setCurrentServiceMap(currentServiceMap);
        obPetLimitFilterDTO.setCurrentStaffServiceMap(currentStaffServiceMap);
        obPetLimitFilterDTO.setPetSizeMap(petSizeMap);
        obPetLimitFilterDTO.setPetBreedMap(petBreedMap);
        obPetLimitFilterDTO.setServiceMap(serviceMap);

        var allPetHasIndex = petParamList.stream().allMatch(pet -> CommonUtil.isNormal(pet.getPetIndex()));
        if (!allPetHasIndex) {
            AtomicInteger atomicInteger = new AtomicInteger(0);
            petParamList.forEach(pet -> pet.setPetIndex(atomicInteger.getAndIncrement()));
        }

        var petIndexList = petParamList.stream()
                .map(OBPetDataDTO::getPetIndex)
                .filter(Objects::nonNull)
                .toList();
        obPetLimitFilterDTO.setPetIndexSubList(generateNonEmptySubLists(petIndexList));
        var petIndexMap =
                petParamList.stream().collect(Collectors.toMap(OBPetDataDTO::getPetIndex, Function.identity()));
        obPetLimitFilterDTO.setPetIndexMap(petIndexMap);

        return obPetLimitFilterDTO;
    }

    public OBPetLimitFilterDTO fillWithExistingAppointmentV2(
            OBPetLimitFilterDTO obPetLimitFilterDTO, Collection<StaffTimeslotPetCountDTO> petIdDTOList) {
        if (Objects.isNull(obPetLimitFilterDTO)) {
            return new OBPetLimitFilterDTO();
        }
        if (CollectionUtils.isEmpty(petIdDTOList)) {
            return obPetLimitFilterDTO;
        }

        // 构造当前时间段已有的预约数据
        Map<String, List<AppointmentPetIdDTO>> petAppointmentMap = petIdDTOList.stream()
                .map(dto -> dto.getPetDetails().stream()
                        .map(petDetail -> AppointmentPetIdDTO.builder()
                                .petId(petDetail.getPetId())
                                .staffId(dto.getStaffId())
                                .appointmentDate(dto.getSlotDate())
                                .appointmentStartTime(dto.getSlotTime())
                                .serviceIds(List.of(petDetail.getServiceId()))
                                .appointmentId(petDetail.getGroomingId())
                                .build())
                        .toList())
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(AppointmentPetIdDTO::getAppointmentDate));

        List<Integer> petIdList = petAppointmentMap.values().stream()
                .flatMap(Collection::stream)
                .map(AppointmentPetIdDTO::getPetId)
                .distinct()
                .toList();

        // 查询当前时间段已有的预约对应的 pet 信息
        Map<Integer, CustomerPetDetailDTO> petIdDetailMap = iPetClient.getCustomerPetListByIdList(petIdList).stream()
                .collect(Collectors.toMap(CustomerPetDetailDTO::getPetId, Function.identity()));

        obPetLimitFilterDTO.setPetAppointmentMap(petAppointmentMap);
        obPetLimitFilterDTO.setPetIdDetailMap(petIdDetailMap);
        return obPetLimitFilterDTO;
    }

    public OBPetLimitFilterDTO fillWithExistingAppointment(
            OBPetLimitFilterDTO obPetLimitFilterDTO, List<AppointmentPetIdDTO> petIdDTOList) {
        if (Objects.isNull(obPetLimitFilterDTO)) {
            return new OBPetLimitFilterDTO();
        }
        if (CollectionUtils.isEmpty(petIdDTOList)) {
            return obPetLimitFilterDTO;
        }

        // 根据 appointmentId, staffId, petId 分组，合并同一 appointment 下 pet 的连续 service
        var newPetIdDTOList = petIdDTOList.stream()
                .collect(Collectors.groupingBy(
                        dto -> Triple.of(dto.getAppointmentId(), dto.getStaffId(), dto.getPetId())))
                .entrySet()
                .stream()
                .map(entry -> {
                    var triple = entry.getKey();
                    var dtoList = entry.getValue();
                    var appointmentPetIdDTO = new AppointmentPetIdDTO();
                    appointmentPetIdDTO.setAppointmentId(triple.getLeft());
                    appointmentPetIdDTO.setStaffId(triple.getMiddle());
                    appointmentPetIdDTO.setPetId(triple.getRight());
                    appointmentPetIdDTO.setAppointmentDate(dtoList.stream()
                            .map(AppointmentPetIdDTO::getAppointmentDate)
                            .filter(Objects::nonNull)
                            .findFirst()
                            .orElse(null));
                    appointmentPetIdDTO.setAppointmentStartTime(dtoList.stream()
                            .map(AppointmentPetIdDTO::getAppointmentStartTime)
                            .filter(Objects::nonNull)
                            .min(Integer::compareTo)
                            .orElse(0));
                    appointmentPetIdDTO.setServiceIds(dtoList.stream()
                            .map(AppointmentPetIdDTO::getServiceIds)
                            .filter(Objects::nonNull)
                            .flatMap(Collection::stream)
                            .toList());
                    return appointmentPetIdDTO;
                })
                .toList();

        // 构造当前时间段已有的预约数据
        Map<String, List<AppointmentPetIdDTO>> petAppointmentMap =
                newPetIdDTOList.stream().collect(Collectors.groupingBy(AppointmentPetIdDTO::getAppointmentDate));
        List<Integer> petIdList = newPetIdDTOList.stream()
                .map(AppointmentPetIdDTO::getPetId)
                .distinct()
                .toList();

        // 查询当前时间段已有的预约对应的 pet 信息
        Map<Integer, CustomerPetDetailDTO> petIdDetailMap = iPetClient.getCustomerPetListByIdList(petIdList).stream()
                .collect(Collectors.toMap(CustomerPetDetailDTO::getPetId, Function.identity()));

        obPetLimitFilterDTO.setPetAppointmentMap(petAppointmentMap);
        obPetLimitFilterDTO.setPetIdDetailMap(petIdDetailMap);
        return obPetLimitFilterDTO;
    }

    /**
     * 生成所有非空子列表的方法
     *
     * @param list 输入列表
     * @return 所有非空子列表的列表
     */
    public static <T> List<Set<T>> generateNonEmptySubLists(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return List.of();
        }

        List<Set<T>> result = new ArrayList<>();
        int n = list.size();

        // 使用二进制表示法生成所有可能的子列表
        // 总共有 2^n - 1 个非空子列表 (减去空列表)
        int totalSubLists = (1 << n) - 1;

        for (int i = 1; i <= totalSubLists; i++) {
            Set<T> subList = new HashSet<>();

            // 检查每一位是否为1，如果是则添加对应元素
            for (int j = 0; j < n; j++) {
                if ((i & (1 << j)) != 0) {
                    subList.add(list.get(j));
                }
            }

            result.add(subList);
        }

        return result;
    }

    /**
     * 后续删除，使用 judgePetLimitAndGetPetRemainQuantity
     * @param obPetLimitFilterDTO
     * @param staffId
     * @param date
     * @param limitDto
     * @param startTime
     * @return
     */
    public static boolean judgePetLimit(
            OBPetLimitFilterDTO obPetLimitFilterDTO,
            Integer staffId,
            String date,
            LimitDto limitDto,
            Integer startTime) {
        if (Objects.isNull(obPetLimitFilterDTO)) {
            return Boolean.FALSE;
        }

        if (limitDto == null || limitDto.isEmpty()) {
            log.debug("[ob limit] current time have not set pet limit. staffId:{}, date:{}", staffId, date);
            return Boolean.FALSE;
        }

        Map<String, List<AppointmentPetIdDTO>> petAppointmentMap = obPetLimitFilterDTO.getPetAppointmentMap();
        Map<Integer, CustomerPetDetailDTO> petIdDetailMap = obPetLimitFilterDTO.getPetIdDetailMap();
        Map<Integer, MoePetBreedDTO> petBreedMap = obPetLimitFilterDTO.getPetBreedMap();
        Map<Long, PetSizeDTO> petSizeMap = obPetLimitFilterDTO.getPetSizeMap();
        Map<Long, ServiceModel> serviceMap = obPetLimitFilterDTO.getServiceMap();

        // 2.0 get staff's pet limit [skip]
        // 2.1 get size map, key is weight range, value is quantity
        Map<Pair<BigDecimal, BigDecimal>, Integer> limitSizeMap =
                getLimitSizeMap(limitDto.getPetSizeLimitList(), petSizeMap);
        // 2.2 get type map, key is typeId, value is quantity
        Map<Integer, Integer> limitTypeMap = getLimitTypeMap(limitDto.getPetBreedLimitList());
        // 2.3 get breed map, key is breed name, value is quantity
        Map<String, Integer> limitBreedMap = getLimitBreedMap(petBreedMap, limitDto.getPetBreedLimitList());
        // 2.4 get breed union map, key is breed union name, value is quantity
        Map<List<String>, Integer> limitBreedUnionMap =
                getLimitBreedUnionMap(petBreedMap, limitDto.getPetBreedLimitList());
        // 2.5 get service map, key is service id, value is quantity
        Map<Set<Long>, Integer> limitServiceMap = getLimitServiceMap(serviceMap, limitDto.getServiceLimitList());
        if (log.isDebugEnabled()) {
            log.debug(
                    "2. staff limit. staffId:{}, date:{}, limitSizeMap:{}, limitTypeMap:{}, limitBreedMap:{}, limitBreedUnionMap:{}, limitServiceMap:{}",
                    staffId,
                    date,
                    limitSizeMap,
                    limitTypeMap,
                    limitBreedMap,
                    limitBreedUnionMap,
                    limitServiceMap);
        }

        // 3.0 subtract from existing appointment's pet quantity
        if (petAppointmentMap.containsKey(date)) {
            var matchedAppointmentPetIdDTOs = petAppointmentMap.get(date).stream()
                    .filter(appointmentPetIdDTO -> {
                        if (Objects.isNull(startTime)) {
                            // working hour
                            return Objects.equals(appointmentPetIdDTO.getStaffId(), staffId);
                        } else {
                            // time slot
                            return (Objects.equals(appointmentPetIdDTO.getStaffId(), staffId)
                                    && Objects.equals(appointmentPetIdDTO.getAppointmentStartTime(), startTime));
                        }
                    })
                    .toList();
            // subtract by pet
            matchedAppointmentPetIdDTOs.stream()
                    .map(AppointmentPetIdDTO::getPetId)
                    .distinct()
                    .map(petIdDetailMap::get)
                    .filter(Objects::nonNull)
                    .forEach(pet -> {
                        if (StringUtils.hasText(pet.getWeight())) {
                            // Rounded pet weight, make sure the weight falls within the pet size gap
                            BigDecimal roundedPetWeight =
                                    new BigDecimal(pet.getWeight()).setScale(2, RoundingMode.HALF_UP);
                            subtractSizeQuantity(limitSizeMap, roundedPetWeight);
                        }
                        if (Objects.nonNull(pet.getPetTypeId())) {
                            subtractTypeQuantity(limitTypeMap, pet.getPetTypeId());
                        }
                        if (StringUtils.hasText(pet.getBreed())) {
                            subtractBreedQuantity(limitBreedMap, pet.getBreed());
                            subtractBreedUnionQuantity(limitBreedUnionMap, pet.getBreed());
                        }
                    });
            // subtract by service
            matchedAppointmentPetIdDTOs.stream()
                    .map(AppointmentPetIdDTO::getServiceIds)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .forEach(serviceId -> subtractServiceQuantity(limitServiceMap, serviceId));
        }
        if (log.isDebugEnabled()) {
            log.debug(
                    "3. subtract appt. staffId:{}, date:{}, limitSizeMap:{}, limitTypeMap:{}, limitBreedMap:{}, limitBreedUnionMap:{}, limitServiceMap:{}",
                    staffId,
                    date,
                    limitSizeMap,
                    limitTypeMap,
                    limitBreedMap,
                    limitBreedUnionMap,
                    limitServiceMap);
        }

        return getResultByCurrentFilter(
                obPetLimitFilterDTO,
                staffId,
                date,
                startTime,
                limitSizeMap,
                limitTypeMap,
                limitBreedMap,
                limitBreedUnionMap,
                limitServiceMap);
    }

    @Nonnull
    public static PetAvailableDTO judgePetLimitAndGetPetRemainQuantity(
            @Nonnull final OBPetLimitFilterDTO obPetLimitFilterDTO,
            final Integer staffId,
            final String date,
            final List<LimitGroupDTO> limitGroups,
            final Integer startTime,
            List<Set<Integer>> petIndexSubList) {
        var petAvailableDTO = new PetAvailableDTO();
        petAvailableDTO.setIsAllPetsAvailable(
                Objects.equals(obPetLimitFilterDTO.getPetIndexSubList(), petIndexSubList));
        petAvailableDTO.setPetAvailableSubList(petIndexSubList);
        petAvailableDTO.setPetSubListToTimeSlotTypeMap(new HashMap<>());
        if (CollectionUtils.isEmpty(limitGroups) || CollectionUtils.isEmpty(petIndexSubList)) {
            return petAvailableDTO;
        }

        Map<String, List<AppointmentPetIdDTO>> petAppointmentMap = obPetLimitFilterDTO.getPetAppointmentMap();
        Map<Integer, CustomerPetDetailDTO> petIdDetailMap = obPetLimitFilterDTO.getPetIdDetailMap();
        Map<Integer, MoePetBreedDTO> petBreedMap = obPetLimitFilterDTO.getPetBreedMap();
        Map<Long, PetSizeDTO> petSizeMap = obPetLimitFilterDTO.getPetSizeMap();
        Map<Long, ServiceModel> serviceMap = obPetLimitFilterDTO.getServiceMap();

        var limitSizeListMap = getLimitSizeListMap(limitGroups, petSizeMap);
        var limitBreedListMap = getLimitBreedListMap(limitGroups, petBreedMap);
        var limitServiceListMap = getLimitServiceListMap(limitGroups, serviceMap);

        // subtract from existing appointment's pet quantity
        if (petAppointmentMap.containsKey(date)) {
            var matchedAppointmentPetIdDTOs = petAppointmentMap.get(date).stream()
                    .filter(appointmentPetIdDTO -> {
                        if (Objects.isNull(startTime)) {
                            // working hour
                            return Objects.equals(appointmentPetIdDTO.getStaffId(), staffId);
                        } else {
                            // time slot
                            return (Objects.equals(appointmentPetIdDTO.getStaffId(), staffId)
                                    && Objects.equals(appointmentPetIdDTO.getAppointmentStartTime(), startTime));
                        }
                    })
                    .toList();
            // subtract by pet
            matchedAppointmentPetIdDTOs.stream()
                    .map(AppointmentPetIdDTO::getPetId)
                    .distinct()
                    .map(petIdDetailMap::get)
                    .filter(Objects::nonNull)
                    .forEach(pet -> {
                        if (StringUtils.hasText(pet.getWeight())) {
                            // Rounded pet weight, make sure the weight falls within the pet size gap
                            BigDecimal roundedPetWeight =
                                    new BigDecimal(pet.getWeight()).setScale(2, RoundingMode.HALF_UP);
                            subtractSizeQuantity(limitSizeListMap, roundedPetWeight);
                        }
                        if (StringUtils.hasText(pet.getBreed())) {
                            subtractBreedQuantity(limitBreedListMap, pet.getBreed());
                        }
                    });
            // subtract by service
            matchedAppointmentPetIdDTOs.stream()
                    .map(AppointmentPetIdDTO::getServiceIds)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .forEach(serviceId -> subtractServiceQuantity(limitServiceListMap, serviceId));
        }

        List<Set<Integer>> res = new ArrayList<>();
        Map<Set<Integer>, List<TimeSlotType>> petSubListToTimeSlotTypesMap = new HashMap<>();
        var petIndexMap = obPetLimitFilterDTO.getPetIndexMap();
        petIndexSubList.forEach(petIndexList -> {
            var petParamList = petIndexList.stream()
                    .map(petIndexMap::get)
                    .filter(Objects::nonNull)
                    .toList();
            // 构造本次预约 pet 的 size 数量、breed 数量、type 数量
            Map<BigDecimal, Long> currentSizeMap = petParamList.stream()
                    .map(OBPetDataDTO::getWeight)
                    .filter(StringUtils::hasText)
                    .map(BigDecimal::new)
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
            Map<Integer, Long> currentTypeMap = petParamList.stream()
                    .map(OBPetDataDTO::getPetTypeId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
            Map<String, Long> currentBreedMap = petParamList.stream()
                    .map(OBPetDataDTO::getBreed)
                    .filter(StringUtils::hasText)
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
            Map<Long, Long> currentServiceMap = obPetLimitFilterDTO.getCurrentServiceMap();
            var currentFilter = new OBPetLimitFilterDTO();
            currentFilter.setCurrentSizeMap(currentSizeMap);
            currentFilter.setCurrentTypeMap(currentTypeMap);
            currentFilter.setCurrentBreedMap(currentBreedMap);
            currentFilter.setCurrentServiceMap(currentServiceMap);
            currentFilter.setCurrentStaffServiceMap(obPetLimitFilterDTO.getCurrentStaffServiceMap());
            var limitationResult = getResultByCurrentFilterV3(
                    currentFilter, staffId, date, startTime, limitSizeListMap, limitBreedListMap, limitServiceListMap);
            if (Boolean.FALSE.equals(limitationResult.getNoMoreQuantity())) {
                res.add(petIndexList);
            }
            petSubListToTimeSlotTypesMap.put(petIndexList, limitationResult.getTimeSlotTypes());
        });
        petAvailableDTO.setIsAllPetsAvailable(Objects.equals(obPetLimitFilterDTO.getPetIndexSubList(), res));
        petAvailableDTO.setPetAvailableSubList(res);
        petAvailableDTO.setPetSubListToTimeSlotTypeMap(petSubListToTimeSlotTypesMap);

        return petAvailableDTO;
    }

    @Nonnull
    private static Boolean getResultByCurrentFilter(
            final OBPetLimitFilterDTO obPetLimitFilterDTO,
            final Integer staffId,
            final String date,
            final Integer startTime,
            final Map<Pair<BigDecimal, BigDecimal>, Integer> limitSizeMap,
            final Map<Integer, Integer> limitTypeMap,
            final Map<String, Integer> limitBreedMap,
            final Map<List<String>, Integer> limitBreedUnionMap,
            final Map<Set<Long>, Integer> limitServiceMap) {
        // 4.0 subtract from current appointment's pet quantity
        Map<BigDecimal, Long> currentSizeMap = new HashMap<>(obPetLimitFilterDTO.getCurrentSizeMap());
        Map<Integer, Long> currentTypeMap = new HashMap<>(obPetLimitFilterDTO.getCurrentTypeMap());
        Map<String, Long> currentBreedMap = new HashMap<>(obPetLimitFilterDTO.getCurrentBreedMap());
        Map<String, Long> currentBreedUnionMap = new HashMap<>(obPetLimitFilterDTO.getCurrentBreedMap());
        Map<Long, Long> currentServiceMap = new HashMap<>(obPetLimitFilterDTO.getCurrentServiceMap());
        if (log.isDebugEnabled()) {
            log.debug(
                    "1. current appt. staffId:{}, date:{}, currentSizeMap:{}, currentTypeMap:{}, currentBreedMap:{}, currentBreedUnionMap:{}, currentServiceMap:{}",
                    staffId,
                    date,
                    currentSizeMap,
                    currentTypeMap,
                    currentBreedMap,
                    currentBreedUnionMap,
                    currentServiceMap);
        }
        limitSizeMap.forEach((weightPair, remainQuantity) -> {
            AtomicLong remainQuantityAtomic = new AtomicLong(remainQuantity);
            currentSizeMap.forEach((weight, quantity) -> {
                BigDecimal weightLow = weightPair.getLeft();
                BigDecimal weightHigh = weightPair.getRight();
                // Rounded pet weight, make sure the weight falls within the pet size gap
                BigDecimal roundedPetWeight = weight.setScale(2, RoundingMode.HALF_UP);
                if (weightLow.compareTo(roundedPetWeight) <= 0 && roundedPetWeight.compareTo(weightHigh) <= 0) {
                    // if there is no more quantity, set quantity as a negative number
                    if (remainQuantityAtomic.get() <= 0) {
                        currentSizeMap.put(weight, NO_MORE_QUANTITY);
                        return;
                    }
                    currentSizeMap.put(weight, remainQuantityAtomic.addAndGet(-quantity));
                }
            });
        });
        limitTypeMap.forEach((limitTypeId, remainQuantity) -> {
            AtomicLong remainQuantityAtomic = new AtomicLong(remainQuantity);
            currentTypeMap.forEach((typeId, quantity) -> {
                if (Objects.equals(typeId, limitTypeId)) {
                    // if there is no more quantity, set quantity as a negative number
                    if (remainQuantityAtomic.get() <= 0) {
                        currentTypeMap.put(typeId, NO_MORE_QUANTITY);
                        return;
                    }
                    currentTypeMap.put(typeId, remainQuantityAtomic.addAndGet(-quantity));
                }
            });
        });
        limitBreedMap.forEach((limitBreed, remainQuantity) -> {
            AtomicLong remainQuantityAtomic = new AtomicLong(remainQuantity);
            currentBreedMap.forEach((breed, quantity) -> {
                if (limitBreed != null && limitBreed.equalsIgnoreCase(breed)) {
                    // if there is no more quantity, set quantity as a negative number
                    if (remainQuantityAtomic.get() <= 0) {
                        currentBreedMap.put(breed, NO_MORE_QUANTITY);
                        return;
                    }
                    currentBreedMap.put(breed, remainQuantityAtomic.addAndGet(-quantity));
                }
            });
        });
        limitBreedUnionMap.forEach((limitBreedUnion, remainQuantity) -> {
            AtomicLong atomicRemainQuantity = new AtomicLong(remainQuantity);
            currentBreedUnionMap.forEach((breed, quantity) -> {
                if (!CollectionUtils.isEmpty(limitBreedUnion) && limitBreedUnion.contains(breed)) {
                    // if there is no more quantity, set quantity as a negative number
                    if (atomicRemainQuantity.get() <= 0) {
                        currentBreedUnionMap.put(breed, NO_MORE_QUANTITY);
                        return;
                    }
                    currentBreedUnionMap.put(breed, atomicRemainQuantity.addAndGet(-quantity));
                }
            });
        });
        limitServiceMap.forEach((limitServiceIds, remainQuantity) -> {
            AtomicLong remainQuantityAtomic = new AtomicLong(remainQuantity);
            currentServiceMap.forEach((serviceId, quantity) -> {
                if (limitServiceIds.contains(serviceId)) {
                    // if there is no more quantity, set quantity as a negative number
                    if (remainQuantityAtomic.get() <= 0) {
                        currentServiceMap.put(serviceId, NO_MORE_QUANTITY);
                        return;
                    }
                    currentServiceMap.put(serviceId, remainQuantityAtomic.addAndGet(-quantity));
                }
            });
        });
        if (log.isDebugEnabled()) {
            log.debug(
                    "4. current appt. staffId:{}, date:{}, currentSizeMap:{}, currentTypeMap:{}, currentBreedMap:{}, currentBreedUnionMap:{}, currentServiceMap:{}",
                    staffId,
                    date,
                    currentSizeMap,
                    currentTypeMap,
                    currentBreedMap,
                    currentBreedUnionMap,
                    currentServiceMap);
        }

        log.info(
                """
                 staffId:{}, date:{}, startTime:{}
                 limitSizeMap:{}, limitTypeMap:{}, limitBreedMap:{}, limitBreedUnionMap:{}, limitServiceMap:{}
                 currentSizeMap:{}, currentTypeMap:{}, currentBreedMap:{}, currentBreedUnionMap:{}, currentServiceMap:{}
                """,
                staffId,
                date,
                startTime,
                limitSizeMap,
                limitTypeMap,
                limitBreedMap,
                limitBreedUnionMap,
                limitServiceMap,
                currentSizeMap,
                currentTypeMap,
                currentBreedMap,
                currentBreedUnionMap,
                currentServiceMap);

        // judge size quantity
        boolean noMoreSizeQuantity = currentSizeMap.values().stream().anyMatch(quantity -> quantity < 0);
        if (noMoreSizeQuantity) {
            log.info("[ob limit] no more size quantity. staffId:{}, date:{}, startTime:{}", staffId, date, startTime);
            return Boolean.TRUE;
        }
        // judge type quantity
        boolean noMoreTypeQuantity = currentTypeMap.values().stream().anyMatch(quantity -> quantity < 0);
        if (noMoreTypeQuantity) {
            log.info("[ob limit] no more type quantity. staffId:{}, date:{}, startTime:{}", staffId, date, startTime);
            return Boolean.TRUE;
        }
        // judge every breed quantity
        boolean noMoreBreedQuantity = currentBreedMap.values().stream().anyMatch(quantity -> quantity < 0);
        if (noMoreBreedQuantity) {
            log.info("[ob limit] no more breed quantity. staffId:{}, date:{}, startTime:{}", staffId, date, startTime);
            return Boolean.TRUE;
        }
        // judge breed union quantity
        boolean noMoreBreedUnionQuantity =
                currentBreedUnionMap.values().stream().anyMatch(quantity -> quantity < 0);
        if (noMoreBreedUnionQuantity) {
            log.info(
                    "[ob limit] no more breed union quantity. staffId:{}, date:{}, startTime:{}",
                    staffId,
                    date,
                    startTime);
            return Boolean.TRUE;
        }
        // judge service quantity
        boolean noMoreServiceQuantity = currentServiceMap.values().stream().anyMatch(quantity -> quantity < 0);
        if (noMoreServiceQuantity) {
            log.info(
                    "[ob limit] no more service quantity. staffId:{}, date:{}, startTime:{}", staffId, date, startTime);
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Nonnull
    private static LimitationResultDTO getResultByCurrentFilterV2(
            final OBPetLimitFilterDTO obPetLimitFilterDTO,
            final Integer staffId,
            final String date,
            final Integer startTime,
            final Map<Set<Pair<BigDecimal, BigDecimal>>, Integer> limitSizeMap,
            final Map<Integer, Integer> limitTypeMap,
            final Map<String, Integer> limitBreedMap,
            final Map<List<String>, Integer> limitBreedUnionMap,
            final Map<Set<Long>, Integer> limitServiceMap) {
        // 4.0 subtract from current appointment's pet quantity
        Map<BigDecimal, Long> currentSizeMap = new HashMap<>(obPetLimitFilterDTO.getCurrentSizeMap());
        Map<Integer, Long> currentTypeMap = new HashMap<>(obPetLimitFilterDTO.getCurrentTypeMap());
        Map<String, Long> currentBreedMap = new HashMap<>(obPetLimitFilterDTO.getCurrentBreedMap());
        Map<String, Long> currentBreedUnionMap = new HashMap<>(obPetLimitFilterDTO.getCurrentBreedMap());
        Map<Long, Long> currentServiceMap;
        Map<Integer, Map<Long, Long>> currentStaffServiceMap =
                new HashMap<>(obPetLimitFilterDTO.getCurrentStaffServiceMap());
        if (!CollectionUtils.isEmpty(currentStaffServiceMap) && currentStaffServiceMap.containsKey(staffId)) {
            currentServiceMap = new HashMap<>(currentStaffServiceMap.getOrDefault(staffId, Map.of()));
        } else {
            currentServiceMap = new HashMap<>(obPetLimitFilterDTO.getCurrentServiceMap());
        }

        if (log.isDebugEnabled()) {
            log.debug(
                    "1. current appt. staffId:{}, date:{}, currentSizeMap:{}, currentTypeMap:{}, currentBreedMap:{}, currentBreedUnionMap:{}, currentServiceMap:{}",
                    staffId,
                    date,
                    currentSizeMap,
                    currentTypeMap,
                    currentBreedMap,
                    currentBreedUnionMap,
                    currentServiceMap);
        }
        limitSizeMap.forEach((setWeightPair, remainQuantity) -> {
            AtomicLong remainQuantityAtomic = new AtomicLong(remainQuantity);
            currentSizeMap.forEach((weight, quantity) -> {
                var anyMatch = setWeightPair.stream().anyMatch(weightPair -> {
                    BigDecimal weightLow = weightPair.getLeft();
                    BigDecimal weightHigh = weightPair.getRight();
                    // Rounded pet weight, make sure the weight falls within the pet size gap
                    BigDecimal roundedPetWeight = weight.setScale(2, RoundingMode.HALF_UP);
                    return weightLow.compareTo(roundedPetWeight) <= 0 && roundedPetWeight.compareTo(weightHigh) <= 0;
                });
                if (anyMatch) {
                    // if there is no more quantity, set quantity as a negative number
                    if (remainQuantityAtomic.get() <= 0) {
                        currentSizeMap.put(weight, NO_MORE_QUANTITY);
                        return;
                    }
                    currentSizeMap.put(weight, remainQuantityAtomic.addAndGet(-quantity));
                }
            });
        });
        limitTypeMap.forEach((limitTypeId, remainQuantity) -> {
            AtomicLong remainQuantityAtomic = new AtomicLong(remainQuantity);
            currentTypeMap.forEach((typeId, quantity) -> {
                if (Objects.equals(typeId, limitTypeId)) {
                    // if there is no more quantity, set quantity as a negative number
                    if (remainQuantityAtomic.get() <= 0) {
                        currentTypeMap.put(typeId, NO_MORE_QUANTITY);
                        return;
                    }
                    currentTypeMap.put(typeId, remainQuantityAtomic.addAndGet(-quantity));
                }
            });
        });
        limitBreedMap.forEach((limitBreed, remainQuantity) -> {
            AtomicLong remainQuantityAtomic = new AtomicLong(remainQuantity);
            currentBreedMap.forEach((breed, quantity) -> {
                if (limitBreed != null && limitBreed.equalsIgnoreCase(breed)) {
                    // if there is no more quantity, set quantity as a negative number
                    if (remainQuantityAtomic.get() <= 0) {
                        currentBreedMap.put(breed, NO_MORE_QUANTITY);
                        return;
                    }
                    currentBreedMap.put(breed, remainQuantityAtomic.addAndGet(-quantity));
                }
            });
        });
        limitBreedUnionMap.forEach((limitBreedUnion, remainQuantity) -> {
            AtomicLong atomicRemainQuantity = new AtomicLong(remainQuantity);
            currentBreedUnionMap.forEach((breed, quantity) -> {
                if (!CollectionUtils.isEmpty(limitBreedUnion) && limitBreedUnion.contains(breed)) {
                    // if there is no more quantity, set quantity as a negative number
                    if (atomicRemainQuantity.get() <= 0) {
                        currentBreedUnionMap.put(breed, NO_MORE_QUANTITY);
                        return;
                    }
                    currentBreedUnionMap.put(breed, atomicRemainQuantity.addAndGet(-quantity));
                }
            });
        });
        limitServiceMap.forEach((limitServiceIds, remainQuantity) -> {
            AtomicLong remainQuantityAtomic = new AtomicLong(remainQuantity);
            currentServiceMap.forEach((serviceId, quantity) -> {
                if (limitServiceIds.contains(serviceId)) {
                    // if there is no more quantity, set quantity as a negative number
                    if (remainQuantityAtomic.get() <= 0) {
                        currentServiceMap.put(serviceId, NO_MORE_QUANTITY);
                        return;
                    }
                    currentServiceMap.put(serviceId, remainQuantityAtomic.addAndGet(-quantity));
                }
            });
        });
        if (log.isDebugEnabled()) {
            log.debug(
                    "4. current appt. staffId:{}, date:{}, currentSizeMap:{}, currentTypeMap:{}, currentBreedMap:{}, currentBreedUnionMap:{}, currentServiceMap:{}",
                    staffId,
                    date,
                    currentSizeMap,
                    currentTypeMap,
                    currentBreedMap,
                    currentBreedUnionMap,
                    currentServiceMap);
        }

        log.info(
                """
             staffId:{}, date:{}, startTime:{},
             limitSizeMap:{}, limitTypeMap:{}, limitBreedMap:{}, limitBreedUnionMap:{}, limitServiceMap:{},
             currentSizeMap:{}, currentTypeMap:{}, currentBreedMap:{}, currentBreedUnionMap:{}, currentServiceMap:{}
            """,
                staffId,
                date,
                startTime,
                limitSizeMap,
                limitTypeMap,
                limitBreedMap,
                limitBreedUnionMap,
                limitServiceMap,
                currentSizeMap,
                currentTypeMap,
                currentBreedMap,
                currentBreedUnionMap,
                currentServiceMap);

        List<TimeSlotType> timeSlotTypes = new ArrayList<>();

        // judge service quantity
        boolean noMoreServiceQuantity = currentServiceMap.values().stream().anyMatch(quantity -> quantity < 0);
        if (noMoreServiceQuantity) {
            log.info(
                    "[ob limit] no more service quantity. staffId:{}, date:{}, startTime:{}", staffId, date, startTime);
            timeSlotTypes.add(TimeSlotType.SERVICE_LIMITATION_NOT_MET);
        }
        // judge size quantity
        boolean noMoreSizeQuantity = currentSizeMap.values().stream().anyMatch(quantity -> quantity < 0);
        if (noMoreSizeQuantity) {
            log.info("[ob limit] no more size quantity. staffId:{}, date:{}, startTime:{}", staffId, date, startTime);
            timeSlotTypes.add(TimeSlotType.PET_SIZE_LIMITATION_NOT_MET);
        }
        // judge type quantity
        boolean noMoreTypeQuantity = currentTypeMap.values().stream().anyMatch(quantity -> quantity < 0);
        if (noMoreTypeQuantity) {
            log.info("[ob limit] no more type quantity. staffId:{}, date:{}, startTime:{}", staffId, date, startTime);
            timeSlotTypes.add(TimeSlotType.PET_BREED_LIMITATION_NOT_MET);
        }
        // judge every breed quantity
        boolean noMoreBreedQuantity = currentBreedMap.values().stream().anyMatch(quantity -> quantity < 0);
        if (noMoreBreedQuantity) {
            log.info("[ob limit] no more breed quantity. staffId:{}, date:{}, startTime:{}", staffId, date, startTime);
            timeSlotTypes.add(TimeSlotType.PET_BREED_LIMITATION_NOT_MET);
        }
        // judge breed union quantity
        boolean noMoreBreedUnionQuantity =
                currentBreedUnionMap.values().stream().anyMatch(quantity -> quantity < 0);
        if (noMoreBreedUnionQuantity) {
            log.info(
                    "[ob limit] no more breed union quantity. staffId:{}, date:{}, startTime:{}",
                    staffId,
                    date,
                    startTime);
            timeSlotTypes.add(TimeSlotType.PET_BREED_LIMITATION_NOT_MET);
        }

        if (CollectionUtils.isEmpty(timeSlotTypes)) {
            return new LimitationResultDTO().setNoMoreQuantity(false).setTimeSlotTypes(timeSlotTypes);
        }

        return new LimitationResultDTO()
                .setNoMoreQuantity(true)
                .setTimeSlotTypes(timeSlotTypes.stream().distinct().toList());
    }

    @Nonnull
    private static LimitationResultDTO getResultByCurrentFilterV3(
            final OBPetLimitFilterDTO obPetLimitFilterDTO,
            final Integer staffId,
            final String date,
            final Integer startTime,
            final List<Map<Set<Pair<BigDecimal, BigDecimal>>, Integer>> limitSizeListMap,
            final List<Map<Set<String>, Integer>> limitBreedListMap,
            final List<Map<Set<Long>, Integer>> limitServiceListMap) {
        // 4.0 subtract from current appointment's pet quantity
        Map<BigDecimal, Long> currentSizeMap = new HashMap<>(obPetLimitFilterDTO.getCurrentSizeMap());
        Map<Integer, Long> currentTypeMap = new HashMap<>(obPetLimitFilterDTO.getCurrentTypeMap());
        Map<String, Long> currentBreedMap = new HashMap<>(obPetLimitFilterDTO.getCurrentBreedMap());
        Map<String, Long> currentBreedUnionMap = new HashMap<>(obPetLimitFilterDTO.getCurrentBreedMap());
        Map<Long, Long> currentServiceMap;
        Map<Integer, Map<Long, Long>> currentStaffServiceMap =
                new HashMap<>(obPetLimitFilterDTO.getCurrentStaffServiceMap());
        if (!CollectionUtils.isEmpty(currentStaffServiceMap) && currentStaffServiceMap.containsKey(staffId)) {
            currentServiceMap = new HashMap<>(currentStaffServiceMap.getOrDefault(staffId, Map.of()));
        } else {
            currentServiceMap = new HashMap<>(obPetLimitFilterDTO.getCurrentServiceMap());
        }

        if (log.isDebugEnabled()) {
            log.debug(
                    "1. current appt. staffId:{}, date:{}, currentSizeMap:{}, currentTypeMap:{}, currentBreedMap:{}, currentBreedUnionMap:{}, currentServiceMap:{}",
                    staffId,
                    date,
                    currentSizeMap,
                    currentTypeMap,
                    currentBreedMap,
                    currentBreedUnionMap,
                    currentServiceMap);
        }

        List<TimeSlotType> timeSlotTypes = new ArrayList<>();

        // judge service quantity
        boolean noMoreServiceQuantity = !CollectionUtils.isEmpty(limitServiceListMap)
                && limitServiceListMap.stream().allMatch(limitServiceMap -> {
                    return limitServiceMap.entrySet().stream().anyMatch(entry -> {
                        var limitServiceIds = entry.getKey();
                        var remainQuantity = entry.getValue();
                        AtomicLong remainQuantityAtomic = new AtomicLong(remainQuantity);
                        Map<Long, Long> cServiceMap = new HashMap<>(currentServiceMap);
                        cServiceMap.forEach((serviceId, quantity) -> {
                            if (limitServiceIds.contains(serviceId)) {
                                if (remainQuantityAtomic.get() <= 0) {
                                    cServiceMap.put(serviceId, NO_MORE_QUANTITY);
                                    return;
                                }
                                cServiceMap.put(serviceId, remainQuantityAtomic.addAndGet(-quantity));
                            }
                        });
                        return cServiceMap.values().stream().anyMatch(quantity -> quantity < 0);
                    });
                });
        if (noMoreServiceQuantity) {
            log.info(
                    "[ob limit] no more service quantity. staffId:{}, date:{}, startTime:{}", staffId, date, startTime);
            timeSlotTypes.add(TimeSlotType.SERVICE_LIMITATION_NOT_MET);
        }

        // judge size quantity
        boolean noMoreSizeQuantity = !CollectionUtils.isEmpty(limitSizeListMap)
                && limitSizeListMap.stream().allMatch(limitSizeMap -> {
                    return limitSizeMap.entrySet().stream().anyMatch(entry -> {
                        var setWeightPair = entry.getKey();
                        var remainQuantity = entry.getValue();
                        AtomicLong remainQuantityAtomic = new AtomicLong(remainQuantity);
                        Map<BigDecimal, Long> cSizeMap = new HashMap<>(currentSizeMap);
                        cSizeMap.forEach((weight, quantity) -> {
                            var anyMatch = setWeightPair.stream().anyMatch(weightPair -> {
                                BigDecimal weightLow = weightPair.getLeft();
                                BigDecimal weightHigh = weightPair.getRight();
                                // Rounded pet weight, make sure the weight falls within the pet size gap
                                BigDecimal roundedPetWeight = weight.setScale(2, RoundingMode.HALF_UP);
                                return weightLow.compareTo(roundedPetWeight) <= 0
                                        && roundedPetWeight.compareTo(weightHigh) <= 0;
                            });
                            if (anyMatch) {
                                // if there is no more quantity, set quantity as a negative number
                                if (remainQuantityAtomic.get() <= 0) {
                                    cSizeMap.put(weight, NO_MORE_QUANTITY);
                                    return;
                                }
                                cSizeMap.put(weight, remainQuantityAtomic.addAndGet(-quantity));
                            }
                        });
                        return cSizeMap.values().stream().anyMatch(quantity -> quantity < 0);
                    });
                });
        if (noMoreSizeQuantity) {
            log.info("[ob limit] no more size quantity. staffId:{}, date:{}, startTime:{}", staffId, date, startTime);
            timeSlotTypes.add(TimeSlotType.PET_SIZE_LIMITATION_NOT_MET);
        }

        // judge every breed quantity
        boolean noMoreBreedQuantity = !CollectionUtils.isEmpty(limitBreedListMap)
                && limitBreedListMap.stream().allMatch(limitBreedMap -> {
                    return limitBreedMap.entrySet().stream().anyMatch(entry -> {
                        var limitBreedUnion = entry.getKey();
                        var remainQuantity = entry.getValue();
                        AtomicLong remainQuantityAtomic = new AtomicLong(remainQuantity);
                        Map<String, Long> cBreedUnionMap = new HashMap<>(currentBreedUnionMap);
                        cBreedUnionMap.forEach((breed, quantity) -> {
                            if (!CollectionUtils.isEmpty(limitBreedUnion) && limitBreedUnion.contains(breed)) {
                                if (remainQuantityAtomic.get() <= 0) {
                                    cBreedUnionMap.put(breed, NO_MORE_QUANTITY);
                                    return;
                                }
                                cBreedUnionMap.put(breed, remainQuantityAtomic.addAndGet(-quantity));
                            }
                        });
                        return cBreedUnionMap.values().stream().anyMatch(quantity -> quantity < 0);
                    });
                });
        if (noMoreBreedQuantity) {
            log.info("[ob limit] no more breed quantity. staffId:{}, date:{}, startTime:{}", staffId, date, startTime);
            timeSlotTypes.add(TimeSlotType.PET_BREED_LIMITATION_NOT_MET);
        }

        if (CollectionUtils.isEmpty(timeSlotTypes)) {
            return new LimitationResultDTO().setNoMoreQuantity(false).setTimeSlotTypes(timeSlotTypes);
        }

        return new LimitationResultDTO()
                .setNoMoreQuantity(true)
                .setTimeSlotTypes(timeSlotTypes.stream().distinct().toList());
    }

    private static void subtractSizeQuantity(
            Map<Pair<BigDecimal, BigDecimal>, Integer> limitSizeMap, BigDecimal weight) {
        limitSizeMap.forEach((weightPair, remainQuantity) -> {
            BigDecimal weightLow = weightPair.getLeft();
            BigDecimal weightHigh = weightPair.getRight();
            if (weightLow.compareTo(weight) <= 0 && weight.compareTo(weightHigh) <= 0) {
                limitSizeMap.put(weightPair, remainQuantity - 1);
            }
        });
    }

    private static void subtractSizeUnionQuantity(
            Map<Set<Pair<BigDecimal, BigDecimal>>, Integer> limitSizeMap, BigDecimal weight) {
        limitSizeMap.forEach((setWeightPair, remainQuantity) -> {
            var anyMatch = setWeightPair.stream().anyMatch(weightPair -> {
                BigDecimal weightLow = weightPair.getLeft();
                BigDecimal weightHigh = weightPair.getRight();
                return weightLow.compareTo(weight) <= 0 && weight.compareTo(weightHigh) <= 0;
            });
            if (anyMatch) {
                limitSizeMap.put(setWeightPair, remainQuantity - 1);
            }
        });
    }

    private static void subtractSizeQuantity(
            List<Map<Set<Pair<BigDecimal, BigDecimal>>, Integer>> limitSizeListMap, BigDecimal weight) {
        limitSizeListMap.forEach(limitSizeMap -> limitSizeMap.forEach((setWeightPair, remainQuantity) -> {
            var anyMatch = setWeightPair.stream().anyMatch(weightPair -> {
                BigDecimal weightLow = weightPair.getLeft();
                BigDecimal weightHigh = weightPair.getRight();
                return weightLow.compareTo(weight) <= 0 && weight.compareTo(weightHigh) <= 0;
            });
            if (anyMatch) {
                limitSizeMap.put(setWeightPair, remainQuantity - 1);
            }
        }));
    }

    private static void subtractTypeQuantity(Map<Integer, Integer> limitTypeMap, Integer typeId) {
        limitTypeMap.forEach((limitTypeId, remainQuantity) -> {
            if (Objects.equals(typeId, limitTypeId)) {
                limitTypeMap.put(limitTypeId, remainQuantity - 1);
            }
        });
    }

    private static void subtractBreedQuantity(Map<String, Integer> limitBreedMap, String breed) {
        limitBreedMap.forEach((limitBreed, remainQuantity) -> {
            if (limitBreed != null && limitBreed.equalsIgnoreCase(breed)) {
                limitBreedMap.put(limitBreed, remainQuantity - 1);
            }
        });
    }

    private static void subtractBreedQuantity(List<Map<Set<String>, Integer>> limitBreedListMap, String breed) {
        limitBreedListMap.forEach(limitBreedMap -> limitBreedMap.forEach((limitBreed, remainQuantity) -> {
            if (limitBreed.contains(breed)) {
                limitBreedMap.put(limitBreed, remainQuantity - 1);
            }
        }));
    }

    private static void subtractBreedUnionQuantity(Map<List<String>, Integer> limitBreedUnionMap, String breed) {
        limitBreedUnionMap.forEach((limitBreedUnion, remainQuantity) -> {
            if (!CollectionUtils.isEmpty(limitBreedUnion) && limitBreedUnion.contains(breed)) {
                limitBreedUnionMap.put(limitBreedUnion, remainQuantity - 1);
            }
        });
    }

    private static void subtractServiceQuantity(
            final Map<Set<Long>, Integer> limitServiceMap, final Integer serviceId) {
        limitServiceMap.forEach((limitServiceIds, remainQuantity) -> {
            if (limitServiceIds.contains(serviceId.longValue())) {
                limitServiceMap.put(limitServiceIds, remainQuantity - 1);
            }
        });
    }

    private static void subtractServiceQuantity(
            final List<Map<Set<Long>, Integer>> limitServiceListMap, final Integer serviceId) {
        limitServiceListMap.forEach(limitServiceMap -> limitServiceMap.forEach((limitServiceIds, remainQuantity) -> {
            if (limitServiceIds.contains(serviceId.longValue())) {
                limitServiceMap.put(limitServiceIds, remainQuantity - 1);
            }
        }));
    }

    private static Map<Pair<BigDecimal, BigDecimal>, Integer> getLimitSizeMap(
            List<PetSizeLimitDto> petSizeLimitList, Map<Long, PetSizeDTO> petSizeMap) {
        Map<Pair<BigDecimal, BigDecimal>, Integer> limitSizeMap = new HashMap<>(8);

        for (PetSizeLimitDto petSizeLimit : petSizeLimitList) {
            for (Long petSizeId : petSizeLimit.getPetSizeIds()) {
                var petSize = petSizeMap.get(petSizeId);
                if (Objects.isNull(petSize)) {
                    continue;
                }
                BigDecimal weightLow = new BigDecimal(petSize.getWeightLow());
                BigDecimal weightHigh = new BigDecimal(petSize.getWeightHigh());
                Pair<BigDecimal, BigDecimal> pair = Pair.of(weightLow, weightHigh);
                Integer maxNumber = petSizeLimit.getCapacity();
                limitSizeMap.put(pair, limitSizeMap.getOrDefault(pair, 0) + maxNumber);
            }
        }
        return limitSizeMap;
    }

    private static Map<Set<Pair<BigDecimal, BigDecimal>>, Integer> getLimitUnionSizeMap(
            List<PetSizeLimitDto> petSizeLimitList, Map<Long, PetSizeDTO> petSizeMap) {
        Map<Set<Pair<BigDecimal, BigDecimal>>, Integer> limitSizeMap = new HashMap<>(8);

        for (PetSizeLimitDto petSizeLimit : petSizeLimitList) {
            Set<Pair<BigDecimal, BigDecimal>> pairList = new HashSet<>(8);
            for (Long petSizeId : petSizeLimit.getPetSizeIds()) {
                var petSize = petSizeMap.get(petSizeId);
                if (Objects.isNull(petSize)) {
                    continue;
                }
                BigDecimal weightLow = new BigDecimal(petSize.getWeightLow());
                BigDecimal weightHigh = new BigDecimal(petSize.getWeightHigh());
                Pair<BigDecimal, BigDecimal> pair = Pair.of(weightLow, weightHigh);
                pairList.add(pair);
            }
            Integer maxNumber = petSizeLimit.getCapacity();
            limitSizeMap.put(pairList, limitSizeMap.getOrDefault(pairList, 0) + maxNumber);
        }
        return limitSizeMap;
    }

    private static List<Map<Set<Pair<BigDecimal, BigDecimal>>, Integer>> getLimitSizeListMap(
            final List<LimitGroupDTO> limitGroups, final Map<Long, PetSizeDTO> petSizeMap) {
        List<Map<Set<Pair<BigDecimal, BigDecimal>>, Integer>> limitSizeListMap = new ArrayList<>();

        for (final var limitGroup : limitGroups) {
            var onlyAcceptSelected = limitGroup.isOnlyAcceptSelected();

            Map<Set<Pair<BigDecimal, BigDecimal>>, Integer> limitSizeMap = new HashMap<>(8);

            // 使用 stream 防止 NullPointerException
            Optional.ofNullable(limitGroup.getPetSizeLimitList())
                    .orElse(Collections.emptyList())
                    .forEach(petSizeLimit -> {
                        Set<Pair<BigDecimal, BigDecimal>> pairList =
                                Optional.ofNullable(petSizeLimit.getPetSizeIds())
                                        .orElse(Collections.emptyList())
                                        .stream()
                                        .map(petSizeMap::get)
                                        .filter(Objects::nonNull)
                                        .map(petSize -> {
                                            BigDecimal weightLow = new BigDecimal(petSize.getWeightLow());
                                            BigDecimal weightHigh = new BigDecimal(petSize.getWeightHigh());
                                            return Pair.of(weightLow, weightHigh);
                                        })
                                        .collect(Collectors.toSet());

                        limitSizeMap.put(pairList, limitSizeMap.getOrDefault(pairList, 0) + petSizeLimit.getCapacity());
                    });

            if (onlyAcceptSelected && !CollectionUtils.isEmpty(limitGroup.getPetSizeLimitList())) {
                Set<Long> selectedPetSizeIds = limitGroup.getPetSizeLimitList().stream()
                        .map(PetSizeLimitDto::getPetSizeIds)
                        .filter(Objects::nonNull)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toSet());

                Set<Pair<BigDecimal, BigDecimal>> pairList = petSizeMap.entrySet().stream()
                        .filter(entry -> !selectedPetSizeIds.contains(entry.getKey()))
                        .map(entry -> {
                            var petSize = entry.getValue();
                            BigDecimal weightLow = new BigDecimal(petSize.getWeightLow());
                            BigDecimal weightHigh = new BigDecimal(petSize.getWeightHigh());
                            return Pair.of(weightLow, weightHigh);
                        })
                        .collect(Collectors.toSet());
                limitSizeMap.put(pairList, 0);
            }

            if (CollectionUtils.isEmpty(limitSizeMap)) {
                continue;
            }
            limitSizeListMap.add(limitSizeMap);
        }

        return limitSizeListMap;
    }

    private static Map<Integer, Integer> getLimitTypeMap(List<PetBreedLimitDto> petTypeBreedList) {
        Map<Integer, Integer> limitTypeMap = new HashMap<>(8);
        petTypeBreedList.stream()
                .filter(petTypeLimit -> Boolean.TRUE.equals(petTypeLimit.getIsAllBreed()))
                .forEach(petTypeLimit -> {
                    Integer maxNumber = petTypeLimit.getCapacity();
                    limitTypeMap.put(petTypeLimit.getPetTypeId().intValue(), maxNumber);
                });
        return limitTypeMap;
    }

    private static Map<String, Integer> getLimitBreedMap(
            Map<Integer, MoePetBreedDTO> petBreedMap, List<PetBreedLimitDto> petTypeBreedList) {
        Map<String, Integer> limitBreedMap = new HashMap<>(128);
        petTypeBreedList.stream()
                .filter(petTypeBreed -> Boolean.FALSE.equals(petTypeBreed.getIsAllBreed()))
                .forEach(petTypeBreed -> {
                    for (Long breedId : petTypeBreed.getBreedIds()) {
                        var breed = petBreedMap.get(breedId.intValue());
                        if (breed == null) {
                            continue;
                        }
                        String breedName = breed.getName();
                        limitBreedMap.put(
                                breedName, limitBreedMap.getOrDefault(breedName, 0) + petTypeBreed.getCapacity());
                    }
                });
        return limitBreedMap;
    }

    private static Map<List<String>, Integer> getLimitBreedUnionMap(
            Map<Integer, MoePetBreedDTO> petBreedMap, List<PetBreedLimitDto> petTypeBreedList) {
        Map<List<String>, Integer> limitBreedUnionMap = new HashMap<>(8);
        petTypeBreedList.stream()
                .filter(petTypeBreed -> Boolean.FALSE.equals(petTypeBreed.getIsAllBreed()))
                .forEach(petTypeBreed -> {
                    List<String> breedUnion = new ArrayList<>();
                    for (Long breedId : petTypeBreed.getBreedIds()) {
                        var breed = petBreedMap.get(breedId.intValue());
                        if (breed == null) {
                            continue;
                        }
                        String breedName = breed.getName();
                        breedUnion.add(breedName);
                    }
                    limitBreedUnionMap.put(breedUnion, petTypeBreed.getCapacity());
                });
        return limitBreedUnionMap;
    }

    private static List<Map<Set<String>, Integer>> getLimitBreedListMap(
            final List<LimitGroupDTO> limitGroups, final Map<Integer, MoePetBreedDTO> petBreedMap) {
        List<Map<Set<String>, Integer>> limitTypeListMap = new ArrayList<>();

        for (final var limitGroup : limitGroups) {
            var onlyAcceptSelected = limitGroup.isOnlyAcceptSelected();

            Map<Set<String>, Integer> limitTypeMap = new HashMap<>(256);

            // 使用 stream 防止 NullPointerException
            Optional.ofNullable(limitGroup.getPetBreedLimitList())
                    .orElse(Collections.emptyList())
                    .forEach(petTypeBreed -> {
                        Set<String> breedSet;

                        if (Boolean.TRUE.equals(petTypeBreed.getIsAllBreed())) {
                            breedSet = petBreedMap.values().stream()
                                    .filter(breed -> Objects.equals(
                                            breed.getPetTypeId().longValue(), petTypeBreed.getPetTypeId()))
                                    .map(MoePetBreedDTO::getName)
                                    .collect(Collectors.toSet());
                        } else {
                            breedSet =
                                    Optional.ofNullable(petTypeBreed.getBreedIds())
                                            .orElse(Collections.emptyList())
                                            .stream()
                                            .map(breedId -> petBreedMap.get(breedId.intValue()))
                                            .filter(Objects::nonNull)
                                            .map(MoePetBreedDTO::getName)
                                            .collect(Collectors.toSet());
                        }

                        limitTypeMap.put(breedSet, limitTypeMap.getOrDefault(breedSet, 0) + petTypeBreed.getCapacity());
                    });

            if (onlyAcceptSelected && !CollectionUtils.isEmpty(limitGroup.getPetBreedLimitList())) {
                Set<String> selectedBreedSet = limitTypeMap.keySet().stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.toSet());
                petBreedMap.values().stream()
                        .map(MoePetBreedDTO::getName)
                        .filter(breedName -> !selectedBreedSet.contains(breedName))
                        .forEach(breedName -> limitTypeMap.put(Set.of(breedName), 0));
            }

            if (CollectionUtils.isEmpty(limitTypeMap)) {
                continue;
            }
            limitTypeListMap.add(limitTypeMap);
        }

        return limitTypeListMap;
    }

    private static Map<Set<Long>, Integer> getLimitServiceMap(
            final Map<Long, ServiceModel> serviceMap, final List<ServiceLimitDto> serviceLimitList) {
        Map<Set<Long>, Integer> limitServiceMap = new HashMap<>(8);
        serviceLimitList.forEach(serviceLimitDto -> {
            Set<Long> key;
            if (Boolean.TRUE.equals(serviceLimitDto.getIsAllService())) {
                key = serviceMap.keySet();
            } else {
                key = new HashSet<>(serviceLimitDto.getServiceIds());
            }
            limitServiceMap.put(key, limitServiceMap.getOrDefault(key, 0) + serviceLimitDto.getCapacity());
        });
        return limitServiceMap;
    }

    private static List<Map<Set<Long>, Integer>> getLimitServiceListMap(
            final List<LimitGroupDTO> limitGroups, final Map<Long, ServiceModel> serviceMap) {
        // only use service, no need to consider add-on
        var onlyServiceMap = serviceMap.entrySet().stream()
                .filter(entry -> ServiceType.SERVICE.equals(entry.getValue().getType()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        List<Map<Set<Long>, Integer>> limitServiceListMap = new ArrayList<>();

        for (final var limitGroup : limitGroups) {
            var onlyAcceptSelected = limitGroup.isOnlyAcceptSelected();

            Map<Set<Long>, Integer> limitServiceMap = new HashMap<>(64);

            // 使用 stream 防止 NullPointerException
            Optional.ofNullable(limitGroup.getServiceLimitList())
                    .orElse(Collections.emptyList())
                    .forEach(serviceLimit -> {
                        Set<Long> key;
                        if (Boolean.TRUE.equals(serviceLimit.getIsAllService())) {
                            key = onlyServiceMap.keySet();
                        } else {
                            key = Optional.ofNullable(serviceLimit.getServiceIds())
                                    .map(HashSet::new)
                                    .orElse(new HashSet<>());
                        }
                        limitServiceMap.put(key, limitServiceMap.getOrDefault(key, 0) + serviceLimit.getCapacity());
                    });

            if (onlyAcceptSelected && !CollectionUtils.isEmpty(limitGroup.getServiceLimitList())) {
                Set<Long> selectedServiceIds = limitServiceMap.keySet().stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.toSet());
                onlyServiceMap.keySet().stream()
                        .filter(serviceId -> !selectedServiceIds.contains(serviceId))
                        .forEach(serviceId -> limitServiceMap.put(Set.of(serviceId), 0));
            }

            if (CollectionUtils.isEmpty(limitServiceMap)) {
                continue;
            }
            limitServiceListMap.add(limitServiceMap);
        }

        return limitServiceListMap;
    }
}
