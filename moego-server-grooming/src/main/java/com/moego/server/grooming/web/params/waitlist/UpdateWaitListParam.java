package com.moego.server.grooming.web.params.waitlist;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class UpdateWaitListParam {
    @Schema(description = "business id", hidden = true)
    Long businessId;

    @Schema(description = "company id", hidden = true)
    Long companyId;

    @Schema(description = "token staff id", hidden = true)
    Long tokenStaffId;

    @Schema(description = "waiting list id")
    @NotNull
    private Long id;

    @Valid
    private DatePreference datePreference;

    @Valid
    private TimePreference timePreference;

    @Valid
    private StaffPreference staffPreference;

    @Schema(description = "valid from, YYYY-MM-DD")
    private LocalDate validFrom;

    @Schema(description = "valid till, YYYY-MM-DD")
    private LocalDate validTill;

    private String ticketComment;
    private List<@Valid WaitListPetServiceParam> petServices;
    private Boolean allPetsStartAtSameTime;
}
