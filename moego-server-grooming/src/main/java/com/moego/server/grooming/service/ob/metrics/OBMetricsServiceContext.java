package com.moego.server.grooming.service.ob.metrics;

import com.moego.server.grooming.enums.OBMetricsEnum;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Component
@RequiredArgsConstructor
public class OBMetricsServiceContext implements InitializingBean {

    private final List<IOBMetricsService> metricsServiceList;

    private Map<OBMetricsEnum, IOBMetricsService> metricsServiceMap;

    @Override
    public void afterPropertiesSet() throws Exception {
        metricsServiceMap = metricsServiceList.stream()
                .collect(Collectors.toMap(IOBMetricsService::getMetricsName, metricsService -> metricsService));
    }

    public IOBMetricsService getMetricsService(OBMetricsEnum metricsEnum) {
        return metricsServiceMap.get(metricsEnum);
    }
}
