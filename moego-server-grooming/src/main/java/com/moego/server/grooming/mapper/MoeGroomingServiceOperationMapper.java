package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapper.po.GroomingStaffIdListPO;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperationExample;
import com.moego.server.grooming.params.report.DescribeOperationReportsParams;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingServiceOperationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_operation
     *
     * @mbg.generated
     */
    long countByExample(MoeGroomingServiceOperationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_operation
     *
     * @mbg.generated
     */
    int deleteByExample(MoeGroomingServiceOperationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_operation
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_operation
     *
     * @mbg.generated
     */
    int insert(MoeGroomingServiceOperation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_operation
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingServiceOperation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_operation
     *
     * @mbg.generated
     */
    List<MoeGroomingServiceOperation> selectByExample(MoeGroomingServiceOperationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_operation
     *
     * @mbg.generated
     */
    MoeGroomingServiceOperation selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_operation
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeGroomingServiceOperation record,
            @Param("example") MoeGroomingServiceOperationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_operation
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeGroomingServiceOperation record,
            @Param("example") MoeGroomingServiceOperationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_operation
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingServiceOperation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service_operation
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingServiceOperation record);

    int batchInsert(List<MoeGroomingServiceOperation> list);

    int batchUpdate(List<MoeGroomingServiceOperation> list);

    List<MoeGroomingServiceOperation> selectByBusinessIdAndGroomingServiceId(
            @Param("businessId") Integer businessId, @Param("groomingServiceId") Integer groomingServiceId);

    List<MoeGroomingServiceOperation> selectByBusinessIdAndGroomingServiceIdList(
            @Param("businessId") Integer businessId,
            @Param("groomingServiceIdList") List<Integer> groomingServiceIdList);

    List<MoeGroomingServiceOperation> selectByBusinessIdAndGroomingId(
            @Param("businessId") Integer businessId, @Param("groomingId") Integer groomingId);

    List<MoeGroomingServiceOperation> selectByGroomingIdList(@Param("groomingIdList") List<Integer> groomingIdList);

    List<MoeGroomingServiceOperation> selectByBusinessIdAndGroomingIdList(
            @Param("businessId") Integer businessId, @Param("groomingIdList") List<Integer> groomingIdList);

    int deleteByGroomingId(@Param("groomingId") Integer groomingId);

    int deleteByGroomingIdList(@Param("groomingIdList") List<Integer> groomingIdList);

    List<Integer> queryStaffIdByGroomingId(@Param("groomingId") Integer groomingId);

    List<GroomingStaffIdListPO> queryStaffIdByGroomingIds(@Param("groomingIds") List<Integer> groomingIds);

    int countOperationWithSameStartTimeByGroomingIdList(
            @Param("targetStaffId") Integer targetStaffId,
            @Param("sourceStaffId") Integer sourceStaffId,
            @Param("groomingIdList") List<Integer> groomingIdList);

    int transferOperation(
            @Param("sourceStaffId") Integer sourceStaffId,
            @Param("targetStaffId") Integer targetStaffId,
            @Param("groomingIdList") List<Integer> groomingIdList);

    List<MoeGroomingServiceOperation> describeOperationReports(DescribeOperationReportsParams params);

    void deleteByPetDetailId(@Param("petDetailId") Long petDetailId);
}
