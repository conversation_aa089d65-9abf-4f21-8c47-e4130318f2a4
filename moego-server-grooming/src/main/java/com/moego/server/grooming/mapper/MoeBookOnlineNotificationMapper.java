package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlineNotification;

public interface MoeBookOnlineNotificationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_notification
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_notification
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlineNotification record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_notification
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlineNotification record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_notification
     *
     * @mbg.generated
     */
    MoeBookOnlineNotification selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_notification
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlineNotification record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_notification
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeBookOnlineNotification record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_notification
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlineNotification record);

    MoeBookOnlineNotification selectByBusinessId(Integer businessId);

    int updateByBusinessIdWithBLOBs(MoeBookOnlineNotification record);
}
