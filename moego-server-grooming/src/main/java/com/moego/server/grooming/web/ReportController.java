package com.moego.server.grooming.web;

import com.moego.common.response.ResponseResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.dto.report.MobileSummaryDTO;
import com.moego.server.grooming.dto.report.ReportAppointmentResponse;
import com.moego.server.grooming.dto.report.ReportApptType;
import com.moego.server.grooming.dto.report.ReportApptsNumberDTO;
import com.moego.server.grooming.dto.report.ReportPaymentSummaryDto;
import com.moego.server.grooming.service.report.GroomingReportService;
import com.moego.server.grooming.service.report.ReportAppointmentService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/grooming/report")
@RequiredArgsConstructor
public class ReportController {

    private final GroomingReportService reportService;
    private final ReportAppointmentService reportAppointmentService;

    @GetMapping("/mobile/appt")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<ReportAppointmentResponse> getReportAppointments(
            AuthContext context,
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam ReportApptType type) {
        return ResponseResult.success(
                reportAppointmentService.getReportAppointments(context.getBusinessId(), startDate, endDate, type));
    }

    @GetMapping("/mobile/summary")
    @Auth(AuthType.BUSINESS)
    MobileSummaryDTO getMobileDashboardSummary(
            AuthContext context, @RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate) {
        try {
            return reportService.getMobileDashboardSummary(context.getBusinessId(), startDate, endDate);
        } catch (RuntimeException e) {
            log.error("Failed getting mobile dashboard summary: {}", e.getMessage(), e);
            throw e;
        }
    }

    // DONE(junbao): 考虑对此接口加校验所有 staffId -> businessId? 但是这个接口表现上是安全的
    // Frank: 该接口确定为内部接口，可移除。另外，检查了内部实现逻辑，其已经根据 businessId 查出来的值对 staffId 进行了映射，因此本身也是安全的。
    // @PostMapping("/mobile/overview")
    // GroomingMobileOverviewDTO getMobileDashboardOverview(...) { ... }

    @Deprecated
    @GetMapping("/mobile/payment")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<ReportPaymentSummaryDto>> getPaymentMethodReport(
            AuthContext context,
            Integer staffId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {
        try {
            return ResponseResult.success(
                    reportService.paymentMethodReport(context.getBusinessId(), startDate, endDate, staffId));
        } catch (RuntimeException e) {
            log.error("Failed getting mobile dashboard summary: {}", e.getMessage(), e);
            throw e;
        }
    }

    @GetMapping("/mobile/apptNum")
    @Auth(AuthType.BUSINESS)
    ReportApptsNumberDTO getReportApptsNumber(
            AuthContext context, @RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate) {
        try {
            return reportService.getReportApptsNumber(context.getBusinessId(), startDate, endDate);
        } catch (RuntimeException e) {
            log.error("Failed getting mobile dashboard summary: {}", e.getMessage(), e);
            throw e;
        }
    }
}
