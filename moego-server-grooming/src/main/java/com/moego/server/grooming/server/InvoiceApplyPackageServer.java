package com.moego.server.grooming.server;

import com.moego.server.grooming.api.IInvoiceApplyPackageServiceBase;
import com.moego.server.grooming.service.MoePackageService;
import com.moego.server.grooming.service.OrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/9/23
 */
@RestController
@RequiredArgsConstructor
public class InvoiceApplyPackageServer extends IInvoiceApplyPackageServiceBase {

    private final OrderService orderService;
    private final MoePackageService packageService;

    @Override
    public UpdateForOrderResult updateAppliedPackageForOrder(UpdateForOrderParam param) {

        var orderDetail = orderService.getOrderWithItemsById(Math.toIntExact(param.orderId()));

        packageService.updateOrderApplyPackageRecord(Math.toIntExact(param.orderId()), orderDetail.getItems());

        return UpdateForOrderResult.builder().build();
    }

    @Override
    public RemoveAllPackagesResult removeAllPackages(RemoveAllPackagesParam param) {

        orderService.removeAllPackages(Math.toIntExact(param.orderId()), param.checkRefund());

        return RemoveAllPackagesResult.builder().build();
    }
}
