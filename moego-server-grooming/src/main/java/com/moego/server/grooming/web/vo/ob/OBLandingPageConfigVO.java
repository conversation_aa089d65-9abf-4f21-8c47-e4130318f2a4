package com.moego.server.grooming.web.vo.ob;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/2/20
 */
@Data
@Accessors(chain = true)
public class OBLandingPageConfigVO {

    @Schema(description = "About us, Inherited from moe_book_online_profile.description")
    private String aboutUs;

    @Schema(description = "Welcome page message, Inherited from moe_business_book_online.description")
    private String welcomePageMessage;

    @Schema(description = "Theme color, Inherited from moe_book_online_profile.button_color")
    private String themeColor;

    @Schema(description = "Publish status")
    private Boolean isPublished;

    @Schema(description = "Showcase before image")
    private String showcaseBeforeImage;

    @Schema(description = "Showcase after image")
    private String showcaseAfterImage;

    @Schema(description = "Customized URL domain name")
    private String urlDomainName;

    @Schema(description = "GA4 PROPERTY ID")
    private String gaMeasurementId;

    @Schema(description = "thank you page url")
    private String thankYouPageUrl;

    @Schema(description = "Page components")
    private Map<String, Boolean> pageComponents;

    @Schema(description = "Amenity, 8 features and 4 payment options")
    private LandingPageAmenitiesVO amenities;

    @Schema(description = "Landing page gallery")
    private List<OBLandingPageGalleryVO> galleryList;

    private LandingPageConfigClientReviewVO clientReviews;

    private List<LandingPageConfigTeamVO> teams;

    private boolean supportMembership;

    @Data
    @Accessors(chain = true)
    public static class LandingPageAmenitiesVO {

        @Schema(description = "Features")
        private Map<String, Boolean> features;

        @Schema(description = "Payment")
        private Map<String, Boolean> payment;
    }

    @Data
    @Accessors(chain = true)
    public static class OBLandingPageGalleryVO {

        @Schema(description = "Gallery image path")
        private String imagePath;

        @Schema(description = "Gallery sort number")
        private Integer sort;
    }

    @Data
    @Accessors(chain = true)
    public static class LandingPageConfigClientReviewVO {
        private Boolean isDisplayClientReviewShowcasePhoto;
        private List<LandingPageConfigClientReviewRecordVO> reviewRecords;
    }

    @Data
    @Accessors(chain = true)
    public static class LandingPageConfigClientReviewRecordVO {
        private Integer reviewId;
        private String reviewContent;
        private Integer customerId;
        private String customerName;
        private String showcasePhotoUrl;
        private Integer petId;
        /**
         * 会根据 pet type 展示不同的 icon
         */
        private Integer petTypeId;

        private Date reviewTime;
    }

    @Data
    @Accessors(chain = true)
    public static class LandingPageConfigTeamVO {
        private Integer staffId;
        private String staffName;
        private String instagramLink;
        private String introduction;
        private List<String> tags;
        private Boolean isEnabled;
    }
}
