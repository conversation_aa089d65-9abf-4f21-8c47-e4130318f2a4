package com.moego.server.grooming.service.ob.metrics;

import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/6/5
 */
@Service
@RequiredArgsConstructor
public class AbandonedProportionMetricService implements IOBMetricsService {

    private final AbandonedRecordsMetricService abandonedRecordsMetricService;

    @Override
    public Object sumMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return abandonedRecordsMetricService.proportionMetrics(timeRangeDTO);
    }

    @Override
    public String proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return "";
    }

    @Override
    public OBMetricsEnum getMetricsName() {
        return OBMetricsEnum.abandoned_proportion;
    }
}
