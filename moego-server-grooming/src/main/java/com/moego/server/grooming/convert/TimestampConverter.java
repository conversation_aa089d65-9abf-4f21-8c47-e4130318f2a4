package com.moego.server.grooming.convert;

import static java.time.ZoneOffset.UTC;

import com.google.protobuf.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface TimestampConverter {

    TimestampConverter INSTANCE = Mappers.getMapper(TimestampConverter.class);

    @Named(value = "dateToTimestamp")
    default Timestamp dateToTimestamp(Date date) {
        if (date == null) {
            return Timestamp.getDefaultInstance();
        }
        return Timestamp.newBuilder().setSeconds(date.getTime() / 1000).build();
    }

    @Named(value = "timestampToDate")
    default Date timestampToDate(Timestamp timestamp) {
        if (timestamp == null) {
            return null;
        }
        return new Date(timestamp.getSeconds() * 1000);
    }

    default LocalDateTime toLocalDateTime(Timestamp timestamp) {
        return LocalDateTime.ofEpochSecond(timestamp.getSeconds(), timestamp.getNanos(), UTC);
    }

    default Timestamp toTimestamp(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return Timestamp.getDefaultInstance();
        }
        return Timestamp.newBuilder()
                .setSeconds(localDateTime.toEpochSecond(UTC))
                .setNanos(localDateTime.getNano())
                .build();
    }

    default Timestamp toTimestamp(Long timestamp) {
        return Timestamp.newBuilder().setSeconds(timestamp).setNanos(0).build();
    }
}
