package com.moego.server.grooming.utils;

import com.alibaba.excel.EasyExcelFactory;
import java.io.OutputStream;
import java.util.List;
import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 */
@UtilityClass
public class ExcelUtil {

    /**
     * Write excel to output stream.
     *
     * @param os        OutputStream
     * @param sheetName excel sheet name
     * @param clazz     header class
     * @param data      data to write
     */
    public static void writeExcel(OutputStream os, String sheetName, Class<?> clazz, List<?> data) {
        EasyExcelFactory.write(os, clazz).sheet(sheetName).doWrite(data);
    }
}
