package com.moego.server.grooming.service.dto;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReportAppointmentDAO {

    private Integer id;
    private Integer businessId;
    private Integer customerId;
    private Integer invoiceId;
    private String orderId;
    private Integer startTime;
    private Integer endTime;
    private Byte noShow;
    private Byte status;
    private Integer cancelByType;
    private Integer cancelById;
    private Integer source;
    private BigDecimal remainAmount;
    private String type;
    private Integer invoiceStatus;
    private Byte isPaid;
    private Byte isWaitingList;
    private String appointmentDate;
}
