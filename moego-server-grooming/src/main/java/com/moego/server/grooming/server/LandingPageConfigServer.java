package com.moego.server.grooming.server;

import com.moego.server.grooming.api.ILandingPageConfigServiceBase;
import com.moego.server.grooming.dto.LandingPageConfigDTO;
import com.moego.server.grooming.mapper.MoeBookOnlineLandingPageConfigMapper;
import com.moego.server.grooming.mapstruct.LandingPageConfigMapper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class LandingPageConfigServer extends ILandingPageConfigServiceBase {

    private final MoeBookOnlineLandingPageConfigMapper landingPageConfigMapper;

    @Override
    public Map<Integer, LandingPageConfigDTO> getLandingPageConfigsByBusinessIds(Collection<Integer> businessIds) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return Map.of();
        }
        return landingPageConfigMapper.listByBusinessId(new ArrayList<>(Set.copyOf(businessIds))).stream()
                .map(LandingPageConfigMapper.INSTANCE::entity2DTO)
                .collect(Collectors.toMap(LandingPageConfigDTO::getBusinessId, Function.identity(), (o, n) -> o));
    }
}
