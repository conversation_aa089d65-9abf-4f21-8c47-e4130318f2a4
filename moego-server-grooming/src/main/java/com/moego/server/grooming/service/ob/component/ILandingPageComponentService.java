package com.moego.server.grooming.service.ob.component;

import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.web.vo.ob.component.BaseComponentVO;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
public interface ILandingPageComponentService {
    /**
     * Get page component by landing page config
     *
     * @param landingPageConfig landing page config
     * @return page component model
     */
    BaseComponentVO getPageComponent(MoeBookOnlineLandingPageConfig landingPageConfig);
}
