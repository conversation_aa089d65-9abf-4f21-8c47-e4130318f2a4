package com.moego.server.grooming.web.params;

import com.moego.server.grooming.dto.ob.SelectedPetServiceDTO;
import com.moego.server.grooming.web.dto.ob.OBTimeSlotSimpleDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class OBBusinessStaffParams {

    @Deprecated // todo: remove businessId
    @Schema(description = "business id")
    private Integer businessId;

    // @NotBlank todo: open this after removing businessId
    @Deprecated
    @Schema(description = "business name")
    private String businessName;

    @Schema(description = "existing client id, or null for new user")
    private Integer customerId;

    @Valid
    @NotEmpty
    @Schema(description = "selected pet service")
    private List<@NotNull SelectedPetServiceDTO> petServiceList;

    @Valid
    @NotNull
    private OBTimeSlotSimpleDTO timeSlotParam;
}
