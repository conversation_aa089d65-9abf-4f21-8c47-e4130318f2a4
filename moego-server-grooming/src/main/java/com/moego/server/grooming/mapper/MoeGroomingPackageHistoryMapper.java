package com.moego.server.grooming.mapper;

import com.moego.server.grooming.dto.GroomingPackageHistoryDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageHistory;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageHistoryExample;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingPackageHistoryMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_history
     *
     * @mbg.generated
     */
    long countByExample(MoeGroomingPackageHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_history
     *
     * @mbg.generated
     */
    int deleteByExample(MoeGroomingPackageHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_history
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_history
     *
     * @mbg.generated
     */
    int insert(MoeGroomingPackageHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_history
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingPackageHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_history
     *
     * @mbg.generated
     */
    List<MoeGroomingPackageHistory> selectByExample(MoeGroomingPackageHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_history
     *
     * @mbg.generated
     */
    MoeGroomingPackageHistory selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_history
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeGroomingPackageHistory record,
            @Param("example") MoeGroomingPackageHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_history
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeGroomingPackageHistory record,
            @Param("example") MoeGroomingPackageHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_history
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingPackageHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_history
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingPackageHistory record);

    List<GroomingPackageHistoryDTO> queryPackageHistory(
            @Param("packageId") Integer packageId,
            @Param("limitOffset") Integer limitOffset,
            @Param("pageSize") Integer pageSize);

    Integer getPackageHistoryCount(@Param("packageId") Integer packageId);

    List<GroomingPackageHistoryDTO> queryPackageHistoryByPackageId(@Param("packageId") Integer packageId);

    List<GroomingPackageHistoryDTO> queryPackageHistoryByPackageIdList(List<Integer> packageIdList);

    Integer batchAddMoeGroomingPackageHistories(List<MoeGroomingPackageHistory> histories);

    List<GroomingPackageHistoryDTO> queryPackageHistoryByInvoiceId(@Param("invoiceId") Integer invoiceId);

    Integer batchInvalidGroomingPackageHistories(@Param("ids") List<Integer> ids);

    Integer batchInvalidGroomingPackageHistoriesByInvoiceIds(@Param("invoiceIds") Collection<Integer> invoiceIds);
}
