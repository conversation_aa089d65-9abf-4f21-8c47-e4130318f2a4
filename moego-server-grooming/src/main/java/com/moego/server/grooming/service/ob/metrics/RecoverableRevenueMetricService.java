package com.moego.server.grooming.service.ob.metrics;

import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Service
@RequiredArgsConstructor
public class RecoverableRevenueMetricService implements IOBMetricsService {

    private final MoeBookOnlineAbandonRecordMapper abandonRecordMapper;
    private final PetDetailMapperProxy petDetailMapper;

    @Override
    public Object sumMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        int recoverableClients = abandonRecordMapper.countRecoverableClients(
                timeRangeDTO.businessId(),
                timeRangeDTO.startTime(),
                timeRangeDTO.endTime(),
                OBStepEnum.listRecoverableSteps());
        // Calculate the ATV(average ticket revenue)
        BigDecimal atv = petDetailMapper.countATVRecentlyDays(
                timeRangeDTO.businessId(),
                LocalDateTime.now()
                                .minusDays(30)
                                .atZone(ZoneId.systemDefault())
                                .toInstant()
                                .toEpochMilli()
                        / 1000,
                LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000);
        return Objects.nonNull(atv)
                ? atv.multiply(BigDecimal.valueOf(recoverableClients)).setScale(2, RoundingMode.HALF_UP)
                : BigDecimal.ZERO;
    }

    @Override
    public Object proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return "";
    }

    @Override
    public OBMetricsEnum getMetricsName() {
        return OBMetricsEnum.recoverable_revenue;
    }
}
