package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeQbSyncPackage;
import com.moego.server.grooming.mapperbean.MoeQbSyncPackageExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeQbSyncPackageMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_package
     *
     * @mbg.generated
     */
    long countByExample(MoeQbSyncPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_package
     *
     * @mbg.generated
     */
    int deleteByExample(MoeQbSyncPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_package
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_package
     *
     * @mbg.generated
     */
    int insert(MoeQbSyncPackage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_package
     *
     * @mbg.generated
     */
    int insertSelective(MoeQbSyncPackage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_package
     *
     * @mbg.generated
     */
    List<MoeQbSyncPackage> selectByExampleWithBLOBs(MoeQbSyncPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_package
     *
     * @mbg.generated
     */
    List<MoeQbSyncPackage> selectByExample(MoeQbSyncPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_package
     *
     * @mbg.generated
     */
    MoeQbSyncPackage selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_package
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeQbSyncPackage record, @Param("example") MoeQbSyncPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_package
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("record") MoeQbSyncPackage record, @Param("example") MoeQbSyncPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_package
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MoeQbSyncPackage record, @Param("example") MoeQbSyncPackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_package
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeQbSyncPackage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_package
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeQbSyncPackage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_package
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeQbSyncPackage record);

    List<MoeQbSyncPackage> selectByBusinessIdRealmIdPackageIds(
            @Param("businessId") Integer businessId,
            @Param("realmId") String realmId,
            @Param("packageIdList") List<Integer> packageIdList);

    int insertOrUpdate(MoeQbSyncPackage syncPackage);
}
