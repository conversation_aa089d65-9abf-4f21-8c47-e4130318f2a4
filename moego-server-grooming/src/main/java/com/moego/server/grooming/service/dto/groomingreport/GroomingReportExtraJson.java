package com.moego.server.grooming.service.dto.groomingreport;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.moego.server.grooming.dto.groomingreport.BodyViewUrl;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GroomingReportExtraJson {

    private List<String> buildInOptions;
    private List<String> options;
    private List<String> choices;
    private List<String> customOptions;
    private Boolean show;
    private String text;
    private String placeholder;
    private BodyViewUrl urls;
}
