package com.moego.server.grooming.web.vo;

import static com.moego.server.grooming.dto.AbandonedScheduleMessageSettingDTO.ClientType;
import static com.moego.server.grooming.dto.AbandonedScheduleMessageSettingDTO.SendOutType;

import com.moego.server.grooming.enums.OBStepEnum;
import java.time.DayOfWeek;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AbandonedScheduleMessageSettingVO {
    private Integer id;
    private Integer businessId;
    private List<ClientType> clientTypes;
    private List<OBStepEnum> abandonedSteps;
    private SendOutType sendOutType;
    private List<DayOfWeek> onTypeDays;
    private Integer onTypeMinute;
    private Integer waitForTypeHour;
    private Integer waitForTypeMinute;
    private String message;
    private Boolean isEnabled;
}
