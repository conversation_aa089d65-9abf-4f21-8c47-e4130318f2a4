package com.moego.server.grooming.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration(proxyBeanMethods = false)
@ConfigurationProperties(prefix = "moego.driving-info")
public class DrivingInfoProperties {

    private int corePoolSize;

    private int maximumPoolSize;

    private int aliveTimeSeconds;

    private int queueCapacity;
}
