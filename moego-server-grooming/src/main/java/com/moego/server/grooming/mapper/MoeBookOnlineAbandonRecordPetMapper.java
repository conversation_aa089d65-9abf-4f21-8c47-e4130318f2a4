package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPet;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPetExample;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

public interface MoeBookOnlineAbandonRecordPetMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    long countByExample(MoeBookOnlineAbandonRecordPetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlineAbandonRecordPet record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlineAbandonRecordPet record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    List<MoeBookOnlineAbandonRecordPet> selectByExampleWithBLOBs(MoeBookOnlineAbandonRecordPetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    List<MoeBookOnlineAbandonRecordPet> selectByExample(MoeBookOnlineAbandonRecordPetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    MoeBookOnlineAbandonRecordPet selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeBookOnlineAbandonRecordPet record,
            @Param("example") MoeBookOnlineAbandonRecordPetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("record") MoeBookOnlineAbandonRecordPet record,
            @Param("example") MoeBookOnlineAbandonRecordPetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeBookOnlineAbandonRecordPet record,
            @Param("example") MoeBookOnlineAbandonRecordPetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlineAbandonRecordPet record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeBookOnlineAbandonRecordPet record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlineAbandonRecordPet record);

    int batchInsertRecordPet(List<MoeBookOnlineAbandonRecordPet> list);

    List<MoeBookOnlineAbandonRecordPet> listRecordPetByBookingFlowId(Integer businessId, String bookingFlowId);

    int deleteRecordPetByBookingFlowId(Integer businessId, String bookingFlowId);

    int batchUpdateRecordPet(List<MoeBookOnlineAbandonRecordPet> list);

    Set<String> selectBookingFlowIdsByKeyword(Integer businessId, String keyword);

    int resetPetIdByBookingFlowId(Integer businessId, String bookingFlowId, List<Integer> petIds);
}
