package com.moego.server.grooming.web.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/13
 */
@Data
@Accessors(chain = true)
public class AddressVO {

    @Schema(description = "Address1")
    private String address1;

    @Schema(description = "Address2")
    private String address2;

    @Schema(description = "City")
    private String city;

    @Schema(description = "Country")
    private String country;

    @Schema(description = "Latitude")
    private String lat;

    @Schema(description = "Longitude")
    private String lng;

    @Schema(description = "State")
    private String state;

    @Schema(description = "Zipcode")
    private String zipcode;
}
