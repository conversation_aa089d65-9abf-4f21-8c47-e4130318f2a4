package com.moego.server.grooming.service.dto;

import com.moego.server.grooming.dto.GroomingPetInfoDetailDTO;
import java.util.List;
import lombok.Data;

@Data
public class CalendarEventParamDto {

    private Integer syncedStaffId;
    private String summary;
    private String fullAddress;
    private String clientEmail;
    private String description;
    private List<PetDetailTimeDto> PetDetailTimes;
    private Long startTime;
    private Boolean isBlock;
    private Integer importGcCalendarId;
    private Long endTime;
    private String appointmentDate;
    private String timezone;
    private Integer groomingId;

    private String staffName;
    private String storeName;
    private String customerName;
    private String petAmount;

    private List<GroomingPetInfoDetailDTO> petInfoDetails;
    private PetDetailTimeDto eventTime;
}
