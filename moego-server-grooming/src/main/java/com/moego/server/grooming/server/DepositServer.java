package com.moego.server.grooming.server;

import com.moego.common.enums.PaymentStatusEnum;
import com.moego.server.grooming.api.IDepositServiceBase;
import com.moego.server.grooming.dto.DepositDto;
import com.moego.server.grooming.mapper.MoeInvoiceDepositMapper;
import com.moego.server.grooming.mapperbean.MoeInvoiceDeposit;
import com.moego.server.grooming.params.DepositVo;
import com.moego.server.grooming.service.DepositService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2021/11/3 6:23 PM
 */
@RestController
@Slf4j
public class DepositServer extends IDepositServiceBase {

    @Autowired
    private MoeInvoiceDepositMapper invoiceDepositMapper;

    @Autowired
    private DepositService depositService;

    @Override
    public Integer updateDepositPaid(Integer invoiceId) {
        MoeInvoiceDeposit deposit = invoiceDepositMapper.selectByInvoiceId(invoiceId);
        if (deposit == null || !PaymentStatusEnum.PROCESSING.equals(deposit.getStatus())) {
            log.warn("invalid deposit record for this {}", invoiceId);
            return 0;
        }
        MoeInvoiceDeposit update = new MoeInvoiceDeposit();
        update.setId(deposit.getId());
        update.setStatus(PaymentStatusEnum.PAID);
        return invoiceDepositMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public String createOrUpdateDeposit(@RequestBody DepositVo depositVo) {
        return depositService.createDepositId(depositVo, Boolean.TRUE);
    }

    @Override
    public DepositDto getDepositByInvoiceId(@RequestParam("invoiceId") Integer invoiceId) {
        MoeInvoiceDeposit deposit = depositService.getDepositByInvoiceId(invoiceId);
        if (deposit == null) {
            return null;
        }
        DepositDto depositDto = new DepositDto();
        BeanUtils.copyProperties(deposit, depositDto);
        return depositDto;
    }
}
