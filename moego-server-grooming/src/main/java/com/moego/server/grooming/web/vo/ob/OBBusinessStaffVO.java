package com.moego.server.grooming.web.vo.ob;

import com.moego.server.grooming.dto.ob.PetApplicableDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/18
 */
@Data
public class OBBusinessStaffVO {

    @Schema(description = "staff id")
    private Integer id;

    @Schema(description = "Account avatar path")
    private String avatarPath;

    @Schema(description = "Settings / Staff / First name")
    private String firstName;

    @Schema(description = "Settings / Staff / Last name")
    private String lastName;

    @Schema(description = "pet applicable service DTO list")
    private List<PetApplicableDTO> petApplicableDTOList;

    @Schema(description = "first available date")
    private String firstAvailableDate;
}
