package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeQbSyncPayment;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeQbSyncPaymentMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_payment
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_payment
     *
     * @mbg.generated
     */
    int insert(MoeQbSyncPayment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_payment
     *
     * @mbg.generated
     */
    int insertSelective(MoeQbSyncPayment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_payment
     *
     * @mbg.generated
     */
    MoeQbSyncPayment selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_payment
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeQbSyncPayment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_payment
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeQbSyncPayment record);

    List<MoeQbSyncPayment> selectByInvoiceId(
            @Param("businessId") Integer businessId,
            @Param("realmId") String realmId,
            @Param("invoiceId") Integer invoiceId);

    MoeQbSyncPayment selectByBusinessIdAndPaymentId(
            @Param("businessId") Integer businessId,
            @Param("realmId") String realmId,
            @Param("paymentId") Integer paymentId);

    List<MoeQbSyncPayment> listByBusinessAndInvoiceIds(
            @Param("businessId") Integer businessId, List<Integer> invoiceIds);
}
