package com.moego.server.grooming.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.service.MoeGroomingQuestionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Will be removed after migration is done
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class WashController {

    private final MoeGroomingQuestionService questionService;

    @GetMapping("/grooming/wash/vaccine/question")
    @Auth(AuthType.ANONYMOUS)
    public void migrateVaccineQuestion(
            @RequestParam Integer fromBusinessId, @RequestParam(required = false) Integer toBusinessId) {
        questionService.migrateVaccineQuestion(fromBusinessId, toBusinessId);
    }
}
