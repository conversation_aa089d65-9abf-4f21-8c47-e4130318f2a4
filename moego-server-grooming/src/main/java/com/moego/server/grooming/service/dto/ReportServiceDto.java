package com.moego.server.grooming.service.dto;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class ReportServiceDto {

    private Integer id;

    private Integer businessId;

    private Integer categoryId;

    private String name;

    private String description;

    private Byte type;

    private Integer taxId;

    private BigDecimal price;

    private Integer duration;

    private Byte inactive;

    private Integer sort;

    private String colorCode;

    private Byte status;

    private String category;

    private Integer quantity;
    private BigDecimal totalSalePrice;
    private BigDecimal taxAmount;
    private BigDecimal totalListPrice;
}
