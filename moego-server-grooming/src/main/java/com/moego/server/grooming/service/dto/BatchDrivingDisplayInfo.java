package com.moego.server.grooming.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;

public record BatchDrivingDisplayInfo(
        @Schema(description = "查询日期内的 driving info 列表") List<DateDrivingDisplayInfo> dateDrivingInfoList) {

    public record DateDrivingDisplayInfo(
            @Schema(description = "日期: yyyy-MM-dd") String date,
            @Schema(description = "当前日期下 staff driving info 列表") List<StaffDrivingDisplayInfo> staffDrivingInfoList) {}

    public record StaffDrivingDisplayInfo(
            @Schema(description = "staff id") Integer staffId,
            @Schema(description = "单个 staff driving info 列表") List<DrivingDisplayInfo> drivingInfoList) {}
}
