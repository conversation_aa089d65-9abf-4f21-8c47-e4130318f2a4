package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.AppointmentOutbox;

public interface AppointmentOutboxMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_outbox
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_outbox
     *
     * @mbg.generated
     */
    int insert(AppointmentOutbox record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_outbox
     *
     * @mbg.generated
     */
    int insertSelective(AppointmentOutbox record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_outbox
     *
     * @mbg.generated
     */
    AppointmentOutbox selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_outbox
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(AppointmentOutbox record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_outbox
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(AppointmentOutbox record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_outbox
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(AppointmentOutbox record);
}
