package com.moego.server.grooming.web.vo.ob;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@Data
@Accessors(chain = true)
public class OBLandingPageServiceCategoryVO {

    @Schema(description = "Category ID")
    private Integer categoryId;

    @Schema(description = "Category name")
    private String categoryName;

    @Schema(description = "Category type, 1-service, 2-addons")
    private Byte categoryType;

    @Schema(description = "Category sort number")
    private Integer sort;

    @Schema(description = "Service list")
    private List<OBLandingPageServiceVO> serviceList;

    @Data
    @Accessors(chain = true)
    public static class OBLandingPageServiceVO {

        @Schema(description = "Service ID")
        private Integer serviceId;

        @Schema(description = "Service name")
        private String serviceName;

        @Schema(description = "Service description")
        private String description;

        @Schema(description = "Service type, 1-service, 2-addons")
        private Byte serviceType;

        @Schema(description = "Color code, #ffffff")
        private String colorCode;

        @Schema(description = "Service duration, minute")
        private Integer duration;

        @Schema(description = "Service price, business currency")
        private BigDecimal price;

        @Schema(description = "Show base price")
        private Byte showBasePrice;

        @Schema(description = "Sort number")
        private Integer sort;

        @Schema(description = "Bundle service IDs")
        private List<Long> bundleServiceIds;
    }
}
