package com.moego.server.grooming.web.vo;

import com.moego.common.dto.PageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class AbandonedClientPageDTO<T> extends PageDTO<T> {

    /**
     * 未经过 filter 的总数量，Recoverable 的总数量
     */
    private int abandonedClientTotalCount;

    /**
     * Recoverable 里 Abandoned 的数量
     */
    private int abandonedClientAbandonedCount;

    public static <T> AbandonedClientPageDTO<T> of(PageDTO<T> pageDTO) {
        AbandonedClientPageDTO<T> abandonedClientPageDTO = new AbandonedClientPageDTO<>();
        abandonedClientPageDTO.setDataList(pageDTO.getDataList());
        abandonedClientPageDTO.setPageNo(pageDTO.getPageNo());
        abandonedClientPageDTO.setPageSize(pageDTO.getPageSize());
        abandonedClientPageDTO.setTotal(pageDTO.getTotal());
        abandonedClientPageDTO.setEnd(pageDTO.isEnd());
        return abandonedClientPageDTO;
    }
}
