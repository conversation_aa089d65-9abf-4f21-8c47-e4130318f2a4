package com.moego.server.grooming.web.vo.ob;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.web.vo.client.StaffDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Builder(toBuilder = true)
public record AbandonClientRecordVO(
        String bookingFlowId,
        OBStepEnum abandonStep,
        Integer abandonTime,
        @Schema(description = "abandon status, abandoned, contacted, recovered") String abandonStatus,
        @Schema(description = "The last texted time of this abandon record") Long lastTextedTime,
        @Schema(description = "The last emailed time of this abandon record") Long lastEmailedTime,
        String appointmentDate,
        Integer appointmentStartTime,
        String additionalNote,
        @Schema(description = "has customer profile request update") boolean hasRequestUpdate,
        ClientDetailVO customer,
        ClientPreferenceVO preference,
        StaffDetailVO staff,
        AddressDetailVO address,
        List<AbandonPetDetailVO> pets,
        List<AbandonServiceDetailVO> services, // 没有考虑 service override
        List<PetDetail> petDetails, // 有考虑 service override
        ServiceItemEnum careType,
        String appointmentEndDate,
        Integer appointmentEndTime,
        List<String> specificDates) {
    @Builder(toBuilder = true)
    public record ClientDetailVO(
            String referer,
            Integer customerId,
            String phoneNumber,
            String firstName,
            String lastName,
            String email,
            List<QuestionAnswerVO> questionAnswerList) {}

    @Builder(toBuilder = true)
    public record ClientPreferenceVO(
            Integer referralSourceId,
            Integer preferredGroomerId,
            Integer preferredFrequencyDay,
            Byte preferredFrequencyType,
            List<Integer> preferredDay,
            List<Integer> preferredTime) {}

    @Builder
    public record AddressDetailVO(
            Integer addressId,
            String address1,
            String address2,
            String city,
            String state,
            String zipcode,
            String country,
            String lat,
            String lng) {}

    @Builder(toBuilder = true)
    public record AbandonPetDetailVO(
            Integer petId,
            String petName,
            Integer petTypeId,
            String breed,
            String weight,
            String avatarPath,
            String fixed,
            Byte gender,
            String hairLength,
            String behavior,
            String birthday,
            List<VaccineDetailVO> vaccineList,
            Integer serviceId,
            List<Integer> addOnIds,
            String vetAddress,
            String vetName,
            String vetPhoneNumber,
            String emergencyContactName,
            String emergencyContactPhone,
            String healthIssues,
            List<QuestionAnswerVO> questionAnswerList) {
        @Builder
        public record VaccineDetailVO(
                Integer vaccineId, String vaccineDocument, List<String> documentUrls, String expirationDate) {}
    }

    public record AbandonServiceDetailVO(
            Integer serviceId, String serviceName, Byte serviceType, Integer duration, BigDecimal price) {}

    @Builder
    public record QuestionAnswerVO(String key, String question, String answer) {}

    @Builder
    public record PetDetail(
            Integer petId,
            Integer serviceId,
            Integer staffId,
            String serviceName,
            Byte serviceType,
            Integer duration,
            BigDecimal price,
            @Schema(type = "integer", format = "int32") @JsonFormat(shape = JsonFormat.Shape.NUMBER)
                    ServiceOverrideType priceOverrideType,
            @Schema(type = "integer", format = "int32") @JsonFormat(shape = JsonFormat.Shape.NUMBER)
                    ServiceOverrideType durationOverrideType) {}
}
