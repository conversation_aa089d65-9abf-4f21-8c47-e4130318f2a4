package com.moego.server.grooming.web;

import com.google.type.LatLng;
import com.moego.idl.models.map.v1.RouteMatrixElement;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerPrimaryAddressRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.utils.model.Pair;
import com.moego.server.business.client.IBusinessSmartSchedulingClient;
import com.moego.server.business.params.GetSmartScheduleSettingParams;
import com.moego.server.grooming.service.GoogleMapService;
import com.moego.server.grooming.web.params.DrivingInfoCalculationParams;
import com.moego.server.grooming.web.vo.DrivingInfoCalculationVo;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/9/13
 */
@RestController
@RequestMapping("/grooming/google/map")
@RequiredArgsConstructor
public class GoogleMapController {

    private final GoogleMapService googleMapService;
    private final BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub customerAddressClient;
    private final IBusinessSmartSchedulingClient iBusinessSmartSchedulingClient;

    @GetMapping("/place-id")
    @Auth(AuthType.BUSINESS)
    public String getPlace(@RequestParam @NotEmpty String lat, @RequestParam @NotEmpty String lng) {
        var address = googleMapService.queryAddress(Double.parseDouble(lat), Double.parseDouble(lng));
        if (address != null) {
            if (address.hasSourceType() && "GOOGLE_MAP".equals(address.getSourceType())) {
                return address.getSourceId();
            }
            if (address.hasAdditional()) {
                var additional = address.getAdditional();
                if (additional.containsFields("GoogleMap/placeId")) {
                    return additional.getFieldsOrThrow("GoogleMap/placeId").getStringValue();
                }
            }
        }

        return null;
    }

    @PostMapping("/driving-info/cal")
    @Auth(AuthType.COMPANY)
    public DrivingInfoCalculationVo drivingInfoCalculation(
            AuthContext context, @Valid @RequestBody DrivingInfoCalculationParams request) {
        Long companyId = context.companyId();
        Integer businessId = request.businessId();

        LatLng targetAddress = GoogleMapService.toGoogleLatLng(request.addressLat(), request.addressLng());

        DrivingInfoCalculationVo result = new DrivingInfoCalculationVo();
        calDrivingInfoByClientAddress(companyId, request.clientIdsFrom(), request.clientIdsTo(), targetAddress, result);
        calDrivingInfoByStaffVanAddress(
                businessId, request.vanStaffIdsFrom(), request.vanStaffIdsTo(), targetAddress, result);
        return result;
    }

    void calDrivingInfoByClientAddress(
            Long companyId,
            List<Long> clientIdsFrom,
            List<Long> clientIdsTo,
            LatLng targetAddress,
            DrivingInfoCalculationVo result) {
        Map<Long, DrivingInfoCalculationVo.DrivingCalculationVo> drivingFromMap = new HashMap<>();
        Map<Long, DrivingInfoCalculationVo.DrivingCalculationVo> drivingToMap = new HashMap<>();
        result.setClientDrivingFromMap(drivingFromMap);
        result.setClientDrivingToMap(drivingToMap);

        // 拉取 customer 地址信息
        List<Long> customerIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(clientIdsFrom)) {
            customerIds.addAll(clientIdsFrom);
        }
        if (!CollectionUtils.isEmpty(clientIdsTo)) {
            customerIds.addAll(clientIdsTo);
        }
        if (customerIds.isEmpty()) {
            return;
        }
        Map<Long, LatLng> addressMap = getCustomerAddress(companyId, customerIds);

        // 计算驾驶信息
        if (!CollectionUtils.isEmpty(clientIdsFrom)) {
            Map<Long, LatLng> addressesFrom = clientIdsFrom.stream()
                    .filter(addressMap::containsKey)
                    .collect(Collectors.toMap(Function.identity(), addressMap::get, (k1, k2) -> k1));
            var drivingInfo = googleMapService.queryDrivingFrom(addressesFrom, targetAddress);
            drivingInfo.forEach((customerId, element) -> drivingFromMap.put(customerId, buildDrivingInfo(element)));
        }
        if (!CollectionUtils.isEmpty(clientIdsTo)) {
            Map<Long, LatLng> addressesTo = clientIdsTo.stream()
                    .filter(addressMap::containsKey)
                    .collect(Collectors.toMap(Function.identity(), addressMap::get, (k1, k2) -> k1));
            var drivingInfo = googleMapService.queryDrivingTo(targetAddress, addressesTo);
            drivingInfo.forEach((customerId, element) -> drivingToMap.put(customerId, buildDrivingInfo(element)));
        }
    }

    void calDrivingInfoByStaffVanAddress(
            Integer businessId,
            List<Long> vanStaffIdsFrom,
            List<Long> vanStaffIdsTo,
            LatLng targetAddress,
            DrivingInfoCalculationVo result) {
        Map<Long, DrivingInfoCalculationVo.DrivingCalculationVo> drivingFromMap = new HashMap<>();
        Map<Long, DrivingInfoCalculationVo.DrivingCalculationVo> drivingToMap = new HashMap<>();
        result.setVanStaffDrivingFromMap(drivingFromMap);
        result.setVanStaffDrivingToMap(drivingToMap);

        // 拉取 staffVan 地址信息
        List<Long> staffIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(vanStaffIdsFrom)) {
            staffIds.addAll(vanStaffIdsFrom);
        }
        if (!CollectionUtils.isEmpty(vanStaffIdsTo)) {
            staffIds.addAll(vanStaffIdsTo);
        }
        if (staffIds.isEmpty()) {
            return;
        }
        var addressMap = getStaffVanAddress(businessId, staffIds);

        // 计算驾驶信息
        if (!CollectionUtils.isEmpty(vanStaffIdsFrom)) {
            Map<Long, LatLng> addressesFrom = vanStaffIdsFrom.stream()
                    .filter(k -> addressMap.get(k) != null && addressMap.get(k).key() != null)
                    .collect(Collectors.toMap(
                            Function.identity(), k -> addressMap.get(k).key(), (k1, k2) -> k1));
            var drivingInfo = googleMapService.queryDrivingFrom(addressesFrom, targetAddress);
            drivingInfo.forEach((staffId, element) -> drivingFromMap.put(staffId, buildDrivingInfo(element)));
        }

        if (!CollectionUtils.isEmpty(vanStaffIdsTo)) {
            Map<Long, LatLng> addressesTo = vanStaffIdsTo.stream()
                    .filter(k -> addressMap.get(k) != null && addressMap.get(k).value() != null)
                    .collect(Collectors.toMap(
                            Function.identity(), k -> addressMap.get(k).value(), (k1, k2) -> k1));
            var drivingInfo = googleMapService.queryDrivingTo(targetAddress, addressesTo);
            drivingInfo.forEach((staffId, element) -> drivingToMap.put(staffId, buildDrivingInfo(element)));
        }
    }

    Map<Long, LatLng> getCustomerAddress(Long companyId, List<Long> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return new HashMap<>();
        }
        var addressRequest = BatchGetCustomerPrimaryAddressRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                .addAllCustomerIds(customerIds.stream().distinct().toList())
                .build();
        var resp = customerAddressClient
                .batchGetCustomerPrimaryAddress(addressRequest)
                .getAddressesMap();
        Map<Long, LatLng> result = new HashMap<>();
        resp.forEach((customerId, address) -> {
            if (address == null || !address.hasCoordinate()) {
                return;
            }
            result.put(customerId, address.getCoordinate());
        });

        return result;
    }

    /**
     *
     * @return Map<staffId, Pair<vanStartLocation, vanEndLocation>>
     */
    Map<Long, Pair<LatLng, LatLng>> getStaffVanAddress(Integer businessId, List<Long> vanStaffIds) {
        if (CollectionUtils.isEmpty(vanStaffIds)) {
            return new HashMap<>();
        }

        var resp = iBusinessSmartSchedulingClient.getStaffSmartScheduleSettingMap(new GetSmartScheduleSettingParams(
                businessId, vanStaffIds.stream().distinct().map(Long::intValue).toList()));
        Map<Long, Pair<LatLng, LatLng>> result = new HashMap<>();
        resp.forEach((staffId, setting) -> {
            if (setting == null || setting.getLocation() == null) {
                return;
            }
            var location = setting.getLocation();
            LatLng start = null;
            LatLng end = null;
            if (StringUtils.hasText(location.getStartLat()) && StringUtils.hasText(location.getStartLng())) {
                start = GoogleMapService.toGoogleLatLng(location.getStartLat(), location.getStartLng());
            }
            if (StringUtils.hasText(location.getEndLat()) && StringUtils.hasText(location.getEndLng())) {
                end = GoogleMapService.toGoogleLatLng(location.getEndLat(), location.getEndLng());
            }
            result.put(staffId.longValue(), new Pair<>(start, end));
        });
        return result;
    }

    DrivingInfoCalculationVo.DrivingCalculationVo buildDrivingInfo(RouteMatrixElement element) {
        return new DrivingInfoCalculationVo.DrivingCalculationVo(
                GoogleMapService.takeMinutesFromElement(element),
                GoogleMapService.takeMilesFromElement1ScaleFloor(element));
    }
}
