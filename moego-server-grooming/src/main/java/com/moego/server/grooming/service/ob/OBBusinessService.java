package com.moego.server.grooming.service.ob;

import com.google.gson.Gson;
import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessServiceAreaClient;
import com.moego.server.business.client.IBusinessWorkingHourClient;
import com.moego.server.business.dto.BusinessWorkingHourDetailDTO;
import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.params.BatchGetAreasByLocationParams;
import com.moego.server.business.params.GetAreasByLocationParams;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.grooming.dto.ob.ServiceAreaResultDTO;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.params.ob.ServiceAreaParams;
import com.moego.server.grooming.service.GoogleMapService;
import com.moego.server.grooming.service.GroomingFeaturePricingCheckService;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.SmartScheduleService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2022/10/25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OBBusinessService {

    private final MoeBusinessBookOnlineMapper moeBusinessBookOnlineMapper;
    private final GroomingFeaturePricingCheckService pricingCheckService;
    private final MoeGroomingBookOnlineService bookOnlineService;
    private final SmartScheduleService smartScheduleService;
    private final IBusinessBusinessClient iBusinessBusinessClient;
    private final IBusinessWorkingHourClient iBusinessWorkingHourClient;
    private final IBusinessServiceAreaClient iBusinessServiceAreaClient;
    private final GoogleMapService googleMapService;

    public OBBusinessInfoDTO getBusinessInfo(Integer businessId) {
        // get business info
        InfoIdParams infoIdParams = new InfoIdParams();
        infoIdParams.setInfoId(businessId);
        return iBusinessBusinessClient.getBusinessInfoForOB(infoIdParams);
    }

    public BusinessWorkingHourDetailDTO getBusinessWorkingHours(Integer businessId) {
        return iBusinessWorkingHourClient.getBusinessWorkingHour(businessId);
    }

    public MoeBusinessBookOnline getSettingInfoByBusinessId(Integer businessId) {
        MoeBusinessBookOnline obSetting = moeBusinessBookOnlineMapper.selectByBusinessId(businessId);
        pricingCheckService.checkOnlineBooking(businessId, obSetting);
        if (!BooleanEnum.VALUE_TRUE.equals(obSetting.getIsEnable())) {
            throw ExceptionUtil.bizException(Code.CODE_BOOK_ONLINE_NOT_ENABLE);
        }
        return obSetting;
    }

    public List<ServiceAreaResultDTO> buildAvailableResult(List<ServiceAreaParams.ClientAddressParams> paramsList) {
        return paramsList.stream()
                .map(params -> {
                    ServiceAreaResultDTO serviceAreaResultDTO = new ServiceAreaResultDTO();
                    serviceAreaResultDTO.setAddressId(params.getAddressId());
                    serviceAreaResultDTO.setOutOfArea(false);
                    return serviceAreaResultDTO;
                })
                .toList();
    }

    public boolean disableServiceArea(MoeBusinessBookOnline businessBookOnline) {
        if (Objects.equals(businessBookOnline.getIsNeedAddress(), CommonConstant.DISABLE)) {
            return true;
        }
        return CollectionUtils.isEmpty(businessBookOnline.getServiceAreas())
                && PrimitiveTypeUtil.isNullOrZero(businessBookOnline.getIsByZipcode())
                && PrimitiveTypeUtil.isNullOrZero(businessBookOnline.getIsByRadius());
    }

    public List<ServiceAreaResultDTO> getServiceAreaResultList(ServiceAreaParams serviceAreaParams) {
        List<ServiceAreaParams.ClientAddressParams> paramsList = serviceAreaParams.getAddressParamsList();

        MoeBusinessBookOnline businessBookOnline =
                bookOnlineService.getSettingInfoByBusinessId(serviceAreaParams.getBusinessId());

        Map<Long, List<CertainAreaDTO>> certainAreaMap;
        if (!CollectionUtils.isEmpty(businessBookOnline.getServiceAreas())) {
            certainAreaMap = iBusinessServiceAreaClient.getAreasByLocation(new BatchGetAreasByLocationParams(
                    serviceAreaParams.getBusinessId().longValue(),
                    businessBookOnline.getServiceAreas(),
                    paramsList.stream()
                            .map(k -> new GetAreasByLocationParams(
                                    k.getAddressId().longValue(), k.getLat(), k.getLng(), k.getZipcode()))
                            .toList()));
        } else {
            certainAreaMap = new HashMap<>();
        }

        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(serviceAreaParams.getBusinessId()).build());
        Byte unitOfDistanceType = businessInfo.getUnitOfDistanceType();
        return paramsList.stream()
                .map(params -> {
                    ServiceAreaResultDTO resultDTO = new ServiceAreaResultDTO();
                    resultDTO.setAddressId(params.getAddressId());
                    resultDTO.setOutOfArea(BooleanUtils.isFalse(
                            checkAvailableDist(params, businessBookOnline, unitOfDistanceType, certainAreaMap)));
                    return resultDTO;
                })
                .toList();
    }

    /**
     * 仅判断 ob setting 中的 zipcode、radius
     * @param serviceAreaParams
     * @return
     */
    public List<ServiceAreaResultDTO> getOBServiceAreaResultList(ServiceAreaParams serviceAreaParams) {
        List<ServiceAreaParams.ClientAddressParams> paramsList = serviceAreaParams.getAddressParamsList();

        MoeBusinessBookOnline businessBookOnline = this.getSettingInfoByBusinessId(serviceAreaParams.getBusinessId());
        if (Objects.equals(businessBookOnline.getIsNeedAddress(), CommonConstant.DISABLE)
                || (PrimitiveTypeUtil.isNullOrZero(businessBookOnline.getIsByZipcode())
                        && PrimitiveTypeUtil.isNullOrZero(businessBookOnline.getIsByRadius()))) {
            return buildAvailableResult(paramsList);
        }

        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(serviceAreaParams.getBusinessId()).build());
        Byte unitOfDistanceType = businessInfo.getUnitOfDistanceType();
        return paramsList.stream()
                .map(params -> {
                    ServiceAreaResultDTO resultDTO = new ServiceAreaResultDTO();
                    resultDTO.setAddressId(params.getAddressId());
                    resultDTO.setOutOfArea(BooleanUtils.isFalse(
                            checkAvailableDist(params, businessBookOnline, unitOfDistanceType, null)));
                    return resultDTO;
                })
                .toList();
    }

    public Boolean checkAvailableDist(
            ServiceAreaParams.ClientAddressParams params,
            MoeBusinessBookOnline obSettings,
            Byte unitOfDistanceType,
            Map<Long, List<CertainAreaDTO>> certainAreaMap) {
        String lat = params.getLat();
        String lng = params.getLng();
        String zipcode = params.getZipcode();

        // 0. by business service area
        if (certainAreaMap != null
                && !CollectionUtils.isEmpty(
                        certainAreaMap.get(params.getAddressId().longValue()))) {
            return true;
        }

        // 如果没有开启 service area，则均视为在服务区
        if (disableServiceArea(obSettings)) {
            log.info(
                    "disable service area, isByZipCode = {}, isByRadius = {}, isNeedAddress = {})",
                    obSettings.getIsByZipcode(),
                    obSettings.getIsByRadius(),
                    obSettings.getIsNeedAddress());
            return true;
        }

        // 1. by zipcode
        if (CommonConstant.ENABLE.equals(obSettings.getIsByZipcode())) {
            if (checkByZipcode(zipcode, lat, lng, obSettings)) {
                log.info("check by zipcode: {}, lat: {}, lng: {} is available.", zipcode, lat, lng);
                return true;
            }
            log.debug(
                    "check by zip code, but no param is passed in. zipcode<{}>, lat<{}>, lng<{}>.", zipcode, lat, lng);
        }

        // 2. by radius
        if (CommonConstant.ENABLE.equals(obSettings.getIsByRadius())) {
            if (disableRadiusSetting(obSettings)) {
                log.info(
                        "check by radius, business: {} setting location not set location.", obSettings.getBusinessId());
                return true;
            }
            if (checkByRadius(lat, lng, obSettings, unitOfDistanceType)) {
                log.info(
                        "check by radius, business: {} lat<{}> lng<{}>, client lat<{}> lng<{}> is available.",
                        obSettings.getBusinessId(),
                        obSettings.getSettingLat(),
                        obSettings.getSettingLng(),
                        lat,
                        lng);
                return true;
            }
            log.debug("check by radius, but no param is passed in. zipcode<{}>, lat<{}>, lng<{}>.", zipcode, lat, lng);
        }

        // zipcode 和 radius都不满足
        return false;
    }

    public boolean disableRadiusSetting(MoeBusinessBookOnline obSettings) {
        return (!StringUtils.hasText(obSettings.getSettingLocation())
                || !StringUtils.hasText(obSettings.getSettingLat())
                || !StringUtils.hasText(obSettings.getSettingLng()));
    }

    public boolean checkByZipcode(String zipcode, String lat, String lng, MoeBusinessBookOnline obSettings) {
        // 如果 ob settings 中没有配置需要匹配的 zipcode，则直接返回 true。
        if (!StringUtils.hasText(obSettings.getZipCodes())) {
            log.info("check by zip code, business: {} not set zip code.", obSettings.getBusinessId());
            return true;
        }
        String country = "";
        if (!StringUtils.hasText(zipcode)) {
            // 如果参数中没有指定 zipcode，则根据坐标值调用 google api 查询 zipcode
            if (!StringUtils.hasText(lat) || !StringUtils.hasText(lng)) {
                log.warn("must provide zipcode or latitude and longitude coordinates");
                return false;
            }
            try {
                var address = googleMapService.queryAddress(Double.parseDouble(lat), Double.parseDouble(lng));
                if (address != null) {
                    country = address.getCountry();
                    if (address.hasPostalCode()) {
                        zipcode = address.getPostalCode();
                    }
                } else {
                    log.warn("query geocoding from GoogleAPI failed, lat: {}, lng: {}", lat, lng);
                    return false;
                }
                log.info("got zipcode: {} by lat: {} lng: {}", zipcode, lat, lng);
            } catch (Exception e) {
                log.error("query geocoding(" + lat + ", " + lng + ") from GoogleAPI occur EXCEPTION: ", e);
                return false;
            }
        }
        log.info("ob check area got zipcode: {}", zipcode);
        // 如果 zipcode 获取失败，则直接返回 false
        if (!StringUtils.hasText(zipcode)) {
            return false;
        }
        // 去除 zipcode 中的 空格
        zipcode = zipcode.replace(" ", "");
        String[] zipcodes = obSettings.getZipCodes().split(",");
        // 先直接比较，如果有完全匹配则直接返回 true。
        for (String code : zipcodes) {
            if (code.equals(zipcode)) {
                return true;
            }
        }
        // 否则根据国家再进行特殊判定
        // 先直接根据 zipcode 判定国家代码
        if (!StringUtils.hasText(country)) {
            country = getCountryByZipcode(zipcode);
        }
        // 再根据 location 信息中提取国家代码
        if (!StringUtils.hasText(country) && CommonConstant.ENABLE.equals(obSettings.getIsByRadius())) {
            country = getCountryByLocation(obSettings.getSettingLocation());
        }
        // 如果始终取不到国家代码，直接返回false。
        if (!StringUtils.hasText(country)) {
            return false;
        }
        // 如果是 Canada，则可以仅匹配前3位
        if ("CA".equalsIgnoreCase(country)) {
            for (String code : zipcodes) {
                if (6 == zipcode.length() && 3 == code.length() && zipcode.startsWith(code)) {
                    return true;
                }
            }
        }
        // 如果是 Britain，舍去后面3位，匹配前面剩下的
        if ("GB".equalsIgnoreCase(country)) {
            for (String code : zipcodes) {
                if (((5 == zipcode.length() && 2 == code.length())
                                || (6 == zipcode.length() && 3 == code.length())
                                || (7 == zipcode.length() && 4 == code.length()))
                        && zipcode.startsWith(code)) {
                    return true;
                }
            }
        }

        return false;
    }

    public boolean checkByRadius(String lat, String lng, MoeBusinessBookOnline obInfo, Byte unitOfDistanceType) {
        if (!StringUtils.hasText(lat) || !StringUtils.hasText(lng)) {
            return false;
        }
        // 起点 business location: obInfo.getSettingLat(),obInfo.getSettingLng()
        // 终点 client location: lat,lng
        try {
            // reverse in new-ob c
            var distanceMatrixElement =
                    smartScheduleService.callGoogleAPI(obInfo.getSettingLat(), obInfo.getSettingLng(), lat, lng);
            log.info("google api result:" + new Gson().toJson(distanceMatrixElement));
            if (!GoogleMapService.isValidMatrixElement(distanceMatrixElement)) {
                return false;
            }
            // meters to mile / kilometer
            log.info("businessId: {}, maxAvailableDist: {} ", obInfo.getBusinessId(), obInfo.getMaxAvailableDist());
            if (obInfo.getMaxAvailableDist()
                    >= bookOnlineService.metersToMiOrKm(unitOfDistanceType, distanceMatrixElement.getDistance())) {
                return true;
            }
            log.info("businessId: {}, maxAvailableTime: {}", obInfo.getBusinessId(), obInfo.getMaxAvailableTime());
            if (obInfo.getMaxAvailableTime()
                    >= (distanceMatrixElement.getDuration().getSeconds() / 60)) {
                return true;
            }
        } catch (CommonException commonException) {
            if (commonException.getCode().equals(ResponseCodeEnum.GOOGLE_INVALID_ADDRESS.getCode())) {
                return false;
            }
            throw commonException;
        }
        // 距离和时间满足至少一个即可
        return false;
    }

    // 根据 location 信息提取国家代码
    public static String getCountryByLocation(String location) {
        if (location != null && !location.isEmpty()) {
            int index = location.lastIndexOf(',');
            if (0 < index) {
                var code = location.substring(index + 1).trim();
                if ("Canada".equals(code) || "CA".equals(code)) {
                    return "CA";
                }
                if ("UK".equals(code)) {
                    return "GB";
                }
                if ("USA".equals(code)) {
                    return "US";
                }
                if ("Australia".equals(code)) {
                    return "AU";
                }
            }
        }

        return null;
    }

    // 根据 zipcode 判定国家代码简写，目前仅支持 加拿大、英国、美国 的判定，后续可增加支持。
    public static String getCountryByZipcode(String zipcode) {
        int v = zipcodeToBitmap(zipcode);
        // 判定是否是加拿大 zipcode：6位，字母和数字交替，参见：https://en.wikipedia.org/wiki/Postal_codes_in_Canada
        if (6 == zipcode.length() && 0b00010101 == v) {
            return "CA";
        }
        // 判定是否是英国 zipcode：5-7位，参见：https://en.wikipedia.org/wiki/Postcodes_in_the_United_Kingdom
        if (isBritainZipcode(zipcode, v)) {
            return "GB";
        }
        // 判定是否是美国 zipcode：5位数字：参见：https://en.wikipedia.org/wiki/ZIP_Code
        if (5 == zipcode.length() && 0b00011111 == v) {
            return "US";
        }

        return null;
    }

    // 判定是否英国 zipcode
    private static boolean isBritainZipcode(String zipcode, int v) {
        return switch (zipcode.length()) {
            case 5 -> 0b00001100 == v;
            case 6 -> 0b00011100 == v
                    || 0b00001100 == v
                    || 0b00010100 == v
                    || (0b00000100 == v && zipcode.startsWith("GIR"));
            case 7 -> 0b00011100 == v || 0b00010100 == v;
            default -> false;
        };
    }

    // 将 zipcode 格式转成二进制数字，1 代表对应的位是数字，0 代表对应位是字母，否则 zipcode 非法。
    private static int zipcodeToBitmap(String zipcode) {
        int n = 0;
        for (int i = 0; i < zipcode.length(); ++i) {
            char c = zipcode.charAt(zipcode.length() - i - 1);
            if (Character.isDigit(c)) {
                n |= (1 << i);
            } else if (Character.isUpperCase(c)) {
                // do nothing
            } else {
                return -1;
            }
        }

        return n;
    }
}
