package com.moego.server.grooming.mapper.typehandler;

import com.moego.server.grooming.enums.PackageActivityEnum;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

public class PackageActivityEnumHandler extends BaseTypeHandler<PackageActivityEnum> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, PackageActivityEnum parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setInt(i, parameter.getValue());
    }

    @Override
    public PackageActivityEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        int status = rs.getInt(columnName);
        return getEnumByValue(status);
    }

    @Override
    public PackageActivityEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        int status = rs.getInt(columnIndex);
        return getEnumByValue(status);
    }

    @Override
    public PackageActivityEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        int status = cs.getInt(columnIndex);
        return getEnumByValue(status);
    }

    private PackageActivityEnum getEnumByValue(int value) {
        return Arrays.stream(PackageActivityEnum.values())
                .filter(e -> e.getValue() == value)
                .findFirst()
                .orElse(null); // 或者抛出异常
    }
}
