package com.moego.server.grooming.server;

import static com.moego.server.grooming.dto.AbandonRecordDTO.AbandonStep.listRecoverableSteps;
import static java.util.Optional.ofNullable;
import static java.util.function.Predicate.not;

import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.common.params.CustomerIdsParams;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.grooming.api.IAbandonRecordServiceBase;
import com.moego.server.grooming.dto.AbandonRecordDTO;
import com.moego.server.grooming.dto.AbandonRecordDTO.AbandonStep;
import com.moego.server.grooming.dto.ob.AssociateCustomerAndPetDTO;
import com.moego.server.grooming.dto.ob.AssociateCustomerDTO;
import com.moego.server.grooming.enums.AbandonDeleteTypeEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordExample;
import com.moego.server.grooming.mapstruct.AbandonRecordMapper;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.server.grooming.service.ob.OBAbandonRecordService;
import com.moego.server.grooming.utils.OBAbandonedUtil;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class AbandonRecordServer extends IAbandonRecordServiceBase {

    private final OBAbandonRecordService abandonRecordService;
    private final MoeBookOnlineAbandonRecordMapper abandonRecordMapper;
    private final MoeGroomingAppointmentService appointmentService;

    @Override
    public AbandonRecordDTO get(Integer id) {
        MoeBookOnlineAbandonRecordExample example = new MoeBookOnlineAbandonRecordExample();
        example.createCriteria().andIdEqualTo(id).andIsDeletedEqualTo(false);
        return abandonRecordMapper.selectByExample(example).stream()
                .map(AbandonRecordMapper.INSTANCE::entity2DTO)
                .findFirst()
                .orElse(null);
    }

    @Override
    public int update(AbandonRecordDTO param) {
        MoeBookOnlineAbandonRecord updateBean = AbandonRecordMapper.INSTANCE.dtoToEntity(param);
        updateBean.setUpdateTime(new Date());
        return abandonRecordMapper.updateByPrimaryKeySelective(updateBean);
    }

    @Override
    public Boolean associateCustomer(@RequestBody AssociateCustomerDTO associateCustomerDTO) {
        return abandonRecordService.associateCustomer(associateCustomerDTO);
    }

    @Override
    public Boolean associateCustomerAndPet(AssociateCustomerAndPetDTO associateCustomerAndPetDTO) {
        return abandonRecordService.associateCustomerAndPet(associateCustomerAndPetDTO);
    }

    @Override
    public Integer deleteAbandonRecords(Integer businessId, Integer customerId) {
        return abandonRecordService.deleteAbandonRecords(businessId, customerId);
    }

    @Override
    public Integer deleteRecordsWhenSubmitBookingRequest(Integer businessId, Integer customerId, String phoneNumber) {
        return abandonRecordMapper.removeAbandonRecords(
                businessId, AbandonDeleteTypeEnum.SUBMITTED.getType(), customerId, phoneNumber);
    }

    @Override
    public Set<Integer> listCustomerIdByFilter(ClientsFilterDTO clientsFilterDTO) {
        return abandonRecordService.listCustomerIdByFilter(clientsFilterDTO);
    }

    @Override
    public Boolean updateLastTextedTime(Integer businessId, Integer customerId, Long textedTime) {
        return abandonRecordService.updateLastTextedTime(businessId, customerId, textedTime);
    }

    @Override
    public Boolean updateLastEmailedTime(@RequestBody CustomerIdsParams params, @RequestParam Long emailedTime) {
        return abandonRecordService.updateLastEmailedTime(params, emailedTime);
    }

    @Override
    public List<AbandonRecordDTO> listAbandonRecordByAbandonTime(ListAbandonRecordByAbandonTimeParam param) {
        var example = new MoeBookOnlineAbandonRecordExample();
        example.createCriteria()
                .andAbandonTimeGreaterThanOrEqualTo(param.abandonTimeGe())
                .andAbandonTimeLessThanOrEqualTo(param.abandonTimeLe())
                .andIsDeletedEqualTo(false)
                .andRecoveryTypeEqualTo(0L)
                .andAbandonStepIn(
                        listRecoverableSteps().stream().map(AbandonStep::name).toList());

        var records = abandonRecordMapper.selectByExample(example);

        // abandon 记录会按照 customer id 去重，所以不能直接通过 SQL 过滤，需要先 filterLatestRecords 之后再过滤
        return OBAbandonedUtil.filterLatestRecords(records).stream()
                .filter(not(MoeBookOnlineAbandonRecord::getIsNotificationSent))
                .map(AbandonRecordMapper.INSTANCE::entity2DTO)
                .toList();
    }

    @Override
    public AbandonRecordDTO getLatestAbandonRecord() {
        MoeBookOnlineAbandonRecord abandonedRecord = abandonRecordMapper.getLatestAbandonedRecord();
        return AbandonRecordMapper.INSTANCE.entity2DTO(abandonedRecord);
    }

    @Override
    public List<AbandonRecordDTO> listByCondition(ListByConditionParam param) {
        MoeBookOnlineAbandonRecordExample example = new MoeBookOnlineAbandonRecordExample();
        MoeBookOnlineAbandonRecordExample.Criteria criteria = example.createCriteria();

        ofNullable(param.businessIdsIn())
                .filter(businessIdsIn -> !businessIdsIn.isEmpty())
                .ifPresent(businessIdsIn -> criteria.andBusinessIdIn(List.copyOf(businessIdsIn)));
        ofNullable(param.abandonTimeRange()).ifPresent(abandonTimeRange -> {
            ofNullable(abandonTimeRange.lower()).ifPresent(criteria::andAbandonTimeGreaterThanOrEqualTo);
            ofNullable(abandonTimeRange.upper()).ifPresent(criteria::andAbandonTimeLessThanOrEqualTo);
        });
        ofNullable(param.abandonStepIn())
                .filter(abandonStepIn -> !abandonStepIn.isEmpty())
                .ifPresent(abandonStepIn -> criteria.andAbandonStepIn(List.copyOf(abandonStepIn)));
        ofNullable(param.abandonStatusIn())
                .filter(abandonStatusIn -> !abandonStatusIn.isEmpty())
                .ifPresent(abandonStatusIn -> criteria.andAbandonStatusIn(List.copyOf(abandonStatusIn)));
        ofNullable(param.leadTypeIn())
                .filter(leadTypeIn -> !leadTypeIn.isEmpty())
                .ifPresent(leadTypeIn -> criteria.andLeadTypeIn(List.copyOf(leadTypeIn)));
        ofNullable(param.isSendScheduleMessage()).ifPresent(criteria::andIsSendScheduleMessageEqualTo);
        ofNullable(param.isNotificationSent()).ifPresent(criteria::andIsNotificationSentEqualTo);
        criteria.andIsDeletedEqualTo(false);

        return abandonRecordMapper.selectByExample(example).stream()
                .map(AbandonRecordMapper.INSTANCE::entity2DTO)
                .toList();
    }

    @Override
    public Boolean updateAbandonRecordToRecoveredByCustomer(
            Integer businessId, Integer customerId, Long appointmentId) {
        var appointment = appointmentService.getAppointment(appointmentId.intValue());
        if (!Objects.equals(appointment.getBusinessId(), businessId)
                || !Objects.equals(appointment.getCustomerId(), customerId)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "appointment not match");
        }
        appointmentService.updateAbandonRecords4Customer(appointment);
        return Boolean.TRUE;
    }

    @Override
    public void abandonRecordSendEvent() {
        ThreadPool.execute(abandonRecordService::abandonRecordSendEvent);
    }
}
