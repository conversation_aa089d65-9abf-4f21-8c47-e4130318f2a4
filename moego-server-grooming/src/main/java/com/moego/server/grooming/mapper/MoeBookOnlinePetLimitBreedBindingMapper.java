package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlinePetLimitBreedBinding;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeBookOnlinePetLimitBreedBindingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_pet_limit_breed_binding
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_pet_limit_breed_binding
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlinePetLimitBreedBinding record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_pet_limit_breed_binding
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlinePetLimitBreedBinding record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_pet_limit_breed_binding
     *
     * @mbg.generated
     */
    MoeBookOnlinePetLimitBreedBinding selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_pet_limit_breed_binding
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlinePetLimitBreedBinding record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_pet_limit_breed_binding
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeBookOnlinePetLimitBreedBinding record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_pet_limit_breed_binding
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlinePetLimitBreedBinding record);

    List<MoeBookOnlinePetLimitBreedBinding> selectByBusinessId(Integer businessId);

    List<MoeBookOnlinePetLimitBreedBinding> selectByIdList(@Param("idList") List<Long> idList);
}
