package com.moego.server.grooming.web.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/6
 */
@Data
@Accessors(chain = true)
public class OBProfileVO {

    @Schema(description = "Business id")
    private Integer businessId;

    @Schema(description = "Online booking business name")
    private String businessName;

    @Schema(description = "Online booking business address")
    private String address;

    @Schema(description = "Online booking business address details")
    private AddressVO addressDetails;

    @Schema(description = "Online booking business address")
    private String phoneNumber;

    @Schema(description = "Business owner account email")
    private String email;

    @Schema(description = "Business avatar path")
    private String avatarPath;
}
