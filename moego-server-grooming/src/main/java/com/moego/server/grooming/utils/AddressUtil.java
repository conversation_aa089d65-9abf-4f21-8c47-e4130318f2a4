package com.moego.server.grooming.utils;

import com.moego.server.business.dto.MoeBusinessDto;
import java.util.StringJoiner;
import org.springframework.util.StringUtils;

public class AddressUtil {

    public static String getBusinessAddress(MoeBusinessDto e) {
        StringJoiner sj = getFullAddress(
                e.getAddress1(),
                e.getAddress2(),
                e.getAddressCity(),
                e.getAddressState(),
                e.getCountry(),
                e.getAddressZipcode());
        return sj.toString();
    }

    public static StringJoiner getFullAddress(
            String address1, String address2, String city, String state, String country, String zipcode) {
        StringJoiner sj = new StringJoiner(", ");

        if (StringUtils.hasText(address1)) {
            sj.add(address1);
        }
        if (StringUtils.hasText(address2)) {
            sj.add(address2);
        }
        if (StringUtils.hasText(city)) {
            sj.add(city);
        }
        if (StringUtils.hasText(state)) {
            sj.add(state);
        }
        if (StringUtils.hasText(country)) {
            sj.add(country);
        }
        if (StringUtils.hasText(zipcode)) {
            sj.add(zipcode);
        }
        return sj;
    }
}
