package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGcEvent;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGcEventMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_event
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_event
     *
     * @mbg.generated
     */
    int insert(MoeGcEvent record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_event
     *
     * @mbg.generated
     */
    int insertSelective(MoeGcEvent record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_event
     *
     * @mbg.generated
     */
    MoeGcEvent selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_event
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGcEvent record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_event
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGcEvent record);

    List<MoeGcEvent> selectByBusinessIdGroomingId(
            @Param("businessId") Integer businessId, @Param("groomingId") Integer groomingId);

    List<MoeGcEvent> selectByGcCalendarIdGroomingId(
            @Param("businessId") Integer businessId,
            @Param("groomingId") Integer groomingId,
            @Param("gcCalendarId") Integer gcCalendarId);

    List<MoeGcEvent> selectByGcCalendarId(
            @Param("businessId") Integer businessId, @Param("gcCalendarId") Integer gcCalendarId);

    List<MoeGcEvent> selectByEventId(@Param("businessId") Integer businessId, @Param("eventId") String eventId);

    int updateSetDeleteByPrimaryKey(@Param("id") Integer id);
}
