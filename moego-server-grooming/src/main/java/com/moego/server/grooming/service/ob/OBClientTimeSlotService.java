package com.moego.server.grooming.service.ob;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.server.grooming.service.ob.OBPetLimitService.generateNonEmptySubLists;

import com.moego.common.constant.CommonConstant;
import com.moego.common.dto.BusinessPreferenceDto;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.CustomerContactEnum;
import com.moego.common.enums.OnlineBookingConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.enums.SubscriptionConst;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.FakeItUtil;
import com.moego.common.utils.WeekUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.AvailabilityType;
import com.moego.idl.models.organization.v1.ScheduleType;
import com.moego.idl.models.organization.v1.SlotAvailabilityDay;
import com.moego.idl.models.organization.v1.StaffAvailability;
import com.moego.idl.models.smart_scheduler.v1.TimeSlotType;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.lib.common.observability.metrics.prometheus.timer.TimerGroup;
import com.moego.lib.common.observability.metrics.prometheus.timer.TimerMetrics;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.lib.utils.model.Pair;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessClosedDateClient;
import com.moego.server.business.dto.AppointmentPetIdDTO;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.dto.ParsedCloseDate;
import com.moego.server.business.dto.StaffTimeslotPetCountDTO;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.BookOnlineStaffAvailabilityDTO;
import com.moego.server.grooming.dto.BookOnlineStaffTimeDTO;
import com.moego.server.grooming.dto.LimitGroupDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.dto.OBAvailableTimeSlotDetailDTO;
import com.moego.server.grooming.dto.OBAvailableTimeStaffDetailDTO;
import com.moego.server.grooming.dto.OBAvailableTimeWithNonAvailableDTO;
import com.moego.server.grooming.dto.StaffBlockInfoDTO;
import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import com.moego.server.grooming.dto.ob.OBTimeSlotDTO;
import com.moego.server.grooming.dto.ss.SmartScheduleResultDto;
import com.moego.server.grooming.dto.ss.SmartScheduleVO;
import com.moego.server.grooming.enums.StaffAvailableTypeEnum;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineStaffService;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.service.GroomingServiceService;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.StaffTimeSyncService;
import com.moego.server.grooming.service.dto.CapacityTimeslotDTO;
import com.moego.server.grooming.service.dto.OBAvailableDateTimeDTO;
import com.moego.server.grooming.service.dto.OBAvailableGroomerDateTimeDTO;
import com.moego.server.grooming.service.dto.OBAvailableTimeDetailDTO;
import com.moego.server.grooming.service.dto.OBAvailableTimeDto;
import com.moego.server.grooming.service.dto.OBAvailableTimeFullDTO;
import com.moego.server.grooming.service.dto.OBAvailableTimeStaffAvailableTimeDto;
import com.moego.server.grooming.service.dto.OBAvailableTimeStaffDto;
import com.moego.server.grooming.service.dto.OneDayTimeslotsDTO;
import com.moego.server.grooming.service.dto.PetToStaffDto;
import com.moego.server.grooming.service.dto.ob.OBPetLimitFilterDTO;
import com.moego.server.grooming.service.dto.ob.PetAvailableDTO;
import com.moego.server.grooming.service.remote.ServiceManagementService;
import com.moego.server.grooming.service.remote.SmartSchedulerService;
import com.moego.server.grooming.utils.OBSettingUtil;
import com.moego.server.grooming.utils.StaffTimeSyncUtil;
import com.moego.server.grooming.web.vo.ob.SmartScheduleV2Request;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotEmpty;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2020-11-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OBClientTimeSlotService {

    private static final int AM_PM_PIVOT = 12 * 60;
    private final GroomingServiceService groomingServiceService;
    private final MoeGroomingAppointmentService moeGroomingAppointmentService;
    private final SmartScheduleV2Service smartScheduleV2Service;
    private final OBBusinessService businessService;
    private final OBBusinessStaffService businessStaffService;
    private final StaffTimeSyncService staffTimeSyncService;
    private final OBClientService clientService;
    private final FeatureFlagApi featureFlagApi;

    private final IBusinessClosedDateClient iBusinessClosedDateClient;
    private final IBusinessBusinessClient iBusinessBusinessClient;
    private final OBPetLimitService obPetLimitService;
    private final MoeGroomingBookOnlineService moeGroomingBookOnlineService;
    private final ServiceManagementService serviceManagementService;
    private final SmartSchedulerService smartSchedulerService;
    private final CompanyHelper companyHelper;

    private final ExecutorService smartScheduleExecutorService;

    public boolean isMultiPetNewFlow(long businessId) {
        return featureFlagApi.isOn(
                FeatureFlags.ENABLE_MULTI_PET_BY_SLOT,
                FeatureFlagContext.builder().business(businessId).build());
    }

    private List<MoeStaffDto> getOBAvailableStaffList(Set<Integer> staffIdSet, Integer businessId) {
        return businessStaffService.getOBAvailableStaffList(businessId).stream()
                .filter(staff -> {
                    if (CollectionUtils.isEmpty(staffIdSet)) {
                        return true;
                    }
                    return staffIdSet.contains(staff.getId());
                })
                .toList();
    }

    public Set<Integer> getSyncWithWorkingHourStaffIdList(Integer businessId) {
        return moeGroomingBookOnlineService.getStaffOBAvailability(businessId, true).stream()
                .filter(Objects::nonNull)
                .filter(staff -> Objects.equals(
                        staff.getSyncWithWorkingHour(), OnlineBookingConst.SYNC_WITH_WORKING_HOUR_ENABLE))
                .map(BookOnlineStaffAvailabilityDTO::getStaffId)
                .collect(Collectors.toSet());
    }

    public static void validBookOnlineEnable(MoeBusinessBookOnline businessBookOnline) {
        if (CustomerContactEnum.BUSINESS_IS_NOT_ENABLE.equals(businessBookOnline.getIsEnable())) {
            log.info("OB 3.0 the business: [{}] disable ob", businessBookOnline.getBusinessId());
            throw new CommonException(
                    ResponseCodeEnum.BOOK_ONLINE_NOT_ENABLE, "please contact business admin to check.");
        }
    }

    public boolean getSmartScheduleFlag(MoeBusinessBookOnline businessBookOnline, OBBusinessInfoDTO obBusinessInfoDTO) {
        // Mobile / Hybrid
        boolean notSalon = !Objects.equals(obBusinessInfoDTO.getBusinessMode(), SubscriptionConst.BUSINESS_TYPE_SALON);
        boolean needAddress = Objects.equals(businessBookOnline.getIsNeedAddress(), CommonConstant.ENABLE);
        boolean enableSS = Objects.equals(businessBookOnline.getSmartScheduleEnable(), CommonConstant.ENABLE);
        // The SS switch needs to be on the premise that the need address switch is
        // turned on
        return notSalon && needAddress && enableSS;
    }

    public boolean getCacdFlag(MoeBusinessBookOnline businessBookOnline, OBBusinessInfoDTO obBusinessInfoDTO) {
        // Mobile / Hybrid
        boolean notSalon = !Objects.equals(obBusinessInfoDTO.getBusinessMode(), SubscriptionConst.BUSINESS_TYPE_SALON);
        boolean needAddress = Objects.equals(businessBookOnline.getIsNeedAddress(), CommonConstant.ENABLE);
        boolean enableCACD = Objects.equals(businessBookOnline.getServiceAreaEnable(), CommonConstant.ENABLE);
        return notSalon && needAddress && enableCACD;
    }

    public Map<Long, Integer> getServiceDurationWithStaffList(
            Long companyId, OBTimeSlotDTO timeSlotDTO, Set<Integer> staffIdList) {
        Integer businessId = timeSlotDTO.getBusinessId();
        List<CustomizedServiceQueryCondition> conditions;

        if (timeSlotDTO.getCustomerId() != null && !CollectionUtils.isEmpty(timeSlotDTO.getPetServices())) {
            conditions = timeSlotDTO.getPetServices().entrySet().stream()
                    .flatMap(entry -> entry.getValue().stream().flatMap(serviceId -> staffIdList.stream()
                            .map(staffId -> CustomizedServiceQueryCondition.newBuilder()
                                    .setBusinessId(businessId)
                                    .setPetId(entry.getKey())
                                    .setServiceId(serviceId)
                                    .setStaffId(staffId)
                                    .build())))
                    .collect(Collectors.toList());
        } else {
            conditions = timeSlotDTO.getServiceIds().stream()
                    .flatMap(serviceId -> staffIdList.stream()
                            .map(staffId -> CustomizedServiceQueryCondition.newBuilder()
                                    .setBusinessId(businessId)
                                    .setServiceId(serviceId)
                                    .setStaffId(staffId)
                                    .build()))
                    .collect(Collectors.toList());
        }

        List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> serviceWithCustomizedInfos =
                serviceManagementService.batchGetCustomizedService(companyId, conditions);

        return serviceWithCustomizedInfos.stream()
                .collect(Collectors.toMap(
                        info -> info.getQueryCondition().getStaffId(),
                        info -> info.getCustomizedService().getDuration(),
                        Integer::sum));
    }

    public Integer getDefaultServiceDuration(OBTimeSlotDTO timeSlotDTO, List<MoeGroomingServiceDTO> services) {
        Integer allServiceDuration = 0;

        for (Integer serviceId : timeSlotDTO.getServiceIds()) {
            for (MoeGroomingServiceDTO itSrv : services) {
                if (itSrv.getId().equals(serviceId)) {
                    allServiceDuration += itSrv.getDuration();
                    break;
                }
            }
        }

        return allServiceDuration;
    }

    public Integer getServiceDuration(OBTimeSlotDTO timeSlotDTO, List<MoeGroomingServiceDTO> services) {
        Integer allServiceDuration = 0;
        if (timeSlotDTO.getCustomerId() != null && !CollectionUtils.isEmpty(timeSlotDTO.getPetServices())) {
            allServiceDuration = groomingServiceService.getServicesWithCustomize(
                    timeSlotDTO.getBusinessId(),
                    timeSlotDTO.getCustomerId(),
                    timeSlotDTO.getPetServices(),
                    timeSlotDTO.getServiceIds(),
                    services);
        } else {
            for (Integer serviceId : timeSlotDTO.getServiceIds()) {
                for (MoeGroomingServiceDTO itSrv : services) {
                    if (itSrv.getId().equals(serviceId)) {
                        allServiceDuration += itSrv.getDuration();
                        break;
                    }
                }
            }
        }
        return allServiceDuration;
    }

    public Set<Integer> getAvailableStaffIdByServiceNew(
            Integer businessId, Set<Integer> obAvailableStaffIdList, List<MoeGroomingServiceDTO> services) {

        var queryServiceIds = services.stream()
                .filter(dto -> ServiceEnum.TYPE_SERVICE.equals(dto.getType()))
                .filter(dto -> !CustomerContactEnum.IS_ALL_STAFF.equals(dto.getIsAllStaff()))
                .map(MoeGroomingServiceDTO::getId)
                .distinct()
                .toList();
        var staffServiceMap = groomingServiceService.getServiceStaffByServiceIds(businessId, queryServiceIds).stream()
                .collect(Collectors.groupingBy(MoeBookOnlineStaffService::getServiceId));

        List<Set<Integer>> serviceAvailableStaffList = new ArrayList<>();
        services.stream()
                // add on service 不需要员工 计算多个主服务的staff 交集
                .filter(dto -> ServiceEnum.TYPE_SERVICE.equals(dto.getType()))
                .forEach(service -> {
                    // get staff list: if is_all_staff is on, return all staff
                    if (CustomerContactEnum.IS_ALL_STAFF.equals(service.getIsAllStaff())) {
                        serviceAvailableStaffList.add(obAvailableStaffIdList);
                    } else {
                        List<MoeBookOnlineStaffService> staffServices =
                                staffServiceMap.getOrDefault(service.getId(), List.of());
                        if (!CollectionUtils.isEmpty(staffServices)) { // WARN:选择了某个服务，但是该服务无可用员工
                            Set<Integer> currentStaff = new HashSet<>();
                            staffServices.forEach(tmpStaffService -> {
                                if (obAvailableStaffIdList.contains(tmpStaffService.getStaffId())) {
                                    currentStaff.add(tmpStaffService.getStaffId());
                                }
                            });
                            serviceAvailableStaffList.add(currentStaff);
                        }
                    }
                });
        Set<Integer> availableStaffIdList =
                !serviceAvailableStaffList.isEmpty() ? serviceAvailableStaffList.get(0) : new HashSet<>();
        if (serviceAvailableStaffList.size() > 1) {
            for (Set<Integer> eachSet : serviceAvailableStaffList.subList(1, serviceAvailableStaffList.size())) {
                if (availableStaffIdList.retainAll(eachSet)) {
                    log.info("update availableStaffIds set. new set size = {}", availableStaffIdList.size());
                }
            }
        }
        // no available staff
        if (availableStaffIdList.isEmpty()) {
            log.info(
                    "OB 3.0 no staff available, ob available staff: {}, service available staff: {}",
                    obAvailableStaffIdList,
                    serviceAvailableStaffList);
            throw new CommonException(
                    ResponseCodeEnum.NO_AVAILABLE_STAFF_WHEN_SUBMIT, "please select another service.");
        }
        return availableStaffIdList;
    }

    /**
     * deprecated, use getTimeSlotListV2
     * @param timeSlotDTO
     * @return
     */
    @Deprecated
    @TimerMetrics(group = TimerGroup.SMART_SCHEDULE)
    public Map<String, OBAvailableTimeDto> getTimeSlotList(OBTimeSlotDTO timeSlotDTO) {

        if (timeSlotDTO.getDate() == null && ObjectUtils.isEmpty(timeSlotDTO.getDates())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Must provide date or dates");
        }

        if (timeSlotDTO.getDate() == null) {
            var firstDate = timeSlotDTO.getDates().stream()
                    .sorted()
                    .findFirst()
                    .map(LocalDate::toString)
                    .orElseThrow();
            timeSlotDTO.setDate(firstDate); // for backward compatible, date 之前是必填
            timeSlotDTO.setQueryEndOfTheMonth(false); // 如果指定了 dates，endOfTheMonth 失效
        }

        Map<String, OBAvailableTimeDto> resultYearMonthDayMap = new HashMap<>();
        Integer businessId = timeSlotDTO.getBusinessId();
        /*
         * 1 检查是否block
         * 2 检查business是否可用
         * 3 是否smart schedule / certain area certain day
         * 4 根据服务查询groomer， 计算可用时间
         */
        var companyId = companyHelper.mustGetCompanyId(businessId);
        if (clientService.validCustomerBlocked(
                timeSlotDTO.getObName(), companyId, businessId, timeSlotDTO.getCustomerId())) {
            return resultYearMonthDayMap;
        }

        MoeBusinessBookOnline businessBookOnline = businessService.getSettingInfoByBusinessId(businessId);

        validBookOnlineEnable(businessBookOnline);

        List<MoeGroomingServiceDTO> services =
                groomingServiceService.getServicesByServiceIds(businessId, timeSlotDTO.getServiceIds());

        // 查询available staff，需要过滤没打开showOnCalendar的staff
        var availableStaffList = getAvailableStaffList(businessId, services, timeSlotDTO.getStaffIdList());

        // 根据available time type决定查询方式：0-available time默认类型，走原来的方法，1-by
        // slot，新的查询方式，2-disable select time，返回空
        return switch (businessBookOnline.getAvailableTimeType()) {
            case OnlineBookingConst.AVAILABLE_TIME_TYPE_DISABLE -> resultYearMonthDayMap;
            case OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT -> getAvailableTimeBySlotCapacity(
                    timeSlotDTO, businessBookOnline, availableStaffList);
            default -> getAvailableTimeByWorkingHours(timeSlotDTO, businessBookOnline, availableStaffList, services);
        };
    }

    @Nonnull
    private List<MoeStaffDto> getAvailableStaffList(
            final Integer businessId, final List<MoeGroomingServiceDTO> services, @Nullable List<Integer> staffIdList) {
        var staffIdSet = staffIdList == null ? new HashSet<Integer>() : new HashSet<>(staffIdList);
        var obAvailableStaffList = getOBAvailableStaffList(staffIdSet, businessId);
        var obAvailableStaffIdList =
                obAvailableStaffList.stream().map(MoeStaffDto::getId).collect(Collectors.toSet());

        var availableStaffIds = getAvailableStaffIdByServiceNew(businessId, obAvailableStaffIdList, services);

        return obAvailableStaffList.stream()
                .filter(staff -> availableStaffIds.contains(staff.getId()))
                .toList();
    }

    private void fillObPetDataDTO(
            final Integer businessId, final List<MoeGroomingServiceDTO> services, OBTimeSlotDTO timeSlotDTO) {

        var staffIdSet = timeSlotDTO.getStaffIdList() == null
                ? new HashSet<Integer>()
                : new HashSet<>(timeSlotDTO.getStaffIdList());
        var obAvailableStaffList = getOBAvailableStaffList(staffIdSet, businessId);
        var obAvailableStaffIdList =
                obAvailableStaffList.stream().map(MoeStaffDto::getId).collect(Collectors.toSet());

        var queryServiceIds = services.stream()
                .filter(dto -> ServiceEnum.TYPE_SERVICE.equals(dto.getType()))
                .filter(dto -> !CustomerContactEnum.IS_ALL_STAFF.equals(dto.getIsAllStaff()))
                .map(MoeGroomingServiceDTO::getId)
                .distinct()
                .toList();
        var staffServiceMap = groomingServiceService.getServiceStaffByServiceIds(businessId, queryServiceIds).stream()
                .collect(Collectors.groupingBy(MoeBookOnlineStaffService::getServiceId));

        Map<Integer /* service id */, Set<Integer> /* staff id */> serviceStaffMap = new HashMap<>(8);
        // List<Set<Integer>> serviceAvailableStaffList = new ArrayList<>();
        services.stream()
                // add on service 不需要员工 计算多个主服务的staff 交集
                .filter(dto -> ServiceEnum.TYPE_SERVICE.equals(dto.getType()))
                .forEach(service -> {
                    // get staff list: if is_all_staff is on, return all staff
                    if (CustomerContactEnum.IS_ALL_STAFF.equals(service.getIsAllStaff())) {
                        // serviceAvailableStaffList.add(obAvailableStaffIdList);
                        serviceStaffMap.put(service.getId(), obAvailableStaffIdList);
                    } else {
                        List<MoeBookOnlineStaffService> staffServices =
                                staffServiceMap.getOrDefault(service.getId(), List.of());
                        if (CollectionUtils.isEmpty(staffServices)) {
                            return;
                        }
                        Set<Integer> currentStaff = staffServices.stream()
                                .map(MoeBookOnlineStaffService::getStaffId)
                                .filter(obAvailableStaffIdList::contains)
                                .collect(Collectors.toSet());
                        // serviceAvailableStaffList.add(currentStaff);
                        serviceStaffMap.put(service.getId(), currentStaff);
                    }
                });
        services.stream()
                .filter(dto -> ServiceEnum.TYPE_ADD_ONS.equals(dto.getType()))
                .forEach(service -> {
                    serviceStaffMap.put(service.getId(), obAvailableStaffIdList);
                });

        var petServiceMap = timeSlotDTO.getPetServices();

        timeSlotDTO.getPetParamList().forEach(petParam -> {
            List<Integer> petServiceIds = new ArrayList<>();
            if (petServiceMap.containsKey(petParam.getPetIndex())) {
                petServiceIds = petServiceMap.get(petParam.getPetIndex());
            } else if (petServiceMap.containsKey(petParam.getPetId())) {
                petServiceIds = petServiceMap.get(petParam.getPetId());
            }

            petParam.setServiceIds(petServiceIds);

            Map<Integer /* service id */, Set<Integer> /* staff id */> currentServiceStaffMap = petServiceIds.stream()
                    .collect(Collectors.toMap(
                            Function.identity(), key -> serviceStaffMap.getOrDefault(key, Set.of()), (a, b) -> a));
            petParam.setServiceStaffMap(currentServiceStaffMap);
        });

        var staffIds = serviceStaffMap.values().stream()
                .flatMap(Collection::stream)
                .distinct()
                .toList();
        timeSlotDTO.setStaffIdList(staffIds);

        var newStaffIdSet = new HashSet<>(staffIds);
        var newObAvailableStaffList = obAvailableStaffList.stream()
                .filter(staff -> newStaffIdSet.contains(staff.getId()))
                .toList();
        timeSlotDTO.setAvailableStaffList(newObAvailableStaffList);
    }

    @TimerMetrics(group = TimerGroup.SMART_SCHEDULE)
    public OBAvailableDateTimeDTO getTimeSlotListV2(OBTimeSlotDTO timeSlotDTO) {

        if (timeSlotDTO.getDate() == null && ObjectUtils.isEmpty(timeSlotDTO.getDates())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Must provide date or dates");
        }

        if (timeSlotDTO.getDate() == null) {
            var firstDate = timeSlotDTO.getDates().stream()
                    .sorted()
                    .findFirst()
                    .map(LocalDate::toString)
                    .orElseThrow();
            timeSlotDTO.setDate(firstDate); // for backward compatible, date 之前是必填
            timeSlotDTO.setQueryEndOfTheMonth(false); // 如果指定了 dates，endOfTheMonth 失效
        }

        Integer businessId = timeSlotDTO.getBusinessId();
        /*
         * 1 检查是否block
         * 2 检查business是否可用
         * 3 是否smart schedule / certain area certain day
         * 4 根据服务查询groomer， 计算可用时间
         */
        var obAvailableDateTimeDTO = new OBAvailableDateTimeDTO();
        obAvailableDateTimeDTO.setAvailableDateTimes(Map.of());
        var companyId = companyHelper.mustGetCompanyId(businessId);
        if (clientService.validCustomerBlocked(
                timeSlotDTO.getObName(), companyId, businessId, timeSlotDTO.getCustomerId())) {
            return obAvailableDateTimeDTO;
        }

        MoeBusinessBookOnline businessBookOnline = businessService.getSettingInfoByBusinessId(businessId);

        validBookOnlineEnable(businessBookOnline);

        List<MoeGroomingServiceDTO> services =
                groomingServiceService.getServicesByServiceIds(businessId, timeSlotDTO.getServiceIds());

        obAvailableDateTimeDTO = switch (businessBookOnline.getAvailableTimeType()) {
            case OnlineBookingConst.AVAILABLE_TIME_TYPE_DISABLE -> obAvailableDateTimeDTO;
            case OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT -> {
                fillObPetDataDTO(businessId, services, timeSlotDTO);
                var availableDateTimeResult = getAvailableTimeBySlotCapacityV2(timeSlotDTO, businessBookOnline);
                setAvailableDateTimeDTO(timeSlotDTO, availableDateTimeResult);
                yield availableDateTimeResult;
            }
            default -> {
                // 查询available staff，需要过滤没打开showOnCalendar的staff
                var availableStaffList = getAvailableStaffList(businessId, services, timeSlotDTO.getStaffIdList());
                var availableTimeByWorkingHours =
                        getAvailableTimeByWorkingHours(timeSlotDTO, businessBookOnline, availableStaffList, services);
                obAvailableDateTimeDTO.setAvailableDateTimes(availableTimeByWorkingHours);
                yield obAvailableDateTimeDTO;
            }};

        return obAvailableDateTimeDTO;
    }

    private static void setAvailableDateTimeDTO(
            final OBTimeSlotDTO timeSlotDTO, final OBAvailableDateTimeDTO obAvailableDateTimeDTO) {

        obAvailableDateTimeDTO.setStaffAvailableType(StaffAvailableTypeEnum.NO_AVAILABLE);
        if (CollectionUtils.isEmpty(obAvailableDateTimeDTO.getAllAvailableDateTimes())) {
            return;
        }

        var singleStaffTimes = Stream.of(
                        obAvailableDateTimeDTO.getSameStaffSingleAvailableDateTimes(),
                        obAvailableDateTimeDTO.getSameStaffSequenceAvailableDateTimes())
                .flatMap(Collection::stream)
                .toList();

        var selectedStaffIdSet = timeSlotDTO.getPetParamList().stream()
                .map(OBPetDataDTO::getStaffId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(selectedStaffIdSet)) {
            setDate(obAvailableDateTimeDTO, obAvailableDateTimeDTO.getAllAvailableDateTimes());
            obAvailableDateTimeDTO.setStaffAvailableType(StaffAvailableTypeEnum.NOT_SELECTED);
        } else if (selectedStaffIdSet.size() == 1) {
            var staffId = CollectionUtils.firstElement(selectedStaffIdSet);
            if (Objects.isNull(staffId)) {
                return;
            }
            var currentStaffTimes = singleStaffTimes.stream()
                    .filter(dto -> dto.getPetToStaffs().stream()
                            .map(PetToStaffDto::getStaffId)
                            .distinct()
                            .allMatch(staffId::equals))
                    .toList();
            var list = Stream.of(obAvailableDateTimeDTO.getSelectedStaffSingleAvailableDateTimes(), currentStaffTimes)
                    .flatMap(Collection::stream)
                    .toList();

            setDate(obAvailableDateTimeDTO, list);
            if (!CollectionUtils.isEmpty(list)) {
                obAvailableDateTimeDTO.setSelectedStaffSingleAvailableDateTimes(list);
                obAvailableDateTimeDTO.setStaffAvailableType(StaffAvailableTypeEnum.SELECTED_AVAILABLE);
            } else if (!CollectionUtils.isEmpty(singleStaffTimes)) {
                obAvailableDateTimeDTO.setStaffAvailableType(StaffAvailableTypeEnum.OTHER_AVAILABLE);
                obAvailableDateTimeDTO.setAvailableGroomerDateTimes(
                        getObAvailableGroomerDateTimeDTOS(singleStaffTimes, List.of()));
            }
        } else {
            setDate(obAvailableDateTimeDTO, obAvailableDateTimeDTO.getSelectedStaffSingleAvailableDateTimes());
            if (!CollectionUtils.isEmpty(obAvailableDateTimeDTO.getSelectedStaffSingleAvailableDateTimes())) {
                obAvailableDateTimeDTO.setStaffAvailableType(StaffAvailableTypeEnum.SELECTED_AVAILABLE);
            } else if (!CollectionUtils.isEmpty(singleStaffTimes)) {
                var staffIdSet = singleStaffTimes.stream()
                        .map(OBAvailableTimeDetailDTO::getPetToStaffs)
                        .flatMap(Collection::stream)
                        .map(PetToStaffDto::getStaffId)
                        .collect(Collectors.toSet());
                staffIdSet.retainAll(selectedStaffIdSet);
                if (staffIdSet.isEmpty()) {
                    obAvailableDateTimeDTO.setStaffAvailableType(StaffAvailableTypeEnum.OTHER_AVAILABLE);
                    obAvailableDateTimeDTO.setAvailableGroomerDateTimes(
                            getObAvailableGroomerDateTimeDTOS(singleStaffTimes, List.of()));
                } else {
                    obAvailableDateTimeDTO.setStaffAvailableType(StaffAvailableTypeEnum.SOME_AVAILABLE);
                    obAvailableDateTimeDTO.setAvailableGroomerDateTimes(
                            getObAvailableGroomerDateTimeDTOS(singleStaffTimes, staffIdSet));
                }
            }
        }
    }

    @Nonnull
    private static List<OBAvailableGroomerDateTimeDTO> getObAvailableGroomerDateTimeDTOS(
            final List<OBAvailableTimeDetailDTO> obAvailableTimeDetailDTOS, final Collection<Integer> filterStaffIds) {
        return obAvailableTimeDetailDTOS.stream()
                .map(dto -> {
                    var staffList = dto.getPetToStaffs().stream()
                            .map(PetToStaffDto::getStaffId)
                            .distinct()
                            .toList();
                    if (CollectionUtils.isEmpty(staffList) || staffList.size() != 1) {
                        return null;
                    }
                    var staffId = staffList.get(0);
                    if (!CollectionUtils.isEmpty(filterStaffIds) && !filterStaffIds.contains(staffId)) {
                        return null;
                    }
                    return new OBAvailableGroomerDateTimeDTO()
                            .setStaffId(staffId)
                            .setFirstAvailableDate(dto.getDate());
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        OBAvailableGroomerDateTimeDTO::getStaffId,
                        Function.identity(),
                        (a, b) -> LocalDate.parse(a.getFirstAvailableDate())
                                        .isBefore(LocalDate.parse(b.getFirstAvailableDate()))
                                ? a
                                : b))
                .values()
                .stream()
                .toList();
    }

    private static void setDate(
            final OBAvailableDateTimeDTO obAvailableDateTimeDTO, List<OBAvailableTimeDetailDTO> allAvailableDateTimes) {
        Map<String, Boolean[]> availableDates = new HashMap<>(32);
        Map<String, List<Integer>> am = new HashMap<>(32);
        Map<String, List<Integer>> pm = new HashMap<>(32);
        allAvailableDateTimes.forEach(dateTime -> {
            var date = dateTime.getDate();
            var startTime = dateTime.getStartTime();
            if (startTime < AM_PM_PIVOT) {
                am.computeIfAbsent(date, k -> new ArrayList<>(8)).add(startTime);
                availableDates.computeIfAbsent(date, k -> new Boolean[] {false, false})[0] = true;
            } else {
                pm.computeIfAbsent(date, k -> new ArrayList<>(8)).add(startTime);
                availableDates.computeIfAbsent(date, k -> new Boolean[] {false, false})[1] = true;
            }
        });

        // Sort AM times
        am.replaceAll((date, timeSet) -> new ArrayList<>(new TreeSet<>(timeSet)));
        // Sort PM times
        pm.replaceAll((date, timeSet) -> new ArrayList<>(new TreeSet<>(timeSet)));

        obAvailableDateTimeDTO.setAvailableDates(availableDates);
        obAvailableDateTimeDTO.setAm(am);
        obAvailableDateTimeDTO.setPm(pm);
    }

    /**
     * AvailabilityType: By working hours
     *
     * @param timeSlotDTO        Frontend parameters
     * @param businessBookOnline business book online setting
     * @param availableStaffList  available staff list
     * @param services           selected service detail
     * @return available time result
     */
    private Map<String, OBAvailableTimeDto> getAvailableTimeByWorkingHours(
            OBTimeSlotDTO timeSlotDTO,
            MoeBusinessBookOnline businessBookOnline,
            List<MoeStaffDto> availableStaffList,
            List<MoeGroomingServiceDTO> services) {
        Map<String, OBAvailableTimeDto> resultYearMonthDayMap = new HashMap<>();
        // get business time zone
        InfoIdParams infoIdParams = new InfoIdParams();
        infoIdParams.setInfoId(timeSlotDTO.getBusinessId());
        OBBusinessInfoDTO obBusinessInfoDTO = iBusinessBusinessClient.getBusinessInfoForOB(infoIdParams);
        // start date
        LocalDate startDate = getStartDate(timeSlotDTO);
        LocalDateTime nowDateTime = LocalDateTime.now(ZoneId.of(obBusinessInfoDTO.getTimezoneName()));
        LocalDate currentDate = nowDateTime.toLocalDate();
        // first start date
        LocalDate soonestDate = currentDate.plusDays(businessBookOnline.getBookingRangeStartOffset());
        // how far can be booked (add limit days param)
        int farthestDay = OBSettingUtil.calculateBookingRangeEndDays(businessBookOnline);
        LocalDate farthestDate = currentDate.plusDays(farthestDay);
        if (startDate.isBefore(soonestDate)) {
            startDate = soonestDate;
        }
        // start date is out of farthest day range
        if (startDate.isAfter(farthestDate)) {
            return resultYearMonthDayMap;
        }
        // some days are beyond the farthest range
        if (startDate.plusDays(farthestDay).isAfter(farthestDate)) {
            farthestDay = (int) startDate.until(farthestDate, ChronoUnit.DAYS);
        }

        // * 3 是否smart schedule: 先检查smart_schedule_enable ， 后检查is_need_address
        final boolean smartSchedulingFlag = getSmartScheduleFlag(businessBookOnline, obBusinessInfoDTO);

        // * 3.1 certain area certain day:
        // 先判断是否在area内(/business/staff/serviceArea/isInsideBatch)
        // + 1)、在area内，才计算每天的timePeriod
        // + 2)、不在area内，这天直接返回空timePeriod
        final boolean certainAreaCertainDayFlag = getCacdFlag(businessBookOnline, obBusinessInfoDTO);

        // get staff C端用户只会选择一个主服务，可能是多个Add on
        // sum all service's duration
        List<Integer> availableStaffIdList =
                availableStaffList.stream().map(MoeStaffDto::getId).toList();
        final Integer serviceDuration = getDefaultServiceDuration(timeSlotDTO, services);
        final Map<Long, Integer> serviceStaffDurationMap = getServiceDurationWithStaffList(
                businessBookOnline.getCompanyId(), timeSlotDTO, new HashSet<>(availableStaffIdList));
        Integer bufferTime = smartSchedulerService.getSmartScheduleBufferTime(
                businessBookOnline.getCompanyId().intValue(), timeSlotDTO.getBusinessId());
        // get all smart schedule result for all day
        log.info(
                "OB 3.0 the business: [{}], SS: [{}], CACD: [{}], serviceDuration: [{}], "
                        + "HowSoon: [{}], HowFar: [{}], MaxDist: [{}], MaxTime: [{}], BufferTime: [{}]",
                timeSlotDTO.getBusinessId(),
                smartSchedulingFlag,
                certainAreaCertainDayFlag,
                serviceStaffDurationMap,
                soonestDate,
                farthestDate,
                businessBookOnline.getMaxAvailableDist(),
                businessBookOnline.getMaxAvailableTime(),
                bufferTime);

        Map<Integer, MoeStaffDto> staffNamesMap = availableStaffList.stream()
                .collect(Collectors.toMap(MoeStaffDto::getId, Function.identity(), (a, b) -> a));

        var ssResults = new ArrayList<SmartScheduleResultDto>();

        var syncWithWorkingHourStaffIdList = getSyncWithWorkingHourStaffIdList(timeSlotDTO.getBusinessId());
        if (!ObjectUtils.isEmpty(timeSlotDTO.getDates())) {

            // 如果传了 dates 参数，并发查询指定 dates 的可用时间

            var futures = new ArrayList<CompletableFuture<SmartScheduleResultDto>>();

            var dates = timeSlotDTO.getDates().stream().distinct().sorted().toList();

            for (var date : dates) {
                SmartScheduleV2Request smartScheduleRequest = SmartScheduleV2Request.builder()
                        .addressLat(timeSlotDTO.getLat())
                        .addressLng(timeSlotDTO.getLng())
                        .addressZipcode(timeSlotDTO.getZipcode())
                        .count(1) // 只查一天
                        .date(date.toString())
                        .serviceDuration(serviceDuration)
                        .staffServiceDuration(serviceStaffDurationMap)
                        .farthestDay(1)
                        .staffIds(availableStaffIdList)
                        .checkCACD(certainAreaCertainDayFlag)
                        .disableSmartScheduling(!smartSchedulingFlag)
                        .isFromOB(true)
                        .queryOne(Optional.ofNullable(timeSlotDTO.getQueryOne())
                                .orElse(businessBookOnline
                                        .getShowOneAvailableTime()
                                        .equals(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_TRUE)))
                        .queryPerHalfDay(timeSlotDTO.getQueryPerHalfDay())
                        .queryEndOfTheMonth(false)
                        .petParamList(timeSlotDTO.getPetParamList())
                        .useVersion(businessBookOnline.getUseVersion())
                        .syncWithWorkingHourStaff(syncWithWorkingHourStaffIdList)
                        .queryLimitDays(timeSlotDTO.getQueryLimitDays())
                        .overLimitDaysSeconds(timeSlotDTO.getOverLimitDaysSeconds())
                        .bufferTime(bufferTime)
                        .filterGroomingId(timeSlotDTO.getFilterAppointmentId())
                        .queryCountPerStaff(timeSlotDTO.getQueryCountPerStaff())
                        .serviceIds(timeSlotDTO.getServiceIds())
                        .build();

                futures.add(CompletableFuture.supplyAsync(
                        () -> smartScheduleV2Service.smartScheduleV2(timeSlotDTO.getBusinessId(), smartScheduleRequest),
                        ThreadPool.getSubmitExecutor()));
            }

            futures.stream().map(CompletableFuture::join).forEach(ssResults::add);

        } else if (StringUtils.hasText(timeSlotDTO.getDate())) {

            SmartScheduleV2Request smartScheduleRequest = SmartScheduleV2Request.builder()
                    .addressLat(timeSlotDTO.getLat())
                    .addressLng(timeSlotDTO.getLng())
                    .addressZipcode(timeSlotDTO.getZipcode())
                    .count(timeSlotDTO.getCount())
                    .date(startDate.toString())
                    .serviceDuration(serviceDuration)
                    .staffServiceDuration(serviceStaffDurationMap)
                    .farthestDay(farthestDay)
                    .staffIds(availableStaffIdList)
                    .checkCACD(certainAreaCertainDayFlag)
                    .disableSmartScheduling(!smartSchedulingFlag)
                    .isFromOB(true)
                    .queryOne(Optional.ofNullable(timeSlotDTO.getQueryOne())
                            .orElse(businessBookOnline
                                    .getShowOneAvailableTime()
                                    .equals(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_TRUE)))
                    .queryPerHalfDay(timeSlotDTO.getQueryPerHalfDay())
                    .queryEndOfTheMonth(timeSlotDTO.getQueryEndOfTheMonth())
                    .petParamList(timeSlotDTO.getPetParamList())
                    .useVersion(businessBookOnline.getUseVersion())
                    .syncWithWorkingHourStaff(syncWithWorkingHourStaffIdList)
                    .queryLimitDays(timeSlotDTO.getQueryLimitDays())
                    .overLimitDaysSeconds(timeSlotDTO.getOverLimitDaysSeconds())
                    .bufferTime(bufferTime)
                    .filterGroomingId(timeSlotDTO.getFilterAppointmentId())
                    .queryCountPerStaff(timeSlotDTO.getQueryCountPerStaff())
                    .serviceIds(timeSlotDTO.getServiceIds())
                    .build();

            ssResults.add(smartScheduleV2Service.smartScheduleV2(timeSlotDTO.getBusinessId(), smartScheduleRequest));
        } else {
            throw bizException(Code.CODE_PARAMS_ERROR, "Must provide date or dates");
        }

        if (ssResults.stream().noneMatch(SmartScheduleResultDto::isAvailable)) {
            return resultYearMonthDayMap;
        }

        for (var scheduleResultDto : ssResults) {
            scheduleResultDto.getDayMap().forEach((workDay, smartScheduleStaffVOMap) -> {
                smartScheduleStaffVOMap.getStaffMap().forEach((staffIdKey, smartScheduleVO) -> {
                    // compute each staff value
                    OBAvailableTimeStaffDto staffListValue = new OBAvailableTimeStaffDto();
                    staffListValue.setId(smartScheduleVO.getStaffId());
                    staffListValue.setFirstName(
                            staffNamesMap.get(smartScheduleVO.getStaffId()).getFirstName());
                    staffListValue.setLastName(
                            staffNamesMap.get(smartScheduleVO.getStaffId()).getLastName());
                    // compute available time
                    OBAvailableTimeStaffAvailableTimeDto timeList = new OBAvailableTimeStaffAvailableTimeDto();
                    timeList.setAm(new ArrayList<>());
                    timeList.setPm(new ArrayList<>());
                    staffListValue.setAvailableTime(timeList);
                    staffListValue.setAvailableRange(smartScheduleVO.getAvailableRange());

                    List<TimeRangeDto> workingTimeRangeDtoList;
                    if (smartScheduleVO.getAvailableRange() != null) {
                        workingTimeRangeDtoList = convertSmartScheduleVO(smartScheduleVO);
                    } else {
                        workingTimeRangeDtoList = Collections.emptyList();
                    }

                    // 3. interval time appointmentInterval; totalDuration
                    generateAvailableTimePoint(
                            workingTimeRangeDtoList,
                            businessBookOnline.getTimeslotMins(),
                            serviceStaffDurationMap.getOrDefault(staffIdKey.longValue(), serviceDuration),
                            timeList,
                            nowDateTime,
                            workDay);
                    // 4. refresh am and pm tag when adding
                    OBAvailableTimeDto currentWorkDay = resultYearMonthDayMap.get(workDay);
                    if (currentWorkDay == null) {
                        currentWorkDay = new OBAvailableTimeDto();
                        currentWorkDay.setAvailable(new Boolean[] {false, false});
                        currentWorkDay.setStaffList(new ArrayList<>());
                        resultYearMonthDayMap.put(workDay, currentWorkDay);
                    }
                    // 5. filter out some time point according to fake_it config or only show 1
                    // available time
                    if (businessBookOnline
                            .getShowOneAvailableTime()
                            .equals(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_TRUE)) {
                        List<Integer> amList = timeList.getAm();
                        List<Integer> pmList = timeList.getPm();
                        if (amList.isEmpty()) {
                            // 上午不可以预约，下午取第一个
                            pmList.subList(!pmList.isEmpty() ? 1 : 0, pmList.size())
                                    .clear();
                        } else {
                            // 上有可以预约， 下午的清空
                            amList.subList(1, amList.size()).clear();
                            pmList.clear();
                        }
                    } else {
                        FakeItUtil.fakeItOut(timeList.getAm(), businessBookOnline.getFakeIt());
                        FakeItUtil.fakeItOut(timeList.getPm(), businessBookOnline.getFakeIt());
                    }
                    Boolean[] amPmTag = currentWorkDay.getAvailable();
                    if (!timeList.getAm().isEmpty()) {
                        amPmTag[0] = true;
                    }
                    if (!timeList.getPm().isEmpty()) {
                        amPmTag[1] = true;
                    }
                    List<OBAvailableTimeStaffDto> staffList = currentWorkDay.getStaffList();
                    staffList.add(staffListValue);
                });
            });
        }

        return resultYearMonthDayMap;
    }

    private static LocalDate getStartDate(OBTimeSlotDTO timeSlotDTO) {
        if (!ObjectUtils.isEmpty(timeSlotDTO.getDates())) {
            return timeSlotDTO.getDates().stream().sorted().findFirst().orElseThrow();
        } else if (StringUtils.hasText(timeSlotDTO.getDate())) {
            return LocalDate.parse(timeSlotDTO.getDate());
        } else {
            throw bizException(Code.CODE_PARAMS_ERROR, "Must provide date or dates");
        }
    }

    /**
     * deprecated, use getAvailableTimeBySlotCapacityV2
     *
     * by slot模式下获取满足以下条件的slot：
     * 1.在B端配置的slot范围
     * 2.slot设定的startTime的appt pets数量未达到capacity：只判断 startTime 和 slot startTime 一致的
     * appt; 同一个appt里包含多只pet累计pet数量，不同appt里包含同一个pet计算多次
     * 3.slot设定的startTime不包含在block中：block startTime <= slot startTime < block
     * endTime 在block的左闭右开区间内则过滤
     * 注：不考虑working hour，by slot允许book在非working hour的时间里
     *
     * @param timeSlotDTO
     * @param bookOnlineSettings
     * @param availableStaffList
     * @return
     */
    @Deprecated
    private Map<String, OBAvailableTimeDto> getAvailableTimeBySlotCapacity(
            OBTimeSlotDTO timeSlotDTO, MoeBusinessBookOnline bookOnlineSettings, List<MoeStaffDto> availableStaffList) {
        Integer businessId = timeSlotDTO.getBusinessId();
        var companyId = bookOnlineSettings.getCompanyId();

        Map<String, OBAvailableTimeDto> resultYearMonthDayMap = new LinkedHashMap<>();
        final OBAvailableTimeDto dayNoneAvailable = new OBAvailableTimeDto();
        dayNoneAvailable.setAvailable(new Boolean[] {false, false});
        dayNoneAvailable.setStaffList(new ArrayList<>());

        var timezoneName = getBusinessTimezoneName(businessId);

        LocalDate startDate = getStartDate(timeSlotDTO);

        LocalDate currentDate = LocalDate.now(ZoneId.of(timezoneName));

        List<String> needQueryDays = new LinkedList<>(); // can be empty

        int farthest = OBSettingUtil.calculateBookingRangeEndDays(bookOnlineSettings);

        if (!ObjectUtils.isEmpty(timeSlotDTO.getDates())) {

            var allDates = timeSlotDTO.getDates().stream()
                    .sorted()
                    .distinct()
                    .map(LocalDate::toString)
                    .toList();

            needQueryDays.addAll(allDates);

        } else if (StringUtils.hasText(timeSlotDTO.getDate())) {

            int soonest = bookOnlineSettings.getBookingRangeStartOffset();

            var soonestDate = currentDate.plusDays(soonest);
            var farthestDate = currentDate.plusDays(farthest);

            for (int i = 0; i < farthest; i++) {
                LocalDate tmpLocalDate = startDate.plusDays(i);
                if (tmpLocalDate.isBefore(soonestDate) || tmpLocalDate.isAfter(farthestDate)) {
                    resultYearMonthDayMap.put(tmpLocalDate.toString(), dayNoneAvailable);
                } else {
                    // 需要查询计算的日期集合
                    needQueryDays.add(tmpLocalDate.toString());
                }
            }

            if (needQueryDays.isEmpty()) {
                return Collections.emptyMap();
            }

        } else {
            throw bizException(Code.CODE_PARAMS_ERROR, "Must provide date or dates");
        }

        // 初始化
        needQueryDays.forEach(currentDateI -> {
            OBAvailableTimeDto tmpCurrentDayValue = new OBAvailableTimeDto();
            tmpCurrentDayValue.setAvailable(new Boolean[] {false, false});
            tmpCurrentDayValue.setStaffList(new ArrayList<>());
            resultYearMonthDayMap.put(currentDateI, tmpCurrentDayValue);
        });

        // 获取商家closed date
        List<ParsedCloseDate> allClosedDate = iBusinessClosedDateClient.getAllCloseDate(businessId);
        // 增加closed date的过滤
        needQueryDays.removeIf(date -> isInClosedDate(date, allClosedDate));

        var availableStaffIds =
                availableStaffList.stream().map(MoeStaffDto::getId).collect(Collectors.toSet());

        // 获取business的slot配置
        //        List<MoeBookOnlineStaffTime> staffTimeList = getStaffTimeByBusinessId(businessId);
        Map<Integer, BookOnlineStaffTimeDTO> staffTimeListMap =
                staffTimeSyncService.queryStaffTime(businessId, new ArrayList<>(availableStaffIds));

        // 从timeslot的配置解析出需要查询日期里的所有的timeslot，以date为key，每个staff的timeslot的map为值
        Map<String /* date */, Map<Integer /* staff id */, OneDayTimeslotsDTO>> timeslotMapByDate =
                new LinkedHashMap<>(needQueryDays.size());
        Set<Integer> queryStartTimes = new HashSet<>(); // 用来查询slot所在时间有没有appt
        int nowMinutes = DateUtil.getNowMinutes(timezoneName);
        for (String date : needQueryDays) {
            for (var staffTimeEntry : staffTimeListMap.entrySet()) {
                var staffId = staffTimeEntry.getKey();
                if (!availableStaffIds.contains(staffId)) {
                    continue;
                }
                // 以staffId为key，存放当天内的timeslot
                Map<Integer, OneDayTimeslotsDTO> staffTimeslotMap =
                        timeslotMapByDate.computeIfAbsent(date, k -> new HashMap<>());
                String dayOfWeek = WeekUtil.getKeyForWeekDay(date);

                Map<String, BookOnlineStaffTimeDTO.StaffSlot> slotsMap =
                        staffTimeEntry.getValue().getStaffSlots();

                // 如果没有勾选当天，则该staff当天无timeslot
                if (slotsMap.containsKey(dayOfWeek) && slotsMap.get(dayOfWeek).getIsSelected()) {
                    // convert dto
                    OneDayTimeslotsDTO timeslotDTO = new OneDayTimeslotsDTO();
                    timeslotDTO.setIsSelected(slotsMap.get(dayOfWeek).getIsSelected());
                    timeslotDTO.setTimeSlot(slotsMap.get(dayOfWeek).getTimeSlot().stream()
                            .map(timeSlot -> {
                                CapacityTimeslotDTO capacityTimeslotDTO = new CapacityTimeslotDTO();
                                capacityTimeslotDTO.setCapacity(timeSlot.getCapacity());
                                capacityTimeslotDTO.setStartTime(timeSlot.getStartTime());
                                // capacityTimeslotDTO.setLimitDto(timeSlot.getLimitDto());
                                capacityTimeslotDTO.setUsedAppointmentPetPairs(new HashSet<>());
                                capacityTimeslotDTO.setLimitGroups(timeSlot.getLimitGroups());
                                return capacityTimeslotDTO;
                            })
                            .toList());

                    // if today filter out of today time range
                    if (Objects.equals(currentDate.toString(), date)) {
                        timeslotDTO.setTimeSlot(timeslotDTO.getTimeSlot().stream()
                                .filter(slot -> slot.getStartTime() > nowMinutes)
                                .collect(Collectors.toList()));
                    }
                    staffTimeslotMap.put(staffId, timeslotDTO);
                    timeslotDTO.getTimeSlot().forEach(slot -> queryStartTimes.add(slot.getStartTime()));
                }
            }
        }

        // 本次预约所需的 slot capacity
        boolean isSlotCapacityByReservation = isSlotCapacityByReservation(companyId);
        int needSlotCount = getSlotCapacityToOccupy(
                isSlotCapacityByReservation, timeSlotDTO.getPetParamList().size());

        // 查询staff name
        Map<Integer, MoeStaffDto> staffNamesMap = availableStaffList.stream()
                .collect(Collectors.toMap(MoeStaffDto::getId, Function.identity(), (a, b) -> a));

        // 查询所选时间的appt pet数
        Collection<StaffTimeslotPetCountDTO> petCounts = moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                businessId, new HashSet<>(needQueryDays), null, availableStaffIds, null);
        Map<String /* date */, List<StaffTimeslotPetCountDTO>> petCountMap = new HashMap<>();
        for (StaffTimeslotPetCountDTO petCount : petCounts) {
            List<StaffTimeslotPetCountDTO> petCountList =
                    petCountMap.computeIfAbsent(petCount.getSlotDate(), p -> new ArrayList<>());
            petCountList.add(petCount);
        }
        // 查询所选时间的block
        List<StaffBlockInfoDTO> blocks =
                moeGroomingAppointmentService.queryStaffBlocksByDates(businessId, new HashSet<>(needQueryDays));
        Map<String, List<StaffBlockInfoDTO>> blocksMap = new HashMap<>();
        for (StaffBlockInfoDTO block : blocks) {
            List<StaffBlockInfoDTO> blockList = blocksMap.computeIfAbsent(block.getDate(), a -> new ArrayList<>());
            blockList.add(block);
        }

        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        if (OnlineBookingConst.VERSION_TWO == bookOnlineSettings.getUseVersion()) {
            // 获取给定日期与时间范围内的预约数据
            List<AppointmentPetIdDTO> petIdDTOList = moeGroomingAppointmentService.queryStaffAppointmentPetIdByTime(
                    businessId, new HashSet<>(needQueryDays), queryStartTimes);
            obPetLimitFilterDTO = obPetLimitService.generateWithCurrentAppointment(
                    companyId, businessId, timeSlotDTO.getPetParamList(), timeSlotDTO.getServiceIds());
            obPetLimitFilterDTO = obPetLimitService.fillWithExistingAppointment(obPetLimitFilterDTO, petIdDTOList);
        }
        OBPetLimitFilterDTO finalObPetLimitFilterDTO = obPetLimitFilterDTO;

        // query count days after the first available day
        AtomicBoolean firstAvailableDay = new AtomicBoolean(false);
        AtomicInteger queryCount = new AtomicInteger(0);
        AtomicInteger firstAvailableIndex = new AtomicInteger(-1);
        long start = System.currentTimeMillis();
        for (var en : timeslotMapByDate.entrySet()) {
            String date = en.getKey();
            // Over limit days seconds
            if (Objects.nonNull(timeSlotDTO.getQueryLimitDays())
                    && LocalDate.parse(date).isAfter(currentDate.plusDays(timeSlotDTO.getQueryLimitDays()))
                    && Objects.nonNull(timeSlotDTO.getOverLimitDaysSeconds())
                    && System.currentTimeMillis() - start > timeSlotDTO.getOverLimitDaysSeconds() * 1000) {
                log.error("OB 3.0 smart schedule over limit days and over 10 seconds");
                break;
            }
            if (BooleanUtils.isNotTrue(firstAvailableDay.get())) {
                firstAvailableIndex.incrementAndGet();
            }
            Map<Integer /* staff id */, OneDayTimeslotsDTO> staffTimeslotMap = en.getValue();
            // 检查过滤
            for (Map.Entry<Integer, OneDayTimeslotsDTO> entry : staffTimeslotMap.entrySet()) {
                Integer staffId = entry.getKey();
                OneDayTimeslotsDTO slotMap = entry.getValue();
                List<Integer> staffStartTimes = new ArrayList<>();
                slotMap.getTimeSlot().forEach(timeslot -> {
                    // block 检查
                    boolean isBlocked = false;
                    // 如果当前日期有 block，则检查 slot 是否在 block 中
                    if (blocksMap.containsKey(date)) {
                        for (StaffBlockInfoDTO block : blocksMap.get(date)) {
                            if (block.getStaffId().equals(staffId)
                                    && timeslot.getStartTime() >= block.getStartTime()
                                    && timeslot.getStartTime() < block.getEndTime()) {
                                isBlocked = true;
                                break;
                            }
                        }
                    }
                    if (!isBlocked) {
                        // capacity检查
                        int occupiedSlotCount = 0;
                        if (petCountMap.containsKey(date)) {
                            for (StaffTimeslotPetCountDTO petCount : petCountMap.get(date)) {
                                if (Objects.equals(petCount.getStaffId(), staffId)
                                        && Objects.equals(petCount.getSlotTime(), timeslot.getStartTime())) {
                                    occupiedSlotCount = getSlotCapacityToOccupy(
                                            isSlotCapacityByReservation,
                                            petCount.getPetCount(),
                                            petCount.getAppointmentCount());
                                    break;
                                }
                            }
                        }

                        if (timeslot.getCapacity() - occupiedSlotCount < needSlotCount) {
                            return;
                        }

                        // pet limit
                        if (OnlineBookingConst.VERSION_TWO == bookOnlineSettings.getUseVersion()) {
                            // boolean noMorePetQuantity = obPetLimitService.judgePetLimit(
                            //         finalObPetLimitFilterDTO,
                            //         staffId,
                            //         date,
                            //         timeslot.getLimitDto(),
                            //         timeslot.getStartTime());
                            var petLimitResult = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                                    finalObPetLimitFilterDTO,
                                    staffId,
                                    date,
                                    timeslot.getLimitGroups(),
                                    timeslot.getStartTime(),
                                    finalObPetLimitFilterDTO.getPetIndexSubList());
                            if (Boolean.FALSE.equals(petLimitResult.getIsAllPetsAvailable())) {
                                log.info(
                                        "[judgePetLimitAndGetPetRemainQuantity] pet limit, staffId: {}, date: {}, time: {}, result: {}",
                                        staffId,
                                        date,
                                        timeslot.getStartTime(),
                                        petLimitResult);
                                return;
                            }
                        }

                        staffStartTimes.add(timeslot.getStartTime());
                    }
                });

                // 如果当前 staff 有可预约的 slot，则将 slot 添加到返回结果中
                if (!CollectionUtils.isEmpty(staffStartTimes)) {
                    OBAvailableTimeDto currentWorkDay = resultYearMonthDayMap.get(date);
                    MoeStaffDto staffDto = staffNamesMap.get(staffId);
                    staffDto.setId(staffId);
                    buildAvailableTimeForSlot(currentWorkDay, staffStartTimes, staffDto);
                    // query the first available day
                    if (BooleanUtils.isNotTrue(firstAvailableDay.get())) {
                        firstAvailableDay.set(true);
                        // dynamic calculate count
                        if (BooleanUtils.isTrue(timeSlotDTO.getQueryEndOfTheMonth())) {
                            timeSlotDTO.setCount(SmartScheduleV2Service.getRemainCount(
                                    needQueryDays.get(0), firstAvailableIndex.get()));
                        }
                    }
                }
            }

            if (Objects.nonNull(timeSlotDTO.getQueryCountPerStaff()) && timeSlotDTO.getQueryCountPerStaff() > 0) {
                var needQueryStaffList = getNeedQueryStaffList(
                        availableStaffIds, resultYearMonthDayMap, timeSlotDTO.getQueryCountPerStaff());
                if (!needQueryStaffList.isEmpty()) {
                    continue;
                }
            }

            // 如果已经查询到第一个可预约的日期，并且查询到的日期数量等于 count，则结束查询
            if (BooleanUtils.isTrue(firstAvailableDay.get())
                    && Objects.nonNull(timeSlotDTO.getCount())
                    && queryCount.incrementAndGet() >= timeSlotDTO.getCount()) {
                break;
            }
        }

        return resultYearMonthDayMap.entrySet().stream()
                .filter(entry -> {
                    Boolean[] available = entry.getValue().getAvailable();
                    return BooleanUtils.isTrue(available[0])
                            || BooleanUtils.isTrue(available[1]) && !isInClosedDate(entry.getKey(), allClosedDate);
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // // remove first available day before time slot
        // boolean findFirst = false;
        //
        // var count = countDate(timeSlotDTO, farthest);
        //
        // // 从第一个可预约的日期开始，查询 count 个日期
        // Map<String, OBAvailableTimeDto> result = new HashMap<>();
        // for (Map.Entry<String, OBAvailableTimeDto> entry : resultYearMonthDayMap.entrySet()) {
        //     Boolean[] available = entry.getValue().getAvailable();
        //     if (BooleanUtils.isTrue(available[0]) || BooleanUtils.isTrue(available[1])) {
        //         findFirst = true;
        //     }
        //     if (findFirst) {
        //         // add count result
        //         if (!isInClosedDate(entry.getKey(), allClosedDate)) {
        //             result.put(entry.getKey(), entry.getValue());
        //         }
        //         if (--count == 0) {
        //             return result;
        //         }
        //     }
        // }
        //
        // return result;
    }

    public Map<String, OBAvailableTimeWithNonAvailableDTO> getAvailableTimeBySlotCapacityForBusiness(
            OBTimeSlotDTO timeSlotDTO, List<String> needQueryDays, Set<Integer> availableStaffIds) {
        if (CollectionUtils.isEmpty(needQueryDays) || CollectionUtils.isEmpty(availableStaffIds)) {
            return Map.of();
        }

        timeSlotDTO.setDates(needQueryDays.stream().map(LocalDate::parse).toList());

        var businessId = timeSlotDTO.getBusinessId();
        var businessBookOnline = businessService.getSettingInfoByBusinessId(businessId);

        return getAvailableTimeFullBySlot(timeSlotDTO, businessBookOnline, availableStaffIds, true)
                .getAvailableDateTimesWithNonAvailable();
    }

    private OBAvailableDateTimeDTO getAvailableTimeBySlotCapacityV2(
            OBTimeSlotDTO timeSlotDTO, MoeBusinessBookOnline bookOnlineSettings) {

        var availableStaffIds = new HashSet<>(timeSlotDTO.getStaffIdList());

        // 获取结果
        var availableTimeFullDTO =
                getAvailableTimeFullBySlot(timeSlotDTO, bookOnlineSettings, availableStaffIds, false);
        var resultYearMonthDayMap = availableTimeFullDTO.getAvailableDateTimes();
        if (CollectionUtils.isEmpty(resultYearMonthDayMap)) {
            return new OBAvailableDateTimeDTO();
        }

        var result = getStringOBAvailableTimeDtoMap(resultYearMonthDayMap);
        if (CollectionUtils.isEmpty(result)) {
            return new OBAvailableDateTimeDTO();
        }

        var petIndexList = timeSlotDTO.getPetParamList().stream()
                .map(OBPetDataDTO::getPetIndex)
                .toList();

        // case 1：单个 staff 有单个 slot 满足的日期及时间
        var singleAvailableTime = getSingleAvailableTime(result, petIndexList);

        // staff name
        Map<Integer, MoeStaffDto> staffNamesMap = timeSlotDTO.getAvailableStaffList().stream()
                .collect(Collectors.toMap(MoeStaffDto::getId, Function.identity(), (a, b) -> a));

        // only 1 pet
        if (petIndexList.size() == 1 && bookOnlineSettings.getBySlotShowOneAvailableTime()) {
            // 只展示每个 staff 每一天第一个可预约的时间
            singleAvailableTime = singleAvailableTime.stream()
                    .collect(Collectors.toMap(
                            dto -> dto.getPetToStaffs().get(0).getStaffId() + "_" + dto.getDate(),
                            Function.identity(),
                            (a, b) -> a.getStartTime() <= b.getStartTime() ? a : b))
                    .values()
                    .stream()
                    .toList();
        }

        // 适配 auto assign 单 staff 逻辑
        if (petIndexList.size() == 1
                || Objects.equals(
                        bookOnlineSettings.getBySlotTimeslotFormat(), BookOnlineDTO.TimeslotFormat.DATE_ONLY)) {
            var newResult = getNewResult(singleAvailableTime, staffNamesMap);

            return new OBAvailableDateTimeDTO()
                    .setAvailableDateTimes(newResult)
                    .setSelectedStaffSingleAvailableDateTimes(List.of())
                    .setSameStaffSingleAvailableDateTimes(singleAvailableTime)
                    .setSameStaffSequenceAvailableDateTimes(List.of())
                    .setAllAvailableDateTimes(singleAvailableTime);
        }

        // case 2：选择的 staff 有单个 slot 满足的日期及时间（过滤多个 staff 有相同 start time 的 slot）
        var selectedStaffSingleAvailableTime =
                getSelectedStaffSingleAvailableTime(result, timeSlotDTO.getPetParamList());

        // case 3：单个 staff 有连续 slot 排列的日期及时间（只展示第一个时间）（return sequenceAvailableTime)
        var sequenceStartTime = getSequenceStartTime(result, availableTimeFullDTO.getTimeslotMapByDate(), petIndexList);

        // case 4：不同 groomer 在同一 start time 有 pet 数量的空（过滤多个 staff 有相同 start time 的 slot）
        var sameStartTime = getSameStartTime(result, petIndexList);

        // 单个 staff 满足的日期及时间
        var newResult = getNewResult(
                Stream.of(singleAvailableTime, sequenceStartTime)
                        .flatMap(Collection::stream)
                        .distinct()
                        .toList(),
                staffNamesMap);

        // 所有可预约的日期及时间
        var allAvailable = Stream.of(
                        selectedStaffSingleAvailableTime, singleAvailableTime, sequenceStartTime, sameStartTime)
                .flatMap(Collection::stream)
                .distinct()
                .toList();

        return new OBAvailableDateTimeDTO()
                .setAvailableDateTimes(newResult)
                .setSelectedStaffSingleAvailableDateTimes(selectedStaffSingleAvailableTime)
                .setSameStaffSingleAvailableDateTimes(singleAvailableTime)
                .setSameStaffSequenceAvailableDateTimes(sequenceStartTime)
                .setAllAvailableDateTimes(allAvailable);
    }

    /**
     * by slot模式下获取满足以下条件的slot：
     * 1.在B端配置的slot范围
     * 2.slot设定的startTime的appt pets数量未达到capacity：只判断 startTime 和 slot startTime 一致的
     * appt; 同一个appt里包含多只pet累计pet数量，不同appt里包含同一个pet计算多次
     * 3.slot设定的startTime不包含在block中：block startTime <= slot startTime < block
     * endTime 在block的左闭右开区间内则过滤
     * 注：不考虑working hour，by slot允许book在非working hour的时间里
     */
    private OBAvailableTimeFullDTO getAvailableTimeFullBySlot(
            OBTimeSlotDTO timeSlotDTO,
            MoeBusinessBookOnline bookOnlineSettings,
            Set<Integer> availableStaffIds,
            boolean needAllCheck) {

        var businessId = bookOnlineSettings.getBusinessId();
        var timezoneName = getBusinessTimezoneName(businessId);

        LocalDate currentDate = LocalDate.now(ZoneId.of(timezoneName));
        var needQueryDays = getNeedQueryDays(timeSlotDTO, bookOnlineSettings, currentDate);

        if (needQueryDays.isEmpty()) {
            return new OBAvailableTimeFullDTO();
        }

        // 从timeslot的配置解析出需要查询日期里的所有的timeslot，以date为key，每个staff的timeslot的map为值
        Map<String /* date */, Map<Integer /* staff id */, OneDayTimeslotsDTO>> timeslotMapByDate = getTimeslotMap(
                bookOnlineSettings,
                availableStaffIds,
                needQueryDays,
                currentDate,
                DateUtil.getNowMinutes(timezoneName));
        if (CollectionUtils.isEmpty(timeslotMapByDate)) {
            return new OBAvailableTimeFullDTO();
        }

        return getResultYearMonthDayMapWithNonAvailableSlot(
                timeSlotDTO,
                availableStaffIds,
                timeslotMapByDate,
                needQueryDays,
                currentDate,
                bookOnlineSettings.getBusinessId(),
                bookOnlineSettings.getCompanyId(),
                needAllCheck);
    }

    private static void offsetFillSlot(
            final Map<String, Map<Integer, OneDayTimeslotsDTO>> timeslotMapByDate,
            final Collection<StaffTimeslotPetCountDTO> petCountDTOList) {

        if (CollectionUtils.isEmpty(timeslotMapByDate) || CollectionUtils.isEmpty(petCountDTOList)) {
            return;
        }

        processSmallDataSet(timeslotMapByDate, petCountDTOList);
    }

    private static void processSmallDataSet(
            final Map<String, Map<Integer, OneDayTimeslotsDTO>> timeslotMapByDate,
            final Collection<StaffTimeslotPetCountDTO> petCountDTOList) {

        petCountDTOList.stream()
                .sorted(Comparator.comparing(StaffTimeslotPetCountDTO::getSlotTime))
                .forEach(slotInfo -> {
                    // time slot
                    setAppointmentPetPairsAndResetSlotTime(
                            findMatchingSlotByDateTime(
                                    timeslotMapByDate,
                                    slotInfo.getSlotDate(),
                                    slotInfo.getStaffId(),
                                    slotInfo.getSlotTime()),
                            slotInfo);
                    // daily
                    setDailyAppointmentPetPairs(
                            findMatchingSlotByDate(timeslotMapByDate, slotInfo.getSlotDate(), slotInfo.getStaffId()),
                            slotInfo);
                });
    }

    private static void setAppointmentPetPairsAndResetSlotTime(
            final Optional<CapacityTimeslotDTO> timeslotMapByDate, final StaffTimeslotPetCountDTO slotInfo) {
        if (timeslotMapByDate.isEmpty()) {
            return;
        }
        var capacityTimeslotDTO = timeslotMapByDate.get();
        var usedPetIds = capacityTimeslotDTO.getUsedAppointmentPetPairs();
        if (CollectionUtils.isEmpty(usedPetIds)) {
            usedPetIds = new HashSet<>();
        }
        usedPetIds.addAll(slotInfo.getPetDetails().stream()
                .map(dto -> Pair.of(dto.getGroomingId(), dto.getPetId()))
                .toList());
        capacityTimeslotDTO.setUsedAppointmentPetPairs(usedPetIds);

        slotInfo.setSlotTime(capacityTimeslotDTO.getStartTime());
    }

    private static void setDailyAppointmentPetPairs(
            final Optional<CapacityTimeslotDTO> timeslotMapByDate, final StaffTimeslotPetCountDTO slotInfo) {
        if (timeslotMapByDate.isEmpty()) {
            return;
        }
        var capacityTimeslotDTO = timeslotMapByDate.get();
        var usedPetIds = capacityTimeslotDTO.getUsedAppointmentPetPairs();
        if (CollectionUtils.isEmpty(usedPetIds)) {
            usedPetIds = new HashSet<>();
        }
        usedPetIds.addAll(slotInfo.getPetDetails().stream()
                .map(dto -> Pair.of(dto.getGroomingId(), dto.getPetId()))
                .toList());
        capacityTimeslotDTO.setUsedAppointmentPetPairs(usedPetIds);

        // slotInfo.setSlotTime(capacityTimeslotDTO.getStartTime());
    }

    private static Optional<CapacityTimeslotDTO> findMatchingSlotByDateTime(
            Map<String, Map<Integer, OneDayTimeslotsDTO>> slotIndexMap, String date, long staffId, int startTime) {

        // 获取指定日期和员工的时间槽位列表
        Map<Integer, OneDayTimeslotsDTO> staffSlots = slotIndexMap.get(date);
        if (staffSlots == null) {
            return Optional.empty();
        }

        OneDayTimeslotsDTO timeSlot = staffSlots.get(Math.toIntExact(staffId));
        if (Objects.isNull(timeSlot)) {
            return Optional.empty();
        }

        // 查找包含指定开始时间的时间槽位
        return timeSlot.getTimeSlot().stream()
                .sorted(Comparator.comparing(CapacityTimeslotDTO::getStartTime))
                .filter(slot -> slot.contains(startTime))
                .findFirst();
    }

    private static Optional<CapacityTimeslotDTO> findMatchingSlotByDate(
            Map<String, Map<Integer, OneDayTimeslotsDTO>> slotIndexMap, String date, long staffId) {

        // 获取指定日期和员工的时间槽位列表
        Map<Integer, OneDayTimeslotsDTO> staffSlots = slotIndexMap.get(date);
        if (staffSlots == null) {
            return Optional.empty();
        }

        OneDayTimeslotsDTO timeSlot = staffSlots.get(Math.toIntExact(staffId));
        if (Objects.isNull(timeSlot)) {
            return Optional.empty();
        }

        // 查找包含指定开始时间的时间槽位
        return Optional.ofNullable(timeSlot.getDailyTimeSlot());
    }

    private OBAvailableTimeFullDTO getResultYearMonthDayMapWithNonAvailableSlot(
            final OBTimeSlotDTO timeSlotDTO,
            final Set<Integer> availableStaffIds,
            final Map<String, Map<Integer, OneDayTimeslotsDTO>> timeslotMapByDate,
            final List<String> needQueryDays,
            final LocalDate currentDate,
            final Integer businessId,
            final Long companyId,
            boolean needAllCheck) {

        // 初始化结果映射表
        Map<String, OBAvailableTimeDto> resultYearMonthDayMap = initializeResultMap(needQueryDays);

        // 异步获取所有必要的数据
        AsyncDataQueryResult asyncData = initializeAndWaitForAsyncData(
                timeSlotDTO, availableStaffIds, needQueryDays, businessId, companyId, timeslotMapByDate);

        // 处理时间槽数据并构建结果
        return processTimeslotsAndBuildResult(
                timeSlotDTO,
                availableStaffIds,
                timeslotMapByDate,
                needQueryDays,
                currentDate,
                resultYearMonthDayMap,
                asyncData,
                needAllCheck);
    }

    /**
     * 初始化结果映射表
     * 为每个需要查询的日期创建初始的可用时间DTO对象
     */
    private static Map<String, OBAvailableTimeDto> initializeResultMap(List<String> needQueryDays) {
        return needQueryDays.stream().collect(Collectors.toMap(Function.identity(), date -> new OBAvailableTimeDto()
                .setAvailable(new Boolean[] {false, false})
                .setStaffList(new ArrayList<>())));
    }

    /**
     * 异步数据查询结果封装类
     */
    private record AsyncDataQueryResult(
            Collection<StaffTimeslotPetCountDTO> petCountList,
            Map<String, List<StaffBlockInfoDTO>> blocksMap,
            OBPetLimitFilterDTO obPetLimitFilterDTO,
            boolean isSlotCapacityByReservation,
            int needSlotCount,
            List<Set<Integer>> petSubList,
            Set<Integer> petSet,
            Map<Long, Set<Integer>> staffPetsMap,
            Map<Pair<Long /* staff id */, Integer /* start time */>, Set<Integer> /* pet index */> staffTimePetsMap) {}

    /**
     * 初始化并等待所有异步数据查询完成
     * 使用CompletableFuture并行执行多个数据查询任务以提高性能
     */
    private AsyncDataQueryResult initializeAndWaitForAsyncData(
            OBTimeSlotDTO timeSlotDTO,
            Set<Integer> availableStaffIds,
            List<String> needQueryDays,
            Integer businessId,
            Long companyId,
            Map<String, Map<Integer, OneDayTimeslotsDTO>> timeslotMapByDate) {

        // 并行启动所有异步查询任务
        CompletableFuture<Map<String, List<StaffBlockInfoDTO>>> blocksFuture = CompletableFuture.supplyAsync(
                () -> queryStaffBlocksByDates(businessId, needQueryDays), smartScheduleExecutorService);

        CompletableFuture<Collection<StaffTimeslotPetCountDTO>> petCountFuture = CompletableFuture.supplyAsync(
                () -> queryStaffTimeslotPetCount(
                        businessId, needQueryDays, availableStaffIds, timeSlotDTO.getFilterAppointmentId()),
                smartScheduleExecutorService);

        var petParamList = timeSlotDTO.getPetParamList();
        CompletableFuture<OBPetLimitFilterDTO> initObPetLimitFilterFuture = CompletableFuture.supplyAsync(
                () -> obPetLimitService.generateWithCurrentAppointment(
                        companyId, businessId, petParamList, timeSlotDTO.getServiceIds()),
                smartScheduleExecutorService);

        CompletableFuture<OBPetLimitFilterDTO> petLimitFilterFuture = petCountFuture.thenCombineAsync(
                initObPetLimitFilterFuture,
                (petCountList, obPetLimitFilterDTO) -> {
                    // 填充时间槽偏移量数据
                    offsetFillSlot(timeslotMapByDate, petCountList);
                    return obPetLimitService.fillWithExistingAppointmentV2(obPetLimitFilterDTO, petCountList);
                },
                smartScheduleExecutorService);

        CompletableFuture<Boolean> slotCapacityFuture = CompletableFuture.supplyAsync(
                () -> isSlotCapacityByReservation(companyId), smartScheduleExecutorService);

        // 等待所有异步任务完成
        CompletableFuture.allOf(petCountFuture, blocksFuture, petLimitFilterFuture, slotCapacityFuture)
                .join();

        // 获取异步查询结果
        var petCountList = petCountFuture.join();
        var blocksMap = blocksFuture.join();
        var obPetLimitFilterDTO = petLimitFilterFuture.join();
        var isSlotCapacityByReservation = slotCapacityFuture.join();

        // 记录调试信息
        logAsyncQueryResults(blocksMap, obPetLimitFilterDTO);

        // 计算本次预约所需的时间槽容量
        int needSlotCount = getSlotCapacityToOccupy(isSlotCapacityByReservation, petParamList.size());
        var petSubList = obPetLimitFilterDTO.getPetIndexSubList();

        var petSet = petSubList.stream().flatMap(Collection::stream).collect(Collectors.toSet());

        Map<Long /* staff id */, Set<Integer> /* pet index */> staffPetsMap = new HashMap<>();
        Map<Pair<Long /* staff id */, Integer /* start time */>, Set<Integer> /* pet index */> staffTimePetsMap =
                new HashMap<>();
        availableStaffIds.forEach(staffId -> {
            petParamList.stream()
                    .filter(petParam -> {
                        if (CollectionUtils.isEmpty(petParam.getServiceStaffMap())) {
                            return true;
                        }
                        return petParam.getServiceStaffMap().values().stream()
                                .allMatch(staffIds -> staffIds.contains(staffId));
                    })
                    .forEach(petParam -> {
                        staffPetsMap
                                .computeIfAbsent(staffId.longValue(), k -> new HashSet<>())
                                .add(petParam.getPetIndex());
                        staffTimePetsMap
                                .computeIfAbsent(
                                        Pair.of(staffId.longValue(), petParam.getServiceStartTime()),
                                        k -> new HashSet<>())
                                .add(petParam.getPetIndex());
                    });
        });

        return new AsyncDataQueryResult(
                petCountList,
                blocksMap,
                obPetLimitFilterDTO,
                isSlotCapacityByReservation,
                needSlotCount,
                petSubList,
                petSet,
                staffPetsMap,
                staffTimePetsMap);
    }

    /**
     * 查询员工阻塞时间信息
     */
    private Map<String, List<StaffBlockInfoDTO>> queryStaffBlocksByDates(
            Integer businessId, List<String> needQueryDays) {
        return moeGroomingAppointmentService.queryStaffBlocksByDates(businessId, new HashSet<>(needQueryDays)).stream()
                .collect(Collectors.groupingBy(StaffBlockInfoDTO::getDate));
    }

    /**
     * 查询员工时间槽宠物数量统计
     */
    private Collection<StaffTimeslotPetCountDTO> queryStaffTimeslotPetCount(
            Integer businessId,
            List<String> needQueryDays,
            Set<Integer> availableStaffIds,
            Integer filterAppointmentId) {
        return moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                businessId, new HashSet<>(needQueryDays), Set.of(), availableStaffIds, filterAppointmentId);
    }

    /**
     * 记录异步查询结果的调试信息
     */
    private void logAsyncQueryResults(
            Map<String, List<StaffBlockInfoDTO>> blocksMap, OBPetLimitFilterDTO obPetLimitFilterDTO) {
        if (log.isDebugEnabled()) {
            log.debug("getAvailableTimeBySlotCapacityV2 blocksMap: {}", blocksMap);
            log.debug("getAvailableTimeBySlotCapacityV2 obPetLimitFilterDTO: {}", obPetLimitFilterDTO);
        }
    }

    /**
     * 处理时间槽数据并构建最终结果
     */
    private static OBAvailableTimeFullDTO processTimeslotsAndBuildResult(
            OBTimeSlotDTO timeSlotDTO,
            Set<Integer> availableStaffIds,
            Map<String, Map<Integer, OneDayTimeslotsDTO>> timeslotMapByDate,
            List<String> needQueryDays,
            LocalDate currentDate,
            Map<String, OBAvailableTimeDto> resultYearMonthDayMap,
            AsyncDataQueryResult asyncData,
            boolean needAllCheck) {

        // 初始化查询状态跟踪变量
        AtomicBoolean firstAvailableDay = new AtomicBoolean(false);
        AtomicInteger queryCount = new AtomicInteger(0);
        AtomicInteger firstAvailableIndex = new AtomicInteger(-1);
        long startTime = System.currentTimeMillis();

        // 遍历每个日期的时间槽数据进行处理
        for (var dateEntry : timeslotMapByDate.entrySet()) {
            String date = dateEntry.getKey();

            // 检查是否超过查询限制时间
            if (shouldStopProcessingDueToTimeLimit(timeSlotDTO, currentDate, date, startTime)) {
                log.error("OB 3.0 smart schedule over limit days and over 10 seconds");
                break;
            }

            // 更新第一个可用日期的索引
            if (BooleanUtils.isNotTrue(firstAvailableDay.get())) {
                firstAvailableIndex.incrementAndGet();
            }

            // 处理当前日期的时间槽数据
            boolean hasAvailableSlots = processDateTimeslots(
                    date,
                    dateEntry.getValue(),
                    resultYearMonthDayMap,
                    asyncData,
                    firstAvailableDay,
                    firstAvailableIndex,
                    timeSlotDTO,
                    needQueryDays,
                    needAllCheck);

            // 检查是否需要继续处理后续日期
            if (!shouldContinueProcessing(
                    timeSlotDTO,
                    availableStaffIds,
                    resultYearMonthDayMap,
                    firstAvailableDay,
                    queryCount,
                    hasAvailableSlots)) {
                break;
            }
        }

        // 构建最终结果并返回
        return buildFinalResult(resultYearMonthDayMap, asyncData.petCountList(), timeslotMapByDate);
    }

    /**
     * 检查是否因为时间限制需要停止处理
     */
    private static boolean shouldStopProcessingDueToTimeLimit(
            OBTimeSlotDTO timeSlotDTO, LocalDate currentDate, String date, long startTime) {
        return Objects.nonNull(timeSlotDTO.getQueryLimitDays())
                && LocalDate.parse(date).isAfter(currentDate.plusDays(timeSlotDTO.getQueryLimitDays()))
                && Objects.nonNull(timeSlotDTO.getOverLimitDaysSeconds())
                && System.currentTimeMillis() - startTime > timeSlotDTO.getOverLimitDaysSeconds() * 1000;
    }

    /**
     * 处理单个日期的时间槽数据
     * 返回该日期是否有可用的时间槽
     */
    private static boolean processDateTimeslots(
            String date,
            Map<Integer, OneDayTimeslotsDTO> staffTimeslotMap,
            Map<String, OBAvailableTimeDto> resultYearMonthDayMap,
            AsyncDataQueryResult asyncData,
            AtomicBoolean firstAvailableDay,
            AtomicInteger firstAvailableIndex,
            OBTimeSlotDTO timeSlotDTO,
            List<String> needQueryDays,
            boolean needAllCheck) {

        // 初始化员工时间槽相关的映射表
        Map<Integer, Map<Integer, Pair<Set<Integer>, List<TimeSlotType>>>> staffAllTimeMap = new LinkedHashMap<>();
        Map<Integer, Map<Integer, Integer>> staffStartTimeCapacityTimeMap = new LinkedHashMap<>();
        Map<Integer, Map<Integer, Integer>> staffStartTimeOccupiedTimeMap = new LinkedHashMap<>();

        boolean hasAvailableSlots = false;

        // 遍历每个员工的时间槽数据
        for (Map.Entry<Integer, OneDayTimeslotsDTO> staffEntry : staffTimeslotMap.entrySet()) {
            Integer staffId = staffEntry.getKey();
            OneDayTimeslotsDTO slotDTO = staffEntry.getValue();

            // 处理单个员工的时间槽
            StaffTimeslotProcessResult processResult = processStaffTimeslots(
                    staffId,
                    slotDTO,
                    date,
                    asyncData,
                    staffAllTimeMap,
                    staffStartTimeCapacityTimeMap,
                    staffStartTimeOccupiedTimeMap,
                    needAllCheck);

            if (processResult.hasAvailableSlots()) {
                hasAvailableSlots = true;
                // 更新第一个可用日期的状态
                updateFirstAvailableDayStatus(firstAvailableDay, firstAvailableIndex, timeSlotDTO, needQueryDays);
            }

            // 构建并添加员工可用时间信息到结果中
            buildAndAddStaffAvailableTime(
                    date,
                    staffId,
                    processResult,
                    resultYearMonthDayMap,
                    staffAllTimeMap,
                    staffStartTimeCapacityTimeMap,
                    staffStartTimeOccupiedTimeMap);
        }

        return hasAvailableSlots;
    }

    /**
     * 员工时间槽处理结果封装类
     */
    private record StaffTimeslotProcessResult(
            Map<Integer, PetAvailableDTO> staffStartTimeMap,
            PetAvailableDTO dailyAvailableDTO,
            boolean hasAvailableSlots) {}

    /**
     * 处理单个员工的时间槽数据
     */
    private static StaffTimeslotProcessResult processStaffTimeslots(
            Integer staffId,
            OneDayTimeslotsDTO slotDTO,
            String date,
            AsyncDataQueryResult asyncData,
            Map<Integer, Map<Integer, Pair<Set<Integer>, List<TimeSlotType>>>> staffAllTimeMap,
            Map<Integer, Map<Integer, Integer>> staffStartTimeCapacityTimeMap,
            Map<Integer, Map<Integer, Integer>> staffStartTimeOccupiedTimeMap,
            boolean needAllCheck) {

        // 初始化员工时间槽映射和日级别可用宠物信息
        Map<Integer, PetAvailableDTO> staffStartTimeMap = new HashMap<>(16);
        PetAvailableDTO dailyAvailableDTO = new PetAvailableDTO();
        dailyAvailableDTO.setIsAllPetsAvailable(true);
        dailyAvailableDTO.setPetAvailableSubList(asyncData.petSubList());

        // 初始化所有时间槽为空状态
        slotDTO.getTimeSlot().forEach(timeslot -> {
            var startTime = timeslot.getStartTime();
            staffAllTimeMap
                    .computeIfAbsent(staffId, k -> new HashMap<>())
                    .put(startTime, Pair.of(asyncData.petSet(), List.of(TimeSlotType.EMPTY)));
        });

        // 处理每个具体的时间槽
        if (needAllCheck) {
            // 检查日级别时间槽设置
            // checkDailySlotCapacityWhenAllCheck(slotDTO, date, staffId, asyncData, staffAllTimeMap,
            // dailyAvailableDTO);

            processIndividualTimeSlotsWhenAllCheck(
                    staffId,
                    slotDTO,
                    date,
                    asyncData,
                    staffStartTimeMap,
                    staffAllTimeMap,
                    staffStartTimeCapacityTimeMap,
                    staffStartTimeOccupiedTimeMap);
        } else {
            // 检查日级别时间槽设置
            List<Set<Integer>> canTakePetSubList =
                    checkDailySlotCapacity(slotDTO, date, staffId, asyncData, dailyAvailableDTO);

            if (CollectionUtils.isEmpty(canTakePetSubList)) {
                return new StaffTimeslotProcessResult(staffStartTimeMap, dailyAvailableDTO, false);
            }

            processIndividualTimeSlots(
                    staffId,
                    slotDTO,
                    date,
                    canTakePetSubList,
                    asyncData,
                    staffStartTimeMap,
                    staffAllTimeMap,
                    staffStartTimeCapacityTimeMap,
                    staffStartTimeOccupiedTimeMap);
        }

        boolean hasAvailableSlots = !staffStartTimeMap.isEmpty();
        return new StaffTimeslotProcessResult(staffStartTimeMap, dailyAvailableDTO, hasAvailableSlots);
    }

    /**
     * 检查日级别时间槽容量和限制
     */
    private static List<Set<Integer>> checkDailySlotCapacity(
            OneDayTimeslotsDTO slotDTO,
            String date,
            Integer staffId,
            AsyncDataQueryResult asyncData,
            PetAvailableDTO dailyAvailableDTO) {
        var dailyTimeSlot = slotDTO.getDailyTimeSlot();
        if (Objects.isNull(dailyTimeSlot)) {
            return new ArrayList<>(asyncData.petSubList());
        }

        // 计算已占用的时间槽数量
        var occupiedSlotCount = getOccupiedSlotCount(dailyTimeSlot, asyncData.isSlotCapacityByReservation());

        // 检查剩余容量是否足够
        var remainCapacity = dailyTimeSlot.getCapacity() - occupiedSlotCount;
        if (remainCapacity <= 0
                || (asyncData.isSlotCapacityByReservation() && remainCapacity < asyncData.needSlotCount())) {
            return List.of(); // 容量不足，返回空列表
        }

        var canTakePetIndexSet = asyncData.staffPetsMap().getOrDefault(staffId.longValue(), Set.of());

        // 根据剩余容量计算可预约的宠物列表
        List<Set<Integer>> canTakePetSubList = asyncData.petSubList().stream()
                .filter(subList -> asyncData.isSlotCapacityByReservation() || subList.size() <= remainCapacity)
                .filter(canTakePetIndexSet::containsAll)
                .toList();

        if (CollectionUtils.isEmpty(canTakePetSubList)) {
            return List.of();
        }

        // 应用宠物限制规则
        var petAvailableDTO = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                asyncData.obPetLimitFilterDTO(),
                staffId,
                date,
                dailyTimeSlot.getLimitGroups(),
                null,
                canTakePetSubList);

        // 更新日级别可用宠物信息
        dailyAvailableDTO.setIsAllPetsAvailable(petAvailableDTO.getIsAllPetsAvailable());
        dailyAvailableDTO.setPetAvailableSubList(petAvailableDTO.getPetAvailableSubList());

        return petAvailableDTO.getPetAvailableSubList();
    }

    /**
     * 检查日级别时间槽容量和限制
     */
    private static void checkDailySlotCapacityWhenAllCheck(
            OneDayTimeslotsDTO slotDTO,
            String date,
            Integer staffId,
            AsyncDataQueryResult asyncData,
            Map<Integer, Map<Integer, Pair<Set<Integer>, List<TimeSlotType>>>> staffAllTimeMap,
            PetAvailableDTO dailyAvailableDTO) {
        var dailyTimeSlot = slotDTO.getDailyTimeSlot();
        if (Objects.isNull(dailyTimeSlot)) {
            return;
        }

        // 计算已占用的时间槽数量
        var occupiedSlotCount = getOccupiedSlotCount(dailyTimeSlot, asyncData.isSlotCapacityByReservation());

        // 检查剩余容量是否足够
        var remainCapacity = dailyTimeSlot.getCapacity() - occupiedSlotCount;
        if (remainCapacity <= 0
                || (asyncData.isSlotCapacityByReservation() && remainCapacity < asyncData.needSlotCount())) {
            slotDTO.getTimeSlot().forEach(timeslot -> {
                var startTime = timeslot.getStartTime();
                staffAllTimeMap
                        .computeIfAbsent(staffId, k -> new HashMap<>())
                        .put(startTime, Pair.of(asyncData.petSet(), List.of(TimeSlotType.FULLY_BOOKED)));
            });
            return;
        }

        var canTakePetIndexSet = asyncData.staffPetsMap().getOrDefault(staffId.longValue(), Set.of());

        // 根据剩余容量计算可预约的宠物列表
        List<Set<Integer>> canTakePetSubList = asyncData.petSubList().stream()
                .filter(subList -> asyncData.isSlotCapacityByReservation() || subList.size() <= remainCapacity)
                .filter(canTakePetIndexSet::containsAll)
                .toList();

        // 应用宠物限制规则
        var petAvailableDTO = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                asyncData.obPetLimitFilterDTO(),
                staffId,
                date,
                dailyTimeSlot.getLimitGroups(),
                null,
                canTakePetSubList);

        // 更新日级别可用宠物信息
        dailyAvailableDTO.setIsAllPetsAvailable(petAvailableDTO.getIsAllPetsAvailable());
        dailyAvailableDTO.setPetAvailableSubList(petAvailableDTO.getPetAvailableSubList());

        // 如果没有可用的宠物，则更新时间槽状态
        if (CollectionUtils.isEmpty(petAvailableDTO.getPetAvailableSubList())) {
            slotDTO.getTimeSlot().forEach(timeslot -> {
                var startTime = timeslot.getStartTime();
                staffAllTimeMap
                        .computeIfAbsent(staffId, k -> new HashMap<>())
                        .put(startTime, Pair.of(asyncData.petSet(), List.of(TimeSlotType.FULLY_BOOKED)));
            });
        }
    }

    /**
     * 处理每个具体的时间槽
     */
    private static void processIndividualTimeSlots(
            Integer staffId,
            OneDayTimeslotsDTO slotDTO,
            String date,
            List<Set<Integer>> canTakePetSubList,
            AsyncDataQueryResult asyncData,
            Map<Integer, PetAvailableDTO> staffStartTimeMap,
            Map<Integer, Map<Integer, Pair<Set<Integer>, List<TimeSlotType>>>> staffAllTimeMap,
            Map<Integer, Map<Integer, Integer>> staffStartTimeCapacityTimeMap,
            Map<Integer, Map<Integer, Integer>> staffStartTimeOccupiedTimeMap) {

        // 按开始时间排序处理每个时间槽
        slotDTO.getTimeSlot().stream()
                .sorted(Comparator.comparingInt(CapacityTimeslotDTO::getStartTime))
                .forEach(timeslot -> {
                    var startTime = timeslot.getStartTime();

                    // 检查时间槽是否被阻塞
                    if (isTimeSlotBlocked(staffId, date, startTime, asyncData.blocksMap())) {
                        staffAllTimeMap
                                .computeIfAbsent(staffId, k -> new HashMap<>())
                                .put(startTime, Pair.of(asyncData.petSet(), List.of(TimeSlotType.IN_BLOCK)));
                        return;
                    }

                    // 检查时间槽容量和占用情况
                    var occupiedSlotCount = getOccupiedSlotCount(timeslot, asyncData.isSlotCapacityByReservation());
                    updateSlotCapacityMaps(
                            staffId,
                            startTime,
                            timeslot.getCapacity(),
                            occupiedSlotCount,
                            staffStartTimeCapacityTimeMap,
                            staffStartTimeOccupiedTimeMap);

                    // 标记部分预订状态
                    if (occupiedSlotCount > 0) {
                        staffAllTimeMap
                                .computeIfAbsent(staffId, k -> new HashMap<>())
                                .put(startTime, Pair.of(asyncData.petSet(), List.of(TimeSlotType.PARTIALLY_BOOKED)));
                    }

                    // 检查剩余容量是否足够
                    var remainCapacity = timeslot.getCapacity() - occupiedSlotCount;
                    if (remainCapacity <= 0
                            || (asyncData.isSlotCapacityByReservation()
                                    && remainCapacity < asyncData.needSlotCount())) {
                        staffAllTimeMap
                                .computeIfAbsent(staffId, k -> new HashMap<>())
                                .put(startTime, Pair.of(asyncData.petSet(), List.of(TimeSlotType.FULLY_BOOKED)));
                        return;
                    }

                    var canTakePetIndexSet = asyncData.staffPetsMap().getOrDefault(staffId.longValue(), Set.of());

                    // 根据剩余容量计算可预约的宠物列表
                    var currentCanTakePetSubList = canTakePetSubList.stream()
                            .filter(subList ->
                                    asyncData.isSlotCapacityByReservation() || subList.size() <= remainCapacity)
                            .filter(canTakePetIndexSet::containsAll)
                            .toList();

                    // 应用宠物限制并检查可用性
                    validateAndProcessPetLimits(
                            staffId,
                            date,
                            startTime,
                            currentCanTakePetSubList,
                            staffStartTimeMap,
                            staffAllTimeMap,
                            timeslot.getLimitGroups(),
                            asyncData.obPetLimitFilterDTO());
                });
    }

    /**
     * 处理每个具体的时间槽
     */
    private static void processIndividualTimeSlotsWhenAllCheck(
            Integer staffId,
            OneDayTimeslotsDTO slotDTO,
            String date,
            AsyncDataQueryResult asyncData,
            Map<Integer, PetAvailableDTO> staffStartTimeMap,
            Map<Integer, Map<Integer, Pair<Set<Integer>, List<TimeSlotType>>>> staffAllTimeMap,
            Map<Integer, Map<Integer, Integer>> staffStartTimeCapacityTimeMap,
            Map<Integer, Map<Integer, Integer>> staffStartTimeOccupiedTimeMap) {

        // 按开始时间排序处理每个时间槽
        slotDTO.getTimeSlot().stream()
                .sorted(Comparator.comparingInt(CapacityTimeslotDTO::getStartTime))
                .forEach(timeslot -> {
                    var startTime = timeslot.getStartTime();

                    // 检查时间槽是否被阻塞
                    if (isTimeSlotBlocked(staffId, date, startTime, asyncData.blocksMap())) {
                        staffAllTimeMap
                                .computeIfAbsent(staffId, k -> new HashMap<>())
                                .put(startTime, Pair.of(asyncData.petSet(), List.of(TimeSlotType.IN_BLOCK)));
                        return;
                    }

                    // 检查时间槽容量和占用情况
                    var occupiedSlotCount = getOccupiedSlotCount(timeslot, asyncData.isSlotCapacityByReservation());
                    updateSlotCapacityMaps(
                            staffId,
                            startTime,
                            timeslot.getCapacity(),
                            occupiedSlotCount,
                            staffStartTimeCapacityTimeMap,
                            staffStartTimeOccupiedTimeMap);

                    // 标记部分预订状态
                    if (occupiedSlotCount > 0) {
                        staffAllTimeMap
                                .computeIfAbsent(staffId, k -> new HashMap<>())
                                .put(startTime, Pair.of(asyncData.petSet(), List.of(TimeSlotType.PARTIALLY_BOOKED)));
                    }

                    // 检查剩余容量是否足够
                    var remainCapacity = timeslot.getCapacity() - occupiedSlotCount;
                    if (remainCapacity <= 0
                            || (asyncData.isSlotCapacityByReservation()
                                    && remainCapacity < asyncData.needSlotCount())) {
                        staffAllTimeMap
                                .computeIfAbsent(staffId, k -> new HashMap<>())
                                .put(startTime, Pair.of(asyncData.petSet(), List.of(TimeSlotType.FULLY_BOOKED)));
                    }

                    var canTakePetIndexSet = asyncData.staffPetsMap().getOrDefault(staffId.longValue(), Set.of());

                    // 根据剩余容量计算可预约的宠物列表
                    List<Set<Integer>> currentCanTakePetSubList = asyncData.petSubList().stream()
                            .filter(subList ->
                                    asyncData.isSlotCapacityByReservation() || subList.size() <= remainCapacity)
                            .filter(canTakePetIndexSet::containsAll)
                            .toList();

                    if (!CollectionUtils.isEmpty(asyncData.staffTimePetsMap())) {
                        var petIndexSet = asyncData.staffTimePetsMap().get(Pair.of(staffId.longValue(), startTime));
                        if (Objects.nonNull(petIndexSet)) {
                            currentCanTakePetSubList = List.of(petIndexSet);
                            if (remainCapacity < petIndexSet.size()) {
                                staffAllTimeMap
                                        .computeIfAbsent(staffId, k -> new HashMap<>())
                                        .put(
                                                startTime,
                                                Pair.of(asyncData.petSet(), List.of(TimeSlotType.FULLY_BOOKED)));
                            }
                        }
                    }

                    // 应用宠物限制并检查可用性
                    validateAndProcessPetLimits(
                            staffId,
                            date,
                            startTime,
                            currentCanTakePetSubList,
                            staffStartTimeMap,
                            staffAllTimeMap,
                            timeslot.getLimitGroups(),
                            asyncData.obPetLimitFilterDTO());
                });
    }

    /**
     * 检查时间槽是否被阻塞
     */
    private static boolean isTimeSlotBlocked(
            Integer staffId, String date, Integer startTime, Map<String, List<StaffBlockInfoDTO>> blocksMap) {
        return blocksMap.getOrDefault(date, List.of()).stream()
                .anyMatch(block -> block.getStaffId().equals(staffId)
                        && startTime >= block.getStartTime()
                        && startTime < block.getEndTime());
    }

    /**
     * 更新时间槽容量相关的映射表
     */
    private static void updateSlotCapacityMaps(
            Integer staffId,
            Integer startTime,
            Integer capacity,
            Integer occupiedCount,
            Map<Integer, Map<Integer, Integer>> staffStartTimeCapacityTimeMap,
            Map<Integer, Map<Integer, Integer>> staffStartTimeOccupiedTimeMap) {
        staffStartTimeCapacityTimeMap
                .computeIfAbsent(staffId, k -> new HashMap<>())
                .put(startTime, capacity);
        staffStartTimeOccupiedTimeMap
                .computeIfAbsent(staffId, k -> new HashMap<>())
                .put(startTime, occupiedCount);
    }

    /**
     * 验证并处理宠物限制
     */
    private static void validateAndProcessPetLimits(
            Integer staffId,
            String date,
            Integer startTime,
            List<Set<Integer>> currentCanTakePetSubList,
            Map<Integer, PetAvailableDTO> staffStartTimeMap,
            Map<Integer, Map<Integer, Pair<Set<Integer>, List<TimeSlotType>>>> staffAllTimeMap,
            List<LimitGroupDTO> limitGroup,
            OBPetLimitFilterDTO obPetLimitFilterDTO) {

        // 应用宠物限制规则
        var petAvailableDTO = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroup, startTime, currentCanTakePetSubList);

        // 如果没有可用的宠物，则更新时间槽状态
        if (CollectionUtils.isEmpty(petAvailableDTO.getPetAvailableSubList())) {
            petAvailableDTO.getPetSubListToTimeSlotTypeMap().forEach((key, value) -> staffAllTimeMap
                    .computeIfAbsent(staffId, k -> new HashMap<>())
                    .merge(startTime, Pair.of(key, value), (oldValue, newValue) -> {
                        if (CollectionUtils.isEmpty(oldValue.value())) {
                            return newValue;
                        }
                        return Pair.of(
                                newValue.key(),
                                Stream.of(oldValue.value(), newValue.value())
                                        .flatMap(Collection::stream)
                                        .distinct()
                                        .toList());
                    }));
            return;
        }

        // 时间槽可用，添加到可用时间映射中
        staffStartTimeMap.put(startTime, petAvailableDTO);
    }

    /**
     * 构建并添加员工可用时间信息到结果中
     */
    private static void buildAndAddStaffAvailableTime(
            String date,
            Integer staffId,
            StaffTimeslotProcessResult processResult,
            Map<String, OBAvailableTimeDto> resultYearMonthDayMap,
            Map<Integer, Map<Integer, Pair<Set<Integer>, List<TimeSlotType>>>> staffAllTimeMap,
            Map<Integer, Map<Integer, Integer>> staffStartTimeCapacityTimeMap,
            Map<Integer, Map<Integer, Integer>> staffStartTimeOccupiedTimeMap) {

        // 获取当前日期的工作日信息
        OBAvailableTimeDto currentWorkDay = resultYearMonthDayMap.get(date);
        if (currentWorkDay.getAvailable() == null) {
            currentWorkDay.setAvailable(new Boolean[] {false, false});
        }
        if (currentWorkDay.getStaffList() == null) {
            currentWorkDay.setStaffList(new ArrayList<>());
        }

        // 构建员工可用时间DTO
        OBAvailableTimeStaffDto availableTimeStaffDto = buildStaffAvailableTimeDto(
                staffId, processResult, staffAllTimeMap, staffStartTimeCapacityTimeMap, staffStartTimeOccupiedTimeMap);

        // 更新上午/下午可用性标志
        updateAmPmAvailabilityFlags(currentWorkDay, availableTimeStaffDto.getAvailableTime());

        // 添加到当前工作日的员工列表中
        currentWorkDay.getStaffList().add(availableTimeStaffDto);
    }

    /**
     * 构建员工可用时间DTO
     */
    private static OBAvailableTimeStaffDto buildStaffAvailableTimeDto(
            Integer staffId,
            StaffTimeslotProcessResult processResult,
            Map<Integer, Map<Integer, Pair<Set<Integer>, List<TimeSlotType>>>> staffAllTimeMap,
            Map<Integer, Map<Integer, Integer>> staffStartTimeCapacityTimeMap,
            Map<Integer, Map<Integer, Integer>> staffStartTimeOccupiedTimeMap) {

        OBAvailableTimeStaffDto availableTimeStaffDto = new OBAvailableTimeStaffDto();
        availableTimeStaffDto.setId(staffId);

        // 构建上午/下午时间列表
        OBAvailableTimeStaffAvailableTimeDto timeList = buildAmPmTimeList(processResult.staffStartTimeMap());
        availableTimeStaffDto.setAvailableTime(timeList);

        // 设置时间到可用宠物的映射
        availableTimeStaffDto.setTimeToAvailablePet(new HashMap<>(processResult.staffStartTimeMap()));
        availableTimeStaffDto.setDailyAvailablePet(processResult.dailyAvailableDTO());

        // 构建时间槽详情
        var slotDetails = buildSlotDetails(
                staffId, staffAllTimeMap, staffStartTimeCapacityTimeMap, staffStartTimeOccupiedTimeMap);
        availableTimeStaffDto.setSlotDetails(slotDetails);

        return availableTimeStaffDto;
    }

    /**
     * 构建上午/下午时间列表
     */
    private static OBAvailableTimeStaffAvailableTimeDto buildAmPmTimeList(
            Map<Integer, PetAvailableDTO> staffStartTimeMap) {
        OBAvailableTimeStaffAvailableTimeDto timeList = new OBAvailableTimeStaffAvailableTimeDto();
        timeList.setAm(new ArrayList<>());
        timeList.setPm(new ArrayList<>());

        staffStartTimeMap.forEach((startTime, petAvailableDTO) -> {
            if (startTime >= AM_PM_PIVOT) {
                timeList.getPm().add(startTime);
            } else {
                timeList.getAm().add(startTime);
            }
        });

        return timeList;
    }

    /**
     * 构建时间槽详情列表
     */
    private static List<OBAvailableTimeSlotDetailDTO> buildSlotDetails(
            Integer staffId,
            Map<Integer, Map<Integer, Pair<Set<Integer>, List<TimeSlotType>>>> staffAllTimeMap,
            Map<Integer, Map<Integer, Integer>> staffStartTimeCapacityTimeMap,
            Map<Integer, Map<Integer, Integer>> staffStartTimeOccupiedTimeMap) {

        return staffAllTimeMap.getOrDefault(staffId, Map.of()).entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, currentEntry -> {
                    var time = currentEntry.getKey();
                    var timeSlotTypes = currentEntry.getValue().value();
                    TimeSlotType timeSlotType =
                            CollectionUtils.isEmpty(timeSlotTypes) ? TimeSlotType.EMPTY : timeSlotTypes.get(0);
                    return new OBAvailableTimeSlotDetailDTO()
                            .setTime(time)
                            .setTimeSlotType(timeSlotType)
                            .setTimeSlotTypes(timeSlotTypes)
                            .setCapacity(staffStartTimeCapacityTimeMap
                                    .getOrDefault(staffId, Map.of())
                                    .getOrDefault(time, 0))
                            .setOccupiedCount(staffStartTimeOccupiedTimeMap
                                    .getOrDefault(staffId, Map.of())
                                    .getOrDefault(time, 0));
                }))
                .values()
                .stream()
                .toList();
    }

    /**
     * 更新上午/下午可用性标志
     */
    private static void updateAmPmAvailabilityFlags(
            OBAvailableTimeDto currentWorkDay, OBAvailableTimeStaffAvailableTimeDto timeList) {
        Boolean[] amPmTag = currentWorkDay.getAvailable();
        if (!timeList.getAm().isEmpty()) {
            amPmTag[0] = true;
        }
        if (!timeList.getPm().isEmpty()) {
            amPmTag[1] = true;
        }
    }

    /**
     * 更新第一个可用日期的状态
     */
    private static void updateFirstAvailableDayStatus(
            AtomicBoolean firstAvailableDay,
            AtomicInteger firstAvailableIndex,
            OBTimeSlotDTO timeSlotDTO,
            List<String> needQueryDays) {
        if (BooleanUtils.isNotTrue(firstAvailableDay.get())) {
            firstAvailableDay.set(true);

            // 动态计算查询数量
            if (BooleanUtils.isTrue(timeSlotDTO.getQueryEndOfTheMonth())) {
                timeSlotDTO.setCount(
                        SmartScheduleV2Service.getRemainCount(needQueryDays.get(0), firstAvailableIndex.get()));
            }
        }
    }

    /**
     * 判断是否需要继续处理后续日期
     */
    private static boolean shouldContinueProcessing(
            OBTimeSlotDTO timeSlotDTO,
            Set<Integer> availableStaffIds,
            Map<String, OBAvailableTimeDto> resultYearMonthDayMap,
            AtomicBoolean firstAvailableDay,
            AtomicInteger queryCount,
            boolean hasAvailableSlots) {

        // 如果需要对每个员工查询满足计数的天数，则继续查询
        if (Objects.nonNull(timeSlotDTO.getQueryCountPerStaff()) && timeSlotDTO.getQueryCountPerStaff() > 0) {
            var needQueryStaffList = getNeedQueryStaffList(
                    availableStaffIds, resultYearMonthDayMap, timeSlotDTO.getQueryCountPerStaff());
            if (!needQueryStaffList.isEmpty()) {
                return true;
            }
        }

        // 如果已经查询到第一个可预约的日期，并且查询到的日期数量不小于 count，则结束查询
        var canStopProcessing = BooleanUtils.isTrue(firstAvailableDay.get())
                && Objects.nonNull(timeSlotDTO.getCount())
                && hasAvailableSlots
                && queryCount.incrementAndGet() >= timeSlotDTO.getCount();
        return !canStopProcessing;
    }

    /**
     * 构建最终返回结果
     */
    private static OBAvailableTimeFullDTO buildFinalResult(
            Map<String, OBAvailableTimeDto> resultYearMonthDayMap,
            Collection<StaffTimeslotPetCountDTO> petCountList,
            Map<String, Map<Integer, OneDayTimeslotsDTO>> timeslotMapByDate) {

        // 构建包含不可用时间槽信息的结果映射
        var availableDateTimesWithNonAvailable = resultYearMonthDayMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> new OBAvailableTimeWithNonAvailableDTO()
                        .setStaffList(entry.getValue().getStaffList().stream()
                                .map(OBClientTimeSlotService::buildDetail)
                                .toList())
                        .setSlotDetailList(petCountList.stream()
                                .filter(dto -> Objects.equals(dto.getSlotDate(), entry.getKey()))
                                .toList())));

        return new OBAvailableTimeFullDTO()
                .setAvailableDateTimesWithNonAvailable(availableDateTimesWithNonAvailable)
                .setAvailableDateTimes(resultYearMonthDayMap)
                .setTimeslotMapByDate(timeslotMapByDate);
    }

    private static OBAvailableTimeStaffDetailDTO buildDetail(OBAvailableTimeStaffDto dto) {
        return new OBAvailableTimeStaffDetailDTO().setStaffId(dto.getId()).setSlotDetails(dto.getSlotDetails());
    }

    private static int getOccupiedSlotCount(
            CapacityTimeslotDTO capacityTimeslotDTO, final boolean isSlotCapacityByReservation) {
        if (CollectionUtils.isEmpty(capacityTimeslotDTO.getUsedAppointmentPetPairs())) {
            return 0;
        }
        if (isSlotCapacityByReservation) {
            return (int) capacityTimeslotDTO.getUsedAppointmentPetPairs().stream()
                    .map(Pair::key)
                    .distinct()
                    .count();
        }
        return capacityTimeslotDTO.getUsedAppointmentPetPairs().size();
    }

    @Nonnull
    private static Map<String, OBAvailableTimeDto> getStringOBAvailableTimeDtoMap(
            final Map<String, OBAvailableTimeDto> resultYearMonthDayMap) {
        // 获取计数，如果为 null 则使用 Long.MAX_VALUE 作为无限制
        // Long count = Optional.ofNullable(countDateV2(timeSlotDTO, farthest)).orElse(Long.MAX_VALUE);

        return resultYearMonthDayMap.entrySet().stream()
                // 跳过直到找到第一个可用日
                .dropWhile(entry -> {
                    Boolean[] available = entry.getValue().getAvailable();
                    return BooleanUtils.isNotTrue(available[0]) && BooleanUtils.isNotTrue(available[1]);
                })
                // // 过滤掉关闭的日期
                // .filter(entry -> !isInClosedDate(entry.getKey(), allClosedDate))
                // 只有当 count 不为 null 且大于 0 时才限制数量
                // .limit(count)
                // 收集结果到 LinkedHashMap 以保持顺序
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v1, LinkedHashMap::new));
    }

    @Nonnull
    private static Map<String, OBAvailableTimeDto> getNewResult(
            final List<OBAvailableTimeDetailDTO> singleAvailableTime, final Map<Integer, MoeStaffDto> staffNamesMap) {
        return singleAvailableTime.stream()
                .collect(Collectors.groupingBy(OBAvailableTimeDetailDTO::getDate))
                .entrySet()
                .stream()
                .map(entry -> {
                    Boolean[] available = new Boolean[] {false, false};
                    List<OBAvailableTimeStaffDto> staffList = new ArrayList<>();

                    entry.getValue().forEach(dto -> {
                        List<Integer> am = new ArrayList<>();
                        List<Integer> pm = new ArrayList<>();
                        if (dto.getStartTime() < AM_PM_PIVOT) {
                            available[0] = true;
                            am.add(dto.getStartTime());
                        } else {
                            available[1] = true;
                            pm.add(dto.getStartTime());
                        }

                        var staffIdList = dto.getPetToStaffs().stream()
                                .map(PetToStaffDto::getStaffId)
                                .distinct()
                                .toList();
                        if (CollectionUtils.isEmpty(staffIdList) || staffIdList.size() > 1) {
                            return;
                        }
                        var staffId = staffIdList.get(0);
                        MoeStaffDto staffDto = staffNamesMap.getOrDefault(staffId, new MoeStaffDto());
                        OBAvailableTimeStaffDto staff = new OBAvailableTimeStaffDto();
                        staff.setId(staffId);
                        staff.setFirstName(staffDto.getFirstName());
                        staff.setLastName(staffDto.getLastName());
                        OBAvailableTimeStaffAvailableTimeDto availableTime = new OBAvailableTimeStaffAvailableTimeDto();
                        availableTime.setAm(am);
                        availableTime.setPm(pm);
                        staff.setAvailableTime(availableTime);
                        staffList.add(staff);
                    });

                    var obAvailableTimeDto = new OBAvailableTimeDto();
                    obAvailableTimeDto.setAvailable(available);
                    obAvailableTimeDto.setStaffList(staffList);
                    return new AbstractMap.SimpleEntry<>(entry.getKey(), obAvailableTimeDto);
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    @Nonnull
    private static List<OBAvailableTimeDetailDTO> getSameStartTime(
            final Map<String, OBAvailableTimeDto> result, final List<Integer> petIndexList) {
        var petSubList = generateNonEmptySubLists(petIndexList);

        List<OBAvailableTimeDetailDTO> sameStartTime = new ArrayList<>();
        result.forEach((date, availableTime) -> {
            var staffToAvailableMap = availableTime.getStaffList().stream()
                    .collect(Collectors.toMap(
                            OBAvailableTimeStaffDto::getId, OBAvailableTimeStaffDto::getTimeToAvailablePet));

            var commonStartTime = availableTime.getStaffList().stream()
                    .map(OBAvailableTimeStaffDto::getTimeToAvailablePet)
                    .filter(Objects::nonNull)
                    .map(Map::keySet)
                    .flatMap(Collection::stream)
                    .distinct()
                    .toList();

            if (CollectionUtils.isEmpty(commonStartTime)) {
                return;
            }

            for (final var startTime : commonStartTime) {
                var currentStaffToAvailableMap = staffToAvailableMap.entrySet().stream()
                        .filter(entry -> entry.getValue().containsKey(startTime))
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()
                                .getOrDefault(startTime, new PetAvailableDTO())
                                .getPetAvailableSubList()));

                var availableToStaffMap = currentStaffToAvailableMap.entrySet().stream()
                        // Step 1: Flatten the structure by creating pairs of (Integer key, each inner List<Integer>)
                        .flatMap(entry -> {
                            Integer key = entry.getKey();
                            List<Set<Integer>> nestedLists = entry.getValue();

                            // For each nested list, create a SimpleEntry with the original key
                            return nestedLists.stream().map(innerList -> new AbstractMap.SimpleEntry<>(key, innerList));
                        })
                        // Step 2: Group by the List<Integer> values
                        .collect(Collectors.groupingBy(
                                AbstractMap.SimpleEntry::getValue, // Group by the List<Integer>
                                Collectors.mapping(
                                        AbstractMap.SimpleEntry::getKey, // Extract the Integer key
                                        Collectors.toList() // Collect keys into List<Integer>
                                        )));

                petSubList.forEach(subList -> {
                    var currentPetList = new HashSet<>(petIndexList);
                    currentPetList.removeAll(subList);
                    if (CollectionUtils.isEmpty(subList) || CollectionUtils.isEmpty(currentPetList)) {
                        return;
                    }
                    if (!CollectionUtils.isEmpty(availableToStaffMap.get(subList))
                            && !CollectionUtils.isEmpty(availableToStaffMap.get(currentPetList))) {
                        // 两个 groomer 有相同的 start time 有 pet 数量的空
                        var staffIdFirst = availableToStaffMap.get(subList).stream()
                                .sorted()
                                .findFirst()
                                .orElse(null);
                        var staffIdSecond = availableToStaffMap.get(currentPetList).stream()
                                .filter(staffId -> !staffId.equals(staffIdFirst))
                                .sorted()
                                .findFirst()
                                .orElse(null);
                        if (Objects.nonNull(staffIdFirst) && Objects.nonNull(staffIdSecond)) {
                            var list = Stream.concat(
                                            subList.stream().map(petId -> new PetToStaffDto()
                                                    .setStaffId(staffIdFirst)
                                                    .setPetIndex(petId)
                                                    .setStartTime(startTime)),
                                            currentPetList.stream().map(petId -> new PetToStaffDto()
                                                    .setStaffId(staffIdSecond)
                                                    .setPetIndex(petId)
                                                    .setStartTime(startTime)))
                                    .toList();
                            var obAvailableTimeDetailDTO = new OBAvailableTimeDetailDTO()
                                    .setDate(date)
                                    .setStartTime(startTime)
                                    .setPetToStaffs(list);
                            sameStartTime.add(obAvailableTimeDetailDTO);
                        }
                    }
                });
            }
        });
        if (log.isDebugEnabled()) {
            log.debug("sameStartTime: {}", sameStartTime);
        }

        return sameStartTime.stream()
                .collect(Collectors.toMap(
                        dto -> dto.getPetToStaffs().stream()
                                .map(pet -> dto.getDate() + "_" + pet.getStaffId() + "_" + pet.getPetIndex() + "_"
                                        + pet.getStartTime())
                                .sorted()
                                .collect(Collectors.joining(",")),
                        Function.identity(),
                        (a, b) -> a))
                .values()
                .stream()
                .toList();
    }

    @Nonnull
    private static List<OBAvailableTimeDetailDTO> getSelectedStaffSingleAvailableTime(
            final Map<String, OBAvailableTimeDto> result, @NotEmpty List<OBPetDataDTO> petParamList) {
        List<OBAvailableTimeDetailDTO> selectedStaffSingleAvailable = new ArrayList<>();
        var hasSelectedStaff =
                petParamList.stream().map(OBPetDataDTO::getStaffId).anyMatch(Objects::nonNull);
        if (!hasSelectedStaff) {
            return selectedStaffSingleAvailable;
        }

        var petToStaffDtos = petParamList.stream()
                .map(param ->
                        new PetToStaffDto().setPetIndex(param.getPetIndex()).setStaffId(param.getStaffId()))
                .toList();
        // Map<Integer /* staff id */, Set<Integer /* pet index */>>
        var staffToPetIndexMap = petParamList.stream()
                .filter(param -> Objects.nonNull(param.getStaffId()))
                .collect(Collectors.groupingBy(
                        OBPetDataDTO::getStaffId, Collectors.mapping(OBPetDataDTO::getPetIndex, Collectors.toSet())));

        result.forEach((date, availableTime) -> {
            var staffToAvailableMap = availableTime.getStaffList().stream()
                    .collect(Collectors.toMap(
                            OBAvailableTimeStaffDto::getId, OBAvailableTimeStaffDto::getTimeToAvailablePet));

            var commonStartTime = availableTime.getStaffList().stream()
                    .map(OBAvailableTimeStaffDto::getTimeToAvailablePet)
                    .filter(Objects::nonNull)
                    .map(Map::keySet)
                    .flatMap(Collection::stream)
                    .distinct()
                    .toList();

            if (CollectionUtils.isEmpty(commonStartTime)) {
                return;
            }

            for (final var startTime : commonStartTime) {
                // Map<Integer /* staff id */, List<Set<Integer /* pet index */>>>
                var currentStaffToAvailableMap = staffToAvailableMap.entrySet().stream()
                        .filter(entry -> entry.getValue().containsKey(startTime))
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()
                                .getOrDefault(startTime, new PetAvailableDTO())
                                .getPetAvailableSubList()));

                var allMatch = staffToPetIndexMap.entrySet().stream()
                        .allMatch(entry -> currentStaffToAvailableMap.getOrDefault(entry.getKey(), List.of()).stream()
                                .anyMatch(list -> Objects.equals(list, entry.getValue())));

                if (allMatch) {
                    var newPetToStaffDtos = petToStaffDtos.stream()
                            .map(petToStaffDto -> {
                                var newPetToStaffDto = new PetToStaffDto();
                                newPetToStaffDto.setPetIndex(petToStaffDto.getPetIndex());
                                newPetToStaffDto.setStaffId(petToStaffDto.getStaffId());
                                newPetToStaffDto.setStartTime(startTime);
                                return newPetToStaffDto;
                            })
                            .toList();
                    var obAvailableTimeDetailDTO = new OBAvailableTimeDetailDTO()
                            .setDate(date)
                            .setStartTime(startTime)
                            .setPetToStaffs(newPetToStaffDtos);
                    selectedStaffSingleAvailable.add(obAvailableTimeDetailDTO);
                }
            }
        });

        if (log.isDebugEnabled()) {
            log.debug("selectedStaffSingleAvailable: {}", selectedStaffSingleAvailable);
        }
        return selectedStaffSingleAvailable;
    }

    @Nonnull
    private static List<OBAvailableTimeDetailDTO> getSequenceStartTime(
            final Map<String, OBAvailableTimeDto> result,
            final Map<String /* date */, Map<Integer /* staff id */, OneDayTimeslotsDTO>> timeslotMapByDate,
            final List<Integer> petIndexList) {

        // 计算每个 staff 时间段的前序时间
        Map<String /* date */, Map<Integer /* staff id */, Map<Integer /* next time */, Integer /* previous time */>>>
                timeslotSequenceMapByDate = getTimeslotSequenceMap(timeslotMapByDate);

        var petIndexSet = new HashSet<>(petIndexList);

        List<OBAvailableTimeDetailDTO> sequenceStartTime = new ArrayList<>();
        result.forEach((date, availableTime) -> availableTime.getStaffList().forEach(timeStaffDto -> {
            var dailyAvailablePet = timeStaffDto.getDailyAvailablePet();
            if (!dailyAvailablePet.getPetAvailableSubList().contains(petIndexSet)) {
                return;
            }

            var sequenceTimeMap =
                    timeslotSequenceMapByDate.getOrDefault(date, Map.of()).getOrDefault(timeStaffDto.getId(), Map.of());
            var startTimeSet = Stream.of(
                            timeStaffDto.getAvailableTime().getAm(),
                            timeStaffDto.getAvailableTime().getPm())
                    .flatMap(Collection::stream)
                    .collect(Collectors.toSet());

            var timeToAvailablePet = timeStaffDto.getTimeToAvailablePet();
            // sequenceAvailableTime 表示所有可以组成连续的两个 slot 列表，每一个组合表示一个对应的开始时间可以预约的 pet index 集合
            List<
                            Pair<
                                    Pair<Integer /* start time */, Set<Integer /* pet index */>>,
                                    Pair<Integer /* previous time */, Set<Integer /* pet index */>>>>
                    sequenceAvailableTime = startTimeSet.stream()
                            .sorted()
                            .map(startTime -> {
                                var previousTime = sequenceTimeMap.get(startTime);
                                if (previousTime == null) {
                                    return null;
                                }

                                var sequenceAvailableTimeResult = getSequenceAvailableTime(
                                        startTime, previousTime, timeToAvailablePet, petIndexSet);
                                if (Objects.isNull(sequenceAvailableTimeResult)) {
                                    return null;
                                }

                                // 如果 previous time 也是可以预约的，则返回
                                if (startTimeSet.contains(previousTime) && previousTime != -1) {
                                    return sequenceAvailableTimeResult;
                                }
                                return null;
                            })
                            .filter(Objects::nonNull)
                            .toList();
            if (CollectionUtils.isEmpty(sequenceAvailableTime)) {
                return;
            }

            var list = sequenceAvailableTime.stream()
                    .map(pr -> {
                        var previous = pr.key();
                        var current = pr.value();

                        var petToStaffs = new ArrayList<PetToStaffDto>();
                        previous.value().stream()
                                .map(petIndex -> new PetToStaffDto()
                                        .setStaffId(timeStaffDto.getId())
                                        .setPetIndex(petIndex)
                                        .setStartTime(previous.key()))
                                .forEach(petToStaffs::add);
                        current.value().stream()
                                .map(petIndex -> new PetToStaffDto()
                                        .setStaffId(timeStaffDto.getId())
                                        .setPetIndex(petIndex)
                                        .setStartTime(current.key()))
                                .forEach(petToStaffs::add);

                        var startTime = Math.min(previous.key(), current.key());

                        return new OBAvailableTimeDetailDTO()
                                .setDate(date)
                                .setStartTime(startTime)
                                .setPetToStaffs(petToStaffs);
                    })
                    .filter(Objects::nonNull)
                    .toList();
            sequenceStartTime.addAll(list);
        }));
        if (log.isDebugEnabled()) {
            log.debug("sequenceStartTime: {}", sequenceStartTime);
        }
        return sequenceStartTime;
    }

    @Nonnull
    private static List<OBAvailableTimeDetailDTO> getSingleAvailableTime(
            final Map<String, OBAvailableTimeDto> result, final List<Integer> petIndexList) {
        var petIndexSet = new HashSet<>(petIndexList);

        List<OBAvailableTimeDetailDTO> singleAvailableTime = new ArrayList<>();
        result.forEach((date, availableTime) -> availableTime
                .getStaffList()
                .forEach(timeStaffDto -> timeStaffDto.getTimeToAvailablePet().entrySet().stream()
                        .filter(entry -> {
                            var petAvailableDTO = entry.getValue();
                            return petAvailableDTO.getPetAvailableSubList().contains(petIndexSet);
                        })
                        .forEach(entry -> {
                            var petToStaffs = petIndexSet.stream()
                                    .map(petId -> new PetToStaffDto()
                                            .setStaffId(timeStaffDto.getId())
                                            .setPetIndex(petId)
                                            .setStartTime(entry.getKey()))
                                    .toList();

                            var obAvailableTimeDetailDTO = new OBAvailableTimeDetailDTO()
                                    .setDate(date)
                                    .setStartTime(entry.getKey())
                                    .setPetToStaffs(petToStaffs);
                            singleAvailableTime.add(obAvailableTimeDetailDTO);
                        })));
        if (log.isDebugEnabled()) {
            log.debug("singleAvailableTime: {}", singleAvailableTime);
        }
        return singleAvailableTime;
    }

    @Nonnull
    private static Map<String, Map<Integer, Map<Integer, Integer>>> getTimeslotSequenceMap(
            final Map<String, Map<Integer, OneDayTimeslotsDTO>> timeslotMapByDate) {
        return timeslotMapByDate.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, e2 -> {
                            var startTimeList = e2.getValue().getTimeSlot().stream()
                                    .map(CapacityTimeslotDTO::getStartTime)
                                    .sorted()
                                    .toList();
                            if (startTimeList.isEmpty()) {
                                return Map.of();
                            }
                            Map<Integer, Integer> result = IntStream.range(1, startTimeList.size())
                                    .boxed()
                                    .collect(Collectors.toMap(
                                            startTimeList::get, // 当前元素作为 key
                                            i -> startTimeList.get(i - 1), // 前一个元素作为 value
                                            Math::min));
                            result.put(startTimeList.get(0), -1); // 第一个 start time 的前序时间特别设置为 -1
                            return result;
                        }))));
    }

    private String getBusinessTimezoneName(final Integer businessId) {
        BusinessPreferenceDto businessPreference = iBusinessBusinessClient.getBusinessPreference(businessId);
        return businessPreference.getTimezoneName();
    }

    private Map<String /* date */, Map<Integer /* staff id */, OneDayTimeslotsDTO>> getTimeslotMap(
            final MoeBusinessBookOnline bookOnlineSettings,
            final Set<Integer> availableStaffIds,
            final List<String> needQueryDays,
            final LocalDate currentDate,
            final int nowMinutes) {

        var businessId = bookOnlineSettings.getBusinessId();
        var companyId = bookOnlineSettings.getCompanyId();

        if (BooleanEnum.VALUE_TRUE.equals(bookOnlineSettings.getAvailableTimeSync())) {
            return getTimeSlotMapWhenSyncEnable(
                    availableStaffIds, needQueryDays, currentDate, nowMinutes, businessId, companyId);
        }

        log.info("ob sync enabled: false");
        // 获取business的slot配置
        Map<Integer, BookOnlineStaffTimeDTO> staffTimeListMap =
                staffTimeSyncService.queryStaffTime(businessId, companyId, availableStaffIds, AvailabilityType.BY_SLOT);
        // 从timeslot的配置解析出需要查询日期里的所有的timeslot，以date为key，每个staff的timeslot的map为值
        Map<String /* date */, Map<Integer /* staff id */, OneDayTimeslotsDTO>> timeslotMapByDate =
                new LinkedHashMap<>(needQueryDays.size());
        for (String date : needQueryDays) {
            for (var staffTimeEntry : staffTimeListMap.entrySet()) {
                var staffId = staffTimeEntry.getKey();
                if (!availableStaffIds.contains(staffId)) {
                    continue;
                }
                String dayOfWeek = WeekUtil.getKeyForWeekDay(date);

                Map<String, BookOnlineStaffTimeDTO.StaffSlot> slotsMap =
                        staffTimeEntry.getValue().getStaffSlots();

                // 如果没有勾选当天，则该staff当天无timeslot
                if (slotsMap.containsKey(dayOfWeek)
                        && Boolean.TRUE.equals(slotsMap.get(dayOfWeek).getIsSelected())) {
                    // convert dto
                    OneDayTimeslotsDTO timeslotDTO = new OneDayTimeslotsDTO();
                    timeslotDTO.setIsSelected(slotsMap.get(dayOfWeek).getIsSelected());

                    AtomicInteger endTime = new AtomicInteger(1439);
                    var timeslotDTOList = slotsMap.get(dayOfWeek).getTimeSlot().stream()
                            .map(timeSlot -> {
                                CapacityTimeslotDTO capacityTimeslotDTO = new CapacityTimeslotDTO();
                                capacityTimeslotDTO.setCapacity(timeSlot.getCapacity());
                                capacityTimeslotDTO.setStartTime(timeSlot.getStartTime());
                                // capacityTimeslotDTO.setLimitDto(timeSlot.getLimitDto());
                                capacityTimeslotDTO.setUsedAppointmentPetPairs(new HashSet<>());
                                capacityTimeslotDTO.setLimitGroups(timeSlot.getLimitGroups());
                                return capacityTimeslotDTO;
                            })
                            .sorted(Comparator.comparing(CapacityTimeslotDTO::getStartTime)
                                    .reversed())
                            .peek(currentTimeslotDTO -> {
                                currentTimeslotDTO.setEndTime(endTime.get());
                                endTime.set(currentTimeslotDTO.getStartTime());
                            })
                            .toList();

                    timeslotDTO.setTimeSlot(timeslotDTOList);

                    Optional.ofNullable(slotsMap.get(dayOfWeek))
                            .map(BookOnlineStaffTimeDTO.StaffSlot::getDailyTimeSlot)
                            .map(timeSlot -> {
                                CapacityTimeslotDTO capacityTimeslotDTO = new CapacityTimeslotDTO();
                                capacityTimeslotDTO.setCapacity(timeSlot.getCapacity());
                                capacityTimeslotDTO.setStartTime(timeSlot.getStartTime());
                                // capacityTimeslotDTO.setLimitDto(timeSlot.getLimitDto());
                                capacityTimeslotDTO.setUsedAppointmentPetPairs(new HashSet<>());
                                capacityTimeslotDTO.setLimitGroups(timeSlot.getLimitGroups());
                                return capacityTimeslotDTO;
                            })
                            .ifPresent(timeslotDTO::setDailyTimeSlot);

                    // if today filter out of today time range
                    if (Objects.equals(currentDate.toString(), date)) {
                        timeslotDTO.setTimeSlot(timeslotDTO.getTimeSlot().stream()
                                .filter(slot -> slot.getStartTime() > nowMinutes)
                                .toList());
                    }

                    // 以staffId为key，存放当天内的timeslot
                    timeslotMapByDate
                            .computeIfAbsent(date, k -> new HashMap<>())
                            .put(staffId, timeslotDTO);
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("timeslotMapByDate: {}", timeslotMapByDate);
        }
        return timeslotMapByDate;
    }

    private Map<String, Map<Integer, OneDayTimeslotsDTO>> getTimeSlotMapWhenSyncEnable(
            final Set<Integer> availableStaffIds,
            final List<String> needQueryDays,
            final LocalDate currentDate,
            final int nowMinutes,
            final Integer businessId,
            final Long companyId) {
        log.info("ob sync enabled: true");
        var shiftManagementStaffTimeMap =
                staffTimeSyncService.queryShiftManagementStaffSlot(businessId, companyId, availableStaffIds);
        var shiftManagementOverrideStaffTime =
                staffTimeSyncService.queryShiftManagementOverrideStaffSlot(businessId, companyId, availableStaffIds);

        // 从timeslot的配置解析出需要查询日期里的所有的timeslot，以date为key，每个staff的timeslot的map为值
        Map<String /* date */, Map<Integer /* staff id */, OneDayTimeslotsDTO>> timeslotMapByDate =
                new LinkedHashMap<>(needQueryDays.size());

        needQueryDays.forEach(date -> {
            var localDate = LocalDate.parse(date);
            var staffMap = shiftManagementStaffTimeMap.entrySet().stream()
                    .map(entry -> shiftManagementOverrideStaffTime.getOrDefault(entry.getKey(), List.of()).stream()
                            .filter(override -> Objects.equals(override.getOverrideDate(), date))
                            .findFirst()
                            .orElseGet(() -> getStaffSlotTimeDataByCurrentDate(entry.getValue(), localDate)))
                    .collect(Collectors.toMap(
                            slotAvailabilityDay -> Math.toIntExact(slotAvailabilityDay.getStaffId()),
                            Function.identity()));

            staffMap.forEach((staffId, slotAvailabilityDay) -> {
                if (!availableStaffIds.contains(staffId) || !slotAvailabilityDay.getIsAvailable()) {
                    return;
                }

                AtomicInteger endTime = new AtomicInteger(1439);
                var timeslotDTOList = slotAvailabilityDay.getSlotHourSettingListList().stream()
                        .map(timeSlot -> {
                            CapacityTimeslotDTO capacityTimeslotDTO = new CapacityTimeslotDTO();
                            capacityTimeslotDTO.setCapacity(timeSlot.getCapacity());
                            capacityTimeslotDTO.setStartTime(timeSlot.getStartTime());
                            // var limitDto = new LimitDto();
                            // limitDto.setServiceLimitList(StaffTimeSyncUtil.convertProtoServiceLimitationToDto(
                            //         timeSlot.getLimit().getServiceLimitsList()));
                            // limitDto.setPetSizeLimitList(StaffTimeSyncUtil.convertProtoPetSizeLimitationToDto(
                            //         timeSlot.getLimit().getPetSizeLimitsList()));
                            // limitDto.setPetBreedLimitList(StaffTimeSyncUtil.convertProtoPetBreedLimitationToDto(
                            //         timeSlot.getLimit().getPetBreedLimitsList()));
                            capacityTimeslotDTO.setLimitGroups(StaffTimeSyncUtil.convertProtoLimitationToGroupDto(
                                    timeSlot.getLimitationGroupsList()));
                            // capacityTimeslotDTO.setLimitDto(limitDto);
                            capacityTimeslotDTO.setUsedAppointmentPetPairs(new HashSet<>());
                            return capacityTimeslotDTO;
                        })
                        .sorted(Comparator.comparing(CapacityTimeslotDTO::getStartTime)
                                .reversed())
                        .peek(currentTimeslotDTO -> {
                            currentTimeslotDTO.setEndTime(endTime.get());
                            endTime.set(currentTimeslotDTO.getStartTime());
                        })
                        .toList();

                var slotDailySetting = slotAvailabilityDay.getSlotDailySetting();

                // convert dto
                OneDayTimeslotsDTO timeslotDTO = new OneDayTimeslotsDTO();
                timeslotDTO.setIsSelected(slotAvailabilityDay.getIsAvailable());
                timeslotDTO.setTimeSlot(timeslotDTOList);
                timeslotDTO.setDailyTimeSlot(StaffTimeSyncUtil.convertDailySettingToDto(slotDailySetting));

                // if today filter out of today time range
                if (Objects.equals(currentDate.toString(), date)) {
                    timeslotDTO.setTimeSlot(timeslotDTO.getTimeSlot().stream()
                            .filter(slot -> slot.getStartTime() > nowMinutes)
                            .toList());
                }

                // 以staffId为key，存放当天内的timeslot
                timeslotMapByDate.computeIfAbsent(date, k -> new HashMap<>()).put(staffId, timeslotDTO);
            });
        });
        if (log.isDebugEnabled()) {
            log.debug("timeslotMapByDate: {}", timeslotMapByDate);
        }
        return timeslotMapByDate;
    }

    @Nonnull
    private List<String> getNeedQueryDays(
            final OBTimeSlotDTO timeSlotDTO,
            final MoeBusinessBookOnline bookOnlineSettings,
            final LocalDate currentDate) {
        List<String> needQueryDays = new LinkedList<>();

        if (!ObjectUtils.isEmpty(timeSlotDTO.getDates())) {
            needQueryDays.addAll(timeSlotDTO.getDates().stream()
                    .distinct()
                    .sorted()
                    .map(LocalDate::toString)
                    .toList());
        } else if (StringUtils.hasText(timeSlotDTO.getDate())) {
            int soonest = bookOnlineSettings.getBookingRangeStartOffset();
            int farthest = OBSettingUtil.calculateBookingRangeEndDays(bookOnlineSettings);

            var soonestDate = currentDate.plusDays(soonest);
            var farthestDate = currentDate.plusDays(farthest);

            LocalDate startDate = LocalDate.parse(timeSlotDTO.getDate());

            List<String> result = IntStream.range(0, farthest)
                    .mapToObj(startDate::plusDays)
                    .filter(tmpLocalDate -> !tmpLocalDate.isBefore(soonestDate) && !tmpLocalDate.isAfter(farthestDate))
                    .map(LocalDate::toString) // 直接转换为 String
                    .toList();

            needQueryDays.addAll(result);
        } else {
            throw bizException(Code.CODE_PARAMS_ERROR, "Must provide date or dates");
        }

        // 获取商家 closed date
        List<ParsedCloseDate> allClosedDate =
                iBusinessClosedDateClient.getAllCloseDate(bookOnlineSettings.getBusinessId());
        if (log.isDebugEnabled()) {
            log.debug("getAvailableTimeBySlotCapacityV2 allClosedDate: {}", allClosedDate);
        }

        // 过滤 closed date
        needQueryDays.removeIf(date -> isInClosedDate(date, allClosedDate));
        if (log.isDebugEnabled()) {
            log.debug("getAvailableTimeBySlotCapacityV2 needQueryDays: {}", needQueryDays);
        }

        return needQueryDays;
    }

    /**
     * 根据 startTime 和 previousTime，判断是否可以组成连续的两个 slot 来满足 booking request 的需求
     *
     * @param startTime          开始时间
     * @param previousTime       前一个开始时间
     * @param timeToAvailablePet 每个开始时间可以满足的 pet 数据
     * @param petIndexSet        booking request 的 pet 数据
     * @return Pair<previousTime, petIndexSet>, Pair<startTime, petIndexSet>
     */
    @Nullable
    private static Pair<Pair<Integer, Set<Integer>>, Pair<Integer, Set<Integer>>> getSequenceAvailableTime(
            final Integer startTime,
            final Integer previousTime,
            final Map<Integer, PetAvailableDTO> timeToAvailablePet,
            final HashSet<Integer> petIndexSet) {
        var petAvailableSubList1 = timeToAvailablePet
                .getOrDefault(startTime, new PetAvailableDTO())
                .getPetAvailableSubList();
        var petAvailableSubList2 = timeToAvailablePet
                .getOrDefault(previousTime, new PetAvailableDTO())
                .getPetAvailableSubList();
        if (Objects.isNull(petAvailableSubList1)) {
            petAvailableSubList1 = List.of();
        }
        if (Objects.isNull(petAvailableSubList2)) {
            petAvailableSubList2 = List.of();
        }
        final List<Set<Integer>> finalPetAvailableSubList1 = petAvailableSubList1;
        final List<Set<Integer>> finalPetAvailableSubList2 = petAvailableSubList2;
        for (final var set : finalPetAvailableSubList1) {
            var currentPetIndexSet = new HashSet<>(petIndexSet);
            currentPetIndexSet.removeAll(set);
            var anyMatch =
                    finalPetAvailableSubList2.stream().anyMatch(set2 -> Objects.equals(currentPetIndexSet, set2));
            if (anyMatch) {
                return Pair.of(Pair.of(previousTime, currentPetIndexSet), Pair.of(startTime, set));
            }
        }
        for (final var set : finalPetAvailableSubList2) {
            var currentPetIndexSet = new HashSet<>(petIndexSet);
            currentPetIndexSet.removeAll(set);
            var anyMatch =
                    finalPetAvailableSubList1.stream().anyMatch(set2 -> Objects.equals(currentPetIndexSet, set2));
            if (anyMatch) {
                return Pair.of(Pair.of(previousTime, set), Pair.of(startTime, currentPetIndexSet));
            }
        }
        return null;
    }

    @Nonnull
    private static List<Integer> getNeedQueryStaffList(
            final Collection<Integer> staffIds,
            final Map<String, OBAvailableTimeDto> resultYearMonthDayMap,
            Integer queryCountPerStaff) {
        var staffCountMap = resultYearMonthDayMap.values().stream()
                .map(OBAvailableTimeDto::getStaffList)
                .flatMap(Collection::stream)
                .filter(dto -> Objects.nonNull(dto.getAvailableTime())
                        && (!CollectionUtils.isEmpty(dto.getAvailableTime().getAm())
                                || !CollectionUtils.isEmpty(
                                        dto.getAvailableTime().getPm())))
                .collect(Collectors.groupingBy(OBAvailableTimeStaffDto::getId, Collectors.counting()));
        return staffIds.stream()
                .filter(staffId -> staffCountMap.getOrDefault(staffId, 0L) < queryCountPerStaff)
                .toList();
    }

    static SlotAvailabilityDay getStaffSlotTimeDataByCurrentDate(
            StaffAvailability availability, LocalDate currentLocalDate) {
        var slotAvailabilityDayList = availability.getSlotAvailabilityDayListList();
        LocalDate workingHourStartDate = LocalDate.parse(availability.getSlotStartSunday());
        var scheduleType =
                availability.getScheduleType().getNumber() > 0 ? availability.getScheduleType() : ScheduleType.ONE_WEEK;
        var dayOfWeek = currentLocalDate.getDayOfWeek();

        // 如果设置为每周相同时间，则直接按星期获取时间数据
        if (Objects.equals(scheduleType, ScheduleType.ONE_WEEK)) {
            return slotAvailabilityDayList.stream()
                    .filter(availabilityDay -> ScheduleType.ONE_WEEK.equals(availabilityDay.getScheduleType()))
                    .filter(availabilityDay -> Objects.equals(
                            dayOfWeek.getValue(), availabilityDay.getDayOfWeek().getNumber()))
                    .findFirst()
                    .orElse(null);
        }

        // 计算 date 距离 absoluteStartDate 的天数差
        long daysBetween = ChronoUnit.DAYS.between(workingHourStartDate, currentLocalDate);
        // 计算 date 是距离 absoluteStartDate 的星期差
        long weeksBetween = Math.floorDiv(daysBetween, 7);
        // 计算循环中第几周，1 是第一周，2 是第二周，以此类推
        int weekNumber = Math.toIntExact(Math.floorMod(weeksBetween, scheduleType.getNumber())) + 1;

        return slotAvailabilityDayList.stream()
                .filter(availabilityDay -> Objects.equals(
                        weekNumber, availabilityDay.getScheduleType().getNumber()))
                .filter(availabilityDay -> Objects.equals(
                        dayOfWeek.getValue(), availabilityDay.getDayOfWeek().getNumber()))
                .findFirst()
                .orElse(null);
    }

    private static int countDate(OBTimeSlotDTO timeSlotDTO, int farthest) {
        if (!ObjectUtils.isEmpty(timeSlotDTO.getDates())) {
            return timeSlotDTO.getDates().size();
        } else if (StringUtils.hasText(timeSlotDTO.getDate())) {
            return Objects.nonNull(timeSlotDTO.getCount()) ? timeSlotDTO.getCount() : farthest;
        } else {
            throw bizException(Code.CODE_PARAMS_ERROR, "Must provide date or dates");
        }
    }

    private boolean isSlotCapacityByReservation(long companyId) {
        return featureFlagApi.isOn(
                FeatureFlags.BOOK_BY_SLOT_WITH_RESERVATION,
                FeatureFlagContext.builder().company(companyId).build());
    }

    public static int getSlotCapacityToOccupy(boolean isSlotCapacityByReservation, int petCount) {
        return getSlotCapacityToOccupy(isSlotCapacityByReservation, petCount, 1);
    }

    public static int getSlotCapacityToOccupy(boolean isSlotCapacityByReservation, int petCount, int appointmentCount) {
        if (isSlotCapacityByReservation) {
            return appointmentCount;
        }
        return petCount;
    }

    public static boolean isInClosedDate(String currDay, List<ParsedCloseDate> ranges) {
        LocalDate curDate = LocalDate.parse(currDay);
        for (ParsedCloseDate range : ranges) {
            // [2020-05-22, 2020-05-27] 22到27的所有日期都是close day， currDay = 2020-05-24
            // close 条件： currDay >= range.start && currDay <= range.end
            if (!curDate.isBefore(range.getStart()) && !curDate.isAfter(range.getEnd())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 将staff当天的timeslot填充到返回对象中
     * OBAvailableTimeDto {
     * "available":[false, false],
     * "staffList":[OBAvailableTimeStaffDto{
     * "id":1,
     * "firstName":"",
     * "lastName":"",
     * "availableTime":OBAvailableTimeStaffAvailableTimeDto{
     * "am":[],
     * "pm":[]
     * }
     * }]
     * }
     *
     * @param currentWorkDay
     * @param startTimes
     * @param staffDto
     */
    private static void buildAvailableTimeForSlot(
            OBAvailableTimeDto currentWorkDay, List<Integer> startTimes, MoeStaffDto staffDto) {
        if (staffDto == null) {
            return;
        }
        if (currentWorkDay.getAvailable() == null) {
            currentWorkDay.setAvailable(new Boolean[] {false, false});
        }
        if (currentWorkDay.getStaffList() == null) {
            currentWorkDay.setStaffList(new ArrayList<>());
        }

        OBAvailableTimeStaffDto availableTimeStaffDto = new OBAvailableTimeStaffDto();
        availableTimeStaffDto.setId(staffDto.getId());
        availableTimeStaffDto.setFirstName(staffDto.getFirstName());
        availableTimeStaffDto.setLastName(staffDto.getLastName());
        OBAvailableTimeStaffAvailableTimeDto timeList = new OBAvailableTimeStaffAvailableTimeDto();
        timeList.setAm(new ArrayList<>());
        timeList.setPm(new ArrayList<>());
        for (Integer startTime : startTimes) {
            if (startTime >= AM_PM_PIVOT) {
                timeList.getPm().add(startTime);
            } else {
                timeList.getAm().add(startTime);
            }
        }
        availableTimeStaffDto.setAvailableTime(timeList);

        Boolean[] amPmTag = currentWorkDay.getAvailable();
        if (!timeList.getAm().isEmpty()) {
            amPmTag[0] = true;
        }
        if (!timeList.getPm().isEmpty()) {
            amPmTag[1] = true;
        }
        currentWorkDay.getStaffList().add(availableTimeStaffDto);
    }

    private static void buildAvailableTimeForSlotV2(
            OBAvailableTimeDto currentWorkDay,
            Map<Integer /* start time */, PetAvailableDTO /* pet available */> staffStartTimeMap,
            final PetAvailableDTO dailyAvailableDTO,
            final Integer staffId) {
        if (currentWorkDay.getAvailable() == null) {
            currentWorkDay.setAvailable(new Boolean[] {false, false});
        }
        if (currentWorkDay.getStaffList() == null) {
            currentWorkDay.setStaffList(new ArrayList<>());
        }

        OBAvailableTimeStaffDto availableTimeStaffDto = new OBAvailableTimeStaffDto();
        availableTimeStaffDto.setId(staffId);

        OBAvailableTimeStaffAvailableTimeDto timeList = new OBAvailableTimeStaffAvailableTimeDto();
        timeList.setAm(new ArrayList<>());
        timeList.setPm(new ArrayList<>());
        staffStartTimeMap.forEach((startTime, petAvailableDTO) -> {
            if (startTime >= AM_PM_PIVOT) {
                timeList.getPm().add(startTime);
            } else {
                timeList.getAm().add(startTime);
            }
        });
        availableTimeStaffDto.setAvailableTime(timeList);

        availableTimeStaffDto.setTimeToAvailablePet(new HashMap<>(staffStartTimeMap));
        availableTimeStaffDto.setDailyAvailablePet(dailyAvailableDTO);

        Boolean[] amPmTag = currentWorkDay.getAvailable();
        if (!timeList.getAm().isEmpty()) {
            amPmTag[0] = true;
        }
        if (!timeList.getPm().isEmpty()) {
            amPmTag[1] = true;
        }
        currentWorkDay.getStaffList().add(availableTimeStaffDto);
    }

    private List<TimeRangeDto> convertSmartScheduleVO(SmartScheduleVO smartScheduleVO) {
        List<TimeRangeDto> result = new ArrayList<>();
        smartScheduleVO.getAvailableRange().forEach(scheduleTimeSlot -> {
            TimeRangeDto currentRange = new TimeRangeDto();
            currentRange.setStartTime(scheduleTimeSlot.getStartTime());
            currentRange.setEndTime(scheduleTimeSlot.getEndTime());
            result.add(currentRange);
        });
        return result;
    }

    public static void generateAvailableTimePoint(
            List<TimeRangeDto> origin,
            int appointInterval,
            int totalDuration,
            OBAvailableTimeStaffAvailableTimeDto timeList,
            LocalDateTime nowDateTime,
            String workDay) {
        // filter before the current time at current day
        int nowMinutes = 0;
        LocalDate workDate = LocalDate.parse(workDay);
        if (nowDateTime.toLocalDate().isEqual(workDate)) {
            nowMinutes = DateUtil.getMinsByHourMins(nowDateTime.getHour(), nowDateTime.getMinute());
        }
        final int finalNowMinutes = nowMinutes;
        origin.stream()
                .filter(timeRange -> timeRange.getEndTime() > timeRange.getStartTime())
                .filter(timeRange -> (timeRange.getEndTime() - timeRange.getStartTime()) >= totalDuration)
                .forEach(timeRangeDto -> {
                    int start = timeRangeDto.getStartTime();
                    int end = timeRangeDto.getEndTime() - totalDuration;
                    for (int point = start; point <= end; point += appointInterval) {
                        if (point < finalNowMinutes) {
                            continue;
                        }
                        if (point >= AM_PM_PIVOT) {
                            timeList.getPm().add(point);
                        } else {
                            timeList.getAm().add(point);
                        }
                    }
                });
    }
}
