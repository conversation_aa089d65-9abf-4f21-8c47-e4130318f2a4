package com.moego.server.grooming.server;

import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.grooming.api.IBookOnlinePetLimitServiceBase;
import com.moego.server.grooming.dto.CompanyBusinessIdDTO;
import com.moego.server.grooming.service.BookOnlinePetLimitService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
public class BookOnlinePetLimitServer extends IBookOnlinePetLimitServiceBase {

    @Autowired
    private BookOnlinePetLimitService bookOnlinePetLimitService;

    @Autowired
    private IBusinessBusinessService iBusinessBusinessService;

    @Override
    public List<Long> getInUsedPetSizeIdList(Integer businessId) {
        return bookOnlinePetLimitService.getInUsedPetSizeIdList(businessId);
    }

    @Override
    public List<Long> getInUsedPetSizeIdListV2(CompanyBusinessIdDTO idDTO) {
        return bookOnlinePetLimitService.getInUsedPetSizeIdList(idDTO.businessId());
    }
}
