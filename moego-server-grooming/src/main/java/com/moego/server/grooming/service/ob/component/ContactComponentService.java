package com.moego.server.grooming.service.ob.component;

import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.web.vo.ob.component.BaseComponentVO;
import com.moego.server.grooming.web.vo.ob.component.ContactComponentVO;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service(value = LandingPageComponentEnum.COMPONENT_CONTACT)
@RequiredArgsConstructor
public class ContactComponentService implements ILandingPageComponentService {

    private final IBusinessBusinessService businessApi;

    @Override
    public BaseComponentVO getPageComponent(MoeBookOnlineLandingPageConfig landingPageConfig) {
        ContactComponentVO vo = new ContactComponentVO();
        InfoIdParams param = new InfoIdParams(landingPageConfig.getBusinessId());
        OBBusinessInfoDTO info = businessApi.getBusinessInfoForOB(param);
        vo.setPhone(
                Optional.ofNullable(info).map(OBBusinessInfoDTO::getPhoneNumber).orElse(null));
        return vo;
    }
}
