package com.moego.server.grooming.convert;

import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/8/23
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface PetDetailConverter {
    PetDetailConverter INSTANCE = Mappers.getMapper(PetDetailConverter.class);

    MoeGroomingPetDetail toEntity(com.moego.server.grooming.dto.GroomingPetDetailDTO dto);
}
