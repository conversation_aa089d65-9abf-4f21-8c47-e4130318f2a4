package com.moego.server.grooming.web.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/5
 */
@Data
@Accessors(chain = true)
public class ServiceDetailVO {

    @Schema(description = "Service id")
    private Integer serviceId;

    @Schema(description = "Service name")
    private String serviceName;

    @Schema(description = "1-service, 2-addons")
    private Byte serviceType;

    @Schema(description = "Service duration in minutes")
    private Integer serviceTime;

    @Schema(description = "Service price, keep two decimal places")
    private BigDecimal servicePrice;

    @Schema(description = "Service start time, minute offset of the day")
    private Integer startTime;

    @Schema(description = "Service end time, minute offset of the day")
    private Integer endTime;
}
