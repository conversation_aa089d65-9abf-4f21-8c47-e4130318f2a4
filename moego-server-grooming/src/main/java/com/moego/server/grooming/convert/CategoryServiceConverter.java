package com.moego.server.grooming.convert;

import com.moego.server.grooming.dto.GroomingAppointmentDto;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface CategoryServiceConverter {
    CategoryServiceConverter INSTANCE = Mappers.getMapper(CategoryServiceConverter.class);

    List<GroomingAppointmentDto> toDtoList(List<MoeGroomingAppointment> appointmentList);
}
