package com.moego.server.grooming.server;

import com.moego.server.grooming.api.IBookOnlineAcceptPetTypeServiceBase;
import com.moego.server.grooming.dto.CompanyBusinessIdDTO;
import com.moego.server.grooming.dto.ob.BookOnlineAcceptPetTypeDTO;
import com.moego.server.grooming.params.ob.BookOnlineAcceptPetTypeUpdateParams;
import com.moego.server.grooming.service.BookOnlineAcceptPetTypeService;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequiredArgsConstructor
public class BookOnlineAcceptPetTypeServer extends IBookOnlineAcceptPetTypeServiceBase {

    private final BookOnlineAcceptPetTypeService bookOnlineAcceptPetTypeService;

    @Override
    public BookOnlineAcceptPetTypeDTO getAcceptPetType(CompanyBusinessIdDTO companyBusinessIdDTO) {
        var list = bookOnlineAcceptPetTypeService.getAcceptPetTypeList(
                companyBusinessIdDTO.companyId(), companyBusinessIdDTO.businessId());
        Map<Integer, Boolean> petTypeAcceptMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(item -> petTypeAcceptMap.put(item.getPetTypeId(), Objects.equals(item.getAccepted(), 1)));
        }
        return new BookOnlineAcceptPetTypeDTO(petTypeAcceptMap);
    }

    @Override
    public Boolean updateAcceptPetTypeList(BookOnlineAcceptPetTypeUpdateParams params) {
        var tenant = params.companyBusinessIdDTO();
        bookOnlineAcceptPetTypeService.updateAcceptPetTypeList(
                tenant.companyId(), tenant.businessId(), params.petTypeAcceptMap());
        return true;
    }
}
