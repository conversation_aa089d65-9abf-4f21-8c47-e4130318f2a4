package com.moego.server.grooming.web.vo.ob;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ExportAbandonedClientVO {

    @ExcelProperty("Abandoned date & time")
    private String abandonDateTime;

    @ExcelProperty("Abandoned step")
    private String abandonedStep;

    @ExcelProperty("Type")
    private String type;

    @ExcelProperty("First name")
    private String firstName;

    @ExcelProperty("Last name")
    private String lastName;

    @ExcelProperty("Contact")
    private String contact;

    @ExcelProperty("Email")
    private String email;

    @ExcelProperty("Address")
    private String address;

    @ExcelProperty("Pet name(breed)")
    private String petName;

    @ExcelProperty("Selected service & add-ons")
    private String selectedService;

    @ExcelProperty("Service date & time")
    private String serviceDateTime;

    @ExcelProperty("Staff")
    private String staff;

    @ExcelProperty("Status")
    private String status;
}
