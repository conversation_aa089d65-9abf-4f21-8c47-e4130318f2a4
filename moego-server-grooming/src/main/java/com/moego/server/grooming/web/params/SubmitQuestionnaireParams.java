package com.moego.server.grooming.web.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Builder;

@Builder(toBuilder = true)
public record SubmitQuestionnaireParams(
        @Schema(description = "company id", hidden = true) Long companyId,
        @Schema(description = "business id", hidden = true) Long businessId,
        @Schema(description = "staff id", hidden = true) Long staffId,
        @Schema(description = "form detail") @NotEmpty String formDetail) {}
