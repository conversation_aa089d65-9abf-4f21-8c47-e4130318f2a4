package com.moego.server.grooming.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.grooming.dto.appointment.StaffUpcomingAppointmentCountDTO;
import com.moego.server.grooming.dto.appointment.StaffUpcomingOperationCountDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.enums.AppointmentPermissionEnums;
import com.moego.server.grooming.params.appointment.DeletePetParams;
import com.moego.server.grooming.params.appointment.EditAppointmentColorCodeParams;
import com.moego.server.grooming.params.appointment.EditAppointmentPetDetailsParams;
import com.moego.server.grooming.params.appointment.EditPetParams;
import com.moego.server.grooming.params.appointment.QuickAddAppointmentParam;
import com.moego.server.grooming.params.appointment.SetMultiPetsStartTime;
import com.moego.server.grooming.params.appointment.StaffUpcomingAppointmentCountParams;
import com.moego.server.grooming.params.appointment.StaffUpcomingOperationCountParams;
import com.moego.server.grooming.params.appointment.TransferAppointmentParamsV2;
import com.moego.server.grooming.params.appointment.UpdateActionTimeParams;
import com.moego.server.grooming.params.appointment.WaitListRescheduleParams;
import com.moego.server.grooming.result.ServiceOperationListResult;
import com.moego.server.grooming.service.AppointmentServiceV2;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grooming/v2/appointment")
@RequiredArgsConstructor
public class AppointmentV2Controller {
    private final AppointmentServiceV2 appointmentServiceV2;
    private final MoeGroomingAppointmentService appointmentService;
    private final MigrateHelper migrateHelper;
    private final PermissionHelper permissionHelper;

    @Deprecated
    @PostMapping("/quick-add")
    @Auth(AuthType.BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.CREATE_APPOINTMENT})
    @ActivityLog(
            action = Action.CREATE,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#result",
            details = "#appointment")
    public Long quickAddAppointment(AuthContext authContext, @RequestBody @Valid QuickAddAppointmentParam param) {
        Integer businessId;
        if (migrateHelper.isMigrate(authContext.companyId())) {
            permissionHelper.checkPermission(
                    authContext.companyId(),
                    Set.of(authContext.businessId()),
                    authContext.staffId(),
                    PermissionEnums.CREATE_APPOINTMENT);
        }
        return appointmentServiceV2.quickAddAppointment(param.toBuilder()
                .businessId(authContext.getBusinessId())
                .companyId(authContext.companyId())
                .tokenStaffId(authContext.getStaffId())
                .build());
    }

    @PutMapping("/petsAndServices")
    @Auth(AuthType.BUSINESS)
    public void updatePetsAndServices(
            AuthContext authContext, @RequestBody @Valid EditAppointmentPetDetailsParams param) {
        Integer businessId = authContext.getBusinessId();
        if (migrateHelper.isMigrate(authContext.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    authContext.companyId(),
                    authContext.staffId(),
                    param.appointmentId(),
                    AppointmentPermissionEnums.UPDATE);
        }
        appointmentServiceV2.editAppointmentPetAndService(param.toBuilder()
                .businessId(businessId)
                .tokenStaffId(authContext.getStaffId())
                .build());
    }

    /**
     * 修改各种 action 的时间，包括 check in time, ready time, checkout time
     */
    @PutMapping("/action-time")
    @Auth(AuthType.BUSINESS)
    public void updateActionTime(AuthContext authContext, @RequestBody @Valid UpdateActionTimeParams param) {
        Integer businessId = authContext.getBusinessId();
        if (migrateHelper.isMigrate(authContext.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    authContext.companyId(),
                    authContext.staffId(),
                    param.appointmentId(),
                    AppointmentPermissionEnums.UPDATE);
        }
        appointmentServiceV2.updateActionTime(param.toBuilder()
                .businessId(businessId)
                .tokenStaffId(authContext.getStaffId())
                .build());
    }

    @PutMapping("/color-code")
    @Auth(AuthType.BUSINESS)
    public void updateColorCode(AuthContext authContext, @RequestBody @Valid EditAppointmentColorCodeParams param) {
        Integer businessId = authContext.getBusinessId();
        if (migrateHelper.isMigrate(authContext.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    authContext.companyId(),
                    authContext.staffId(),
                    param.appointmentId(),
                    AppointmentPermissionEnums.UPDATE);
        }
        appointmentServiceV2.updateColorCode(param.toBuilder()
                .businessId(businessId)
                .tokenStaffId(authContext.getStaffId())
                .build());
    }

    @GetMapping("/operation/list")
    @Auth(AuthType.BUSINESS)
    ServiceOperationListResult getOperationList(AuthContext authContext, @RequestParam Integer petDetailId) {
        return appointmentServiceV2.getServiceOperationList(authContext.getBusinessId(), petDetailId);
    }

    /**
     * 修改预约中的宠物&服务信息，一次只会更新一只 pet
     */
    @PutMapping("/pet")
    @Auth(AuthType.BUSINESS)
    public void updatePet(AuthContext authContext, @RequestBody @Valid EditPetParams param) {
        Integer businessId = authContext.getBusinessId();
        if (migrateHelper.isMigrate(authContext.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    authContext.companyId(),
                    authContext.staffId(),
                    param.appointmentId(),
                    AppointmentPermissionEnums.UPDATE);
        }
        appointmentServiceV2.editAppointmentPet(param.toBuilder()
                .businessId(businessId)
                .tokenStaffId(authContext.getStaffId())
                .build());
    }

    @DeleteMapping("/pet")
    @Auth(AuthType.BUSINESS)
    public void deletePet(AuthContext authContext, @RequestBody @Valid DeletePetParams param) {
        Integer businessId = authContext.getBusinessId();
        if (migrateHelper.isMigrate(authContext.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    authContext.companyId(),
                    authContext.staffId(),
                    param.appointmentId(),
                    AppointmentPermissionEnums.UPDATE);
        }
        appointmentServiceV2.deleteAppointmentPet(param.toBuilder()
                .businessId(businessId)
                .tokenStaffId(authContext.getStaffId())
                .build());
    }

    @PutMapping("/multi-pets/start-time")
    @Auth(AuthType.BUSINESS)
    public void setMultiPetsStartTime(AuthContext authContext, @RequestBody @Valid SetMultiPetsStartTime param) {
        Integer businessId = authContext.getBusinessId();
        if (migrateHelper.isMigrate(authContext.companyId())) {
            businessId = appointmentService.checkPermissionToAppointment(
                    authContext.companyId(),
                    authContext.staffId(),
                    param.appointmentId(),
                    AppointmentPermissionEnums.UPDATE);
        }
        appointmentServiceV2.setMultiPetsStartTime(param.toBuilder()
                .tokenBusinessId(businessId)
                .tokenStaffId(authContext.getStaffId())
                .build());
    }

    /**
     * 查询 staff upcoming appointment 数量
     *
     * @param params staffId - 查询的 staff id, businessIds - 需要查 upcoming appointment 数量的 business id 列表
     */
    @PostMapping("/staff/upcoming/count")
    @Auth(AuthType.COMPANY)
    public StaffUpcomingAppointmentCountDTO getStaffUpcomingCount(
            @RequestBody @Valid StaffUpcomingAppointmentCountParams params) {
        params = params.toBuilder()
                .tokenCompanyId(AuthContext.get().getCompanyId())
                .build();
        return appointmentServiceV2.queryStaffUpComingAppointmentCountV2(params);
    }

    @PostMapping("/staff/upcoming/operation/count")
    @Auth(AuthType.COMPANY)
    @Schema(description = "查询指定 sourceStaff 的 upcoming 预约中和 targetStaff startAtSameTime 的 operation 数量")
    public StaffUpcomingOperationCountDTO getStaffUpcomingOperationCount(
            @RequestBody @Valid StaffUpcomingOperationCountParams params) {
        Integer operationCount = appointmentServiceV2.queryStaffUpComingOperationCountV2(params);
        return StaffUpcomingOperationCountDTO.builder()
                .upcomingOperationCount(operationCount)
                .build();
    }

    @PostMapping("/transfer")
    @Auth(AuthType.COMPANY)
    @ActivityLog(
            action = AppointmentAction.TRANSFER_UPCOMING_APPOINTMENT,
            resourceType = ResourceType.APPOINTMENT,
            details = "#params",
            beforeInvocation = true)
    public Boolean transferAppointment(@RequestBody @Valid TransferAppointmentParamsV2 params) {
        params.setTokenCompanyId(AuthContext.get().getCompanyId());
        params.setTokenStaffId(AuthContext.get().getStaffId());
        return appointmentServiceV2.transferStaffUpcomingAppointment(params);
    }

    @PutMapping("/rescheduleFromWaitList")
    @Auth(AuthType.BUSINESS)
    public Long reschedule(AuthContext authContext, @RequestBody @Valid WaitListRescheduleParams params) {
        return appointmentServiceV2.rescheduleFromWaitList(params.toBuilder()
                .businessId(authContext.getBusinessId())
                .tokenStaffId(authContext.getStaffId())
                .build());
    }
}
