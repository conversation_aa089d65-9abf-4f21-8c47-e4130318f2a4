package com.moego.server.grooming.service.dto.report;

import com.moego.common.utils.AmountUtils;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CollectedPriceDTO {

    private BigDecimal collectedRevenue;
    private BigDecimal collectedServicePrice;
    private BigDecimal collectedProductPrice;
    private BigDecimal collectedServiceChargePrice;

    private BigDecimal collectedTips;
    private BigDecimal collectedServiceTax;
    private BigDecimal collectedProductTax;
    private BigDecimal collectedServiceChargeTax;

    private BigDecimal serviceDiscountAmount;
    private BigDecimal productDiscountAmount;
    private BigDecimal serviceChargeDiscountAmount;

    private BigDecimal netSaleRevenue; // collectedPrice - discount

    public BigDecimal getTotalCollectedTax() {
        return AmountUtils.sum(collectedServiceTax, collectedProductTax, collectedServiceChargeTax);
    }
}
