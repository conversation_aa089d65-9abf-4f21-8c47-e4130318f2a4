package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeQbSyncServiceCharge;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeQbSyncServiceChargeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    int insert(MoeQbSyncServiceCharge record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    int insertSelective(MoeQbSyncServiceCharge record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    MoeQbSyncServiceCharge selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeQbSyncServiceCharge record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeQbSyncServiceCharge record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service_charge
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeQbSyncServiceCharge record);

    List<MoeQbSyncServiceCharge> selectByBusinessIdRealmIdServiceChargeIds(
            @Param("businessId") Integer businessId,
            @Param("realmId") String realmId,
            @Param("serviceChargeIdList") List<Integer> serviceChargeIdList);

    int insertOrUpdate(MoeQbSyncServiceCharge record);
}
