package com.moego.server.grooming.service.dto;

import com.intuit.ipp.services.DataService;
import com.moego.server.grooming.mapperbean.MoeQbConnect;
import lombok.Data;

@Data
public class DataServiceTmpDto {

    private DataService dataService;
    private MoeQbConnect qbConnect;

    public DataServiceTmpDto(DataService dataService, MoeQbConnect qbConnect) {
        this.dataService = dataService;
        this.qbConnect = qbConnect;
    }

    public String getRealmId() {
        return qbConnect.getRealmId();
    }

    public Integer getConnectId() {
        return qbConnect.getId();
    }
}
