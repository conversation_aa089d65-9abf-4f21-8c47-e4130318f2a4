package com.moego.server.grooming.web.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.util.List;
import lombok.Builder;

@Builder(toBuilder = true)
public record DrivingInfoCalculationParams(
        Integer businessId,
        @Schema(description = "目标地点经度，不能为空") @NotBlank String addressLat,
        @Schema(description = "目标地点纬度，不能为空") @NotBlank String addressLng,
        @Schema(description = "client 出发列表") List<Long> clientIdsFrom,
        @Schema(description = "client 到达列表") List<Long> clientIdsTo,
        @Schema(description = "van 出发列表") List<Long> vanStaffIdsFrom,
        @Schema(description = "van 到达列表") List<Long> vanStaffIdsTo) {}
