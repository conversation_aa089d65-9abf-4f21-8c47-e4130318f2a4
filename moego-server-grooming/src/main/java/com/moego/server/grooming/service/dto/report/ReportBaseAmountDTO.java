package com.moego.server.grooming.service.dto.report;

import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public abstract class ReportBaseAmountDTO {

    private Integer invoiceId;

    private String invoiceType;

    private BigDecimal tipsAmount;
    private BigDecimal tipsBaseAmount;

    private BigDecimal taxAmount;

    private BigDecimal discountAmount;

    private BigDecimal convenienceFee;

    private BigDecimal subTotalAmount;

    private BigDecimal totalAmount;

    private BigDecimal paymentAmount;

    private BigDecimal paidAmount;

    private BigDecimal remainAmount;

    private BigDecimal refundedAmount;

    // service/product/service-charge discount
    private BigDecimal serviceDiscountAmount;
    private BigDecimal productDiscountAmount;
    private BigDecimal serviceChargeDiscountAmount;

    // service/product/service-charge tax
    private BigDecimal serviceTaxAmount;
    private BigDecimal productTaxAmount;
    private BigDecimal serviceChargeTaxAmount;

    // service/product/service-charge 总价，单价 * 数量，不含tax、tips、discount
    private BigDecimal totalServicePrice;
    private BigDecimal totalProductPrice;
    private BigDecimal totalServiceChargePrice;

    // service/product/service-charge 应收总金额，含tax，discount
    private BigDecimal totalServiceSale;
    private BigDecimal totalProductSale;
    private BigDecimal totalServiceChargeSale;
}
