package com.moego.server.grooming.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import com.moego.server.grooming.dto.groomingreport.GroomingReportInfoForInputDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportPreviewDataDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportRecordDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportSettingDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportTemplateDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportThemeConfigDTO;
import com.moego.server.grooming.params.groomingreport.GroomingReportInfoParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportPreviewParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportSettingParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportTemplateParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportViewCountParams;
import com.moego.server.grooming.service.MoeGroomingReportService;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/grooming/grooming-report")
public class GroomingReportController {

    @Autowired
    private MoeGroomingReportService groomingReportService;

    @Autowired
    private MigrateHelper migrateHelper;

    @PostMapping("/init")
    @Auth(AuthType.BUSINESS)
    @Permission(permissionsNeedToCheck = PermissionEnums.EDIT_TICKET_COMMENT_AND_GROOMING_REPORT)
    public GroomingReportSettingDTO initGroomingReport() {
        return groomingReportService.initGroomingReportSetting(
                AuthContext.get().getBusinessId(), AuthContext.get().companyId(), true);
    }

    @GetMapping("/setting")
    @Auth(AuthType.BUSINESS)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_GROOMING_REPORT_SETTINGS)
    public GroomingReportSettingDTO getGroomingReportSetting() {
        return groomingReportService.getGroomingReportSetting(AuthContext.get().getBusinessId(), true);
    }

    @PutMapping("/setting")
    @Auth(AuthType.BUSINESS)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_GROOMING_REPORT_SETTINGS)
    public GroomingReportSettingDTO saveGroomingReportSetting(@Valid @RequestBody GroomingReportSettingParams params) {
        params.setBusinessId(AuthContext.get().getBusinessId());
        params.setUpdateBy(AuthContext.get().getStaffId());
        return groomingReportService.saveGroomingReportSetting(params);
    }

    @GetMapping("/template")
    @Auth(AuthType.BUSINESS)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_GROOMING_REPORT_SETTINGS)
    public GroomingReportTemplateDTO getGroomingReportTemplate() {
        return groomingReportService.getGroomingReportTemplate(AuthContext.get().getBusinessId(), true);
    }

    @PutMapping("/template")
    @Auth(AuthType.BUSINESS)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_GROOMING_REPORT_SETTINGS)
    public GroomingReportTemplateDTO saveGroomingReportTemplate(
            @Valid @RequestBody GroomingReportTemplateParams params) {
        params.setBusinessId(AuthContext.get().getBusinessId());
        params.setUpdateBy(AuthContext.get().getStaffId());
        params.setCompanyId(AuthContext.get().companyId());
        return groomingReportService.saveGroomingReportTemplate(params);
    }

    @GetMapping("/theme/config")
    @Auth(AuthType.BUSINESS)
    public List<GroomingReportThemeConfigDTO> getGroomingReportThemeConfigList() {
        return groomingReportService.getGroomingReportThemeConfigList(
                AuthContext.get().getBusinessId());
    }

    @GetMapping("/preview/data")
    @Auth(AuthType.BUSINESS)
    public GroomingReportPreviewDataDTO getPreviewData(
            @RequestParam(value = "reportId", required = false) Integer reportId,
            @RequestParam(value = "themeCode", required = false) String themeCode) {
        return groomingReportService.getPreviewData(AuthContext.get().getBusinessId(), reportId, themeCode);
    }

    @PostMapping("/preview/url")
    @Auth(AuthType.BUSINESS)
    public String getPreviewUrl(@RequestBody GroomingReportPreviewParams params) {
        params.setBusinessId(AuthContext.get().getBusinessId());
        params.setCompanyId(AuthContext.get().companyId());
        return groomingReportService.savePreviewData(params);
    }

    @GetMapping("/records")
    @Auth(AuthType.BUSINESS)
    public List<GroomingReportRecordDTO> getGroomingReportRecords(@RequestParam("groomingId") Integer groomingId) {
        var migratedInfo = migrateHelper.getMigrationInfo(AuthContext.get().businessId());
        return groomingReportService.getGroomingReportRecords(
                migratedInfo.isMigrate(),
                migratedInfo.companyId(),
                AuthContext.get().getBusinessId(),
                groomingId);
    }

    @GetMapping("/info")
    @Auth(AuthType.BUSINESS)
    @Permission(permissionsNeedToCheck = PermissionEnums.EDIT_TICKET_COMMENT_AND_GROOMING_REPORT)
    public GroomingReportInfoForInputDTO getGroomingReportInfo(
            @RequestParam("groomingId") Integer groomingId, @RequestParam("petId") Integer petId) {
        var migrated =
                migrateHelper.getMigrationInfo(AuthContext.get().businessId()).isMigrate();
        return groomingReportService.getGroomingReportInfoForInput(
                migrated, AuthContext.get().getBusinessId(), groomingId, petId);
    }

    @PostMapping("/info")
    @Auth(AuthType.BUSINESS)
    @Permission(permissionsNeedToCheck = PermissionEnums.EDIT_TICKET_COMMENT_AND_GROOMING_REPORT)
    public GroomingReportInfoForInputDTO saveGroomingReportInfo(
            @Parameter(description = "modify type: 1-save as draft, 2-submit") @Max(2) @Min(1) @RequestParam("type")
                    Integer type,
            @Valid @RequestBody GroomingReportInfoParams params) {
        params.setBusinessId(AuthContext.get().getBusinessId());
        params.setCompanyId(AuthContext.get().companyId());
        params.setUpdateBy(AuthContext.get().getStaffId());
        return groomingReportService.saveOrSubmitGroomingReportInfo(
                migrateHelper.isMigrate(AuthContext.get().companyId()), params, type == 2);
    }

    @GetMapping("/summaryInfo")
    @Auth(AuthType.BUSINESS)
    public GroomingReportSummaryInfoDTO getGroomingReportSummaryInfo(
            @RequestParam("uuid") String uuid, @RequestParam(value = "reportId", required = false) Integer reportId) {
        return groomingReportService.getGroomingReportSummaryInfo(uuid, reportId);
    }

    @GetMapping("/client/info")
    @Auth(AuthType.ANONYMOUS)
    public GroomingReportSummaryInfoDTO getGroomingReportInfoForClient(
            @RequestParam("uuid") String uuid, @RequestParam(value = "reportId", required = false) Integer reportId) {
        return groomingReportService.getGroomingReportSummaryInfo(uuid, reportId);
    }

    @PostMapping("/client/view/upload")
    @Auth(AuthType.ANONYMOUS)
    public Boolean addUpGroomingReportOpenedCount(@Valid @RequestBody GroomingReportViewCountParams params) {
        return groomingReportService.addUpGroomingReportOpenedCount(params);
    }
}
