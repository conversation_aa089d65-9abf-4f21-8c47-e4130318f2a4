package com.moego.server.grooming.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/6/8
 */
@Getter
@RequiredArgsConstructor
public enum DataRangeAliasEnum {
    LAST_7_DAYS("last_7_days"),
    LAST_14_DAYS("last_14_days"),
    LAST_30_DAYS("last_30_days"),
    LAST_14_DAYS_TO_7_DAYS("last_14_days_to_7_days"),
    LAST_28_DAYS_TO_14_DAYS("last_28_days_to_14_days"),
    LAST_60_DAYS_TO_30_DAYS("last_60_days_to_30_days");

    private final String alias;
}
