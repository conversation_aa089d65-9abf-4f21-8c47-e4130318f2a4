package com.moego.server.grooming.service.dto;

import java.util.List;
import lombok.Data;
import org.apache.pulsar.shade.io.swagger.annotations.ApiModelProperty;

@Data
public class CompanyCreateServiceDto {
    @ApiModelProperty("为 null 不会更新 list")
    private List<ServiceLocationOverrideDto> locationOverrideList;

    @ApiModelProperty("是否对所有 location 可见，如果不可见，需要指定 locationId 的话，通过 locationOverrideList 传递")
    private Byte isAllLocation;

    private Integer serviceId;

    @ApiModelProperty(
            "mobile 特殊参数，传递这个参数后，不需要传递 locationOverrideList，会优先覆盖 db 内 locationOverrideList 的数据，找不到则覆盖 company service 数据")
    private Integer singleLocationId;
}
