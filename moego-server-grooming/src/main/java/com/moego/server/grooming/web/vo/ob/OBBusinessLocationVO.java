package com.moego.server.grooming.web.vo.ob;

import com.moego.server.grooming.web.vo.client.AddressVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/17
 */
@Data
public class OBBusinessLocationVO {

    @Schema(description = "book online profile address")
    private String address;

    @Schema(description = "location address details")
    private AddressVO addressDetails;

    @Schema(description = "book online profile avatar path")
    private String avatarPath;

    @Schema(description = "book online profile business name")
    private String businessName;

    @Schema(description = "book online profile business name")
    private String phoneNumber;

    @Schema(description = "business book online name")
    private String bookOnlineName;

    @Schema(description = "Biz customized URL domain name")
    private String urlDomainName;

    @Schema(description = "default true")
    private Boolean isAvailable;

    @Schema(description = "online booking available")
    private Byte isEnable;
}
