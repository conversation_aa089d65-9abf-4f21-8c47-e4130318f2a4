package com.moego.server.grooming.mapper.typehandler;

import com.moego.server.grooming.enums.WaitListStatusEnum;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

public class WaitListStatusEnumHandler extends BaseTypeHandler<WaitListStatusEnum> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, WaitListStatusEnum parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setInt(i, parameter.getValue());
    }

    @Override
    public WaitListStatusEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return WaitListStatusEnum.fromValue(rs.getInt(columnName));
    }

    @Override
    public WaitListStatusEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return WaitListStatusEnum.fromValue(rs.getInt(columnIndex));
    }

    @Override
    public WaitListStatusEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return WaitListStatusEnum.fromValue(cs.getInt(columnIndex));
    }
}
