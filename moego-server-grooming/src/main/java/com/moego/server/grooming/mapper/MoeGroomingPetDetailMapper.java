package com.moego.server.grooming.mapper;

import com.moego.server.grooming.dto.CustomerGroomingAppointmentPetDetailDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.GroomingPetServiceListInfoDTO;
import com.moego.server.grooming.dto.PetDetailInvoiceDTO;
import com.moego.server.grooming.dto.PetDetailServiceDTO;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import com.moego.server.grooming.dto.StaffConflictDTO;
import com.moego.server.grooming.mapper.po.GroomingStaffIdListPO;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetailExample;
import com.moego.server.grooming.params.report.DescribePetDetailReportsParams;
import com.moego.server.grooming.service.dto.ob.OBClientApptDTO;
import com.moego.server.grooming.web.vo.PetDetailMonthlyQueryVo;
import com.moego.server.grooming.web.vo.PetDetailQueryVo;
import java.math.BigDecimal;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingPetDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    long countByExample(MoeGroomingPetDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    int deleteByExample(MoeGroomingPetDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    int insert(MoeGroomingPetDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingPetDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    List<MoeGroomingPetDetail> selectByExample(MoeGroomingPetDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    MoeGroomingPetDetail selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeGroomingPetDetail record, @Param("example") MoeGroomingPetDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeGroomingPetDetail record, @Param("example") MoeGroomingPetDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingPetDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_pet_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingPetDetail record);

    Integer insertSelectiveBatch(@Param("moeGroomingPetDetails") List<MoeGroomingPetDetail> moeGroomingPetDetails);

    Integer deleteByAppointmentId(@Param("groomingId") Integer appointmentId);

    List<GroomingPetDetailDTO> queryPetDetailByGroomingId(@Param("groomingId") Integer groomingId);

    List<GroomingPetDetailDTO> queryPetDetailByServiceItems(
            @Param("groomingId") Integer groomingId, @Param("serviceItems") List<Integer> serviceItems);

    List<GroomingPetServiceListInfoDTO> queryPetDetailListForGroomingOnly(
            @Param("petDetailQuery") PetDetailQueryVo petDetailQueryVo);

    List<GroomingPetServiceListInfoDTO> queryPetDetailListIncludeHybrid(
            @Param("petDetailQuery") PetDetailQueryVo petDetailQueryVo);

    List<GroomingPetServiceListInfoDTO> queryPetDetailListMonthlyForGroomingOnly(
            @Param("petDetailMonthlyQuery") PetDetailMonthlyQueryVo petDetailMonthlyQueryVo);

    List<GroomingPetServiceListInfoDTO> queryPetDetailListMonthlyIncludeHybrid(
            @Param("petDetailMonthlyQuery") PetDetailMonthlyQueryVo petDetailMonthlyQueryVo);

    List<MoeGroomingPetDetail> queryPetDetailByAppointmentDateAndStaffId(
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String appointmentDate,
            @Param("staffId") Integer staffId);

    List<StaffConflictDTO> queryPetDetailByAppointmentDatesAndStaffIds(
            @Param("businessId") Integer businessId,
            @Param("appointmentDates") List<String> appointmentDates,
            @Param("staffIds") List<Integer> staffIds,
            @Param("exceptIds") List<Integer> exceptIds,
            @Param("exceptRepeatId") Integer exceptRepeatId);

    List<MoeGroomingPetDetail> queryPetDetailByAppointmentDateAndStaffIdBlock(
            @Param("appointmentDate") String appointmentDate,
            @Param("businessId") Integer businessId,
            @Param("staffId") Integer staffId);

    List<GroomingPetDetailDTO> queryPetDetailByGroomingIds(List<Integer> groomingIds);

    List<GroomingPetDetailDTO> queryHasStaffPetDetailByGroomingIds(
            @Param("list") List<Integer> groomingIds,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<GroomingPetDetailDTO> queryAllPetDetailByGroomingIds(List<Integer> groomingIds);

    Integer deletePetDetail(List<Integer> ids);

    Integer deleteByGroomingIds(List<Integer> groomingIds);

    List<PetDetailServiceDTO> queryPetDetailServiceByGroomingId(
            @Param("groomingId") Integer groomingId, @Param("petId") Integer petId);

    List<PetDetailInvoiceDTO> queryPetDetailInvoiceByGroomingId(@Param("groomingId") Integer groomingId);

    List<PetDetailInvoiceDTO> queryPetDetailInvoiceByCidBidGid(
            @Param("companyId") Long companyId,
            @Param("businessId") Integer businessId,
            @Param("groomingId") Integer groomingId);

    List<MoeGroomingPetDetail> queryPetDetailCountByGroomingId(@Param("groomingId") Integer groomingId);

    List<Integer> queryTransferAppointment(
            @Param("businessId") Integer businessId,
            @Param("sourceStaffId") Integer sourceStaffId,
            @Param("nowDate") String nowDate,
            @Param("endTimes") Integer endTimes);

    int transferAppointment(
            @Param("sourceStaffId") Integer sourceStaffId,
            @Param("targetStaffId") Integer targetStaffId,
            @Param("updateTime") Long updateTime,
            @Param("groomingIdList") List<Integer> groomingIdList);

    List<CustomerGroomingAppointmentPetDetailDTO> queryCustomerAppointmentPetDetail(
            @Param("groomingId") Integer groomingId);

    List<OBClientApptDTO.OBClientApptPetDetailDTO> getApptPetDetail(@Param("groomingId") Integer groomingId);

    List<MoeGroomingAppointment> selectUpcomingByCustomerId(
            @Param("companyId") Long companyId,
            @Param("customerId") Integer customerId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes);

    List<MoeGroomingPetDetail> selectPetDetailByGroomingIdList(@Param("groomingIdList") List<Integer> groomingIdList);

    List<MoeGroomingPetDetail> selectByAppointmentIdList(@Param("appointmentIdList") List<Integer> appointmentIdList);

    List<MoeGroomingPetDetail> queryByBusinessIdAndDateOrderByTime(
            @Param("businessId") Integer businessId, @Param("date") String date);

    /**
     * 查询所有进行中的预约，包含 block
     * 日期区间： [startDate, endDate)
     *
     * @param businessId
     * @param startDate
     * @param endDate
     * @param staffIds
     * @return
     */
    List<SmartScheduleGroomingDetailsDTO> queryInProgressByBusinessIdBetweenDates(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("staffIds") List<Integer> staffIds);

    List<Integer> queryStaffIdByGroomingId(@Param("groomingId") Integer groomingId);

    List<GroomingStaffIdListPO> queryStaffIdByGroomingIds(@Param("groomingIds") List<Integer> groomingIds);

    List<Integer> queryStaffIdListByDateRange(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<GroomingPetDetailDTO> queryUpcomingApptsByPetId(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("petId") Integer petId,
            @Param("serviceId") Integer serviceId);

    List<Integer> queryUpcomingApptsByServiceId(
            @Param("businessId") Integer businessId,
            @Param("appointmentDate") String appointmentDate,
            @Param("endTimes") Integer endTimes,
            @Param("serviceId") Integer serviceId);

    BigDecimal countApptRevenue(@Param("groomingIds") List<Integer> groomingIds);

    BigDecimal countATVRecentlyDays(
            @Param("businessId") Integer businessId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    List<GroomingPetServiceListInfoDTO> queryFinishedPetDetail(
            @Param("businessIdList") List<Integer> businessIdList,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<MoeGroomingPetDetail> describePetDetailReports(DescribePetDetailReportsParams params);

    /**
     * 根据id列表查询
     *
     * @param petDetailIdList pet detail id 列表
     * @return List<GroomingPetDetailDTO>
     */
    List<GroomingPetDetailDTO> queryNormalPetDetailDTOByIds(@Param("list") List<Long> petDetailIdList);

    /**
     * 根据id列表查询
     *
     * @param petDetailIdList pet detail id 列表
     * @return List<MoeGroomingPetDetail>
     */
    List<MoeGroomingPetDetail> selectNormalPetDetailByIdList(@Param("list") List<Long> petDetailIdList);

    Integer batchInsertBlockPetDetail(@Param("moeGroomingPetDetails") List<MoeGroomingPetDetail> moeGroomingPetDetails);
}
