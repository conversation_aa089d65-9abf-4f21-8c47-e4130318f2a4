package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestionSave;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MoeBookOnlineQuestionSaveMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question_save
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question_save
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlineQuestionSave record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question_save
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlineQuestionSave record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question_save
     *
     * @mbg.generated
     */
    MoeBookOnlineQuestionSave selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question_save
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlineQuestionSave record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question_save
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeBookOnlineQuestionSave record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question_save
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlineQuestionSave record);

    List<MoeBookOnlineQuestionSave> selectByCustomerIdWithPetIds(
            @Param("businessId") Integer businessId, @Param("customerId") Integer customerId);

    List<MoeBookOnlineQuestionSave> selectByCustomerIdsWithPetIds(
            @Param("businessId") Integer businessId, @Param("customerIds") List<Integer> customerIds);

    int updateByCustomerIdOrPetId(MoeBookOnlineQuestionSave record);
}
