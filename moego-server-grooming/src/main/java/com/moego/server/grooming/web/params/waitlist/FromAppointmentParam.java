package com.moego.server.grooming.web.params.waitlist;

import com.moego.server.grooming.params.appointment.PetParams;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.LocalDate;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class FromAppointmentParam {
    @Schema(description = "business id", hidden = true)
    Long businessId;

    @Schema(description = "company id", hidden = true)
    Long companyId;

    @Schema(description = "token staff id", hidden = true)
    Long tokenStaffId;

    @NotNull
    @Positive
    private Long appointmentId;

    @Valid
    private DatePreference datePreference;

    @Valid
    private TimePreference timePreference;

    @Valid
    private StaffPreference staffPreference;

    @Schema(description = "valid from, YYYY-MM-DD")
    private LocalDate validFrom;

    @Schema(description = "valid till, YYYY-MM-DD")
    private LocalDate validTill;

    private String ticketComment;
    private List<PetParams> petList;
    private Boolean allPetsStartAtSameTime; // 仅在需要编辑 petList 时有效

    @Schema(description = "if need delete original appointment/ob request")
    private Boolean deleteOriginalAppointment;
}
