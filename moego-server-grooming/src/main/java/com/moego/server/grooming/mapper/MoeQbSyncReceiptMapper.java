package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeQbSyncReceipt;
import com.moego.server.grooming.mapperbean.MoeQbSyncReceiptExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeQbSyncReceiptMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    long countByExample(MoeQbSyncReceiptExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    int deleteByExample(MoeQbSyncReceiptExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    int insert(MoeQbSyncReceipt record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    int insertSelective(MoeQbSyncReceipt record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    List<MoeQbSyncReceipt> selectByExample(MoeQbSyncReceiptExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    MoeQbSyncReceipt selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeQbSyncReceipt record, @Param("example") MoeQbSyncReceiptExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MoeQbSyncReceipt record, @Param("example") MoeQbSyncReceiptExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeQbSyncReceipt record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_receipt
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeQbSyncReceipt record);

    @Deprecated
    MoeQbSyncReceipt selectByPaymentDetailIdAndReceiptType(
            @Param("paymentDetailId") Integer paymentDetailId, @Param("receiptType") Byte receiptType);

    List<MoeQbSyncReceipt> selectByBusinessRealmIdDetailIdAndType(
            @Param("businessId") Integer businessId,
            @Param("realmId") String realmId,
            @Param("paymentDetailId") Integer paymentDetailId,
            @Param("receiptType") Byte receiptType);

    List<MoeQbSyncReceipt> selectByPaymentDetailId(@Param("paymentDetailId") Integer paymentDetailId);

    List<MoeQbSyncReceipt> selectByBusinessIdAndRealmId(
            @Param("businessId") Integer businessId, @Param("realmId") String realmId);
}
