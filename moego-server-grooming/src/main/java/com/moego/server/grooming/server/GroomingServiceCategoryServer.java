package com.moego.server.grooming.server;

import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.server.grooming.api.IGroomingServiceCategoryServiceBase;
import com.moego.server.grooming.dto.ServiceCategoryDTO;
import com.moego.server.grooming.mapper.MoeGroomingServiceCategoryMapper;
import com.moego.server.grooming.service.GroomingCompanyServiceCategoryService;
import com.moego.server.grooming.service.dto.ServiceCategoryListDto;
import com.moego.server.grooming.service.dto.ServiceCategoryUpdateDto;
import com.moego.server.grooming.utils.PetDetailDTOUtil;
import com.moego.server.grooming.web.params.ServiceCategoryBatchUpdateParams;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class GroomingServiceCategoryServer extends IGroomingServiceCategoryServiceBase {

    private final MoeGroomingServiceCategoryMapper serviceCategoryMapper;
    private final GroomingCompanyServiceCategoryService groomingCompanyServiceCategoryService;

    @Override
    public List<ServiceCategoryDTO> getServiceCategories(Collection<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return serviceCategoryMapper.selectByIdSet(Set.copyOf(ids)).stream()
                .map(category -> {
                    ServiceCategoryDTO dto = new ServiceCategoryDTO();
                    dto.setId(category.getId());
                    dto.setBusinessId(category.getBusinessId());
                    dto.setName(category.getName());
                    dto.setType(category.getType());
                    dto.setSort(category.getSort());
                    return dto;
                })
                .toList();
    }

    @Override
    public List<com.moego.server.grooming.dto.ServiceCategoryListDto> getEditServiceCategory(
            @RequestBody com.moego.server.grooming.params.ServiceCategoryBatchUpdateParams batchUpdateParams) {
        if (batchUpdateParams.getServiceItemType() == null) {
            batchUpdateParams.setServiceItemType(ServiceItemType.GROOMING.getNumber());
        }

        /**
         * pack request params
         */
        ServiceCategoryBatchUpdateParams param = new ServiceCategoryBatchUpdateParams();
        param.setType(batchUpdateParams.getType());
        param.setServiceItemType(batchUpdateParams.getServiceItemType());
        List<ServiceCategoryUpdateDto> updateDto = new ArrayList<>();

        for (com.moego.server.grooming.params.ServiceCategoryUpdateDto dto : batchUpdateParams.getCategorySaveList()) {
            ServiceCategoryUpdateDto update = new ServiceCategoryUpdateDto();
            update.setCategoryId(dto.getCategoryId());
            update.setName(dto.getName());
            update.setType(dto.getType());
            update.setServiceItemType(dto.getServiceItemType());
            updateDto.add(update);
        }

        param.setCategorySaveList(updateDto);

        groomingCompanyServiceCategoryService.updateServiceCategoryBatch(batchUpdateParams.getCompanyId(), param);

        List<ServiceCategoryListDto> editServiceCategoryForCompany =
                groomingCompanyServiceCategoryService.getEditServiceCategoryForCompany(
                        batchUpdateParams.getCompanyId(),
                        batchUpdateParams.getType(),
                        PetDetailDTOUtil.mapServiceItemType(batchUpdateParams.getServiceItemType()));

        List<com.moego.server.grooming.dto.ServiceCategoryListDto> result = new ArrayList<>();

        /**
         * pack result
         */
        editServiceCategoryForCompany.forEach(dto -> {
            com.moego.server.grooming.dto.ServiceCategoryListDto serviceCategoryListDto =
                    new com.moego.server.grooming.dto.ServiceCategoryListDto();
            serviceCategoryListDto.setCategoryId(dto.getCategoryId());
            serviceCategoryListDto.setName(dto.getName());
            serviceCategoryListDto.setType(dto.getType());
            serviceCategoryListDto.setCreateTime(dto.getCreateTime());
            result.add(serviceCategoryListDto);
        });

        return result;
    }

    @Override
    public List<com.moego.server.grooming.dto.ServiceCategoryListDto> listServiceCategories(
            @RequestParam("companyId") Long companyId,
            @RequestParam("type") Byte type,
            @RequestParam("serviceItemType") Byte serviceItemType) {
        List<com.moego.server.grooming.service.dto.ServiceCategoryListDto> editServiceCategoryForCompany =
                groomingCompanyServiceCategoryService.getEditServiceCategoryForCompany(
                        companyId, type, PetDetailDTOUtil.mapServiceItemType(serviceItemType.intValue()));
        if (CollectionUtils.isEmpty(editServiceCategoryForCompany)) {
            return List.of();
        }
        List<com.moego.server.grooming.dto.ServiceCategoryListDto> result = new ArrayList<>();

        editServiceCategoryForCompany.forEach(dto -> {
            com.moego.server.grooming.dto.ServiceCategoryListDto serviceCategoryListDto =
                    new com.moego.server.grooming.dto.ServiceCategoryListDto();
            serviceCategoryListDto.setCategoryId(dto.getCategoryId());
            serviceCategoryListDto.setName(dto.getName());
            serviceCategoryListDto.setType(dto.getType());
            serviceCategoryListDto.setCreateTime(dto.getCreateTime());
            result.add(serviceCategoryListDto);
        });

        return result;
    }
}
