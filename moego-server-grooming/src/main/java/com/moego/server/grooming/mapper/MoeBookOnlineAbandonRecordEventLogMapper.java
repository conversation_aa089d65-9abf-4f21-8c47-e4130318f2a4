package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordEventLog;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordEventLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeBookOnlineAbandonRecordEventLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_event_log
     *
     * @mbg.generated
     */
    long countByExample(MoeBookOnlineAbandonRecordEventLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_event_log
     *
     * @mbg.generated
     */
    int deleteByExample(MoeBookOnlineAbandonRecordEventLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_event_log
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_event_log
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlineAbandonRecordEventLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_event_log
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlineAbandonRecordEventLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_event_log
     *
     * @mbg.generated
     */
    List<MoeBookOnlineAbandonRecordEventLog> selectByExample(MoeBookOnlineAbandonRecordEventLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_event_log
     *
     * @mbg.generated
     */
    MoeBookOnlineAbandonRecordEventLog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_event_log
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeBookOnlineAbandonRecordEventLog record,
            @Param("example") MoeBookOnlineAbandonRecordEventLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_event_log
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeBookOnlineAbandonRecordEventLog record,
            @Param("example") MoeBookOnlineAbandonRecordEventLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_event_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlineAbandonRecordEventLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_event_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlineAbandonRecordEventLog record);
}
