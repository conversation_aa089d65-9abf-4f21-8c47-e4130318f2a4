package com.moego.server.grooming.service.ob.metrics;

import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Service
@RequiredArgsConstructor
public class AbandonedClientsMetricService implements IOBMetricsService {

    private final MoeBookOnlineAbandonRecordMapper abandonRecordMapper;

    @Override
    public Integer sumMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return abandonRecordMapper.countAbandonedClients(
                timeRangeDTO.businessId(),
                timeRangeDTO.startTime(),
                timeRangeDTO.endTime(),
                OBStepEnum.listRecoverableSteps());
    }

    @Override
    public Object proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        // abandoned clients / started clients
        int abandonedClients = abandonRecordMapper.countAbandonedClients(
                timeRangeDTO.businessId(),
                timeRangeDTO.startTime(),
                timeRangeDTO.endTime(),
                OBStepEnum.listRecoverableSteps());
        int startedClients = abandonRecordMapper.countStartedClients(
                timeRangeDTO.businessId(),
                timeRangeDTO.startTime(),
                timeRangeDTO.endTime(),
                OBStepEnum.listRecoverableSteps());
        if (startedClients == 0) {
            return "";
        }
        return BigDecimal.valueOf(abandonedClients).divide(BigDecimal.valueOf(startedClients), 2, RoundingMode.HALF_UP);
    }

    @Override
    public OBMetricsEnum getMetricsName() {
        return OBMetricsEnum.abandoned_clients;
    }
}
