name: Lint Workflow

on:
  push:
    branches:
      - "*"
    paths:
      - 'backend/app/**'
      - 'backend/proto/**'
      - '.github/workflows/lint.yaml'
  workflow_call:
  workflow_dispatch:

jobs:
  proto-lint:
    name: Proto Lint
    runs-on: ubuntu-24.04
    container:
      image: ghcr.io/moegolibrary/moego-ci-builder:latest
      credentials:
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run proto lint
        run: |
          echo "🔍 Checking proto files..."
          proto_dir=./backend/proto
          deps=$proto_dir/deps
          config_path=$proto_dir/api-linter-config.yaml

          # Skip if no proto directory or config
          if [ ! -d "$proto_dir" ] || [ ! -f "$config_path" ]; then
            echo "⏭️  Skipping proto lint (no proto files or config)"
            exit 0
          fi

          # Find proto files
          files=$(find $proto_dir -name '*.proto' -print | grep -v "$proto_dir/helloworld/" | grep -v "$proto_dir/deps/" || true)
          if [ -z "$files" ]; then
            echo "⏭️  No proto files to lint"
            exit 0
          fi

          echo "Found $(echo "$files" | wc -l) proto files to check"

          # Setup dependencies in parallel
          mkdir -p $deps
          buf export buf.build/envoyproxy/protoc-gen-validate -o $deps 2>/dev/null &
          buf export buf.build/googleapis/googleapis -o $deps 2>/dev/null &
          wait

          # Run linter
          if api-linter \
            --set-exit-status \
            --config $config_path \
            -I $deps \
            -I . \
            $files; then
            echo "✅ Proto lint passed"
            rm -rf $deps
          else
            echo "❌ Proto lint failed"
            rm -rf $deps
            exit 1
          fi

  go-lint:
    name: Go Lint
    runs-on: ubuntu-24.04
    container:
      image: ghcr.io/moegolibrary/moego-ci-builder:latest
      credentials:
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup git for private modules
        run: |
          git config --global url."https://${{ secrets.ADMIN_TOKEN_GITHUB }}:<EMAIL>/".insteadOf "https://github.com/"
          go env -w GOPRIVATE=github.com/MoeGolibrary/*

      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Cache golangci-lint
        uses: actions/cache@v4
        with:
          path: ~/.cache/golangci-lint
          key: ${{ runner.os }}-golangci-lint-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-golangci-lint-

      - name: Run go lint
        run: |
          echo "🔍 Checking go lint..."
          if golangci-lint run -v --allow-parallel-runners --tests=false ./backend/app/...; then
            echo "✅ Go lint passed"
          else
            echo "❌ Go lint failed"
            exit 1
          fi

  bazel-gazelle:
    name: Bazel Gazelle Check
    runs-on: ubuntu-24.04
    container:
      image: ghcr.io/moegolibrary/moego-ci-builder:latest
      credentials:
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup git for private modules
        run: |
          git config --global url."https://${{ secrets.ADMIN_TOKEN_GITHUB }}:<EMAIL>/".insteadOf "https://github.com/"
          go env -w GOPRIVATE=github.com/MoeGolibrary/*

      - name: Check Bazel Gazelle
        run: |
          echo "🔍 Checking Bazel build files..."
          mkdir -p /tmp/bazel-sandbox
          if bazelisk --bazelrc=./bazel/.bazelrc run //:gazelle -- -mode=diff ./backend/app ./backend/proto ; then
            echo "✅ Bazel files are up to date"
          else
            echo "❌ Bazel files need update. Please run: make gazelle"
            exit 1
          fi
