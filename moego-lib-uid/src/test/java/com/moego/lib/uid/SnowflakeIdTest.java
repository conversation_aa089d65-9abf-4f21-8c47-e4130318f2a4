package com.moego.lib.uid;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import me.ahoo.cosid.converter.SnowflakeFriendlyIdConverter;
import me.ahoo.cosid.machine.MachineIdGuarder;
import me.ahoo.cosid.snowflake.DefaultSnowflakeFriendlyId;
import me.ahoo.cosid.snowflake.MillisecondSnowflakeIdStateParser;
import me.ahoo.cosid.snowflake.SnowflakeId;
import me.ahoo.cosid.spring.boot.starter.machine.CosIdMachineAutoConfiguration;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(classes = SnowflakeIdTest.Cfg.class)
@ActiveProfiles("test")
public class SnowflakeIdTest {

    @Autowired
    private UidGenerator uidGenerator;

    @Autowired
    private MachineIdGuarder guarder;

    /**
     * 检查配置是否正确
     */
    @Test
    public void test_properties() {
        assertTrue(uidGenerator.getShareGenerator() instanceof SnowflakeId);
        var snowflakeId = (SnowflakeId) uidGenerator.getShareGenerator();

        System.out.println("machineId: " + snowflakeId.getMachineId());

        assertEquals(42, snowflakeId.getTimestampBit());
        assertEquals(11, snowflakeId.getMachineBit());
        assertEquals(10, snowflakeId.getSequenceBit());

        assertEquals((1L << 42) - 1, snowflakeId.getMaxTimestamp());
        assertEquals((1L << 11) - 1, snowflakeId.getMaxMachine());
        assertEquals((1L << 10) - 1, snowflakeId.getMaxSequence());

        assertEquals(1672531200000L, snowflakeId.getEpoch());
        assertTrue(snowflakeId.getLastTimestamp() <= System.currentTimeMillis());

        assertTrue(snowflakeId instanceof DefaultSnowflakeFriendlyId);
        var friendlyId = (DefaultSnowflakeFriendlyId) snowflakeId;
        assertTrue(friendlyId.getParser() instanceof MillisecondSnowflakeIdStateParser);
        assertEquals("UTC", friendlyId.getParser().getZoneId().getId());

        assertTrue(friendlyId.idConverter() instanceof SnowflakeFriendlyIdConverter);
        var convertor = (SnowflakeFriendlyIdConverter) friendlyId.idConverter();
        assertTrue(convertor.getParser() instanceof MillisecondSnowflakeIdStateParser);
        assertEquals("UTC", convertor.getParser().getZoneId().getId());

        assertTrue(guarder.isRunning());
    }

    /**
     * 检查 snowflake id 转换是否正确
     */
    @Test
    public void test_snowflakeIdConverter() {
        assertTrue(uidGenerator.getShareGenerator() instanceof SnowflakeId);
        var snowflakeId = (SnowflakeId) uidGenerator.getShareGenerator();

        System.out.println("machineId: " + snowflakeId.getMachineId());

        var id = 38620335626521602L;
        var friendlyId = "20230802032651089-3-2";

        var convertor = snowflakeId.idConverter();

        assertEquals(id, convertor.asLong(friendlyId));
        assertEquals(friendlyId, convertor.asString(id));
    }

    @Test
    public void test_generator() {
        var id1 = uidGenerator.nextId();
        var id2 = uidGenerator.nextId();
        assertTrue(id1 < id2);

        var friendlyId1 = uidGenerator.nextFriendlyId();
        var friendlyId2 = uidGenerator.nextFriendlyId();
        assertTrue(friendlyId1.compareTo(friendlyId2) < 0);
    }

    @Test
    public void test_convert() {
        var id = uidGenerator.nextId();
        var friendlyId = uidGenerator.toFriendlyId(id);
        var id1 = uidGenerator.toId(friendlyId);
        assertEquals(id, id1);
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    @ImportAutoConfiguration(InitAutoConfiguration.class)
    static class Cfg {}

    @AutoConfiguration(after = SqlInitializationAutoConfiguration.class, before = CosIdMachineAutoConfiguration.class)
    static class InitAutoConfiguration {}
}
