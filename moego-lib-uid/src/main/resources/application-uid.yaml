cosid:
  enabled: true
  # 默认使用 spring 的应用名作为 namespace
  namespace: ${spring.application.name:moego}
  snowflake:
    enabled: true
    # friendly id 格式化输出时使用的时区
    zone-id: UTC
    # 2023-01-01 00:00:00 UTC
    epoch: 1672531200000
    share:
      # 42 bit 最大值为 4398046511103
      timestamp-bit: 42
      # 10 bit 最大值为 1023
      sequence-bit: 10
      converter:
        type: snowflake_friendly
      clock-sync: true
      friendly: true
      # 可以设置一个阈值，当上一秒的 sequence 大于这个阈值时，下一秒的 sequence 从 0 开始自增，而不是从上一秒的最后一个 sequence 开始自增。
      # 这个配置可以使得每一秒生成的 ID 数量是均匀的，避免下一秒可用的 sequence 不足。
      # 建议设为 ~(-1 << (sequence-bit - 1)), 即 sequence 最大值的一半
      sequence-reset-threshold: 511
  machine:
    enabled: true
    # 11 bit 最大值为 2047
    machine-bit: 11
    # machine id 分发器
    distributor:
      # 使用 jdbc 分发。如果使用 redis，改为 type: redis 即可，同时需要添加 redis 相关配置
      type: jdbc
    # 时钟发生回拨时：
    clock-backwards:
      # 如果差距小于 10ms 则自旋等待，否则线程 sleep 等待
      spin-threshold: 10
      # 如果差距大于 1000ms 则抛出时钟异常
      broken-threshold: 1000

    # 通过心跳机制守住 machine id
    guarder:
      enabled: true
      # 第一次心跳在启动过后 1 分钟开始
      initial-delay: 1m
      # 之后每隔 1 分钟上报一次心跳
      delay: 1m
      # 如果超过 60 分钟未上报心跳，machine id 可被回收（可被分配给其他机器）
      safe-guard-duration: 60m
  # 这个配置是自己定义的，官方依赖没提供这个配置
  jdbc:
    # 是否自动初始化 machine id 分配记录表
    # 前置条件是 cosid.enabled = true, cosid.machine.enabled = true, cosid.machine.distributor.type = jdbc
    auto-init: true
