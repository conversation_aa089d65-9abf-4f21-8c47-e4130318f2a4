package com.moego.lib.uid.config;

import com.moego.lib.uid.UidGenerator;
import me.ahoo.cosid.provider.IdGeneratorProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

/**
 * 自动装配 {@link UidGenerator} bean
 */
@AutoConfiguration
public class UidGeneratorAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public UidGenerator uidGenerator(IdGeneratorProvider idGeneratorProvider) {
        return new UidGenerator(idGeneratorProvider);
    }
}
