# moego-lib-uid

该模块提供了一个基于 snowflake 算法的分布式 ID 生成器 `UidGenerator`。引入该模块依赖后， `@Autowired` 这个类的 bean 即可使用，例如：
```java
@Service
public class MyService {
    @Autowired
    private UidGenerator uidGenerator;

    public void doSomething() {
        long uid = uidGenerator.nextId();
        // ...
    }
}
```

## Requirements
1. Spring boot 需要升级到 3
2. 需要自行配置数据库，目前支持 MySQL、PostgreSQL、H2（单测场景）


## Configuration

除了以下列出的配置，其他配置不应当自行更改。

### cosid.namespace
`cosid.namespace` 用于配置命名空间，区分不同的应用或业务场景。
统一取自 `spring.application.name` 的值，如果没有配置，则兜底取为 `moego`。
通常情况下不需要自行配置，除非 `spring.application.name` 这个配置不存在。

### cosid.enabled
`cosid.enabled` 用于控制是否启用该模块，默认为 `true`。
如果希望禁用该模块，可以设置为 `false`（当然也可以直接移除依赖）。

### cosid.jdbc.auto-init
`cosid.jdbc.auto-init` 用于控制是否自动初始化数据库，默认为 `true`。
如果不需要自动初始化数据库，可以设置为 `false`。
执行脚本在 `src/main/resources/sql` 目录下。
执行时会自动根据数据库驱动类型选择对应的脚本，目前只支持 PostgreSQL、MySQL、H2。
执行的前置条件是：
1. `cosid.enabled:true`
2. `cosid.machine.enabled:true`
3. `cosid.machine.distributor.type:jdbc`


### provider
同一个 namespace 下，除了全局的 provider (share provider)，还可以自定义 provider。
同名的 provider 产生的 ID 都是全局唯一的，但是不同名的 provider 产生的 ID 可能会重复。
通常情况下，每个应用 (namespace) 只用全局的 provider 即可，并且不推荐修改全局 provider 的配置。
如需自定义 provider，可在应用的 application.yaml 中添加如下配置：
```yaml
cosid:
  snowflake:
    provider:
      # 自定义 provider 名称，必须唯一
      my-provider:
        # 自定义 timestamp 位数
        timestamp-bit: 42
        # 自定义 sequence 位数
        sequence-bit: 10
        converter:
          type: snowflake_friendly
        clock-sync: true
        friendly: true
        # 可以设置一个阈值，当上一秒的 sequence 大于这个阈值时，下一秒的 sequence 从 0 开始自增，而不是从上一秒的最后一个 sequence 开始自增。
        # 这个配置可以使得每一秒生成的 ID 数量是均匀的，避免下一秒可用的 sequence 不足。
        # 建议设为 ~(-1 << (sequence-bit - 1)), 即 sequence 最大值的一半
        sequence-reset-threshold: 511
```
然后在代码中，指定 provider name 来生成 ID：
```java
@Service
public class MyService {
    @Autowired
    private UidGenerator uidGenerator;

    public void doSomething() {
        long uid = uidGenerator.nextId("my-provider");
        // ...
    }
}
```
