package com.moego.common;

import lombok.extern.slf4j.Slf4j;

/**
 * @create: 2023/3/10 10:37
 * @author: channy.shu
 **/
@Slf4j
public enum StripePaymentMethodEnum {
    CARD(1, "card"),
    COF(2, "cof"),
    BLUETOOTH_READER(3, "Bluetooth reader"),
    SMART_READER(4, "Smart reader"),
    APPLE_PAY(5, "Apple Pay"),
    GOOGLE_PAY(6, "Google Pay"),
    TTPOI(7, "Tap To Pay on iPhone"),
    ACH(8, "Bank account");

    private final Integer code;
    private final String desc;

    StripePaymentMethodEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String valueOf(Byte code) {
        if (code == null) {
            return "";
        }
        Integer c = Integer.valueOf(code);
        for (StripePaymentMethodEnum value : StripePaymentMethodEnum.values()) {
            if (value.getCode().equals(c)) {
                return value.getDesc();
            }
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
