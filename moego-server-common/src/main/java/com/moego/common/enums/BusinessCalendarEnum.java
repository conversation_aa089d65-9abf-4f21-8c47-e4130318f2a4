package com.moego.common.enums;

public interface BusinessCalendarEnum {
    Integer DEFAULT_CALENDAR_VIEW_START_AT = 5 * 60; // 9:00 am
    Integer DEFAULT_CALENDAR_VIEW_END_AT = 21 * 60; // 18:00 pm
    Integer DEFAULT_CALENDAR_MAX_TIME = 24 * 60; // 24:00 pm
    Integer DEFAULT_CALENDAR_MIN_TIME = 0; // 00:00 pm
    Byte SHOW_COLOR_CODE_DISPLAY_STRIPE_COLOR = 1; // Stripe color
    Byte SHOW_COLOR_CODE_DISPLAY_BACKGROUND_COLOR = 2; // Background Color
}
