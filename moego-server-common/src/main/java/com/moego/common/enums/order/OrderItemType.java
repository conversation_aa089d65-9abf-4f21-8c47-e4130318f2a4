package com.moego.common.enums.order;

// BitVal 左移的位数 +1 与 PB 中定义的序号对应。
public enum OrderItemType {
    ITEM_TYPE_SERVICE("service", 1),
    ITEM_TYPE_PRODUCT("product", 1 << 1),
    ITEM_TYPE_PACKAGE("package", 1 << 2),
    ITEM_TYPE_NO_SHOW("noshow", 1 << 3),
    ITEM_TYPE_SERVICE_CHARGE("service_charge", 1 << 4),
    ITEM_TYPE_EVALUATION_SERVICE("evaluation_service", 1 << 5),
    ITEM_TYPE_CANCELLATION_FEE("cancellation_fee", 1 << 6),
    ITEM_TYPE_MEMBERSHIP_PRODUCT("membership_product", 1 << 7),
    ITEM_TYPE_DEPOSIT("deposit", 1 << 8);

    private final String type;
    // 当前type二进制占位值：service占第一位，0b1 = 1，product占第2位，0b10 = 2，package占第3位，0b100 = 4，no show占第4位，0b1000 = 8，后续有扩展可以继续往后增加
    // 这个字段是用于记录order表的lineItemTypes，方便记录items的类型组合，或运算后存起来
    // 比如要查有product的订单，那么可以通过 line_item_types & 0b10 = 0b10 与运算查出
    private final Integer bitVal;

    OrderItemType(String type, Integer bitVal) {
        this.type = type;
        this.bitVal = bitVal;
    }

    public String getType() {
        return this.type;
    }

    public Integer getBitVal() {
        return this.bitVal;
    }

    public static Integer getBitVal(String type) {
        for (OrderItemType orderItemType : OrderItemType.values()) {
            if (orderItemType.getType().equals(type)) {
                return orderItemType.getBitVal();
            }
        }
        return 0;
    }

    public static boolean hasServiceItem(int val) {
        return (val & ITEM_TYPE_SERVICE.getBitVal()) == ITEM_TYPE_SERVICE.getBitVal();
    }

    public static boolean hasProductItem(int val) {
        return (val & ITEM_TYPE_PRODUCT.getBitVal()) == ITEM_TYPE_PRODUCT.getBitVal();
    }

    public static boolean hasPackageItem(int val) {
        return (val & ITEM_TYPE_PACKAGE.getBitVal()) == ITEM_TYPE_PACKAGE.getBitVal();
    }

    public static boolean hasNoShowItem(int val) {
        return (val & ITEM_TYPE_NO_SHOW.getBitVal()) == ITEM_TYPE_NO_SHOW.getBitVal();
    }

    public static boolean hasServiceChargeItem(int val) {
        return (val & ITEM_TYPE_SERVICE_CHARGE.getBitVal()) == ITEM_TYPE_SERVICE_CHARGE.getBitVal();
    }
}
