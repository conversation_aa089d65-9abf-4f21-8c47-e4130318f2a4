package com.moego.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Client profile source
 *
 * <AUTHOR>
 * @since 2023/3/6
 */
@Getter
@AllArgsConstructor
public enum ClientSourceEnum {
    SOURCE_0("0", ""),
    SOURCE_1("1", ""),
    SOURCE_2("2", ""),
    SOURCE_3("3", ""),
    SOURCE_4("4", ""),
    SOURCE_5("5", ""),
    SOURCE_MANUAL_CREATE("manual", "Business staff create in the Clients & pets list"),
    SOURCE_SELF_IMPORT("contacts", "Business import via contacts app"),
    SOURCE_DATA_IMPORT("dm", "Business contact customer support and import via script"),
    SOURCE_ONLINE_BOOKING("ob", "Created when a client submits an appointment via online booking"),
    SOURCE_INTAKE_FORM(
            "if", "The business creates the profile by collecting the Intake form and then creating the profile"),
    SOURCE_CALL_IN("call", "Client create profile by calling into the business's twilio phone"),
    SOURCE_TEXT_IN("text", "Client create profile by send message into the business's twilio phone"),
    SOURCE_UNKNOWN("unknown", "Unknown source");

    private final String source;

    private final String description;
}
