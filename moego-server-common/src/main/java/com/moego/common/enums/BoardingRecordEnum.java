package com.moego.common.enums;

/**
 * <AUTHOR>
 */
public enum BoardingRecordEnum {
    ;

    // appointment 1-50
    public static final byte TYPE_CREATE = 1;
    public static final String TYPE_RECORD_CREATE = "Boarding has been created";
    public static final byte TYPE_MOVE_TO_WAITING_LIST = 2;
    public static final byte TYPE_DELETE = 3;
    public static final String TYPE_RECORD_DELETE = "Boarding has been created";
    // BOARDING TYEP  51-80
    public static final byte TYPE_BOARDING_STATUS_CHANGE = 51;
    public static final String TYPE_BOARDING_STATUS_CHANGE_RECORD = "status has been updated to {statusName}";
    public static final byte TYPE_BOARDING_CHECK_IN = 52;
    public static final String TYPE_BOARDING_CHECK_IN_RECORD = "has been checked in";
    public static final byte TYPE_BOARDING_CHECK_OUT = 53;
    public static final String TYPE_BOARDING_CHECK_OUT_RECORD = "has been checked out";
    public static final byte TYPE_BOARDING_UPDATE_TIME = 54;
    public static final String TYPE_BOARDING_UPDATE_TIME_RECORD = "has been rescheduled to {fromTime}-{toTime}";
}
