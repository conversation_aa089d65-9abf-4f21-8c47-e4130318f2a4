package com.moego.common.enums;

/**
 * <AUTHOR>
 */
public interface BooleanEnum {
    Byte IS_AVAILABLE_TRUE = 1;
    Byte IS_AVAILABLE_FALSE = 0;
    Byte INACTIVE_TRUE = 1;
    Byte INACTIVE_FALSE = 0;
    Byte VALUE_TRUE = 1;
    Byte VALUE_FALSE = 0;
    Byte VALUE_DELETED = 2;
    /**
     * gallery
     */
    Byte IS_STAR_TRUE = 1;

    Byte IS_STAR_FALSE = 0;
    String HAS_TRUE = "Yes";
    String HAS_FALSE = "No";
}
