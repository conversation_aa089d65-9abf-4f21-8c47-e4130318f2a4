package com.moego.common.enums;

public interface BusinessCustomerConst {
    // share appt 状态  0 all 1 unconfirm 2confirm 4 finished
    Byte SHARE_APPT_STATUS_ALL = 0;
    Byte SHARE_APPT_STATUS_UNCONFIRM = 1;
    Byte SHARE_APPT_STATUS_CONFIRM = 2;
    Byte SHARE_APPT_STATUS_FINISHED = 3;

    // 0 all  1 in x days  2 next x appointment  3 manually apptids
    Byte SHARE_RANGE_TYPE_ALL = 0;
    Byte SHARE_RANGE_TYPE_NEXT_DAYS = 1;
    Byte SHARE_RANGE_TYPE_NEXT_APPT = 2;
    Byte SHARE_RANGE_TYPE_MANUALLY = 3;
    // share appointment max limit
    Integer MAX_SHARE_RANGE_LIMIT = 1000;
    Integer MAX_SHARE_RANGE_MAX_DAY = 1000;

    // client source
    String SOURCE_CREATE_BY_OB = "ob";
    String SOURCE_CREATE_BY_IF = "if";
    String SOURCE_CREATE_BY_SMS = "sms";
    String SOURCE_CREATE_BY_CALL = "call";
    String SOURCE_CREATE_BY_DATA_FILE = "file";
    String SOURCE_CREATE_BY_PHONE_CONTACT = "contact";
    String SOURCE_ADD_MANUALLY = "manual";

    String DEFAULT_LAST_NAME_CALL = "by_call";
    String DEFAULT_LAST_NAME_SMS = "by_sms";

    byte FREQUENCY_TYPE_BY_DAY = 0;
    byte FREQUENCY_TYPE_BY_WEEK = 1;
    byte FREQUENCY_TYPE_BY_MONTH = 2;
}
