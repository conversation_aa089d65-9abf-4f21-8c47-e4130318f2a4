package com.moego.common.enums;

/**
 * <AUTHOR>
 */
public interface PhoneNumberTypeConst {
    String PHONE_NUMBER_TYPE_MOBILE = "mobile";
    String PHONE_NUMBER_PLUS_CHAR = "+";
    String PHONE_NUMBER_TYPE_LANDLINE = "landline";
    String PHONE_NUMBER_TYPE_VOIP = "voip";
    String PHONE_NUMBER_TYPE_OTHER = "other";
    /**
     * twilio返回的数据内，需要获取的key-type
     */
    String TWILIO_RETURN_KEY_TYPE = "type";
    /**
     * 加拿大无法查询号码类型
     */
    String CANADA_NO_PHONE_NUMBER_TYPE = "CA";
    /**
     * 号码无法发送的提示内容
     */
    String ERROR_MESSAGE =
            "The recipient's contact is a landline/VoIP number. Contact support to correct if it is a false alert.";
}
