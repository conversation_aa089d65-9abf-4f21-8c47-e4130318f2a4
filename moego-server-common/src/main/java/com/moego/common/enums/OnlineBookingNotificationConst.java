package com.moego.common.enums;

/**
 * 通知模版常量
 *
 * <AUTHOR>
 * @see <a>https://shimo.im/docs/6kGwRt3tPWRdH9vt</a>
 */
public interface OnlineBookingNotificationConst {
    String TAG_CLIENT_NAME = "{customerName}";
    String TAG_STAFF_NAME = "{staffName}";
    String TAG_APPT_STAFF_NAME = "{appointmentStaffName}";
    String TAG_CLIENT_FIRST_NAME = "{customerFirstName}";
    String TAG_CLIENT_FIRST_NAME_NEW = "{clientFirstName}";
    String TAG_CLIENT_LAST_NAME = "{clientLastName}";
    String TAG_CLIENT_PRIMARY_ADDRESS = "{clientPrimaryAddress}";
    String TAG_CLIENT_ADDRESS = "{clientAddress}";
    String TAG_BUSINESS_NAME = "{storeName}";
    String TAG_BUSINESS_EMAIL = "{businessEmail}";
    String TAG_BUSINESS_PHONE_NUMBER = "{businessPhoneNumber}";

    /**
     * @see <a href="https://moego.atlassian.net/browse/ERP-4791">ERP-4791</a>
     * @deprecated by Freeman since 2023/7/31,
     * use {@link #TAG_APPOINTMENT_DATE} & {@link #TAG_APPOINTMENT_TIME} instead.
     */
    @Deprecated
    String TAG_TIME = "{time}";

    String TAG_APPOINTMENT_DATE = "{appointmentDate}";
    String TAG_APPOINTMENT_TIME = "{appointmentTime}";
    String TAG_PET_SERVICES = "{pet&Service}";
    String TAG_AGREEMENT_SIGN = "{AgreementStatus}";
    String TAG_AGREEMENT_SIGN_EMAIL_TEXT = "Agreement: Signed";
    String TAG_AUTO_MOVE_WAIT = "{autoMoveWaitListTips}";
    String TAG_AUTO_MOVE_WAIT_EMAIL_TEXT =
            "Appointment from online booking will be reserved on your calendar for 48 hours. After that, the request will be moved to waiting list and the reserved schedule will be open for booking again.";

    Byte TYPE_EMAIL = 1;
    Byte TYPE_MSG = 2;
    Byte TYPE_EMAIL_AND_MSG = 3;
    Byte TYPE_NOTHING = 4;

    /**
     * message template  Customer C端短息模板
     */
    String SUBMIT_TEMPLATE =
            "Your appointment request with {storeName} has been received and pending for approval. We will notify you once accepted.";

    String ACCEPT_TEMPLATE =
            "Your appointment request with us on {appointmentDate} {appointmentTime} has been accepted. We look forward to seeing you soon!";
    String AUTO_MOVE_TEMPLATE =
            "Your appointment request with {storeName} has been added to waitinglist. Please call the business if you have any questions.";
    String DECLINE_TEMPLATE =
            "Your appointment request with {storeName} has been declined. Please call the business if you have any questions.";
    /**
     * email Customer C端邮件模版
     */
    /**
     * email subject and content
     */
    String SUBMIT_EMAIL_SUBJECT_CLIENT_TEMPLATE = "Appointment request pending from {storeName}";

    String SUBMIT_EMAIL_CONTENT_CLIENT_TEMPLATE = "Hello {customerName},\n\n"
            + "Thank you for submitting your appointment request with us. You will receive a confirmation from us once your appointment is confirmed. \n"
            + "Time: {appointmentDate} {appointmentTime}\n"
            + "Pets & Services:\n"
            + "{pet&Service}\n"
            + "Groomer: {staffName}\n"
            + "Owner: {customerName}\n"
            + "Store name: {storeName}\n"
            + "Store contact: {businessPhoneNumber}\n"
            + "Store email: {businessEmail}\n"
            + "\n"
            + "Thanks,\n"
            + "{storeName}\n"
            + "\n"
            + "If you have any questions, please contact ({businessPhoneNumber}). Please do not reply to this email, as we are not able to respond to messages sent to this address.";
    String ACCEPT_EMAIL_SUBJECT_CLIENT_TEMPLATE =
            "Congratulations! Your appointment request is accepted from {storeName}";
    String ACCEPT_EMAIL_CONTENT_CLIENT_TEMPLATE =
            "Hello {customerName},\n\n" + "Your appointment request with us is accepted. \n"
                    + "Time: {appointmentDate} {appointmentTime}\n"
                    + "Pets & Services:\n"
                    + "{pet&Service}\n"
                    + "Groomer: {staffName}\n"
                    + "Owner: {customerName}\n"
                    + "Store name: {storeName}\n"
                    + "Store contact: {businessPhoneNumber}\n"
                    + "Store email: {businessEmail}\n"
                    + "\n"
                    + "Looking forward to seeing you soon!\n"
                    + "\n"
                    + "{storeName}\n"
                    + "\n"
                    + "If you have any questions, please contact ({businessPhoneNumber}). Please do not reply to this email, as we are not able to respond to messages sent to this address.";
    String AUTO_MOVE_EMAIL_SUBJECT_CLIENT_TEMPLATE =
            "Your appointment request with {storeName} has been added to waitinglist";
    String AUTO_MOVE_EMAIL_CONTENT_CLIENT_TEMPLATE =
            "Hello {customerName},\n\n" + "Your appointment request with {storeName} has been added to waitinglist. \n"
                    + "\n"
                    + "Please call the business if you have any questions.\n"
                    + "Business number: {businessPhoneNumber}\n"
                    + "\n"
                    + "Your appointment detail:\n"
                    + "Time: {appointmentDate} {appointmentTime}\n"
                    + "Pets & Services: \n"
                    + "{pet&Service}\n"
                    + "Groomer: {staffName}\n"
                    + "Owner: {customerName}\n"
                    + "Address: {clientPrimaryAddress}\n"
                    + "Store name: {storeName}\n"
                    + "{AgreementStatus}\n"
                    + "\n"
                    + "Thanks,\n"
                    + "{storeName}\n"
                    + "\n"
                    + "If you have any questions, please contact ({businessPhoneNumber}). Please do not reply to this email, as we are not able to respond to messages sent to this address.";
    String DECLINE_EMAIL_SUBJECT_CLIENT_TEMPLATE = "Your appointment request with {storeName} has been declined";
    String DECLINE_EMAIL_CONTENT_CLIENT_TEMPLATE =
            "Hello {customerName},\n\n" + "Your appointment request with {storeName} has been declined. \n"
                    + "\n"
                    + "Please call the business if you have any questions.\n"
                    + "Business number: {businessPhoneNumber}\n"
                    + "\n"
                    + "Your appointment detail:\n"
                    + "Time: {appointmentDate} {appointmentTime}\n"
                    + "Pets & Services: \n"
                    + "{pet&Service}\n"
                    + "Groomer: {staffName}\n"
                    + "Owner: {customerName}\n"
                    + "Address: {clientPrimaryAddress}\n"
                    + "Store name: {storeName}\n"
                    + "{AgreementStatus}\n"
                    + "\n"
                    + "Thanks,\n"
                    + "{storeName}\n"
                    + "\n"
                    + "If you have any questions, please contact ({businessPhoneNumber}). Please do not reply to this email, as we are not able to respond to messages sent to this address.";

    /**
     * email subject and content  商家固定邮件模板
     */
    String SUBMIT_EMAIL_SUBJECT_TEMPLATE = "New appointment request -MoeGo Online Booking";

    String SUBMIT_EMAIL_CONTENT_TEMPLATE =
            "You have a new appointment request from online booking. Please login from MoeGo app to respond to this request.\n"
                    + "{autoMoveWaitListTips}\n"
                    + "Time: {appointmentDate} {appointmentTime}\n"
                    + "Pet&Service:\n"
                    + "{pet&Service}\n"
                    + "Groomer: {staffName}\n"
                    + "Owner: {customerName}\n"
                    + "Address: {clientPrimaryAddress}\n"
                    + "Store name: {storeName}\n"
                    + "{AgreementStatus}\n";
    String BOOKING_REQUEST_SUBMIT_EMAIL_CONTENT_TEMPLATE =
            """
        You have a new appointment request from online booking. Please login from MoeGo app to respond to this request.

        Time: {appointmentDate}
        Pet&Service: {pet&Service}
        Owner: {customerName}
        Store name: {storeName}
        {AgreementStatus}
        """;
    String ACCEPT_EMAIL_SUBJECT_TEMPLATE = "You have accepted a booking request";
    String ACCEPT_EMAIL_CONTENT_TEMPLATE = "This email confirms that the booking request below has been accepted.\n"
            + "Time: {appointmentDate} {appointmentTime}\n"
            + "Pets & Services: \n"
            + "{pet&Service}\n"
            + "Groomer: {staffName}\n"
            + "Owner: {customerName}\n"
            + "Address: {clientPrimaryAddress}\n"
            + "Store name: {storeName}\n"
            + "{AgreementStatus}";
    String AUTO_MOVE_EMAIL_SUBJECT_TEMPLATE = "You have added an appointment to waitinglist";
    String AUTO_MOVE_EMAIL_CONTENT_TEMPLATE =
            "This email confirms that the appointment below has been added to waitinglist. You can move this appointment back to calendar anytime from the waitinglist. \n"
                    + "Time: {appointmentDate} {appointmentTime}\n"
                    + "Pets & Services: \n"
                    + "{pet&Service}\n"
                    + "Groomer: {staffName}\n"
                    + "Owner: {customerName}\n"
                    + "Address: {clientPrimaryAddress}\n"
                    + "Store name: {storeName}\n"
                    + "{AgreementStatus}";
    String DECLINE_EMAIL_SUBJECT_TEMPLATE = "You have declined a booking request";
    String DECLINE_EMAIL_CONTENT_TEMPLATE =
            "This email confirms that the appointment below has been declined. Time: {appointmentDate} {appointmentTime}\n"
                    + "Pets & Services: \n"
                    + "{pet&Service}\n"
                    + "Groomer: {staffName}\n"
                    + "Owner: {customerName}\n"
                    + "Address: {clientPrimaryAddress}\n"
                    + "Store name: {storeName}\n"
                    + "{AgreementStatus}";

    String OB_MESSAGE_NO_TIME_TIPS_DISABLE = " no time selected ";
    String OB_MESSAGE_NO_DATE_SELECTED = " pending date ";
    String OB_MESSAGE_NO_TIME_SELECTED = " pending time ";
    String OB_MESSAGE_NO_TIME_TIPS_OUT_OF_AREA = " no time selected due to out of service area ";
}
