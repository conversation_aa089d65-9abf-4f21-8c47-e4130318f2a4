package com.moego.common.enums;

public interface PayrollConst {
    // service commission type: 1-fixed rate, 2-tier rate
    Byte SERVICE_COMMISSION_TYPE_FIXED_RATE = 1;
    Byte SERVICE_COMMISSION_TYPE_TIER_RATE = 2;

    Byte TIER_TYPE_SLIDING_SCALE = 1;
    Byte TIER_TYPE_PROGRESSIVE = 2;

    // service commission based type
    Byte COMMISSION_BASED_ACTUAL_PAYMENT = 1;
    Byte COMMISSION_BASED_FINISH_EXPECTED = 2;
}
