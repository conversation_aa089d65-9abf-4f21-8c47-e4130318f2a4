package com.moego.common.enums;

public interface FeatureConst {
    Byte ENABLE_TRUE = 1;
    Byte ENABLE_FALSE = 0;
    Integer DEFAULT_QUOTA = 0;
    Integer MIN_QUOTA = 0;
    Integer MAX_QUOTA = -1;
    Boolean DEFAULT_ENABLE = false;
    Byte IS_DELETED_TRUE = 1;
    Byte IS_DELETED_FALSE = 0;

    // default feature code
    String FC_AUTO_MESSAGE = "autoMessage";
    String FC_THREE_CONFIRM_REMINDER = "confirmReminder";
    String FC_PICK_UP = "pickUp";
    String FC_SMART_SCHEDULING = "ss";
    String FC_SMART_SCHEDULING_FOR_REPEAT = "ssForRepeat";
    String FC_STRIPE = "stripe";
    String FC_SQUARE = "square";
    String FC_DIGITAL_AGREEMENT = "agreement";
    String FC_INTAKE_FORM = "intakeForm";
    String FC_ONLINE_BOOKING = "onlineBooking";
    String FC_AUTO_REPLY = "autoReply";
    String FC_TWO_WAY = "twoWay";
    String FC_MASS_TEXT = "massText";
    String FC_REVIEW_BOOSTER = "reviewBooster";
    String FC_PAYROLL = "payroll";
    String FC_PAYMENT = "payment";
    String FC_REPORT_LEADERBOARD = "leaderBoard";
    String FC_REPORT_NORMAL = "reportNormal";
    String FC_ETA = "eta";
    String FC_OB_BY_BREED = "obByBreed";
    String FC_NEARBY = "nearby";
    String FC_CERTAIN_AREA_FOR_CERTAIN_DAYS = "cacd";
    String FC_ARRIVAL_WINDOW = "arrivalWindow";
    String FC_ROUTE_OPTIMIZATION = "routeOptimization";
    String FC_BIRTHDAY_REMINDER = "birthdayReminder";
    String FC_REBOOK_REMINDER = "rebookReminder";
    String FC_REPEATAPPT = "repeatAppt";
    String FC_WAITINGLIST = "waitingList";
    String FC_GOOGLE_CALENDAR = "googleCalendar";
    String FC_QUICKBOOK = "quickBook";
    String FC_STRIPE_READER = "stripeReader";
    String FC_CALL_FORWARDING = "callForwarding";
    String FC_MAP_VIEW = "mapView";
    String FC_OB_BY_SLOT = "obBySlot";
    String FC_PROCESS_FEE = "processFee";
    String FC_OB_DEPOSIT = "obDeposit";
    String FC_RETAIL = "retail";
    String FC_PACKAGE = "package";
    String FC_GROOMING_REPORT_LV1 = "groomingReportLv1";
    String FC_GROOMING_REPORT_LV2 = "groomingReportLv2";
    String FC_GROOMING_REPORT_LV3 = "groomingReportLv3";
    String FC_SERVICE_CHARGE = "serviceCharge";
    String FC_EXPIRY_REMINDER = "expiryReminder";
    String FC_VAN_CURD = "vanCURD";
    String FC_MULTI_VAN = "multiVan";
    String FC_UNLIMITED_AUTO = "unlimitedAuto";
    String FC_ABANDONED_BOOKINGS = "abandonedBookings";
    String FC_DISCOUNT_CODE = "discountCode";
    String FC_SCHEDULED_MESSAGE = "scheduledMessage";

    // OB landing page 3.0, New plan features
    String FC_BRANDED_URL = "brandedUrl";
    String FC_QR_CODE = "qrCode";
    String FC_SHOWCASE = "showcase";
    String FC_AMENITIES = "amenities";
    String FC_GOOGLE_ANALYTICS = "googleAnalytics";
    String FC_GOOGLE_RESERVE = "googleReserve";
    String FC_PAYOUT_T1_WHITE_LIST = "payoutT1WhiteList";
    String FC_SERVICE_AREA = "serviceArea";
    String FC_OB_CONFIG_TEAMS = "obConfigTeams";
    String FC_OB_CONFIG_CLIENT_REVIEWS = "obConfigClientReviews";
    String FC_ABANDONED_SCHEDULE_MESSAGE = "abandonedScheduleMessage";
    String FC_INSIGHTS_BASIC = "insightsBasic";
    String FC_BOOK_BY_SLOT = "bookBySlot";
    /*
    旧套餐中，付费会员一定都有的feature
     */
    String[] OLD_PRICE_ALLOW_ARRAY = {
        FC_MAP_VIEW,
        FC_NEARBY,
        FC_ARRIVAL_WINDOW,
        FC_VAN_CURD,
        FC_SMART_SCHEDULING,
        FC_ROUTE_OPTIMIZATION,
        FC_CERTAIN_AREA_FOR_CERTAIN_DAYS,
        FC_MULTI_VAN,
        FC_OB_BY_SLOT,
        FC_REPEATAPPT,
        FC_ONLINE_BOOKING,
        FC_INTAKE_FORM,
        FC_WAITINGLIST,
        FC_AUTO_MESSAGE,
        FC_THREE_CONFIRM_REMINDER,
        FC_ETA,
        FC_REBOOK_REMINDER,
        FC_BIRTHDAY_REMINDER,
        FC_EXPIRY_REMINDER,
        FC_PICK_UP,
        FC_AUTO_REPLY,
        FC_STRIPE,
        FC_SQUARE,
        FC_DIGITAL_AGREEMENT,
        FC_PROCESS_FEE,
        FC_REPORT_NORMAL,
        FC_RETAIL,
        FC_GOOGLE_CALENDAR,
        FC_QUICKBOOK,
        FC_PACKAGE,
        FC_GROOMING_REPORT_LV1,
        FC_PAYOUT_T1_WHITE_LIST,
        FC_INSIGHTS_BASIC,
    };
}
