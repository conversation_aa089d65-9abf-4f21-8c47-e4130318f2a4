package com.moego.common.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <a href="https://stripe.com/docs/api/invoices/object#invoice_object-billing_reason">Invoice billing_reason</a>,
 * <AUTHOR>
 * @since 2022/8/1
 */
@Getter
@AllArgsConstructor
public enum InvoiceBillingReasonEnum {
    SUBSCRIPTION_CYCLE((byte) 1, "subscription_cycle"),
    SUBSCRIPTION_CREATE((byte) 2, "subscription_create"),
    SUBSCRIPTION_UPDATE((byte) 3, "subscription_update"),
    SUBSCRIPTION((byte) 4, "subscription"),
    MANUAL((byte) 5, "manual"),
    UPCOMING((byte) 6, "upcoming"),
    SUBSCRIPTION_THRESHOLD((byte) 7, "subscription_threshold"),
    AUTOMATIC_PENDING_INVOICE_ITEM_INVOICE((byte) 8, "automatic_pending_invoice_item_invoice"),
    QUOTE_ACCEPT((byte) 9, "quote_accept");

    private final Byte code;

    private final String reason;

    public static Byte getCodeByReason(String billingReason) {
        for (InvoiceBillingReasonEnum billingReasonEnum : values()) {
            if (Objects.equals(billingReasonEnum.getReason(), billingReason)) {
                return billingReasonEnum.getCode();
            }
        }
        return 0;
    }

    public static List<Byte> getValidSubscriptionReason() {
        return Arrays.asList(SUBSCRIPTION_CYCLE.code, SUBSCRIPTION_CREATE.code);
    }
}
