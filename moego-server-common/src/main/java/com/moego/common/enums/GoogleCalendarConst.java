package com.moego.common.enums;

/**
 * <AUTHOR>
 */
public interface GoogleCalendarConst {
    // 开关状态 1打开  2关闭
    Byte SETTING_STATUS_NORMAL = 1;
    Byte SETTING_STATUS_EXPIRE = 2;
    // 设置 同步类型
    Byte SETTING_SYNC_TYPE_ALL = 1;
    Byte SETTING_SYNC_TYPE_ONLY_EXPORT = 2;
    Byte SETTING_SYNC_TYPE_ONLY_IMPORT = 3;
    // 授权状态 1正常  2用户主动取消  3过期失效
    Byte AUTH_STATUS_NORMAL = 1;
    Byte AUTH_STATUS_CANCEL = 2;
    Byte AUTH_STATUS_EXPIRE = 3;
    // 默认token type
    String DEFAULT_TOKEN_TYPE = "Bearer";
    // google calendar更新授权时 默认scope
    String DEFAULT_CALENDAR_SCOPE = "https://www.googleapis.com/auth/calendar";
    // 相比过期时间，提前多少秒刷新accessToken
    Integer BEFORE_TIME = 360;
    // auth被使用，需要限制在24小时内
    Integer AUTH_USE_TIME_LIMIT = 24 * 3600;
    // calendar status 检查的时间间隔，每 24h 检查一次
    Integer CALENDAR_CHECK_INTERVAL = 24 * 3600;

    // calendar状态
    Byte CALENDAR_STATUS_NORMAL = 1;
    Byte CALENDAR_STATUS_DELETE = 2;
    // event 状态
    Byte EVENT_STATUS_NORMAL = 1;
    Byte EVENT_STATUS_DELETE = 2;
    // appt sync_type
    Byte SYNC_TYPE_IMPORT = 1; // 从google calendar >>> moego
    Byte SYNC_TYPE_EXPORT = 2; // 从moego >>> google calendar

    // webhook watch type
    String WATCH_TYPE_WEBHOOK = "webHook";

    Byte TASK_STATUS_START = 1;
    Byte TASK_STATUS_COMPLETE = 2;
    Byte TASK_STATUS_FAILED = 3;
    /**
     * web hook过期预留时间，当过期时间剩余24小时时，获取新的web hook
     */
    long WEB_HOOK_EXPIRED_TIME_BEFORE_SECOND = 3600 * 24;
    // 任务失败截止时间(10s不更新则默认失败)
    Integer TASK_FAIL_TIME_SECOND = 60;

    // %s是syncedStaffId
    String REDIS_KEY_FORMAT_STR = "gcBid_%s";
    /**
     * google calendar同步失败的key
     */
    String REDIS_KEY_FORMAT_STR_ERROR = "gcEBid_%s";
    /**
     * google calendar正在同步的key
     */
    String REDIS_KEY_FORMAT_STR_SYNCING = "gcSyncIngBid_%s";
    /**
     * google calendar 每小时同步量
     * gcSyncGcCalendar_{id}_{month}_{day}_{24hour}
     */
    String REDIS_KEY_FORMAT_STR_API_LIMIT = "gcSyncGcCalendar_%s_%s_%s_%s";

    Integer GOOGLE_CALENDAR_API_LIMIT_BY_HOUR = 1000;
    Long GOOGLE_CALENDAR_REDIS_KEY_EXPIRE_TIME = 7L;

    Long LOCK_MAX_SECONDS = 6 * 60 * 3600L;
}
