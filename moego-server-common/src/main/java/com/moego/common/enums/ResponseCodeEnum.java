package com.moego.common.enums;

/**
 * 响应码规划说明
 * 0-999为基础，任何子系统可公用，错误码，基本于http状态码保持一致
 * 业务错误码，按业务分，先定义服务模块，
 * 举例，业务模块一 1000-1999，业务模块二 2000-2999
 * 业务模块的自模块功能，在大范围内，再次分段
 * 业务模块一中的子模块一：1000-1099 子模块二 1100-1199
 * moego 业务error定义如下：
 * <p>
 * moego-common   [0,1000)
 * <p>
 * moego-boarding [10000,20000)
 * <p>
 * moego-business [20000,30000)
 * <p>
 * moego-customer [30000,40000)
 * <p>
 * ob [40000,50000)
 * <p>
 * moego-grooming [50000,60000)
 * <p>
 * moego-retail [60000,70000)
 * <p>
 * moego-payment [70000, 80000)
 * <p>
 * moego-message [80000, 90000)
 *
 * @deprecated since 2022/11/14 by <PERSON>, use {@link com.moego.idl.models.errors.v1.Code} instead.
 */
@Deprecated
public enum ResponseCodeEnum {
    SUCCESS(200, "Operation is successful !"),
    PARAMS_ERROR(400, "params error"),
    UNAUTHORIZED_ERROR(401, "no auth"),
    FORBIDDEN(403, "Forbidden"),
    TOO_MANY_REQUESTS(429, "Too many requests, please try again later."),
    SERVER_ERROR(500, "server error"),

    // moego-boarding error code start 10000 end 20000
    // boarding is not used so far , so this is also used as Common Error Code
    ORDERID_EXISTS(10010, "Order id already exists"),
    ROOM_ERROR(10015, "Room is not available"),
    PRIMARY_ID_EMPTY(10016, "primary id is empty"),
    VERIFY_CODE_NOT_EQUAL(10017, "No access right."),
    PARALLEL_ERROR(10018, "Concurrent error: refresh to get the updated status. "),

    // moego-business error code start 20000 end 30000
    CHECK_IN_ERROR(20001, "Check in is not allowed"),
    CHECK_OUT_ERROR(20002, "Check out is not allowed"),
    PASSWORD_ERROR(20003, "Account or password error"),
    ACCOUNT_NOT_FOUND(20004, "Account not found"),
    INVALID_CODE(20005, "Invalid Code"),
    STAFF_BINDING_ERROR1(20006, "The invite code has been bound"),
    STAFF_BINDING_ERROR2(20007, "The Business does not enable the login switch"),
    STAFF_BINDING_ERROR3(20008, "Invalid Invite Code"),
    STAFF_BINDING_ERROR4(20009, "Account already exists in this business"),
    DELETE_ROLE_ERROR(20010, "Some staff have this role ,can not delete"),
    PAYMENT_METHOD_NAME_EXISTS(20011, "method already exists"),
    DEFAULT_METHOD_OPER_FALSE(20012, "The default method cannot be modified"),
    FILE_SIZE_EXCEEDS_LIMIT(20013, "File size exceeds limit"),
    TAX_IS_USED(20014, "Cannot delete. There are services associated with this tax."),
    TAG_NAME_EXIST(20015, "Tag name is already in use"),
    TAG_NAME_NULL(20016, "Tag name is null"),
    THE_LAST_DELETED(20017, "The last payment method cannot be deleted"),
    THE_LAST_INACTIVE(20018, "The last payment method cannot be closed"),
    PERMISSION_NOT_ENOUGH(20019, "No permission to complete the operation"),
    NO_PERMISSION_SHOW_MESSAGE_THREAD(20020, "No permission to view customer chat"),
    TAX_IS_USED_RETAIL(20021, "Cannot delete. There are products or packages associated with this tax."),
    NOT_ENOUGH_VANS_NUM(20022, "can't create new staff in this mobile van."),
    PASSWORD_UPDATE_OLDPWD_ERROR(20023, "Old password error."),
    ACCOUNT_NOT_OWN_THIS_COMPANY(20024, "this account does not own current company"),
    CHECK_EMAIL_EXIST_PHP_VERSION(20025, "Email was found in Moego 1.0"),
    COMPANY_NOT_FOUND(20026, "The company does not exist."),
    NOT_ENOUGH_VANS_NUM2(20027, "can't create new van, need upgrade"),
    STAFF_ONLY_BANDING_ONE_VAN(20028, "Staff can only be bound to one van, please refresh and try again."),
    STAFF_NOT_FOUND_IN_FROM_VAN(20029, "Staff record not found in from van"),
    STAFF_FOUND_IN_TO_VAN(20030, "Staff record was found in to van"),
    PROCESSING_FEE_SHOULD_IN_STRIPE_PAY(
            20031, "This feature is for Stripe only. Please set Stripe as primary credit card provider."),
    PROCESSING_FEE_SHOULD_IN_US_AREA(
            20032, "Passing credit card processing fees to clients is not available in this area."),
    STAFF_UNLINK_REASON_OWNER(20033, "Business Owner cannot be unlink"),
    STAFF_UNLINK_REASON_NO_LINK(20034, "This staff is not linked to an account"),
    STAFF_UNLINK_REASON_HAS_BEEN_DELETED(20035, "Staff has been deleted"),
    CAN_NOT_CREATE_STAFF(20036, "The current plan cannot create more staff."),

    // moego-customer error code start 30000 end 40000
    EMAIL_NOT_EXIST(30000, "Email not exist"),
    EMAIL_EXIST(30001, "Email is already in use"),
    PHONE_EXIST(30002, "Phone number is already in use"),
    CUSTOMER_NOT_FOUND(30003, "Customer not found"),
    PET_NOT_FOUND(30004, "Pet not found"),
    APPOINTMENT_NOT_FOUND(30005, "Appointment not found"),
    PET_HAVE_APPOINTMENT(30006, "this is pet have appointment"),
    APPOINTMENT_SIGN_OVER(30007, "Appointment sign over"),
    APPOINTMENT_PET_NOT_EXIST(30008, "Appointment pet not exits"),
    APPOINTMENT_AGREEMENT_SEND_CONTENT_NOT_CONFIG(30009, "Appointment agreement send not config"),
    PET_CODE_EXISTS(30010, "pet code is already in use"),
    EMAIL_EXISTS_MULTI(30011, "Multiple accounts are queried and cannot log in"),
    BIRTHDAY_FORMAT_ERROR(30012, "pet birthday format error"),
    CUSTOMER_IS_BLOCKED(30013, "customer is blocking."),
    CUSTOMER_ALREADY_EXISTS(30014, "customer already exists."),
    INTAKE_FORM_IS_DELETED(30015, "form has been deleted."),
    INTAKE_FORM_MESSAGE_TOO_LONG(30016, "message is too long"),
    PET_BREED_NOT_ALLOW_DELETE(30017, "At least 1 breed is required."),
    CUSTOMER_ACCOUNT_OR_PASSWORD_ERROR(30018, "Account or password error."),
    CUSTOMER_ACCOUNT_NOT_FOUND(30019, "Account not found."),
    CUSTOMER_SESSION_NOT_FOUND(30020, "Session not found."),
    CUSTOMER_INVALID_CODE(30021, "Invalid code."),
    PET_CODE_DESCRIPTION_TOO_LONG(30022, "Description should less than 50 characters."),
    EMAIL_OR_CODE_ERROR(30023, "Email or verification code error."),
    PHONE_OR_CODE_ERROR(30024, "Phone number or verification code error."),
    LICENSE_INVALID(30025, "License is invalid."),
    PHONE_INVALID(30026, "The phone number is invalid, please contact your service provider."),
    EMAIL_INVALID(30027, "The email is invalid, please contact your service provider."),

    // book online client error code start 40000 end 50000
    BOOK_ONLINE_NAME_INVALID(40404, "business name is invalid"),
    BOOK_ONLINE_NOT_ENABLE(40070, "book online is not available"),
    AGREEMENT_NOT_CONFIRM(40090, "Agreement not confirmed."),
    SIGNATURE_IS_EMPTY(40091, "Signature is empty."),
    ACCEPT_TYPE_NOW_ALLOWED(40092, "Accept client is not allowed."),
    CANCEL_POLICY_NOT_CONFIRMED(40093, "Cancellation policy not confirmed."),
    CUSTOMER_NOT_FOUND_FOR_OB(40094, "This user cannot be found, please enter the correct phone number."),
    STRIPE_CARD_TOKEN_IS_EMPTY(40095, "No credit card is supplied."),
    APPOINTMENT_TIME_IS_NOT_AVAILABLE(
            40096, "The appointment time is not available anymore. Please reselect the appointment time."),
    APPOINTMENT_CANCELED_INVOICE_INVALID(
            40097, "The grooming appointment has been cancelled and the invoice is invalid."),
    // book online deposit
    APPOINTMENT_TIME_LOCKED(40098, "Current staff's time is locked, please choose another time."),
    OB_DEPOSIT_NOT_PAID(40099, "Deposit should be paid first."),

    // moego-grooming error code start 50000 end 60000
    SERVICE_CATEGORY_NAME_IS_EXIST(51001, "Name is already in use"),
    SERVICE_NAME_IS_EXIST(51002, "Name is already in use"),
    SERVICE_CATEGORY_NOT_FOUND(51003, "No service category found"),
    INVALID_VALUE_TYPE(51004, "Value type is invalid (valid type: 'percentage', 'amount')"),
    APPLY_PACKAGE_CHANGED(51005, "Applied package is changed. Check again."),
    INVOICE_INVALID_STATUS(51006, "Invoice status is invalid."),
    SERVICE_NOT_FOUND(51007, "Service is not found."),
    APPOINTMENT_INVALID_STATUS(51008, "Appointment status is invalid."),
    SERVICE_HAVE_BINDING(51009, "Cannot delete this service, since it is associated with a future appointment."),
    NO_AVAILABLE_SERVICE_SELECTED(51010, "service is not selected correctly."),
    NO_CUSTOMER_ADDRESS(51011, "Customer address is empty."),
    TOO_MANY_DAYS_TO_QUERY(51012, "cant query multiple days when smart scheduling flag is on."),
    NO_AVAILABLE_STAFF_WHEN_SUBMIT(51013, "can not get available staff, please choose another service."),
    CREDIT_CARD_NEED_OPEN(51014, "Please open credit card payment method."),
    GOOGLE_INVALID_ADDRESS(51015, "Cannot get driving route between the locations."),
    TASK_FAILURE_GENERAL(51016, "Scheduled task failed"),
    TASK_MESSAGE_RESET_FAILURE(51017, "Message count reset task failed."),
    TOO_MANY_APPOINTMENTS(51018, "Cannot create more appointments."),
    SERVICE_CATEGORY_NAME_IS_TOO_LONG(51019, "Name is too long. (Max length is 100)"),
    REPEAT_IS_NOT_FOUND(51020, "Repeat is not found"),
    QUICKBOOKS_DEV_ERROR(51021, "quickbooks create oauth url error"),
    QUICKBOOKS_OAUTH_ERROR(51022, "quickbooks oauth error,please try again"),
    QUICKBOOKS_REFRESH_TOKEN_ERROR(51023, "quickbooks refresh token error"),
    QUICKBOOKS_UNEXPECTED_EXCEPTION(51024, "quickbooks unexpected exception"),
    QUICKBOOKS_SETTING_ERROR(51025, "quickbooks setting status error"),
    QUICKBOOKS_DATA_DUPLICATE_NAME_EXISTS(51026, "quickbooks Duplicate Name Exists Error"),
    QUICKBOOKS_DATA_DUPLICATE_EMAIL_FORMAT_ERROR(51027, "quickbooks email format error"),
    UPCOMING_ID_ERROR(51031, "Url Invalid "),
    INVOICE_SET_TIPS_ERROR(51038, "This invoice has been edited. Please refresh."),
    GROOMING_NOT_FOUND(51039, "Grooming is not found"),
    INVOICE_NOT_FOUND(51040, "Invoice is not found"),
    PET_SIZE_IN_USED(51041, "Can not delete this pet size, since it is associated with a booking limitation."),
    // 同步 api-definitions 的 error code
    GROOMING_REPORT_NOT_AVAILABLE(51042, "Grooming report is not available."),
    GROOMING_REPORT_BOOK_AGAIN_EXPIRED(51043, "Grooming report book again expired."),

    // google calendar
    GOOGLE_OAUTH_CHECK_ERROR(51028, "Google calendar sync has been disconnected. Please link again if you need."),
    GOOGLE_OAUTH_ERROR(51029, "google auth error,please try again"),
    GOOGLE_OAUTH_IS_USED(51030, "auth error, please try again."),
    CREATE_ERROR(51032, "create google calendar error."),
    WATCH_EVENT_ERROR(51033, "watch event error."),
    GET_EVENT_ERROR(51034, "get event list error."),
    SS_FOR_REPEAT_FREQUENCY_PARAMS_ERROR(51035, "before and after days could not less than frequency."),
    FREE_SS_FOR_REPEAT_TIMES_USED_OUT(51036, "free trail times of repeat with smart scheduling used out."),
    SS_FOR_REPEAT_TIMES_OVER_LIMIT(51037, "repeat times over limit."),
    UPDATE_GOOGLE_CALENDAR_ERROR(51038, "update google calendar error."),

    // moego-retail error code start 60000 end 70000
    BUSINESS_IS_EMPTY(60000, "BusinessId is required parameter"),
    SUPPLIER_NOT_FOUND(60001, "Supplier not found"),
    CATEGORY_NOT_FOUND(60002, "Category not found"),
    PACKAGE_NOT_FOUND(60003, "Package not found"),
    PRODUCT_NOT_FOUND(60004, "Product not found"),
    CART_NOT_FOUND(60005, "Cart not found"),
    NAME_IS_EXIST(60006, "Name is already in use"),
    SKU_IS_EXIST(60007, "SKU is already in use"),
    NOT_PAID(60008, "Not paid"),
    INVALID_CART_ID(60009, "cartId or itemId is invalid"),
    CART_ALREADY_PROCESSING(60010, "This cart is already started payment"),
    CART_ALREADY_COMPLETED(60011, "This cart is already invoiced"),
    INVALID_DISCOUNT_TYPE(60012, "Discount type is invalid (valid type: 'percentage', 'amount')"),
    NAME_IS_EMPTY(60013, "Name is empty"),
    STAFF_IS_EMPTY(60014, "staff id is required parameter"),
    BARCODE_IS_EXIST(60015, "Barcode is already in use"),

    // payment error code start 70000 end 80000
    PAY_AMOUNT_INVALID(70001, "Pay amount is invalid"),
    PAY_MODULE_EMPTY(70002, "Pay module is empty"),
    PAY_INVOICE_ID_EMPTY(70003, "invoiceId is empty"),
    PAY_METHOD_INVALID(70004, "Pay method is invalid"),
    PAY_DATA_INVALID(70005, "Pay data is invalid"),
    INVALID_CHECK_NUMBER(70006, "Check number is invalid"),
    REFUND_AMOUNT_INVALID(70007, "Refund amount is invalid"),
    STRIPE_INTENT_NOT_FOUND(70008, "Stripe intent is not found"),
    PAYMENT_NOT_FOUND(70009, "Payment is not found"),
    STRIPE_EXCEPTION(400, "params error"),
    STRIPE_ACCOUNT_NOT_FOUND(70011, "Please setup your bank account first."),
    SUBSCRIPTION_EXPIRATION(70012, "subscription has expired"),
    SUBSCRIPTION_NOT_EXIST(70013, "this company has no subscription"),
    COMPANY_STATE_NOT_VALID(70014, "this company permission state is invalid"),
    STRIPE_ACCOUNT_ERROR(70015, "please set up your primary credit card processor."),
    REFUSE_TO_STRIPE_SET_UP(70016, "Please contact support to set up account."),
    SUBSCRIPTION_NOT_VALID(70017, "subscription is not active(may be charge failed)"),
    REFUND_TIME_OUT(70018, "Cannot refund after 48 hours"),
    STRIPE_CARD_EXCEPTION(70019, "Credit card is invalid "),
    PAYMENT_STATUS_EXCEPTION(70020, "Payment status has been updated, please refresh."),
    SUBSCRIPTION_UPDATE_FAILED(70021, "Cannot Update subscription for failed payments."),

    STRIPE_COMPANY_CUSTOMER_NOT_FOUND(70022, "Stripe company customer is not found"),
    STRIPE_UPDATE_CUSTOMER_EXCEPTION(70023, "Update Stripe customer error"),
    DEPOSIT_HAS_PAID_EXCEPTION(70024, "This invoice has already paid deposit"),
    A2P_NOW_ALLOW_EDITING(70025, "The current state does not allow editing of the data"),
    A2P_CHARGE_FAILED(70026, "Payment failed, please check your credit card."),

    // message error code start 80000 end 90000
    MESSAGE_AUTO_TEMPLATE_NOT_CONFIG(80100, "business auto template not config "),
    MESSAGE_SEND_PHONE_IS_NULL(80101, "There is no phone number for this client."),
    MESSAGE_SEND_EMAIL_IS_NULL(80102, "There is no email for this client."),
    MESSAGE_SEND_REVIEW_IS_NULL(80103, "send message review not config"),
    MESSAGE_SEND_PHONE_FAILED(80104, "Send phone number failed."),
    MESSAGE_SEND_EMAIL_FAILED(80105, "Send email failed."),
    MESSAGE_CANNOT_DELETE_OTHER_STAFF_MESSAGE(80106, "Cannot delete other staff's message."),
    CUSTOMER_PHONE_NUMBER_ERROR(80107, "Fail to send message.Please check your phone number."),
    NOTIFICATION_TYPE_NOT_FOUND(80108, "Unknown notification type."),
    MESSAGE_AMOUNT_RUN_OUT(80109, "Bought message amount run out, no message left."),
    CODE_SEND_LIMIT(80110, "The verification code can only be sent once per minute."),
    FAIL_TO_SEND_CODE(80111, "Fail to send code."),
    BUSINESS_TWILIO_MESSAGE_NOE_MORE(80112, "No message left to send code. Please contact the business directly."),
    MESSAGE_CONTROL_NO_RECORD(80113, "No message control record found."),
    MESSAGE_SEND_FAILED(80114, "customer send message error"),
    MESSAGE_SEND_FAILED_BY_SWITCH_CLOSE(80115, "Receive appt scheduled reminders by is off for this client"),
    MESSAGE_EMAIL_RECEIVE_NOT_FOUND_BUSINESS(80116, "Email received, but last business email to customer not found."),
    NOT_ALLOW_UPDATE_TWILIO_CALL_FORWARDING(80117, "Your country does not support this setting."),
    TWILIO_QUERY_ERROR(80118, "query twilio number error. Please contact support to update setting"),
    TWILIO_SEND_MESSAGE_ERROR(80119, "failed to send the message because twilio throw an exception"),
    MESSAGE_TASK_LOCK_FAILED(80120, "failed to get redis lock"),
    MESSAGE_FILTERED(80121, ""),
    GROOMING_REPORT_SEND_FAILED(80122, "Grooming report send failed"),
    NOT_ALLOW_SUBMIT_REVIEW(80123, "Current link not allow submit review"),
    MESSAGE_CUSTOMER_OPTOUT_BLOCK(80124, "Current Customer has unsubscribed the channel information");

    private Integer code;

    private String message;

    ResponseCodeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    @Override
    public String toString() {
        return String.format("[%s][%d] Msg[%s]", this.name(), code, message);
    }
}
