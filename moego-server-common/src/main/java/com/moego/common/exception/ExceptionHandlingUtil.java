package com.moego.common.exception;

import com.moego.common.constant.CommonConstant;
import jakarta.servlet.http.HttpServletResponse;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@UtilityClass
@Slf4j
class ExceptionHandlingUtil {

    /**
     * Handle {@link CommonException}.
     *
     * @param commonException     {@link CommonException}
     * @param httpServletResponse {@link HttpServletResponse}
     * @return {@link CommonException.CommonResponse}
     */
    public static CommonException.CommonResponse handleCommonException(
            CommonException commonException, HttpServletResponse httpServletResponse) {
        log.error("Caught CommonException:", commonException);
        httpServletResponse.setHeader(
                CommonConstant.X_MOE_STATUS, commonException.getCode().toString());
        return commonException.toResponse();
    }
}
