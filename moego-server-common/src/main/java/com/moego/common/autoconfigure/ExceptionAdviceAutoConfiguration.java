package com.moego.common.autoconfigure;

import static org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication.Type.SERVLET;

import com.moego.common.exception.CommonException;
import com.moego.common.exception.ExceptionHandlerAdvice;
import com.moego.common.exception.FallbackExceptionHandlerAdvice;
import com.moego.common.exception.ValidationExceptionHandlerAdvice;
import jakarta.validation.ConstraintViolationException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * In order to be compatible, we can only add an autoconfiguration class here.
 * <p>
 * TODO(<PERSON>): remove this class after all {@link CommonException} are migrated to BizException.
 *
 * <AUTHOR>
 * @since 2022/10/12
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnWebApplication(type = SERVLET)
public class ExceptionAdviceAutoConfiguration {

    @Bean
    public ExceptionHandlerAdvice exceptionHandlerAdvice() {
        return new ExceptionHandlerAdvice();
    }

    @Bean
    public FallbackExceptionHandlerAdvice fallbackExceptionHandlerAdvice() {
        return new FallbackExceptionHandlerAdvice();
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass(ConstraintViolationException.class)
    static class ValidateConfiguration {

        @Bean
        public ValidationExceptionHandlerAdvice validationExceptionHandlerAdvice() {
            return new ValidationExceptionHandlerAdvice();
        }
    }
}
