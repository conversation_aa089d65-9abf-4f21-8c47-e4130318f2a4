package com.moego.common.params;

import com.fasterxml.jackson.annotation.JsonTypeName;
import com.moego.common.enums.PropertyEnum;
import com.moego.common.enums.filter.OperatorEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

/**
 * Single-condition filter structure
 *
 * <AUTHOR>
 * @since 2023/4/1
 */
@Builder
@JsonTypeName(value = "filter")
public record Filter(
        @Schema(description = "Filter property") PropertyEnum property,
        @Schema(description = "Filter comparison operator") OperatorEnum operator,
        @Schema(description = "Filter condition single value") String value,
        @Schema(description = "Filter condition multi value") List<@NotNull String> values)
        implements FilterStructure {}
