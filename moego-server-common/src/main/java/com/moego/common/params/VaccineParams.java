package com.moego.common.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * moe_pet_pet_vaccine_binding
 *
 *   `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 *   `pet_id` int(11) NOT NULL DEFAULT '0',
 *   `vaccine_id` int(11) NOT NULL DEFAULT '0',
 *   `expiration_date` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT '过期时间',
 *   `document_urls` varchar(1000) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '[]' COMMENT '文档url json格式',
 *   `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1 正常 2 失效',
 * <AUTHOR>
 */
@Data
public class VaccineParams {

    @Schema(description = "Existing vaccine binding id")
    private Integer vaccineBindingId;

    @Schema(description = "Vaccine source type, 1: business, 2: book online", hidden = true)
    private Byte type;

    @NotNull
    private Integer vaccineId;

    @Schema(description = "Expiration date, format: yyyy-MM-dd")
    private String expirationDate;

    @Schema(description = "Document url json format, default []")
    private String vaccineDocument;

    @Schema(description = "Document url array")
    private List<String> documentUrls;
}
