package com.moego.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Collections;
import java.util.List;
import lombok.Data;

/**
 * 所有简单list都可以通过此dto返回
 *
 * @param <T>
 */
@Data
@Schema(description = "通用list结果返回dto")
public class ListResultDto<T> {

    @Schema(description = "数据List")
    private List<T> list;

    @Schema(description = "总数量")
    private Integer count;

    @Schema(description = "当前页码")
    private Integer pageNo;

    @Schema(description = "每页最大数量")
    private Integer pageSize;

    public ListResultDto() {
        this.list = Collections.emptyList();
        this.count = 0;
        this.pageNo = 0;
        this.pageSize = 0;
    }
}
