package com.moego.common.dto.notificationDto;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class NotificationExtraOBReqestDto {

    private Integer groomingId;
    private String appointmentDate;
    private Integer appointmentStartTime;
    private Boolean noStartTime;
    private Integer appointmentEndTime;
    private Integer customerId;
    private String customerFirstName;
    private String customerLastName;
    private Integer staffId;

    private Long bookingRequestId;

    List<Service> services;

    @Data
    public static class Service {
        private Grooming grooming;
        private Boarding boarding;
        private Daycare daycare;
        private Evaluation evaluation;
    }

    @Data
    public static class Grooming {}

    @Data
    public static class Boarding {}

    @Data
    public static class Daycare {}

    @Data
    public static class Evaluation {}
}
