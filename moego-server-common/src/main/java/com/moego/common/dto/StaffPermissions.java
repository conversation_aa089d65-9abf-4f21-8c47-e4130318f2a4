package com.moego.common.dto;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class StaffPermissions {

    /**
     * 是否owner 1:是 0:否
     */
    private Byte employeeCategory;
    /**
     * owner的roleId为0
     */
    private Integer roleId;
    /**
     * 是owner list内只会包含一个 -1的值
     * idStr就是permissionIdList的json格式
     */
    private String idStr;

    private List<Integer> permissionIdList;
}
