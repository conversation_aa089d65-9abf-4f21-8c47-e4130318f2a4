package com.moego.common.dto;

import com.moego.common.utils.Pagination;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-06-27 19:18
 * @deprecated Please use {@link Pagination} instead.
 */
@Deprecated
public class PageDTO<T> implements Serializable {

    private static final long serialVersionUID = -7870044768796304513L;

    private List<T> dataList;

    // from 1
    private int pageNo;

    private int pageSize;

    private long total;

    private boolean end;

    protected PageDTO() {}

    public static <T> PageDTO<T> createEmpty() {
        return createEmpty(10);
    }

    public static <T> PageDTO<T> createEmpty(Integer pageSize) {
        PageDTO<T> pageDTO = new PageDTO<>();
        pageDTO.dataList = Collections.EMPTY_LIST;
        pageDTO.pageNo = 0;
        pageDTO.pageSize = pageSize;
        pageDTO.total = 0;
        pageDTO.end = true;
        return pageDTO;
    }

    public static <T> PageDTO<T> create(List<T> dataList, long total, int pageNo, int pageSize) {
        PageDTO<T> pageDTO = new PageDTO<>();
        pageDTO.dataList = dataList;
        pageDTO.pageNo = pageNo;
        pageDTO.pageSize = pageSize;
        pageDTO.total = total;
        if ((long) pageSize * pageNo >= total) {
            pageDTO.end = true;
        }
        return pageDTO;
    }

    public List<T> getDataList() {
        return dataList;
    }

    public void setDataList(List<T> dataList) {
        this.dataList = dataList;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public boolean isEnd() {
        return end;
    }

    public void setEnd(boolean end) {
        this.end = end;
    }
}
