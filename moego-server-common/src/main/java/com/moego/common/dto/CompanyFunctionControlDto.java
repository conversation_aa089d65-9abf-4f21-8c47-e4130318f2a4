package com.moego.common.dto;

import com.moego.common.enums.CompanyFunctionControlConst;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "company function control data object")
public class CompanyFunctionControlDto {

    /**
     * Integer PREMIUM_TYPE_FREE = 0;
     * Integer PREMIUM_TYPE_39 = 1;
     * Integer PREMIUM_TYPE_69 = 2;
     */
    @Schema(description = "付费级别 0 免费   1-39   2-69")
    private Integer premiumType;

    /**
     * 是否允许发送 two way message
     */
    @Schema(description = "是否允许发送two way")
    private Boolean allowSendTwoWay;

    /**
     * 是否允许发送 mass text
     */
    @Schema(description = "是否允许发送 mass text")
    private Boolean allowSendMassText;

    /**
     * 是否允许使用 review booster功能
     */
    @Schema(description = "是否允许发送review booster")
    private Boolean allowReviewBooster;

    /**
     * company允许的 appt 数量
     * -1 无限制
     * 正整数  限制的 appt 数量
     */
    @Schema(description = "company 允许的appt数量")
    private Integer appointmentNumber;

    @Schema(description = "是否允许smart schedule for repeat")
    private Boolean allowSmartScheduleForRepeat;

    @Schema(description = "是否允许call forwarding")
    private Boolean allowCallForwarding;

    @Schema(description = "是否启用 stripe reader")
    private Boolean enableStripeReader;

    public CompanyFunctionControlDto() {
        /**
         * 免费business 默认无法发送、无法使用相关功能，每个月也只能创建100个预约
         */
        this.premiumType = CompanyFunctionControlConst.PREMIUM_TYPE_FREE;
        this.allowSendTwoWay = false;
        this.allowSendMassText = false;
        this.allowReviewBooster = false;
        this.appointmentNumber = CompanyFunctionControlConst.APPOINTMENT_NUMBER_100;
        this.allowSmartScheduleForRepeat = false;
        this.allowCallForwarding = false;
        this.enableStripeReader = false;
    }
}
