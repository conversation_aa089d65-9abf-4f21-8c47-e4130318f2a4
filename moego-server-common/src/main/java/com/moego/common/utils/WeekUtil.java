package com.moego.common.utils;

import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import java.time.DayOfWeek;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
public class WeekUtil {

    public static final String DEFAULT_OB_STAFF_TIME =
            "{\"Monday\":{\"isSelected\":true,\"timeRange\":[{\"startTime\":540,\"endTime\":1140}]},\"Thursday\":{\"isSelected\":true,\"timeRange\":[{\"startTime\":540,\"endTime\":1140}]},\"Friday\":{\"isSelected\":true,\"timeRange\":[{\"startTime\":540,\"endTime\":1140}]},\"Sunday\":{\"isSelected\":true,\"timeRange\":[{\"startTime\":540,\"endTime\":1140}]},\"Wednesday\":{\"isSelected\":true,\"timeRange\":[{\"startTime\":540,\"endTime\":1140}]},\"Tuesday\":{\"isSelected\":true,\"timeRange\":[{\"startTime\":540,\"endTime\":1140}]},\"Saturday\":{\"isSelected\":true,\"timeRange\":[{\"startTime\":540,\"endTime\":1140}]}}";
    public static final String DEFAULT_OB_STAFF_TIME_BY_SLOT =
            "{\"Monday\":{\"isSelected\":true,\"timeSlot\":[{\"startTime\":540,\"capacity\":10}]},\"Thursday\":{\"isSelected\":true,\"timeSlot\":[{\"startTime\":540,\"capacity\":10}]},\"Friday\":{\"isSelected\":true,\"timeSlot\":[{\"startTime\":540,\"capacity\":10}]},\"Sunday\":{\"isSelected\":true,\"timeSlot\":[{\"startTime\":540,\"capacity\":10}]},\"Wednesday\":{\"isSelected\":true,\"timeSlot\":[{\"startTime\":540,\"capacity\":10}]},\"Tuesday\":{\"isSelected\":true,\"timeSlot\":[{\"startTime\":540,\"capacity\":10}]},\"Saturday\":{\"isSelected\":true,\"timeSlot\":[{\"startTime\":540,\"capacity\":10}]}}";
    public static final String KEY_OF_MONDAY = "Monday";
    public static final String KEY_OF_TUESDAY = "Tuesday";
    public static final String KEY_OF_WEDNESDAY = "Wednesday";
    public static final String KEY_OF_THURSDAY = "Thursday";
    public static final String KEY_OF_FRIDAY = "Friday";
    public static final String KEY_OF_SATURDAY = "Saturday";
    public static final String KEY_OF_SUNDAY = "Sunday";
    private static final String MONDAY = "monday";
    private static final String TUESDAY = "tuesday";
    private static final String WEDNESDAY = "wednesday";
    private static final String THURSDAY = "thursday";
    private static final String FRIDAY = "friday";
    private static final String SATURDAY = "saturday";
    private static final String SUNDAY = "sunday";

    /**
     * @see java.time.DayOfWeek#of(int)
     * The {@code int} value follows the ISO-8601 standard, from 1 (Monday) to 7 (Sunday).
     */
    public static final int INDEX_OF_MONDAY = 1;

    public static final int INDEX_OF_TUESDAY = 2;
    public static final int INDEX_OF_WEDNESDAY = 3;
    public static final int INDEX_OF_THURSDAY = 4;
    public static final int INDEX_OF_FRIDAY = 5;
    public static final int INDEX_OF_SATURDAY = 6;
    public static final int INDEX_OF_SUNDAY = 7;
    public static final int INDEX_OF_SUNDAY_MOE = 0;
    public static final int[] WEEK_ALL_INDEX = new int[] {
        INDEX_OF_MONDAY,
        INDEX_OF_TUESDAY,
        INDEX_OF_WEDNESDAY,
        INDEX_OF_THURSDAY,
        INDEX_OF_FRIDAY,
        INDEX_OF_SATURDAY,
        INDEX_OF_SUNDAY_MOE,
    };

    public static int getDayOfWeek(LocalDate day) {
        DayOfWeek index = day.getDayOfWeek();
        if (DayOfWeek.SUNDAY == index) {
            return INDEX_OF_SUNDAY_MOE;
        } else {
            return index.getValue();
        }
    }

    public static int getDayIndexOfWeek(String day) {
        switch (day.toLowerCase()) {
            case MONDAY:
                return INDEX_OF_MONDAY;
            case TUESDAY:
                return INDEX_OF_TUESDAY;
            case WEDNESDAY:
                return INDEX_OF_WEDNESDAY;
            case THURSDAY:
                return INDEX_OF_THURSDAY;
            case FRIDAY:
                return INDEX_OF_FRIDAY;
            case SATURDAY:
                return INDEX_OF_SATURDAY;
            case SUNDAY:
                return INDEX_OF_SUNDAY_MOE;
            default:
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR);
        }
    }

    public static String getKeyForWeekDay(int dayOfWeek) {
        switch (dayOfWeek) {
            case INDEX_OF_MONDAY:
                return KEY_OF_MONDAY;
            case INDEX_OF_TUESDAY:
                return KEY_OF_TUESDAY;
            case INDEX_OF_WEDNESDAY:
                return KEY_OF_WEDNESDAY;
            case INDEX_OF_THURSDAY:
                return KEY_OF_THURSDAY;
            case INDEX_OF_FRIDAY:
                return KEY_OF_FRIDAY;
            case INDEX_OF_SATURDAY:
                return KEY_OF_SATURDAY;
            case INDEX_OF_SUNDAY:
            case INDEX_OF_SUNDAY_MOE:
                return KEY_OF_SUNDAY;
            default:
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR);
        }
    }

    /**
     * 根据日期返回周几字符串
     *
     * @param date
     * @return
     */
    public static String getKeyForWeekDay(String date) {
        Integer index = getDayIndexOfWeekByDate(date);
        return getKeyForWeekDay(index);
    }

    /**
     * 根据日期返回 对应周几的索引
     * @param date 2020-12-03 周四
     * @return     4
     */
    public static Integer getDayIndexOfWeekByDate(String date) {
        LocalDate localDate = LocalDate.parse(date);
        // 与moego自定义的保持一致
        Integer dayIndex = localDate.getDayOfWeek().getValue();
        if (dayIndex == INDEX_OF_SUNDAY) {
            return 0;
        }
        return dayIndex;
    }
}
