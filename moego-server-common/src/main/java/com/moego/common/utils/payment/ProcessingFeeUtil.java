package com.moego.common.utils.payment;

import com.moego.common.dto.NewPricingLevelDto;
import com.moego.common.enums.CompanyFunctionControlConst;

/**
 * <AUTHOR>
 * @since 2022/8/30
 */
public class ProcessingFeeUtil {

    private static final ProcessingFeePair DEFAULT_ONLINE_FEE = new ProcessingFeePair(30, 0.034);
    private static final ProcessingFeePair DEFAULT_TERMINAL_FEE = new ProcessingFeePair(50, 0.029);
    public static final ProcessingFee OLD_PLAN_FEE =
            new ProcessingFee().setOnline(DEFAULT_ONLINE_FEE).setTerminal(DEFAULT_TERMINAL_FEE);
    public static final ProcessingFee T1_PLAN_FEE = OLD_PLAN_FEE;
    public static final ProcessingFee T2_PLAN_FEE = new ProcessingFee()
            .setOnline(new ProcessingFeePair(30, 0.034))
            .setTerminal(new ProcessingFeePair(50, 0.029));
    public static final ProcessingFee T3_PLAN_FEE = new ProcessingFee()
            .setOnline(new ProcessingFeePair(30, 0.034))
            .setTerminal(new ProcessingFeePair(50, 0.029));

    public static ProcessingFee getProcessFee(NewPricingLevelDto planTier) {
        if (planTier == null
                || planTier.getPlanVersion() == null
                || planTier.getPlanVersion() < CompanyFunctionControlConst.PLAN_VERSION_3) {
            return OLD_PLAN_FEE;
        }
        if (CompanyFunctionControlConst.TIER_LEVEL_THIRD.equals(planTier.getPremiumType())
                || CompanyFunctionControlConst.TIER_LEVEL_FOURTH.equals(planTier.getPremiumType())) {
            return T3_PLAN_FEE;
        } else if (CompanyFunctionControlConst.TIER_LEVEL_SECOND.equals(planTier.getPremiumType())) {
            return T2_PLAN_FEE;
        } else {
            return T1_PLAN_FEE;
        }
    }
}
