package com.moego.common.utils;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;

public class LocalizeUtil {

    private static final PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();

    /**
     * Naively remove number country code.
     *
     * @param numberWithCountryCode number with country code in E164 format
     * @return number in string with country code removed.
     * <p>
     * e.g. input +12138874590 -> output 2138874590; input +447911123456 -> output 7911123456
     */
    public static String removeE164NumberCountryCode(String numberWithCountryCode) {
        //        String number = numberWithCountryCode.replaceAll("[^+0-9]", "");  // not necessary with E.164 format
        try {
            Phonenumber.PhoneNumber parsedNumber = phoneNumberUtil.parse(numberWithCountryCode, null);
            long nationalNumber = parsedNumber.getNationalNumber();
            return String.valueOf(nationalNumber);
        } catch (NumberParseException e) {
            throw new CommonException(
                    ResponseCodeEnum.SERVER_ERROR,
                    "Failed parsing phone number: " + numberWithCountryCode + "\n" + e.getMessage(),
                    e);
        }
    }

    /**
     * Remove country code for "+1" country code number.
     *
     * @return number in string with country code removed. e.g. 2138874590
     * <p>
     * e.g. input +12138874590 -> output 2138874590
     */
    public static String naiveRemoveUSNumberCountryCode(String number) {
        if (number.startsWith("+1")) {
            return number.substring(2);
        }
        if (number.startsWith("+61")) {
            return "0" + number.substring(3);
        }
        if (number.startsWith("+44")) {
            return number.substring(3);
        }
        return number;
    }
}
