package com.moego.common.utils;

import com.moego.common.enums.BusinessConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import java.util.Objects;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2021/3/22 9:20 PM
 */
public class BusinessUtil {

    /**
     * 根据国家名字，返回固定的区域，用于确定短信条数
     * @param country
     * @return
     */
    public static Byte getAreaByCountry(String country) {
        switch (country) {
            case BusinessConst.COUNTRY_US:
            case BusinessConst.COUNTRY_US2:
                return BusinessConst.AREA_US;
            case BusinessConst.COUNTRY_CA:
            case BusinessConst.COUNTRY_CA2:
                return BusinessConst.AREA_CA;
            case BusinessConst.COUNTRY_UK:
            case BusinessConst.COUNTRY_AU:
                return BusinessConst.AREA_UK_AU;
            default:
                return BusinessConst.AREA_OTHER;
        }
    }

    public static boolean checkBusinessId(Integer aBusinessId, Integer bBusinessId) {
        if ((aBusinessId == null && bBusinessId == null) || !Objects.equals(aBusinessId, bBusinessId)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR);
        }
        return true;
    }

    /**
     * business appType 转换为 businessMode
     *
     * @param appType
     * @return
     */
    public static Byte getBusinessMode(Byte appType) {
        if (appType == null) {
            return null;
        }
        if (BusinessConst.BIZ_MODE_SALON.equals(appType)) {
            return BusinessConst.BIZ_MODE_SALON;
        } else {
            return BusinessConst.BIZ_MODE_MOBILE;
        }
    }

    public static String getAppType(Byte appType) {
        if (BusinessConst.APP_TYPE_MOBILE.equals(appType)) {
            return "Mobile Grooming";
        } else if (BusinessConst.APP_TYPE_SALON.equals(appType)) {
            return "Grooming Salon";
        } else if (BusinessConst.APP_TYPE_HYBRID.equals(appType)) {
            return "Hybrid";
        } else {
            return "Unknown";
        }
    }

    public static String getCompanyType(Integer salonNum, Integer vanNum) {
        if (salonNum != null && salonNum > 0 && vanNum != null && vanNum > 0) {
            return "Salon and mobile";
        } else if (vanNum != null && vanNum > 0) {
            return "Mobile grooming";
        } else {
            return "Grooming salon";
        }
    }

    public static String getLocationVans(String location, String van) {
        boolean locationIsEmpty = !StringUtils.hasText(location);
        boolean vanIsEmpty = !StringUtils.hasText(van);
        if (!locationIsEmpty && !vanIsEmpty) {
            return String.format("location: %s, van: %s", location, van);
        } else if (!locationIsEmpty) {
            return String.format("location: %s", location);
        } else if (!vanIsEmpty) {
            return String.format("van: %s", van);
        } else {
            return "";
        }
    }
}
