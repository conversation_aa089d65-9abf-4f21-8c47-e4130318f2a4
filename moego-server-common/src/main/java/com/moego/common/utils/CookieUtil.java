package com.moego.common.utils;

import jakarta.servlet.http.Cookie;

public class CookieUtil {

    /**
     * 构造安全的 cookie (http only & secure)
     *
     * @param cookieName cookie name
     * @param cookieValue cookie value
     * @param domain domain
     * @param maxAge max age, in seconds
     * @return cookie
     */
    public static Cookie buildCookie(String cookieName, String cookieValue, String domain, int maxAge) {
        Cookie cookie = new Cookie(cookieName, cookieValue);
        cookie.setDomain(domain);
        cookie.setHttpOnly(true);
        cookie.setSecure(true);
        cookie.setMaxAge(maxAge);
        cookie.setPath("/");
        return cookie;
    }
}
