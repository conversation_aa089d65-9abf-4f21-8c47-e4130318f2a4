package com.moego.common.utils.payment;

/**
 * <AUTHOR>
 * @since 2022/8/31
 */
public interface PlanMsgNum {
    Integer T1_US_CA_VAN = 300;
    Integer T2_US_CA_VAN = 900;
    Integer T3_US_CA_VAN = 900;
    Integer T1_UK_AU_VAN = 100;
    Integer T2_UK_AU_VAN = 300;
    Integer T3_UK_AU_VAN = 300;

    Integer T1_US_CA_SAL = 450;
    Integer T2_US_CA_SAL = 1350;
    Integer T3_US_CA_SAL = 1350;
    Integer T1_UK_AU_SAL = 150;
    Integer T2_UK_AU_SAL = 450;
    Integer T3_UK_AU_SAL = 450;

    // solo plan SMS 数量的下发，salon 300/100，mobile 200/70，100 和 70 是 AU/UK。验证 account 页面和 message 页面的展示
    Integer VERSION_4_T1_US_CA_SAL = 300;
    Integer VERSION_4_T1_UK_AU_SAL = 100;

    Integer VERSION_4_T1_US_CA_VAN = 200;
    Integer VERSION_4_T1_UK_AU_VAN = 70;
}
