package com.moego.common.utils.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/8/30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProcessingFeePair {

    @Schema(description = "单位美分")
    private int cent;

    @Schema(description = "百分比，收费2.9%, 记为0.029")
    private double percent;
}
