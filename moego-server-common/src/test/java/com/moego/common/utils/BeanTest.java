package com.moego.common.utils;

import com.moego.common.params.SortIdListParams;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;

@AllArgsConstructor
@NoArgsConstructor
@ToString
class Point {

    String name = "pp";
    int x = 1;
    float y = -0.1F;
}

@ToString
@Data
class TargetClassDemo {

    String name;
    List<Integer> idList;
    List<Point> points;
}

public class BeanTest {

    @Test
    public void testCopyBean() {
        List<Integer> ids = new ArrayList<>();
        ids.add(222);
        ids.add(333);
        SortIdListParams param = new SortIdListParams();
        param.setIdList(ids);
        TargetClassDemo demo = new TargetClassDemo();
        BeanUtils.copyProperties(param, demo);
        System.out.println(demo);
        Assertions.assertEquals(ids, demo.getIdList());
        demo.setPoints(new ArrayList<>());
        demo.getPoints().add(new Point("p1", 5, 3));
        demo.getPoints().add(new Point("p2", 6, 6));
        TargetClassDemo target = new TargetClassDemo();
        BeanUtils.copyProperties(demo, target);
        target.getPoints().forEach(System.out::println);
    }
}
