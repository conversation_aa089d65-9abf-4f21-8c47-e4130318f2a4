package com.moego.common.utils;

import static com.moego.common.utils.PhoneUtil.removeCountryCode;
import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

/**
 * {@link PhoneUtil} tester.
 */
class PhoneUtilTest {

    /**
     * {@link PhoneUtil#removeCountryCode(String)}
     */
    @Test
    void testRemoveCountryCode() {
        String number1 = "+11234567890";
        String result1 = removeCountryCode(number1);

        assertThat(result1).isEqualTo("1234567890");

        String number2 = "+861234567890";
        String result2 = removeCountryCode(number2);
        assertThat(result2).isEqualTo("1234567890");

        String number3 = "1234567890";
        String result3 = removeCountryCode(number3);
        assertThat(result3).isEqualTo("1234567890");

        String number4 = "+4401234567890";
        String result4 = removeCountryCode(number4);
        assertThat(result4).isEqualTo("1234567890");
    }
}
