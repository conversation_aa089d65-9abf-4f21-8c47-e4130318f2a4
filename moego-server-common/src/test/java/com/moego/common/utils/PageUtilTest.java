package com.moego.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.List;
import java.util.function.Function;
import java.util.stream.IntStream;
import org.junit.jupiter.api.Test;

class PageUtilTest {
    static class Loader<I, R> implements Function<I, R> {
        private int callTimes = 0;
        private final Function<I, R> fn;

        public static <I, R> Loader<I, R> of(Function<I, R> fn) {
            return new Loader<>(fn);
        }

        public static <I> Loader<I, I> of() {
            return new Loader<>(Function.identity());
        }

        public Loader(Function<I, R> fn) {
            this.fn = fn;
        }

        @Override
        public R apply(I i) {
            callTimes++;
            return fn.apply(i);
        }

        public int count() {
            var count = callTimes;
            callTimes = 0;
            return count;
        }
    }

    @Test
    void mapAll() {
        final var input = IntStream.range(0, 3000).boxed().toList();
        final var loader = Loader.<List<Integer>>of();

        var out = PageUtil.mapAll(null, loader);
        assertEquals(List.of(), out);
        assertEquals(0, loader.count());

        out = PageUtil.mapAll(List.of(), loader);
        assertEquals(List.of(), out);
        assertEquals(0, loader.count());

        out = PageUtil.mapAll(input.subList(0, 999), loader);
        assertEquals(input.subList(0, 999), out);
        assertEquals(2, loader.count());

        out = PageUtil.mapAll(input.subList(0, 1000), loader);
        assertEquals(input.subList(0, 1000), out);
        assertEquals(2, loader.count());

        out = PageUtil.mapAll(input.subList(0, 1001), loader);
        assertEquals(input.subList(0, 1001), out);
        assertEquals(3, loader.count());
    }
}
