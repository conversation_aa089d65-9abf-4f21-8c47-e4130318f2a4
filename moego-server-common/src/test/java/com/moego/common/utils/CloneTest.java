package com.moego.common.utils;

import java.util.ArrayList;
import org.junit.jupiter.api.Test;

class B {

    A a;

    @Override
    public String toString() {
        return "B{" + "a=" + (a == null) + '}';
    }
}

class A {

    B b;

    @Override
    public String toString() {
        return "A{" + "b=" + (b == null) + '}';
    }
}

public class CloneTest {

    @Test
    public void testClone() throws CloneNotSupportedException {
        Car car = new Car();
        car.list = new ArrayList<>();
        car.list.add(1);
        car.brand = "BYD";
        Car c2 = (Car) car.clone();
        c2.brand = "QQ";
        c2.list.add(3);
        System.out.println(car);
        System.out.println(c2);
    }

    @Test
    public void testCreateA() {
        A objA = new A();
        B objB = new B();
        System.out.println(objA);
        System.out.println(objB);
        objA.b = objB;
        objB.a = objA;
        System.out.println(objA.b);
        System.out.println(objB.a);
    }
}
