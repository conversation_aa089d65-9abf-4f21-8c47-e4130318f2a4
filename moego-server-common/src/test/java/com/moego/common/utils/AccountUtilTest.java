/*
 * @since 2022-03-16 11:15:17
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.common.utils;

import org.junit.jupiter.api.Test;

public class AccountUtilTest {

    @Test
    public void testGetAccountToken() {
        String token = AccountUtil.getAccountToken(100069);
        System.out.println("account token " + token);
    }

    @Test
    public void testGetStaffToken() {
        String token = AccountUtil.getStaffToken(100069, 100000, 100395);
        System.out.println("staff token " + token);
    }
}
