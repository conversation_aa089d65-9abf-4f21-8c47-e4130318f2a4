package com.moego.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.DayOfWeek;
import java.time.LocalDate;
import org.junit.jupiter.api.Test;

public class WeekUtilTest {

    @Test
    public void testParseWeek() {
        LocalDate localDate = LocalDate.parse("2020-12-03");
        DayOfWeek day = localDate.getDayOfWeek();
        System.out.println(day);
        assertEquals(DayOfWeek.THURSDAY.getValue(), day.getValue());
    }

    @Test
    public void testAddMonth() {
        long curr = DateUtil.get10Timestamp();
        long nextMonth = DateUtil.getNextMonthTimeStamp(curr);
        long secondsOfMonth = 27 * 24 * 3600;
        System.out.printf(
                "curr %d, next month %d \nsecondsOf28Days %d \nsecondsOfMonth  %d",
                curr, nextMonth, secondsOfMonth, nextMonth - curr);
        assertTrue(nextMonth - curr > secondsOfMonth);
    }
}
