package com.moego.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.junit.jupiter.api.Test;

@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Setter
class Car implements Cloneable {

    String brand;
    int doors;
    List<Integer> list;

    @Override
    protected Object clone() throws CloneNotSupportedException {
        Car result = (Car) super.clone();
        if (list != null) {
            List<Integer> newPetService = new ArrayList<>();
            list.forEach(srv -> {
                newPetService.add(srv);
            });
            result.list = (newPetService);
        }
        return result;
    }
}

public class GsonUtilTest {

    @Test
    public void testGsonNull() {
        System.out.println(GsonUtil.toJson(null, false));
    }

    @Test
    public void testParseStringToJavaObject() {
        String json = "{\"brand\":\"Jeep\", \"doors\": 3}";
        Gson gson = new Gson();
        Car car = gson.fromJson(json, Car.class);
        System.out.println(car);
    }

    @Test
    public void testJavaObjectToJsonStr() {
        Car car = new Car();
        car.brand = "Rover";
        car.doors = 5;

        // One line printing:  {"brand":"Rover","doors":5}
        Gson gson = new Gson();
        String json = gson.toJson(car);
        System.out.println(json);

        // Pretty Printing:
        // {
        //  "brand": "Rover",
        //  "doors": 5
        // }
        gson = new GsonBuilder().setPrettyPrinting().create();
        json = gson.toJson(car);
        System.out.println(json);
        assertEquals(json, GsonUtil.toJson(car, true));
    }

    @Test
    public void testJavaMapToJsonStr() {
        Car car = new Car();
        car.brand = "Rover";
        car.doors = 5;
        Map<String, Car> map = new HashMap<>();
        map.put("name", car);
        System.out.println(GsonUtil.toJson(map, true));
        System.out.println(GsonUtil.toJson(map, false));
    }

    @Test
    public void testTreeModel() {
        String jsonString = "{\"name\":\"Mahesh Kumar\", \"age\":21,\"verified\":false,\"marks\": [100,90,85]}";
        // create tree from JSON
        JsonElement rootNode = JsonParser.parseReader(new StringReader(jsonString));
        JsonObject details = rootNode.getAsJsonObject();

        JsonElement nameNode = details.get("name");
        System.out.println("Name: " + nameNode.getAsString());

        JsonElement ageNode = details.get("age");
        System.out.println("Age: " + ageNode.getAsInt());
    }

    @Test
    public void testCustomFromJson() {
        String json = "{\"brand\":\"Jeep\", \"doors\": 3}";
        Car car = GsonUtil.fromJson(json, Car.class);
        System.out.println(car);
        String arrayJson = "[{\"brand\":\"Jeep\", \"doors\": 3},{\"brand\":\"Jeep2\", \"doors\": 666}]";
        Car[] list = GsonUtil.fromJson(arrayJson, Car[].class);
        System.out.println(list);
    }

    @Test
    public void testMap() {
        String jsonTime =
                "{\"Monday\":{\"isSelected\":true,\"timeRange\":[{\"startTime\":540,\"entTime\":720},{\"startTime\":840,\"entTime\":1200}]},\"Tuesday\":{\"isSelected\":true,\"timeRange\":[{\"startTime\":540,\"entTime\":720},{\"startTime\":840,\"entTime\":1200}]},\"Wednesday\":{\"isSelected\":true,\"timeRange\":[{\"startTime\":540,\"entTime\":720},{\"startTime\":840,\"entTime\":1200}]},\"Thursday\":{\"isSelected\":true,\"timeRange\":[{\"startTime\":540,\"entTime\":720},{\"startTime\":840,\"entTime\":1200}]},\"Friday\":{\"isSelected\":true,\"timeRange\":[{\"startTime\":540,\"entTime\":720},{\"startTime\":840,\"entTime\":1200}]},\"Saturday\":{\"isSelected\":true,\"timeRange\":[{\"startTime\":540,\"entTime\":720},{\"startTime\":840,\"entTime\":1200}]},\"Sunday\":{\"isSelected\":true,\"timeRange\":[{\"startTime\":540,\"entTime\":720},{\"startTime\":840,\"entTime\":1200}]}}\n";
        JsonObject root = JsonParser.parseReader(new StringReader(jsonTime)).getAsJsonObject();
        root.entrySet().forEach(stringJsonElementEntry -> {
            String key = stringJsonElementEntry.getKey();
            JsonObject value = stringJsonElementEntry.getValue().getAsJsonObject();
            System.out.println("day of week for " + key + " is " + WeekUtil.getDayIndexOfWeek(key));
            if (value.get("isSelected").getAsBoolean()) {
                JsonArray timeRange = value.get("timeRange").getAsJsonArray();
                timeRange.iterator().forEachRemaining(jsonElement -> {
                    JsonObject range = jsonElement.getAsJsonObject();
                    System.out.println(range.get("startTime").getAsInt());
                    System.out.println(range.get("entTime").getAsInt());
                });
            }
        });
    }
}
