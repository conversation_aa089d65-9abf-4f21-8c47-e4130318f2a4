package com.moego.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.CollectionUtils;

public class CommonUtilTest {

    @Test
    public void testUuid() {
        System.out.println(CommonUtil.getUuid());
        // email
        assertTrue(EmailFormatUtil.isEmailValid("<EMAIL>"));
        assertTrue(PrimitiveTypeUtil.isNumberNullOrZero(null));
        assertTrue(PrimitiveTypeUtil.isNumberNullOrZero(0));
        assertTrue(PrimitiveTypeUtil.isNumberNullOrZero(Long.valueOf(0)));
    }

    @Test
    public void testStream() {
        List<Car> cars = new LinkedList<>();
        cars.add(new Car("BYD", 4, null));
        cars.add(new Car("BYD2", 5, null));
        cars.add(new Car("BYD3", 6, null));
        System.out.println(
                cars.stream().filter(car -> car.doors == 5).findFirst().get().brand);
    }

    @Data
    class Demo {

        Double lat;
    }

    @Test
    public void testInt() {
        Demo demo = new Demo();
        demo.setLat(0d);
        System.out.println(demo.lat == 0);
    }

    @Test
    public void testAccurateDecimalString() {
        String s = "10.1234";
        assertEquals("10", CommonUtil.accurateDecimalString(s, -1));
        assertEquals("10", CommonUtil.accurateDecimalString(s, 0));
        assertEquals("10.1", CommonUtil.accurateDecimalString(s, 1));
        assertEquals("10.12", CommonUtil.accurateDecimalString(s, 2));
        assertEquals("10.123", CommonUtil.accurateDecimalString(s, 3));
        assertEquals("10.1234", CommonUtil.accurateDecimalString(s, 4));
        assertEquals("10.12340", CommonUtil.accurateDecimalString(s, 5));
        assertEquals("10.123400", CommonUtil.accurateDecimalString(s, 6));

        assertEquals("10.123450", CommonUtil.accurateDecimalString("10.12345", 6));
        assertEquals("10.123456", CommonUtil.accurateDecimalString("10.123456", 6));
        assertEquals("10.123456", CommonUtil.accurateDecimalString("10.1234567", 6));
    }

    @Test
    public void testSplitListByItemNum() {
        Integer[] arr = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12};
        var itemsList = CommonUtil.splitListByItemNum(CollectionUtils.toSet(arr), 5);
        assertEquals(3, itemsList.size());
        assertEquals(5, itemsList.get(0).size());
        assertEquals(5, itemsList.get(1).size());
        assertEquals(2, itemsList.get(2).size());

        var itemsList1 = CommonUtil.splitListByItemNum(CollectionUtils.toSet(arr), 3);
        assertEquals(4, itemsList1.size());
        assertEquals(3, itemsList1.get(0).size());
        assertEquals(3, itemsList1.get(1).size());
        assertEquals(3, itemsList1.get(2).size());
    }

    @Test
    public void testSplitList() {
        List<Integer> arr = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12);
        var itemsList = CommonUtil.splitList(arr, 5);
        assertEquals(5, itemsList.size());
        assertEquals(3, itemsList.get(0).size());
        assertEquals(3, itemsList.get(1).size());
        assertEquals(2, itemsList.get(2).size());
        assertEquals(2, itemsList.get(3).size());
        assertEquals(2, itemsList.get(4).size());

        arr = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9);
        itemsList = CommonUtil.splitList(arr, 5);
        assertEquals(5, itemsList.size());
        assertEquals(2, itemsList.get(0).size());
        assertEquals(2, itemsList.get(1).size());
        assertEquals(2, itemsList.get(2).size());
        assertEquals(2, itemsList.get(3).size());
        assertEquals(1, itemsList.get(4).size());

        arr = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);
        itemsList = CommonUtil.splitList(arr, 5);
        assertEquals(5, itemsList.size());
        assertEquals(2, itemsList.get(0).size());
        assertEquals(2, itemsList.get(1).size());
        assertEquals(2, itemsList.get(2).size());
        assertEquals(2, itemsList.get(3).size());
        assertEquals(2, itemsList.get(4).size());
    }
}
