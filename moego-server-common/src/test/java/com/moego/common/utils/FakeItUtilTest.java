package com.moego.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2020/12/16 8:02 PM
 */
public class FakeItUtilTest {

    @Test
    public void fakeItOut() {
        List<Integer> list = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            list.add(i);
        }
        int originSize = list.size();
        FakeItUtil.fakeItOut(list, FakeItUtil.FAKE_TYPE_3);
        list.forEach(System.out::println);
        assertEquals(list.size(), originSize / 2);
    }

    @Test
    public void fakeItOutMap() {
        List<Integer> list = new ArrayList<>();
        for (int i = 0; i < 9; i++) {
            list.add(i);
        }
        List<Integer> list2 = new LinkedList<>();
        for (int i = 0; i < 4; i++) {
            list2.add(i);
        }
        Map<String, List<Integer>> map = new HashMap<>();
        map.put("AM", list);
        map.put("PM", list2);
        FakeItUtil.fakeItOut(map, FakeItUtil.FAKE_TYPE_3);
        assertEquals((9 - 9 / 2), list.size());
        assertEquals((4 - 4 / 2), list2.size());
    }

    @Test
    public void testRandom() {
        int size = 10;
        for (int i = 0; i < size; i++) {
            System.out.println(ThreadLocalRandom.current().nextInt(0, size));
        }
    }

    @Test
    public void remoteTail() {
        List<Integer> list = new ArrayList<>();
        list.addAll(Arrays.asList(1, 2, 3, 4, 5, 6));
        list.subList(1, list.size()).clear();
        System.out.println(list.size());
        System.out.println(list.get(0));
        list.clear();
        List<Integer> empty = list.subList(list.size() > 0 ? 1 : 0, list.size());
        assertEquals(0, empty.size());
    }
}
