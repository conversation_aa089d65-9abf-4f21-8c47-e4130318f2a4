package com.moego.common.enums;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

class ServiceItemEnumTest {

    private static final List<Integer> GROOMING_BIT_VALUES = List.of(1, 3, 5, 7, 9, 11, 13, 15, 17);
    private static final List<Integer> BOARDING_BIT_VALUES = List.of(2, 3, 6, 7, 10, 11, 14, 15, 18);
    private static final List<Integer> DAYCARE_BIT_VALUES = List.of(4, 5, 6, 7, 12, 13, 14, 15, 20);
    private static final List<Integer> EVALUATION_BIT_VALUES = List.of(8, 9, 10, 11, 12, 13, 14, 15);
    private static final List<Integer> DOG_WALKING_BIT_VALUES = List.of(16, 17, 18, 20);
    private static final List<Integer> GROUP_CLASS_BIT_VALUES = List.of(32);

    @Test
    void testFromServiceItem() {
        assertThat(ServiceItemEnum.fromServiceItem(1)).isEqualTo(ServiceItemEnum.GROOMING);
        assertThat(ServiceItemEnum.fromServiceItem(2)).isEqualTo(ServiceItemEnum.BOARDING);
        assertThat(ServiceItemEnum.fromServiceItem(3)).isEqualTo(ServiceItemEnum.DAYCARE);
        assertThat(ServiceItemEnum.fromServiceItem(4)).isEqualTo(ServiceItemEnum.EVALUATION);
        assertThat(ServiceItemEnum.fromServiceItem(5)).isEqualTo(ServiceItemEnum.DOG_WALKING);
        assertThat(ServiceItemEnum.fromServiceItem(6)).isEqualTo(ServiceItemEnum.GROUP_CLASS);
        assertThat(ServiceItemEnum.fromServiceItem(99)).isNull();
    }

    @Test
    void testConvertBitValueListFromServiceItems() {
        // Test single items
        assertThat(ServiceItemEnum.convertBitValueList(List.of(1))).isEqualTo(0b001);
        assertThat(ServiceItemEnum.convertBitValueList(List.of(2))).isEqualTo(0b010);
        assertThat(ServiceItemEnum.convertBitValueList(List.of(3))).isEqualTo(0b100);
        assertThat(ServiceItemEnum.convertBitValueList(List.of(4))).isEqualTo(0b1000);
        assertThat(ServiceItemEnum.convertBitValueList(List.of(5))).isEqualTo(0b10000);
        assertThat(ServiceItemEnum.convertBitValueList(List.of(6))).isEqualTo(0b100000);

        // Test combinations
        assertThat(ServiceItemEnum.convertBitValueList(List.of(1, 2))).isEqualTo(0b011);
        assertThat(ServiceItemEnum.convertBitValueList(List.of(1, 2, 3))).isEqualTo(0b111);
        assertThat(ServiceItemEnum.convertBitValueList(List.of(1, 6))).isEqualTo(0b100001);

        // Test with invalid item
        assertThat(ServiceItemEnum.convertBitValueList(List.of(1, 99))).isEqualTo(0b001);

        // Test empty list
        assertThat(ServiceItemEnum.convertBitValueList(List.of())).isZero();
    }

    @Test
    void testConvertBitValueListFromBitValue() {
        // Test single items
        assertThat(ServiceItemEnum.convertBitValueList(0b001)).containsExactly(ServiceItemEnum.GROOMING);
        assertThat(ServiceItemEnum.convertBitValueList(0b010)).containsExactly(ServiceItemEnum.BOARDING);
        assertThat(ServiceItemEnum.convertBitValueList(0b100)).containsExactly(ServiceItemEnum.DAYCARE);
        assertThat(ServiceItemEnum.convertBitValueList(0b1000)).containsExactly(ServiceItemEnum.EVALUATION);
        assertThat(ServiceItemEnum.convertBitValueList(0b10000)).containsExactly(ServiceItemEnum.DOG_WALKING);
        assertThat(ServiceItemEnum.convertBitValueList(0b100000)).containsExactly(ServiceItemEnum.GROUP_CLASS);

        // Test combinations
        assertThat(ServiceItemEnum.convertBitValueList(0b011))
                .containsExactly(ServiceItemEnum.GROOMING, ServiceItemEnum.BOARDING);
        assertThat(ServiceItemEnum.convertBitValueList(0b111))
                .containsExactly(ServiceItemEnum.GROOMING, ServiceItemEnum.BOARDING, ServiceItemEnum.DAYCARE);
        assertThat(ServiceItemEnum.convertBitValueList(0b10001))
                .containsExactly(ServiceItemEnum.GROOMING, ServiceItemEnum.DOG_WALKING);
        assertThat(ServiceItemEnum.convertBitValueList(0b100001))
                .containsExactly(ServiceItemEnum.GROOMING, ServiceItemEnum.GROUP_CLASS);

        // Test with zero
        assertThat(ServiceItemEnum.convertBitValueList(0)).isEmpty();
    }

    @Test
    void testConvertBitValues() {
        // Test single bit values
        assertThat(ServiceItemEnum.convertBitValues(List.of(0b001))).containsExactly(ServiceItemEnum.GROOMING);
        assertThat(ServiceItemEnum.convertBitValues(List.of(0b100000))).containsExactly(ServiceItemEnum.GROUP_CLASS);

        // Test multiple bit values
        assertThat(ServiceItemEnum.convertBitValues(List.of(0b001, 0b010)))
                .containsExactly(ServiceItemEnum.GROOMING, ServiceItemEnum.BOARDING);
        assertThat(ServiceItemEnum.convertBitValues(List.of(0b001, 0b100000)))
                .containsExactly(ServiceItemEnum.GROOMING, ServiceItemEnum.GROUP_CLASS);

        // Test overlapping bit values
        assertThat(ServiceItemEnum.convertBitValues(List.of(0b001, 0b011)))
                .containsExactly(ServiceItemEnum.GROOMING, ServiceItemEnum.BOARDING);
        assertThat(ServiceItemEnum.convertBitValues(List.of(0b100000, 0b100001)))
                .containsExactly(ServiceItemEnum.GROOMING, ServiceItemEnum.GROUP_CLASS);
    }

    @Test
    void testConvertServiceItemListToBitValueList() {
        // Test single service item
        assertThat(ServiceItemEnum.convertServiceItemListToBitValueList(
                        List.of(ServiceItemEnum.GROOMING.getServiceItem())))
                .containsExactlyInAnyOrder(GROOMING_BIT_VALUES.toArray(new Integer[0]));
        assertThat(ServiceItemEnum.convertServiceItemListToBitValueList(
                        List.of(ServiceItemEnum.BOARDING.getServiceItem())))
                .containsExactlyInAnyOrder(BOARDING_BIT_VALUES.toArray(new Integer[0]));
        assertThat(ServiceItemEnum.convertServiceItemListToBitValueList(
                        List.of(ServiceItemEnum.DAYCARE.getServiceItem())))
                .containsExactlyInAnyOrder(DAYCARE_BIT_VALUES.toArray(new Integer[0]));
        assertThat(ServiceItemEnum.convertServiceItemListToBitValueList(
                        List.of(ServiceItemEnum.EVALUATION.getServiceItem())))
                .containsExactlyInAnyOrder(EVALUATION_BIT_VALUES.toArray(new Integer[0]));
        assertThat(ServiceItemEnum.convertServiceItemListToBitValueList(
                        List.of(ServiceItemEnum.DOG_WALKING.getServiceItem())))
                .containsExactlyInAnyOrder(DOG_WALKING_BIT_VALUES.toArray(new Integer[0]));
        assertThat(ServiceItemEnum.convertServiceItemListToBitValueList(
                        List.of(ServiceItemEnum.GROUP_CLASS.getServiceItem())))
                .containsExactlyInAnyOrder(GROUP_CLASS_BIT_VALUES.toArray(new Integer[0]));

        // Test multiple service items
        assertThat(ServiceItemEnum.convertServiceItemListToBitValueList(List.of(1, 2)))
                .containsExactlyInAnyOrder(Stream.concat(GROOMING_BIT_VALUES.stream(), BOARDING_BIT_VALUES.stream())
                        .distinct()
                        .toArray(Integer[]::new));

        // Test with invalid service item
        assertThat(ServiceItemEnum.convertServiceItemListToBitValueList(List.of(1, 99)))
                .containsExactlyInAnyOrder(GROOMING_BIT_VALUES.toArray(new Integer[0]));

        // Test empty list
        assertThat(ServiceItemEnum.convertServiceItemListToBitValueList(List.of()))
                .isEmpty();
    }

    @ParameterizedTest
    @MethodSource("getBitValueListByServiceItemTestCases")
    void testGetBitValueListByServiceItem(ServiceItemEnum serviceItem, List<Integer> expectedBitValues) {
        assertThat(ServiceItemEnum.getBitValueListByServiceItem(serviceItem))
                .containsExactlyElementsOf(expectedBitValues);
    }

    static Stream<Arguments> getBitValueListByServiceItemTestCases() {
        return Stream.of(
                Arguments.of(ServiceItemEnum.GROOMING, GROOMING_BIT_VALUES),
                Arguments.of(ServiceItemEnum.BOARDING, BOARDING_BIT_VALUES),
                Arguments.of(ServiceItemEnum.DAYCARE, DAYCARE_BIT_VALUES),
                Arguments.of(ServiceItemEnum.EVALUATION, EVALUATION_BIT_VALUES),
                Arguments.of(ServiceItemEnum.DOG_WALKING, DOG_WALKING_BIT_VALUES),
                Arguments.of(ServiceItemEnum.GROUP_CLASS, GROUP_CLASS_BIT_VALUES));
    }

    @ParameterizedTest
    @MethodSource("isIncludedInTestCases")
    void testIsIncludedIn(ServiceItemEnum serviceItem, Integer serviceTypeInclude, boolean expected) {
        assertThat(serviceItem.isIncludedIn(serviceTypeInclude)).isEqualTo(expected);
    }

    static Stream<Arguments> isIncludedInTestCases() {
        return Stream.of(
                Arguments.of(ServiceItemEnum.GROOMING, 0b001, true),
                Arguments.of(ServiceItemEnum.GROOMING, 0b011, true),
                Arguments.of(ServiceItemEnum.GROOMING, 0b010, false),
                Arguments.of(ServiceItemEnum.BOARDING, 0b010, true),
                Arguments.of(ServiceItemEnum.BOARDING, 0b011, true),
                Arguments.of(ServiceItemEnum.BOARDING, 0b001, false),
                Arguments.of(ServiceItemEnum.GROUP_CLASS, 0b100000, true),
                Arguments.of(ServiceItemEnum.GROUP_CLASS, 0b100001, true),
                Arguments.of(ServiceItemEnum.GROUP_CLASS, 0b000001, false));
    }

    @ParameterizedTest
    @MethodSource("getMainServiceItemTypeTestCases")
    void testGetMainServiceItemType(Integer serviceTypeInclude, ServiceItemEnum expected) {
        assertThat(ServiceItemEnum.getMainServiceItemType(serviceTypeInclude)).isEqualTo(expected);
    }

    static Stream<Arguments> getMainServiceItemTypeTestCases() {
        return Stream.of(
                Arguments.of(0b001, ServiceItemEnum.GROOMING),
                Arguments.of(0b010, ServiceItemEnum.BOARDING),
                Arguments.of(0b011, ServiceItemEnum.BOARDING), // BOARDING takes precedence
                Arguments.of(0b100, ServiceItemEnum.DAYCARE),
                Arguments.of(0b101, ServiceItemEnum.DAYCARE), // DAYCARE takes precedence over GROOMING
                Arguments.of(0b1000, ServiceItemEnum.EVALUATION),
                Arguments.of(0b10000, ServiceItemEnum.DOG_WALKING),
                Arguments.of(0b100000, ServiceItemEnum.GROUP_CLASS),
                Arguments.of(0b100001, ServiceItemEnum.GROUP_CLASS), // GROUP_CLASS takes precedence over GROOMING
                Arguments.of(0b110000, ServiceItemEnum.DOG_WALKING), // DOG_WALKING takes precedence over GROUP_CLASS
                Arguments.of(0b11000, ServiceItemEnum.EVALUATION)); // EVALUATION takes precedence over DOG_WALKING
    }
}
