package com.moego.common.security;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.moego.common.utils.CommonUtil;
import org.junit.jupiter.api.Test;

public class AESUtilTest {

    private static final String AES_KEY = "bPeShVmYq3t6w9z$C&F%C*F-JaNdRnZr";

    /**
     * 用于测试需要时，手动解密token
     */
    @Test
    public void decode_util() {
        String token =
                "ypAJi1Jn82HEcfxac4/AJUyM5FK7dYrOUYyFpS8D49B1uLyT5jjSqNTsacb8QRHUY71BZdsdEs37xFUP4eCleR/GdgrMNaYghoI+la/IfbU=";
        String ak = "EAAAENQsAuMyyPXN7hB_IeQSxYbUUg-VwqkDnDDKJfSnjWpo9t3CTCTWsUsSJOJC";
        String decodedAk = AESUtil.decode(AES_KEY, token);
        System.out.println(decodedAk);
        assertEquals(ak, decodedAk);
    }

    @Test
    public void decodeSquare() {
        String token = "";
        if (token.length() < 10) {
            System.out.println("nothing");
        } else {
            System.out.println(AESUtil.decode(AES_KEY, token));
        }
    }

    @Test
    public void aesEncrypt_then_decrypt_match() {
        String original = CommonUtil.getRandomString(12);
        String key = "Yq3m6w8z$C&F)J@N7x!A%cQfTjWnZr7u";
        String encryptedText = AESUtil.encode(key, original);
        String decryptedText = AESUtil.decode(key, encryptedText);

        System.out.println(original + ", " + encryptedText + ", " + decryptedText);
        assertEquals(original, decryptedText);
    }
}
