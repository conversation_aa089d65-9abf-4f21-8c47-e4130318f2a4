/*
 * @since 2021-01-11 18:10:33
 * <AUTHOR> <<EMAIL>>
 */

module.exports = {
  extends: ['@commitlint/config-angular'],
  rules: {
    'references-empty': [2, 'never'],
    'header-max-length': [2, 'never', 100],
  },
  parserPreset: {
    parserOpts: {
      issuePrefixes: [
        'BETA-',
        'CS-',
        'APP-',
        'MOEG-',
        'MC-',
        'OBV-',
        'DBO-',
        'PEC-',
        'UF-',
        'GROOM-',
        'WT-',
        'MOE-',
        'ERP-',
        'TECH-',
        'MER-',
        'FIN-',
        'CA-',
        'FDN-',
        'IFRBE-',
        'CRM-'
      ],
    },
  },
};
