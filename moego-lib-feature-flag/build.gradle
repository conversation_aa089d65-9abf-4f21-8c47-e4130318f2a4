dependencies {
  api(project(":moego-lib-common"))

  // Use GrowthBook as default implementation
  implementation("com.github.growthbook:growthbook-sdk-java:${growthBookVersion}")

  annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")

  testImplementation("org.springframework.boot:spring-boot-starter-test")
  testImplementation("com.freemanan:classpath-replacer-junit5:${classpathReplacerVersion}")
}
