# Feature Flag

This module provides an abstraction for feature flags, and it provides [growth book](https://www.growthbook.io/) implementation.

## Usage

> ⚠️ **Warning**  
> Growth Book SDK is not published to Maven Central; it uses JitPack instead.
> ```groovy
> repositories {
>   mavenCentral()
>   maven { url 'https://jitpack.io' }
> }
> ```

```yaml
# application-local.yaml
moego:
  feature-flag:
    growth-book:
      api-host: https://growthbook.moego.pet/growthbook-api
      client-key: sdk-qygeRRneunZQJxf
```

```java
@Service
@RequiredArgsConstructor
public class AppointmentService {

    private final FeatureFlagApi featureFlagApi;
    
    public void createAppointment(Appointment appointment) {
        if (featureFlagApi.isOn(FeatureFlags.NEW_APPOINTMENT_FLOW, FeatureFlagContext.builder().company(111L).build())) {
            // new appointment flow
        } else {
            // old appointment flow
        }
    }
}
```
