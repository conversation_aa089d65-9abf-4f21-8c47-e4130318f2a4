-- ClickHouse SQL grammar
CREATE TABLE IF NOT EXISTS moego_testing.activity_log ON CLUSTER moego
(
    id            String comment 'id',
    business_id   Int64 comment 'business id',
    operator_id   String comment 'operator id, 一般为 staff id',
    operator_name String comment 'operator name, 一般为 staff name',
    action        String comment '操作名称, 例如: Create, Update',
    resource_type String comment '资源类型, 例如: Appointment, Customer',
    resource_id   String comment '资源 id, 例如: Appointment id, Customer id',
    resource_name String comment '资源名称, 例如 Customer name',
    owner_id      String comment '资源拥有者 id, 一般为 customer id',
    owner_name    String comment '资源拥有者名称, 一般为 customer name',
    time          DateTime64(3) comment '操作时间',
    details       String        default '{}' comment '操作详情, JSON value, 例如: {"name": "Freeman", "phone": "123456789"}',
    is_root       BOOLEAN       default false comment 'is root activity log',
    request_id    String comment 'request id',
    created_at    DateTime64(3) default now64() comment '该条记录插入时间'
) ENGINE = MergeTree()
    ORDER BY (business_id, resource_type, action, time)
    TTL toDateTime(time) + INTERVAL 60 DAY;

alter table moego_testing.activity_log on cluster moego add index idx_activity_log_id (id) TYPE minmax GRANULARITY 1;
alter table moego_testing.activity_log on cluster moego add index idx_activity_log_request_id (request_id) TYPE minmax GRANULARITY 1;

-- Create distributed table
-- 可以直接创建分布式表吗？-- 不能
CREATE TABLE IF NOT EXISTS moego_testing.all_activity_log ON CLUSTER moego AS moego_testing.activity_log
    ENGINE = Distributed(moego, moego_testing, activity_log, rand());
