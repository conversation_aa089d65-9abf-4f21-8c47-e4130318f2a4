// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/open_platform/v1/open_platform_service.proto

package open_platformpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetGoogleAdsUserInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetGoogleAdsUserInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetGoogleAdsUserInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetGoogleAdsUserInfoRequestMultiError, or nil if none found.
func (m *GetGoogleAdsUserInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetGoogleAdsUserInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	if len(errors) > 0 {
		return GetGoogleAdsUserInfoRequestMultiError(errors)
	}

	return nil
}

// GetGoogleAdsUserInfoRequestMultiError is an error wrapping multiple
// validation errors returned by GetGoogleAdsUserInfoRequest.ValidateAll() if
// the designated constraints aren't met.
type GetGoogleAdsUserInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetGoogleAdsUserInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetGoogleAdsUserInfoRequestMultiError) AllErrors() []error { return m }

// GetGoogleAdsUserInfoRequestValidationError is the validation error returned
// by GetGoogleAdsUserInfoRequest.Validate if the designated constraints
// aren't met.
type GetGoogleAdsUserInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetGoogleAdsUserInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetGoogleAdsUserInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetGoogleAdsUserInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetGoogleAdsUserInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetGoogleAdsUserInfoRequestValidationError) ErrorName() string {
	return "GetGoogleAdsUserInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetGoogleAdsUserInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetGoogleAdsUserInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetGoogleAdsUserInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetGoogleAdsUserInfoRequestValidationError{}

// Validate checks the field values on GetGoogleAdsUserInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetGoogleAdsUserInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetGoogleAdsUserInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetGoogleAdsUserInfoResponseMultiError, or nil if none found.
func (m *GetGoogleAdsUserInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetGoogleAdsUserInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUserInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetGoogleAdsUserInfoResponseValidationError{
					field:  "UserInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetGoogleAdsUserInfoResponseValidationError{
					field:  "UserInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetGoogleAdsUserInfoResponseValidationError{
				field:  "UserInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAdsSetting()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetGoogleAdsUserInfoResponseValidationError{
					field:  "AdsSetting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetGoogleAdsUserInfoResponseValidationError{
					field:  "AdsSetting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdsSetting()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetGoogleAdsUserInfoResponseValidationError{
				field:  "AdsSetting",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetGoogleAdsUserInfoResponseMultiError(errors)
	}

	return nil
}

// GetGoogleAdsUserInfoResponseMultiError is an error wrapping multiple
// validation errors returned by GetGoogleAdsUserInfoResponse.ValidateAll() if
// the designated constraints aren't met.
type GetGoogleAdsUserInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetGoogleAdsUserInfoResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetGoogleAdsUserInfoResponseMultiError) AllErrors() []error { return m }

// GetGoogleAdsUserInfoResponseValidationError is the validation error returned
// by GetGoogleAdsUserInfoResponse.Validate if the designated constraints
// aren't met.
type GetGoogleAdsUserInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetGoogleAdsUserInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetGoogleAdsUserInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetGoogleAdsUserInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetGoogleAdsUserInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetGoogleAdsUserInfoResponseValidationError) ErrorName() string {
	return "GetGoogleAdsUserInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetGoogleAdsUserInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetGoogleAdsUserInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetGoogleAdsUserInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetGoogleAdsUserInfoResponseValidationError{}

// Validate checks the field values on RevokeGoogleAdsOAuthRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RevokeGoogleAdsOAuthRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RevokeGoogleAdsOAuthRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RevokeGoogleAdsOAuthRequestMultiError, or nil if none found.
func (m *RevokeGoogleAdsOAuthRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RevokeGoogleAdsOAuthRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for StaffId

	if len(errors) > 0 {
		return RevokeGoogleAdsOAuthRequestMultiError(errors)
	}

	return nil
}

// RevokeGoogleAdsOAuthRequestMultiError is an error wrapping multiple
// validation errors returned by RevokeGoogleAdsOAuthRequest.ValidateAll() if
// the designated constraints aren't met.
type RevokeGoogleAdsOAuthRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RevokeGoogleAdsOAuthRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RevokeGoogleAdsOAuthRequestMultiError) AllErrors() []error { return m }

// RevokeGoogleAdsOAuthRequestValidationError is the validation error returned
// by RevokeGoogleAdsOAuthRequest.Validate if the designated constraints
// aren't met.
type RevokeGoogleAdsOAuthRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RevokeGoogleAdsOAuthRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RevokeGoogleAdsOAuthRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RevokeGoogleAdsOAuthRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RevokeGoogleAdsOAuthRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RevokeGoogleAdsOAuthRequestValidationError) ErrorName() string {
	return "RevokeGoogleAdsOAuthRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RevokeGoogleAdsOAuthRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRevokeGoogleAdsOAuthRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RevokeGoogleAdsOAuthRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RevokeGoogleAdsOAuthRequestValidationError{}

// Validate checks the field values on ListGoogleAdsAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListGoogleAdsAccountsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListGoogleAdsAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListGoogleAdsAccountsRequestMultiError, or nil if none found.
func (m *ListGoogleAdsAccountsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListGoogleAdsAccountsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	if len(errors) > 0 {
		return ListGoogleAdsAccountsRequestMultiError(errors)
	}

	return nil
}

// ListGoogleAdsAccountsRequestMultiError is an error wrapping multiple
// validation errors returned by ListGoogleAdsAccountsRequest.ValidateAll() if
// the designated constraints aren't met.
type ListGoogleAdsAccountsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListGoogleAdsAccountsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListGoogleAdsAccountsRequestMultiError) AllErrors() []error { return m }

// ListGoogleAdsAccountsRequestValidationError is the validation error returned
// by ListGoogleAdsAccountsRequest.Validate if the designated constraints
// aren't met.
type ListGoogleAdsAccountsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListGoogleAdsAccountsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListGoogleAdsAccountsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListGoogleAdsAccountsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListGoogleAdsAccountsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListGoogleAdsAccountsRequestValidationError) ErrorName() string {
	return "ListGoogleAdsAccountsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListGoogleAdsAccountsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListGoogleAdsAccountsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListGoogleAdsAccountsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListGoogleAdsAccountsRequestValidationError{}

// Validate checks the field values on ListGoogleAdsAccountsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListGoogleAdsAccountsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListGoogleAdsAccountsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListGoogleAdsAccountsResponseMultiError, or nil if none found.
func (m *ListGoogleAdsAccountsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListGoogleAdsAccountsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCustomers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListGoogleAdsAccountsResponseValidationError{
						field:  fmt.Sprintf("Customers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListGoogleAdsAccountsResponseValidationError{
						field:  fmt.Sprintf("Customers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListGoogleAdsAccountsResponseValidationError{
					field:  fmt.Sprintf("Customers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListGoogleAdsAccountsResponseMultiError(errors)
	}

	return nil
}

// ListGoogleAdsAccountsResponseMultiError is an error wrapping multiple
// validation errors returned by ListGoogleAdsAccountsResponse.ValidateAll()
// if the designated constraints aren't met.
type ListGoogleAdsAccountsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListGoogleAdsAccountsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListGoogleAdsAccountsResponseMultiError) AllErrors() []error { return m }

// ListGoogleAdsAccountsResponseValidationError is the validation error
// returned by ListGoogleAdsAccountsResponse.Validate if the designated
// constraints aren't met.
type ListGoogleAdsAccountsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListGoogleAdsAccountsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListGoogleAdsAccountsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListGoogleAdsAccountsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListGoogleAdsAccountsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListGoogleAdsAccountsResponseValidationError) ErrorName() string {
	return "ListGoogleAdsAccountsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListGoogleAdsAccountsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListGoogleAdsAccountsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListGoogleAdsAccountsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListGoogleAdsAccountsResponseValidationError{}

// Validate checks the field values on LinkGoogleAdsAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LinkGoogleAdsAccountsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinkGoogleAdsAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LinkGoogleAdsAccountsRequestMultiError, or nil if none found.
func (m *LinkGoogleAdsAccountsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LinkGoogleAdsAccountsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for StaffId

	// no validation rules for BusinessId

	if len(errors) > 0 {
		return LinkGoogleAdsAccountsRequestMultiError(errors)
	}

	return nil
}

// LinkGoogleAdsAccountsRequestMultiError is an error wrapping multiple
// validation errors returned by LinkGoogleAdsAccountsRequest.ValidateAll() if
// the designated constraints aren't met.
type LinkGoogleAdsAccountsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinkGoogleAdsAccountsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinkGoogleAdsAccountsRequestMultiError) AllErrors() []error { return m }

// LinkGoogleAdsAccountsRequestValidationError is the validation error returned
// by LinkGoogleAdsAccountsRequest.Validate if the designated constraints
// aren't met.
type LinkGoogleAdsAccountsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinkGoogleAdsAccountsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinkGoogleAdsAccountsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinkGoogleAdsAccountsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinkGoogleAdsAccountsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinkGoogleAdsAccountsRequestValidationError) ErrorName() string {
	return "LinkGoogleAdsAccountsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LinkGoogleAdsAccountsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinkGoogleAdsAccountsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinkGoogleAdsAccountsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinkGoogleAdsAccountsRequestValidationError{}

// Validate checks the field values on GetMetaAdsUserInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMetaAdsUserInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMetaAdsUserInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMetaAdsUserInfoRequestMultiError, or nil if none found.
func (m *GetMetaAdsUserInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMetaAdsUserInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	if len(errors) > 0 {
		return GetMetaAdsUserInfoRequestMultiError(errors)
	}

	return nil
}

// GetMetaAdsUserInfoRequestMultiError is an error wrapping multiple validation
// errors returned by GetMetaAdsUserInfoRequest.ValidateAll() if the
// designated constraints aren't met.
type GetMetaAdsUserInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMetaAdsUserInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMetaAdsUserInfoRequestMultiError) AllErrors() []error { return m }

// GetMetaAdsUserInfoRequestValidationError is the validation error returned by
// GetMetaAdsUserInfoRequest.Validate if the designated constraints aren't met.
type GetMetaAdsUserInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMetaAdsUserInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMetaAdsUserInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMetaAdsUserInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMetaAdsUserInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMetaAdsUserInfoRequestValidationError) ErrorName() string {
	return "GetMetaAdsUserInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMetaAdsUserInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMetaAdsUserInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMetaAdsUserInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMetaAdsUserInfoRequestValidationError{}

// Validate checks the field values on GetMetaAdsUserInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMetaAdsUserInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMetaAdsUserInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMetaAdsUserInfoResponseMultiError, or nil if none found.
func (m *GetMetaAdsUserInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMetaAdsUserInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUserInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMetaAdsUserInfoResponseValidationError{
					field:  "UserInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMetaAdsUserInfoResponseValidationError{
					field:  "UserInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMetaAdsUserInfoResponseValidationError{
				field:  "UserInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAdsSetting()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMetaAdsUserInfoResponseValidationError{
					field:  "AdsSetting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMetaAdsUserInfoResponseValidationError{
					field:  "AdsSetting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdsSetting()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMetaAdsUserInfoResponseValidationError{
				field:  "AdsSetting",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMetaAdsUserInfoResponseMultiError(errors)
	}

	return nil
}

// GetMetaAdsUserInfoResponseMultiError is an error wrapping multiple
// validation errors returned by GetMetaAdsUserInfoResponse.ValidateAll() if
// the designated constraints aren't met.
type GetMetaAdsUserInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMetaAdsUserInfoResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMetaAdsUserInfoResponseMultiError) AllErrors() []error { return m }

// GetMetaAdsUserInfoResponseValidationError is the validation error returned
// by GetMetaAdsUserInfoResponse.Validate if the designated constraints aren't met.
type GetMetaAdsUserInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMetaAdsUserInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMetaAdsUserInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMetaAdsUserInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMetaAdsUserInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMetaAdsUserInfoResponseValidationError) ErrorName() string {
	return "GetMetaAdsUserInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMetaAdsUserInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMetaAdsUserInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMetaAdsUserInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMetaAdsUserInfoResponseValidationError{}

// Validate checks the field values on RevokeMetaAdsOAuthRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RevokeMetaAdsOAuthRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RevokeMetaAdsOAuthRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RevokeMetaAdsOAuthRequestMultiError, or nil if none found.
func (m *RevokeMetaAdsOAuthRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RevokeMetaAdsOAuthRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for StaffId

	if len(errors) > 0 {
		return RevokeMetaAdsOAuthRequestMultiError(errors)
	}

	return nil
}

// RevokeMetaAdsOAuthRequestMultiError is an error wrapping multiple validation
// errors returned by RevokeMetaAdsOAuthRequest.ValidateAll() if the
// designated constraints aren't met.
type RevokeMetaAdsOAuthRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RevokeMetaAdsOAuthRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RevokeMetaAdsOAuthRequestMultiError) AllErrors() []error { return m }

// RevokeMetaAdsOAuthRequestValidationError is the validation error returned by
// RevokeMetaAdsOAuthRequest.Validate if the designated constraints aren't met.
type RevokeMetaAdsOAuthRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RevokeMetaAdsOAuthRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RevokeMetaAdsOAuthRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RevokeMetaAdsOAuthRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RevokeMetaAdsOAuthRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RevokeMetaAdsOAuthRequestValidationError) ErrorName() string {
	return "RevokeMetaAdsOAuthRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RevokeMetaAdsOAuthRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRevokeMetaAdsOAuthRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RevokeMetaAdsOAuthRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RevokeMetaAdsOAuthRequestValidationError{}

// Validate checks the field values on ListMetaAdsAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMetaAdsAccountsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMetaAdsAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMetaAdsAccountsRequestMultiError, or nil if none found.
func (m *ListMetaAdsAccountsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMetaAdsAccountsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	if len(errors) > 0 {
		return ListMetaAdsAccountsRequestMultiError(errors)
	}

	return nil
}

// ListMetaAdsAccountsRequestMultiError is an error wrapping multiple
// validation errors returned by ListMetaAdsAccountsRequest.ValidateAll() if
// the designated constraints aren't met.
type ListMetaAdsAccountsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMetaAdsAccountsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMetaAdsAccountsRequestMultiError) AllErrors() []error { return m }

// ListMetaAdsAccountsRequestValidationError is the validation error returned
// by ListMetaAdsAccountsRequest.Validate if the designated constraints aren't met.
type ListMetaAdsAccountsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMetaAdsAccountsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMetaAdsAccountsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMetaAdsAccountsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMetaAdsAccountsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMetaAdsAccountsRequestValidationError) ErrorName() string {
	return "ListMetaAdsAccountsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListMetaAdsAccountsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMetaAdsAccountsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMetaAdsAccountsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMetaAdsAccountsRequestValidationError{}

// Validate checks the field values on ListMetaAdsAccountsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMetaAdsAccountsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMetaAdsAccountsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMetaAdsAccountsResponseMultiError, or nil if none found.
func (m *ListMetaAdsAccountsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMetaAdsAccountsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAccounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListMetaAdsAccountsResponseValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListMetaAdsAccountsResponseValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListMetaAdsAccountsResponseValidationError{
					field:  fmt.Sprintf("Accounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListMetaAdsAccountsResponseMultiError(errors)
	}

	return nil
}

// ListMetaAdsAccountsResponseMultiError is an error wrapping multiple
// validation errors returned by ListMetaAdsAccountsResponse.ValidateAll() if
// the designated constraints aren't met.
type ListMetaAdsAccountsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMetaAdsAccountsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMetaAdsAccountsResponseMultiError) AllErrors() []error { return m }

// ListMetaAdsAccountsResponseValidationError is the validation error returned
// by ListMetaAdsAccountsResponse.Validate if the designated constraints
// aren't met.
type ListMetaAdsAccountsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMetaAdsAccountsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMetaAdsAccountsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMetaAdsAccountsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMetaAdsAccountsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMetaAdsAccountsResponseValidationError) ErrorName() string {
	return "ListMetaAdsAccountsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListMetaAdsAccountsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMetaAdsAccountsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMetaAdsAccountsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMetaAdsAccountsResponseValidationError{}

// Validate checks the field values on LinkMetaAdsAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LinkMetaAdsAccountsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinkMetaAdsAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LinkMetaAdsAccountsRequestMultiError, or nil if none found.
func (m *LinkMetaAdsAccountsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LinkMetaAdsAccountsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for StaffId

	// no validation rules for BusinessId

	if len(errors) > 0 {
		return LinkMetaAdsAccountsRequestMultiError(errors)
	}

	return nil
}

// LinkMetaAdsAccountsRequestMultiError is an error wrapping multiple
// validation errors returned by LinkMetaAdsAccountsRequest.ValidateAll() if
// the designated constraints aren't met.
type LinkMetaAdsAccountsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinkMetaAdsAccountsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinkMetaAdsAccountsRequestMultiError) AllErrors() []error { return m }

// LinkMetaAdsAccountsRequestValidationError is the validation error returned
// by LinkMetaAdsAccountsRequest.Validate if the designated constraints aren't met.
type LinkMetaAdsAccountsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinkMetaAdsAccountsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinkMetaAdsAccountsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinkMetaAdsAccountsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinkMetaAdsAccountsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinkMetaAdsAccountsRequestValidationError) ErrorName() string {
	return "LinkMetaAdsAccountsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LinkMetaAdsAccountsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinkMetaAdsAccountsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinkMetaAdsAccountsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinkMetaAdsAccountsRequestValidationError{}

// Validate checks the field values on GetLinkSettingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLinkSettingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLinkSettingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLinkSettingRequestMultiError, or nil if none found.
func (m *GetLinkSettingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLinkSettingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	if len(errors) > 0 {
		return GetLinkSettingRequestMultiError(errors)
	}

	return nil
}

// GetLinkSettingRequestMultiError is an error wrapping multiple validation
// errors returned by GetLinkSettingRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLinkSettingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLinkSettingRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLinkSettingRequestMultiError) AllErrors() []error { return m }

// GetLinkSettingRequestValidationError is the validation error returned by
// GetLinkSettingRequest.Validate if the designated constraints aren't met.
type GetLinkSettingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLinkSettingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLinkSettingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLinkSettingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLinkSettingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLinkSettingRequestValidationError) ErrorName() string {
	return "GetLinkSettingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLinkSettingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLinkSettingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLinkSettingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLinkSettingRequestValidationError{}

// Validate checks the field values on GetLinkSettingResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLinkSettingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLinkSettingResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLinkSettingResponseMultiError, or nil if none found.
func (m *GetLinkSettingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLinkSettingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Link

	if len(errors) > 0 {
		return GetLinkSettingResponseMultiError(errors)
	}

	return nil
}

// GetLinkSettingResponseMultiError is an error wrapping multiple validation
// errors returned by GetLinkSettingResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLinkSettingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLinkSettingResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLinkSettingResponseMultiError) AllErrors() []error { return m }

// GetLinkSettingResponseValidationError is the validation error returned by
// GetLinkSettingResponse.Validate if the designated constraints aren't met.
type GetLinkSettingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLinkSettingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLinkSettingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLinkSettingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLinkSettingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLinkSettingResponseValidationError) ErrorName() string {
	return "GetLinkSettingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLinkSettingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLinkSettingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLinkSettingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLinkSettingResponseValidationError{}
