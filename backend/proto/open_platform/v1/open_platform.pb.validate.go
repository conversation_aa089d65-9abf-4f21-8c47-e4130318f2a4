// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/open_platform/v1/open_platform.proto

package open_platformpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GoogleOAuthUserInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GoogleOAuthUserInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoogleOAuthUserInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoogleOAuthUserInfoMultiError, or nil if none found.
func (m *GoogleOAuthUserInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GoogleOAuthUserInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	// no validation rules for FamilyName

	// no validation rules for Gender

	// no validation rules for GivenName

	// no validation rules for Hd

	// no validation rules for Id

	// no validation rules for Link

	// no validation rules for Locale

	// no validation rules for Name

	// no validation rules for Picture

	if m.VerifiedEmail != nil {
		// no validation rules for VerifiedEmail
	}

	if len(errors) > 0 {
		return GoogleOAuthUserInfoMultiError(errors)
	}

	return nil
}

// GoogleOAuthUserInfoMultiError is an error wrapping multiple validation
// errors returned by GoogleOAuthUserInfo.ValidateAll() if the designated
// constraints aren't met.
type GoogleOAuthUserInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoogleOAuthUserInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoogleOAuthUserInfoMultiError) AllErrors() []error { return m }

// GoogleOAuthUserInfoValidationError is the validation error returned by
// GoogleOAuthUserInfo.Validate if the designated constraints aren't met.
type GoogleOAuthUserInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoogleOAuthUserInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoogleOAuthUserInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoogleOAuthUserInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoogleOAuthUserInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoogleOAuthUserInfoValidationError) ErrorName() string {
	return "GoogleOAuthUserInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GoogleOAuthUserInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoogleOAuthUserInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoogleOAuthUserInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoogleOAuthUserInfoValidationError{}

// Validate checks the field values on GoogleAdsSetting with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GoogleAdsSetting) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoogleAdsSetting with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoogleAdsSettingMultiError, or nil if none found.
func (m *GoogleAdsSetting) ValidateAll() error {
	return m.validate(true)
}

func (m *GoogleAdsSetting) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GoogleAdsSettingMultiError(errors)
	}

	return nil
}

// GoogleAdsSettingMultiError is an error wrapping multiple validation errors
// returned by GoogleAdsSetting.ValidateAll() if the designated constraints
// aren't met.
type GoogleAdsSettingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoogleAdsSettingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoogleAdsSettingMultiError) AllErrors() []error { return m }

// GoogleAdsSettingValidationError is the validation error returned by
// GoogleAdsSetting.Validate if the designated constraints aren't met.
type GoogleAdsSettingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoogleAdsSettingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoogleAdsSettingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoogleAdsSettingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoogleAdsSettingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoogleAdsSettingValidationError) ErrorName() string { return "GoogleAdsSettingValidationError" }

// Error satisfies the builtin error interface
func (e GoogleAdsSettingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoogleAdsSetting.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoogleAdsSettingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoogleAdsSettingValidationError{}

// Validate checks the field values on GoogleAdsCustomer with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GoogleAdsCustomer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoogleAdsCustomer with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoogleAdsCustomerMultiError, or nil if none found.
func (m *GoogleAdsCustomer) ValidateAll() error {
	return m.validate(true)
}

func (m *GoogleAdsCustomer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ResourceName

	if m.Id != nil {
		// no validation rules for Id
	}

	if m.DescriptiveName != nil {
		// no validation rules for DescriptiveName
	}

	if m.CurrencyCode != nil {
		// no validation rules for CurrencyCode
	}

	if m.TimeZone != nil {
		// no validation rules for TimeZone
	}

	if m.TrackingUrlTemplate != nil {
		// no validation rules for TrackingUrlTemplate
	}

	if m.FinalUrlSuffix != nil {
		// no validation rules for FinalUrlSuffix
	}

	if m.AutoTaggingEnabled != nil {
		// no validation rules for AutoTaggingEnabled
	}

	if m.HasPartnersBadge != nil {
		// no validation rules for HasPartnersBadge
	}

	if m.Manager != nil {
		// no validation rules for Manager
	}

	if m.TestAccount != nil {
		// no validation rules for TestAccount
	}

	if m.OptimizationScore != nil {
		// no validation rules for OptimizationScore
	}

	if len(errors) > 0 {
		return GoogleAdsCustomerMultiError(errors)
	}

	return nil
}

// GoogleAdsCustomerMultiError is an error wrapping multiple validation errors
// returned by GoogleAdsCustomer.ValidateAll() if the designated constraints
// aren't met.
type GoogleAdsCustomerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoogleAdsCustomerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoogleAdsCustomerMultiError) AllErrors() []error { return m }

// GoogleAdsCustomerValidationError is the validation error returned by
// GoogleAdsCustomer.Validate if the designated constraints aren't met.
type GoogleAdsCustomerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoogleAdsCustomerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoogleAdsCustomerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoogleAdsCustomerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoogleAdsCustomerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoogleAdsCustomerValidationError) ErrorName() string {
	return "GoogleAdsCustomerValidationError"
}

// Error satisfies the builtin error interface
func (e GoogleAdsCustomerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoogleAdsCustomer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoogleAdsCustomerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoogleAdsCustomerValidationError{}

// Validate checks the field values on MetaOAuthUserInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MetaOAuthUserInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MetaOAuthUserInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MetaOAuthUserInfoMultiError, or nil if none found.
func (m *MetaOAuthUserInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MetaOAuthUserInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	// no validation rules for Name

	// no validation rules for Id

	if len(errors) > 0 {
		return MetaOAuthUserInfoMultiError(errors)
	}

	return nil
}

// MetaOAuthUserInfoMultiError is an error wrapping multiple validation errors
// returned by MetaOAuthUserInfo.ValidateAll() if the designated constraints
// aren't met.
type MetaOAuthUserInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MetaOAuthUserInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MetaOAuthUserInfoMultiError) AllErrors() []error { return m }

// MetaOAuthUserInfoValidationError is the validation error returned by
// MetaOAuthUserInfo.Validate if the designated constraints aren't met.
type MetaOAuthUserInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MetaOAuthUserInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MetaOAuthUserInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MetaOAuthUserInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MetaOAuthUserInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MetaOAuthUserInfoValidationError) ErrorName() string {
	return "MetaOAuthUserInfoValidationError"
}

// Error satisfies the builtin error interface
func (e MetaOAuthUserInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMetaOAuthUserInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MetaOAuthUserInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MetaOAuthUserInfoValidationError{}

// Validate checks the field values on MetaAdsAccount with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MetaAdsAccount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MetaAdsAccount with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MetaAdsAccountMultiError,
// or nil if none found.
func (m *MetaAdsAccount) ValidateAll() error {
	return m.validate(true)
}

func (m *MetaAdsAccount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for AccountId

	// no validation rules for Name

	// no validation rules for AccountStatus

	// no validation rules for CurrencyCode

	// no validation rules for AmountSpent

	// no validation rules for BusinessName

	if len(errors) > 0 {
		return MetaAdsAccountMultiError(errors)
	}

	return nil
}

// MetaAdsAccountMultiError is an error wrapping multiple validation errors
// returned by MetaAdsAccount.ValidateAll() if the designated constraints
// aren't met.
type MetaAdsAccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MetaAdsAccountMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MetaAdsAccountMultiError) AllErrors() []error { return m }

// MetaAdsAccountValidationError is the validation error returned by
// MetaAdsAccount.Validate if the designated constraints aren't met.
type MetaAdsAccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MetaAdsAccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MetaAdsAccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MetaAdsAccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MetaAdsAccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MetaAdsAccountValidationError) ErrorName() string { return "MetaAdsAccountValidationError" }

// Error satisfies the builtin error interface
func (e MetaAdsAccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMetaAdsAccount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MetaAdsAccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MetaAdsAccountValidationError{}

// Validate checks the field values on MetaAdsSetting with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MetaAdsSetting) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MetaAdsSetting with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MetaAdsSettingMultiError,
// or nil if none found.
func (m *MetaAdsSetting) ValidateAll() error {
	return m.validate(true)
}

func (m *MetaAdsSetting) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return MetaAdsSettingMultiError(errors)
	}

	return nil
}

// MetaAdsSettingMultiError is an error wrapping multiple validation errors
// returned by MetaAdsSetting.ValidateAll() if the designated constraints
// aren't met.
type MetaAdsSettingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MetaAdsSettingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MetaAdsSettingMultiError) AllErrors() []error { return m }

// MetaAdsSettingValidationError is the validation error returned by
// MetaAdsSetting.Validate if the designated constraints aren't met.
type MetaAdsSettingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MetaAdsSettingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MetaAdsSettingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MetaAdsSettingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MetaAdsSettingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MetaAdsSettingValidationError) ErrorName() string { return "MetaAdsSettingValidationError" }

// Error satisfies the builtin error interface
func (e MetaAdsSettingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMetaAdsSetting.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MetaAdsSettingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MetaAdsSettingValidationError{}
