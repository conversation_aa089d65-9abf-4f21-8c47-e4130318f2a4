syntax = "proto3";

package backend.proto.search.v1;
import "google/protobuf/struct.proto";
import "validate/validate.proto";
import "google/api/field_behavior.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/search/v1;searchpb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.search.v1";

// 精确查询（完全匹配字段值）
// 适用场景：字段值完全等于指定值的场景
// 示例：查询 name 字段值为 "jett" 的文档
// TermStrategy { field: "name", value: "jett" }
// 对应open search的查询语句为:
// {
//     "query": {
//         "term": {
//             "name": "jett"
//         }
//     }
// }
// 
// 值得注意的是, open search的text模式会自动分词, 所以如果存在例如"jett-4396"这样的字段
// 需要强行使用keyword模式, 否则会查询不到结果
// 即使用: TermStrategy { field: "name.keyword", value: "jett-4396" }
// 对应open search的查询语句为:
// {
//     "query": {
//         "term": {
//             "name.keyword": "jett-4396"
//         }
//     }
// }
message TermStrategy {
    // 字段名
    string field = 1 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
    // 字段值
    google.protobuf.Value value = 2 [(validate.rules).any.required = true, (google.api.field_behavior) = REQUIRED];
}


// 全文搜索（支持分词查询）
// 适用场景：文本字段的模糊搜索
// 示例1：查询 title 字段包含 "手机" AND "5G" 的商品
// MatchStrategy { field: "title", query: "手机 5G", operator: AND }
// 对应open search的查询语句为:
// {
//     "query": {
//         "match": {
//             "title": {
//                 "query": "手机 5G",
//                 "operator": "AND"
//             }
//         }
//     }
// }
// result: {
//     "hits": {
//         "hits": [
//             {
//                 "title": "华为手机5G就是牛"
//             }
//         ]
//     }
// }
// 
// 示例2：查询 content 字段包含 "人工智能" OR "AI" 的文章
// MatchStrategy { field: "content", query: "人工智能 AI", operator: OR }
// 对应open search的查询语句为:
// {
//     "query": {
//         "match": {
//             "content": {
//                 "query": "人工智能 AI",
//                 "operator": "OR"
//             }
//         }
//     }
// }
// result: {
//     "hits": {
//         "hits": [
//             {
//                 "content": "华为人工智能真牛"
//             },
//             {
//                 "content": "华为AI真牛"
//             }
//         ]
//     }
// }
message MatchStrategy {
    // 需要添加的枚举
    enum MatchOperator {
        // 未指定匹配操作符
        MATCH_OPERATOR_UNSPECIFIED = 0;
        // 必须匹配所有条件
        AND = 1;
        // 至少匹配一个条件
        OR = 2;
    }
    // 字段名
    string field = 1 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
    // 查询条件
    string query = 2 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
    // 权重
    float boost = 3 [(google.api.field_behavior) = OPTIONAL];
    // 匹配方式
    MatchOperator operator = 4 [(google.api.field_behavior) = REQUIRED];
}

// 范围查询（支持数值/日期范围）
// 示例1：查询价格在 1000-2000 之间的商品
// RangeStrategy { field: "price", gte: 1000, lte: 2000 }
// 对应open search的查询语句为:
// {
//     "query": {
//         "range": {
//             "price": {
//                 "gte": 1000,
//                 "lte": 2000
//             }
//         }
//     }
// }
// 需要注意的是, 必须知道字段类型, 例如:
// 如果字段是日期类型, 则gte和lte需要传入时间戳, 例如: 1712985600000
// 如果字段是字符串类型的时间, 则gte和lte需要传入时间字符串, 例如: "2024-04-01 00:00:00"
// 如果字段是数值类型, 则gte和lte需要传入数值, 例如: 1000
message RangeStrategy {
    // 字段名
    string field = 1 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
    // 大于等于的值
    google.protobuf.Value gte = 2 [(google.api.field_behavior) = OPTIONAL];
    // 小于等于的值
    google.protobuf.Value lte = 3 [(google.api.field_behavior) = OPTIONAL];
}


// 通配符匹配（支持 * 和 ? 通配符）
// 适用场景：简单模式匹配（慎用，性能较低）
// 示例：查询以 "华为" 开头，以 "手机" 结尾的商品名称
// WildcardStrategy { field: "name", value: "华为手机*" }
// 对应open search的查询语句为:
// {
//     "query": {
//         "wildcard": {
//             "name": "华为手机*"
//         }
//     }
// }
// result: {
//     "hits": {
//         "hits": [
//             {
//                 "name": "华为手机就是牛"
//             }
//         ]
//     }
// }
message WildcardStrategy {
    // 字段名
    string field = 1 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
    // 通配符表达式
    string value = 2 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
}

// 多值精确匹配（支持多个值的精确匹配）
// 适用场景：字段值为多个固定值的场景
// 示例：查询 name 字段值为 "华为" 或 "小米" 的商品
// TermsStrategy { field: "name", values: ["华为", "小米"] }
// 对应open search的查询语句为:
// {
//     "query": {
//         "terms": {
//             "name": ["华为", "小米"]
//         }
//     }
// }
// result: {
//     "hits": {
//         "hits": [
//             {
//                 "name": "华为"
//             },
//             {
//                 "name": "小米"
//             }
//         ]
//     }
// }
// 需要注意的是, Terms 有着和Term 一样的问题, 如果避免分词, 请使用Terms{ filed: "name.keyword", values: ["华为-4396","小米-2200"]}
message TermsStrategy {
    // 字段名
    string field = 1 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
    // 字段值
    repeated google.protobuf.Value values = 2 [(validate.rules).repeated .min_items = 1, (google.api.field_behavior) = REQUIRED];
}

// 布尔组合查询（支持复杂逻辑组合）
// 适用场景：
// 1. 需要组合多个查询条件的场景（AND/OR/NOT）
// 2. 需要控制相关性得分的场景
// 
// 示例1：电商商品搜索（必须满足所有条件）
// 条件: category == 手机 && price >= 1000 && price <= 5000 && title contains "5G 旗舰" && status != 0
// BoolStrategy {
//     must: [
//         { terms: { field: "category", values: ["手机"] } },  // 分类必须是手机
//         { range: { field: "price", gte: 1000, lte: 5000 } }, // 价格在1000-5000
//         { match: { field: "title", query: "5G 旗舰", operator: AND } } // 标题包含5G和旗舰
//     ],
//     must_not: [
//         { term: { field: "status", value: 0 } }  // 排除下架商品
//     ]
// }
//
// 示例2：订单搜索（至少满足一个条件）
// 条件: order_no == "MOE202307011234" || user_name == "jett"
// BoolStrategy {
//     should: {
//         strategies: [
//             { term: { field: "order_no", value: "MOE202307011234" } },  // 按订单号查询
//             { match: { field: "user_name", query: "jett" } }  // 按用户名模糊匹配
//         ],
//         minimum_match: 1  // 至少满足一个条件
//     }
// }
//
// 示例3：组合复杂查询（必须满足A且B，同时排除C，或满足D）
// BoolStrategy {
//     must: [A, B],
//     must_not: [C],
//     should: {
//         strategies: [D],
//         minimum_match: 1
//     }
// }
message BoolStrategy {
    // 用于指定至少满足条件的策略
    message BoolShouldStrategy {
        // 需要满足的条件
        repeated Strategy strategies = 1 [(google.api.field_behavior) = REQUIRED];
        // 至少满足的条件数量
        int32 minimum_match = 2 [(google.api.field_behavior) = REQUIRED];
    }
    // 必须满足的条件
    repeated Strategy must = 1 [(google.api.field_behavior) = OPTIONAL];
    // 必须不满足的条件
    repeated Strategy must_not = 2 [(google.api.field_behavior) = OPTIONAL];
    // 过滤条件
    repeated Strategy filter = 3 [(google.api.field_behavior) = OPTIONAL];
    // 至少满足的条件
    BoolShouldStrategy should = 4 [(google.api.field_behavior) = OPTIONAL];
}

// 多字段匹配策略（支持在多个字段中搜索相同的文本）
// 适用场景：需要在多个字段中搜索相同内容，并可以为不同字段设置权重
// 示例：在名称、描述、标签等多个字段中搜索"手机"
// MultiMatchStrategy {
//     query: "手机",
//     type: "most_fields",
//     fields: ["name^3", "description^2", "tags"],
//     fuzziness: "AUTO:3,6",
//     prefix_length: 2,
//     boost: 2
// }
// 对应OpenSearch查询:
// {
//     "multi_match": {
//         "query": "手机",
//         "type": "most_fields",
//         "fields": ["name^3", "description^2", "tags"],
//         "fuzziness": "AUTO:3,6",
//         "prefix_length": 2,
//         "boost": 2
//     }
// }
message MultiMatchStrategy {
    // 多字段匹配类型枚举
    enum MatchType {
        // 未指定匹配类型
        MATCH_TYPE_UNSPECIFIED = 0;
        // 查找最匹配的字段 (默认)
        BEST_FIELDS = 1;
        // 合并多个字段的匹配分数
        MOST_FIELDS = 2;
        // 将所有字段视为一个大字段
        CROSS_FIELDS = 3;
        // 在每个字段中作为短语匹配
        PHRASE = 4;
        // 短语前缀匹配
        PHRASE_PREFIX = 5;
    }
    // 查询文本
    string query = 1 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
    // 匹配类型
    MatchType type = 2 [(google.api.field_behavior) = REQUIRED];
    // 要搜索的字段列表，可以包含权重，如 "name^3"
    repeated string fields = 3 [(validate.rules).repeated.min_items = 1, (google.api.field_behavior) = REQUIRED];
    // 模糊匹配参数，如 "AUTO" 或 "AUTO:3,6" 或具体数字
    string fuzziness = 4 [(google.api.field_behavior) = OPTIONAL];
    // 前缀长度（用于模糊匹配时保持前缀不变）
    int32 prefix_length = 5 [(google.api.field_behavior) = OPTIONAL];
    // 查询权重
    float boost = 6 [(google.api.field_behavior) = OPTIONAL];
}

// 查询字符串策略（支持高级查询语法）
// 适用场景：需要使用复杂查询语法，如通配符、正则表达式、模糊搜索等
// 示例：搜索包含"手机"且价格在1000-5000之间的商品
// QueryStringStrategy {
//     query: "手机 AND price:[1000 TO 5000]",
//     fields: ["name", "description"],
//     analyze_wildcard: true,
//     default_operator: "AND",
//     boost: 1.5
// }
// 对应OpenSearch查询:
// {
//     "query_string": {
//         "query": "手机 AND price:[1000 TO 5000]",
//         "fields": ["name", "description"],
//         "analyze_wildcard": true,
//         "default_operator": "AND",
//         "boost": 1.5
//     }
// }
message QueryStringStrategy {
    // 默认操作作
    enum DefaultOperator {
        // 未指定默认操作作
        DEFAULT_OPERATOR_UNSPECIFIED = 0;
        // 必须匹配所有条件
        AND = 1;
        // 至少匹配一个条件
        OR = 2;
    }
    // 查询字符串，支持高级语法
    string query = 1 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
    // 要搜索的字段列表
    repeated string fields = 2 [(google.api.field_behavior) = OPTIONAL];
    // 是否分析通配符
    bool analyze_wildcard = 3 [(google.api.field_behavior) = OPTIONAL];
    // 默认操作符
    DefaultOperator default_operator = 4 [(google.api.field_behavior) = OPTIONAL];
    // 查询权重
    float boost = 5 [(google.api.field_behavior) = OPTIONAL];
}

// 短语匹配策略（精确匹配短语，考虑词序）
// 适用场景：需要精确匹配短语，并可以设置词之间的最大间隔
// 示例：搜索标题中包含"5G手机"的商品
// MatchPhraseStrategy {
//     field: "title",
//     query: "5G手机",
//     slop: 1,
//     boost: 1.2
// }
// 对应OpenSearch查询:
// {
//     "match_phrase": {
//         "title": {
//             "query": "5G手机",
//             "slop": 1,
//             "boost": 1.2
//         }
//     }
// }
message MatchPhraseStrategy {
    // 字段名
    string field = 1 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
    // 查询短语
    string query = 2 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
    // 词之间允许的最大间隔
    int32 slop = 3 [(google.api.field_behavior) = OPTIONAL];
    // 查询权重
    float boost = 4 [(google.api.field_behavior) = OPTIONAL];
}


// 搜索策略
// 用于定义不同类型的搜索条件
message Strategy {
    oneof strategy {
        // 精确搜索
        TermStrategy term = 1;
        // 模糊匹配
        MatchStrategy match = 2;
        // 范围查询
        RangeStrategy range = 3;
        // 通配符匹配
        WildcardStrategy wildcard = 4;
        // 多值精确匹配
        TermsStrategy terms = 5;
        // 多条件匹配
        BoolStrategy bool = 6;

        // 以下为扩展字段, 用于支持更多搜索策略
        // multi match
        MultiMatchStrategy multi_match = 7;
        // match phrase
        MatchPhraseStrategy match_phrase = 8;
        // query string
        QueryStringStrategy query_string = 9;
    }
}