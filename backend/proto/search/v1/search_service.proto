syntax = "proto3";

package backend.proto.search.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/search/v1;searchpb";// 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.search.v1";

import "validate/validate.proto";
import "google/protobuf/struct.proto";
import "google/api/annotations.proto";
import "google/api/field_behavior.proto";

import "backend/proto/search/v1/search_strategy.proto";
import "backend/proto/search/v1/document.proto";
// 搜索服务定义
service SearchService {
    // Search 执行搜索操作
    rpc SearchDocument (SearchDocumentRequest) returns (SearchDocumentResponse);

    // Bulk Document, 批量操作, 可以是增删改document
    // 注意:
    // 1. 非原子性操作: bulk API 不是事务性的，不支持回滚
    // 2. 独立执行: 每个操作都是独立的，某个操作失败不会影响其他操作的执行
    // 3. 返回结果: 会返回每个操作的执行结果，包括成功和失败的详细信息
    rpc BulkDocument (BulkDocumentRequest) returns (BulkDocumentResponse);
}

// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 搜索场景下没有分页的需求, 所以没有必要设置page_token --)
// SearchDocumentRequest 定义搜索请求消息
message SearchDocumentRequest {
    // 排序字段
    message Sort {
        // 排序方向
        enum Order {
            // 未指定排序方向
            ORDER_UNSPECIFIED = 0;
            // 升序
            ASC = 1;
            // 降序
            DESC = 2;
        }
        // 排序字段
        string field = 1 [
            (validate.rules).string = {ignore_empty: false, min_len: 1},
            (google.api.field_behavior) = REQUIRED
        ];
        // 排序方向, 必填, search service 不会设置任何默认值
        Order order = 2 [
            (validate.rules).enum = {in: [1, 2]},
            (google.api.field_behavior) = REQUIRED
        ];
    }

    // 搜索策略组合(使用 BoolStrategy 构建复杂查询条件)
    // 具体组合可以翻阅 Strategy 实现
    Strategy strategy = 1 [
        (validate.rules).message.required = true,
        (google.api.field_behavior) = REQUIRED
    ];

    // index 索引
    string index = 2 [
        (validate.rules).string = {ignore_empty: false, min_len: 1},
        (google.api.field_behavior) = REQUIRED
    ];

    // 每页大小, 必填
    // 请根据业务需求自定义分页大小, search service 不会设置默认值
    int32 page_size = 3 [
        (validate.rules).int32 = {gt: 0},
        (google.api.field_behavior) = REQUIRED
    ];

    // (-- api-linter: core::0140::prepositions=disabled
    //     aip.dev/not-precedent: 这里必须叫 search_after, 不能叫 offset --)
    // 分页起点, 非必填
    // 可以作为分页查询的起始点, 用于获取分页数据, 返回结果为offset + 1, 如果为空则从第一页开始查询
    // 当填写 offset 时, 字段值必须和 sort 字段值一致, 比如:
    // sort = {field: "created_at", order: DESC}, 则 offset: "2024-03-14T10:00:00Z"
    // open search 会先按照 created_at 字段降序排序, 然后从 2024-03-14T10:00:00Z 开始查询, ** 注意: 不包含2024-03-14T10:00:00Z **
    // 
    // 同理 sort = {field: "id", order: DESC} 则 offset: 4396 
    // open search 会先按照 id 字段降序排序, 然后从 4396 开始查询, ** 注意: 不包含4396 **
    repeated google.protobuf.Value search_after = 4 [(google.api.field_behavior) = OPTIONAL];

    // 排序字段
    // Search Service 不会赋予任何默认排序, open search 默认根据查询条件相关性排序
    // 上游需要根据业务需求自行排序
    // 
    // 举例: 如果使用_id(open search的id) 排序, 则 sort = {field: "_id", order: ASC} (注意区分open search 的 _id 和 业务数据 的 id)
    // 如果使用创建时间排序, 则 sort = {field: "created_at", order: DESC}
    repeated Sort sort = 5 [
        (validate.rules).repeated = {min_items: 1},
        (google.api.field_behavior) = REQUIRED
    ];
}

// SearchDocumentResponse 定义搜索响应
message SearchDocumentResponse {
    // Hit 搜索结果命中项
    message Hit {
        // 文档所属索引, 类似数据库表名
        string index = 1;
        // 文档 id
        string id = 2;
        // 相关性得分, 越高表示匹配程度越高, 可用于结果排序（但不同查询的分数不可直接比较）
        double score = 3;
        // 原始文档数据
        google.protobuf.Struct source = 4;
        // 排序
        repeated google.protobuf.Value sort = 5;
    }
    // Total 定义总数信息
    message Total {
        // 分页时计算总页数的重要依据（但要注意 relation 的说明）
        int32 value = 1;
        // 总数关系标识：
        // - "eq" 表示精确总数
        // - "gte" 表示最小总数（当总数过大时可能返回近似值）
        string relation = 2;
    }
    // 搜索命中项列表
    repeated Hit hits = 1;
    // 下一页令牌
    string next_page_token = 2;
    // 响应耗时（单位：毫秒），表示整个搜索请求的处理时间
    int32 took = 3;
    // 是否超时
    bool timed_out = 4;
    // 搜索命中总数
    Total total = 5;
    // 最大相关性得分, 帮助判断整体匹配质量（比如 0 分可能表示无匹配）
    double max_score = 6;

}

// ErrCode 定义错误码枚举
// 
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
enum ErrCode {
    // (-- api-linter: core::0126::unspecified=disabled
    //     aip.dev/not-precedent: We need to do this because 
    //     the content of the error code is automatically generated by
    //     the script and is exclusive to each service.
    //     Please do not turn off this linter for the rest of the enum --)
    // 成功
    ERR_CODE_OK = 0;
    // 服务自动生成的唯一错误码
    ERR_CODE_UNSPECIFIED = 119900; 
}