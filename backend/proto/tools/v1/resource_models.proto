syntax = "proto3";

package backend.proto.tools.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.tools.v1";

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";


// PlatformIdentifier
message PlatformIdentifier {
    // resource platform
    string platform = 1 [(validate.rules).string = {max_len: 128}];
    // resource identifier
    string identifier = 2 [(validate.rules).string = {max_len: 1024}];
}

// EnvironmentIdentifier: unique identifier for an environment
message EnvironmentIdentifier {
    // kubernetes cluster
    string cluster = 1 [(validate.rules).string = {max_len: 63}];
    // kubernetes namespace
    string namespace = 2 [(validate.rules).string = {max_len: 63}];
}

// ResourceIdentifier
message ResourceIdentifier {
    // api group/version
    optional string api_version = 1;
    // resource kind
    string kind = 2;
    // resource name
    string name = 3;
}

// Endpoint
message Endpoint {
    // address
    string address = 1 [(validate.rules).string = { max_len: 1024}];
    // port
    int32 port = 2 [(validate.rules).int32 = { lt: 65536}];
}

// Environment
message Environment {
    // environment identifier
    PlatformIdentifier identifier = 1;
    // environment status
    string status = 2;
    // environment is managed?
    bool is_managed = 3;
    // environment name, use to display
    optional string name = 4 [(validate.rules).string = {max_len: 64}];
    // environment description
    optional string description = 5 [(validate.rules).string = {max_len: 1024}];
    // list of databases
    repeated PlatformIdentifier databases = 6;
    // list of caches
    repeated PlatformIdentifier caches = 7;
    // list of message queues
    repeated PlatformIdentifier message_queues = 8;

    // extra info
    optional google.protobuf.Struct extra = 13;
    // create time
    optional google.protobuf.Timestamp create_time = 14;
    // update time
    optional google.protobuf.Timestamp update_time = 15;
    // delete time
    optional google.protobuf.Timestamp delete_time = 16;
}

// Cluster
message Cluster {
    // cluster identifier
    PlatformIdentifier identifier = 1;
    // cluster version
    string version = 2;
    // cluster endpoint
    string endpoint = 3;
    // cluster status
    string status = 4;

    // cluster labels
    map<string, string> labels = 6;
    // create time
    google.protobuf.Timestamp create_time = 7;
}

// NodeGroup
message NodeGroup {
    // nodegroup identifier
    Identifier identifier = 1;
    // node group status
    string status = 2;
    // The instance class of the node group
    string instance_class = 3;
    // The image used by the node group
    string image = 4;
    // Minimum number of nodes in a node group
    optional int32 min_size = 5;
    // Maximum number of nodes in a node group
    optional int32 max_size = 6;
    // Desired number of nodes in a node group
    optional int32 desired_size = 7;
    // disk size for instance
    optional int32 disk_size = 8;
    // The labels attached to the node group
    map<string, string> labels = 13 [(validate.rules).map = {
        max_pairs: 256,
        keys: {
            string: {max_len: 255}
        },
        values: {
            string: {max_len: 63}
        }
    }];

    // extra info
    google.protobuf.Struct extra = 14;
    // create time
    google.protobuf.Timestamp create_time = 15;
    // update time
    google.protobuf.Timestamp update_time = 16;

    // Identifier
    message Identifier {
        // The platform where the node group is located
        string platform = 1 [(validate.rules).string = {max_len: 128}];
        // The cluster where the node group is located
        string cluster = 2 [(validate.rules).string = {max_len: 63}];
        // node group name
        string name = 3 [(validate.rules).string = {max_len: 63}];
    }
}

// Database
message Database {
    // database identifier
    PlatformIdentifier identifier = 1;
    // database status
    string status = 2;
    // database engine
    string engine = 3;
    // database engine version
    string version = 4;
    // database endpoints
    repeated Endpoint endpoints = 5;
    // database port
    int32 port = 6;
    // database instance class
    string instance_class = 7;
    // database storage size
    int32 allocated_size = 8;
    // Whether the database is publicly accessible
    bool publicly_accessible = 9;
    // The labels attached to the database
    map<string, string> labels = 13 [(validate.rules).map = {
        max_pairs: 256,
        keys: {
            string: {max_len: 255}
        },
        values: {
            string: {max_len: 63}
        }
    }];
    // extra info
    google.protobuf.Struct extra = 14;
    // create time
    google.protobuf.Timestamp create_time = 15;
    // update time
    google.protobuf.Timestamp update_time = 16;
}

// Cache
message Cache {
    // cache identifier
    PlatformIdentifier identifier = 1;
    // cache status
    string status = 2;
    // cache engine
    string engine = 3;
    // cache engine version
    string version = 4;
    // cache endpoints
    repeated Endpoint endpoints = 5;
    // cache engine version
    int32 port = 6;
    // cache instance class
    string instance_class = 7;
    // cache storage size
    int32 allocated_size = 8;
    // whether the cache is publicly accessible
    bool publicly_accessible = 9;
    // whether the cache is enable tls
    bool tls = 10;
    // The labels attached to the cache
    map<string, string> labels = 13 [(validate.rules).map = {
        max_pairs: 256,
        keys: {
            string: {max_len: 255}
        },
        values: {
            string: {max_len: 63}
        }
    }];
    // extra info
    google.protobuf.Struct extra = 14;
    // create time
    google.protobuf.Timestamp create_time = 15;
    // update time
    google.protobuf.Timestamp update_time = 16;
}

// MessageQueue
message MessageQueue {
    // MQ identifier
    PlatformIdentifier identifier = 1;
    // MQ status
    string status = 2;
    // MQ engine
    string engine = 3;
    // MQ engine version
    string version = 4;
    // MQ endpoints
    repeated Endpoint endpoints = 5;
    // MQ replicas
    int32 replicas = 6;
    // MQ storage size
    int32 allocated_size = 7;
    // MQ instance class
    string instance_class = 8;
    // Whether the MQ has deletion protection enabled
    bool deletion_protection = 9;
    // Whether the MQ is publicly accessible
    bool publicly_accessible = 10;
    // The labels attached to the MQ
    map<string, string> labels = 13 [(validate.rules).map = {
        max_pairs: 256,
        keys: {
            string: {max_len: 255}
        },
        values: {
            string: {max_len: 63}
        }
    }];
    // MQ extra info
    google.protobuf.Struct extra = 14;
    // create time
    google.protobuf.Timestamp create_time = 15;
    // update time
    google.protobuf.Timestamp update_time = 16;
}

// Topic
message Topic {
    // topic id
    optional string id = 1;
    // topic name
    string name = 2;
    // topic engine
    string engine = 3;
    // topic tenant
    optional string tenant = 4;
    // topic namespace
    optional string namespace = 5;
    // is internal topic?
    optional bool internal = 6;
    // partition number
    optional int32 partition_number = 7;
    // replication factor
    optional int32 replication_factor = 8;
    // extra config
    optional google.protobuf.Struct config = 9;
}

// PlatformType
enum PlatformType {
    // unspecified
    PLATFORM_TYPE_UNSPECIFIED = 0;
    // self-hosted
    SELF_HOSTED = 1;
    // AWS Elastic Kubernetes Service
    AWS_EKS = 11;
    // AWS Elastic Container Registry
    AWS_ECR = 12;
    // AWS Relation Database Service
    AWS_RDS = 13;
    // AWS Managed Streaming for Apache Kafka
    AWS_MSK = 14;
    // AWS Simple Storage Service
    AWS_S3 = 15;
    // AWS Elastic Cache
    AWS_ELASTIC_CACHE = 16;
}
