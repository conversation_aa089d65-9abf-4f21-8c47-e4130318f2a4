syntax = "proto3";

package backend.proto.tools.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.tools.v1";

import "validate/validate.proto";
import "google/api/annotations.proto";
import "google/api/field_behavior.proto";

// test account service
service TestAccountService {
  // Borrow a test account
  rpc BorrowTestAccount(BorrowTestAccountRequest) returns (BorrowTestAccountResponse);

  // Return a test account via contract_id
  rpc ReturnTestAccount(ReturnTestAccountRequest) returns (ReturnTestAccountResponse);

  // Create a test account
  rpc CreateTestAccount(CreateTestAccountRequest) returns (TestAccount);

  // Release test accounts
  rpc ReleaseTestAccounts(ReleaseTestAccountsRequest) returns (ReleaseTestAccountsResponse);

  // List test accounts
  rpc ListTestAccounts(ListTestAccountsRequest) returns (ListTestAccountsResponse);
}


// TestAccount resource message
message TestAccount {
  // id
  int64 id = 1 [(google.api.field_behavior) = IMMUTABLE];
  // email
  string email = 2 [(google.api.field_behavior) = OPTIONAL];
  // password
  string password = 3 [(google.api.field_behavior) = OPTIONAL];
  // onwer
  string owner = 4 [(google.api.field_behavior) = OPTIONAL];
  // disposable
  bool disposable = 5 [(google.api.field_behavior) = OPTIONAL];
  // attributes
  Attributes attributes = 7 [(google.api.field_behavior) = OPTIONAL];
  // occupied
  bool occupied = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// attributes of test account
message Attributes {
  // 地区代码, 暂时只支持这些地区: US, CA, GB, AU, CN, 后续再添加其他地区
  optional string region_code = 1 [(validate.rules).string = {in: ["US", "CA", "GB", "AU", "CN"]}];

  // 是否启用 boarding daycare
  optional bool enable_boarding_daycare = 2;
  // 是否启用 online booking
  optional bool enable_online_booking = 3;
  // 是否启用 stripe
  optional bool enable_stripe = 6;
  // 是否有 SMS credit
  optional bool has_sms_credit = 7;
  // 是否有 email credit
  optional bool has_email_credit = 8;
}

// BorrowTestAccountRequest
message BorrowTestAccountRequest {
  // borrower (who are you)
  // suggest to use test case/suite name
  string borrower = 1 [
    (validate.rules).string = {
      min_len: 1
      max_len: 255
    },
    (google.api.field_behavior) = REQUIRED
  ];

  // 指定 identifier 获取账号
  // 可以指定一个 id 或者 email
  // 如果都不指定，则随机获取一个账号
  oneof identifier {
    // id
    int64 id = 2 [(validate.rules).int64.gt = 0];
    // email
    string email = 3 [(validate.rules).string.min_len = 1];
  }

  // attributes
  optional Attributes attributes = 4;

  // 在借用期间是否允许共享测试账号，即不占用测试账号，建议只读场景的 test case 开启
  optional bool shared = 5;
}

// BorrowTestAccountResponse
message BorrowTestAccountResponse {
  // id
  int64 id = 1;
  // email
  string email = 2;
  // password
  string password = 3;
  // contract id
  int64 contract_id = 4 [(google.api.field_behavior) = REQUIRED];
}

// ReturnTestAccountRequest
message ReturnTestAccountRequest {
  // contract id
  int64 contract_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// ReturnTestAccountResponse
message ReturnTestAccountResponse {}

// CreateTestAccountRequest
message CreateTestAccountRequest {
  // Required. The test account to create
  TestAccount test_account = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The test account ID to use for this request
  optional string test_account_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// ReleaseTestAccountsRequest
message ReleaseTestAccountsRequest {
  // overdue
  bool overdue = 1 [(google.api.field_behavior) = REQUIRED];
}

// ReleaseTestAccountsResponse
message ReleaseTestAccountsResponse {}

// ListTestAccountsRequest
message ListTestAccountsRequest {
  // Optional. The maximum number of accounts to return.
  // If empty, fetch 20 accounts by default.
  optional int32 page_size = 1 [(validate.rules).int32.gt = 0];
  // Optional. A token to retrieve the next page of results. 
  // Currently use as page number.
  // If empty, fetch the first page by default.
  optional string page_token = 2 [(validate.rules).string.min_len = 1];
}

// ListTestAccountsResponse
message ListTestAccountsResponse {
  // The list of test accounts.
  repeated TestAccount test_accounts = 1;
  // A token to retrieve the next page of results.
  // Currently use as page number.
  // If empty, there are no more pages.
  optional string next_page_token = 2;
  // total size
  int32 total_size = 3;
}
