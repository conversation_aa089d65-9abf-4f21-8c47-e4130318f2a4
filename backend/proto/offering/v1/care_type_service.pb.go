// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/offering/v1/care_type_service.proto

package offeringpb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request message for CreateCareType.
type CreateCareTypeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The care type to create.
	CareType      *CareType `protobuf:"bytes,1,opt,name=care_type,json=careType,proto3" json:"care_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCareTypeRequest) Reset() {
	*x = CreateCareTypeRequest{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCareTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCareTypeRequest) ProtoMessage() {}

func (x *CreateCareTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCareTypeRequest.ProtoReflect.Descriptor instead.
func (*CreateCareTypeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCareTypeRequest) GetCareType() *CareType {
	if x != nil {
		return x.CareType
	}
	return nil
}

// Response message for CreateCareType.
type CreateCareTypeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The created care type.
	CareType      *CareType `protobuf:"bytes,1,opt,name=care_type,json=careType,proto3" json:"care_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCareTypeResponse) Reset() {
	*x = CreateCareTypeResponse{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCareTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCareTypeResponse) ProtoMessage() {}

func (x *CreateCareTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCareTypeResponse.ProtoReflect.Descriptor instead.
func (*CreateCareTypeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateCareTypeResponse) GetCareType() *CareType {
	if x != nil {
		return x.CareType
	}
	return nil
}

// Request message for GetCareType.
type GetCareTypeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the care type to retrieve.
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCareTypeRequest) Reset() {
	*x = GetCareTypeRequest{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCareTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCareTypeRequest) ProtoMessage() {}

func (x *GetCareTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCareTypeRequest.ProtoReflect.Descriptor instead.
func (*GetCareTypeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetCareTypeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Response message for GetCareType.
type GetCareTypeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The retrieved care type.
	CareType      *CareType `protobuf:"bytes,1,opt,name=care_type,json=careType,proto3" json:"care_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCareTypeResponse) Reset() {
	*x = GetCareTypeResponse{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCareTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCareTypeResponse) ProtoMessage() {}

func (x *GetCareTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCareTypeResponse.ProtoReflect.Descriptor instead.
func (*GetCareTypeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetCareTypeResponse) GetCareType() *CareType {
	if x != nil {
		return x.CareType
	}
	return nil
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 使用 paginationRef 替代 --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 paginationRef 替代 --)
//
// (-- api-linter: core::0158::request-next_page_token-field=disabled
//
//	aip.dev/not-precedent: 使用 paginationRef 替代 --)
//
// Request message for ListCareTypes.
type ListCareTypesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The organization type
	OrganizationType OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.offering.v1.OrganizationType" json:"organization_type,omitempty"`
	// The organization ID.
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// Pagination options. If omitted, all records will be returned.
	Pagination    *PaginationRef `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCareTypesRequest) Reset() {
	*x = ListCareTypesRequest{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCareTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCareTypesRequest) ProtoMessage() {}

func (x *ListCareTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCareTypesRequest.ProtoReflect.Descriptor instead.
func (*ListCareTypesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListCareTypesRequest) GetOrganizationType() OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return OrganizationType_ORGANIZATION_TYPE_UNSPECIFIED
}

func (x *ListCareTypesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *ListCareTypesRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 paginationRef 替代 --)
//
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: 额外返回 total. --)
//
// Response message for ListCareTypes.
type ListCareTypesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// A list of care types.
	CareTypes []*CareType `protobuf:"bytes,1,rep,name=care_types,json=careTypes,proto3" json:"care_types,omitempty"`
	// Pagination options. If omitted, all records will be returned.
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// Total number of care types.
	Total         int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCareTypesResponse) Reset() {
	*x = ListCareTypesResponse{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCareTypesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCareTypesResponse) ProtoMessage() {}

func (x *ListCareTypesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCareTypesResponse.ProtoReflect.Descriptor instead.
func (*ListCareTypesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListCareTypesResponse) GetCareTypes() []*CareType {
	if x != nil {
		return x.CareTypes
	}
	return nil
}

func (x *ListCareTypesResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListCareTypesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// Request message for UpdateCareType.
type UpdateCareTypeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The care type to update. Its `id` must be set.
	CareType *CareType `protobuf:"bytes,1,opt,name=care_type,json=careType,proto3" json:"care_type,omitempty"`
	// Field mask to specify which fields to update.
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCareTypeRequest) Reset() {
	*x = UpdateCareTypeRequest{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCareTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCareTypeRequest) ProtoMessage() {}

func (x *UpdateCareTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCareTypeRequest.ProtoReflect.Descriptor instead.
func (*UpdateCareTypeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateCareTypeRequest) GetCareType() *CareType {
	if x != nil {
		return x.CareType
	}
	return nil
}

func (x *UpdateCareTypeRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// Response message for UpdateCareType.
type UpdateCareTypeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The updated care type.
	CareType      *CareType `protobuf:"bytes,1,opt,name=care_type,json=careType,proto3" json:"care_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCareTypeResponse) Reset() {
	*x = UpdateCareTypeResponse{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCareTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCareTypeResponse) ProtoMessage() {}

func (x *UpdateCareTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCareTypeResponse.ProtoReflect.Descriptor instead.
func (*UpdateCareTypeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateCareTypeResponse) GetCareType() *CareType {
	if x != nil {
		return x.CareType
	}
	return nil
}

// Request message for DeleteCareType.
type DeleteCareTypeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the care type to delete.
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCareTypeRequest) Reset() {
	*x = DeleteCareTypeRequest{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCareTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCareTypeRequest) ProtoMessage() {}

func (x *DeleteCareTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCareTypeRequest.ProtoReflect.Descriptor instead.
func (*DeleteCareTypeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteCareTypeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Response message for DeleteCareType.
type DeleteCareTypeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCareTypeResponse) Reset() {
	*x = DeleteCareTypeResponse{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCareTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCareTypeResponse) ProtoMessage() {}

func (x *DeleteCareTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCareTypeResponse.ProtoReflect.Descriptor instead.
func (*DeleteCareTypeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{9}
}

// Request message for CreateCareTypeAttribute.
type CreateCareTypeAttributeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The care type attribute to create.
	CareTypeAttribute *CareTypeAttribute `protobuf:"bytes,1,opt,name=care_type_attribute,json=careTypeAttribute,proto3" json:"care_type_attribute,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateCareTypeAttributeRequest) Reset() {
	*x = CreateCareTypeAttributeRequest{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCareTypeAttributeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCareTypeAttributeRequest) ProtoMessage() {}

func (x *CreateCareTypeAttributeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCareTypeAttributeRequest.ProtoReflect.Descriptor instead.
func (*CreateCareTypeAttributeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{10}
}

func (x *CreateCareTypeAttributeRequest) GetCareTypeAttribute() *CareTypeAttribute {
	if x != nil {
		return x.CareTypeAttribute
	}
	return nil
}

// Response message for CreateCareTypeAttribute.
type CreateCareTypeAttributeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The created care type attribute.
	CareTypeAttribute *CareTypeAttribute `protobuf:"bytes,1,opt,name=care_type_attribute,json=careTypeAttribute,proto3" json:"care_type_attribute,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateCareTypeAttributeResponse) Reset() {
	*x = CreateCareTypeAttributeResponse{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCareTypeAttributeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCareTypeAttributeResponse) ProtoMessage() {}

func (x *CreateCareTypeAttributeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCareTypeAttributeResponse.ProtoReflect.Descriptor instead.
func (*CreateCareTypeAttributeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{11}
}

func (x *CreateCareTypeAttributeResponse) GetCareTypeAttribute() *CareTypeAttribute {
	if x != nil {
		return x.CareTypeAttribute
	}
	return nil
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 使用 paginationRef 替代 --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 paginationRef 替代 --)
//
// (-- api-linter: core::0158::request-next_page_token-field=disabled
//
//	aip.dev/not-precedent: 使用 paginationRef 替代 --)
//
// Request message for ListCareTypeAttributes.
type ListCareTypeAttributesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the care type to list attributes for.
	CareTypeId int64 `protobuf:"varint,1,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// Pagination options. If omitted, all records will be returned.
	Pagination    *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCareTypeAttributesRequest) Reset() {
	*x = ListCareTypeAttributesRequest{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCareTypeAttributesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCareTypeAttributesRequest) ProtoMessage() {}

func (x *ListCareTypeAttributesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCareTypeAttributesRequest.ProtoReflect.Descriptor instead.
func (*ListCareTypeAttributesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{12}
}

func (x *ListCareTypeAttributesRequest) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *ListCareTypeAttributesRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 paginationRef 替代 --)
//
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: 额外返回 total. --)
//
// Response message for ListCareTypeAttributes.
type ListCareTypeAttributesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// A list of care type attributes.
	CareTypeAttributes []*CareTypeAttribute `protobuf:"bytes,1,rep,name=care_type_attributes,json=careTypeAttributes,proto3" json:"care_type_attributes,omitempty"`
	// Pagination options. If omitted, all records will be returned.
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// Total number of care type attributes.
	Total         int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCareTypeAttributesResponse) Reset() {
	*x = ListCareTypeAttributesResponse{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCareTypeAttributesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCareTypeAttributesResponse) ProtoMessage() {}

func (x *ListCareTypeAttributesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCareTypeAttributesResponse.ProtoReflect.Descriptor instead.
func (*ListCareTypeAttributesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{13}
}

func (x *ListCareTypeAttributesResponse) GetCareTypeAttributes() []*CareTypeAttribute {
	if x != nil {
		return x.CareTypeAttributes
	}
	return nil
}

func (x *ListCareTypeAttributesResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListCareTypeAttributesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// Request message for DeleteCareTypeAttribute.
type DeleteCareTypeAttributeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the care type attribute to delete.
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCareTypeAttributeRequest) Reset() {
	*x = DeleteCareTypeAttributeRequest{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCareTypeAttributeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCareTypeAttributeRequest) ProtoMessage() {}

func (x *DeleteCareTypeAttributeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCareTypeAttributeRequest.ProtoReflect.Descriptor instead.
func (*DeleteCareTypeAttributeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{14}
}

func (x *DeleteCareTypeAttributeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Response message for DeleteCareTypeAttribute.
type DeleteCareTypeAttributeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCareTypeAttributeResponse) Reset() {
	*x = DeleteCareTypeAttributeResponse{}
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCareTypeAttributeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCareTypeAttributeResponse) ProtoMessage() {}

func (x *DeleteCareTypeAttributeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCareTypeAttributeResponse.ProtoReflect.Descriptor instead.
func (*DeleteCareTypeAttributeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP(), []int{15}
}

var File_backend_proto_offering_v1_care_type_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_care_type_service_proto_rawDesc = "" +
	"\n" +
	"1backend/proto/offering/v1/care_type_service.proto\x12\x19backend.proto.offering.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a google/protobuf/field_mask.proto\x1a)backend/proto/offering/v1/care_type.proto\x1a&backend/proto/offering/v1/common.proto\"Y\n" +
	"\x15CreateCareTypeRequest\x12@\n" +
	"\tcare_type\x18\x01 \x01(\v2#.backend.proto.offering.v1.CareTypeR\bcareType\"Z\n" +
	"\x16CreateCareTypeResponse\x12@\n" +
	"\tcare_type\x18\x01 \x01(\v2#.backend.proto.offering.v1.CareTypeR\bcareType\"$\n" +
	"\x12GetCareTypeRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"W\n" +
	"\x13GetCareTypeResponse\x12@\n" +
	"\tcare_type\x18\x01 \x01(\v2#.backend.proto.offering.v1.CareTypeR\bcareType\"\xe3\x01\n" +
	"\x14ListCareTypesRequest\x12X\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2+.backend.proto.offering.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x02 \x01(\x03R\x0eorganizationId\x12H\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2(.backend.proto.offering.v1.PaginationRefR\n" +
	"pagination\"\xc0\x01\n" +
	"\x15ListCareTypesResponse\x12B\n" +
	"\n" +
	"care_types\x18\x01 \x03(\v2#.backend.proto.offering.v1.CareTypeR\tcareTypes\x12M\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2(.backend.proto.offering.v1.PaginationRefB\x03\xe0A\x01R\n" +
	"pagination\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\"\x9b\x01\n" +
	"\x15UpdateCareTypeRequest\x12@\n" +
	"\tcare_type\x18\x01 \x01(\v2#.backend.proto.offering.v1.CareTypeR\bcareType\x12@\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB\x03\xe0A\x01R\n" +
	"updateMask\"Z\n" +
	"\x16UpdateCareTypeResponse\x12@\n" +
	"\tcare_type\x18\x01 \x01(\v2#.backend.proto.offering.v1.CareTypeR\bcareType\"'\n" +
	"\x15DeleteCareTypeRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\x18\n" +
	"\x16DeleteCareTypeResponse\"~\n" +
	"\x1eCreateCareTypeAttributeRequest\x12\\\n" +
	"\x13care_type_attribute\x18\x01 \x01(\v2,.backend.proto.offering.v1.CareTypeAttributeR\x11careTypeAttribute\"\x7f\n" +
	"\x1fCreateCareTypeAttributeResponse\x12\\\n" +
	"\x13care_type_attribute\x18\x01 \x01(\v2,.backend.proto.offering.v1.CareTypeAttributeR\x11careTypeAttribute\"\x8b\x01\n" +
	"\x1dListCareTypeAttributesRequest\x12 \n" +
	"\fcare_type_id\x18\x01 \x01(\x03R\n" +
	"careTypeId\x12H\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2(.backend.proto.offering.v1.PaginationRefR\n" +
	"pagination\"\xe5\x01\n" +
	"\x1eListCareTypeAttributesResponse\x12^\n" +
	"\x14care_type_attributes\x18\x01 \x03(\v2,.backend.proto.offering.v1.CareTypeAttributeR\x12careTypeAttributes\x12M\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2(.backend.proto.offering.v1.PaginationRefB\x03\xe0A\x01R\n" +
	"pagination\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\"0\n" +
	"\x1eDeleteCareTypeAttributeRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"!\n" +
	"\x1fDeleteCareTypeAttributeResponse2\x8e\b\n" +
	"\x0fCareTypeService\x12u\n" +
	"\x0eCreateCareType\x120.backend.proto.offering.v1.CreateCareTypeRequest\x1a1.backend.proto.offering.v1.CreateCareTypeResponse\x12l\n" +
	"\vGetCareType\x12-.backend.proto.offering.v1.GetCareTypeRequest\x1a..backend.proto.offering.v1.GetCareTypeResponse\x12r\n" +
	"\rListCareTypes\x12/.backend.proto.offering.v1.ListCareTypesRequest\x1a0.backend.proto.offering.v1.ListCareTypesResponse\x12u\n" +
	"\x0eUpdateCareType\x120.backend.proto.offering.v1.UpdateCareTypeRequest\x1a1.backend.proto.offering.v1.UpdateCareTypeResponse\x12u\n" +
	"\x0eDeleteCareType\x120.backend.proto.offering.v1.DeleteCareTypeRequest\x1a1.backend.proto.offering.v1.DeleteCareTypeResponse\x12\x90\x01\n" +
	"\x17CreateCareTypeAttribute\x129.backend.proto.offering.v1.CreateCareTypeAttributeRequest\x1a:.backend.proto.offering.v1.CreateCareTypeAttributeResponse\x12\x8d\x01\n" +
	"\x16ListCareTypeAttributes\x128.backend.proto.offering.v1.ListCareTypeAttributesRequest\x1a9.backend.proto.offering.v1.ListCareTypeAttributesResponse\x12\x90\x01\n" +
	"\x17DeleteCareTypeAttribute\x129.backend.proto.offering.v1.DeleteCareTypeAttributeRequest\x1a:.backend.proto.offering.v1.DeleteCareTypeAttributeResponseBk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_care_type_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_care_type_service_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_care_type_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_care_type_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_care_type_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_care_type_service_proto_rawDesc), len(file_backend_proto_offering_v1_care_type_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_care_type_service_proto_rawDescData
}

var file_backend_proto_offering_v1_care_type_service_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_backend_proto_offering_v1_care_type_service_proto_goTypes = []any{
	(*CreateCareTypeRequest)(nil),           // 0: backend.proto.offering.v1.CreateCareTypeRequest
	(*CreateCareTypeResponse)(nil),          // 1: backend.proto.offering.v1.CreateCareTypeResponse
	(*GetCareTypeRequest)(nil),              // 2: backend.proto.offering.v1.GetCareTypeRequest
	(*GetCareTypeResponse)(nil),             // 3: backend.proto.offering.v1.GetCareTypeResponse
	(*ListCareTypesRequest)(nil),            // 4: backend.proto.offering.v1.ListCareTypesRequest
	(*ListCareTypesResponse)(nil),           // 5: backend.proto.offering.v1.ListCareTypesResponse
	(*UpdateCareTypeRequest)(nil),           // 6: backend.proto.offering.v1.UpdateCareTypeRequest
	(*UpdateCareTypeResponse)(nil),          // 7: backend.proto.offering.v1.UpdateCareTypeResponse
	(*DeleteCareTypeRequest)(nil),           // 8: backend.proto.offering.v1.DeleteCareTypeRequest
	(*DeleteCareTypeResponse)(nil),          // 9: backend.proto.offering.v1.DeleteCareTypeResponse
	(*CreateCareTypeAttributeRequest)(nil),  // 10: backend.proto.offering.v1.CreateCareTypeAttributeRequest
	(*CreateCareTypeAttributeResponse)(nil), // 11: backend.proto.offering.v1.CreateCareTypeAttributeResponse
	(*ListCareTypeAttributesRequest)(nil),   // 12: backend.proto.offering.v1.ListCareTypeAttributesRequest
	(*ListCareTypeAttributesResponse)(nil),  // 13: backend.proto.offering.v1.ListCareTypeAttributesResponse
	(*DeleteCareTypeAttributeRequest)(nil),  // 14: backend.proto.offering.v1.DeleteCareTypeAttributeRequest
	(*DeleteCareTypeAttributeResponse)(nil), // 15: backend.proto.offering.v1.DeleteCareTypeAttributeResponse
	(*CareType)(nil),                        // 16: backend.proto.offering.v1.CareType
	(OrganizationType)(0),                   // 17: backend.proto.offering.v1.OrganizationType
	(*PaginationRef)(nil),                   // 18: backend.proto.offering.v1.PaginationRef
	(*fieldmaskpb.FieldMask)(nil),           // 19: google.protobuf.FieldMask
	(*CareTypeAttribute)(nil),               // 20: backend.proto.offering.v1.CareTypeAttribute
}
var file_backend_proto_offering_v1_care_type_service_proto_depIdxs = []int32{
	16, // 0: backend.proto.offering.v1.CreateCareTypeRequest.care_type:type_name -> backend.proto.offering.v1.CareType
	16, // 1: backend.proto.offering.v1.CreateCareTypeResponse.care_type:type_name -> backend.proto.offering.v1.CareType
	16, // 2: backend.proto.offering.v1.GetCareTypeResponse.care_type:type_name -> backend.proto.offering.v1.CareType
	17, // 3: backend.proto.offering.v1.ListCareTypesRequest.organization_type:type_name -> backend.proto.offering.v1.OrganizationType
	18, // 4: backend.proto.offering.v1.ListCareTypesRequest.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	16, // 5: backend.proto.offering.v1.ListCareTypesResponse.care_types:type_name -> backend.proto.offering.v1.CareType
	18, // 6: backend.proto.offering.v1.ListCareTypesResponse.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	16, // 7: backend.proto.offering.v1.UpdateCareTypeRequest.care_type:type_name -> backend.proto.offering.v1.CareType
	19, // 8: backend.proto.offering.v1.UpdateCareTypeRequest.update_mask:type_name -> google.protobuf.FieldMask
	16, // 9: backend.proto.offering.v1.UpdateCareTypeResponse.care_type:type_name -> backend.proto.offering.v1.CareType
	20, // 10: backend.proto.offering.v1.CreateCareTypeAttributeRequest.care_type_attribute:type_name -> backend.proto.offering.v1.CareTypeAttribute
	20, // 11: backend.proto.offering.v1.CreateCareTypeAttributeResponse.care_type_attribute:type_name -> backend.proto.offering.v1.CareTypeAttribute
	18, // 12: backend.proto.offering.v1.ListCareTypeAttributesRequest.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	20, // 13: backend.proto.offering.v1.ListCareTypeAttributesResponse.care_type_attributes:type_name -> backend.proto.offering.v1.CareTypeAttribute
	18, // 14: backend.proto.offering.v1.ListCareTypeAttributesResponse.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	0,  // 15: backend.proto.offering.v1.CareTypeService.CreateCareType:input_type -> backend.proto.offering.v1.CreateCareTypeRequest
	2,  // 16: backend.proto.offering.v1.CareTypeService.GetCareType:input_type -> backend.proto.offering.v1.GetCareTypeRequest
	4,  // 17: backend.proto.offering.v1.CareTypeService.ListCareTypes:input_type -> backend.proto.offering.v1.ListCareTypesRequest
	6,  // 18: backend.proto.offering.v1.CareTypeService.UpdateCareType:input_type -> backend.proto.offering.v1.UpdateCareTypeRequest
	8,  // 19: backend.proto.offering.v1.CareTypeService.DeleteCareType:input_type -> backend.proto.offering.v1.DeleteCareTypeRequest
	10, // 20: backend.proto.offering.v1.CareTypeService.CreateCareTypeAttribute:input_type -> backend.proto.offering.v1.CreateCareTypeAttributeRequest
	12, // 21: backend.proto.offering.v1.CareTypeService.ListCareTypeAttributes:input_type -> backend.proto.offering.v1.ListCareTypeAttributesRequest
	14, // 22: backend.proto.offering.v1.CareTypeService.DeleteCareTypeAttribute:input_type -> backend.proto.offering.v1.DeleteCareTypeAttributeRequest
	1,  // 23: backend.proto.offering.v1.CareTypeService.CreateCareType:output_type -> backend.proto.offering.v1.CreateCareTypeResponse
	3,  // 24: backend.proto.offering.v1.CareTypeService.GetCareType:output_type -> backend.proto.offering.v1.GetCareTypeResponse
	5,  // 25: backend.proto.offering.v1.CareTypeService.ListCareTypes:output_type -> backend.proto.offering.v1.ListCareTypesResponse
	7,  // 26: backend.proto.offering.v1.CareTypeService.UpdateCareType:output_type -> backend.proto.offering.v1.UpdateCareTypeResponse
	9,  // 27: backend.proto.offering.v1.CareTypeService.DeleteCareType:output_type -> backend.proto.offering.v1.DeleteCareTypeResponse
	11, // 28: backend.proto.offering.v1.CareTypeService.CreateCareTypeAttribute:output_type -> backend.proto.offering.v1.CreateCareTypeAttributeResponse
	13, // 29: backend.proto.offering.v1.CareTypeService.ListCareTypeAttributes:output_type -> backend.proto.offering.v1.ListCareTypeAttributesResponse
	15, // 30: backend.proto.offering.v1.CareTypeService.DeleteCareTypeAttribute:output_type -> backend.proto.offering.v1.DeleteCareTypeAttributeResponse
	23, // [23:31] is the sub-list for method output_type
	15, // [15:23] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_care_type_service_proto_init() }
func file_backend_proto_offering_v1_care_type_service_proto_init() {
	if File_backend_proto_offering_v1_care_type_service_proto != nil {
		return
	}
	file_backend_proto_offering_v1_care_type_proto_init()
	file_backend_proto_offering_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_care_type_service_proto_rawDesc), len(file_backend_proto_offering_v1_care_type_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_offering_v1_care_type_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_care_type_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_v1_care_type_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_care_type_service_proto = out.File
	file_backend_proto_offering_v1_care_type_service_proto_goTypes = nil
	file_backend_proto_offering_v1_care_type_service_proto_depIdxs = nil
}
