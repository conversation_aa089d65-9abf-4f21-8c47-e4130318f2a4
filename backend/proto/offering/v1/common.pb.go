// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/offering/v1/common.proto

package offeringpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Organization level.
type OrganizationType int32

const (
	// Unspecified organization type.
	OrganizationType_ORGANIZATION_TYPE_UNSPECIFIED OrganizationType = 0
	// Enterprise level.
	OrganizationType_ORGANIZATION_TYPE_ENTERPRISE OrganizationType = 1
	// Company level.
	OrganizationType_ORGANIZATION_TYPE_COMPANY OrganizationType = 2
	// Business level.
	OrganizationType_ORGANIZATION_TYPE_BUSINESS OrganizationType = 3
)

// Enum value maps for OrganizationType.
var (
	OrganizationType_name = map[int32]string{
		0: "ORGANIZATION_TYPE_UNSPECIFIED",
		1: "ORGANIZATION_TYPE_ENTERPRISE",
		2: "ORGANIZATION_TYPE_COMPANY",
		3: "ORGANIZATION_TYPE_BUSINESS",
	}
	OrganizationType_value = map[string]int32{
		"ORGANIZATION_TYPE_UNSPECIFIED": 0,
		"ORGANIZATION_TYPE_ENTERPRISE":  1,
		"ORGANIZATION_TYPE_COMPANY":     2,
		"ORGANIZATION_TYPE_BUSINESS":    3,
	}
)

func (x OrganizationType) Enum() *OrganizationType {
	p := new(OrganizationType)
	*p = x
	return p
}

func (x OrganizationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrganizationType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_common_proto_enumTypes[0].Descriptor()
}

func (OrganizationType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_common_proto_enumTypes[0]
}

func (x OrganizationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrganizationType.Descriptor instead.
func (OrganizationType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{0}
}

// Defines the unique keys for all supported attributes in the system.
// This enum is the single source of truth for what attributes are available.
type AttributeKey int32

const (
	// Unspecified attribute key.
	AttributeKey_ATTRIBUTE_KEY_UNSPECIFIED AttributeKey = 0
	// Duration in minutes.
	AttributeKey_ATTRIBUTE_KEY_DURATION AttributeKey = 1
	// Max stay duration in minutes.
	AttributeKey_ATTRIBUTE_KEY_MAX_DURATION AttributeKey = 2
	// Indicates if lodging is required.
	AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED AttributeKey = 3
	// Indicates if an evaluation is required.
	AttributeKey_ATTRIBUTE_KEY_IS_EVALUATION_REQUIRED AttributeKey = 4
	// Indicates if an evaluation for observation is required.
	AttributeKey_ATTRIBUTE_KEY_IS_EVALUATION_FOR_OB_REQUIRED AttributeKey = 5
	// The ID of the required evaluation.
	AttributeKey_ATTRIBUTE_KEY_REQUIRED_EVALUATION_ID AttributeKey = 6
	// The number of sessions.
	AttributeKey_ATTRIBUTE_KEY_SESSION_COUNT AttributeKey = 7
	// The minimum duration of a session in minutes.
	AttributeKey_ATTRIBUTE_KEY_SESSION_DURATION_MIN AttributeKey = 8
	// The capacity of the class.
	AttributeKey_ATTRIBUTE_KEY_CLASS_CAPACITY AttributeKey = 9
	// Indicates if a prerequisite class is required.
	AttributeKey_ATTRIBUTE_KEY_IS_PREREQUISITE_REQUIRED AttributeKey = 10
	// A list of prerequisite class IDs.
	AttributeKey_ATTRIBUTE_KEY_PREREQUISITE_SERVICE_IDS AttributeKey = 11
	// Indicates if staff can be auto-assigned.
	AttributeKey_ATTRIBUTE_KEY_ALLOW_STAFF_AUTO_ASSIGN AttributeKey = 12
	// Indicates if the service is resettable.
	AttributeKey_ATTRIBUTE_KEY_IS_RESETTABLE AttributeKey = 13
	// The interval in days for resetting.
	AttributeKey_ATTRIBUTE_KEY_RESET_INTERVAL_DAYS AttributeKey = 14
	// Indicates if pet type binding is supported.
	AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_PET_TYPE_BINDING AttributeKey = 15
	// Indicates if coat type binding is supported.
	AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_COAT_TYPE_BINDING AttributeKey = 16
	// Indicates if pet size binding is supported.
	AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_PET_SIZE_BINDING AttributeKey = 17
	// Indicates if pet code binding is supported.
	AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_PET_CODE_BINDING AttributeKey = 18
	// Indicates if staff binding is supported.
	AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_STAFF_BINDING AttributeKey = 19
	// Indicates if lodging type binding is supported.
	AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_LODGING_TYPE_BINDING AttributeKey = 20
	// Indicates if auto-rollover is supported.
	AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_AUTO_ROLLOVER AttributeKey = 21
)

// Enum value maps for AttributeKey.
var (
	AttributeKey_name = map[int32]string{
		0:  "ATTRIBUTE_KEY_UNSPECIFIED",
		1:  "ATTRIBUTE_KEY_DURATION",
		2:  "ATTRIBUTE_KEY_MAX_DURATION",
		3:  "ATTRIBUTE_KEY_IS_LODGING_REQUIRED",
		4:  "ATTRIBUTE_KEY_IS_EVALUATION_REQUIRED",
		5:  "ATTRIBUTE_KEY_IS_EVALUATION_FOR_OB_REQUIRED",
		6:  "ATTRIBUTE_KEY_REQUIRED_EVALUATION_ID",
		7:  "ATTRIBUTE_KEY_SESSION_COUNT",
		8:  "ATTRIBUTE_KEY_SESSION_DURATION_MIN",
		9:  "ATTRIBUTE_KEY_CLASS_CAPACITY",
		10: "ATTRIBUTE_KEY_IS_PREREQUISITE_REQUIRED",
		11: "ATTRIBUTE_KEY_PREREQUISITE_SERVICE_IDS",
		12: "ATTRIBUTE_KEY_ALLOW_STAFF_AUTO_ASSIGN",
		13: "ATTRIBUTE_KEY_IS_RESETTABLE",
		14: "ATTRIBUTE_KEY_RESET_INTERVAL_DAYS",
		15: "ATTRIBUTE_KEY_IS_SUPPORT_PET_TYPE_BINDING",
		16: "ATTRIBUTE_KEY_IS_SUPPORT_COAT_TYPE_BINDING",
		17: "ATTRIBUTE_KEY_IS_SUPPORT_PET_SIZE_BINDING",
		18: "ATTRIBUTE_KEY_IS_SUPPORT_PET_CODE_BINDING",
		19: "ATTRIBUTE_KEY_IS_SUPPORT_STAFF_BINDING",
		20: "ATTRIBUTE_KEY_IS_SUPPORT_LODGING_TYPE_BINDING",
		21: "ATTRIBUTE_KEY_IS_SUPPORT_AUTO_ROLLOVER",
	}
	AttributeKey_value = map[string]int32{
		"ATTRIBUTE_KEY_UNSPECIFIED":                     0,
		"ATTRIBUTE_KEY_DURATION":                        1,
		"ATTRIBUTE_KEY_MAX_DURATION":                    2,
		"ATTRIBUTE_KEY_IS_LODGING_REQUIRED":             3,
		"ATTRIBUTE_KEY_IS_EVALUATION_REQUIRED":          4,
		"ATTRIBUTE_KEY_IS_EVALUATION_FOR_OB_REQUIRED":   5,
		"ATTRIBUTE_KEY_REQUIRED_EVALUATION_ID":          6,
		"ATTRIBUTE_KEY_SESSION_COUNT":                   7,
		"ATTRIBUTE_KEY_SESSION_DURATION_MIN":            8,
		"ATTRIBUTE_KEY_CLASS_CAPACITY":                  9,
		"ATTRIBUTE_KEY_IS_PREREQUISITE_REQUIRED":        10,
		"ATTRIBUTE_KEY_PREREQUISITE_SERVICE_IDS":        11,
		"ATTRIBUTE_KEY_ALLOW_STAFF_AUTO_ASSIGN":         12,
		"ATTRIBUTE_KEY_IS_RESETTABLE":                   13,
		"ATTRIBUTE_KEY_RESET_INTERVAL_DAYS":             14,
		"ATTRIBUTE_KEY_IS_SUPPORT_PET_TYPE_BINDING":     15,
		"ATTRIBUTE_KEY_IS_SUPPORT_COAT_TYPE_BINDING":    16,
		"ATTRIBUTE_KEY_IS_SUPPORT_PET_SIZE_BINDING":     17,
		"ATTRIBUTE_KEY_IS_SUPPORT_PET_CODE_BINDING":     18,
		"ATTRIBUTE_KEY_IS_SUPPORT_STAFF_BINDING":        19,
		"ATTRIBUTE_KEY_IS_SUPPORT_LODGING_TYPE_BINDING": 20,
		"ATTRIBUTE_KEY_IS_SUPPORT_AUTO_ROLLOVER":        21,
	}
)

func (x AttributeKey) Enum() *AttributeKey {
	p := new(AttributeKey)
	*p = x
	return p
}

func (x AttributeKey) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AttributeKey) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_common_proto_enumTypes[1].Descriptor()
}

func (AttributeKey) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_common_proto_enumTypes[1]
}

func (x AttributeKey) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AttributeKey.Descriptor instead.
func (AttributeKey) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{1}
}

// 排序方式
type SortType int32

const (
	// SORT_TYPE_UNSPECIFIED 默认排序类型
	SortType_SORT_TYPE_UNSPECIFIED SortType = 0
	// SORT_TYPE_START_TIME 按服务开始时间
	SortType_SORT_TYPE_START_TIME SortType = 1
)

// Enum value maps for SortType.
var (
	SortType_name = map[int32]string{
		0: "SORT_TYPE_UNSPECIFIED",
		1: "SORT_TYPE_START_TIME",
	}
	SortType_value = map[string]int32{
		"SORT_TYPE_UNSPECIFIED": 0,
		"SORT_TYPE_START_TIME":  1,
	}
)

func (x SortType) Enum() *SortType {
	p := new(SortType)
	*p = x
	return p
}

func (x SortType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SortType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_common_proto_enumTypes[2].Descriptor()
}

func (SortType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_common_proto_enumTypes[2]
}

func (x SortType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SortType.Descriptor instead.
func (SortType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{2}
}

// 护理类别，包含系统预设和自定义
type CareCategory int32

const (
	// 未知护理类型
	CareCategory_CARE_CATEGORY_UNSPECIFIED CareCategory = 0
	// GROOMING类型
	CareCategory_CARE_CATEGORY_GROOMING CareCategory = 1
	// BOARDING类型
	CareCategory_CARE_CATEGORY_BOARDING CareCategory = 2
	// DAYCARE类型
	CareCategory_CARE_CATEGORY_DAYCARE CareCategory = 3
	// EVALUATION类型
	CareCategory_CARE_CATEGORY_EVALUATION CareCategory = 4
	// DOG_WALKING类型
	CareCategory_CARE_CATEGORY_DOG_WALKING CareCategory = 5
	// GROUP_CLASS类型
	CareCategory_CARE_CATEGORY_GROUP_CLASS CareCategory = 6
	// 自定义类型
	CareCategory_CARE_CATEGORY_CUSTOM CareCategory = 99
)

// Enum value maps for CareCategory.
var (
	CareCategory_name = map[int32]string{
		0:  "CARE_CATEGORY_UNSPECIFIED",
		1:  "CARE_CATEGORY_GROOMING",
		2:  "CARE_CATEGORY_BOARDING",
		3:  "CARE_CATEGORY_DAYCARE",
		4:  "CARE_CATEGORY_EVALUATION",
		5:  "CARE_CATEGORY_DOG_WALKING",
		6:  "CARE_CATEGORY_GROUP_CLASS",
		99: "CARE_CATEGORY_CUSTOM",
	}
	CareCategory_value = map[string]int32{
		"CARE_CATEGORY_UNSPECIFIED": 0,
		"CARE_CATEGORY_GROOMING":    1,
		"CARE_CATEGORY_BOARDING":    2,
		"CARE_CATEGORY_DAYCARE":     3,
		"CARE_CATEGORY_EVALUATION":  4,
		"CARE_CATEGORY_DOG_WALKING": 5,
		"CARE_CATEGORY_GROUP_CLASS": 6,
		"CARE_CATEGORY_CUSTOM":      99,
	}
)

func (x CareCategory) Enum() *CareCategory {
	p := new(CareCategory)
	*p = x
	return p
}

func (x CareCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CareCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_common_proto_enumTypes[3].Descriptor()
}

func (CareCategory) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_common_proto_enumTypes[3]
}

func (x CareCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CareCategory.Descriptor instead.
func (CareCategory) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{3}
}

// 价格单位, 1-per session, 2-per night, 3-per hour, 4-per day
type PriceUnit int32

const (
	// 未指定
	PriceUnit_PRICE_UNIT_UNSPECIFIED PriceUnit = 0
	// 每次服务
	PriceUnit_PRICE_UNIT_PER_SESSION PriceUnit = 1
	// 每晚
	PriceUnit_PRICE_UNIT_PER_NIGHT PriceUnit = 2
	// 每小时
	PriceUnit_PRICE_UNIT_PER_HOUR PriceUnit = 3
	// 每天
	PriceUnit_PRICE_UNIT_PER_DAY PriceUnit = 4
)

// Enum value maps for PriceUnit.
var (
	PriceUnit_name = map[int32]string{
		0: "PRICE_UNIT_UNSPECIFIED",
		1: "PRICE_UNIT_PER_SESSION",
		2: "PRICE_UNIT_PER_NIGHT",
		3: "PRICE_UNIT_PER_HOUR",
		4: "PRICE_UNIT_PER_DAY",
	}
	PriceUnit_value = map[string]int32{
		"PRICE_UNIT_UNSPECIFIED": 0,
		"PRICE_UNIT_PER_SESSION": 1,
		"PRICE_UNIT_PER_NIGHT":   2,
		"PRICE_UNIT_PER_HOUR":    3,
		"PRICE_UNIT_PER_DAY":     4,
	}
)

func (x PriceUnit) Enum() *PriceUnit {
	p := new(PriceUnit)
	*p = x
	return p
}

func (x PriceUnit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PriceUnit) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_common_proto_enumTypes[4].Descriptor()
}

func (PriceUnit) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_common_proto_enumTypes[4]
}

func (x PriceUnit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PriceUnit.Descriptor instead.
func (PriceUnit) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{4}
}

// 服务来源
type ServiceSource int32

const (
	// 未指定
	ServiceSource_SERVICE_SOURCE_UNSPECIFIED ServiceSource = 0
	// MoeGo 平台
	ServiceSource_SERVICE_SOURCE_PLATFORM ServiceSource = 1
	// Enterprise Hub 企业
	ServiceSource_SERVICE_SOURCE_ENTERPRISE ServiceSource = 2
)

// Enum value maps for ServiceSource.
var (
	ServiceSource_name = map[int32]string{
		0: "SERVICE_SOURCE_UNSPECIFIED",
		1: "SERVICE_SOURCE_PLATFORM",
		2: "SERVICE_SOURCE_ENTERPRISE",
	}
	ServiceSource_value = map[string]int32{
		"SERVICE_SOURCE_UNSPECIFIED": 0,
		"SERVICE_SOURCE_PLATFORM":    1,
		"SERVICE_SOURCE_ENTERPRISE":  2,
	}
)

func (x ServiceSource) Enum() *ServiceSource {
	p := new(ServiceSource)
	*p = x
	return p
}

func (x ServiceSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceSource) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_common_proto_enumTypes[5].Descriptor()
}

func (ServiceSource) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_common_proto_enumTypes[5]
}

func (x ServiceSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceSource.Descriptor instead.
func (ServiceSource) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{5}
}

// ValueType defines the data type of an attribute's value, for frontend rendering.
type ValueType int32

const (
	// Unspecified value type.
	ValueType_VALUE_TYPE_UNSPECIFIED ValueType = 0
	// A single-line string.
	ValueType_VALUE_TYPE_STRING ValueType = 1
	// A multi-line text.
	ValueType_VALUE_TYPE_TEXT ValueType = 2
	// A numerical value (integer or decimal).
	ValueType_VALUE_TYPE_NUMBER ValueType = 3
	// A boolean value (true or false).
	ValueType_VALUE_TYPE_BOOLEAN ValueType = 4
	// A single selection from a list of options.
	ValueType_VALUE_TYPE_SINGLE_SELECT ValueType = 5
	// Multiple selections from a list of options.
	ValueType_VALUE_TYPE_MULTI_SELECT ValueType = 6
	// A date value.
	ValueType_VALUE_TYPE_DATE ValueType = 7
	// A date and time value.
	ValueType_VALUE_TYPE_DATETIME ValueType = 8
	// A JSON object.
	ValueType_VALUE_TYPE_JSON ValueType = 9
)

// Enum value maps for ValueType.
var (
	ValueType_name = map[int32]string{
		0: "VALUE_TYPE_UNSPECIFIED",
		1: "VALUE_TYPE_STRING",
		2: "VALUE_TYPE_TEXT",
		3: "VALUE_TYPE_NUMBER",
		4: "VALUE_TYPE_BOOLEAN",
		5: "VALUE_TYPE_SINGLE_SELECT",
		6: "VALUE_TYPE_MULTI_SELECT",
		7: "VALUE_TYPE_DATE",
		8: "VALUE_TYPE_DATETIME",
		9: "VALUE_TYPE_JSON",
	}
	ValueType_value = map[string]int32{
		"VALUE_TYPE_UNSPECIFIED":   0,
		"VALUE_TYPE_STRING":        1,
		"VALUE_TYPE_TEXT":          2,
		"VALUE_TYPE_NUMBER":        3,
		"VALUE_TYPE_BOOLEAN":       4,
		"VALUE_TYPE_SINGLE_SELECT": 5,
		"VALUE_TYPE_MULTI_SELECT":  6,
		"VALUE_TYPE_DATE":          7,
		"VALUE_TYPE_DATETIME":      8,
		"VALUE_TYPE_JSON":          9,
	}
)

func (x ValueType) Enum() *ValueType {
	p := new(ValueType)
	*p = x
	return p
}

func (x ValueType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ValueType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_common_proto_enumTypes[6].Descriptor()
}

func (ValueType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_common_proto_enumTypes[6]
}

func (x ValueType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ValueType.Descriptor instead.
func (ValueType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{6}
}

// 分页信息
type PaginationRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 偏移量，默认0
	Offset int32 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	// 每页数量，默认200
	Limit         int32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaginationRef) Reset() {
	*x = PaginationRef{}
	mi := &file_backend_proto_offering_v1_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginationRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationRef) ProtoMessage() {}

func (x *PaginationRef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationRef.ProtoReflect.Descriptor instead.
func (*PaginationRef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{0}
}

func (x *PaginationRef) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *PaginationRef) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

var File_backend_proto_offering_v1_common_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_common_proto_rawDesc = "" +
	"\n" +
	"&backend/proto/offering/v1/common.proto\x12\x19backend.proto.offering.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17validate/validate.proto\"=\n" +
	"\rPaginationRef\x12\x16\n" +
	"\x06offset\x18\x01 \x01(\x05R\x06offset\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit*\x96\x01\n" +
	"\x10OrganizationType\x12!\n" +
	"\x1dORGANIZATION_TYPE_UNSPECIFIED\x10\x00\x12 \n" +
	"\x1cORGANIZATION_TYPE_ENTERPRISE\x10\x01\x12\x1d\n" +
	"\x19ORGANIZATION_TYPE_COMPANY\x10\x02\x12\x1e\n" +
	"\x1aORGANIZATION_TYPE_BUSINESS\x10\x03*\x93\a\n" +
	"\fAttributeKey\x12\x1d\n" +
	"\x19ATTRIBUTE_KEY_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16ATTRIBUTE_KEY_DURATION\x10\x01\x12\x1e\n" +
	"\x1aATTRIBUTE_KEY_MAX_DURATION\x10\x02\x12%\n" +
	"!ATTRIBUTE_KEY_IS_LODGING_REQUIRED\x10\x03\x12(\n" +
	"$ATTRIBUTE_KEY_IS_EVALUATION_REQUIRED\x10\x04\x12/\n" +
	"+ATTRIBUTE_KEY_IS_EVALUATION_FOR_OB_REQUIRED\x10\x05\x12(\n" +
	"$ATTRIBUTE_KEY_REQUIRED_EVALUATION_ID\x10\x06\x12\x1f\n" +
	"\x1bATTRIBUTE_KEY_SESSION_COUNT\x10\a\x12&\n" +
	"\"ATTRIBUTE_KEY_SESSION_DURATION_MIN\x10\b\x12 \n" +
	"\x1cATTRIBUTE_KEY_CLASS_CAPACITY\x10\t\x12*\n" +
	"&ATTRIBUTE_KEY_IS_PREREQUISITE_REQUIRED\x10\n" +
	"\x12*\n" +
	"&ATTRIBUTE_KEY_PREREQUISITE_SERVICE_IDS\x10\v\x12)\n" +
	"%ATTRIBUTE_KEY_ALLOW_STAFF_AUTO_ASSIGN\x10\f\x12\x1f\n" +
	"\x1bATTRIBUTE_KEY_IS_RESETTABLE\x10\r\x12%\n" +
	"!ATTRIBUTE_KEY_RESET_INTERVAL_DAYS\x10\x0e\x12-\n" +
	")ATTRIBUTE_KEY_IS_SUPPORT_PET_TYPE_BINDING\x10\x0f\x12.\n" +
	"*ATTRIBUTE_KEY_IS_SUPPORT_COAT_TYPE_BINDING\x10\x10\x12-\n" +
	")ATTRIBUTE_KEY_IS_SUPPORT_PET_SIZE_BINDING\x10\x11\x12-\n" +
	")ATTRIBUTE_KEY_IS_SUPPORT_PET_CODE_BINDING\x10\x12\x12*\n" +
	"&ATTRIBUTE_KEY_IS_SUPPORT_STAFF_BINDING\x10\x13\x121\n" +
	"-ATTRIBUTE_KEY_IS_SUPPORT_LODGING_TYPE_BINDING\x10\x14\x12*\n" +
	"&ATTRIBUTE_KEY_IS_SUPPORT_AUTO_ROLLOVER\x10\x15*?\n" +
	"\bSortType\x12\x19\n" +
	"\x15SORT_TYPE_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14SORT_TYPE_START_TIME\x10\x01*\xf6\x01\n" +
	"\fCareCategory\x12\x1d\n" +
	"\x19CARE_CATEGORY_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16CARE_CATEGORY_GROOMING\x10\x01\x12\x1a\n" +
	"\x16CARE_CATEGORY_BOARDING\x10\x02\x12\x19\n" +
	"\x15CARE_CATEGORY_DAYCARE\x10\x03\x12\x1c\n" +
	"\x18CARE_CATEGORY_EVALUATION\x10\x04\x12\x1d\n" +
	"\x19CARE_CATEGORY_DOG_WALKING\x10\x05\x12\x1d\n" +
	"\x19CARE_CATEGORY_GROUP_CLASS\x10\x06\x12\x18\n" +
	"\x14CARE_CATEGORY_CUSTOM\x10c*\x8e\x01\n" +
	"\tPriceUnit\x12\x1a\n" +
	"\x16PRICE_UNIT_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16PRICE_UNIT_PER_SESSION\x10\x01\x12\x18\n" +
	"\x14PRICE_UNIT_PER_NIGHT\x10\x02\x12\x17\n" +
	"\x13PRICE_UNIT_PER_HOUR\x10\x03\x12\x16\n" +
	"\x12PRICE_UNIT_PER_DAY\x10\x04*k\n" +
	"\rServiceSource\x12\x1e\n" +
	"\x1aSERVICE_SOURCE_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17SERVICE_SOURCE_PLATFORM\x10\x01\x12\x1d\n" +
	"\x19SERVICE_SOURCE_ENTERPRISE\x10\x02*\x80\x02\n" +
	"\tValueType\x12\x1a\n" +
	"\x16VALUE_TYPE_UNSPECIFIED\x10\x00\x12\x15\n" +
	"\x11VALUE_TYPE_STRING\x10\x01\x12\x13\n" +
	"\x0fVALUE_TYPE_TEXT\x10\x02\x12\x15\n" +
	"\x11VALUE_TYPE_NUMBER\x10\x03\x12\x16\n" +
	"\x12VALUE_TYPE_BOOLEAN\x10\x04\x12\x1c\n" +
	"\x18VALUE_TYPE_SINGLE_SELECT\x10\x05\x12\x1b\n" +
	"\x17VALUE_TYPE_MULTI_SELECT\x10\x06\x12\x13\n" +
	"\x0fVALUE_TYPE_DATE\x10\a\x12\x17\n" +
	"\x13VALUE_TYPE_DATETIME\x10\b\x12\x13\n" +
	"\x0fVALUE_TYPE_JSON\x10\tBk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_common_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_common_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_common_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_common_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_common_proto_rawDesc), len(file_backend_proto_offering_v1_common_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_common_proto_rawDescData
}

var file_backend_proto_offering_v1_common_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_backend_proto_offering_v1_common_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_backend_proto_offering_v1_common_proto_goTypes = []any{
	(OrganizationType)(0), // 0: backend.proto.offering.v1.OrganizationType
	(AttributeKey)(0),     // 1: backend.proto.offering.v1.AttributeKey
	(SortType)(0),         // 2: backend.proto.offering.v1.SortType
	(CareCategory)(0),     // 3: backend.proto.offering.v1.CareCategory
	(PriceUnit)(0),        // 4: backend.proto.offering.v1.PriceUnit
	(ServiceSource)(0),    // 5: backend.proto.offering.v1.ServiceSource
	(ValueType)(0),        // 6: backend.proto.offering.v1.ValueType
	(*PaginationRef)(nil), // 7: backend.proto.offering.v1.PaginationRef
}
var file_backend_proto_offering_v1_common_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_common_proto_init() }
func file_backend_proto_offering_v1_common_proto_init() {
	if File_backend_proto_offering_v1_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_common_proto_rawDesc), len(file_backend_proto_offering_v1_common_proto_rawDesc)),
			NumEnums:      7,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_v1_common_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_common_proto_depIdxs,
		EnumInfos:         file_backend_proto_offering_v1_common_proto_enumTypes,
		MessageInfos:      file_backend_proto_offering_v1_common_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_common_proto = out.File
	file_backend_proto_offering_v1_common_proto_goTypes = nil
	file_backend_proto_offering_v1_common_proto_depIdxs = nil
}
