// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用name后缀 --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/customer/v2/activity.proto

package customerpb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// State 表示任务状态
type Task_State int32

const (
	// 未指定的状态
	Task_STATE_UNSPECIFIED Task_State = 0
	// 新任务
	Task_NEW Task_State = 1
	// 已完成
	Task_FINISH Task_State = 2
)

// Enum value maps for Task_State.
var (
	Task_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "NEW",
		2: "FINISH",
	}
	Task_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"NEW":               1,
		"FINISH":            2,
	}
)

func (x Task_State) Enum() *Task_State {
	p := new(Task_State)
	*p = x
	return p
}

func (x Task_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Task_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_proto_enumTypes[0].Descriptor()
}

func (Task_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_proto_enumTypes[0]
}

func (x Task_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Task_State.Descriptor instead.
func (Task_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{2, 0}
}

// Type 表示历史记录类型
type ActivityLog_Type int32

const (
	// 未指定的记录类型
	ActivityLog_TYPE_UNSPECIFIED ActivityLog_Type = 0
	// 短信记录
	ActivityLog_MESSAGE ActivityLog_Type = 1
	// 通话记录
	ActivityLog_CALL ActivityLog_Type = 2
	// 备注记录
	ActivityLog_NOTE ActivityLog_Type = 3
	// 任务记录
	ActivityLog_TASK ActivityLog_Type = 4
	// 类型转化的记录
	ActivityLog_CONVERT ActivityLog_Type = 5
	// 用户创建记录
	ActivityLog_CREATE ActivityLog_Type = 6
)

// Enum value maps for ActivityLog_Type.
var (
	ActivityLog_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "MESSAGE",
		2: "CALL",
		3: "NOTE",
		4: "TASK",
		5: "CONVERT",
		6: "CREATE",
	}
	ActivityLog_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"MESSAGE":          1,
		"CALL":             2,
		"NOTE":             3,
		"TASK":             4,
		"CONVERT":          5,
		"CREATE":           6,
	}
)

func (x ActivityLog_Type) Enum() *ActivityLog_Type {
	p := new(ActivityLog_Type)
	*p = x
	return p
}

func (x ActivityLog_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityLog_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_proto_enumTypes[1].Descriptor()
}

func (ActivityLog_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_proto_enumTypes[1]
}

func (x ActivityLog_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityLog_Type.Descriptor instead.
func (ActivityLog_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{3, 0}
}

// State 表示短信状态
type ActivityLog_Message_State int32

const (
	// 未指定的状态
	ActivityLog_Message_STATE_UNSPECIFIED ActivityLog_Message_State = 0
	// 发送成功
	ActivityLog_Message_SUCCEEDED ActivityLog_Message_State = 1
	// 发送失败
	ActivityLog_Message_FAILED ActivityLog_Message_State = 2
)

// Enum value maps for ActivityLog_Message_State.
var (
	ActivityLog_Message_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "SUCCEEDED",
		2: "FAILED",
	}
	ActivityLog_Message_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"SUCCEEDED":         1,
		"FAILED":            2,
	}
)

func (x ActivityLog_Message_State) Enum() *ActivityLog_Message_State {
	p := new(ActivityLog_Message_State)
	*p = x
	return p
}

func (x ActivityLog_Message_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityLog_Message_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_proto_enumTypes[2].Descriptor()
}

func (ActivityLog_Message_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_proto_enumTypes[2]
}

func (x ActivityLog_Message_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityLog_Message_State.Descriptor instead.
func (ActivityLog_Message_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{3, 0, 0}
}

// Direction 表示收发方向
type ActivityLog_Message_Direction int32

const (
	// 未指定的状态
	ActivityLog_Message_DIRECTION_UNSPECIFIED ActivityLog_Message_Direction = 0
	// 发送
	ActivityLog_Message_SEND ActivityLog_Message_Direction = 1
	// 接收
	ActivityLog_Message_RECEIVE ActivityLog_Message_Direction = 2
)

// Enum value maps for ActivityLog_Message_Direction.
var (
	ActivityLog_Message_Direction_name = map[int32]string{
		0: "DIRECTION_UNSPECIFIED",
		1: "SEND",
		2: "RECEIVE",
	}
	ActivityLog_Message_Direction_value = map[string]int32{
		"DIRECTION_UNSPECIFIED": 0,
		"SEND":                  1,
		"RECEIVE":               2,
	}
)

func (x ActivityLog_Message_Direction) Enum() *ActivityLog_Message_Direction {
	p := new(ActivityLog_Message_Direction)
	*p = x
	return p
}

func (x ActivityLog_Message_Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityLog_Message_Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_proto_enumTypes[3].Descriptor()
}

func (ActivityLog_Message_Direction) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_proto_enumTypes[3]
}

func (x ActivityLog_Message_Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityLog_Message_Direction.Descriptor instead.
func (ActivityLog_Message_Direction) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{3, 0, 1}
}

// State 表示通话状态
type ActivityLog_Call_State int32

const (
	// 未指定的状态
	ActivityLog_Call_STATE_UNSPECIFIED ActivityLog_Call_State = 0
	// 接通
	ActivityLog_Call_ANSWERED ActivityLog_Call_State = 1
	// 未接
	ActivityLog_Call_NO_ANSWER ActivityLog_Call_State = 2
)

// Enum value maps for ActivityLog_Call_State.
var (
	ActivityLog_Call_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ANSWERED",
		2: "NO_ANSWER",
	}
	ActivityLog_Call_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ANSWERED":          1,
		"NO_ANSWER":         2,
	}
)

func (x ActivityLog_Call_State) Enum() *ActivityLog_Call_State {
	p := new(ActivityLog_Call_State)
	*p = x
	return p
}

func (x ActivityLog_Call_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityLog_Call_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_proto_enumTypes[4].Descriptor()
}

func (ActivityLog_Call_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_proto_enumTypes[4]
}

func (x ActivityLog_Call_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityLog_Call_State.Descriptor instead.
func (ActivityLog_Call_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{3, 1, 0}
}

// Direction 表示收发方向
type ActivityLog_Call_Direction int32

const (
	// 未指定的状态
	ActivityLog_Call_DIRECTION_UNSPECIFIED ActivityLog_Call_Direction = 0
	// 用户联系商家
	ActivityLog_Call_INCOMING ActivityLog_Call_Direction = 1
	// 商家联系用户
	ActivityLog_Call_OUTGOING ActivityLog_Call_Direction = 2
)

// Enum value maps for ActivityLog_Call_Direction.
var (
	ActivityLog_Call_Direction_name = map[int32]string{
		0: "DIRECTION_UNSPECIFIED",
		1: "INCOMING",
		2: "OUTGOING",
	}
	ActivityLog_Call_Direction_value = map[string]int32{
		"DIRECTION_UNSPECIFIED": 0,
		"INCOMING":              1,
		"OUTGOING":              2,
	}
)

func (x ActivityLog_Call_Direction) Enum() *ActivityLog_Call_Direction {
	p := new(ActivityLog_Call_Direction)
	*p = x
	return p
}

func (x ActivityLog_Call_Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityLog_Call_Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_proto_enumTypes[5].Descriptor()
}

func (ActivityLog_Call_Direction) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_proto_enumTypes[5]
}

func (x ActivityLog_Call_Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityLog_Call_Direction.Descriptor instead.
func (ActivityLog_Call_Direction) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{3, 1, 1}
}

// Type 表示任务历史记录类型
type ActivityLog_Task_Type int32

const (
	// 未指定的记录类型
	ActivityLog_Task_TYPE_UNSPECIFIED ActivityLog_Task_Type = 0
	// 创建任务
	ActivityLog_Task_CREATE ActivityLog_Task_Type = 1
	// 更新任务
	ActivityLog_Task_UPDATE ActivityLog_Task_Type = 2
	// 完成任务
	ActivityLog_Task_FINISH ActivityLog_Task_Type = 3
	// 删除任务
	ActivityLog_Task_DELETE ActivityLog_Task_Type = 4
)

// Enum value maps for ActivityLog_Task_Type.
var (
	ActivityLog_Task_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "CREATE",
		2: "UPDATE",
		3: "FINISH",
		4: "DELETE",
	}
	ActivityLog_Task_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"CREATE":           1,
		"UPDATE":           2,
		"FINISH":           3,
		"DELETE":           4,
	}
)

func (x ActivityLog_Task_Type) Enum() *ActivityLog_Task_Type {
	p := new(ActivityLog_Task_Type)
	*p = x
	return p
}

func (x ActivityLog_Task_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityLog_Task_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_proto_enumTypes[6].Descriptor()
}

func (ActivityLog_Task_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_proto_enumTypes[6]
}

func (x ActivityLog_Task_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityLog_Task_Type.Descriptor instead.
func (ActivityLog_Task_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{3, 3, 0}
}

// 转换类型
type ActivityLog_Convert_ConvertType int32

const (
	// 未指定的记录类型
	ActivityLog_Convert_CONVERT_TYPE_UNSPECIFIED ActivityLog_Convert_ConvertType = 0
	// LEAD
	ActivityLog_Convert_LEAD ActivityLog_Convert_ConvertType = 1
	// CUSTOMER
	ActivityLog_Convert_CUSTOMER ActivityLog_Convert_ConvertType = 2
)

// Enum value maps for ActivityLog_Convert_ConvertType.
var (
	ActivityLog_Convert_ConvertType_name = map[int32]string{
		0: "CONVERT_TYPE_UNSPECIFIED",
		1: "LEAD",
		2: "CUSTOMER",
	}
	ActivityLog_Convert_ConvertType_value = map[string]int32{
		"CONVERT_TYPE_UNSPECIFIED": 0,
		"LEAD":                     1,
		"CUSTOMER":                 2,
	}
)

func (x ActivityLog_Convert_ConvertType) Enum() *ActivityLog_Convert_ConvertType {
	p := new(ActivityLog_Convert_ConvertType)
	*p = x
	return p
}

func (x ActivityLog_Convert_ConvertType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityLog_Convert_ConvertType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_proto_enumTypes[7].Descriptor()
}

func (ActivityLog_Convert_ConvertType) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_proto_enumTypes[7]
}

func (x ActivityLog_Convert_ConvertType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityLog_Convert_ConvertType.Descriptor instead.
func (ActivityLog_Convert_ConvertType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{3, 4, 0}
}

// Source 来源类型
type SystemSource_Source int32

const (
	// 未指定的记录类型
	SystemSource_SOURCE_UNSPECIFIED SystemSource_Source = 0
	// 员工操作
	SystemSource_STAFF SystemSource_Source = 1
	// 预约
	SystemSource_APPOINTMENT SystemSource_Source = 2
	// OB
	SystemSource_ONLINE_BOOKING SystemSource_Source = 3
	// product(retail)
	SystemSource_PRODUCT SystemSource_Source = 4
	// package
	SystemSource_PACKAGE SystemSource_Source = 5
	// fulfillment(new appointment)
	SystemSource_FULFILLMENT SystemSource_Source = 6
	// membership
	SystemSource_MEMBERSHIP SystemSource_Source = 7
)

// Enum value maps for SystemSource_Source.
var (
	SystemSource_Source_name = map[int32]string{
		0: "SOURCE_UNSPECIFIED",
		1: "STAFF",
		2: "APPOINTMENT",
		3: "ONLINE_BOOKING",
		4: "PRODUCT",
		5: "PACKAGE",
		6: "FULFILLMENT",
		7: "MEMBERSHIP",
	}
	SystemSource_Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED": 0,
		"STAFF":              1,
		"APPOINTMENT":        2,
		"ONLINE_BOOKING":     3,
		"PRODUCT":            4,
		"PACKAGE":            5,
		"FULFILLMENT":        6,
		"MEMBERSHIP":         7,
	}
)

func (x SystemSource_Source) Enum() *SystemSource_Source {
	p := new(SystemSource_Source)
	*p = x
	return p
}

func (x SystemSource_Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SystemSource_Source) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_proto_enumTypes[8].Descriptor()
}

func (SystemSource_Source) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_proto_enumTypes[8]
}

func (x SystemSource_Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SystemSource_Source.Descriptor instead.
func (SystemSource_Source) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{7, 0}
}

// 生命周期状态
type LifeCycle struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 排序
	Sort int32 `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	// 是否默认 0-否 1-是
	IsDefault     int32 `protobuf:"varint,4,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LifeCycle) Reset() {
	*x = LifeCycle{}
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LifeCycle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LifeCycle) ProtoMessage() {}

func (x *LifeCycle) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LifeCycle.ProtoReflect.Descriptor instead.
func (*LifeCycle) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{0}
}

func (x *LifeCycle) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LifeCycle) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LifeCycle) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *LifeCycle) GetIsDefault() int32 {
	if x != nil {
		return x.IsDefault
	}
	return 0
}

// 行动状态
type ActionState struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 排序
	Sort int32 `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	// 颜色
	Color         string `protobuf:"bytes,4,opt,name=color,proto3" json:"color,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActionState) Reset() {
	*x = ActionState{}
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActionState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionState) ProtoMessage() {}

func (x *ActionState) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionState.ProtoReflect.Descriptor instead.
func (*ActionState) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{1}
}

func (x *ActionState) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ActionState) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ActionState) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *ActionState) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

// Task 表示任务信息
type Task struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 任务ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 任务名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 分配员工
	AllocateStaffId *int64 `protobuf:"varint,3,opt,name=allocate_staff_id,json=allocateStaffId,proto3,oneof" json:"allocate_staff_id,omitempty"`
	// 预期完成的时间
	CompleteTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=complete_time,json=completeTime,proto3,oneof" json:"complete_time,omitempty"`
	// 任务状态
	State         Task_State `protobuf:"varint,5,opt,name=state,proto3,enum=backend.proto.customer.v2.Task_State" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Task) Reset() {
	*x = Task{}
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{2}
}

func (x *Task) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Task) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Task) GetAllocateStaffId() int64 {
	if x != nil && x.AllocateStaffId != nil {
		return *x.AllocateStaffId
	}
	return 0
}

func (x *Task) GetCompleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompleteTime
	}
	return nil
}

func (x *Task) GetState() Task_State {
	if x != nil {
		return x.State
	}
	return Task_STATE_UNSPECIFIED
}

// ActivityLog 表示活动记录
type ActivityLog struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 客户名称
	CustomerName string `protobuf:"bytes,10,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	// 客户电话
	CustomerPhoneNumber string `protobuf:"bytes,11,opt,name=customer_phone_number,json=customerPhoneNumber,proto3" json:"customer_phone_number,omitempty"`
	// 记录类型
	Type ActivityLog_Type `protobuf:"varint,3,opt,name=type,proto3,enum=backend.proto.customer.v2.ActivityLog_Type" json:"type,omitempty"`
	// 记录数据
	Action *ActivityLog_Action `protobuf:"bytes,4,opt,name=action,proto3" json:"action,omitempty"`
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 记录来源
	Source        *SystemSource `protobuf:"bytes,6,opt,name=source,proto3" json:"source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityLog) Reset() {
	*x = ActivityLog{}
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityLog) ProtoMessage() {}

func (x *ActivityLog) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityLog.ProtoReflect.Descriptor instead.
func (*ActivityLog) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{3}
}

func (x *ActivityLog) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ActivityLog) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ActivityLog) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *ActivityLog) GetCustomerPhoneNumber() string {
	if x != nil {
		return x.CustomerPhoneNumber
	}
	return ""
}

func (x *ActivityLog) GetType() ActivityLog_Type {
	if x != nil {
		return x.Type
	}
	return ActivityLog_TYPE_UNSPECIFIED
}

func (x *ActivityLog) GetAction() *ActivityLog_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *ActivityLog) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ActivityLog) GetSource() *SystemSource {
	if x != nil {
		return x.Source
	}
	return nil
}

// Note 表示摘要
type Note struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 备注内容
	Text string `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	// 创建来源
	CreateSource *SystemSource `protobuf:"bytes,3,opt,name=create_source,json=createSource,proto3" json:"create_source,omitempty"`
	// 更新来源
	UpdateSource *SystemSource `protobuf:"bytes,4,opt,name=update_source,json=updateSource,proto3" json:"update_source,omitempty"`
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// CustomerID
	CustomerId    int64 `protobuf:"varint,7,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Note) Reset() {
	*x = Note{}
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Note) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Note) ProtoMessage() {}

func (x *Note) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Note.ProtoReflect.Descriptor instead.
func (*Note) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{4}
}

func (x *Note) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Note) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *Note) GetCreateSource() *SystemSource {
	if x != nil {
		return x.CreateSource
	}
	return nil
}

func (x *Note) GetUpdateSource() *SystemSource {
	if x != nil {
		return x.UpdateSource
	}
	return nil
}

func (x *Note) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Note) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Note) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// Tag 标签
type Tag struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 排序
	Sort          int32 `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Tag) Reset() {
	*x = Tag{}
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Tag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tag) ProtoMessage() {}

func (x *Tag) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tag.ProtoReflect.Descriptor instead.
func (*Tag) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{5}
}

func (x *Tag) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Tag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Tag) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// 用户自定义来源
type Source struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 排序
	Sort          int32 `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Source) Reset() {
	*x = Source{}
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Source) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source) ProtoMessage() {}

func (x *Source) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source.ProtoReflect.Descriptor instead.
func (*Source) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{6}
}

func (x *Source) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Source) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Source) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// SystemSource 系统维护的来源字段
type SystemSource struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录来源
	Source SystemSource_Source `protobuf:"varint,1,opt,name=source,proto3,enum=backend.proto.customer.v2.SystemSource_Source" json:"source,omitempty"`
	// 记录来源ID
	SourceId int64 `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// 记录来源名称
	SourceName    string `protobuf:"bytes,3,opt,name=source_name,json=sourceName,proto3" json:"source_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SystemSource) Reset() {
	*x = SystemSource{}
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SystemSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemSource) ProtoMessage() {}

func (x *SystemSource) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemSource.ProtoReflect.Descriptor instead.
func (*SystemSource) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{7}
}

func (x *SystemSource) GetSource() SystemSource_Source {
	if x != nil {
		return x.Source
	}
	return SystemSource_SOURCE_UNSPECIFIED
}

func (x *SystemSource) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *SystemSource) GetSourceName() string {
	if x != nil {
		return x.SourceName
	}
	return ""
}

// SMS 表示短信历史记录
type ActivityLog_Message struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 短信ID
	MessageId int64 `protobuf:"varint,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	// 短信内容
	Text string `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	// 发送状态
	State ActivityLog_Message_State `protobuf:"varint,3,opt,name=state,proto3,enum=backend.proto.customer.v2.ActivityLog_Message_State" json:"state,omitempty"`
	// 失败原因
	FailReason string `protobuf:"bytes,4,opt,name=fail_reason,json=failReason,proto3" json:"fail_reason,omitempty"`
	// 收发方向
	Direction     ActivityLog_Message_Direction `protobuf:"varint,5,opt,name=direction,proto3,enum=backend.proto.customer.v2.ActivityLog_Message_Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityLog_Message) Reset() {
	*x = ActivityLog_Message{}
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityLog_Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityLog_Message) ProtoMessage() {}

func (x *ActivityLog_Message) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityLog_Message.ProtoReflect.Descriptor instead.
func (*ActivityLog_Message) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ActivityLog_Message) GetMessageId() int64 {
	if x != nil {
		return x.MessageId
	}
	return 0
}

func (x *ActivityLog_Message) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ActivityLog_Message) GetState() ActivityLog_Message_State {
	if x != nil {
		return x.State
	}
	return ActivityLog_Message_STATE_UNSPECIFIED
}

func (x *ActivityLog_Message) GetFailReason() string {
	if x != nil {
		return x.FailReason
	}
	return ""
}

func (x *ActivityLog_Message) GetDirection() ActivityLog_Message_Direction {
	if x != nil {
		return x.Direction
	}
	return ActivityLog_Message_DIRECTION_UNSPECIFIED
}

// Call 表示通话历史记录
type ActivityLog_Call struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 通话ID
	CallId int64 `protobuf:"varint,1,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	// 通话记录
	Text string `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	// 通话状态
	State ActivityLog_Call_State `protobuf:"varint,3,opt,name=state,proto3,enum=backend.proto.customer.v2.ActivityLog_Call_State" json:"state,omitempty"`
	// 失败原因
	FailReason string `protobuf:"bytes,4,opt,name=fail_reason,json=failReason,proto3" json:"fail_reason,omitempty"`
	// 收发方向
	Direction     ActivityLog_Call_Direction `protobuf:"varint,5,opt,name=direction,proto3,enum=backend.proto.customer.v2.ActivityLog_Call_Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityLog_Call) Reset() {
	*x = ActivityLog_Call{}
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityLog_Call) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityLog_Call) ProtoMessage() {}

func (x *ActivityLog_Call) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityLog_Call.ProtoReflect.Descriptor instead.
func (*ActivityLog_Call) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{3, 1}
}

func (x *ActivityLog_Call) GetCallId() int64 {
	if x != nil {
		return x.CallId
	}
	return 0
}

func (x *ActivityLog_Call) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ActivityLog_Call) GetState() ActivityLog_Call_State {
	if x != nil {
		return x.State
	}
	return ActivityLog_Call_STATE_UNSPECIFIED
}

func (x *ActivityLog_Call) GetFailReason() string {
	if x != nil {
		return x.FailReason
	}
	return ""
}

func (x *ActivityLog_Call) GetDirection() ActivityLog_Call_Direction {
	if x != nil {
		return x.Direction
	}
	return ActivityLog_Call_DIRECTION_UNSPECIFIED
}

// Note 表示备注历史记录
type ActivityLog_Note struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 备注内容
	Text          string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityLog_Note) Reset() {
	*x = ActivityLog_Note{}
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityLog_Note) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityLog_Note) ProtoMessage() {}

func (x *ActivityLog_Note) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityLog_Note.ProtoReflect.Descriptor instead.
func (*ActivityLog_Note) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{3, 2}
}

func (x *ActivityLog_Note) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

// Task 表示任务历史记录
type ActivityLog_Task struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录类型
	Type ActivityLog_Task_Type `protobuf:"varint,1,opt,name=type,proto3,enum=backend.proto.customer.v2.ActivityLog_Task_Type" json:"type,omitempty"`
	// 任务信息
	Task          *Task `protobuf:"bytes,2,opt,name=task,proto3" json:"task,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityLog_Task) Reset() {
	*x = ActivityLog_Task{}
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityLog_Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityLog_Task) ProtoMessage() {}

func (x *ActivityLog_Task) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityLog_Task.ProtoReflect.Descriptor instead.
func (*ActivityLog_Task) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{3, 3}
}

func (x *ActivityLog_Task) GetType() ActivityLog_Task_Type {
	if x != nil {
		return x.Type
	}
	return ActivityLog_Task_TYPE_UNSPECIFIED
}

func (x *ActivityLog_Task) GetTask() *Task {
	if x != nil {
		return x.Task
	}
	return nil
}

// Convert 表示类型转化记录
type ActivityLog_Convert struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 转化前类型
	OriginType ActivityLog_Convert_ConvertType `protobuf:"varint,1,opt,name=origin_type,json=originType,proto3,enum=backend.proto.customer.v2.ActivityLog_Convert_ConvertType" json:"origin_type,omitempty"`
	// 转化前的ID
	SourceId ActivityLog_Convert_ConvertType `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3,enum=backend.proto.customer.v2.ActivityLog_Convert_ConvertType" json:"source_id,omitempty"`
	// 转化后的类型
	TargetType ActivityLog_Convert_ConvertType `protobuf:"varint,3,opt,name=target_type,json=targetType,proto3,enum=backend.proto.customer.v2.ActivityLog_Convert_ConvertType" json:"target_type,omitempty"`
	// 转化后的ID
	TargetId      ActivityLog_Convert_ConvertType `protobuf:"varint,4,opt,name=target_id,json=targetId,proto3,enum=backend.proto.customer.v2.ActivityLog_Convert_ConvertType" json:"target_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityLog_Convert) Reset() {
	*x = ActivityLog_Convert{}
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityLog_Convert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityLog_Convert) ProtoMessage() {}

func (x *ActivityLog_Convert) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityLog_Convert.ProtoReflect.Descriptor instead.
func (*ActivityLog_Convert) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{3, 4}
}

func (x *ActivityLog_Convert) GetOriginType() ActivityLog_Convert_ConvertType {
	if x != nil {
		return x.OriginType
	}
	return ActivityLog_Convert_CONVERT_TYPE_UNSPECIFIED
}

func (x *ActivityLog_Convert) GetSourceId() ActivityLog_Convert_ConvertType {
	if x != nil {
		return x.SourceId
	}
	return ActivityLog_Convert_CONVERT_TYPE_UNSPECIFIED
}

func (x *ActivityLog_Convert) GetTargetType() ActivityLog_Convert_ConvertType {
	if x != nil {
		return x.TargetType
	}
	return ActivityLog_Convert_CONVERT_TYPE_UNSPECIFIED
}

func (x *ActivityLog_Convert) GetTargetId() ActivityLog_Convert_ConvertType {
	if x != nil {
		return x.TargetId
	}
	return ActivityLog_Convert_CONVERT_TYPE_UNSPECIFIED
}

// Create 表示用户创建记录
type ActivityLog_Create struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityLog_Create) Reset() {
	*x = ActivityLog_Create{}
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityLog_Create) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityLog_Create) ProtoMessage() {}

func (x *ActivityLog_Create) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityLog_Create.ProtoReflect.Descriptor instead.
func (*ActivityLog_Create) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{3, 5}
}

// actions
type ActivityLog_Action struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Action:
	//
	//	*ActivityLog_Action_Message
	//	*ActivityLog_Action_Call
	//	*ActivityLog_Action_Note
	//	*ActivityLog_Action_Task
	//	*ActivityLog_Action_Convert
	//	*ActivityLog_Action_Create
	Action        isActivityLog_Action_Action `protobuf_oneof:"action"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityLog_Action) Reset() {
	*x = ActivityLog_Action{}
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityLog_Action) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityLog_Action) ProtoMessage() {}

func (x *ActivityLog_Action) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityLog_Action.ProtoReflect.Descriptor instead.
func (*ActivityLog_Action) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_proto_rawDescGZIP(), []int{3, 6}
}

func (x *ActivityLog_Action) GetAction() isActivityLog_Action_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *ActivityLog_Action) GetMessage() *ActivityLog_Message {
	if x != nil {
		if x, ok := x.Action.(*ActivityLog_Action_Message); ok {
			return x.Message
		}
	}
	return nil
}

func (x *ActivityLog_Action) GetCall() *ActivityLog_Call {
	if x != nil {
		if x, ok := x.Action.(*ActivityLog_Action_Call); ok {
			return x.Call
		}
	}
	return nil
}

func (x *ActivityLog_Action) GetNote() *ActivityLog_Note {
	if x != nil {
		if x, ok := x.Action.(*ActivityLog_Action_Note); ok {
			return x.Note
		}
	}
	return nil
}

func (x *ActivityLog_Action) GetTask() *ActivityLog_Task {
	if x != nil {
		if x, ok := x.Action.(*ActivityLog_Action_Task); ok {
			return x.Task
		}
	}
	return nil
}

func (x *ActivityLog_Action) GetConvert() *ActivityLog_Convert {
	if x != nil {
		if x, ok := x.Action.(*ActivityLog_Action_Convert); ok {
			return x.Convert
		}
	}
	return nil
}

func (x *ActivityLog_Action) GetCreate() *ActivityLog_Create {
	if x != nil {
		if x, ok := x.Action.(*ActivityLog_Action_Create); ok {
			return x.Create
		}
	}
	return nil
}

type isActivityLog_Action_Action interface {
	isActivityLog_Action_Action()
}

type ActivityLog_Action_Message struct {
	// 短信消息
	Message *ActivityLog_Message `protobuf:"bytes,1,opt,name=message,proto3,oneof"`
}

type ActivityLog_Action_Call struct {
	// 通话记录
	Call *ActivityLog_Call `protobuf:"bytes,2,opt,name=call,proto3,oneof"`
}

type ActivityLog_Action_Note struct {
	// 备注记录
	Note *ActivityLog_Note `protobuf:"bytes,3,opt,name=note,proto3,oneof"`
}

type ActivityLog_Action_Task struct {
	// 任务记录
	Task *ActivityLog_Task `protobuf:"bytes,4,opt,name=task,proto3,oneof"`
}

type ActivityLog_Action_Convert struct {
	// 转化记录
	Convert *ActivityLog_Convert `protobuf:"bytes,5,opt,name=convert,proto3,oneof"`
}

type ActivityLog_Action_Create struct {
	// 创建记录
	Create *ActivityLog_Create `protobuf:"bytes,6,opt,name=create,proto3,oneof"`
}

func (*ActivityLog_Action_Message) isActivityLog_Action_Action() {}

func (*ActivityLog_Action_Call) isActivityLog_Action_Action() {}

func (*ActivityLog_Action_Note) isActivityLog_Action_Action() {}

func (*ActivityLog_Action_Task) isActivityLog_Action_Action() {}

func (*ActivityLog_Action_Convert) isActivityLog_Action_Action() {}

func (*ActivityLog_Action_Create) isActivityLog_Action_Action() {}

var File_backend_proto_customer_v2_activity_proto protoreflect.FileDescriptor

const file_backend_proto_customer_v2_activity_proto_rawDesc = "" +
	"\n" +
	"(backend/proto/customer/v2/activity.proto\x12\x19backend.proto.customer.v2\x1a&backend/proto/customer/v2/common.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"b\n" +
	"\tLifeCycle\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x03 \x01(\x05R\x04sort\x12\x1d\n" +
	"\n" +
	"is_default\x18\x04 \x01(\x05R\tisDefault\"[\n" +
	"\vActionState\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x03 \x01(\x05R\x04sort\x12\x14\n" +
	"\x05color\x18\x04 \x01(\tR\x05color\"\xc0\x02\n" +
	"\x04Task\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12/\n" +
	"\x11allocate_staff_id\x18\x03 \x01(\x03H\x00R\x0fallocateStaffId\x88\x01\x01\x12D\n" +
	"\rcomplete_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampH\x01R\fcompleteTime\x88\x01\x01\x12@\n" +
	"\x05state\x18\x05 \x01(\x0e2%.backend.proto.customer.v2.Task.StateB\x03\xe0A\x03R\x05state\"3\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03NEW\x10\x01\x12\n" +
	"\n" +
	"\x06FINISH\x10\x02B\x14\n" +
	"\x12_allocate_staff_idB\x10\n" +
	"\x0e_complete_time\"\xf1\x12\n" +
	"\vActivityLog\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vcustomer_id\x18\x02 \x01(\x03R\n" +
	"customerId\x12#\n" +
	"\rcustomer_name\x18\n" +
	" \x01(\tR\fcustomerName\x122\n" +
	"\x15customer_phone_number\x18\v \x01(\tR\x13customerPhoneNumber\x12?\n" +
	"\x04type\x18\x03 \x01(\x0e2+.backend.proto.customer.v2.ActivityLog.TypeR\x04type\x12E\n" +
	"\x06action\x18\x04 \x01(\v2-.backend.proto.customer.v2.ActivityLog.ActionR\x06action\x12;\n" +
	"\vcreate_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12?\n" +
	"\x06source\x18\x06 \x01(\v2'.backend.proto.customer.v2.SystemSourceR\x06source\x1a\x80\x03\n" +
	"\aMessage\x12\x1d\n" +
	"\n" +
	"message_id\x18\x01 \x01(\x03R\tmessageId\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x12O\n" +
	"\x05state\x18\x03 \x01(\x0e24.backend.proto.customer.v2.ActivityLog.Message.StateB\x03\xe0A\x03R\x05state\x12\x1f\n" +
	"\vfail_reason\x18\x04 \x01(\tR\n" +
	"failReason\x12V\n" +
	"\tdirection\x18\x05 \x01(\x0e28.backend.proto.customer.v2.ActivityLog.Message.DirectionR\tdirection\"9\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\r\n" +
	"\tSUCCEEDED\x10\x01\x12\n" +
	"\n" +
	"\x06FAILED\x10\x02\"=\n" +
	"\tDirection\x12\x19\n" +
	"\x15DIRECTION_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04SEND\x10\x01\x12\v\n" +
	"\aRECEIVE\x10\x02\x1a\xf8\x02\n" +
	"\x04Call\x12\x17\n" +
	"\acall_id\x18\x01 \x01(\x03R\x06callId\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x12L\n" +
	"\x05state\x18\x03 \x01(\x0e21.backend.proto.customer.v2.ActivityLog.Call.StateB\x03\xe0A\x03R\x05state\x12\x1f\n" +
	"\vfail_reason\x18\x04 \x01(\tR\n" +
	"failReason\x12S\n" +
	"\tdirection\x18\x05 \x01(\x0e25.backend.proto.customer.v2.ActivityLog.Call.DirectionR\tdirection\";\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bANSWERED\x10\x01\x12\r\n" +
	"\tNO_ANSWER\x10\x02\"B\n" +
	"\tDirection\x12\x19\n" +
	"\x15DIRECTION_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bINCOMING\x10\x01\x12\f\n" +
	"\bOUTGOING\x10\x02\x1a\x1a\n" +
	"\x04Note\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x1a\xcf\x01\n" +
	"\x04Task\x12D\n" +
	"\x04type\x18\x01 \x01(\x0e20.backend.proto.customer.v2.ActivityLog.Task.TypeR\x04type\x123\n" +
	"\x04task\x18\x02 \x01(\v2\x1f.backend.proto.customer.v2.TaskR\x04task\"L\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06CREATE\x10\x01\x12\n" +
	"\n" +
	"\x06UPDATE\x10\x02\x12\n" +
	"\n" +
	"\x06FINISH\x10\x03\x12\n" +
	"\n" +
	"\x06DELETE\x10\x04\x1a\xba\x03\n" +
	"\aConvert\x12[\n" +
	"\vorigin_type\x18\x01 \x01(\x0e2:.backend.proto.customer.v2.ActivityLog.Convert.ConvertTypeR\n" +
	"originType\x12W\n" +
	"\tsource_id\x18\x02 \x01(\x0e2:.backend.proto.customer.v2.ActivityLog.Convert.ConvertTypeR\bsourceId\x12[\n" +
	"\vtarget_type\x18\x03 \x01(\x0e2:.backend.proto.customer.v2.ActivityLog.Convert.ConvertTypeR\n" +
	"targetType\x12W\n" +
	"\ttarget_id\x18\x04 \x01(\x0e2:.backend.proto.customer.v2.ActivityLog.Convert.ConvertTypeR\btargetId\"C\n" +
	"\vConvertType\x12\x1c\n" +
	"\x18CONVERT_TYPE_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04LEAD\x10\x01\x12\f\n" +
	"\bCUSTOMER\x10\x02\x1a\b\n" +
	"\x06Create\x1a\xbc\x03\n" +
	"\x06Action\x12J\n" +
	"\amessage\x18\x01 \x01(\v2..backend.proto.customer.v2.ActivityLog.MessageH\x00R\amessage\x12A\n" +
	"\x04call\x18\x02 \x01(\v2+.backend.proto.customer.v2.ActivityLog.CallH\x00R\x04call\x12A\n" +
	"\x04note\x18\x03 \x01(\v2+.backend.proto.customer.v2.ActivityLog.NoteH\x00R\x04note\x12A\n" +
	"\x04task\x18\x04 \x01(\v2+.backend.proto.customer.v2.ActivityLog.TaskH\x00R\x04task\x12J\n" +
	"\aconvert\x18\x05 \x01(\v2..backend.proto.customer.v2.ActivityLog.ConvertH\x00R\aconvert\x12G\n" +
	"\x06create\x18\x06 \x01(\v2-.backend.proto.customer.v2.ActivityLog.CreateH\x00R\x06createB\b\n" +
	"\x06action\"`\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aMESSAGE\x10\x01\x12\b\n" +
	"\x04CALL\x10\x02\x12\b\n" +
	"\x04NOTE\x10\x03\x12\b\n" +
	"\x04TASK\x10\x04\x12\v\n" +
	"\aCONVERT\x10\x05\x12\n" +
	"\n" +
	"\x06CREATE\x10\x06\"\xe1\x02\n" +
	"\x04Note\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x12L\n" +
	"\rcreate_source\x18\x03 \x01(\v2'.backend.proto.customer.v2.SystemSourceR\fcreateSource\x12L\n" +
	"\rupdate_source\x18\x04 \x01(\v2'.backend.proto.customer.v2.SystemSourceR\fupdateSource\x12;\n" +
	"\vcreate_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12\x1f\n" +
	"\vcustomer_id\x18\a \x01(\x03R\n" +
	"customerId\"=\n" +
	"\x03Tag\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x03 \x01(\x05R\x04sort\"@\n" +
	"\x06Source\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x03 \x01(\x05R\x04sort\"\xa2\x02\n" +
	"\fSystemSource\x12F\n" +
	"\x06source\x18\x01 \x01(\x0e2..backend.proto.customer.v2.SystemSource.SourceR\x06source\x12\x1b\n" +
	"\tsource_id\x18\x02 \x01(\x03R\bsourceId\x12\x1f\n" +
	"\vsource_name\x18\x03 \x01(\tR\n" +
	"sourceName\"\x8b\x01\n" +
	"\x06Source\x12\x16\n" +
	"\x12SOURCE_UNSPECIFIED\x10\x00\x12\t\n" +
	"\x05STAFF\x10\x01\x12\x0f\n" +
	"\vAPPOINTMENT\x10\x02\x12\x12\n" +
	"\x0eONLINE_BOOKING\x10\x03\x12\v\n" +
	"\aPRODUCT\x10\x04\x12\v\n" +
	"\aPACKAGE\x10\x05\x12\x0f\n" +
	"\vFULFILLMENT\x10\x06\x12\x0e\n" +
	"\n" +
	"MEMBERSHIP\x10\aBk\n" +
	"#com.moego.backend.proto.customer.v2P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpbb\x06proto3"

var (
	file_backend_proto_customer_v2_activity_proto_rawDescOnce sync.Once
	file_backend_proto_customer_v2_activity_proto_rawDescData []byte
)

func file_backend_proto_customer_v2_activity_proto_rawDescGZIP() []byte {
	file_backend_proto_customer_v2_activity_proto_rawDescOnce.Do(func() {
		file_backend_proto_customer_v2_activity_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_activity_proto_rawDesc), len(file_backend_proto_customer_v2_activity_proto_rawDesc)))
	})
	return file_backend_proto_customer_v2_activity_proto_rawDescData
}

var file_backend_proto_customer_v2_activity_proto_enumTypes = make([]protoimpl.EnumInfo, 9)
var file_backend_proto_customer_v2_activity_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_backend_proto_customer_v2_activity_proto_goTypes = []any{
	(Task_State)(0),                      // 0: backend.proto.customer.v2.Task.State
	(ActivityLog_Type)(0),                // 1: backend.proto.customer.v2.ActivityLog.Type
	(ActivityLog_Message_State)(0),       // 2: backend.proto.customer.v2.ActivityLog.Message.State
	(ActivityLog_Message_Direction)(0),   // 3: backend.proto.customer.v2.ActivityLog.Message.Direction
	(ActivityLog_Call_State)(0),          // 4: backend.proto.customer.v2.ActivityLog.Call.State
	(ActivityLog_Call_Direction)(0),      // 5: backend.proto.customer.v2.ActivityLog.Call.Direction
	(ActivityLog_Task_Type)(0),           // 6: backend.proto.customer.v2.ActivityLog.Task.Type
	(ActivityLog_Convert_ConvertType)(0), // 7: backend.proto.customer.v2.ActivityLog.Convert.ConvertType
	(SystemSource_Source)(0),             // 8: backend.proto.customer.v2.SystemSource.Source
	(*LifeCycle)(nil),                    // 9: backend.proto.customer.v2.LifeCycle
	(*ActionState)(nil),                  // 10: backend.proto.customer.v2.ActionState
	(*Task)(nil),                         // 11: backend.proto.customer.v2.Task
	(*ActivityLog)(nil),                  // 12: backend.proto.customer.v2.ActivityLog
	(*Note)(nil),                         // 13: backend.proto.customer.v2.Note
	(*Tag)(nil),                          // 14: backend.proto.customer.v2.Tag
	(*Source)(nil),                       // 15: backend.proto.customer.v2.Source
	(*SystemSource)(nil),                 // 16: backend.proto.customer.v2.SystemSource
	(*ActivityLog_Message)(nil),          // 17: backend.proto.customer.v2.ActivityLog.Message
	(*ActivityLog_Call)(nil),             // 18: backend.proto.customer.v2.ActivityLog.Call
	(*ActivityLog_Note)(nil),             // 19: backend.proto.customer.v2.ActivityLog.Note
	(*ActivityLog_Task)(nil),             // 20: backend.proto.customer.v2.ActivityLog.Task
	(*ActivityLog_Convert)(nil),          // 21: backend.proto.customer.v2.ActivityLog.Convert
	(*ActivityLog_Create)(nil),           // 22: backend.proto.customer.v2.ActivityLog.Create
	(*ActivityLog_Action)(nil),           // 23: backend.proto.customer.v2.ActivityLog.Action
	(*timestamppb.Timestamp)(nil),        // 24: google.protobuf.Timestamp
}
var file_backend_proto_customer_v2_activity_proto_depIdxs = []int32{
	24, // 0: backend.proto.customer.v2.Task.complete_time:type_name -> google.protobuf.Timestamp
	0,  // 1: backend.proto.customer.v2.Task.state:type_name -> backend.proto.customer.v2.Task.State
	1,  // 2: backend.proto.customer.v2.ActivityLog.type:type_name -> backend.proto.customer.v2.ActivityLog.Type
	23, // 3: backend.proto.customer.v2.ActivityLog.action:type_name -> backend.proto.customer.v2.ActivityLog.Action
	24, // 4: backend.proto.customer.v2.ActivityLog.create_time:type_name -> google.protobuf.Timestamp
	16, // 5: backend.proto.customer.v2.ActivityLog.source:type_name -> backend.proto.customer.v2.SystemSource
	16, // 6: backend.proto.customer.v2.Note.create_source:type_name -> backend.proto.customer.v2.SystemSource
	16, // 7: backend.proto.customer.v2.Note.update_source:type_name -> backend.proto.customer.v2.SystemSource
	24, // 8: backend.proto.customer.v2.Note.create_time:type_name -> google.protobuf.Timestamp
	24, // 9: backend.proto.customer.v2.Note.update_time:type_name -> google.protobuf.Timestamp
	8,  // 10: backend.proto.customer.v2.SystemSource.source:type_name -> backend.proto.customer.v2.SystemSource.Source
	2,  // 11: backend.proto.customer.v2.ActivityLog.Message.state:type_name -> backend.proto.customer.v2.ActivityLog.Message.State
	3,  // 12: backend.proto.customer.v2.ActivityLog.Message.direction:type_name -> backend.proto.customer.v2.ActivityLog.Message.Direction
	4,  // 13: backend.proto.customer.v2.ActivityLog.Call.state:type_name -> backend.proto.customer.v2.ActivityLog.Call.State
	5,  // 14: backend.proto.customer.v2.ActivityLog.Call.direction:type_name -> backend.proto.customer.v2.ActivityLog.Call.Direction
	6,  // 15: backend.proto.customer.v2.ActivityLog.Task.type:type_name -> backend.proto.customer.v2.ActivityLog.Task.Type
	11, // 16: backend.proto.customer.v2.ActivityLog.Task.task:type_name -> backend.proto.customer.v2.Task
	7,  // 17: backend.proto.customer.v2.ActivityLog.Convert.origin_type:type_name -> backend.proto.customer.v2.ActivityLog.Convert.ConvertType
	7,  // 18: backend.proto.customer.v2.ActivityLog.Convert.source_id:type_name -> backend.proto.customer.v2.ActivityLog.Convert.ConvertType
	7,  // 19: backend.proto.customer.v2.ActivityLog.Convert.target_type:type_name -> backend.proto.customer.v2.ActivityLog.Convert.ConvertType
	7,  // 20: backend.proto.customer.v2.ActivityLog.Convert.target_id:type_name -> backend.proto.customer.v2.ActivityLog.Convert.ConvertType
	17, // 21: backend.proto.customer.v2.ActivityLog.Action.message:type_name -> backend.proto.customer.v2.ActivityLog.Message
	18, // 22: backend.proto.customer.v2.ActivityLog.Action.call:type_name -> backend.proto.customer.v2.ActivityLog.Call
	19, // 23: backend.proto.customer.v2.ActivityLog.Action.note:type_name -> backend.proto.customer.v2.ActivityLog.Note
	20, // 24: backend.proto.customer.v2.ActivityLog.Action.task:type_name -> backend.proto.customer.v2.ActivityLog.Task
	21, // 25: backend.proto.customer.v2.ActivityLog.Action.convert:type_name -> backend.proto.customer.v2.ActivityLog.Convert
	22, // 26: backend.proto.customer.v2.ActivityLog.Action.create:type_name -> backend.proto.customer.v2.ActivityLog.Create
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_backend_proto_customer_v2_activity_proto_init() }
func file_backend_proto_customer_v2_activity_proto_init() {
	if File_backend_proto_customer_v2_activity_proto != nil {
		return
	}
	file_backend_proto_customer_v2_common_proto_init()
	file_backend_proto_customer_v2_activity_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_proto_msgTypes[14].OneofWrappers = []any{
		(*ActivityLog_Action_Message)(nil),
		(*ActivityLog_Action_Call)(nil),
		(*ActivityLog_Action_Note)(nil),
		(*ActivityLog_Action_Task)(nil),
		(*ActivityLog_Action_Convert)(nil),
		(*ActivityLog_Action_Create)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_activity_proto_rawDesc), len(file_backend_proto_customer_v2_activity_proto_rawDesc)),
			NumEnums:      9,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_customer_v2_activity_proto_goTypes,
		DependencyIndexes: file_backend_proto_customer_v2_activity_proto_depIdxs,
		EnumInfos:         file_backend_proto_customer_v2_activity_proto_enumTypes,
		MessageInfos:      file_backend_proto_customer_v2_activity_proto_msgTypes,
	}.Build()
	File_backend_proto_customer_v2_activity_proto = out.File
	file_backend_proto_customer_v2_activity_proto_goTypes = nil
	file_backend_proto_customer_v2_activity_proto_depIdxs = nil
}
