syntax = "proto3";
// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: This is a legacy inner service package that doesn't follow versioned package naming convention. --)
package backend.proto.fulfillment.inner;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "backend/proto/fulfillment/v1/common.proto"; // 这里需要引入common.proto
import "backend/proto/fulfillment/v1/appointment.proto"; // 这里需要引入appointment.proto

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/inner;fulfillmentinnerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.inner";

// 美容宠物详情DTO - 对应Java的GroomingPetDetailDTO
// (-- api-linter: core::0142::time-field-type=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
message GroomingPetDetailDTO {
    // 详情ID
    int64 id = 1;
    // 履约ID
    int64 grooming_id = 2;
    // 宠物ID
    int64 pet_id = 3;
    // 员工ID
    int64 staff_id = 4;
    // 服务实例ID
    int64 service_id = 5;
    // 服务时长(分钟)
    int32 service_time = 6;
    // 服务价格
    double service_price = 7;
    // 开始时间
    // (-- api-linter: core::0142::time-field-type=disabled
    //     aip.dev/not-precedent: Legacy field using int64 timestamp for backward compatibility. --)
    int64 start_time = 8;
    // 结束时间
    // (-- api-linter: core::0142::time-field-type=disabled
    //     aip.dev/not-precedent: Legacy field using int64 timestamp for backward compatibility. --)
    int64 end_time = 9;
    // 范围类型价格
    int32 scope_type_price = 10;
    // 范围类型时长
    int32 scope_type_time = 11;
    // 标星员工ID
    int64 star_staff_id = 12;
    // 服务名称
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: Legacy field naming convention maintained for backward compatibility. --)
    string service_name = 13;
    // 服务类型
    int32 service_type = 14;
    // 服务状态
    int32 service_status = 15;
    // 员工名字
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: Legacy field naming convention maintained for backward compatibility. --)
    string staff_first_name = 16;
    // 员工姓氏
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: Legacy field naming convention maintained for backward compatibility. --)
    string staff_last_name = 17;
    // 宠物名字
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: Legacy field naming convention maintained for backward compatibility. --)
    string pet_name = 18;
    // 宠物生命状态
    int32 pet_life_status = 19;
    // 是否启用操作
    bool enable_operation = 20;
    // 颜色代码
    string color_code = 21;
    // 工作模式 (0 - parallel, 1 - sequence)
    int32 work_mode = 22;
    // 操作记录列表
    repeated GroomingServiceOperationDTO operation_list = 23;
    // 服务项目类型
    int32 service_item_type = 24;
    // 住宿ID
    int64 lodging_id = 25;
    // 开始日期
    // (-- api-linter: core::0142::time-field-type=disabled
    //     aip.dev/not-precedent: Legacy field using string date for backward compatibility. --)
    string start_date = 26;
    // 结束日期
    // (-- api-linter: core::0142::time-field-type=disabled
    //     aip.dev/not-precedent: Legacy field using string date for backward compatibility. --)
    string end_date = 27;
    // 价格单位 (内部计算用，暂时不用对外暴露)
    int32 price_unit = 28;
    // 特定日期
    string specific_dates = 29;
    // 关联服务ID
    int64 associated_service_id = 30;
    // 本条pet detail等价的order line item中quantity数量
    int32 quantity = 31;
    // 价格覆盖类型
    backend.proto.fulfillment.v1.OverrideType price_override_type = 32;
    // 时长覆盖类型
    backend.proto.fulfillment.v1.OverrideType duration_override_type = 33;
    // 更新时间
    // (-- api-linter: core::0142::time-field-names=disabled
    //     api-linter: core::0140::prepositions=disabled
    //     aip.dev/not-precedent: Legacy field naming convention using 'at' suffix maintained for backward compatibility. --)
    google.protobuf.Timestamp updated_at = 34;
    // 每日数量
    int32 quantity_per_day = 35;
    // 是否需要专用员工
    bool require_dedicated_staff = 36;
    // 日期类型 (1-every day except checkout day, 2-specific date, 3-date point, 4-every day include checkout day)
    int32 date_type = 37;
    // 订单行项目ID
    int64 order_line_item_id = 38;
    // 外部ID (petdetail_xxx, evaluation_xxx)
    string external_id = 39;
}

// 美容服务操作DTO - 对应Java的GroomingServiceOperationDTO
message GroomingServiceOperationDTO {
    // 操作ID
    int64 id = 1;
    // 商家ID
    int32 business_id = 2;
    // 履约ID
    int32 grooming_id = 3;
    // 美容服务ID
    int32 grooming_service_id = 4;
    // 宠物ID
    int32 pet_id = 5;
    // 员工ID (应该大于0)
    int32 staff_id = 6;
    // 操作名称 (最大150字符)
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: Legacy field naming convention maintained for backward compatibility. --)
    string operation_name = 7;
    // 开始时间 (0-1440分钟)
    // (-- api-linter: core::0142::time-field-type=disabled
    //     aip.dev/not-precedent: Legacy field using int32 time for backward compatibility. --)
    int32 start_time = 8;
    // 服务时长 (0-1440分钟)
    // (-- api-linter: core::0142::time-field-type=disabled
    //     aip.dev/not-precedent: Legacy field using int32 duration for backward compatibility. --)
    int32 duration = 9;
    // 备注 (最大200字符)
    string comment = 10;
    // 操作价格 (最多18位整数，2位小数)
    double price = 11;
    // 价格比例 (最多1位整数，2位小数)
    double price_ratio = 12;
}
