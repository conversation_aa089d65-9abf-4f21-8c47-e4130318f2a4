syntax = "proto3";

package backend.proto.helloworld.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/helloworld/v1;helloworldpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.helloworld.v1";

import "validate/validate.proto";
import "google/api/annotations.proto";
import "google/api/field_behavior.proto";

// HelloworldService
service HelloworldService {
  // SendPing sends a ping request and returns a pong response
  rpc SendPing(SendPingRequest) returns (SendPingResponse) {
    option (google.api.http) = {
      get: "/v1/helloworld:sendPing"
    };
  }
}

// SendPingRequest
message SendPingRequest {
  // 测试用的字段
  string ping = 1 [
    (validate.rules).string.min_len = 1,
    (google.api.field_behavior) = REQUIRED
  ]; 
}

// SendPingResponse
message SendPingResponse {
  // pong
  string pong = 1;
}

// ErrCode 定义错误码枚举
// 
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
enum ErrCode {
  // (-- api-linter: core::0126::unspecified=disabled
  //     aip.dev/not-precedent: We need to do this because 
  //     the content of the error code is automatically generated by
  //     the script and is exclusive to each service.
  //     Please do not turn off this linter for the rest of the enum --)
  // 成功
  ERR_CODE_OK = 0;
  // 本服务自动分配的全局唯一的起始错误码
  ERR_CODE_UNSPECIFIED = 106400; 
}