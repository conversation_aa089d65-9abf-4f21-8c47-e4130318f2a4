package validation

import (
	"context"

	protovalidate "github.com/bufbuild/protovalidate-go"
	"github.com/bufbuild/protovalidate-go/legacy"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/proto"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/filter"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/plugin"
)

const (
	pluginName = "validation"
	pluginType = "auth"
)

func init() {
	plugin.Register(pluginName, &Plugin{})
}

// Plugin implements the trpc validation plugin.
type Plugin struct {
	Validator *protovalidate.Validator
}

// Type validation plugin type.
func (p *Plugin) Type() string {
	return pluginType
}

// Setup initializes the validation plugin instance.
func (p *Plugin) Setup(_ string, configDec plugin.Decoder) error {
	c := defaultConfig

	// When the configuration is not empty,
	// the default values are overridden during configuration parsing.
	if configDec != nil {
		if err := configDec.Decode(&c); err != nil {
			return err
		}
	}

	v, err := protovalidate.New(
		legacy.WithLegacySupport(legacy.ModeMerge),
		protovalidate.WithFailFast(true),
	)
	if err != nil {
		panic(err)
	}

	p.Validator = v

	if !c.EnableErrorLog {
		c.EnableErrorLog = true
	}

	filter.Register(pluginName, p.ServerFilterWithOptions(c), nil)
	return nil
}

// ServerFilterWithOptions automatically validates the req input parameters during server-side RPC invocation.
func (p *Plugin) ServerFilterWithOptions(c config) filter.ServerFilter {
	return func(ctx context.Context, req interface{}, handler filter.ServerHandleFunc) (interface{}, error) {
		// The request structure has not been validated by Validator.
		msg, ok := req.(proto.Message)
		if !ok {
			return handler(ctx, req)
		}

		// Verification passed.
		err := p.Validator.Validate(msg)
		if err == nil {
			return handler(ctx, req)
		}

		// Record logs as needed when the verification fails.
		errMsg := err.Error()
		if c.EnableErrorLog {
			if head, ok := ctx.Value(http.ContextKeyHeader).(*http.Header); ok {
				reqPath := head.Request.URL.Path
				reqRawQuery := head.Request.URL.RawQuery
				reqUserAgent := head.Request.Header.Get("User-Agent")
				reqReferer := head.Request.Header.Get("Referer")
				log.WithContext(ctx,
					log.Field{Key: "request_content", Value: req},
					log.Field{Key: "request_path", Value: reqPath},
					log.Field{Key: "request_query", Value: reqRawQuery},
					log.Field{Key: "request_useragent", Value: reqUserAgent},
					log.Field{Key: "request_referer", Value: reqReferer},
				).Errorf("validation request error: %s", errMsg)
			} else {
				log.WithContext(ctx, log.Field{Key: "request_content", Value: req}).
					Errorf("validation request error: %s", errMsg)
			}
		}

		return nil, errs.Newm(c.ServerValidateErrCode, errMsg)
	}
}

// defaultConfig is the default config of parameter.
var defaultConfig = config{
	EnableErrorLog:        false,
	ServerValidateErrCode: int32(codes.InvalidArgument),
}

// config is the config for parameter validation.
type config struct {
	EnableErrorLog        bool  `yaml:"enable_error_log"`
	ServerValidateErrCode int32 `yaml:"server_validate_err_code"`
}

// Option sets an option for the parameter.
type Option func(*config)

// WithErrorLog sets whether to log errors.
func WithErrorLog(allow bool) Option {
	return func(opts *config) {
		opts.EnableErrorLog = allow
	}
}

// WithServerValidateErrCode sets the error code for server-side request validation failure.
func WithServerValidateErrCode(code int32) Option {
	return func(opts *config) {
		opts.ServerValidateErrCode = code
	}
}
