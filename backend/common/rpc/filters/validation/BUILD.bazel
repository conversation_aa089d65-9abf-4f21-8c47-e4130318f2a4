load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "validation",
    srcs = ["validation.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/filters/validation",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/filter",
        "//backend/common/rpc/framework/http",
        "//backend/common/rpc/framework/log",
        "//backend/common/rpc/framework/plugin",
        "@com_github_bufbuild_protovalidate_go//:protovalidate-go",
        "@com_github_bufbuild_protovalidate_go//legacy",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_protobuf//proto",
    ],
)
