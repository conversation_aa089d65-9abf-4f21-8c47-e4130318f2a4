load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "adminopenapi_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/adminopenapi",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/adminopenapi/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "//backend/proto/openapi/admin/platform_sales/v1:platform_sales",
    ],
)

go_binary(
    name = "adminopenapi",
    embed = [":adminopenapi_lib"],
    visibility = ["//visibility:public"],
)
