package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/adminopenapi/logic/platformsales"
	platformsalespb "github.com/MoeGolibrary/moego/backend/proto/openapi/admin/platform_sales/v1"
)

type PlatformSales struct {
	ps *platformsales.Logic
	platformsalespb.UnimplementedPlatformSalesServiceServer
}

func NewPlatformSales() *PlatformSales {
	return &PlatformSales{
		ps: platformsales.NewLogic(),
	}
}

func (g *PlatformSales) CreateSalesLink(ctx context.Context, req *platformsalespb.CreateSalesLinkRequest) (
	*platformsalespb.CreateSalesLinkResponse, error) {
	link, err := g.ps.CreateSalesLink(ctx, req)
	if err != nil {
		return nil, err
	}

	return &platformsalespb.CreateSalesLinkResponse{
		SalesLink: link,
	}, nil
}
