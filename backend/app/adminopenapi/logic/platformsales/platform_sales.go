package platformsales

import (
	"context"
	"errors"
	"os"
	"strconv"

	"github.com/shopspring/decimal"
	decimalpb "google.golang.org/genproto/googleapis/type/decimal"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/MoeGolibrary/moego/backend/app/adminopenapi/repo"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	platformsalespb "github.com/MoeGolibrary/moego/backend/proto/openapi/admin/platform_sales/v1"
)

type Logic struct {
	payment *repo.Payment
}

func NewLogic() *Logic {
	return &Logic{
		payment: repo.NewPayment(),
	}
}

func (l *Logic) CreateSalesLink(ctx context.Context, req *platformsalespb.CreateSalesLinkRequest) (string, error) {
	log.DebugContextf(ctx, "Hello")

	params, err := BuildParams(req)
	if err != nil {
		return "", err
	}

	log.InfoContextf(ctx, "params: %v", params)

	token, err := l.payment.CreateSalesToken(ctx, params)
	if err != nil {
		return "", err
	}

	return BuildSalesLink(token), nil
}

func BuildParams(req *platformsalespb.CreateSalesLinkRequest) (map[string]interface{}, error) {
	params := map[string]interface{}{}

	params["creator"] = req.GetCreator()
	params["email"] = req.GetEmail()

	// plan
	params["premiumType"] = GetPremiumType(req.GetSubscriptionPlan())
	if req.IsAnnualPlan {
		params["showAnnuallyTerm"] = true
		params["showMonthlyTerm"] = false

		if req.GetContractTermMonths() <= 0 {
			return nil, errors.New("contract term months must be greater than 0")
		}
		params["subTerm"] = req.GetContractTermMonths()

		// 目前只支持整数, 如果需要支持小数, 需要 payment 服务端支持
		discount, err := DecimalToInt(req.GetAnnualPlanDiscount())
		if err != nil {
			return nil, err
		}
		params["subPriceDiscount"] = discount

	} else {
		params["showMonthlyTerm"] = true
		params["showAnnuallyTerm"] = false
	}

	// company type
	companyType, err := GetCompanyType(req.GetVanCount(), req.GetGroomingLocationCount(), req.GetBdLocationCount())
	if err != nil {
		return nil, err
	}
	params["companyType"] = companyType
	params["vansNum"] = req.GetVanCount()
	// 目前还没拆分两种类型的 locationNum, 所以直接相加, 后面应该需要拆分
	params["locationNum"] = req.GetGroomingLocationCount() + req.GetBdLocationCount()
	// bd 开白名单
	params["isBdPlan"] = req.GetBdLocationCount() > 0

	// custom rate
	if req.GetEnableCustomRate() {
		params["isCustomRate"] = true

		// terminal card rate
		terminalCardRateString := GetRateString(req.GetInPersonRate(), req.GetInPersonFee())
		if len(terminalCardRateString) > 0 {
			params["terminalCardRate"] = terminalCardRateString
		}

		// non-terminal card rate
		nonTerminalCardRateString := GetRateString(req.GetOnlineRate(), req.GetOnlineFee())
		if len(nonTerminalCardRateString) > 0 {
			params["nonTerminalCardRate"] = nonTerminalCardRateString
		}

		// min monthly transaction
		minMonthlyTransaction := MoneyToString(req.GetMinMonthlyTransactionVolume())
		if len(minMonthlyTransaction) > 0 {
			params["minMonthlyTransaction"] = minMonthlyTransaction
		}
	}

	// hardware and discount
	params["needHardware"] = req.GetIncludeHardware()
	params["showHardware"] = req.GetIncludeHardware()
	if req.GetIncludeHardware() {
		// 目前只支持整数, 如果需要支持小数, 需要 payment 服务端支持
		discount, err := DecimalToInt(req.GetHardwareDiscount())
		if err != nil {
			return nil, err
		}
		params["hardwareDiscount"] = discount
	}

	// accounting
	params["showAccounting"] = req.GetIncludeAccounting()

	return params, nil
}

func GetCompanyType(vanCount, groomingLocationCount, bdLocationCount int32) (int32, error) {
	if vanCount <= 0 && groomingLocationCount <= 0 && bdLocationCount <= 0 {
		return 0, errors.New("at least one of vanCount, groomingLocationCount, bdLocationCount must be greater than 0")
	}

	// mobile
	if vanCount > 0 {
		// only mobile
		if groomingLocationCount == 0 && bdLocationCount == 0 {
			return 0, nil
		}
		// mobile + solon
		return 2, nil
	}

	// only salon
	return 1, nil
}

func GetPremiumType(plan string) int {
	switch plan {
	case "growth":
		return 2
	case "ultimate":
		return 3
	}
	// should not reach here
	return 0
}

func GetRateString(rate *decimalpb.Decimal, fee *money.Money) string {
	if rate == nil && fee == nil {
		return ""
	}

	rateString := DecimalToPercentString(rate)
	feeString := MoneyToString(fee)

	result := ""
	if len(rateString) > 0 {
		result += rateString
	}

	if len(feeString) > 0 {
		if len(result) > 0 {
			result += " + "
		}
		result += feeString
	}

	return result
}

func DecimalToPercentString(d *decimalpb.Decimal) string {
	if d == nil {
		return ""
	}
	if len(d.GetValue()) == 0 {
		return ""
	}

	return d.GetValue() + "%"
}

func MoneyToString(money *money.Money) string {
	if money == nil {
		return ""
	}

	units := money.GetUnits()
	nanos := money.GetNanos()
	if units == 0 && nanos == 0 {
		return ""
	}
	unitsStr := strconv.FormatInt(units, 10)

	// 计算小数部分, 保留两位小数, 超过两位小数部分直接舍弃
	d := decimal.NewFromInt32(nanos).Shift(-9 + 2).IntPart()
	// 如果小数部分为0, 则不显示
	if d == 0 {
		return "$" + unitsStr
	}
	dStr := strconv.FormatInt(d, 10)

	return "$" + unitsStr + "." + dStr
}

// 将 pb decimal 转成整数
func DecimalToInt(d *decimalpb.Decimal) (int64, error) {
	if d == nil {
		return 0, nil
	}

	value, err := decimal.NewFromString(d.GetValue())
	if err != nil {
		return 0, err
	}

	return value.IntPart(), nil
}

func BuildSalesLink(token string) string {
	host := ""
	env := os.Getenv("MOEGO_ENVIRONMENT")
	switch env {
	case "production":
		host = "https://go.moego.pet"
	case "staging":
		host = "https://go.s1.moego.dev"
	default:
		host = "https://go.t2.moego.dev"
	}

	return host + "/sales/setup?salesCode=" + token
}
