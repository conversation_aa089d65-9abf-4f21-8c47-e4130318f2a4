package repo

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"

	toolsutils "github.com/MoeGolibrary/moego/backend/app/tools/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Payment struct {
	client *http.Client
}

func NewPayment() *Payment {
	return &Payment{
		client: &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
		},
	}
}

func (p *Payment) CreateSalesToken(ctx context.Context, params map[string]interface{}) (string, error) {

	url := "http://moego-service-payment:9204/service/payment/platform/sales/link"
	body, err := json.Marshal(params)
	if err != nil {
		return "", err
	}

	req, err := http.NewRequest(http.MethodPost, url, bytes.NewBuffer(body))
	if err != nil {
		return "", err
	}

	// set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", toolsutils.GetUserAgent(ctx))

	resp, err := p.client.Do(req)
	if err != nil {
		return "", err
	}

	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			log.ErrorContext(ctx, "failed to close response body")
		}
	}(resp.Body)

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.ErrorContext(ctx, "failed to read response body, error: %v", err)
		bodyBytes = []byte("")
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d, response: %s", resp.StatusCode, string(bodyBytes))
	}

	moeStatusCode, _ := strconv.Atoi(resp.Header.Get("X-MOE-STATUS"))
	if moeStatusCode != 0 {
		return "", fmt.Errorf("unexpected moe status code: %d, response: %s", moeStatusCode, string(bodyBytes))
	}

	return string(bodyBytes), nil
}
