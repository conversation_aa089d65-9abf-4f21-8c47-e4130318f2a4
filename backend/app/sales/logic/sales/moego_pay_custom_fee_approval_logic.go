package sales

import (
	"context"
	"fmt"
	"time"

	"github.com/slack-go/slack"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/sales/config"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/payment"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	slackclient "github.com/MoeGolibrary/moego/backend/app/sales/repo/slack"
	salesutils "github.com/MoeGolibrary/moego/backend/app/sales/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

type MoegoPayCustomFeeApprovalCreateParams struct {
	CompanyID             int64
	AccountID             int64
	OwnerEmail            string
	Creator               string
	TerminalPercentage    string
	TerminalFixed         string
	NonTerminalPercentage string
	NonTerminalFixed      string
	MinVolume             string
	Spif                  *string
	OpportunityID         *string
	ContractID            *string
}

type MoegoPayCustomFeeApprovalLogic struct {
	rw          sales.MoegoPayCustomFeeApprovalReadWriter
	payment     payment.MoegoPayCustomFeeClient
	slackClient slackclient.WebhookClient
	slackPath   string
}

func NewMoegoPayCustomFeeApprovalLogic() *MoegoPayCustomFeeApprovalLogic {
	return &MoegoPayCustomFeeApprovalLogic{
		rw:          sales.NewMoegoPayCustomFeeApprovalRW(),
		payment:     payment.NewMoegoPayCustomFeeClient(),
		slackPath:   config.GetCfg().Slack.MoegoPayCustomRatePath,
		slackClient: slackclient.New(),
	}
}

func NewMoegoPayCustomFeeApprovalLogicByParams(rw sales.MoegoPayCustomFeeApprovalReadWriter,
	payment payment.MoegoPayCustomFeeClient,
	slackPath string, slackClient slackclient.WebhookClient) *MoegoPayCustomFeeApprovalLogic {
	return &MoegoPayCustomFeeApprovalLogic{
		rw:          rw,
		payment:     payment,
		slackPath:   slackPath,
		slackClient: slackClient,
	}
}

func (l *MoegoPayCustomFeeApprovalLogic) CreateMoegoPayCustomFeeApproval(ctx context.Context,
	params *MoegoPayCustomFeeApprovalCreateParams) (*salespb.MoegoPayCustomFeeApproval, error) {

	metadata := &sales.MoegoPayCustomFeeApprovalMetadata{
		ContractID:    params.ContractID,
		OpportunityID: params.OpportunityID,
		Spif:          params.Spif,
	}

	approval := &sales.MoegoPayCustomFeeApproval{
		ID:                    salesutils.NewUUID(),
		AccountID:             params.AccountID,
		CompanyID:             params.CompanyID,
		OwnerEmail:            params.OwnerEmail,
		TerminalPercentage:    params.TerminalPercentage,
		TerminalFixed:         params.TerminalFixed,
		NonTerminalPercentage: params.NonTerminalPercentage,
		NonTerminalFixed:      params.NonTerminalFixed,
		MinVolume:             params.MinVolume,
		Creator:               params.Creator,
		ApprovalState:         salespb.MoegoPayCustomFeeApproval_PENDING.String(),
		Metadata:              salesutils.ToJSONB(metadata),
	}
	if err := l.rw.Save(ctx, approval); err != nil {
		return nil, err
	}

	// 发送 slack 通知，发送失败不阻塞审批创建
	slackMsg := buildSlackMessage(params)
	if err := l.slackClient.SendMessage(ctx, l.slackPath, slackMsg); err != nil {
		log.ErrorContextf(ctx, "Send slack message err, err:%+v", err)
	}

	return buildMoegoPayCustomFeeApproval(approval), nil
}

func (l *MoegoPayCustomFeeApprovalLogic) ListMoegoPayCustomFeeApprovals(ctx context.Context,
	req *salespb.ListMoegoPayCustomFeeApprovalsRequest) (*salespb.ListMoegoPayCustomFeeApprovalsResponse, error) {

	filters := buildMoegoPayCustomFeeApprovalQueryFilters(req.GetFilters())
	page, err := sales.NewOffsetLimitPageFromPageToken[*sales.MoegoPayCustomFeeApproval](req.PageSize, req.PageToken)
	if err != nil {
		return nil, err
	}

	items, err := l.rw.List(ctx, page, filters...)
	if err != nil {
		return nil, err
	}

	nextPageToken := ""
	if nextPage := page.NextPage(items); nextPage != nil {
		nextPageToken = nextPage.Token()
	}

	approvals := make([]*salespb.MoegoPayCustomFeeApproval, 0, len(items))
	for _, a := range items {
		approvals = append(approvals, buildMoegoPayCustomFeeApproval(a))
	}

	return &salespb.ListMoegoPayCustomFeeApprovalsResponse{
		MoegoPayCustomFeeApprovals: approvals,
		NextPageToken:              nextPageToken,
	}, nil
}

func (l *MoegoPayCustomFeeApprovalLogic) GetMoegoPayCustomFeeApproval(ctx context.Context,
	req *salespb.GetMoegoPayCustomFeeApprovalRequest) (*salespb.MoegoPayCustomFeeApproval, error) {
	approval, err := l.rw.Get(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return buildMoegoPayCustomFeeApproval(approval), nil
}

func (l *MoegoPayCustomFeeApprovalLogic) CountMoegoPayCustomFeeApprovals(ctx context.Context,
	req *salespb.CountMoegoPayCustomFeeApprovalsRequest) (int64, error) {
	filters := buildMoegoPayCustomFeeApprovalQueryFilters(req.GetFilters())

	return l.rw.Count(ctx, filters...)
}

func (l *MoegoPayCustomFeeApprovalLogic) ApproveMoegoPayCustomFeeApproval(ctx context.Context, id, handler string) (
	*salespb.MoegoPayCustomFeeApproval, error) {
	approval, err := l.rw.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 幂等
	if approval.ApprovalState == salespb.MoegoPayCustomFeeApproval_APPROVED.String() {
		return buildMoegoPayCustomFeeApproval(approval), nil
	}

	// 非 pending 状态不能 approve
	if approval.ApprovalState != salespb.MoegoPayCustomFeeApproval_PENDING.String() {
		return nil, fmt.Errorf("can not approve a %s approval", approval.ApprovalState)
	}

	// 更新审批状态
	now := time.Now()
	approval.ApprovalState = salespb.MoegoPayCustomFeeApproval_APPROVED.String()
	approval.Handler = &handler
	approval.HandledAt = &now
	if err = l.rw.Save(ctx, approval); err != nil {
		return nil, err
	}

	return buildMoegoPayCustomFeeApproval(approval), nil
}

func (l *MoegoPayCustomFeeApprovalLogic) RejectMoegoPayCustomFeeApproval(ctx context.Context, id, handler string) (
	*salespb.MoegoPayCustomFeeApproval, error) {

	approval, err := l.rw.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 幂等
	if approval.ApprovalState == salespb.MoegoPayCustomFeeApproval_REJECTED.String() {
		return buildMoegoPayCustomFeeApproval(approval), nil
	}

	// 非 pending 状态不能 reject
	if approval.ApprovalState != salespb.MoegoPayCustomFeeApproval_PENDING.String() {
		return nil, fmt.Errorf("can not approve a %s approval", approval.ApprovalState)
	}

	// 更新审批状态
	now := time.Now()
	approval.ApprovalState = salespb.MoegoPayCustomFeeApproval_REJECTED.String()
	approval.Handler = &handler
	approval.HandledAt = &now
	if err = l.rw.Save(ctx, approval); err != nil {
		return nil, err
	}

	return buildMoegoPayCustomFeeApproval(approval), nil
}

//func (l *MoegoPayCustomFeeApprovalLogic) ApplyCustomFee(ctx context.Context, ap *salespb.MoegoPayCustomFeeApproval) (
//	*payment.MoegoPayCustomFee, error) {
//	// 查询 custom fee 是否存在
//	if customFee, err := l.payment.GetByCompanyID(ctx, ap.CompanyId); err != nil {
//		return nil, err
//	} else if customFee != nil {
//		// TODO: 暂时不支持对存在的 custom fee 进行更新，等 chi 的接口支持
//		return nil, fmt.Errorf("custom fee of company %d exists, skip", ap.CompanyId)
//	}
//
//	// 创建 custom fee
//	customFee := buildMoegoPayCustomFeeFromApproval(ctx, ap)
//	if err := l.payment.Create(ctx, customFee); err != nil {
//		return nil, err
//	}
//	return customFee, nil
//}

//func buildMoegoPayCustomFeeFromApproval(ctx context.Context,
//	ap *salespb.MoegoPayCustomFeeApproval) *payment.MoegoPayCustomFee {
//
//	terminalPercentage := salesutils.StringToFloat(&ap.TerminalPercentage)
//	terminalFixedDecimal, err := decimal.NewFromString(ap.TerminalFixed)
//	if err != nil {
//		log.ErrorContextf(ctx, "Fail to parse terminal fixed, err: %+v", err)
//	}
//	terminalFixedCents := terminalFixedDecimal.Mul(decimal.NewFromInt(100)).IntPart()
//
//	nonTerminalPercentage := salesutils.StringToFloat(&ap.NonTerminalPercentage)
//	nonTerminalFixedDecimal, err := decimal.NewFromString(ap.NonTerminalFixed)
//	if err != nil {
//		log.ErrorContextf(ctx, "Fail to parse non terminal fixed, err: %+v", err)
//	}
//	nonTerminalFixedCents := nonTerminalFixedDecimal.Mul(decimal.NewFromInt(100)).IntPart()
//
//	minVolumeDecimal, err := decimal.NewFromString(ap.MinVolume)
//	if err != nil {
//		log.ErrorContextf(ctx, "Fail to parse min volume, err: %+v", err)
//	}
//	minVolume := minVolumeDecimal.IntPart()
//
//	return &payment.MoegoPayCustomFee{
//		CompanyID:             &ap.CompanyId,
//		TerminalPercentage:    terminalPercentage,
//		TerminalFixedCents:    &terminalFixedCents,
//		NonTerminalPercentage: nonTerminalPercentage,
//		NonTerminalFixedCents: &nonTerminalFixedCents,
//		MinVolume:             &minVolume,
//	}
//}

func buildMoegoPayCustomFeeApproval(a *sales.MoegoPayCustomFeeApproval) *salespb.MoegoPayCustomFeeApproval {

	state := salespb.MoegoPayCustomFeeApproval_ApprovalState(
		salespb.MoegoPayCustomFeeApproval_ApprovalState_value[a.ApprovalState])

	var handledAt *timestamppb.Timestamp
	if a.HandledAt != nil {
		handledAt = timestamppb.New(*a.HandledAt)
	}

	metadata := a.GetMetadata()

	return &salespb.MoegoPayCustomFeeApproval{
		Id:                    a.ID,
		AccountId:             a.AccountID,
		CompanyId:             a.CompanyID,
		OwnerEmail:            a.OwnerEmail,
		TerminalPercentage:    a.TerminalPercentage,
		TerminalFixed:         a.TerminalFixed,
		NonTerminalPercentage: a.NonTerminalPercentage,
		NonTerminalFixed:      a.NonTerminalFixed,
		MinVolume:             a.MinVolume,
		ApprovalState:         state,
		Creator:               a.Creator,
		Handler:               a.Handler,
		HandleTime:            handledAt,
		CreateTime:            timestamppb.New(a.CreatedAt),
		UpdateTime:            timestamppb.New(a.UpdatedAt),
		Metadata: &salespb.MoegoPayCustomFeeApproval_Metadata{
			Spif:          metadata.Spif,
			OpportunityId: metadata.OpportunityID,
			ContractId:    metadata.ContractID,
		},
	}
}

func buildMoegoPayCustomFeeApprovalQueryFilters(
	qf *salespb.MoegoPayCustomFeeApprovalQueryFilters) []sales.MoegoPayCustomFeeApprovalQueryFilter {
	if qf == nil {
		return []sales.MoegoPayCustomFeeApprovalQueryFilter{}
	}
	a := sales.MoegoPayCustomFeeApproval{}
	states := make([]string, len(qf.ApprovalStates))
	for i, state := range qf.ApprovalStates {
		states[i] = state.String()
	}

	return []sales.MoegoPayCustomFeeApprovalQueryFilter{
		a.QueryWithCompanyID(qf.CompanyId),
		a.QueryWithAccountID(qf.AccountId),
		a.QueryWithOwnerEmail(qf.OwnerEmail),
		a.QueryWithApprovalStates(states),
	}
}

// buildSlackMessage example:
//
//	{
//	   "blocks": [
//	       {
//	           "type": "section",
//	           "text": {
//	               "type": "mrkdwn",
//	               "text": "New MoeGo Pay custom fee approval created by *_xxx@moego.pet_*"
//	           }
//	       },
//	       {
//	           "type": "section",
//	           "fields": [
//	               {
//	                   "type": "mrkdwn",
//	                   "text": "*Owner email:* <EMAIL>"
//	               },
//	               {
//	                   "type": "mrkdwn",
//	                   "text": "*Terminal fee:* 2.4% + $0.1"
//	               },
//	               {
//	                   "type": "mrkdwn",
//	                   "text": "*Min monthly transaction volume:* $80000"
//	               },
//	               {
//	                   "type": "mrkdwn",
//	                   "text": "*Non-Terminal fee:* 2.5% + $0.2"
//	               }
//	           ]
//	       },
//	       {
//	           "type": "section",
//	           "text": {
//	               "type": "mrkdwn",
//	               "text": "<https://mis.t2.moego.dev|Click to approve>"
//	           }
//	       }
//	   ]
//	}
func buildSlackMessage(params *MoegoPayCustomFeeApprovalCreateParams) *slack.Message {
	descriptionSection := slack.NewSectionBlock(
		&slack.TextBlockObject{
			Type: slack.MarkdownType,
			Text: fmt.Sprintf("<@U0365329L13> New MoeGo Pay custom fee approval created by *_%s_*",
				params.Creator),
		},
		nil,
		nil,
	)

	paramsSection := slack.NewSectionBlock(
		nil,
		[]*slack.TextBlockObject{
			{
				Type: slack.MarkdownType,
				Text: fmt.Sprintf("*Owner email:* %s", params.OwnerEmail),
			},
			{
				Type: slack.MarkdownType,
				Text: fmt.Sprintf("*Terminal fee:* %s%% + $%s", params.TerminalPercentage, params.TerminalFixed),
			},
			{
				Type: slack.MarkdownType,
				Text: fmt.Sprintf("*Min monthly transaction volume:* $%s", params.MinVolume),
			},
			{
				Type: slack.MarkdownType,
				Text: fmt.Sprintf("*Non-terminal fee:* %s%% + $%s",
					params.NonTerminalPercentage, params.NonTerminalFixed),
			},
		},
		nil,
	)

	// TODO: add email as url params
	linkSection := slack.NewSectionBlock(
		&slack.TextBlockObject{
			Type: slack.MarkdownType,
			Text: fmt.Sprintf("<https://%s/pay_ops/custom_fee_approval|Click to approval page>",
				salesutils.GetMISHost()),
		}, nil,
		nil,
	)

	msg := slack.NewBlockMessage(descriptionSection, paramsSection, linkSection)

	return &msg
}
