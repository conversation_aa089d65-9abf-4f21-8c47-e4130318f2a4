package sales_test

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	accountpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	"github.com/MoeGolibrary/moego/backend/app/sales/logic/sales"
	accountmock "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/account"
	organizationmock "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/organization"
	salesmock "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/sales"
	salesrepo "github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

func TestGetFormatedDate(t *testing.T) {
	now := time.UnixMilli(*************)
	s := sales.GetFormatedDate(context.Background(), &now, "Asia/Shanghai")
	require.Equal(t, "June 25, 2025, 08:15AM", s)
	s = sales.GetFormatedDate(context.Background(), &now, "America/New_York")
	require.Equal(t, "June 24, 2025, 08:15PM", s)
	s = sales.GetFormatedDate(context.Background(), &now, "UTC")
	require.Equal(t, "June 25, 2025, 12:15AM", s)
	// unknown timezone, default to UTC
	s = sales.GetFormatedDate(context.Background(), &now, "Unknown")
	require.Equal(t, "June 25, 2025, 12:15AM", s)
}

func TestCreateMoegoPayContract(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTpl := salesmock.NewMockContractTemplateReadWriter(ctrl)
	mockContract := salesmock.NewMockContractReadWriter(ctrl)
	mockOrg := organizationmock.NewMockOrganization(ctrl)
	mockAccount := accountmock.NewMockAccount(ctrl)

	logic := sales.NewMoegoPayContractLogicByParams(mockTpl, mockContract, mockOrg, mockAccount)

	t.Run("Create Moego Pay Contract", func(t *testing.T) {
		req := &salespb.CreateMoegoPayContractRequest{
			CompanyId:             123,
			TerminalPercentage:    "2.30",
			TerminalFixed:         "0.10",
			NonTerminalPercentage: "2.30",
			NonTerminalFixed:      "0.10",
			MinVolume:             "1000.00",
			Creator:               "<EMAIL>",
		}

		template := &salesrepo.ContractTemplate{
			ID:       "xxx",
			Name:     "Moego Pay Contract",
			Template: "Contract content {{.CompanyName}}",
		}

		company := &organizationpb.CompanyModel{
			Id:        123,
			Name:      "test company",
			AccountId: 456,
		}

		companySetting := &organizationpb.CompanyPreferenceSettingModel{
			TimeZone: &organizationpb.TimeZone{
				Name: "Asia/Shanghai",
			},
		}

		account := &accountpb.AccountModel{
			Id:        456,
			FirstName: "First",
			LastName:  "Last",
			Email:     "<EMAIL>",
		}

		// 设置期望的调用
		mockTpl.EXPECT().GetNewestTemplate(gomock.Any(), sales.MoegoPayContractName).Return(template, nil)
		mockOrg.EXPECT().GetCompany(gomock.Any(), int64(123)).Return(company, nil)
		mockOrg.EXPECT().GetCompanySetting(gomock.Any(), int64(123)).Return(companySetting, nil)
		mockAccount.EXPECT().GetAccount(gomock.Any(), int64(456)).Return(account, nil)
		mockContract.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)

		result, err := logic.CreateMoegoPayContract(context.Background(), req)
		assert.NoError(t, err)
		assert.NotNil(t, result)

		assert.Equal(t, req.CompanyId, result.Metadata.CompanyId)
		assert.Equal(t, account.Id, result.Metadata.AccountId)
		assert.Equal(t, account.Email, result.Parameters.OwnerEmail)
		assert.Equal(t, company.Name, result.Parameters.CompanyName)
		assert.Equal(t, fmt.Sprintf("%s %s", account.FirstName, account.LastName), result.Parameters.OwnerName)
		assert.Equal(t, req.TerminalPercentage, result.Parameters.TerminalPercentage)
		assert.Equal(t, req.TerminalFixed, result.Parameters.TerminalFixed)
		assert.Equal(t, req.NonTerminalPercentage, result.Parameters.NonTerminalPercentage)
		assert.Equal(t, req.NonTerminalFixed, result.Parameters.NonTerminalFixed)
		assert.Equal(t, req.MinVolume, result.Parameters.MinVolume)
		assert.Equal(t, req.Creator, result.Creator)
		assert.Equal(t, template.ID, result.TemplateId)
		assert.Equal(t, "Contract content test company", result.Content)
		assert.Nil(t, result.SignTime)
		assert.Nil(t, result.SignatureUri)
	})
}

func TestDeleteMoegoPayContract(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTpl := salesmock.NewMockContractTemplateReadWriter(ctrl)
	mockContract := salesmock.NewMockContractReadWriter(ctrl)
	mockOrg := organizationmock.NewMockOrganization(ctrl)
	mockAccount := accountmock.NewMockAccount(ctrl)

	logic := sales.NewMoegoPayContractLogicByParams(mockTpl, mockContract, mockOrg, mockAccount)

	t.Run("Delete Unsigned Moego Pay Contract", func(t *testing.T) {
		contractID := "abc"
		contract := &salesrepo.Contract{
			ID:       contractID,
			SignedAt: nil,
		}

		mockContract.EXPECT().Get(gomock.Any(), contractID).Return(contract, nil)
		mockContract.EXPECT().Delete(gomock.Any(), contractID).Return(nil)

		err := logic.DeleteMoegoPayContract(context.Background(), contractID)
		assert.NoError(t, err)
	})

	t.Run("Unable to delete signed Moego Pay Contract", func(t *testing.T) {
		contractID := "abc"
		signedAt := time.Now()
		contract := &salesrepo.Contract{
			ID:       contractID,
			SignedAt: &signedAt,
		}

		mockContract.EXPECT().Get(gomock.Any(), contractID).Return(contract, nil)

		err := logic.DeleteMoegoPayContract(context.Background(), contractID)
		assert.Error(t, err)
	})
}

func TestGetMoegoPayContract(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTpl := salesmock.NewMockContractTemplateReadWriter(ctrl)
	mockContract := salesmock.NewMockContractReadWriter(ctrl)
	mockOrg := organizationmock.NewMockOrganization(ctrl)
	mockAccount := accountmock.NewMockAccount(ctrl)

	logic := sales.NewMoegoPayContractLogicByParams(mockTpl, mockContract, mockOrg, mockAccount)

	t.Run("Get Moego Pay Contract", func(t *testing.T) {
		contractID := "abc"
		contract := &salesrepo.Contract{
			ID:         contractID,
			Creator:    "<EMAIL>",
			TemplateID: "xxx",
			Content:    "Contract content test company",
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		}

		mockContract.EXPECT().Get(gomock.Any(), contractID).Return(contract, nil)

		result, err := logic.GetMoegoPayContract(context.Background(), contractID)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, contractID, result.Id)
		assert.Equal(t, contract.Creator, result.Creator)
	})
}

func TestListMoegoPayContracts(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTpl := salesmock.NewMockContractTemplateReadWriter(ctrl)
	mockContract := salesmock.NewMockContractReadWriter(ctrl)
	mockOrg := organizationmock.NewMockOrganization(ctrl)
	mockAccount := accountmock.NewMockAccount(ctrl)

	logic := sales.NewMoegoPayContractLogicByParams(mockTpl, mockContract, mockOrg, mockAccount)

	templates := []*salesrepo.ContractTemplate{
		{ID: "template1"},
		{ID: "template2"},
	}

	now := time.Now()
	nowAddHour := now.Add(time.Hour)
	contracts := []*salesrepo.Contract{
		{
			ID:         "contract1",
			Creator:    "<EMAIL>",
			TemplateID: "template1",
			Content:    "content1",
			CreatedAt:  now,
			UpdatedAt:  now,
		},
		{
			ID:           "contract2",
			Creator:      "<EMAIL>",
			TemplateID:   "template1",
			Content:      "content2",
			CreatedAt:    now,
			UpdatedAt:    now,
			SignedAt:     &nowAddHour,
			SignatureURI: pointer.Get("xxxx"),
		},
	}

	t.Run("List Moego Pay Contracts", func(t *testing.T) {
		req := &salespb.ListMoegoPayContractsRequest{
			PageSize:  10,
			PageToken: "1",
			Filters:   &salespb.MoegoPayContractQueryFilters{},
		}

		mockTpl.EXPECT().List(gomock.Any(), gomock.Any()).Return(templates, nil)
		mockContract.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(contracts, nil)

		result, err := logic.ListMoegoPayContracts(context.Background(), req)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.MoegoPayContracts, 2)
		assert.Equal(t, result.NextPageToken, "")
	})

	t.Run("List Moego Pay Contracts with multi pages", func(t *testing.T) {
		req := &salespb.ListMoegoPayContractsRequest{
			PageSize:  1,
			PageToken: "1",
		}

		mockTpl.EXPECT().List(gomock.Any(), gomock.Any()).Return(templates, nil)
		mockContract.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(contracts[:1], nil)

		result, err := logic.ListMoegoPayContracts(context.Background(), req)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.MoegoPayContracts, 1)
		assert.Equal(t, "2", result.NextPageToken)
	})
}

func TestCountMoegoPayContracts(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTpl := salesmock.NewMockContractTemplateReadWriter(ctrl)
	mockContract := salesmock.NewMockContractReadWriter(ctrl)
	mockOrg := organizationmock.NewMockOrganization(ctrl)
	mockAccount := accountmock.NewMockAccount(ctrl)

	logic := sales.NewMoegoPayContractLogicByParams(mockTpl, mockContract, mockOrg, mockAccount)

	t.Run("Count Moego Pay Contracts", func(t *testing.T) {
		req := &salespb.CountMoegoPayContractsRequest{
			Filters: &salespb.MoegoPayContractQueryFilters{},
		}

		count := int64(5)
		templates := []*salesrepo.ContractTemplate{
			{ID: "template1"},
			{ID: "template2"},
		}

		mockTpl.EXPECT().List(gomock.Any(), gomock.Any()).Return(templates, nil)
		mockContract.EXPECT().Count(gomock.Any(), gomock.Any()).Return(count, nil)

		count, err := logic.CountMoegoPayContracts(context.Background(), req)
		assert.NoError(t, err)
		assert.Equal(t, count, count)
	})

	t.Run("Count Moego Pay Contracts failed", func(t *testing.T) {
		req := &salespb.CountMoegoPayContractsRequest{
			Filters: &salespb.MoegoPayContractQueryFilters{},
		}

		mockTpl.EXPECT().List(gomock.Any(), gomock.Any()).Return(nil, errors.New("error"))
		count, err := logic.CountMoegoPayContracts(context.Background(), req)
		assert.Error(t, err)
		assert.Equal(t, int64(0), count)
	})
}

func TestSignMoegoPayContract(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTpl := salesmock.NewMockContractTemplateReadWriter(ctrl)
	mockContract := salesmock.NewMockContractReadWriter(ctrl)
	mockOrg := organizationmock.NewMockOrganization(ctrl)
	mockAccount := accountmock.NewMockAccount(ctrl)

	logic := sales.NewMoegoPayContractLogicByParams(mockTpl, mockContract, mockOrg, mockAccount)

	t.Run("Sign MoeGo Pay Contract", func(t *testing.T) {
		contractID := "abc"
		templateID := "xxx"
		signatureURI := "https://example.com/signature.png"
		req := &salespb.SignMoegoPayContractRequest{
			Id:           contractID,
			SignatureUri: signatureURI,
		}

		contract := &salesrepo.Contract{
			ID:         contractID,
			TemplateID: templateID,
			SignedAt:   nil,
		}

		template := &salesrepo.ContractTemplate{
			ID:       templateID,
			Template: "Contract content test company",
		}

		mockContract.EXPECT().Get(gomock.Any(), contractID).Return(contract, nil)
		mockTpl.EXPECT().Get(gomock.Any(), templateID).Return(template, nil)
		mockContract.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)

		result, err := logic.SignMoegoPayContract(context.Background(), req)
		assert.NoError(t, err)
		assert.NotNil(t, result)
	})

	t.Run("Unable to sign signed MoeGo Pay Contract", func(t *testing.T) {
		contractID := "abc"
		req := &salespb.SignMoegoPayContractRequest{
			Id:           contractID,
			SignatureUri: "https://example.com/signature.png",
		}

		signedAt := time.Now()
		contract := &salesrepo.Contract{
			ID:       contractID,
			SignedAt: &signedAt,
		}

		mockContract.EXPECT().Get(gomock.Any(), contractID).Return(contract, nil)

		result, err := logic.SignMoegoPayContract(context.Background(), req)
		assert.Error(t, err)
		assert.Nil(t, result)
	})
}
