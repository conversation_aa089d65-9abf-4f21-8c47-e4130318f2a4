load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "sales",
    srcs = [
        "annual_contract_logic.go",
        "const.go",
        "moego_pay_contract_logic.go",
        "moego_pay_custom_fee_approval_logic.go",
        "sales_logic.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/sales/logic/sales",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/sales/config",
        "//backend/app/sales/repo/account",
        "//backend/app/sales/repo/organization",
        "//backend/app/sales/repo/payment",
        "//backend/app/sales/repo/sales",
        "//backend/app/sales/repo/salesforce",
        "//backend/app/sales/repo/slack",
        "//backend/app/sales/utils",
        "//backend/common/rpc/framework/log",
        "//backend/proto/sales/v1:sales",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@com_github_shopspring_decimal//:decimal",
        "@com_github_slack_go_slack//:slack",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "sales_test",
    srcs = [
        "annual_contract_logic_test.go",
        "moego_pay_contract_logic_test.go",
        "moego_pay_custom_fee_approval_logic_test.go",
        "sales_logic_test.go",
    ],
    deps = [
        ":sales",
        "//backend/app/sales/repo/mock/account",
        "//backend/app/sales/repo/mock/organization",
        "//backend/app/sales/repo/mock/payment",
        "//backend/app/sales/repo/mock/sales",
        "//backend/app/sales/repo/mock/salesforce",
        "//backend/app/sales/repo/mock/slack",
        "//backend/app/sales/repo/payment",
        "//backend/app/sales/repo/sales",
        "//backend/app/sales/repo/salesforce",
        "//backend/common/utils/pointer",
        "//backend/proto/sales/v1:sales",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/account/v1:account",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@com_github_shopspring_decimal//:decimal",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_genproto//googleapis/type/money",
        "@org_uber_go_mock//gomock",
    ],
)
