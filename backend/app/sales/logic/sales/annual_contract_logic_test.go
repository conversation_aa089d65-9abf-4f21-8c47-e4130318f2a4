package sales_test

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	accountpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	"github.com/MoeGolibrary/moego/backend/app/sales/logic/sales"
	accountmock "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/account"
	organizationmock "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/organization"
	paymentmock "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/payment"
	salesmock "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/sales"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/payment"
	salesrepo "github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

func TestCreateAnnualContract(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTpl := salesmock.NewMockContractTemplateReadWriter(ctrl)
	mockContract := salesmock.NewMockContractReadWriter(ctrl)
	mockOrg := organizationmock.NewMockOrganization(ctrl)
	mockAccount := accountmock.NewMockAccount(ctrl)
	mockPrice := paymentmock.NewMockPriceConfigClient(ctrl)

	logic := sales.NewAnnualContractLogicByParams(mockTpl, mockContract, mockOrg, mockAccount, mockPrice)

	t.Run("Create Annual Contract", func(t *testing.T) {
		req := &salespb.CreateAnnualContractRequest{
			CompanyId:              123,
			SubscriptionPlan:       salespb.SubscriptionPlan_GROWTH_BOARDING_DAYCARE,
			SubscriptionTermMonths: 12,
			DiscountPercentage:     "10",
			GroomingLocationCount:  2,
			BdLocationCount:        1,
			GroomingVanCount:       1,
			Creator:                "<EMAIL>",
		}

		template := &salesrepo.ContractTemplate{
			ID:       "tpl-xxx",
			Name:     "Annual Contract",
			Template: "Contract content {{.CompanyName}}",
		}

		company := &organizationpb.CompanyModel{
			Id:        123,
			Name:      "test company",
			AccountId: 456,
		}

		companySetting := &organizationpb.CompanyPreferenceSettingModel{
			TimeZone: &organizationpb.TimeZone{
				Name: "Asia/Shanghai",
			},
		}

		// 应当命中 address id = 1 的记录
		locations := []*organizationpb.LocationBriefView{
			{
				Id: 2,
				Address: &organizationpb.AddressDef{
					Address1: pointer.Get("Address1"),
				},
			},
			{
				Id: 1,
				Address: &organizationpb.AddressDef{
					Address1: pointer.Get("Address1"),
					Address2: pointer.Get("Address2"),
					City:     pointer.Get("City"),
					State:    pointer.Get("State"),
					Country:  pointer.Get("Country"),
					Zipcode:  pointer.Get("Zipcode"),
				},
			},
		}

		account := &accountpb.AccountModel{
			Id:        456,
			FirstName: "First",
			LastName:  "Last",
			Email:     "<EMAIL>",
		}

		price := &payment.Price{
			GroomingLocation: decimal.NewFromFloat(100.00),
			BDLocation:       decimal.NewFromFloat(150.00),
			Van:              decimal.NewFromFloat(200.00),
		}

		mockTpl.EXPECT().GetNewestTemplate(gomock.Any(), sales.AnnualContractName).Return(template, nil)
		mockOrg.EXPECT().GetCompany(gomock.Any(), int64(123)).Return(company, nil)
		mockOrg.EXPECT().GetCompanySetting(gomock.Any(), int64(123)).Return(companySetting, nil)
		mockOrg.EXPECT().GetLocations(gomock.Any(), int64(123)).Return(locations, nil)
		mockAccount.EXPECT().GetAccount(gomock.Any(), int64(456)).Return(account, nil)
		mockPrice.EXPECT().GetPrice(gomock.Any(), salespb.SubscriptionPlan_GROWTH_BOARDING_DAYCARE).Return(price, nil)
		mockContract.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)

		result, err := logic.CreateAnnualContract(context.Background(), req)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, req.CompanyId, result.Metadata.CompanyId)
		assert.Equal(t, account.Id, result.Metadata.AccountId)
		assert.Equal(t, account.Email, result.Parameters.OwnerEmail)
		assert.Equal(t, company.Name, result.Parameters.CompanyName)
		assert.Equal(t, "Address1, Address2, City, State, Country, Zipcode", result.Parameters.Address)
		assert.Equal(t, fmt.Sprintf("%s %s", account.FirstName, account.LastName), result.Parameters.OwnerName)
		assert.Equal(t, req.SubscriptionTermMonths, result.Parameters.SubscriptionTermMonths)
		assert.Equal(t, req.DiscountPercentage, result.Parameters.DiscountPercentage)
		assert.Equal(t, req.Creator, result.Creator)
		assert.Equal(t, template.ID, result.TemplateId)
		assert.Contains(t, result.Content, company.Name)
		assert.Nil(t, result.SignTime)
		assert.Nil(t, result.SignatureUri)

		// assert line items
		assert.NotNil(t, result.Parameters.GroomingLocation)
		assert.Equal(t, req.GroomingLocationCount, result.Parameters.GroomingLocation.Quantity)
		assert.Equal(t, "100", result.Parameters.GroomingLocation.UnitPrice)
		assert.Equal(t, "90.00", result.Parameters.GroomingLocation.DiscountedUnitPrice)

		assert.NotNil(t, result.Parameters.BdLocation)
		assert.Equal(t, req.BdLocationCount, result.Parameters.BdLocation.Quantity)
		assert.Equal(t, "150", result.Parameters.BdLocation.UnitPrice)
		assert.Equal(t, "135.00", result.Parameters.BdLocation.DiscountedUnitPrice)

		assert.NotNil(t, result.Parameters.GroomingVan)
		assert.Equal(t, req.GroomingVanCount, result.Parameters.GroomingVan.Quantity)
		assert.Equal(t, "200", result.Parameters.GroomingVan.UnitPrice)
		assert.Equal(t, "180.00", result.Parameters.GroomingVan.DiscountedUnitPrice)

		// assert total amount
		// (2 * 90) + (1 * 135) + (1 * 180) = 180 + 135 + 180 = 495
		assert.Equal(t, "495.00", result.Parameters.TotalAmount)
	})
}

func TestDeleteAnnualContract(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTpl := salesmock.NewMockContractTemplateReadWriter(ctrl)
	mockContract := salesmock.NewMockContractReadWriter(ctrl)
	mockOrg := organizationmock.NewMockOrganization(ctrl)
	mockAccount := accountmock.NewMockAccount(ctrl)
	mockPrice := paymentmock.NewMockPriceConfigClient(ctrl)

	logic := sales.NewAnnualContractLogicByParams(mockTpl, mockContract, mockOrg, mockAccount, mockPrice)

	t.Run("Delete Unsigned Annual Contract", func(t *testing.T) {
		contractID := "abc"
		contract := &salesrepo.Contract{
			ID:       contractID,
			SignedAt: nil,
		}

		mockContract.EXPECT().Get(gomock.Any(), contractID).Return(contract, nil)
		mockContract.EXPECT().Delete(gomock.Any(), contractID).Return(nil)

		err := logic.DeleteAnnualContract(context.Background(), contractID)
		assert.NoError(t, err)
	})

	t.Run("Unable to delete signed Annual Contract", func(t *testing.T) {
		contractID := "abc"
		signedAt := time.Now()
		contract := &salesrepo.Contract{
			ID:       contractID,
			SignedAt: &signedAt,
		}

		mockContract.EXPECT().Get(gomock.Any(), contractID).Return(contract, nil)

		err := logic.DeleteAnnualContract(context.Background(), contractID)
		assert.Error(t, err)
	})
}

func TestGetAnnualContract(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTpl := salesmock.NewMockContractTemplateReadWriter(ctrl)
	mockContract := salesmock.NewMockContractReadWriter(ctrl)
	mockOrg := organizationmock.NewMockOrganization(ctrl)
	mockAccount := accountmock.NewMockAccount(ctrl)
	mockPrice := paymentmock.NewMockPriceConfigClient(ctrl)

	logic := sales.NewAnnualContractLogicByParams(mockTpl, mockContract, mockOrg, mockAccount, mockPrice)

	t.Run("Get Annual Contract", func(t *testing.T) {
		contractID := "abc"
		contract := &salesrepo.Contract{
			ID:         contractID,
			Creator:    "<EMAIL>",
			TemplateID: "tpl-xxx",
			Content:    "Contract content test company",
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		}

		mockContract.EXPECT().Get(gomock.Any(), contractID).Return(contract, nil)

		result, err := logic.GetAnnualContract(context.Background(), contractID)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, contractID, result.Id)
		assert.Equal(t, contract.Creator, result.Creator)
	})
}

func TestListAnnualContracts(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTpl := salesmock.NewMockContractTemplateReadWriter(ctrl)
	mockContract := salesmock.NewMockContractReadWriter(ctrl)
	mockOrg := organizationmock.NewMockOrganization(ctrl)
	mockAccount := accountmock.NewMockAccount(ctrl)
	mockPrice := paymentmock.NewMockPriceConfigClient(ctrl)

	logic := sales.NewAnnualContractLogicByParams(mockTpl, mockContract, mockOrg, mockAccount, mockPrice)

	templates := []*salesrepo.ContractTemplate{
		{ID: "template1"},
		{ID: "template2"},
	}

	now := time.Now()
	nowAddHour := now.Add(time.Hour)
	contracts := []*salesrepo.Contract{
		{
			ID:         "contract1",
			Creator:    "<EMAIL>",
			TemplateID: "template1",
			Content:    "content1",
			CreatedAt:  now,
			UpdatedAt:  now,
		},
		{
			ID:           "contract2",
			Creator:      "<EMAIL>",
			TemplateID:   "template1",
			Content:      "content2",
			CreatedAt:    now,
			UpdatedAt:    now,
			SignedAt:     &nowAddHour,
			SignatureURI: pointer.Get("xxxx"),
		},
	}

	t.Run("List Annual Contracts", func(t *testing.T) {
		req := &salespb.ListAnnualContractsRequest{
			PageSize:  10,
			PageToken: "1",
			Filters:   &salespb.AnnualContractQueryFilters{},
		}

		mockTpl.EXPECT().List(gomock.Any(), gomock.Any()).Return(templates, nil)
		mockContract.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(contracts, nil)

		result, err := logic.ListAnnualContracts(context.Background(), req)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.AnnualContracts, 2)
		assert.Equal(t, result.NextPageToken, "")
	})

	t.Run("List Annual Contracts with multi pages", func(t *testing.T) {
		req := &salespb.ListAnnualContractsRequest{
			PageSize:  1,
			PageToken: "1",
		}

		mockTpl.EXPECT().List(gomock.Any(), gomock.Any()).Return(templates, nil)
		mockContract.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(contracts[:1], nil)

		result, err := logic.ListAnnualContracts(context.Background(), req)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.AnnualContracts, 1)
		assert.Equal(t, "2", result.NextPageToken)
	})
}

func TestCountAnnualContracts(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTpl := salesmock.NewMockContractTemplateReadWriter(ctrl)
	mockContract := salesmock.NewMockContractReadWriter(ctrl)
	mockOrg := organizationmock.NewMockOrganization(ctrl)
	mockAccount := accountmock.NewMockAccount(ctrl)
	mockPrice := paymentmock.NewMockPriceConfigClient(ctrl)

	logic := sales.NewAnnualContractLogicByParams(mockTpl, mockContract, mockOrg, mockAccount, mockPrice)

	t.Run("Count Annual Contracts", func(t *testing.T) {
		req := &salespb.CountAnnualContractsRequest{
			Filters: &salespb.AnnualContractQueryFilters{},
		}

		count := int64(5)
		templates := []*salesrepo.ContractTemplate{
			{ID: "template1"},
			{ID: "template2"},
		}

		mockTpl.EXPECT().List(gomock.Any(), gomock.Any()).Return(templates, nil)
		mockContract.EXPECT().Count(gomock.Any(), gomock.Any()).Return(count, nil)

		c, err := logic.CountAnnualContracts(context.Background(), req)
		assert.NoError(t, err)
		assert.Equal(t, count, c)
	})

	t.Run("Count Annual Contracts failed", func(t *testing.T) {
		req := &salespb.CountAnnualContractsRequest{
			Filters: &salespb.AnnualContractQueryFilters{},
		}

		mockTpl.EXPECT().List(gomock.Any(), gomock.Any()).Return(nil, errors.New("error"))
		c, err := logic.CountAnnualContracts(context.Background(), req)
		assert.Error(t, err)
		assert.Equal(t, int64(0), c)
	})
}

func TestSignAnnualContract(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTpl := salesmock.NewMockContractTemplateReadWriter(ctrl)
	mockContract := salesmock.NewMockContractReadWriter(ctrl)
	mockOrg := organizationmock.NewMockOrganization(ctrl)
	mockAccount := accountmock.NewMockAccount(ctrl)
	mockPrice := paymentmock.NewMockPriceConfigClient(ctrl)

	logic := sales.NewAnnualContractLogicByParams(mockTpl, mockContract, mockOrg, mockAccount, mockPrice)

	t.Run("Sign Annual Contract", func(t *testing.T) {
		contractID := "abc"
		templateID := "tpl-xxx"
		signatureURI := "https://example.com/signature.png"
		req := &salespb.SignAnnualContractRequest{
			Id:           contractID,
			SignatureUri: signatureURI,
		}

		contract := &salesrepo.Contract{
			ID:         contractID,
			TemplateID: templateID,
			SignedAt:   nil,
		}

		template := &salesrepo.ContractTemplate{
			ID:       templateID,
			Template: "Contract content test company",
		}

		mockContract.EXPECT().Get(gomock.Any(), contractID).Return(contract, nil)
		mockTpl.EXPECT().Get(gomock.Any(), templateID).Return(template, nil)
		mockContract.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)

		result, err := logic.SignAnnualContract(context.Background(), req)
		assert.NoError(t, err)
		assert.NotNil(t, result)
	})

	t.Run("Unable to sign signed Annual Contract", func(t *testing.T) {
		contractID := "abc"
		req := &salespb.SignAnnualContractRequest{
			Id:           contractID,
			SignatureUri: "https://example.com/signature.png",
		}

		signedAt := time.Now()
		contract := &salesrepo.Contract{
			ID:       contractID,
			SignedAt: &signedAt,
		}

		mockContract.EXPECT().Get(gomock.Any(), contractID).Return(contract, nil)

		result, err := logic.SignAnnualContract(context.Background(), req)
		assert.Error(t, err)
		assert.Nil(t, result)
	})
}
