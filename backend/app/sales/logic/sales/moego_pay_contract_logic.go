package sales

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/sales/repo/account"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/organization"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	salesutils "github.com/MoeGolibrary/moego/backend/app/sales/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

type MoegoPayContractRenderParams struct {
	CompanyName           string
	OwnerName             string
	OwnerEmail            string
	TerminalPercentage    string
	TerminalFixed         string
	NonTerminalPercentage string
	NonTerminalFixed      string
	MinVolume             string
	SignatureURI          string
	SignTime              string
}

type MoegoPayContractMetadata struct {
	CompanyID int64
	AccountID int64
	Timezone  string
}

type MoegoPayContractLogic struct {
	tpl sales.ContractTemplateReadWriter
	c   sales.ContractReadWriter
	org organization.Organization
	a   account.Account
}

func NewMoegoPayContractLogic() *MoegoPayContractLogic {
	return &MoegoPayContractLogic{
		tpl: sales.NewContractTemplateRW(),
		c:   sales.NewContractRW(),
		org: organization.New(),
		a:   account.New(),
	}
}

func NewMoegoPayContractLogicByParams(tpl sales.ContractTemplateReadWriter, c sales.ContractReadWriter,
	org organization.Organization, a account.Account) *MoegoPayContractLogic {
	return &MoegoPayContractLogic{tpl, c, org, a}
}

func (l *MoegoPayContractLogic) CreateMoegoPayContract(ctx context.Context,
	req *salespb.CreateMoegoPayContractRequest) (*salespb.MoegoPayContract, error) {
	// get moego pay contract template
	tpl, err := l.GetNewestTemplate(ctx)
	if err != nil {
		return nil, err
	}

	// prepare params and metadata
	params, md, err := l.buildParamsAndMetadata(ctx, req)
	if err != nil {
		return nil, err
	}

	// render contract
	content, err := tpl.Render(params)
	if err != nil {
		return nil, err
	}

	// save contract
	contract := &sales.Contract{
		ID:         salesutils.NewUUID(),
		Creator:    req.Creator,
		TemplateID: tpl.ID,
		Metadata:   salesutils.ToJSONB(md),
		Parameters: salesutils.ToJSONB(params),
		Content:    content,
	}
	if err := l.c.Save(ctx, contract); err != nil {
		return nil, err
	}

	return buildMoegoPayContract(contract)
}

func (l *MoegoPayContractLogic) DeleteMoegoPayContract(ctx context.Context, id string) error {
	contract, err := l.c.Get(ctx, id)
	if err != nil {
		return err
	}

	if contract.SignedAt != nil {
		return fmt.Errorf("failed to delete because the contract has been signed")
	}

	return l.c.Delete(ctx, id)
}

func (l *MoegoPayContractLogic) GetMoegoPayContract(ctx context.Context, id string) (*salespb.MoegoPayContract, error) {
	contract, err := l.c.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return buildMoegoPayContract(contract)
}

func (l *MoegoPayContractLogic) ListMoegoPayContracts(ctx context.Context, req *salespb.ListMoegoPayContractsRequest) (
	*salespb.ListMoegoPayContractsResponse, error) {
	filters, err := l.buildQueryFilters(ctx, req.GetFilters())
	if err != nil {
		return nil, err
	}

	page, err := sales.NewOffsetLimitPageFromPageToken[*sales.Contract](req.PageSize, req.PageToken)
	if err != nil {
		return nil, err
	}

	items, err := l.c.List(ctx, page, filters...)
	if err != nil {
		return nil, err
	}

	nextPageToken := ""
	if nextPage := page.NextPage(items); nextPage != nil {
		nextPageToken = nextPage.Token()
	}

	contracts := make([]*salespb.MoegoPayContract, 0, len(items))
	for _, i := range items {
		contract, err := buildMoegoPayContract(i)
		if err != nil {
			return nil, err
		}
		contracts = append(contracts, contract)
	}

	return &salespb.ListMoegoPayContractsResponse{
		MoegoPayContracts: contracts,
		NextPageToken:     nextPageToken,
	}, nil
}

func (l *MoegoPayContractLogic) CountMoegoPayContracts(ctx context.Context,
	req *salespb.CountMoegoPayContractsRequest) (int64, error) {
	filters, err := l.buildQueryFilters(ctx, req.GetFilters())
	if err != nil {
		return 0, err
	}

	return l.c.Count(ctx, filters...)
}

func (l *MoegoPayContractLogic) SignMoegoPayContract(ctx context.Context, req *salespb.SignMoegoPayContractRequest) (
	*salespb.MoegoPayContract, error) {
	// get contract
	contract, err := l.c.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// check if the contract has benn signed
	if contract.SignedAt != nil {
		return nil, fmt.Errorf("contract %s has been signed", req.Id)
	}

	// get template
	tpl, err := l.tpl.Get(ctx, contract.TemplateID)
	if err != nil {
		return nil, err
	}

	// 重新生成 render params, 包含签名和日期
	params := &MoegoPayContractRenderParams{}
	if err := contract.UnmarshalParameters(params); err != nil {
		return nil, err
	}
	metadata := &MoegoPayContractMetadata{}
	if err := contract.UnmarshalMetadata(metadata); err != nil {
		return nil, err
	}

	now := time.Now()
	params.SignTime = GetFormatedDate(ctx, &now, metadata.Timezone)
	params.SignatureURI = req.SignatureUri

	// 重新渲染合同
	content, err := tpl.Render(params)
	if err != nil {
		return nil, err
	}

	contract.Parameters = salesutils.ToJSONB(params)
	contract.Content = content
	contract.SignedAt = &now
	contract.SignatureURI = &req.SignatureUri

	if err := l.c.Save(ctx, contract); err != nil {
		return nil, err
	}

	return buildMoegoPayContract(contract)
}

func (l *MoegoPayContractLogic) GetNewestTemplate(ctx context.Context) (*sales.ContractTemplate, error) {
	template, err := l.tpl.GetNewestTemplate(ctx, MoegoPayContractName)
	if err != nil {
		return nil, err
	}

	return template, nil
}

func (l *MoegoPayContractLogic) buildQueryFilters(ctx context.Context, filters *salespb.MoegoPayContractQueryFilters) (
	[]sales.ContractQueryFilter, error) {
	// list all template ids as filter
	tpls, err := l.tpl.List(ctx, sales.WithContractTemplateName(MoegoPayContractName))
	if err != nil {
		return nil, err
	}
	tplIDs := make([]string, 0, len(tpls))
	for _, i := range tpls {
		tplIDs = append(tplIDs, i.ID)
	}

	c := sales.Contract{}
	result := []sales.ContractQueryFilter{
		c.QueryWithTemplateIDs(tplIDs),
	}

	if filters != nil {
		result = append(result,
			c.QueryWithMetadata("CompanyID", salesutils.Int64ToString(filters.CompanyId), false),
			c.QueryWithMetadata("AccountID", salesutils.Int64ToString(filters.AccountId), false),
			c.QueryWithParameter("OwnerEmail", filters.OwnerEmail, true),
			c.QueryWithCreator(filters.Creator),
			c.QueryWithSigned(filters.Signed))
	}

	return result, nil
}

func (l *MoegoPayContractLogic) buildParamsAndMetadata(ctx context.Context,
	req *salespb.CreateMoegoPayContractRequest) (*MoegoPayContractRenderParams, *MoegoPayContractMetadata, error) {
	company, err := l.org.GetCompany(ctx, req.CompanyId)
	if err != nil {
		return nil, nil, err
	}

	setting, err := l.org.GetCompanySetting(ctx, company.Id)
	if err != nil {
		return nil, nil, err
	}

	acc, err := l.a.GetAccount(ctx, company.AccountId)
	if err != nil {
		return nil, nil, err
	}

	return &MoegoPayContractRenderParams{
			CompanyName:           company.Name,
			OwnerName:             fmt.Sprintf("%s %s", acc.FirstName, acc.LastName),
			OwnerEmail:            acc.Email,
			TerminalPercentage:    req.TerminalPercentage,
			TerminalFixed:         req.TerminalFixed,
			NonTerminalPercentage: req.NonTerminalPercentage,
			NonTerminalFixed:      req.NonTerminalFixed,
			MinVolume:             req.MinVolume,
		}, &MoegoPayContractMetadata{
			CompanyID: company.Id,
			AccountID: acc.Id,
			Timezone:  setting.GetTimeZone().GetName(),
		}, nil
}

func buildMoegoPayContract(c *sales.Contract) (*salespb.MoegoPayContract, error) {
	p := &MoegoPayContractRenderParams{}
	if err := c.UnmarshalParameters(p); err != nil {
		return nil, err
	}

	md := &MoegoPayContractMetadata{}
	if err := c.UnmarshalMetadata(md); err != nil {
		return nil, err
	}

	metadata := &salespb.MoegoPayContract_Metadata{
		CompanyId: md.CompanyID,
		AccountId: md.AccountID,
	}
	parameters := &salespb.MoegoPayContract_Parameters{
		CompanyName:           p.CompanyName,
		OwnerName:             p.OwnerName,
		OwnerEmail:            p.OwnerEmail,
		TerminalPercentage:    p.TerminalPercentage,
		TerminalFixed:         p.TerminalFixed,
		NonTerminalPercentage: p.NonTerminalPercentage,
		NonTerminalFixed:      p.NonTerminalFixed,
		MinVolume:             p.MinVolume,
	}

	var signTime *timestamppb.Timestamp
	if c.SignedAt != nil {
		signTime = timestamppb.New(*c.SignedAt)
	} else {
		signTime = nil
	}

	return &salespb.MoegoPayContract{
		Id:           c.ID,
		TemplateId:   c.TemplateID,
		Metadata:     metadata,
		Parameters:   parameters,
		Content:      c.Content,
		Creator:      c.Creator,
		CreateTime:   timestamppb.New(c.CreatedAt),
		UpdateTime:   timestamppb.New(c.UpdatedAt),
		SignTime:     signTime,
		SignatureUri: c.SignatureURI,
	}, nil
}

func GetFormatedDate(ctx context.Context, t *time.Time, timezone string) string {
	loc, err := time.LoadLocation(timezone)
	// 时区解析失败，默认用 UTC
	if err != nil {
		log.ErrorContextf(ctx, "load location %s failed: %v", timezone, err)
		loc = time.UTC
	}
	now := t.In(loc)

	return now.Format("January 2, 2006, 03:04PM")
}
