package sales

import (
	"context"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/types/known/timestamppb"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/account"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/organization"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/payment"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	salesutils "github.com/MoeGolibrary/moego/backend/app/sales/utils"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

type ProductLineItem struct {
	UnitPrice           decimal.Decimal
	DiscountedUnitPrice decimal.Decimal
	Quantity            int32
}

type AnnualContractRenderParams struct {
	CompanyName            string
	OwnerName              string
	OwnerEmail             string
	Address                string
	SubscriptionPlanName   string
	SubscriptionTermMonths int32
	BDLocation             *ProductLineItem
	GroomingLocation       *ProductLineItem
	GroomingVan            *ProductLineItem
	DiscountPercentage     string
	TotalAmount            string
	SignatureURI           string
	SignTime               string
}

type AnnualContractMetadata struct {
	CompanyID        int64
	AccountID        int64
	Timezone         string
	SubscriptionPlan string
}

type AnnualContractLogic struct {
	tpl sales.ContractTemplateReadWriter
	c   sales.ContractReadWriter
	org organization.Organization
	a   account.Account
	p   payment.PriceConfigClient
}

func NewAnnualContractLogic() *AnnualContractLogic {
	return &AnnualContractLogic{
		tpl: sales.NewContractTemplateRW(),
		c:   sales.NewContractRW(),
		org: organization.New(),
		a:   account.New(),
		p:   payment.NewPriceConfigClient(),
	}
}

func NewAnnualContractLogicByParams(tpl sales.ContractTemplateReadWriter, c sales.ContractReadWriter,
	org organization.Organization, a account.Account, p payment.PriceConfigClient) *AnnualContractLogic {
	return &AnnualContractLogic{tpl, c, org, a, p}
}

func (l *AnnualContractLogic) CreateAnnualContract(ctx context.Context, req *salespb.CreateAnnualContractRequest) (
	*salespb.AnnualContract, error) {
	// get annual contract template
	tpl, err := l.GetNewestTemplate(ctx)
	if err != nil {
		return nil, err
	}

	// prepare params and metadata
	params, md, err := l.buildParamsAndMetadata(ctx, req)
	if err != nil {
		return nil, err
	}

	// render contract
	content, err := tpl.Render(params)
	if err != nil {
		return nil, err
	}

	// save contract
	contract := &sales.Contract{
		ID:         salesutils.NewUUID(),
		Creator:    req.Creator,
		TemplateID: tpl.ID,
		Metadata:   salesutils.ToJSONB(md),
		Parameters: salesutils.ToJSONB(params),
		Content:    content,
	}
	if err := l.c.Save(ctx, contract); err != nil {
		return nil, err
	}

	return buildAnnualContract(contract)
}

func (l *AnnualContractLogic) DeleteAnnualContract(ctx context.Context, id string) error {
	contract, err := l.c.Get(ctx, id)
	if err != nil {
		return err
	}

	if contract.SignedAt != nil {
		return fmt.Errorf("failed to delete because the contract has been signed")
	}

	return l.c.Delete(ctx, id)
}

func (l *AnnualContractLogic) GetAnnualContract(ctx context.Context, id string) (*salespb.AnnualContract, error) {
	contract, err := l.c.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return buildAnnualContract(contract)
}

func (l *AnnualContractLogic) ListAnnualContracts(ctx context.Context, req *salespb.ListAnnualContractsRequest) (
	*salespb.ListAnnualContractsResponse, error) {
	filters, err := l.buildQueryFilters(ctx, req.GetFilters())
	if err != nil {
		return nil, err
	}

	page, err := sales.NewOffsetLimitPageFromPageToken[*sales.Contract](req.PageSize, req.PageToken)
	if err != nil {
		return nil, err
	}

	items, err := l.c.List(ctx, page, filters...)
	if err != nil {
		return nil, err
	}

	nextPageToken := ""
	if nextPage := page.NextPage(items); nextPage != nil {
		nextPageToken = nextPage.Token()
	}

	contracts := make([]*salespb.AnnualContract, 0, len(items))
	for _, i := range items {
		contract, err := buildAnnualContract(i)
		if err != nil {
			return nil, err
		}
		contracts = append(contracts, contract)
	}

	return &salespb.ListAnnualContractsResponse{
		AnnualContracts: contracts,
		NextPageToken:   nextPageToken,
	}, nil
}

func (l *AnnualContractLogic) CountAnnualContracts(ctx context.Context, req *salespb.CountAnnualContractsRequest) (
	int64, error) {
	filters, err := l.buildQueryFilters(ctx, req.GetFilters())
	if err != nil {
		return 0, err
	}

	return l.c.Count(ctx, filters...)
}

func (l *AnnualContractLogic) SignAnnualContract(ctx context.Context, req *salespb.SignAnnualContractRequest) (
	*salespb.AnnualContract, error) {
	// get contract
	contract, err := l.c.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// check if the contract has benn signed
	if contract.SignedAt != nil {
		return nil, fmt.Errorf("contract %s has been signed", req.Id)
	}

	// get template
	tpl, err := l.tpl.Get(ctx, contract.TemplateID)
	if err != nil {
		return nil, err
	}

	// 重新生成 render params, 包含签名和日期
	params := &AnnualContractRenderParams{}
	if err := contract.UnmarshalParameters(params); err != nil {
		return nil, err
	}
	metadata := &AnnualContractMetadata{}
	if err := contract.UnmarshalMetadata(metadata); err != nil {
		return nil, err
	}

	now := time.Now()
	params.SignTime = GetFormatedDate(ctx, &now, metadata.Timezone)
	params.SignatureURI = req.SignatureUri

	// 重新渲染合同
	content, err := tpl.Render(params)
	if err != nil {
		return nil, err
	}

	contract.Parameters = salesutils.ToJSONB(params)
	contract.Content = content
	contract.SignedAt = &now
	contract.SignatureURI = &req.SignatureUri

	if err := l.c.Save(ctx, contract); err != nil {
		return nil, err
	}

	return buildAnnualContract(contract)
}

func (l *AnnualContractLogic) GetNewestTemplate(ctx context.Context) (*sales.ContractTemplate, error) {
	template, err := l.tpl.GetNewestTemplate(ctx, AnnualContractName)
	if err != nil {
		return nil, err
	}

	return template, nil
}

func (l *AnnualContractLogic) buildQueryFilters(ctx context.Context, filters *salespb.AnnualContractQueryFilters) (
	[]sales.ContractQueryFilter, error) {
	// list all template ids as filter
	tpls, err := l.tpl.List(ctx, sales.WithContractTemplateName(AnnualContractName))
	if err != nil {
		return nil, err
	}
	tplIDs := make([]string, 0, len(tpls))
	for _, i := range tpls {
		tplIDs = append(tplIDs, i.ID)
	}

	c := sales.Contract{}
	result := []sales.ContractQueryFilter{
		c.QueryWithTemplateIDs(tplIDs),
	}

	if filters != nil {
		result = append(result,
			c.QueryWithMetadata("CompanyID", salesutils.Int64ToString(filters.CompanyId), false),
			c.QueryWithMetadata("AccountID", salesutils.Int64ToString(filters.AccountId), false),
			c.QueryWithParameter("OwnerEmail", filters.OwnerEmail, true),
			c.QueryWithCreator(filters.Creator),
			c.QueryWithSigned(filters.Signed))
	}

	return result, nil
}

func (l *AnnualContractLogic) buildParamsAndMetadata(ctx context.Context,
	req *salespb.CreateAnnualContractRequest) (*AnnualContractRenderParams, *AnnualContractMetadata, error) {
	company, err := l.org.GetCompany(ctx, req.CompanyId)
	if err != nil {
		return nil, nil, err
	}

	setting, err := l.org.GetCompanySetting(ctx, company.Id)
	if err != nil {
		return nil, nil, err
	}

	locations, err := l.org.GetLocations(ctx, company.Id)
	if err != nil {
		return nil, nil, err
	}
	address := getFullAddress(getFirstLocation(locations))

	acc, err := l.a.GetAccount(ctx, company.AccountId)
	if err != nil {
		return nil, nil, err
	}

	price, err := l.p.GetPrice(ctx, req.SubscriptionPlan)
	if err != nil {
		return nil, nil, err
	}

	grooming, bd, van, totalAmount, discount := getProductLineItems(price, req)

	return &AnnualContractRenderParams{
			CompanyName:            company.Name,
			OwnerName:              fmt.Sprintf("%s %s", acc.FirstName, acc.LastName),
			OwnerEmail:             acc.Email,
			Address:                address,
			SubscriptionPlanName:   getSubscriptionPlanName(req.SubscriptionPlan),
			SubscriptionTermMonths: req.SubscriptionTermMonths,
			BDLocation:             bd,
			GroomingLocation:       grooming,
			GroomingVan:            van,
			DiscountPercentage:     discount.String(),
			TotalAmount:            totalAmount.StringFixed(2),
		}, &AnnualContractMetadata{
			CompanyID:        company.Id,
			AccountID:        acc.Id,
			Timezone:         setting.GetTimeZone().GetName(),
			SubscriptionPlan: req.SubscriptionPlan.String(),
		}, nil
}

func getDiscountedPrice(price decimal.Decimal, discountPercentage decimal.Decimal) decimal.Decimal {
	// (100 - discountPercentage) / 100
	m := decimal.NewFromInt(100).Sub(discountPercentage).Shift(-2)

	return price.Mul(m)
}

func getProductLineItems(price *payment.Price, req *salespb.CreateAnnualContractRequest) (
	grooming, bd, van *ProductLineItem, totalAmount, discount decimal.Decimal) {

	totalAmount = decimal.Zero
	discount = decimal.Zero
	if d, err := decimal.NewFromString(req.DiscountPercentage); err == nil {
		discount = d
	}

	if req.BdLocationCount > 0 {
		bd = &ProductLineItem{
			UnitPrice:           price.BDLocation,
			DiscountedUnitPrice: getDiscountedPrice(price.BDLocation, discount),
			Quantity:            req.BdLocationCount,
		}
		totalAmount = totalAmount.Add(bd.DiscountedUnitPrice.Mul(decimal.NewFromInt32(bd.Quantity)))
	}

	if req.GroomingLocationCount > 0 {
		grooming = &ProductLineItem{
			UnitPrice:           price.GroomingLocation,
			DiscountedUnitPrice: getDiscountedPrice(price.GroomingLocation, discount),
			Quantity:            req.GroomingLocationCount,
		}
		totalAmount = totalAmount.Add(grooming.DiscountedUnitPrice.Mul(decimal.NewFromInt32(grooming.Quantity)))
	}

	if req.GroomingVanCount > 0 {
		van = &ProductLineItem{
			UnitPrice:           price.Van,
			DiscountedUnitPrice: getDiscountedPrice(price.Van, discount),
			Quantity:            req.GroomingVanCount,
		}
		totalAmount = totalAmount.Add(van.DiscountedUnitPrice.Mul(decimal.NewFromInt32(van.Quantity)))
	}

	return
}

func getSubscriptionPlanName(plan salespb.SubscriptionPlan) string {
	switch plan {
	case salespb.SubscriptionPlan_GROWTH_GROOMING:
		return "Growth"
	case salespb.SubscriptionPlan_ULTIMATE_GROOMING:
		return "Ultimate"
	case salespb.SubscriptionPlan_GROWTH_BOARDING_DAYCARE:
		return "Growth - Multi Service"
	case salespb.SubscriptionPlan_ULTIMATE_BOARDING_DAYCARE:
		return "Ultimate - Multi Service"
	case salespb.SubscriptionPlan_ENTERPRISE_BOARDING_DAYCARE:
		return "Enterprise - Multi Service"
	}

	return ""
}

func getFullAddress(location *organizationpb.LocationBriefView) string {
	if location == nil || location.GetAddress() == nil {
		return ""
	}

	parts := []string{
		location.GetAddress().GetAddress1(),
		location.GetAddress().GetAddress2(),
		location.GetAddress().GetCity(),
		location.GetAddress().GetState(),
		location.GetAddress().GetCountry(),
		location.GetAddress().GetZipcode(),
	}

	// remove empty strings
	parts = slices.DeleteFunc(parts, func(s string) bool {
		return s == ""
	})

	// separated by comma and space
	return strings.Join(parts, ", ")
}

func getFirstLocation(locations []*organizationpb.LocationBriefView) *organizationpb.LocationBriefView {
	if len(locations) == 0 {
		return nil
	}
	// location with the smallest id is the first location
	location := locations[0]
	for _, l := range locations {
		if l.Id < location.Id {
			location = l
		}
	}

	return location
}

func buildAnnualContract(c *sales.Contract) (*salespb.AnnualContract, error) {
	p := &AnnualContractRenderParams{}
	if err := c.UnmarshalParameters(p); err != nil {
		return nil, err
	}

	md := &AnnualContractMetadata{}
	if err := c.UnmarshalMetadata(md); err != nil {
		return nil, err
	}

	metadata := &salespb.AnnualContract_Metadata{
		CompanyId:        md.CompanyID,
		AccountId:        md.AccountID,
		SubscriptionPlan: salespb.SubscriptionPlan(salespb.SubscriptionPlan_value[md.SubscriptionPlan]),
	}

	parameters := &salespb.AnnualContract_Parameters{
		CompanyName:            p.CompanyName,
		OwnerName:              p.OwnerName,
		OwnerEmail:             p.OwnerEmail,
		Address:                p.Address,
		SubscriptionPlanName:   p.SubscriptionPlanName,
		SubscriptionTermMonths: p.SubscriptionTermMonths,
		DiscountPercentage:     p.DiscountPercentage,
		TotalAmount:            p.TotalAmount,
	}

	if p.GroomingLocation != nil {
		parameters.GroomingLocation = &salespb.AnnualContract_ProductLineItem{
			Quantity:            p.GroomingLocation.Quantity,
			UnitPrice:           p.GroomingLocation.UnitPrice.String(),
			DiscountedUnitPrice: p.GroomingLocation.DiscountedUnitPrice.StringFixed(2),
		}
	}
	if p.BDLocation != nil {
		parameters.BdLocation = &salespb.AnnualContract_ProductLineItem{
			Quantity:            p.BDLocation.Quantity,
			UnitPrice:           p.BDLocation.UnitPrice.String(),
			DiscountedUnitPrice: p.BDLocation.DiscountedUnitPrice.StringFixed(2),
		}
	}
	if p.GroomingVan != nil {
		parameters.GroomingVan = &salespb.AnnualContract_ProductLineItem{
			Quantity:            p.GroomingVan.Quantity,
			UnitPrice:           p.GroomingVan.UnitPrice.String(),
			DiscountedUnitPrice: p.GroomingVan.DiscountedUnitPrice.StringFixed(2),
		}
	}

	var signTime *timestamppb.Timestamp
	if c.SignedAt != nil {
		signTime = timestamppb.New(*c.SignedAt)
	} else {
		signTime = nil
	}

	return &salespb.AnnualContract{
		Id:           c.ID,
		TemplateId:   c.TemplateID,
		Metadata:     metadata,
		Parameters:   parameters,
		Content:      c.Content,
		Creator:      c.Creator,
		CreateTime:   timestamppb.New(c.CreatedAt),
		UpdateTime:   timestamppb.New(c.UpdatedAt),
		SignTime:     signTime,
		SignatureUri: c.SignatureURI,
	}, nil
}
