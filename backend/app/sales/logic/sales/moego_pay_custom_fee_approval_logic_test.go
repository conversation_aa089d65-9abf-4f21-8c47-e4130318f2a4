package sales_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/moego/backend/app/sales/logic/sales"
	salesmock "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/sales"
	slackmock "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/slack"
	salesrepo "github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

func TestCreateMoegoPayCustomFeeApproval(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockRW := salesmock.NewMockMoegoPayCustomFeeApprovalReadWriter(ctrl)
	mockSlackClient := slackmock.NewMockWebhookClient(ctrl)

	logic := sales.NewMoegoPayCustomFeeApprovalLogicByParams(mockRW, nil, "/xxx", mockSlackClient)

	t.Run("Create MoeGo Pay Custom Fee Approval", func(t *testing.T) {
		params := &sales.MoegoPayCustomFeeApprovalCreateParams{
			CompanyID:             123,
			AccountID:             456,
			OwnerEmail:            "<EMAIL>",
			Creator:               "<EMAIL>",
			TerminalPercentage:    "2.30",
			TerminalFixed:         "0.10",
			NonTerminalPercentage: "2.30",
			NonTerminalFixed:      "0.10",
			MinVolume:             "1000.00",
		}

		mockRW.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)
		mockSlackClient.EXPECT().SendMessage(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

		result, err := logic.CreateMoegoPayCustomFeeApproval(context.Background(), params)
		assert.NoError(t, err)
		assert.NotNil(t, result)

		assert.Equal(t, params.CompanyID, result.CompanyId)
		assert.Equal(t, params.AccountID, result.AccountId)
		assert.Equal(t, params.OwnerEmail, result.OwnerEmail)
		assert.Equal(t, params.Creator, result.Creator)
		assert.Equal(t, params.TerminalPercentage, result.TerminalPercentage)
		assert.Equal(t, params.TerminalFixed, result.TerminalFixed)
		assert.Equal(t, params.NonTerminalPercentage, result.NonTerminalPercentage)
		assert.Equal(t, params.NonTerminalFixed, result.NonTerminalFixed)
		assert.Equal(t, params.MinVolume, result.MinVolume)
		assert.Equal(t, salespb.MoegoPayCustomFeeApproval_PENDING, result.ApprovalState)
		assert.NotEmpty(t, result.Id)
		assert.NotNil(t, result.CreateTime)
		assert.NotNil(t, result.UpdateTime)
	})

	t.Run("Create MoeGo Pay Custom Fee Approval - Slack Error", func(t *testing.T) {
		params := &sales.MoegoPayCustomFeeApprovalCreateParams{}

		mockRW.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)
		mockSlackClient.EXPECT().SendMessage(gomock.Any(), gomock.Any(), gomock.Any()).Return(assert.AnError)

		// Slack错误不应该影响审批创建
		result, err := logic.CreateMoegoPayCustomFeeApproval(context.Background(), params)
		assert.NoError(t, err)
		assert.NotNil(t, result)
	})
}

func TestListMoegoPayCustomFeeApprovals(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockRW := salesmock.NewMockMoegoPayCustomFeeApprovalReadWriter(ctrl)
	mockSlackClient := slackmock.NewMockWebhookClient(ctrl)

	logic := sales.NewMoegoPayCustomFeeApprovalLogicByParams(mockRW, nil, "/xxx", mockSlackClient)

	now := time.Now()
	approvals := []*salesrepo.MoegoPayCustomFeeApproval{
		{
			ID:            "approval_1",
			ApprovalState: "PENDING",
		},
		{
			ID:            "approval_2",
			ApprovalState: "APPROVED",
			HandledAt:     &now,
		},
	}

	t.Run("List MoeGo Pay custom fee approvals", func(t *testing.T) {
		req := &salespb.ListMoegoPayCustomFeeApprovalsRequest{
			PageSize:  10,
			PageToken: "1",
		}

		mockRW.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(approvals, nil)

		result, err := logic.ListMoegoPayCustomFeeApprovals(context.Background(), req)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.MoegoPayCustomFeeApprovals, 2)
	})

	t.Run("List MoeGo Pay custom fee approvals with multi pages", func(t *testing.T) {
		req := &salespb.ListMoegoPayCustomFeeApprovalsRequest{
			PageSize:  1,
			PageToken: "1",
		}

		mockRW.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(approvals[:1], nil)

		result, err := logic.ListMoegoPayCustomFeeApprovals(context.Background(), req)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.MoegoPayCustomFeeApprovals, 1)
		assert.Equal(t, "2", result.NextPageToken)
	})

	t.Run("List approved MoeGo Pay custom fee approvals", func(t *testing.T) {
		req := &salespb.ListMoegoPayCustomFeeApprovalsRequest{
			PageSize:  10,
			PageToken: "1",
			Filters: &salespb.MoegoPayCustomFeeApprovalQueryFilters{
				ApprovalStates: []salespb.MoegoPayCustomFeeApproval_ApprovalState{
					salespb.MoegoPayCustomFeeApproval_PENDING,
				},
			},
		}

		mockRW.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(approvals[:1], nil)
		result, err := logic.ListMoegoPayCustomFeeApprovals(context.Background(), req)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.MoegoPayCustomFeeApprovals, 1)
		assert.Equal(t, "PENDING", result.MoegoPayCustomFeeApprovals[0].ApprovalState.String())
	})
}

func TestGetMoegoPayCustomFeeApproval(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockRW := salesmock.NewMockMoegoPayCustomFeeApprovalReadWriter(ctrl)
	mockSlackClient := slackmock.NewMockWebhookClient(ctrl)

	logic := sales.NewMoegoPayCustomFeeApprovalLogicByParams(mockRW, nil, "/xxx", mockSlackClient)

	t.Run("Get MoeGo Pay Custom Fee Approval", func(t *testing.T) {
		approvalID := "approval_123"
		approval := &salesrepo.MoegoPayCustomFeeApproval{
			ID:                    approvalID,
			CompanyID:             123,
			AccountID:             456,
			OwnerEmail:            "<EMAIL>",
			TerminalPercentage:    "2.30",
			TerminalFixed:         "0.10",
			NonTerminalPercentage: "2.30",
			NonTerminalFixed:      "0.10",
			MinVolume:             "1000.00",
			Creator:               "<EMAIL>",
			ApprovalState:         salespb.MoegoPayCustomFeeApproval_PENDING.String(),
			CreatedAt:             time.Now(),
			UpdatedAt:             time.Now(),
		}

		req := &salespb.GetMoegoPayCustomFeeApprovalRequest{
			Id: approvalID,
		}

		mockRW.EXPECT().Get(gomock.Any(), approvalID).Return(approval, nil)

		result, err := logic.GetMoegoPayCustomFeeApproval(context.Background(), req)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, approvalID, result.Id)
		assert.Equal(t, approval.CompanyID, result.CompanyId)
		assert.Equal(t, approval.AccountID, result.AccountId)
		assert.Equal(t, approval.OwnerEmail, result.OwnerEmail)
		assert.Equal(t, approval.TerminalPercentage, result.TerminalPercentage)
		assert.Equal(t, approval.TerminalFixed, result.TerminalFixed)
		assert.Equal(t, approval.NonTerminalPercentage, result.NonTerminalPercentage)
		assert.Equal(t, approval.NonTerminalFixed, result.NonTerminalFixed)
		assert.Equal(t, approval.MinVolume, result.MinVolume)
		assert.Equal(t, approval.Creator, result.Creator)
		assert.Equal(t, salespb.MoegoPayCustomFeeApproval_PENDING, result.ApprovalState)
		assert.NotNil(t, result.CreateTime)
		assert.NotNil(t, result.UpdateTime)
	})
}

func TestCountMoegoPayCustomFeeApprovals(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockRW := salesmock.NewMockMoegoPayCustomFeeApprovalReadWriter(ctrl)
	mockSlackClient := slackmock.NewMockWebhookClient(ctrl)

	logic := sales.NewMoegoPayCustomFeeApprovalLogicByParams(mockRW, nil, "/xxx", mockSlackClient)

	t.Run("Count MoeGo Pay Custom Fee Approvals", func(t *testing.T) {
		req := &salespb.CountMoegoPayCustomFeeApprovalsRequest{}

		expectedCount := int64(5)
		mockRW.EXPECT().Count(gomock.Any(), gomock.Any()).Return(expectedCount, nil)

		count, err := logic.CountMoegoPayCustomFeeApprovals(context.Background(), req)
		assert.NoError(t, err)
		assert.Equal(t, expectedCount, count)
	})
}

func TestApproveMoegoPayCustomFeeApproval(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockRW := salesmock.NewMockMoegoPayCustomFeeApprovalReadWriter(ctrl)
	mockSlackClient := slackmock.NewMockWebhookClient(ctrl)

	logic := sales.NewMoegoPayCustomFeeApprovalLogicByParams(mockRW, nil, "/xxx", mockSlackClient)

	t.Run("Approve MoeGo Pay Custom Fee Approval", func(t *testing.T) {
		approvalID := "approval_123"
		approval := &salesrepo.MoegoPayCustomFeeApproval{
			ID:            approvalID,
			ApprovalState: salespb.MoegoPayCustomFeeApproval_PENDING.String(),
		}

		handler := "<EMAIL>"

		mockRW.EXPECT().Get(gomock.Any(), approvalID).Return(approval, nil)
		mockRW.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)

		result, err := logic.ApproveMoegoPayCustomFeeApproval(context.Background(), approvalID, handler)
		assert.NoError(t, err)
		assert.Equal(t, salespb.MoegoPayCustomFeeApproval_APPROVED, result.ApprovalState)
		assert.Equal(t, handler, *result.Handler)
		assert.NotNil(t, result.HandleTime)
	})

	t.Run("Approve an already approved approval (idempotency)", func(t *testing.T) {
		approvalID := "approval_123"
		approval := &salesrepo.MoegoPayCustomFeeApproval{
			ID:            approvalID,
			ApprovalState: salespb.MoegoPayCustomFeeApproval_APPROVED.String(),
		}

		handler := "<EMAIL>"

		mockRW.EXPECT().Get(gomock.Any(), approvalID).Return(approval, nil)

		result, err := logic.ApproveMoegoPayCustomFeeApproval(context.Background(), approvalID, handler)
		assert.NoError(t, err)
		assert.Equal(t, salespb.MoegoPayCustomFeeApproval_APPROVED, result.ApprovalState)
	})

	t.Run("Cannot approve a rejected approval", func(t *testing.T) {
		approvalID := "approval_123"
		approval := &salesrepo.MoegoPayCustomFeeApproval{
			ID:            approvalID,
			ApprovalState: salespb.MoegoPayCustomFeeApproval_REJECTED.String(),
		}

		handler := "<EMAIL>"

		mockRW.EXPECT().Get(gomock.Any(), approvalID).Return(approval, nil)

		result, err := logic.ApproveMoegoPayCustomFeeApproval(context.Background(), approvalID, handler)
		assert.Error(t, err)
		assert.Nil(t, result)
	})
}

func TestRejectMoegoPayCustomFeeApproval(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockRW := salesmock.NewMockMoegoPayCustomFeeApprovalReadWriter(ctrl)
	mockSlackClient := slackmock.NewMockWebhookClient(ctrl)

	logic := sales.NewMoegoPayCustomFeeApprovalLogicByParams(mockRW, nil, "/xxx", mockSlackClient)

	t.Run("Reject MoeGo Pay Custom Fee Approval", func(t *testing.T) {
		approvalID := "approval_123"
		approval := &salesrepo.MoegoPayCustomFeeApproval{
			ID:            approvalID,
			ApprovalState: salespb.MoegoPayCustomFeeApproval_PENDING.String(),
		}

		handler := "<EMAIL>"

		mockRW.EXPECT().Get(gomock.Any(), approvalID).Return(approval, nil)
		mockRW.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)

		result, err := logic.RejectMoegoPayCustomFeeApproval(context.Background(), approvalID, handler)
		assert.NoError(t, err)
		assert.Equal(t, salespb.MoegoPayCustomFeeApproval_REJECTED, result.ApprovalState)
		assert.Equal(t, handler, *result.Handler)
		assert.NotNil(t, result.HandleTime)
	})

	t.Run("Reject an already rejected approval (idempotency)", func(t *testing.T) {
		approvalID := "approval_123"
		approval := &salesrepo.MoegoPayCustomFeeApproval{
			ID:            approvalID,
			ApprovalState: salespb.MoegoPayCustomFeeApproval_REJECTED.String(),
		}

		handler := "<EMAIL>"

		mockRW.EXPECT().Get(gomock.Any(), approvalID).Return(approval, nil)

		result, err := logic.RejectMoegoPayCustomFeeApproval(context.Background(), approvalID, handler)
		assert.NoError(t, err)
		assert.Equal(t, salespb.MoegoPayCustomFeeApproval_REJECTED, result.ApprovalState)
	})

	t.Run("Cannot reject an approved approval", func(t *testing.T) {
		approvalID := "approval_123"
		approval := &salesrepo.MoegoPayCustomFeeApproval{
			ID:            approvalID,
			ApprovalState: salespb.MoegoPayCustomFeeApproval_APPROVED.String(),
		}

		handler := "<EMAIL>"

		mockRW.EXPECT().Get(gomock.Any(), approvalID).Return(approval, nil)

		result, err := logic.RejectMoegoPayCustomFeeApproval(context.Background(), approvalID, handler)
		assert.Error(t, err)
		assert.Nil(t, result)
	})
}

//func TestApplyCustomFee(t *testing.T) {
//	ctrl := gomock.NewController(t)
//
//	mockPayment := paymentmock.NewMockMoegoPayCustomFeeClient(ctrl)
//
//	logic := sales.NewMoegoPayCustomFeeApprovalLogicByParams(nil, mockPayment, "/xxx", nil)
//
//	t.Run("Apply MoeGo Pay Custom Fee", func(t *testing.T) {
//
//		mockPayment.EXPECT().GetByCompanyID(gomock.Any(), gomock.Any()).Return(nil, nil)
//		mockPayment.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
//		customFee, err := logic.ApplyCustomFee(context.Background(), &salespb.MoegoPayCustomFeeApproval{
//			Id:                    "xxx",
//			CompanyId:             1,
//			TerminalPercentage:    "0.1",
//			TerminalFixed:         "0.2",
//			NonTerminalPercentage: "0.3",
//			NonTerminalFixed:      "0.4",
//			MinVolume:             "100",
//		})
//
//		assert.NoError(t, err)
//		assert.Equal(t, int64(1), *customFee.CompanyID)
//		assert.Equal(t, 0.1, *customFee.TerminalPercentage)
//		assert.Equal(t, int64(20), *customFee.TerminalFixedCents)
//		assert.Equal(t, 0.3, *customFee.NonTerminalPercentage)
//		assert.Equal(t, int64(40), *customFee.NonTerminalFixedCents)
//		assert.Equal(t, int64(100), *customFee.MinVolume)
//	})
//}
