package salesforce

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/client"
)

type queryResult[T any] struct {
	TotalSize int  `json:"totalSize"`
	Done      bool `json:"done"`
	Records   []T  `json:"records"`
}

const queryPath = "/services/data/v63.0/query?"

func (i *impl) Query(ctx context.Context, q string, result any) error {
	headers, err := i.getHeaders(ctx)
	if err != nil {
		return err
	}

	return i.client.Get(ctx, queryPath+q, result, client.WithReqHead(headers))
}
