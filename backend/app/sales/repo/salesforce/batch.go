package salesforce

import (
	"context"

	salesutils "github.com/MoeGolibrary/moego/backend/app/sales/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/client"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type batchRequestBody struct {
	Requests []*subRequest `json:"batchRequests"`
}

type subRequest struct {
	Method    string      `json:"method"`
	URL       string      `json:"url"`
	RichInput interface{} `json:"richInput,omitempty"`
}

type batchResponseBody struct {
	Responses []*subRequestResult `json:"results"`
	HasErrors bool                `json:"hasErrors"`
}

type subRequestResult struct {
	StatusCode int `json:"statusCode"`
	Result     any `json:"result"`
}

const batchPath = "/services/data/v63.0/composite/batch"

func (i *impl) batch(ctx context.Context, batchReq *batchRequestBody) (*batchResponseBody, error) {
	headers, err := i.getHeaders(ctx)
	if err != nil {
		return nil, err
	}

	reqBody := salesutils.ToJSON(batchReq)
	log.InfoContextf(ctx, "request body: %s", reqBody)

	respBody := &batchResponseBody{}
	if err := i.client.Post(ctx, batchPath, batchReq, respBody, client.WithReqHead(headers)); err != nil {
		return nil, err
	}

	return respBody, nil
}
