load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "account",
    srcs = ["account.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/sales/repo/account",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/codec/grpc",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/account/v1:account",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/account/v1:account",
    ],
)
