package sales

import (
	"context"
	"encoding/json"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type MoegoPayCustomFeeApproval struct {
	ID                    string         `gorm:"column:id"`
	Metadata              datatypes.JSON `gorm:"column:metadata"`
	CompanyID             int64          `gorm:"column:company_id"`
	AccountID             int64          `gorm:"column:account_id"`
	OwnerEmail            string         `gorm:"column:owner_email"`
	TerminalPercentage    string         `gorm:"column:terminal_percentage"`
	TerminalFixed         string         `gorm:"column:terminal_fixed"`
	NonTerminalPercentage string         `gorm:"column:non_terminal_percentage"`
	NonTerminalFixed      string         `gorm:"column:non_terminal_fixed"`
	MinVolume             string         `gorm:"column:min_volume"`
	Creator               string         `gorm:"column:creator"`
	Handler               *string        `gorm:"column:handler"`
	ApprovalState         string         `gorm:"column:approval_state"`
	CreatedAt             time.Time      `gorm:"column:created_at"`
	UpdatedAt             time.Time      `gorm:"column:updated_at"`
	HandledAt             *time.Time     `gorm:"column:handled_at"`
}

type MoegoPayCustomFeeApprovalMetadata struct {
	ContractID    *string `json:"contract_id"`
	Spif          *string `json:"spif"`
	OpportunityID *string `json:"opportunity_id"`
}

func (*MoegoPayCustomFeeApproval) TableName() string {
	return "moego_sales.public.moego_pay_custom_fee_approval"
}

func (a *MoegoPayCustomFeeApproval) GetMetadata() *MoegoPayCustomFeeApprovalMetadata {
	v := &MoegoPayCustomFeeApprovalMetadata{}
	if a.Metadata == nil {
		return v
	}

	if err := json.Unmarshal(a.Metadata, v); err != nil {
		// log and ignore err
		log.Error(err)
	}

	return v
}

func (*MoegoPayCustomFeeApproval) QueryWithNoCondition() MoegoPayCustomFeeApprovalQueryFilter {
	return func(db *gorm.DB) *gorm.DB {
		return db
	}
}

func (a *MoegoPayCustomFeeApproval) QueryWithCompanyID(companyID *int64) MoegoPayCustomFeeApprovalQueryFilter {
	if companyID == nil {
		return a.QueryWithNoCondition()
	}

	return func(db *gorm.DB) *gorm.DB {
		return db.Where("company_id = ?", companyID)
	}
}

func (a *MoegoPayCustomFeeApproval) QueryWithAccountID(accountID *int64) MoegoPayCustomFeeApprovalQueryFilter {
	if accountID == nil {
		return a.QueryWithNoCondition()
	}

	return func(db *gorm.DB) *gorm.DB {
		return db.Where("account_id = ?", accountID)
	}
}

// QueryWithOwnerEmail query email prefix match
func (a *MoegoPayCustomFeeApproval) QueryWithOwnerEmail(email *string) MoegoPayCustomFeeApprovalQueryFilter {
	if email == nil {
		return a.QueryWithNoCondition()
	}

	return func(db *gorm.DB) *gorm.DB {
		return db.Where("owner_email ilike ?", *email+"%")
	}
}

func (a *MoegoPayCustomFeeApproval) QueryWithApprovalStates(
	states []string) MoegoPayCustomFeeApprovalQueryFilter {
	if len(states) == 0 {
		return a.QueryWithNoCondition()
	}

	return func(db *gorm.DB) *gorm.DB {
		return db.Where("approval_state in ?", states)
	}
}

type MoegoPayCustomFeeApprovalQueryFilter func(*gorm.DB) *gorm.DB

type MoegoPayCustomFeeApprovalReadWriter interface {
	Get(ctx context.Context, id string) (*MoegoPayCustomFeeApproval, error)
	List(ctx context.Context, page Page[*MoegoPayCustomFeeApproval], filters ...MoegoPayCustomFeeApprovalQueryFilter) (
		[]*MoegoPayCustomFeeApproval, error)
	Count(ctx context.Context, filters ...MoegoPayCustomFeeApprovalQueryFilter) (int64, error)
	Save(ctx context.Context, approval *MoegoPayCustomFeeApproval) error
}

type moegoPayCustomFeeApprovalRWImpl struct {
	db *gorm.DB
}

func NewMoegoPayCustomFeeApprovalRW() MoegoPayCustomFeeApprovalReadWriter {
	return &moegoPayCustomFeeApprovalRWImpl{
		db: NewDB(),
	}
}

func (i *moegoPayCustomFeeApprovalRWImpl) Get(ctx context.Context, id string) (*MoegoPayCustomFeeApproval, error) {
	approval := &MoegoPayCustomFeeApproval{}
	if err := i.db.WithContext(ctx).Where("id = ?", id).First(approval).Error; err != nil {
		return nil, err
	}

	return approval, nil
}

func (i *moegoPayCustomFeeApprovalRWImpl) List(ctx context.Context, page Page[*MoegoPayCustomFeeApproval],
	filters ...MoegoPayCustomFeeApprovalQueryFilter) (
	[]*MoegoPayCustomFeeApproval, error) {
	sql := i.db.WithContext(ctx)
	for _, filter := range filters {
		sql = filter(sql)
	}
	sql = sql.Order("id DESC")
	sql = page.Apply(sql)

	var approvals []*MoegoPayCustomFeeApproval
	if err := sql.Find(&approvals).Error; err != nil {
		return nil, err
	}

	return approvals, nil
}

func (i *moegoPayCustomFeeApprovalRWImpl) Count(ctx context.Context, filters ...MoegoPayCustomFeeApprovalQueryFilter) (
	int64, error) {
	var count int64
	sql := i.db.WithContext(ctx).Model(&MoegoPayCustomFeeApproval{})
	for _, filter := range filters {
		sql = filter(sql)
	}
	if err := sql.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (i *moegoPayCustomFeeApprovalRWImpl) Save(ctx context.Context, approval *MoegoPayCustomFeeApproval) error {
	return i.db.WithContext(ctx).Save(approval).Error
}
