package sales

import (
	"strconv"

	"gorm.io/gorm"
)

type Page[T any] interface {
	Apply(sql *gorm.DB) *gorm.DB
	NextPage(items []T) Page[T]
	Token() string
}

func NewOffsetLimitPageFromPageToken[T any](pageSize int32, pageToken string) (Page[T], error) {
	// convert pageToken to pageNum
	pageNum := 1
	if len(pageToken) > 0 {
		var err error
		if pageNum, err = strconv.Atoi(pageToken); err != nil {
			return nil, err
		}
	}

	if pageSize == 0 {
		pageSize = 10
	}

	return &OffsetLimitPage[T]{
		Offset:   (pageNum - 1) * int(pageSize),
		Limit:    int(pageSize),
		pageNum:  pageNum,
		pageSize: int(pageSize),
	}, nil
}

type OffsetLimitPage[T any] struct {
	Offset   int
	Limit    int
	pageNum  int
	pageSize int
}

func (p *OffsetLimitPage[T]) Apply(sql *gorm.DB) *gorm.DB {
	if p == nil {
		return sql
	}

	return sql.Offset(p.Offset).Limit(p.Limit)
}

func (p *OffsetLimitPage[T]) Token() string {
	if p == nil {
		return ""
	}

	return strconv.Itoa(p.pageNum)
}

func (p *OffsetLimitPage[T]) NextPage(items []T) Page[T] {
	if p == nil {
		return nil
	}
	if len(items) < p.Limit {
		return nil
	}

	return &OffsetLimitPage[T]{
		Offset:   p.Offset + len(items),
		Limit:    p.Limit,
		pageNum:  p.pageNum + 1,
		pageSize: p.pageSize,
	}
}
