package sales

import (
	"context"
	"encoding/json"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type Contract struct {
	ID           string         `gorm:"column:id"`
	Creator      string         `gorm:"column:creator"`
	TemplateID   string         `gorm:"column:template_id"`
	Metadata     datatypes.JSON `gorm:"column:metadata"`
	Parameters   datatypes.JSON `gorm:"column:parameters"`
	Content      string         `gorm:"column:content"`
	SignatureURI *string        `gorm:"column:signature_uri"`
	SignedAt     *time.Time     `gorm:"column:signed_at"`
	CreatedAt    time.Time      `gorm:"column:created_at"`
	UpdatedAt    time.Time      `gorm:"column:updated_at"`
	DeletedAt    *time.Time     `gorm:"column:deleted_at"`
}

func (c *Contract) TableName() string {
	return "moego_sales.public.contract"
}

func (c *Contract) UnmarshalMetadata(v any) error {
	if c.Metadata == nil {
		return nil
	}

	return json.Unmarshal(c.<PERSON>, v)
}

func (c *Contract) UnmarshalParameters(v any) error {
	if c.<PERSON>meters == nil {
		return nil
	}

	return json.Unmarshal(c.<PERSON>, v)
}

func (c *Contract) QueryWithNoCondition() ContractQueryFilter {
	return func(db *gorm.DB) *gorm.DB {
		return db
	}
}

func (c *Contract) QueryWithTemplateIDs(ids []string) ContractQueryFilter {
	if len(ids) == 0 {
		return c.QueryWithNoCondition()
	}

	return func(db *gorm.DB) *gorm.DB {
		return db.Where("template_id in (?)", ids)
	}
}

func (c *Contract) QueryWithSigned(signed *bool) ContractQueryFilter {
	if signed == nil {
		return c.QueryWithNoCondition()
	}

	return func(db *gorm.DB) *gorm.DB {
		if *signed {
			return db.Where("signed_at is not null")
		}

		return db.Where("signed_at is null")
	}
}

func (c *Contract) QueryWithCreator(creator *string) ContractQueryFilter {
	if creator == nil {
		return c.QueryWithNoCondition()
	}

	return func(db *gorm.DB) *gorm.DB {
		return db.Where("creator ilike ?", *creator+"%")
	}
}

func (c *Contract) QueryWithMetadata(key string, value *string, ilike bool) ContractQueryFilter {
	if value == nil {
		return c.QueryWithNoCondition()
	}

	return func(db *gorm.DB) *gorm.DB {
		if ilike {
			return db.Where("metadata->>? ilike ?", key, *value+"%")
		}

		return db.Where("metadata->>? = ?", key, *value)
	}
}

func (c *Contract) QueryWithParameter(key string, value *string, ilike bool) ContractQueryFilter {
	if value == nil {
		return c.QueryWithNoCondition()
	}

	return func(db *gorm.DB) *gorm.DB {
		if ilike {
			return db.Where("parameters->>? ilike ?", key, *value+"%")
		}

		return db.Where("parameters->>? = ?", key, *value)
	}
}

type ContractQueryFilter func(*gorm.DB) *gorm.DB

type ContractReadWriter interface {
	Save(ctx context.Context, contract *Contract) error
	Delete(ctx context.Context, id string) error
	Get(ctx context.Context, id string) (*Contract, error)
	List(ctx context.Context, page Page[*Contract], opts ...ContractQueryFilter) ([]*Contract, error)
	Count(ctx context.Context, opts ...ContractQueryFilter) (int64, error)
}

type contractRWImpl struct {
	db *gorm.DB
}

func NewContractRW() ContractReadWriter {
	return &contractRWImpl{
		db: NewDB(),
	}
}

func (i *contractRWImpl) Save(ctx context.Context, contract *Contract) error {
	return i.db.WithContext(ctx).Clauses(clause.Returning{}).Save(contract).Error
}

func (i *contractRWImpl) Delete(ctx context.Context, id string) error {
	return i.db.WithContext(ctx).Delete(&Contract{}, "id = ?", id).Error
}

func (i *contractRWImpl) Get(ctx context.Context, id string) (*Contract, error) {
	contract := &Contract{}
	if err := i.db.WithContext(ctx).Where("id = ?", id).First(contract).Error; err != nil {
		return nil, err
	}

	return contract, nil
}

func (i *contractRWImpl) List(ctx context.Context, page Page[*Contract], filters ...ContractQueryFilter) (
	[]*Contract, error) {
	sql := i.db.WithContext(ctx).Where("deleted_at is null")
	for _, filter := range filters {
		sql = filter(sql)
	}
	sql = sql.Order("id DESC")
	sql = page.Apply(sql)

	var contracts []*Contract
	if err := sql.Find(&contracts).Error; err != nil {
		return nil, err
	}

	return contracts, nil
}

func (i *contractRWImpl) Count(ctx context.Context, filters ...ContractQueryFilter) (int64, error) {
	sql := i.db.WithContext(ctx).Model(&Contract{}).Where("deleted_at is null")
	for _, filter := range filters {
		sql = filter(sql)
	}
	var count int64
	if err := sql.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}
