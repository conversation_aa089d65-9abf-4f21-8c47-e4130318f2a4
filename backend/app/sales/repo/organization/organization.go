package organization

import (
	"context"
	"fmt"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
)

type Organization interface {
	GetCompany(ctx context.Context, id int64) (*organizationpb.CompanyModel, error)
	GetCompanySetting(ctx context.Context, id int64) (*organizationpb.CompanyPreferenceSettingModel, error)
	GetLocations(ctx context.Context, companyID int64) ([]*organizationpb.LocationBriefView, error)
}

type impl struct {
	company  organizationsvcpb.CompanyServiceClient
	location organizationsvcpb.BusinessServiceClient
}

func New() Organization {
	return &impl{
		company:  grpc.NewClient("moego-svc-organization", organizationsvcpb.NewCompanyServiceClient),
		location: grpc.NewClient("moego-svc-organization", organizationsvcpb.NewBusinessServiceClient),
	}
}

func (i *impl) GetCompany(ctx context.Context, id int64) (*organizationpb.CompanyModel, error) {
	result, err := i.company.QueryCompaniesByIds(ctx, &organizationsvcpb.QueryCompaniesByIdsRequest{
		CompanyIds: []int64{id},
	})
	if err != nil {
		return nil, err
	}

	company, ok := result.GetCompanyIdToCompany()[id]
	if !ok {
		return nil, fmt.Errorf("company not found for id %v", id)
	}

	return company, nil
}

func (i *impl) GetCompanySetting(ctx context.Context, id int64) (*organizationpb.CompanyPreferenceSettingModel, error) {
	resp, err := i.company.GetCompanyPreferenceSetting(ctx, &organizationsvcpb.GetCompanyPreferenceSettingRequest{
		CompanyId: id,
	})
	if err != nil {
		return nil, err
	}

	return resp.GetPreferenceSetting(), nil
}

func (i *impl) GetLocations(ctx context.Context, companyID int64) ([]*organizationpb.LocationBriefView, error) {
	resp, err := i.location.GetLocationList(ctx, &organizationsvcpb.GetLocationListRequest{
		TokenCompanyId: companyID,
	})
	if err != nil {
		return nil, err
	}

	return resp.GetLocation(), nil
}
