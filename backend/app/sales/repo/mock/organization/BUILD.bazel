load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "organization",
    srcs = ["organization_mock.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/organization",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@org_uber_go_mock//gomock",
    ],
)
