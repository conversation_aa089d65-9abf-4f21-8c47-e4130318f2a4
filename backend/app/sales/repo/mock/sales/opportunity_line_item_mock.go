// Code generated by MockGen. DO NOT EDIT.
// Source: ./sales/opportunity_line_item.go
//
// Generated by this command:
//
//	mockgen -source=./sales/opportunity_line_item.go -destination=mock/./sales/opportunity_line_item_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	sales "github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	gomock "go.uber.org/mock/gomock"
)

// MockOpportunityLineItemReadWriter is a mock of OpportunityLineItemReadWriter interface.
type MockOpportunityLineItemReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockOpportunityLineItemReadWriterMockRecorder
	isgomock struct{}
}

// MockOpportunityLineItemReadWriterMockRecorder is the mock recorder for MockOpportunityLineItemReadWriter.
type MockOpportunityLineItemReadWriterMockRecorder struct {
	mock *MockOpportunityLineItemReadWriter
}

// NewMockOpportunityLineItemReadWriter creates a new mock instance.
func NewMockOpportunityLineItemReadWriter(ctrl *gomock.Controller) *MockOpportunityLineItemReadWriter {
	mock := &MockOpportunityLineItemReadWriter{ctrl: ctrl}
	mock.recorder = &MockOpportunityLineItemReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOpportunityLineItemReadWriter) EXPECT() *MockOpportunityLineItemReadWriterMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockOpportunityLineItemReadWriter) BatchCreate(ctx context.Context, items []*sales.OpportunityLineItem) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", ctx, items)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockOpportunityLineItemReadWriterMockRecorder) BatchCreate(ctx, items any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockOpportunityLineItemReadWriter)(nil).BatchCreate), ctx, items)
}

// DeleteItems mocks base method.
func (m *MockOpportunityLineItemReadWriter) DeleteItems(ctx context.Context, opportunityID string, productIDs ...string) ([]*sales.OpportunityLineItem, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, opportunityID}
	for _, a := range productIDs {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteItems", varargs...)
	ret0, _ := ret[0].([]*sales.OpportunityLineItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteItems indicates an expected call of DeleteItems.
func (mr *MockOpportunityLineItemReadWriterMockRecorder) DeleteItems(ctx, opportunityID any, productIDs ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, opportunityID}, productIDs...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteItems", reflect.TypeOf((*MockOpportunityLineItemReadWriter)(nil).DeleteItems), varargs...)
}

// ListItems mocks base method.
func (m *MockOpportunityLineItemReadWriter) ListItems(ctx context.Context, opportunityID string) ([]*sales.OpportunityLineItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListItems", ctx, opportunityID)
	ret0, _ := ret[0].([]*sales.OpportunityLineItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListItems indicates an expected call of ListItems.
func (mr *MockOpportunityLineItemReadWriterMockRecorder) ListItems(ctx, opportunityID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListItems", reflect.TypeOf((*MockOpportunityLineItemReadWriter)(nil).ListItems), ctx, opportunityID)
}
