// Code generated by MockGen. DO NOT EDIT.
// Source: ./sales/opportunity.go
//
// Generated by this command:
//
//	mockgen -source=./sales/opportunity.go -destination=mock/./sales/opportunity_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	sales "github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	gomock "go.uber.org/mock/gomock"
)

// MockOpportunityReadWriter is a mock of OpportunityReadWriter interface.
type MockOpportunityReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockOpportunityReadWriterMockRecorder
	isgomock struct{}
}

// MockOpportunityReadWriterMockRecorder is the mock recorder for MockOpportunityReadWriter.
type MockOpportunityReadWriterMockRecorder struct {
	mock *MockOpportunityReadWriter
}

// NewMockOpportunityReadWriter creates a new mock instance.
func NewMockOpportunityReadWriter(ctrl *gomock.Controller) *MockOpportunityReadWriter {
	mock := &MockOpportunityReadWriter{ctrl: ctrl}
	mock.recorder = &MockOpportunityReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOpportunityReadWriter) EXPECT() *MockOpportunityReadWriterMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockOpportunityReadWriter) Get(ctx context.Context, id string) (*sales.Opportunity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*sales.Opportunity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockOpportunityReadWriterMockRecorder) Get(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockOpportunityReadWriter)(nil).Get), ctx, id)
}

// Save mocks base method.
func (m *MockOpportunityReadWriter) Save(ctx context.Context, op *sales.Opportunity) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", ctx, op)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockOpportunityReadWriterMockRecorder) Save(ctx, op any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockOpportunityReadWriter)(nil).Save), ctx, op)
}
