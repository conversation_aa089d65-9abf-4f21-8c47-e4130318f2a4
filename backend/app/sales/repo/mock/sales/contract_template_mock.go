// Code generated by MockGen. DO NOT EDIT.
// Source: ./sales/contract_template.go
//
// Generated by this command:
//
//	mockgen -source=./sales/contract_template.go -destination=mock/./sales/contract_template_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	sales "github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	gomock "go.uber.org/mock/gomock"
)

// MockContractTemplateReadWriter is a mock of ContractTemplateReadWriter interface.
type MockContractTemplateReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockContractTemplateReadWriterMockRecorder
	isgomock struct{}
}

// MockContractTemplateReadWriterMockRecorder is the mock recorder for MockContractTemplateReadWriter.
type MockContractTemplateReadWriterMockRecorder struct {
	mock *MockContractTemplateReadWriter
}

// NewMockContractTemplateReadWriter creates a new mock instance.
func NewMockContractTemplateReadWriter(ctrl *gomock.Controller) *MockContractTemplateReadWriter {
	mock := &MockContractTemplateReadWriter{ctrl: ctrl}
	mock.recorder = &MockContractTemplateReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockContractTemplateReadWriter) EXPECT() *MockContractTemplateReadWriterMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockContractTemplateReadWriter) Get(ctx context.Context, id string) (*sales.ContractTemplate, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*sales.ContractTemplate)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockContractTemplateReadWriterMockRecorder) Get(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockContractTemplateReadWriter)(nil).Get), ctx, id)
}

// GetNewestTemplate mocks base method.
func (m *MockContractTemplateReadWriter) GetNewestTemplate(ctx context.Context, name string) (*sales.ContractTemplate, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewestTemplate", ctx, name)
	ret0, _ := ret[0].(*sales.ContractTemplate)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewestTemplate indicates an expected call of GetNewestTemplate.
func (mr *MockContractTemplateReadWriterMockRecorder) GetNewestTemplate(ctx, name any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewestTemplate", reflect.TypeOf((*MockContractTemplateReadWriter)(nil).GetNewestTemplate), ctx, name)
}

// List mocks base method.
func (m *MockContractTemplateReadWriter) List(ctx context.Context, filters ...sales.ContractTemplateQueryOption) ([]*sales.ContractTemplate, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx}
	for _, a := range filters {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "List", varargs...)
	ret0, _ := ret[0].([]*sales.ContractTemplate)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockContractTemplateReadWriterMockRecorder) List(ctx any, filters ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx}, filters...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockContractTemplateReadWriter)(nil).List), varargs...)
}
