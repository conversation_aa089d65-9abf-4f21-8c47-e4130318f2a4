package salesutils

import (
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

var (
	ContractTermOneYear    = "1-year"
	ContractTermTwoYears   = "2-year"
	ContractTermThreeYears = "3-year"
)

func GetContractTerm(term salespb.SubscriptionTerm) *string {
	switch term {
	case salespb.SubscriptionTerm_MONTHLY:
		// salesforce 没有这个值
		return nil
	case salespb.SubscriptionTerm_ONE_YEAR:
		return &ContractTermOneYear
	case salespb.SubscriptionTerm_TWO_YEARS:
		return &ContractTermTwoYears
	case salespb.SubscriptionTerm_THREE_YEARS:
		return &ContractTermThreeYears
	}

	return nil
}
