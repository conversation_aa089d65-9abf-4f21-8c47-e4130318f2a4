package salesutils

import (
	"os"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func GetMISHost() string {
	env := os.Getenv("MOEGO_ENVIRONMENT")
	if len(env) == 0 {
		env = "local"
		log.Warnf("MOEGO_ENVIRONMENT is not set, using default value: %s", env)
	}

	switch env {
	case "production":
		return "mis.moego.pet"
	case "staging":
		return "mis.s1.moego.dev"
	default:
		return "mis.t2.moego.dev"
	}
}
