package salesutils_test

import (
	"testing"

	"github.com/stretchr/testify/require"

	salesutils "github.com/MoeGolibrary/moego/backend/app/sales/utils"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

func TestGetContractTerm(t *testing.T) {
	require.Equal(t, salesutils.ContractTermOneYear, *salesutils.GetContractTerm(salespb.SubscriptionTerm_ONE_YEAR))
	require.Equal(t, salesutils.ContractTermTwoYears, *salesutils.GetContractTerm(salespb.SubscriptionTerm_TWO_YEARS))
	require.Equal(t, salesutils.ContractTermThreeYears, *salesutils.GetContractTerm(salespb.SubscriptionTerm_THREE_YEARS))
	require.Nil(t, salesutils.GetContractTerm(salespb.SubscriptionTerm_MONTHLY))
	require.Nil(t, salesutils.GetContractTerm(salespb.SubscriptionTerm_SUBSCRIPTION_TERM_UNSPECIFIED))
}
