package config

import (
	"fmt"
	"os"
	"sync"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

var cfg *SalesConfig

var initCfg sync.Once

type SalesConfig struct {
	Salesforce *Salesforce `yaml:"salesforce"`
	Slack      *Slack      `yaml:"slack"`
}

type Salesforce struct {
	ClientID     string `yaml:"client_id"`
	ClientSecret string `yaml:"client_secret"`
	InstanceURL  string `yaml:"instance_url"`
}

type Slack struct {
	MoegoPayCustomRatePath string `yaml:"moego_pay_custom_rate_path"`
}

func Init(dir string) {
	initCfg.Do(func() {
		env := os.Getenv("MOEGO_ENVIRONMENT")
		if len(env) == 0 {
			env = "local"
			log.Warnf("MOEGO_ENVIRONMENT is not set, using default value: %s", env)
		}

		c, err := config.DefaultConfigLoader.Load(fmt.Sprintf("%s/%s/sales.yaml", dir, env))
		if err != nil {
			panic(err)
		}

		cfg = &SalesConfig{}
		err = c.Unmarshal(cfg)
		if err != nil {
			panic(err)
		}
	})
}

func GetCfg() *SalesConfig {
	return cfg
}
