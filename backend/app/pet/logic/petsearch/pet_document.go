package petsearch

import (
	"context"
	"encoding/base64"
	"slices"
	"strconv"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm/customer"
	"github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm/pet"
	"github.com/MoeGolibrary/moego/backend/app/pet/repo/search"
	searchutils "github.com/MoeGolibrary/moego/backend/app/search/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	structutil "github.com/MoeGolibrary/moego/backend/common/utils/struct"
	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

type Logic struct {
	pet      pet.ReadWriter
	customer customer.ReadWriter
	search   search.ReadWriter
}

func NewLogic() *Logic {
	return &Logic{
		pet:      pet.New(),
		customer: customer.New(),
		search:   search.New(),
	}
}

func NewByParams(pet pet.ReadWriter,
	customer customer.ReadWriter,
	search search.ReadWriter) *Logic {
	return &Logic{
		pet:      pet,
		customer: customer,
		search:   search,
	}
}

const (
	petIndex = "erp-search-pets"
)

// createBulkOperations 创建批量操作请求
func createBulkOperations(pets []*pet.CustomerPet,
	customerMap map[uint]*customer.
		BusinessCustomer) []*searchpb.BulkDocumentRequest_BulkOperation {
	opts := make([]*searchpb.BulkDocumentRequest_BulkOperation, 0, len(pets))

	for _, p := range pets {
		// customer
		c, ok := customerMap[uint(p.CustomerID)]
		if !ok {
			continue
		}

		customerName := c.FirstName + " " + c.LastName
		doc := &petDocument{
			ID:        int64(p.ID),
			Name:      p.PetName,
			Status:    p.Status,
			CompanyID: p.CompanyID,
			Customers: &petDocumentCustomer{ID: p.CustomerID, Status: c.Status, Name: customerName},
		}

		// 这里不用检查错误
		docStruct, _ := structutil.ConvertToProtoStruct(doc)

		petIDstr := strconv.FormatInt(int64(p.ID), 10)
		opts = append(opts, &searchpb.BulkDocumentRequest_BulkOperation{
			OperationType: searchpb.OperationType_INDEX,
			Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
				Index: petIndex,
				Id:    &petIDstr,
			},
			Document: docStruct,
		})
	}

	return opts
}

// getCustomersForPets 获取宠物对应的客户信息
func (l *Logic) getCustomersForPets(ctx context.Context,
	pets []*pet.CustomerPet) (map[uint]*customer.BusinessCustomer, error) {
	customerIDs := make([]int64, 0, len(pets))
	for _, p := range pets {
		customerIDs = append(customerIDs, int64(p.CustomerID))
	}

	customers, err := l.customer.List(ctx, &customer.ListParams{
		CustomerIDs: customerIDs,
	})
	if err != nil {
		return nil, err
	}

	customerMap := make(map[uint]*customer.BusinessCustomer)
	for _, c := range customers {
		customerMap[c.ID] = c
	}

	return customerMap, nil
}

func (l *Logic) SearchPetByTerm(ctx context.Context, term *TermSearchParam) (*petpb.SearchPetResponse, error) {
	request := term.ConvertToSearchPetTermRequest(ctx)

	response, err := l.search.SearchDocument(ctx, request)
	if err != nil {
		return nil, err
	}

	log.WithContext(ctx).Debug("SearchPetByTerm", zap.Any("response", response))
	petIDs := make([]int64, 0, len(response.Hits))
	sorts := make([][]*structpb.Value, 0, len(response.Hits))
	for _, hit := range response.Hits {
		var doc *petDocument
		docjson, _ := hit.Source.MarshalJSON()
		_ = sonic.Unmarshal(docjson, &doc)
		petIDs = append(petIDs, doc.ID)
		sorts = append(sorts, hit.Sort)
	}
	if len(petIDs) == 0 {
		return &petpb.SearchPetResponse{
			Pets:          nil,
			NextPageToken: response.NextPageToken,
		}, nil
	}

	pets, err := l.pet.List(ctx, &pet.Query{
		CompanyIDs: []int64{term.CompanyID},
	}, &pet.Filter{
		Status: 1,
	}, nil)
	if err != nil {
		return nil, err
	}

	petMap := make(map[uint]*pet.CustomerPet)
	for _, p := range pets {
		petMap[p.ID] = p
	}

	customers, err := l.getCustomersForPets(ctx, pets)
	if err != nil {
		return nil, err
	}

	petResult := make([]*petpb.SearchPetResponse_Pet, 0, len(pets))
	for _, pm := range petIDs {
		p, ok := petMap[uint(pm)]
		if !ok {
			continue
		}
		customerName := customers[uint(p.CustomerID)].FirstName +
			" " + customers[uint(p.CustomerID)].LastName
		petResult = append(petResult, &petpb.SearchPetResponse_Pet{
			Id:         int64(p.ID),
			CustomerId: int64(p.CustomerID),
			Name:       p.PetName,
			AvatarPath: p.AvatarPath,
			PetType:    int32(p.PetTypeID),
			Breed:      p.Breed,
			Weight:     p.Weight,
			CoatType:   p.HairLength,
			Client: &petpb.SearchPetResponse_Pet_Client{
				Id:         int64(p.CustomerID),
				Name:       customerName,
				GivenName:  customers[uint(p.CustomerID)].FirstName,
				FamilyName: customers[uint(p.CustomerID)].LastName,
			},
		})
	}

	log.DebugContext(ctx, "SearchPetByTerm", zap.Any("petIDs", petIDs), zap.Any("sorts", sorts))

	return &petpb.SearchPetResponse{
		Pets:          petResult,
		NextPageToken: buildNextPageToken(sorts),
	}, nil
}

func buildNextPageToken(sorts [][]*structpb.Value) string {
	sort := sorts[len(sorts)-1]
	page := make([]any, 0, len(sort))
	for _, s := range sort {
		page = append(page, searchutils.GetValue(s))
	}
	sortValuesJSON, err := sonic.Marshal(page)
	if err != nil {
		return ""
	}

	return base64.StdEncoding.EncodeToString(sortValuesJSON)

}

type SyncStrategy interface {
	opts(ctx context.Context) ([]*searchpb.BulkDocumentRequest_BulkOperation, error)
}

func (l *Logic) IndexPetDocument(ctx context.Context,
	req *petpb.IndexPetDocumentRequest) (*petpb.IndexPetDocumentResponse, error) {
	syncStrategy, err := l.MatchSyncStrategy(ctx, req)
	if err != nil {
		return nil, err
	}

	opts, err := syncStrategy.opts(ctx)
	if err != nil {
		return nil, err
	}

	success := make([]int64, 0, len(opts))
	fail := make([]*petpb.IndexPetDocumentResponse_Error, 0, len(opts))

	// 批量大小设置为5000
	const batchSize = 5000
	// 按批次处理文档
	for i := 0; i < len(opts); i += batchSize {
		end := i + batchSize
		if end > len(opts) {
			end = len(opts)
		}
		batchOps := opts[i:end]
		request := &searchpb.BulkDocumentRequest{
			Operations: batchOps,
		}

		response, err := l.search.IndexPetDocument(ctx, request)
		if err != nil {
			return nil, err
		}

		log.DebugContext(ctx, "sync opts to elasticsearch success", zap.Any("response", response))

		for _, result := range response.Results {
			petID, err := strconv.ParseInt(result.GetDocumentId(), 10, 64)
			if err != nil {
				return nil, err
			}
			// error
			if result.GetError() != nil {
				fail = append(fail, &petpb.IndexPetDocumentResponse_Error{
					Id:          petID,
					ErrorType:   result.GetError().GetErrorType(),
					ErrorReason: result.GetError().GetErrorReason(),
				})

				continue
			}
			// success
			success = append(success, petID)
		}
	}

	return &petpb.IndexPetDocumentResponse{
		SuccessIds: success,
		Errors:     fail,
	}, nil
}

func (l *Logic) MatchSyncStrategy(ctx context.Context,
	req *petpb.IndexPetDocumentRequest) (SyncStrategy, error) {
	switch req.GetFlags().(type) {
	case *petpb.IndexPetDocumentRequest_Company_:
		return &SyncCompanyStrategy{
			companyIDs: req.GetCompany().GetCompanyIds(),
			customer:   l.customer,
			pet:        l.pet,
		}, nil
	case *petpb.IndexPetDocumentRequest_Pet_:
		return &SyncPetStrategy{
			petIDs:   req.GetPet().GetPetIds(),
			customer: l.customer,
			pet:      l.pet,
		}, nil
	default:
		log.Error(ctx, "IndexPetDocument", zap.Any("req", req))

		return nil, status.Errorf(codes.InvalidArgument, "invalid index pet document request")
	}
}

type SyncCompanyStrategy struct {
	companyIDs []int64
	customer   customer.ReadWriter
	pet        pet.ReadWriter
}

func (c *SyncCompanyStrategy) opts(ctx context.Context) ([]*searchpb.
	BulkDocumentRequest_BulkOperation, error) {
	// 批量大小设置为30000
	const batchSize = 30000

	allOpts := make([]*searchpb.BulkDocumentRequest_BulkOperation, 0, len(c.companyIDs))

	// 按公司ID分批获取客户
	for _, companyID := range c.companyIDs {
		offset := 0
		for {
			customers, err := c.customer.List(ctx, &customer.ListParams{
				CompanyIDs: []int64{companyID},
				Limit:      batchSize,
				Offset:     int32(offset),
			})
			if err != nil {
				return nil, err
			}

			// 如果没有更多客户，退出循环
			if len(customers) == 0 {
				break
			}

			customerMap := make(map[uint]*customer.BusinessCustomer)
			customerIDs := make([]int64, 0, len(customers))
			for _, customer := range customers {
				customerMap[customer.ID] = customer
				customerIDs = append(customerIDs, int64(customer.ID))
			}

			// 获取这批客户的宠物
			pets, err := c.pet.List(ctx, &pet.Query{
				CustomerIDs: customerIDs,
			}, &pet.Filter{
				Status: 1,
			}, nil)
			if err != nil {
				return nil, err
			}

			// 创建批量操作并添加到结果中
			batchOpts := createBulkOperations(pets, customerMap)
			allOpts = append(allOpts, batchOpts...)
			// 更新偏移量，准备获取下一批
			offset += len(customers)

			// 如果获取的客户数少于批量大小，说明已经获取完所有客户
			if len(customers) < batchSize {
				break
			}
		}
	}

	log.DebugContext(ctx, "CompanySyncStrategy completed", zap.Int("total_operations", len(allOpts)))

	return allOpts, nil
}

type SyncPetStrategy struct {
	petIDs   []int64
	customer customer.ReadWriter
	pet      pet.ReadWriter
}

func (p *SyncPetStrategy) opts(ctx context.Context) ([]*searchpb.
	BulkDocumentRequest_BulkOperation, error) {
	pets, err := p.pet.List(ctx, &pet.Query{
		PetIDs: p.petIDs,
	}, &pet.Filter{
		Status: 1,
	}, nil)
	if err != nil {
		return nil, err
	}

	// 无pet 报错
	if len(pets) == 0 {
		return nil, status.Errorf(codes.NotFound, "pet list is empty")
	}

	customerIDs := make([]int64, 0, len(pets))
	for _, p := range pets {
		// 去重
		if slices.Contains(customerIDs, int64(p.CustomerID)) {
			continue
		}
		customerIDs = append(customerIDs, int64(p.CustomerID))
	}

	customers, err := p.customer.List(ctx, &customer.ListParams{
		CustomerIDs: customerIDs,
	})
	if err != nil {
		return nil, err
	}

	customerMap := make(map[uint]*customer.BusinessCustomer)
	for _, c := range customers {
		customerMap[c.ID] = c
	}

	opts := createBulkOperations(pets, customerMap)

	return opts, nil
}
