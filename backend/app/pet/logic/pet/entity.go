package pet

import petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"

type Pet struct {
	ID         int64
	PetName    string
	PetType    petpb.Pet_PetType
	PetGender  petpb.Pet_PetGender
	Breed      string
	CompanyID  int64
	BusinessID int64
	CustomerID int64
	State      petpb.Pet_State
	Mixed      bool
}

func (p *Pet) ToPB() *petpb.Pet {
	return &petpb.Pet{
		Id:         p.ID,
		Name:       p.PetName,
		PetType:    p.PetType,
		Gender:     p.PetGender,
		Breed:      p.Breed,
		CompanyId:  p.CompanyID,
		BusinessId: p.BusinessID,
		CustomerId: p.CustomerID,
	}
}

type CreatePetParams struct {
	CustomerID int64
	PetName    string
	PetType    petpb.Pet_PetType
	PetGender  petpb.Pet_PetGender
	PetBreed   string
	CompanyID  int64
	BusinessID int64
	Mixed      bool
}

func (p *CreatePetParams) GetMixed() int8 {
	if p.Mixed {
		return 1
	}

	return 0
}

type UpdatePetParams struct {
	PetID      int64
	PetName    string
	PetType    petpb.Pet_PetType
	PetGender  petpb.Pet_PetGender
	PetBreed   string
	CompanyID  int64
	BusinessID int64
	Mixed      bool
}

func (p *UpdatePetParams) GetMixed() int8 {
	if p.Mixed {
		return 1
	}

	return 0
}

type ListPetParams struct {
	CustomerIDs []int64
	PetIDs      []int64
	CompanyIDs  []int64
	Page        int
	PageSize    int
	OrderBy     string
	Status      int8
}
