package synccustomer

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/app/pet/repo/customer"
	"github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm/pet"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Logic struct {
	customerRPC customer.ReadWriter
	pet         pet.ReadWriter
}

func NewLogic() *Logic {
	return &Logic{
		customerRPC: customer.New(),
		pet:         pet.New(),
	}
}

func (l *Logic) SyncCustomerSearch(ctx context.Context, customerID int64) error {
	return l.customerRPC.SyncCustomerSearch(ctx, customerID)
}

func (l *Logic) SyncCustomerSearchByPetID(ctx context.Context, petID int64) error {
	pet, err := l.pet.Get(ctx, petID)
	if err != nil {
		return err
	}
	if pet == nil || pet.CustomerID <= 0 {
		log.ErrorContextf(ctx, "SyncCustomerByPetID Get pet nil, petID:%d", petID)

		return fmt.Errorf("get pet fail")
	}

	return l.customerRPC.SyncCustomerSearch(ctx, int64(pet.CustomerID))
}
