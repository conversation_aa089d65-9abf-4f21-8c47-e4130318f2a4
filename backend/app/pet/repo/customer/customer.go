package customer

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

type ReadWriter interface {
	SyncCustomerSearch(ctx context.Context, customerID int64) error
}

type impl struct {
	customer customerpb.CustomerServiceClient
}

func New() ReadWriter {
	return &impl{
		customer: grpc.NewClient("moego-customer", customerpb.NewCustomerServiceClient),
	}
}

func (i *impl) SyncCustomerSearch(ctx context.Context, customerID int64) error {
	_, err := i.customer.SyncCustomerSearch(ctx, &customerpb.SyncCustomerSearchRequest{
		CustomerId: customerID,
	})
	if err != nil {
		log.ErrorContextf(ctx, "Customer SyncCustomerSearch err, customerID:%d, err:%+v", customerID, err)

		return err
	}
	log.InfoContextf(ctx, "Customer SyncCustomerSearch success, customerID:%d", customerID)

	return nil
}
