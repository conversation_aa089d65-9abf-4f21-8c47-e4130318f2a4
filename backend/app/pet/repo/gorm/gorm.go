package gorm

import (
	"sync"

	"gorm.io/gorm"

	igorm "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
)

var (
	database *gorm.DB
	once     sync.Once
)

func Set(db *gorm.DB) {
	once.Do(func() {
		database = db
	})
}

func Get() *gorm.DB {
	if database == nil {
		New()
	}

	return database
}

func New() {
	once.Do(func() {
		db, err := igorm.NewClientProxy("mysql.moe_customer")
		if err != nil {
			panic(err)
		}
		database = db
	})
}
