package service

import (
	"context"
	"encoding/base64"
	"encoding/json"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/MoeGolibrary/moego/backend/app/pet/logic/pet"
	"github.com/MoeGolibrary/moego/backend/app/pet/logic/petsearch"
	synccustomer "github.com/MoeGolibrary/moego/backend/app/pet/logic/sync_customer"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
)

type PetService struct {
	psl *petsearch.Logic
	pl  *pet.Logic
	scl *synccustomer.Logic
	petpb.UnimplementedPetServiceServer
}

func NewPetService() *PetService {
	return &PetService{
		psl: petsearch.NewLogic(),
		pl:  pet.NewLogic(),
		scl: synccustomer.NewLogic(),
	}
}

func (s *PetService) CreatePet(ctx context.Context, req *petpb.CreatePetRequest) (*emptypb.Empty, error) {
	petreq := req.GetPet()
	if petreq == nil {
		return nil, status.Errorf(codes.InvalidArgument, "pet is nil")
	}
	createPet := &pet.CreatePetParams{
		CustomerID: petreq.GetCustomerId(),
		PetName:    petreq.GetName(),
		PetType:    petreq.GetPetType(),
		PetGender:  petreq.GetGender(),
		PetBreed:   petreq.GetBreed(),
		CompanyID:  petreq.GetCompanyId(),
		BusinessID: petreq.GetBusinessId(),
		Mixed:      petreq.GetMixed(),
	}
	_, err := s.pl.Create(ctx, createPet)
	if err != nil {
		return nil, err
	}

	// sync customer to es
	if err := s.scl.SyncCustomerSearch(ctx, createPet.CustomerID); err != nil {
		log.ErrorContextf(ctx, "SyncCustomerSearch err, customerID:%d, err:%+v", createPet.CustomerID, err)
	}

	return &emptypb.Empty{}, nil
}

func (s *PetService) BatchCreatePets(ctx context.Context,
	req *petpb.BatchCreatePetsRequest) (*petpb.BatchCreatePetsResponse, error) {
	petreq := req.GetRequests()
	if len(petreq) == 0 {
		return nil, status.Errorf(codes.InvalidArgument, "pet is nil")
	}

	var customerID int64
	params := make([]*pet.CreatePetParams, 0, len(petreq))
	for i, p := range petreq {
		params = append(params, &pet.CreatePetParams{
			CustomerID: p.GetPet().GetCustomerId(),
			PetName:    p.GetPet().GetName(),
			PetType:    p.GetPet().GetPetType(),
			PetGender:  p.GetPet().GetGender(),
			PetBreed:   p.GetPet().GetBreed(),
			CompanyID:  p.GetPet().GetCompanyId(),
			BusinessID: p.GetPet().GetBusinessId(),
			Mixed:      p.GetPet().GetMixed(),
		})
		if i == 0 {
			customerID = p.GetPet().GetCustomerId()
		}
		if p.GetPet().GetCustomerId() != customerID {
			return nil, status.Errorf(codes.InvalidArgument, "not support different CustomerIDs")
		}
	}

	ids, err := s.pl.BatchCreate(ctx, params)
	if err != nil {
		return nil, err
	}

	// sync customer to es
	if err := s.scl.SyncCustomerSearch(ctx, customerID); err != nil {
		log.ErrorContextf(ctx, "SyncCustomerSearch err, customerID:%d, err:%+v", customerID, err)
	}

	pbPets := make([]*petpb.Pet, len(ids))
	for i, id := range ids {
		pbPets[i] = &petpb.Pet{
			Id: id,
		}
	}

	return &petpb.BatchCreatePetsResponse{
		Pets: pbPets,
	}, nil
}

func (s *PetService) UpdatePet(ctx context.Context, req *petpb.UpdatePetRequest) (*emptypb.Empty, error) {
	petreq := req.GetPet()
	if petreq == nil {
		return nil, status.Errorf(codes.InvalidArgument, "pet is nil")
	}

	if petreq.GetId() == 0 {
		return nil, status.Errorf(codes.InvalidArgument, "pet id is required")
	}

	_, err := s.pl.Update(ctx, &pet.UpdatePetParams{
		PetID:     petreq.GetId(),
		PetName:   petreq.GetName(),
		PetType:   petreq.GetPetType(),
		PetGender: petreq.GetPetGender(),
		PetBreed:  petreq.GetBreed(),
	})
	if err != nil {
		return nil, err
	}

	// sync customer to es
	if err := s.scl.SyncCustomerSearchByPetID(ctx, petreq.GetId()); err != nil {
		log.ErrorContextf(ctx, "SyncCustomerSearchByPetID err, petID:%d, err:%+v", petreq.GetId(), err)
	}

	return &emptypb.Empty{}, nil
}

func (s *PetService) DeletePet(ctx context.Context, req *petpb.DeletePetRequest) (*emptypb.Empty, error) {
	err := s.pl.Delete(ctx, req.GetId())
	if err != nil {
		return nil, err
	}
	// sync customer to es
	if err := s.scl.SyncCustomerSearchByPetID(ctx, req.GetId()); err != nil {
		log.ErrorContextf(ctx, "SyncCustomerSearchByPetID err, petID:%d, err:%+v", req.GetId(), err)
	}

	return &emptypb.Empty{}, nil
}

func (s *PetService) ListPet(ctx context.Context, req *petpb.ListPetRequest) (*petpb.ListPetResponse, error) {

	pets, err := s.pl.List(ctx, &pet.ListPetParams{
		CustomerIDs: []int64{req.GetParent()},
		PetIDs:      req.GetFilter().GetPetIds(),
		Page:        int(req.GetPage()),
		PageSize:    int(req.GetPageSize()),
		OrderBy:     req.GetOrderBy(),
	})
	if err != nil {
		return nil, err
	}

	pbPets := make([]*petpb.Pet, 0, len(pets))
	for _, p := range pets {
		pbPets = append(pbPets, p.ToPB())
	}

	return &petpb.ListPetResponse{
		Pets: pbPets,
	}, nil
}

func (s *PetService) IndexPetDocument(ctx context.Context,
	req *petpb.IndexPetDocumentRequest) (*petpb.IndexPetDocumentResponse, error) {
	log.DebugContext(ctx, "IndexPetDocument", zap.Any("req", req))

	return s.psl.IndexPetDocument(ctx, req)
}

func (s *PetService) SearchPet(ctx context.Context,
	req *petpb.SearchPetRequest) (*petpb.SearchPetResponse, error) {
	log.DebugContext(ctx, "SearchPet", zap.Any("req", req))
	switch req.GetSearchCriteria().(type) {
	case *petpb.SearchPetRequest_Term:
		searchAfter, err := decodePageToken(req.GetPageToken())
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "invalid page token")
		}
		log.DebugContext(ctx, "SearchPet", zap.Any("searchAfter", searchAfter))

		return s.psl.SearchPetByTerm(ctx, &petsearch.TermSearchParam{
			CompanyID:   req.GetCompanyId(),
			Size:        req.PageSize,
			Term:        req.GetTerm(),
			SearchAfter: searchAfter,
		})
	default:
		return nil, status.Errorf(codes.InvalidArgument, "invalid search criteria")
	}
}

func decodePageToken(pageToken string) ([]float64, error) {
	if pageToken == "" {
		return nil, nil
	}
	decoded, err := base64.StdEncoding.DecodeString(pageToken)
	if err != nil {
		return nil, err
	}
	var result []float64
	err = json.Unmarshal(decoded, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
