package attribute

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/query"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/serviceattribute"
)

// Logic implements the business logic for ServiceAttribute.
type Logic struct {
	repo serviceattribute.Repository
}

// NewLogic creates a new Logic.
func NewLogic() *Logic {
	return &Logic{
		repo: serviceattribute.NewRepository(),
	}
}

// NewWithRepository creates a new Logic with the given repository (for testing).
func NewWithRepository(repo serviceattribute.Repository) *Logic {
	return &Logic{
		repo: repo,
	}
}

// WithQuery returns a new Logic with the given query
func (l *Logic) WithQuery(q *query.Query) *Logic {
	return &Logic{
		repo: l.repo.WithQuery(q),
	}
}

// CreateAttribute creates a new service attribute value.
func (l *Logic) CreateAttribute(ctx context.Context, e *ServiceAttribute) (*ServiceAttribute, error) {
	m := EntityToModel(e)

	err := l.repo.Create(ctx, m)
	if err != nil {
		return nil, err
	}

	return ModelToEntity(m), nil
}

// CreateAttributes creates multiple service attribute values.
func (l *Logic) CreateAttributes(ctx context.Context, attributes []*ServiceAttribute) error {
	if len(attributes) == 0 {
		return nil
	}

	err := l.repo.BatchCreate(ctx, EntityToModels(attributes))
	if err != nil {
		return err
	}

	return nil
}

// GetAttribute gets a service attribute value by ID.
func (l *Logic) GetAttribute(ctx context.Context, id int64) (*ServiceAttribute, error) {
	m, err := l.repo.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return ModelToEntity(m), nil
}

// ListAttributes lists service attribute values by service template ID.
func (l *Logic) ListAttributes(ctx context.Context, serviceID int64) ([]*ServiceAttribute, error) {
	ms, err := l.repo.List(ctx, serviceID)
	if err != nil {
		return nil, err
	}

	return ModelToEntities(ms), nil
}

// UpdateServiceAttribute updates a service attribute value.
func (l *Logic) UpdateServiceAttribute(ctx context.Context, e *ServiceAttribute) (*ServiceAttribute, error) {
	m := EntityToModel(e)
	err := l.repo.Update(ctx, m)
	if err != nil {
		return nil, err
	}
	// After update, we should get the latest version from the DB.
	updatedModel, err := l.repo.Get(ctx, e.ID)
	if err != nil {
		return nil, err
	}

	return ModelToEntity(updatedModel), nil
}

// DeleteAttribute deletes a service attribute value.
func (l *Logic) DeleteAttribute(ctx context.Context, id int64) error {
	return l.repo.Delete(ctx, id)
}

// DeleteAttributesByServiceID deletes all service attribute values by service ID.
func (l *Logic) DeleteAttributesByServiceID(ctx context.Context, serviceID int64) error {
	_, err := l.repo.DeleteByServiceID(ctx, serviceID)
	if err != nil {
		return err
	}

	return nil
}
