package service

import (
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestProtoToEntity(t *testing.T) {
	// 测试正常转换
	now := time.Now()
	pbTimestamp := timestamppb.New(now)

	description := "Test Description"
	pbService := &offeringpb.Service{
		Id:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationId:   123,
		CareTypeId:       456,
		CategoryId:       789,
		Name:             "Test Service",
		Description:      &description,
		ColorCode:        "#FF0000",
		Sort:             10,
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM,
		IsActive:         true,
		CreateTime:       pbTimestamp,
		UpdateTime:       pbTimestamp,
	}

	entity := ProtoToEntity(pbService)

	assert.NotNil(t, entity)
	assert.Equal(t, int64(1), entity.ID)
	assert.Equal(t, offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY, entity.OrganizationType)
	assert.Equal(t, int64(123), entity.OrganizationID)
	assert.Equal(t, int64(456), entity.CareTypeID)
	assert.Equal(t, int64(789), entity.CategoryID)
	assert.Equal(t, "Test Service", entity.Name)
	assert.Equal(t, "Test Description", *entity.Description)
	assert.Equal(t, "#FF0000", entity.ColorCode)
	assert.Equal(t, int64(10), entity.Sort)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, entity.Images)
	assert.Equal(t, offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM, entity.Source)
	assert.True(t, entity.IsActive)

	// 测试 nil 输入
	entity = ProtoToEntity(nil)
	assert.Nil(t, entity)
}

func TestEntityToProto(t *testing.T) {
	// 测试正常转换
	now := time.Now()
	deleteTime := now.Add(time.Hour)

	description := "Test Description"
	entity := &Service{
		ID:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       456,
		CategoryID:       789,
		Name:             "Test Service",
		Description:      &description,
		ColorCode:        "#FF0000",
		Sort:             10,
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM,
		IsActive:         true,
		CreateTime:       now,
		UpdateTime:       now,
		DeleteTime:       &deleteTime,
	}

	proto := EntityToProto(entity)

	assert.NotNil(t, proto)
	assert.Equal(t, int64(1), proto.Id)
	assert.Equal(t, offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY, proto.OrganizationType)
	assert.Equal(t, int64(123), proto.OrganizationId)
	assert.Equal(t, int64(456), proto.CareTypeId)
	assert.Equal(t, int64(789), proto.CategoryId)
	assert.Equal(t, "Test Service", proto.Name)
	assert.Equal(t, "Test Description", *proto.Description)
	assert.Equal(t, "#FF0000", proto.ColorCode)
	assert.Equal(t, int64(10), proto.Sort)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, proto.Images)
	assert.Equal(t, offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM, proto.Source)
	assert.True(t, proto.IsActive)
	assert.NotNil(t, proto.CreateTime)
	assert.NotNil(t, proto.UpdateTime)
	assert.NotNil(t, proto.DeleteTime)
	assert.Equal(t, timestamppb.New(now).Seconds, proto.CreateTime.Seconds)
	assert.Equal(t, timestamppb.New(now).Seconds, proto.UpdateTime.Seconds)
	assert.Equal(t, timestamppb.New(deleteTime).Seconds, proto.DeleteTime.Seconds)

	// 测试无 DeleteTime
	entity.DeleteTime = nil
	proto = EntityToProto(entity)
	assert.NotNil(t, proto)
	assert.Nil(t, proto.DeleteTime)

	// 测试 nil 输入
	proto = EntityToProto(nil)
	assert.Nil(t, proto)
}

func TestEntityToModel(t *testing.T) {
	// 测试正常转换
	now := time.Now()
	deleteTime := now.Add(time.Hour)
	description := "Test Description"

	entity := &Service{
		ID:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       456,
		CategoryID:       789,
		Name:             "Test Service",
		Description:      &description,
		ColorCode:        "#FF0000",
		Sort:             10,
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM,
		IsActive:         true,
		CreateTime:       now,
		UpdateTime:       now,
		DeleteTime:       &deleteTime,
	}

	model := EntityToModel(entity)

	assert.NotNil(t, model)
	assert.Equal(t, int64(1), model.ID)
	assert.Equal(t, offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY, model.OrganizationType)
	assert.Equal(t, int64(123), model.OrganizationID)
	assert.Equal(t, int64(456), model.CareTypeID)
	assert.Equal(t, int64(789), model.CategoryID)
	assert.Equal(t, "Test Service", model.Name)
	assert.Equal(t, "Test Description", *model.Description)
	assert.Equal(t, "#FF0000", model.ColorCode)
	assert.Equal(t, int64(10), model.Sort)
	assert.Equal(t, `["image1.jpg","image2.jpg"]`, *model.Images)
	assert.Equal(t, offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM, model.Source)
	assert.True(t, model.IsActive)
	// EntityToModel 不设置 CreateTime 和 UpdateTime，所以不进行断言

	// 测试空字段
	entity.Description = nil
	entity.Images = nil
	entity.DeleteTime = nil

	model = EntityToModel(entity)
	assert.NotNil(t, model)
	assert.Nil(t, model.Description)
	assert.Nil(t, model.Images)
	assert.Nil(t, model.DeleteTime)

	// 测试 nil 输入
	model = EntityToModel(nil)
	assert.Nil(t, model)
}

func TestModelToEntity(t *testing.T) {
	// 准备测试数据
	now := time.Now()
	deleteTime := now.Add(time.Hour)
	description := "Test Description"

	// 测试正常转换
	m := &model.Service{
		ID:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       456,
		CategoryID:       789,
		Name:             "Test Service",
		Description:      &description,
		ColorCode:        "#FF0000",
		Sort:             10,
		Images:           lo.ToPtr(`["image1.jpg","image2.jpg"]`),
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM,
		IsActive:         true,
		CreateTime:       &now,
		UpdateTime:       &now,
		DeleteTime:       &deleteTime,
	}

	entity := ModelToEntity(m)

	assert.NotNil(t, entity)
	assert.Equal(t, int64(1), entity.ID)
	assert.Equal(t, offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY, entity.OrganizationType)
	assert.Equal(t, int64(123), entity.OrganizationID)
	assert.Equal(t, int64(456), entity.CareTypeID)
	assert.Equal(t, int64(789), entity.CategoryID)
	assert.Equal(t, "Test Service", entity.Name)
	assert.Equal(t, "Test Description", *entity.Description)
	assert.Equal(t, "#FF0000", entity.ColorCode)
	assert.Equal(t, int64(10), entity.Sort)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, entity.Images)
	assert.Equal(t, offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM, entity.Source)
	assert.True(t, entity.IsActive)
	assert.Equal(t, now, entity.CreateTime)
	assert.Equal(t, now, entity.UpdateTime)
	assert.Equal(t, &deleteTime, entity.DeleteTime)

	// 测试空字段
	m = &model.Service{
		ID:         1,
		IsActive:   false,
		Images:     lo.ToPtr("[]"),
		CreateTime: &now,
		UpdateTime: &now,
	}
	entity = ModelToEntity(m)
	assert.NotNil(t, entity)
	assert.Nil(t, entity.Description)
	assert.Empty(t, entity.Images)
	assert.Nil(t, entity.DeleteTime)

	// 测试 nil 输入
	entity = ModelToEntity(nil)
	assert.Nil(t, entity)
}

func TestModelToEntityList(t *testing.T) {
	// 准备测试数据
	now := time.Now()
	description1 := "Service 1"
	description2 := "Service 2"

	models := []*model.Service{
		{ID: 1, Name: "Service 1", Description: &description1, CreateTime: &now, UpdateTime: &now},
		{ID: 2, Name: "Service 2", Description: &description2, CreateTime: &now, UpdateTime: &now},
	}

	// 测试正常转换
	entities := ModelToEntityList(models)

	assert.NotNil(t, entities)
	assert.Len(t, entities, 2)
	assert.Equal(t, int64(1), entities[0].ID)
	assert.Equal(t, "Service 1", entities[0].Name)
	assert.Equal(t, "Service 1", *entities[0].Description)
	assert.Equal(t, int64(2), entities[1].ID)
	assert.Equal(t, "Service 2", entities[1].Name)
	assert.Equal(t, "Service 2", *entities[1].Description)

	// 测试空列表
	entities = ModelToEntityList([]*model.Service{})
	assert.Empty(t, entities)

	// 测试 nil 列表
	entities = ModelToEntityList(nil)
	assert.Empty(t, entities)
}

func TestEntityToProtoList(t *testing.T) {
	// 准备测试数据
	now := time.Now()
	description1 := "Service 1"
	description2 := "Service 2"
	entities := []*Service{
		{ID: 1, Name: "Service 1", Description: &description1, CreateTime: now, UpdateTime: now},
		{ID: 2, Name: "Service 2", Description: &description2, CreateTime: now, UpdateTime: now},
	}

	// 测试正常转换
	protos := EntityToProtoList(entities)

	assert.NotNil(t, protos)
	assert.Len(t, protos, 2)
	assert.Equal(t, int64(1), protos[0].Id)
	assert.Equal(t, "Service 1", protos[0].Name)
	assert.Equal(t, "Service 1", *protos[0].Description)
	assert.Equal(t, int64(2), protos[1].Id)
	assert.Equal(t, "Service 2", protos[1].Name)
	assert.Equal(t, "Service 2", *protos[1].Description)

	// 测试空列表
	protos = EntityToProtoList([]*Service{})
	assert.Empty(t, protos)

	// 测试 nil 列表
	protos = EntityToProtoList(nil)
	assert.Empty(t, protos)
}
