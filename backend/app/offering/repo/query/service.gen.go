// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
)

func newService(db *gorm.DB, opts ...gen.DOOption) service {
	_service := service{}

	_service.serviceDo.UseDB(db, opts...)
	_service.serviceDo.UseModel(&model.Service{})

	tableName := _service.serviceDo.TableName()
	_service.ALL = field.NewAsterisk(tableName)
	_service.ID = field.NewInt64(tableName, "id")
	_service.OrganizationType = field.NewField(tableName, "organization_type")
	_service.OrganizationID = field.NewInt64(tableName, "organization_id")
	_service.CareTypeID = field.NewInt64(tableName, "care_type_id")
	_service.CategoryID = field.NewInt64(tableName, "category_id")
	_service.Name = field.NewString(tableName, "name")
	_service.Description = field.NewString(tableName, "description")
	_service.ColorCode = field.NewString(tableName, "color_code")
	_service.Sort = field.NewInt64(tableName, "sort")
	_service.Images = field.NewString(tableName, "images")
	_service.Source = field.NewField(tableName, "source")
	_service.IsActive = field.NewBool(tableName, "is_active")
	_service.CreateTime = field.NewTime(tableName, "create_time")
	_service.UpdateTime = field.NewTime(tableName, "update_time")
	_service.DeleteTime = field.NewTime(tableName, "delete_time")

	_service.fillFieldMap()

	return _service
}

type service struct {
	serviceDo serviceDo

	ALL              field.Asterisk
	ID               field.Int64  // Primary key ID of the service
	OrganizationType field.Field  // Level of the organization: 1enterprise, 2company, 3business, etc.
	OrganizationID   field.Int64  // Organization ID corresponding to the type
	CareTypeID       field.Int64  // Reference to care_type used in this service
	CategoryID       field.Int64  // Optional category to organize services
	Name             field.String // Name of the service, unique within the same organization
	Description      field.String // Optional description of the service
	ColorCode        field.String // Color code for UI display, such as #F15A2B
	Sort             field.Int64  // Sort order for UI display
	Images           field.String // List of image URLs in JSON array
	Source           field.Field  // Source of the service: 1-MoeGo Platform 2-Enterprise Hub
	IsActive         field.Bool   // Whether the service is currently active
	CreateTime       field.Time
	UpdateTime       field.Time
	DeleteTime       field.Time

	fieldMap map[string]field.Expr
}

func (s service) Table(newTableName string) *service {
	s.serviceDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s service) As(alias string) *service {
	s.serviceDo.DO = *(s.serviceDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *service) updateTableName(table string) *service {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.OrganizationType = field.NewField(table, "organization_type")
	s.OrganizationID = field.NewInt64(table, "organization_id")
	s.CareTypeID = field.NewInt64(table, "care_type_id")
	s.CategoryID = field.NewInt64(table, "category_id")
	s.Name = field.NewString(table, "name")
	s.Description = field.NewString(table, "description")
	s.ColorCode = field.NewString(table, "color_code")
	s.Sort = field.NewInt64(table, "sort")
	s.Images = field.NewString(table, "images")
	s.Source = field.NewField(table, "source")
	s.IsActive = field.NewBool(table, "is_active")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")
	s.DeleteTime = field.NewTime(table, "delete_time")

	s.fillFieldMap()

	return s
}

func (s *service) WithContext(ctx context.Context) *serviceDo { return s.serviceDo.WithContext(ctx) }

func (s service) TableName() string { return s.serviceDo.TableName() }

func (s service) Alias() string { return s.serviceDo.Alias() }

func (s service) Columns(cols ...field.Expr) gen.Columns { return s.serviceDo.Columns(cols...) }

func (s *service) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *service) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 15)
	s.fieldMap["id"] = s.ID
	s.fieldMap["organization_type"] = s.OrganizationType
	s.fieldMap["organization_id"] = s.OrganizationID
	s.fieldMap["care_type_id"] = s.CareTypeID
	s.fieldMap["category_id"] = s.CategoryID
	s.fieldMap["name"] = s.Name
	s.fieldMap["description"] = s.Description
	s.fieldMap["color_code"] = s.ColorCode
	s.fieldMap["sort"] = s.Sort
	s.fieldMap["images"] = s.Images
	s.fieldMap["source"] = s.Source
	s.fieldMap["is_active"] = s.IsActive
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["delete_time"] = s.DeleteTime
}

func (s service) clone(db *gorm.DB) service {
	s.serviceDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s service) replaceDB(db *gorm.DB) service {
	s.serviceDo.ReplaceDB(db)
	return s
}

type serviceDo struct{ gen.DO }

func (s serviceDo) Debug() *serviceDo {
	return s.withDO(s.DO.Debug())
}

func (s serviceDo) WithContext(ctx context.Context) *serviceDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s serviceDo) ReadDB() *serviceDo {
	return s.Clauses(dbresolver.Read)
}

func (s serviceDo) WriteDB() *serviceDo {
	return s.Clauses(dbresolver.Write)
}

func (s serviceDo) Session(config *gorm.Session) *serviceDo {
	return s.withDO(s.DO.Session(config))
}

func (s serviceDo) Clauses(conds ...clause.Expression) *serviceDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s serviceDo) Returning(value interface{}, columns ...string) *serviceDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s serviceDo) Not(conds ...gen.Condition) *serviceDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s serviceDo) Or(conds ...gen.Condition) *serviceDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s serviceDo) Select(conds ...field.Expr) *serviceDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s serviceDo) Where(conds ...gen.Condition) *serviceDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s serviceDo) Order(conds ...field.Expr) *serviceDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s serviceDo) Distinct(cols ...field.Expr) *serviceDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s serviceDo) Omit(cols ...field.Expr) *serviceDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s serviceDo) Join(table schema.Tabler, on ...field.Expr) *serviceDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s serviceDo) LeftJoin(table schema.Tabler, on ...field.Expr) *serviceDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s serviceDo) RightJoin(table schema.Tabler, on ...field.Expr) *serviceDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s serviceDo) Group(cols ...field.Expr) *serviceDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s serviceDo) Having(conds ...gen.Condition) *serviceDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s serviceDo) Limit(limit int) *serviceDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s serviceDo) Offset(offset int) *serviceDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s serviceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *serviceDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s serviceDo) Unscoped() *serviceDo {
	return s.withDO(s.DO.Unscoped())
}

func (s serviceDo) Create(values ...*model.Service) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s serviceDo) CreateInBatches(values []*model.Service, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s serviceDo) Save(values ...*model.Service) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s serviceDo) First() (*model.Service, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Service), nil
	}
}

func (s serviceDo) Take() (*model.Service, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Service), nil
	}
}

func (s serviceDo) Last() (*model.Service, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Service), nil
	}
}

func (s serviceDo) Find() ([]*model.Service, error) {
	result, err := s.DO.Find()
	return result.([]*model.Service), err
}

func (s serviceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Service, err error) {
	buf := make([]*model.Service, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s serviceDo) FindInBatches(result *[]*model.Service, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s serviceDo) Attrs(attrs ...field.AssignExpr) *serviceDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s serviceDo) Assign(attrs ...field.AssignExpr) *serviceDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s serviceDo) Joins(fields ...field.RelationField) *serviceDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s serviceDo) Preload(fields ...field.RelationField) *serviceDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s serviceDo) FirstOrInit() (*model.Service, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Service), nil
	}
}

func (s serviceDo) FirstOrCreate() (*model.Service, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Service), nil
	}
}

func (s serviceDo) FindByPage(offset int, limit int) (result []*model.Service, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s serviceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s serviceDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s serviceDo) Delete(models ...*model.Service) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *serviceDo) withDO(do gen.Dao) *serviceDo {
	s.DO = *do.(*gen.DO)
	return s
}
