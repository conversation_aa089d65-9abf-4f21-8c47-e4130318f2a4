package caretypeattribute

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/query"
)

const (
	defaultLimit  = 100
	defaultOffset = 0
	batchSize     = 100
)

//go:generate mockgen -package=mock -destination=mocks/mock_care_type_attribute_repo.go . Repository
type Repository interface {
	WithQuery(q *query.Query) Repository

	BatchCreate(ctx context.Context, careTypeAttributes []*model.CareTypeAttribute) error
	Create(ctx context.Context, careTypeAttribute *model.CareTypeAttribute) error
	Get(ctx context.Context, id int64) (*model.CareTypeAttribute, error)
	Update(ctx context.Context, careTypeAttribute *model.CareTypeAttribute) error
	Delete(ctx context.Context, id int64) error
	DeleteByCareTypeID(ctx context.Context, careTypeID int64) (int64, error)
	List(ctx context.Context, filter *ListCareTypeAttributeFilter) ([]*model.CareTypeAttribute, error)
}

// repository implements the data access logic for model.CareTypeAttribute.
type repository struct {
	query *query.Query
}

// NewRepository creates a new repository.
func NewRepository() Repository {
	return &repository{query: query.Use(db.GetDB())}
}

// WithQuery sets the query for the repository, useful for multi operation in one transaction.
func (r *repository) WithQuery(q *query.Query) Repository {
	return &repository{query: q}
}

// BatchCreate creates multiple care type attributes.
func (r *repository) BatchCreate(ctx context.Context, careTypeAttributes []*model.CareTypeAttribute) error {
	return r.query.CareTypeAttribute.WithContext(ctx).CreateInBatches(careTypeAttributes, batchSize)
}

// Create creates a new care type attribute.
func (r *repository) Create(ctx context.Context, careTypeAttribute *model.CareTypeAttribute) error {
	return r.query.CareTypeAttribute.WithContext(ctx).Create(careTypeAttribute)
}

// Get gets a care type attribute by ID.
func (r *repository) Get(ctx context.Context, id int64) (*model.CareTypeAttribute, error) {
	return r.query.CareTypeAttribute.WithContext(ctx).Where(r.query.CareTypeAttribute.ID.Eq(id)).First()
}

// Update updates a care type attribute.
func (r *repository) Update(ctx context.Context, careTypeAttribute *model.CareTypeAttribute) error {
	_, err := r.query.CareTypeAttribute.WithContext(ctx).
		Where(r.query.CareTypeAttribute.ID.Eq(careTypeAttribute.ID)).Updates(careTypeAttribute)
	if err != nil {
		return err
	}

	return nil
}

// Delete deletes a care type attribute by ID.
func (r *repository) Delete(ctx context.Context, id int64) error {
	result, err := r.query.CareTypeAttribute.WithContext(ctx).Where(r.query.CareTypeAttribute.ID.Eq(id)).Delete()
	if err != nil {
		return err
	}

	return result.Error
}

// DeleteByCareTypeID deletes all care type attributes by care type ID.
func (r *repository) DeleteByCareTypeID(ctx context.Context, careTypeID int64) (int64, error) {
	result, err := r.query.CareTypeAttribute.WithContext(ctx).
		Where(r.query.CareTypeAttribute.CareTypeID.Eq(careTypeID)).Delete()
	if err != nil {
		return 0, err
	}

	return result.RowsAffected, nil
}

// List lists care type attributes.
func (r *repository) List(
	ctx context.Context, filter *ListCareTypeAttributeFilter) ([]*model.CareTypeAttribute, error) {
	if filter == nil {
		return []*model.CareTypeAttribute{}, nil
	}
	q := r.query.CareTypeAttribute.WithContext(ctx).
		Where(r.query.CareTypeAttribute.CareTypeID.Eq(filter.CareTypeID))
	if filter.IsRequired != nil {
		q = q.Where(r.query.CareTypeAttribute.IsRequired.Is(*filter.IsRequired))
	}

	return q.Limit(defaultLimit).Offset(defaultOffset).
		Order(r.query.CareTypeAttribute.CreateTime.Asc()).Find()
}
