// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego/backend/app/offering/repo/caretypeattribute (interfaces: Repository)
//
// Generated by this command:
//
//	mockgen -package=mock -destination=mocks/mock_care_type_attribute_repo.go . Repository
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	caretypeattribute "github.com/MoeGolibrary/moego/backend/app/offering/repo/caretypeattribute"
	model "github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
	query "github.com/MoeGolibrary/moego/backend/app/offering/repo/query"
	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockRepository) BatchCreate(ctx context.Context, careTypeAttributes []*model.CareTypeAttribute) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", ctx, careTypeAttributes)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockRepositoryMockRecorder) BatchCreate(ctx, careTypeAttributes any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockRepository)(nil).BatchCreate), ctx, careTypeAttributes)
}

// Create mocks base method.
func (m *MockRepository) Create(ctx context.Context, careTypeAttribute *model.CareTypeAttribute) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, careTypeAttribute)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockRepositoryMockRecorder) Create(ctx, careTypeAttribute any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRepository)(nil).Create), ctx, careTypeAttribute)
}

// Delete mocks base method.
func (m *MockRepository) Delete(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockRepositoryMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockRepository)(nil).Delete), ctx, id)
}

// DeleteByCareTypeID mocks base method.
func (m *MockRepository) DeleteByCareTypeID(ctx context.Context, careTypeID int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByCareTypeID", ctx, careTypeID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteByCareTypeID indicates an expected call of DeleteByCareTypeID.
func (mr *MockRepositoryMockRecorder) DeleteByCareTypeID(ctx, careTypeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByCareTypeID", reflect.TypeOf((*MockRepository)(nil).DeleteByCareTypeID), ctx, careTypeID)
}

// Get mocks base method.
func (m *MockRepository) Get(ctx context.Context, id int64) (*model.CareTypeAttribute, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*model.CareTypeAttribute)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockRepositoryMockRecorder) Get(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRepository)(nil).Get), ctx, id)
}

// List mocks base method.
func (m *MockRepository) List(ctx context.Context, filter *caretypeattribute.ListCareTypeAttributeFilter) ([]*model.CareTypeAttribute, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, filter)
	ret0, _ := ret[0].([]*model.CareTypeAttribute)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockRepositoryMockRecorder) List(ctx, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockRepository)(nil).List), ctx, filter)
}

// Update mocks base method.
func (m *MockRepository) Update(ctx context.Context, careTypeAttribute *model.CareTypeAttribute) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, careTypeAttribute)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockRepositoryMockRecorder) Update(ctx, careTypeAttribute any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRepository)(nil).Update), ctx, careTypeAttribute)
}

// WithQuery mocks base method.
func (m *MockRepository) WithQuery(q *query.Query) caretypeattribute.Repository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithQuery", q)
	ret0, _ := ret[0].(caretypeattribute.Repository)
	return ret0
}

// WithQuery indicates an expected call of WithQuery.
func (mr *MockRepositoryMockRecorder) WithQuery(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithQuery", reflect.TypeOf((*MockRepository)(nil).WithQuery), q)
}
