load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mocks",
    srcs = ["mock_care_type_attribute_repo.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/caretypeattribute/mocks",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/caretypeattribute",
        "//backend/app/offering/repo/model",
        "//backend/app/offering/repo/query",
        "@org_uber_go_mock//gomock",
    ],
)
