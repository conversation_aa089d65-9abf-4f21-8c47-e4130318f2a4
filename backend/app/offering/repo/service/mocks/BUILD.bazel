load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mocks",
    srcs = ["mock_service_repo.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/service/mocks",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/model",
        "//backend/app/offering/repo/query",
        "//backend/app/offering/repo/service",
        "@org_uber_go_mock//gomock",
    ],
)
