// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	"time"
)

const TableNameCareTypeAttribute = "care_type_attribute"

// CareTypeAttribute mapped from table <care_type_attribute>
type CareTypeAttribute struct {
	ID           int64                   `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:Primary key ID of the care type attribute entry" json:"id"`                                  // Primary key ID of the care type attribute entry
	CareTypeID   int64                   `gorm:"column:care_type_id;type:bigint;not null;comment:Care type to which this attribute is assigned" json:"care_type_id"`                                     // Care type to which this attribute is assigned
	AttributeKey offeringpb.AttributeKey `gorm:"column:attribute_key;type:integer;not null;comment:Unique name (key) of the attribute within the care type (e.g., size, duration)" json:"attribute_key"` // Unique name (key) of the attribute within the care type (e.g., size, duration)
	Label        *string                 `gorm:"column:label;type:text;comment:Display label for frontend UI (e.g., Pet Size)" json:"label"`                                                             // Display label for frontend UI (e.g., Pet Size)
	ValueType    offeringpb.ValueType    `gorm:"column:value_type;type:integer;not null;comment:Value type of the attribute (1STRING, 2NUMBER, 3BOOLEAN, 4ENUM)" json:"value_type"`                      // Value type of the attribute (1STRING, 2NUMBER, 3BOOLEAN, 4ENUM)
	Options      *string                 `gorm:"column:options;type:jsonb;comment:JSON list of options (used when value_type is ENUM or BOOLEAN)" json:"options"`                                        // JSON list of options (used when value_type is ENUM or BOOLEAN)
	Description  *string                 `gorm:"column:description;type:text;comment:Optional explanation or usage hint" json:"description"`                                                             // Optional explanation or usage hint
	IsRequired   bool                    `gorm:"column:is_required;type:boolean;not null;comment:Whether this attribute is required for the care type" json:"is_required"`                               // Whether this attribute is required for the care type
	DefaultValue string                  `gorm:"column:default_value;type:text;not null;comment:Default value assigned to this attribute" json:"default_value"`                                          // Default value assigned to this attribute
	CreateTime   *time.Time              `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	UpdateTime   *time.Time              `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
	DeleteTime   *time.Time              `gorm:"column:delete_time;type:timestamp with time zone" json:"delete_time"`
}

// TableName CareTypeAttribute's table name
func (*CareTypeAttribute) TableName() string {
	return TableNameCareTypeAttribute
}
