// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	"time"
)

const TableNameCareType = "care_type"

// CareType mapped from table <care_type>
type CareType struct {
	ID               int64                       `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:Primary key ID of the care type" json:"id"`                                                     // Primary key ID of the care type
	OrganizationType offeringpb.OrganizationType `gorm:"column:organization_type;type:integer;not null;comment:Level of the organization: 1enterprise, 2company, 3business, etc." json:"organization_type"`         // Level of the organization: 1enterprise, 2company, 3business, etc.
	OrganizationID   int64                       `gorm:"column:organization_id;type:bigint;not null;comment:ID of the organization unit corresponding to the organization_type" json:"organization_id"`             // ID of the organization unit corresponding to the organization_type
	Name             string                      `gorm:"column:name;type:character varying(50);not null;comment:Name of the care type, unique within the same organization" json:"name"`                            // Name of the care type, unique within the same organization
	CareCategory     offeringpb.CareCategory     `gorm:"column:care_category;type:integer;not null;comment:System code of the care category (e.g. 1grooming, 2boarding, 3boarding, 99custom)" json:"care_category"` // System code of the care category (e.g. 1grooming, 2boarding, 3boarding, 99custom)
	Description      *string                     `gorm:"column:description;type:character varying(512);default:NULL;comment:Optional description of the care type" json:"description"`                              // Optional description of the care type
	CreateTime       *time.Time                  `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	UpdateTime       *time.Time                  `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
	DeleteTime       *time.Time                  `gorm:"column:delete_time;type:timestamp with time zone" json:"delete_time"`
}

// TableName CareType's table name
func (*CareType) TableName() string {
	return TableNameCareType
}
