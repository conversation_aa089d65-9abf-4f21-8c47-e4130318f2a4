package main

import (
	"gorm.io/driver/postgres"
	"gorm.io/gen"
	"gorm.io/gorm"
)

// Dynamic SQL
//type Querier interface {
//	// SELECT * FROM @@table WHERE name = @name{{if role !=""}} AND role = @role{{end}}
//	FilterWithNameAndRole(name, role string) ([]gen.T, error)
//}

func main() {
	config := gen.Config{
		OutPath:           "./backend/app/offering/repo/query/",
		ModelPkgPath:      "./backend/app/offering/repo/model/",
		FieldWithIndexTag: true, // Generate field with `gorm:"index"` tag for index columns
		FieldWithTypeTag:  true, // Generate field with `gorm:"type"` tag for data type columns
		FieldNullable:     true, // 数据库中的字段可为空，则生成struct字段为指针类型
		// 见：https://gorm.io/docs/create.html#Default-Values
		FieldCoverable: true, // 如果数据库中字段有默认值，则生成指针类型的字段，以避免零值（zero-value）问题
	}
	g := gen.NewGenerator(config)

	db, _ := gorm.Open(postgres.Open("postgresql://moego_developer_240310_eff7a0dc:G0MxI7NM_jX_f7Ky73vnrwej97xg1tly" +
		"@postgres.t2.moego.dev:40132/moego_offering?sslmode=disable"))
	// gormdb, _ := gorm.Open(mysql.Open("root:@(127.0.0.1:3306)/demo?charset=utf8mb4&parseTime=True&loc=Local"))
	g.UseDB(db) // reuse your gorm db

	// Generate basic type-safe DAO API for struct `model.User` following conventions
	careType := g.GenerateModel("care_type",
		gen.FieldType("organization_type", "offeringpb.OrganizationType"),
		gen.FieldType("care_category", "offeringpb.CareCategory"),
	)
	careTypeAttribute := g.GenerateModel("care_type_attribute",
		gen.FieldType("organization_type", "offeringpb.OrganizationType"),
		gen.FieldType("attribute_key", "offeringpb.AttributeKey"),
		gen.FieldType("value_type", "offeringpb.ValueType"),
	)
	service := g.GenerateModel("service",
		gen.FieldType("organization_type", "offeringpb.OrganizationType"),
		gen.FieldType("source", "offeringpb.ServiceSource"),
	)
	serviceAttributeValue := g.GenerateModel("service_attribute",
		gen.FieldType("attribute_key", "offeringpb.AttributeKey"),
	)

	g.ApplyBasic(careType, careTypeAttribute, service, serviceAttributeValue)

	// Generate Type Safe API with Dynamic SQL defined on Querier interface for `model.User` and `model.Company`
	// g.ApplyInterface(func(Querier) {}, model.User{}, model.Company{})

	// Generate the code
	g.Execute()
}

//go:generate go run gorm_gen.go
