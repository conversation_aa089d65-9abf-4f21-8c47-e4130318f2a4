package customerutils

import (
	"encoding/base64"

	"github.com/bytedance/sonic"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
)

func DecodeCursor(cursor string) *postgres.Cursor {
	if cursor == "" {
		return nil
	}
	bytes, err := base64.StdEncoding.DecodeString(cursor)
	if err != nil {
		return nil
	}
	c := &postgres.Cursor{}
	_ = sonic.Unmarshal(bytes, c)

	return c
}

func EncodeCursor(cursor *postgres.Cursor) string {
	bytes, err := sonic.Marshal(cursor)
	if err != nil {
		return ""
	}

	return base64.StdEncoding.EncodeToString(bytes)
}
