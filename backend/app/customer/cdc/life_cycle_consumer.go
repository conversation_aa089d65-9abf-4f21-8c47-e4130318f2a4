package cdc

import (
	"context"
	"time"

	"github.com/IBM/sarama"
	"github.com/bytedance/sonic"

	lifecycle "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/life_cycle"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type LifeCycleConsumer struct {
	lifeCycleRepo lifecycle.ReadWriter
}

func NewLifeCycleConsumer() *LifeCycleConsumer {
	return &LifeCycleConsumer{
		lifeCycleRepo: lifecycle.New(),
	}
}

func (c *LifeCycleConsumer) LifeCycleEventHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	log.InfoContextf(ctx, "LifeCycleEventHandler get message, key:%s, value:%s", string(msg.Key), string(msg.Value))

	// 2. 反序列化
	var cdcMsg LifeCycleMessage
	if err := sonic.Unmarshal(msg.Value, &cdcMsg); err != nil {
		log.ErrorContextf(ctx, "LifeCycleEventHandler unmarshal error: %v", err)

		return err
	}

	// 4. 根据 op 类型分发
	switch cdcMsg.Op {
	case "c", "r":
		// insert or snapshot read
		if cdcMsg.After == nil {
			log.InfoContextf(ctx, "CDC create op but after is nil")

			return nil
		}
		model := toLifeCycleModel(cdcMsg.After)
		if err := c.lifeCycleRepo.Create(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC CreateLifeCycle error: %v", err)

			return err
		}
		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, model.ID)
	case "u":
		// update
		if cdcMsg.After == nil {
			log.InfoContextf(ctx, "CDC update op but after is nil")

			return nil
		}
		model := toLifeCycleModel(cdcMsg.After)
		if err := c.lifeCycleRepo.Update(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC UpdateLifeCycle error: %v", err)

			return err
		}
		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, model.ID)
	case "d":
		// delete
		var id int64
		var staffID int64
		if cdcMsg.Before != nil {
			id = cdcMsg.Before.ID
			staffID = cdcMsg.Before.UpdatedBy
		} else if cdcMsg.After != nil {
			id = cdcMsg.After.ID
			staffID = cdcMsg.After.UpdatedBy
		}
		if id == 0 {
			log.InfoContextf(ctx, "CDC delete op but id is 0")

			return nil
		}
		if err := c.lifeCycleRepo.Delete(ctx, id, staffID); err != nil {
			log.ErrorContextf(ctx, "CDC DeleteLifeCycle error: %v", err)

			return err
		}
		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, id)
	default:
		log.InfoContextf(ctx, "CDC unknown op: %s", cdcMsg.Op)
	}

	return nil
}

// 3. 工具函数：将 LifeCycleRow 转为 CustomerLifeCycle
func toLifeCycleModel(row *LifeCycleRow) *lifecycle.CustomerLifeCycle {
	if row == nil {
		return nil
	}
	model := &lifecycle.CustomerLifeCycle{
		ID:         row.ID,
		CompanyID:  row.CompanyID,
		BusinessID: row.BusinessID,
		CreatedBy:  row.CreatedBy,
		UpdatedBy:  row.UpdatedBy,
		DeletedBy:  row.DeletedBy,
		Name:       row.Name,
		Sort:       row.Sort,
		IsDefault:  row.IsDefault,
	}
	model.CreatedAt = time.UnixMilli(row.CreatedAt)
	model.UpdatedAt = time.UnixMilli(row.UpdatedAt)
	if row.DeletedAt != nil {
		t := time.UnixMilli(*row.DeletedAt)
		model.DeletedAt = &t
	}

	return model
}
