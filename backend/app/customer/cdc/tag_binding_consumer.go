package cdc

import (
	"context"
	"time"

	"github.com/IBM/sarama"
	"github.com/bytedance/sonic"

	activityrel "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/activity_rel"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type TagBindingConsumer struct {
	activityRelRepo activityrel.ReadWriter
}

func NewTagBindingConsumer() *TagBindingConsumer {
	return &TagBindingConsumer{
		activityRelRepo: activityrel.New(),
	}
}

func (c *TagBindingConsumer) TagBindingEventHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	log.InfoContextf(ctx, "TagBindingEventHandler get message, key:%s, value:%s", string(msg.Key), string(msg.Value))

	var cdcMsg TagBindingMessage
	if err := sonic.Unmarshal(msg.Value, &cdcMsg); err != nil {
		log.ErrorContextf(ctx, "TagBindingEventHandler unmarshal error: %v", err)

		return err
	}

	switch cdcMsg.Op {
	case "c", "r":
		if cdcMsg.After == nil {
			log.InfoContextf(ctx, "CDC create op but after is nil")

			return nil
		}
		model := toTagBindingModel(cdcMsg.After)
		if model.ID != 0 {
			list, err := c.activityRelRepo.List(ctx, &activityrel.ListActivityRelDatum{
				ID: &model.ID,
			})
			if err != nil {
				return err
			}
			if len(list) > 0 {
				log.InfoContextf(ctx, "CDC tag binding id exist, op=%s id=%d", cdcMsg.Op, model.ID)

				return nil
			}
		}
		list, err := c.activityRelRepo.List(ctx, &activityrel.ListActivityRelDatum{
			CustomerID:   &model.CustomerID,
			ActivityID:   &model.ActivityID,
			ActivityType: &model.ActivityType,
		})
		if err != nil {
			return err
		}
		if len(list) > 0 {
			log.InfoContextf(ctx, "CDC tag binding exist, op=%s model=%v", cdcMsg.Op, model)

			return nil
		}
		if err := c.activityRelRepo.Create(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC CreateTagBinding error: %v", err)

			return err
		}
		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, model.ID)
	case "u":
		if cdcMsg.After == nil {
			log.InfoContextf(ctx, "CDC update op but after is nil")

			return nil
		}
		model := toTagBindingModel(cdcMsg.After)
		if err := c.activityRelRepo.Update(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC UpdateTagBinding error: %v", err)

			return err
		}
		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, model.ID)
	case "d":
		var id int64
		if cdcMsg.Before != nil {
			id = cdcMsg.Before.ID
		} else if cdcMsg.After != nil {
			id = cdcMsg.After.ID
		}
		if id == 0 {
			log.InfoContextf(ctx, "CDC delete op but id is 0")

			return nil
		}
		// staffID 可选，如有需要可补充
		if err := c.activityRelRepo.Delete(ctx, id, 0); err != nil {
			log.ErrorContextf(ctx, "CDC DeleteTagBinding error: %v", err)

			return err
		}
		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, id)
	default:
		log.InfoContextf(ctx, "CDC unknown op: %s", cdcMsg.Op)
	}

	return nil
}

// 工具函数：将 TagBindingRow 转为 ActivityRel entity
func toTagBindingModel(row *TagBindingRow) *activityrel.ActivityRel {
	if row == nil {
		return nil
	}

	return &activityrel.ActivityRel{
		ID:           row.ID,
		CustomerID:   row.CustomerID,
		ActivityID:   row.CustomerTagID,
		ActivityType: activityrel.TagActivityRelType, // 如有业务类型区分可调整
		CreateTime:   time.Now(),
		UpdateTime:   time.Now(),
	}
}
