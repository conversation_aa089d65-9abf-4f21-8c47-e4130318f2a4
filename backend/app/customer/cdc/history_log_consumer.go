package cdc

import (
	"context"
	"time"

	"github.com/IBM/sarama"
	"github.com/bytedance/sonic"
	"google.golang.org/protobuf/encoding/protojson"

	activitylog "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/activity_log"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type HistoryLogConsumer struct {
	activityLogRepo activitylog.ReadWriter
}

func NewHistoryLogConsumer() *HistoryLogConsumer {
	return &HistoryLogConsumer{
		activityLogRepo: activitylog.New(),
	}
}

func (c *HistoryLogConsumer) HistoryLogEventHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	log.InfoContextf(ctx, "HistoryLogEventHandler get message, key:%s, value:%s", string(msg.Key), string(msg.Value))

	var cdcMsg HistoryLogMessage
	if err := sonic.Unmarshal(msg.Value, &cdcMsg); err != nil {
		log.ErrorContextf(ctx, "HistoryLogEventHandler unmarshal error: %v", err)

		return err
	}

	switch cdcMsg.Op {
	case "c", "r":
		if cdcMsg.After == nil {
			log.InfoContextf(ctx, "CDC create op but after is nil")

			return nil
		}
		model, err := toActivityLogModel(cdcMsg.After)
		if err != nil {
			log.ErrorContextf(ctx, "CDC toActivityLogModel error: %v, cdcMsg.After:%+v", err, cdcMsg.After)

			return err
		}
		if err := c.activityLogRepo.Create(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC CreateHistoryLog error: %v", err)

			return err
		}
		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, model.ID)
	case "u":
		if cdcMsg.After == nil {
			log.InfoContextf(ctx, "CDC update op but after is nil")

			return nil
		}
		model, err := toActivityLogModel(cdcMsg.After)
		if err != nil {
			log.ErrorContextf(ctx, "CDC toActivityLogModel error: %v, cdcMsg.After:%+v", err, cdcMsg.After)

			return err
		}
		if err := c.activityLogRepo.Update(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC UpdateHistoryLog error: %v", err)

			return err
		}
		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, model.ID)
	default:
		log.InfoContextf(ctx, "CDC unknown op: %s", cdcMsg.Op)
	}

	return nil
}

func toActivityLogModel(row *HistoryLogRow) (*activitylog.ActivityLog, error) {
	if row == nil {
		return nil, nil
	}
	action, err := parseActionProto(row.Action)
	if err != nil {
		return nil, err
	}
	model := &activitylog.ActivityLog{
		ID:                  row.ID,
		CompanyID:           row.CompanyID,
		BusinessID:          row.BusinessID,
		CustomerID:          row.CustomerID,
		CustomerName:        row.CustomerName,
		CustomerPhoneNumber: row.CustomerPhoneNumber,
		Type:                customerpb.ActivityLog_Type(customerpb.ActivityLog_Type_value[row.Type]),
		Action:              action,
		Source:              convSystemSource(row.Source, row.SourceID, row.SourceName),
	}
	model.CreateTime = time.UnixMilli(row.CreateTime)
	model.UpdateTime = time.UnixMilli(row.UpdateTime)

	return model, nil
}

func convSystemSource(source string, sourceID int64, sourceName string) *customerpb.SystemSource {
	return &customerpb.SystemSource{
		Source:     customerpb.SystemSource_Source(customerpb.SystemSource_Source_value[source]),
		SourceId:   sourceID,
		SourceName: sourceName,
	}
}

func parseActionProto(actionStr string) (*customerpb.ActivityLog_Action, error) {
	if actionStr == "" {
		return nil, nil
	}
	var act customerpb.ActivityLog_Action
	if err := protojson.Unmarshal([]byte(actionStr), &act); err != nil {
		return nil, err
	}

	return &act, nil
}
