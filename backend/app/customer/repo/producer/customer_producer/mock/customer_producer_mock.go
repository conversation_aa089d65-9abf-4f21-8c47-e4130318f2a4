// Code generated by MockGen. DO NOT EDIT.
// Source: ./producer/customer_producer/customer_producer.go
//
// Generated by this command:
//
//	mockgen -source=./producer/customer_producer/customer_producer.go -destination=./producer/customer_producer/mock/customer_producer_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	customerproducer "github.com/MoeGolibrary/moego/backend/app/customer/repo/producer/customer_producer"
	gomock "go.uber.org/mock/gomock"
)

// MockAPI is a mock of API interface.
type MockAPI struct {
	ctrl     *gomock.Controller
	recorder *MockAPIMockRecorder
	isgomock struct{}
}

// MockAPIMockRecorder is the mock recorder for MockAPI.
type MockAPIMockRecorder struct {
	mock *MockAPI
}

// NewMockAPI creates a new mock instance.
func NewMockAPI(ctrl *gomock.Controller) *MockAPI {
	mock := &MockAPI{ctrl: ctrl}
	mock.recorder = &MockAPIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAPI) EXPECT() *MockAPIMockRecorder {
	return m.recorder
}

// SendCustomerCreateMessage mocks base method.
func (m *MockAPI) SendCustomerCreateMessage(ctx context.Context, datum *customerproducer.CustomerCreateMessageDatum) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendCustomerCreateMessage", ctx, datum)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendCustomerCreateMessage indicates an expected call of SendCustomerCreateMessage.
func (mr *MockAPIMockRecorder) SendCustomerCreateMessage(ctx, datum any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCustomerCreateMessage", reflect.TypeOf((*MockAPI)(nil).SendCustomerCreateMessage), ctx, datum)
}
