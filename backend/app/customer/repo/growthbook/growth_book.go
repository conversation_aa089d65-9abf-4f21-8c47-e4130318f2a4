package growthbook

import (
	"context"

	"github.com/bytedance/sonic"
	growthbook "github.com/growthbook/growthbook-golang"

	gb "github.com/MoeGolibrary/moego/backend/common/rpc/config/growthbook"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type API interface {
	GetK9CompanyIDList(ctx context.Context) []int64
}

type impl struct {
	cli *growthbook.Client
}

func New() API {
	return &impl{
		cli: gb.GrowthBookClient.Client,
	}
}

func (i *impl) GetK9CompanyIDList(ctx context.Context) []int64 {
	feature, ok := i.cli.EvalFeature(ctx, "k9_company_ids").Value.(string)
	if !ok {
		return nil
	}

	var res []int64
	if err := sonic.Unmarshal([]byte(feature), &res); err != nil {
		log.ErrorContextf(ctx, "GetK9CompanyIDList err, err:%+v", err)

		return nil
	}

	log.InfoContextf(ctx, "GetK9CompanyIDList res:%+v", res)

	return res
}
