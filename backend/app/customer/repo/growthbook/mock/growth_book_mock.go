// Code generated by MockGen. DO NOT EDIT.
// Source: ./growthbook/growth_book.go
//
// Generated by this command:
//
//	mockgen -source=./growthbook/growth_book.go -destination=./growthbook/mock/growth_book_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockAPI is a mock of API interface.
type MockAPI struct {
	ctrl     *gomock.Controller
	recorder *MockAPIMockRecorder
	isgomock struct{}
}

// MockAPIMockRecorder is the mock recorder for MockAPI.
type MockAPIMockRecorder struct {
	mock *MockAPI
}

// NewMockAPI creates a new mock instance.
func NewMockAPI(ctrl *gomock.Controller) *MockAPI {
	mock := &MockAPI{ctrl: ctrl}
	mock.recorder = &MockAPIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAPI) EXPECT() *MockAPIMockRecorder {
	return m.recorder
}

// GetK9CompanyIDList mocks base method.
func (m *MockAPI) GetK9CompanyIDList(ctx context.Context) []int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetK9CompanyIDList", ctx)
	ret0, _ := ret[0].([]int64)
	return ret0
}

// GetK9CompanyIDList indicates an expected call of GetK9CompanyIDList.
func (mr *MockAPIMockRecorder) GetK9CompanyIDList(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetK9CompanyIDList", reflect.TypeOf((*MockAPI)(nil).GetK9CompanyIDList), ctx)
}
