package actionstate

import "time"

type CustomerActionState struct {
	ID         int64      `gorm:"column:id"`
	CompanyID  int64      `gorm:"column:company_id"`
	BusinessID int64      `gorm:"column:business_id"`
	CreatedBy  int64      `gorm:"column:created_by"`
	UpdatedBy  int64      `gorm:"column:updated_by"`
	DeletedBy  *int64     `gorm:"column:deleted_by"`
	CreatedAt  time.Time  `gorm:"column:created_at"`
	UpdatedAt  time.Time  `gorm:"column:updated_at"`
	DeletedAt  *time.Time `gorm:"column:deleted_at"`
	Name       string     `gorm:"column:name"`
	Sort       int32      `gorm:"column:sort"`
	Color      string     `gorm:"column:color"`
}

// TableName sets the insert table name for this struct type
func (CustomerActionState) TableName() string {
	return "action_state"
}
