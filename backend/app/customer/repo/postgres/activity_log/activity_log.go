package activitylog

import (
	"context"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	Create(context context.Context, historyLog *ActivityLog) error
	List(context context.Context, datum *ListActivityLogsDatum) ([]*ActivityLog, int64, error)
	Get(context context.Context, id int64) (*ActivityLog, error)
	Update(ctx context.Context, historyLog *ActivityLog) error
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) Create(ctx context.Context, historyLog *ActivityLog) error {
	if err := i.db.WithContext(ctx).Create(historyLog).Error; err != nil {
		log.Error(ctx, "Create ActivityLog err", zap.Error(err))

		return err
	}

	return nil
}

func (i *impl) List(ctx context.Context, datum *ListActivityLogsDatum) ([]*ActivityLog, int64, error) {
	query := i.db.WithContext(ctx).Table("activity_log")
	// filter
	if datum.CustomerID != nil && *datum.CustomerID != 0 {
		query = query.Where("customer_id = ?", datum.CustomerID)
	}
	if datum.ActivityLogType != nil {
		query = query.Where("type = ?", datum.ActivityLogType.String())
	}
	if datum.CompanyID != nil {
		query = query.Where("company_id = ?", *datum.CompanyID)
	}

	var total int64
	var res []*ActivityLog
	if err := query.Count(&total).
		Offset((datum.PageNum - 1) * (datum.PageSize)).
		Limit(datum.PageSize).
		Order("create_time desc").
		Find(&res).Error; err != nil {
		log.ErrorContextf(ctx, "List ActivityLog err, err:%+v", err)

		return nil, 0, err
	}

	return res, total, nil
}

func (i *impl) Get(ctx context.Context, id int64) (*ActivityLog, error) {
	var res *ActivityLog
	if err := i.db.WithContext(ctx).
		Where("id = ?", id).
		First(&res).Error; err != nil {
		log.ErrorContextf(ctx, "Get ActivityLog err, id:%d, err:%+v", id, err)

		return nil, err
	}

	return res, nil
}

func (i *impl) Update(ctx context.Context, historyLog *ActivityLog) error {
	if err := i.db.WithContext(ctx).Updates(historyLog).Error; err != nil {
		log.ErrorContextf(ctx, "Update ActivityLog err, historyLog:%+v, err:%+v", historyLog, err)

		return err
	}

	return nil
}
