package tag

import "time"

type Tag struct {
	ID         int64      `gorm:"column:id"`
	CompanyID  int64      `gorm:"column:company_id"`
	BusinessID int64      `gorm:"column:business_id"`
	Name       string     `gorm:"column:name"`
	Sort       int32      `gorm:"column:sort"`
	CreateBy   int64      `gorm:"column:create_by"`
	UpdateBy   int64      `gorm:"column:update_by"`
	DeleteBy   *int64     `gorm:"column:delete_by"`
	CreateTime time.Time  `gorm:"column:create_time"`
	UpdateTime time.Time  `gorm:"column:update_time"`
	DeleteTime *time.Time `gorm:"column:delete_time"`
}

// TableName sets the insert table name for this struct type
func (t *Tag) TableName() string {
	return "tag"
}
