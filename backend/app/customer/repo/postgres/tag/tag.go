package tag

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// ReadWriter defines CRUD operations for Tag.
type ReadWriter interface {
	WithTransaction(context.Context, func(api ReadWriter) error) error
	Create(ctx context.Context, tag *Tag) error
	Update(ctx context.Context, tag *Tag) error
	List(ctx context.Context, datum *ListTagsDatum) ([]*Tag, error)
	Delete(ctx context.Context, id int64, staffID int64) error
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) WithTransaction(ctx context.Context, fn func(api ReadWriter) error) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		txReadWriter := &impl{db: tx}

		return fn(txReadWriter)
	})
}

// Create inserts a new Tag record.
func (i *impl) Create(ctx context.Context, tag *Tag) error {
	if err := i.db.WithContext(ctx).Create(tag).Error; err != nil {
		log.ErrorContextf(ctx, "Create Tag err, err:%+v", err)

		return err
	}

	return nil
}

// Update updates an existing Tag record.
func (i *impl) Update(ctx context.Context, tag *Tag) error {
	if err := i.db.WithContext(ctx).Updates(tag).Error; err != nil {
		log.ErrorContextf(ctx, "Update Tag err, err:%+v", err)

		return err
	}

	return nil
}

// ListTagsDatum is the filter for listing tags.
type ListTagsDatum struct {
	CompanyID *int64
	IDs       []int64
	Names     []string
}

// List returns a list of Tag records matching the filter.
func (i *impl) List(ctx context.Context, datum *ListTagsDatum) ([]*Tag, error) {
	query := i.db.WithContext(ctx).Table("tag").Where("delete_time IS NULL")

	if datum.CompanyID != nil {
		query = query.Where("company_id = ?", *datum.CompanyID)
	}
	if len(datum.IDs) > 0 {
		query = query.Where("id IN (?)", datum.IDs)
	}
	if len(datum.Names) > 0 {
		query = query.Where("name IN ?", datum.Names)
	}

	var res []*Tag
	if err := query.Order("sort desc").Find(&res).Error; err != nil {
		log.ErrorContextf(ctx, "List Tag err, err:%+v", err)

		return nil, err
	}

	return res, nil
}

// Delete performs a soft delete on a Tag record.
func (i *impl) Delete(ctx context.Context, id int64, staffID int64) error {
	if err := i.db.WithContext(ctx).Table("tag").
		Where("id = ?", id).
		Update("delete_by", staffID).
		Update("delete_time", time.Now()).
		Error; err != nil {
		log.ErrorContextf(ctx, "Delete Tag err, id:%d, staffID:%d", id, staffID)

		return err
	}

	return nil
}
