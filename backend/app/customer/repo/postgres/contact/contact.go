package contact

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	contacttag "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag"
	contacttagrel "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag_rel"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Repository interface {
	Create(ctx context.Context, contact *Contact) (*Contact, error)
	Get(ctx context.Context, id int64) (*Contact, error)
	ListByCursor(ctx context.Context,
		filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error)
	Update(ctx context.Context, contact *Contact) (*Contact, error)
}

type impl struct {
	db *gorm.DB
}

func New() Repository {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) Create(ctx context.Context, contact *Contact) (*Contact, error) {
	tx := i.db.WithContext(ctx).Begin()
	defer tx.Rollback()

	// 1. 创建联系人
	err := tx.Create(contact).Error
	if err != nil {
		return nil, err
	}

	// 2. 创建联系人标签，并绑定关系
	if len(contact.Tags) > 0 {
		for _, tag := range contact.Tags {
			tagID := tag.ID
			if tag.ID == 0 {
				// tag
				err = tx.Create(&tag).Error
				if err != nil {
					return nil, err
				}
				tagID = tag.ID
			}

			// 2.2 创建绑定关系
			rel := contacttagrel.ContactTagRel{
				OrganizationType: tag.OrganizationType,
				OrganizationID:   tag.OrganizationID,
				ContactID:        contact.ID,
				TagID:            tagID,
			}
			if err := tx.Create(&rel).Error; err != nil {
				return nil, err
			}
		}
	}

	err = tx.Commit().Error
	if err != nil {
		return nil, err
	}

	return contact, nil
}

func (i *impl) Get(ctx context.Context, id int64) (*Contact, error) {
	var contact Contact

	if err := i.db.WithContext(ctx).
		Where("id = ?", id).
		Where("state = ?", customerpb.Contact_ACTIVE.String()).
		First(&contact).Error; err != nil {
		return nil, err
	}

	return &contact, nil
}

// ListWithCursor 基于游标分页的列表查询
func (i *impl) ListByCursor(ctx context.Context,
	filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error) {
	var total *int64
	if pagination.ReturnTotalSize {
		count, err := i.getTotalCount(ctx, filter)
		if err != nil {
			return nil, err
		}
		total = &count
	}
	query := i.db.WithContext(ctx).Model(&Contact{})
	query = i.applyFilter(query, filter)
	query = i.applyCursor(query, pagination.Cursor, orderBy)
	query = i.applyOrderBy(query, orderBy)

	// 多查一条用于判断是否有下一页
	query = query.Limit(int(pagination.PageSize + 1))

	var contacts []*Contact
	if err := query.Find(&contacts).Error; err != nil {
		return nil, err
	}

	hasNext := len(contacts) > int(pagination.PageSize)
	if hasNext {
		contacts = contacts[:pagination.PageSize]
	}

	return &CursorResult{
		Data:       contacts,
		HasNext:    hasNext,
		TotalCount: total,
	}, nil
}

func (i *impl) applyFilter(query *gorm.DB, filter *ListFilter) *gorm.DB {
	if filter == nil {
		return query
	}

	if filter.IDs != nil {
		query = query.Where("id IN (?)", filter.IDs)
	}

	if len(filter.CustomerIDs) > 0 {
		query = query.Where("customer_id IN (?)", filter.CustomerIDs)
	}

	if len(filter.States) > 0 {
		states := make([]string, 0, len(filter.States))
		for _, state := range filter.States {
			states = append(states, state.String())
		}
		query = query.Where("state IN (?)", states)
	}

	return query
}

func (i *impl) applyCursor(query *gorm.DB, cursor *postgres.Cursor, orderBy *OrderBy) *gorm.DB {
	if cursor == nil {
		return query
	}

	if orderBy != nil && orderBy.Direction == customerpb.ListContactsRequest_Sorting_DESC {
		query = query.Where("(created_time, id) < (?, ?)", cursor.CreatedAt, cursor.ID)
	} else {
		query = query.Where("(created_time, id) > (?, ?)", cursor.CreatedAt, cursor.ID)
	}

	return query
}

func (i *impl) applyOrderBy(query *gorm.DB, orderBy *OrderBy) *gorm.DB {
	if orderBy != nil {
		query = query.
			Order(fmt.Sprintf("%s %s, id %s", orderBy.Field.String(),
				orderBy.Direction.String(), orderBy.Direction.String()))
	} else {
		query = query.Order("created_time DESC, id DESC")
	}

	return query
}

func (i *impl) getTotalCount(ctx context.Context, filter *ListFilter) (int64, error) {
	query := i.db.WithContext(ctx).Model(&Contact{})
	query = i.applyFilter(query, filter)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (i *impl) Update(ctx context.Context, contact *Contact) (*Contact, error) {
	tx := i.db.WithContext(ctx).Begin()
	defer tx.Rollback()

	err := tx.Updates(contact).Error
	if err != nil {
		return nil, err
	}

	// 2. 更新联系人标签，并绑定关系
	if len(contact.Tags) > 0 {
		// 2.1 查询数据库中已绑定的 tag id
		var oldTagIDs []int64
		err := tx.Model(&contacttagrel.ContactTagRel{}).
			Where("contact_id = ? and deleted_time is null", contact.ID).
			Pluck("tag_id", &oldTagIDs).Error
		if err != nil {
			return nil, err
		}

		// 2.2 提取新传入的 tag id
		newTagIDs := make([]int64, 0, len(contact.Tags))
		for _, tag := range contact.Tags {
			newTagIDs = append(newTagIDs, tag.ID)
		}

		// 2.3 计算需要新增和删除的 tag id
		toAdd := difference(newTagIDs, oldTagIDs)
		toDelete := difference(oldTagIDs, newTagIDs)

		// 2.4 新增绑定关系
		for _, tagID := range toAdd {
			var tag *contacttag.ContactTag
			for _, t := range contact.Tags {
				if t.ID == tagID {
					tag = t

					break
				}
			}
			if tag == nil {
				continue
			}
			rel := &contacttagrel.ContactTagRel{
				OrganizationType: tag.OrganizationType,
				OrganizationID:   tag.OrganizationID,
				ContactID:        contact.ID,
				TagID:            tagID,
			}
			if err := tx.Create(rel).Error; err != nil {
				return nil, err
			}
		}

		// 2.5 删除多余的绑定关系
		if len(toDelete) > 0 {
			now := time.Now()
			if err := tx.Model(&contacttagrel.ContactTagRel{}).
				Where("contact_id = ? AND tag_id IN (?)", contact.ID, toDelete).
				Update("deleted_time", now).Error; err != nil {
				return nil, err
			}
		}
	}

	err = tx.Commit().Error
	if err != nil {
		return nil, err
	}

	return contact, nil
}

// difference 返回 a 中有但 b 中没有的元素
func difference(a, b []int64) []int64 {
	m := make(map[int64]struct{}, len(b))
	for _, v := range b {
		m[v] = struct{}{}
	}
	var diff []int64
	for _, v := range a {
		if _, found := m[v]; !found {
			diff = append(diff, v)
		}
	}

	return diff
}
