// Code generated by MockGen. DO NOT EDIT.
// Source: ./postgres/contact/contact.go
//
// Generated by this command:
//
//	mockgen -source=./postgres/contact/contact.go -destination=./postgres/contact/mock/contact_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	contact "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact"
	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRepository) Create(ctx context.Context, arg1 *contact.Contact) (*contact.Contact, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, arg1)
	ret0, _ := ret[0].(*contact.Contact)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRepositoryMockRecorder) Create(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRepository)(nil).Create), ctx, arg1)
}

// Get mocks base method.
func (m *MockRepository) Get(ctx context.Context, id int64) (*contact.Contact, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*contact.Contact)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockRepositoryMockRecorder) Get(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRepository)(nil).Get), ctx, id)
}

// ListByCursor mocks base method.
func (m *MockRepository) ListByCursor(ctx context.Context, filter *contact.ListFilter, pagination *contact.Pagination, orderBy *contact.OrderBy) (*contact.CursorResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByCursor", ctx, filter, pagination, orderBy)
	ret0, _ := ret[0].(*contact.CursorResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByCursor indicates an expected call of ListByCursor.
func (mr *MockRepositoryMockRecorder) ListByCursor(ctx, filter, pagination, orderBy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByCursor", reflect.TypeOf((*MockRepository)(nil).ListByCursor), ctx, filter, pagination, orderBy)
}

// Update mocks base method.
func (m *MockRepository) Update(ctx context.Context, arg1 *contact.Contact) (*contact.Contact, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, arg1)
	ret0, _ := ret[0].(*contact.Contact)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockRepositoryMockRecorder) Update(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRepository)(nil).Update), ctx, arg1)
}
