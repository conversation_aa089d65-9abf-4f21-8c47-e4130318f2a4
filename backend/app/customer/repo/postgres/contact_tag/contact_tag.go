package contacttag

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Repository interface {
	Create(ctx context.Context, contactTag *ContactTag) (*ContactTag, error)
	Get(ctx context.Context, id int64) (*ContactTag, error)
	ListByCursor(ctx context.Context,
		filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error)
	Update(ctx context.Context, contactTag *ContactTag) (*ContactTag, error)
}

type impl struct {
	db *gorm.DB
}

func New() Repository {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) Create(ctx context.Context, contactTag *ContactTag) (*ContactTag, error) {
	err := i.db.WithContext(ctx).Create(contactTag).Error
	if err != nil {
		return nil, err
	}

	return contactTag, nil
}

func (i *impl) Get(ctx context.Context, id int64) (*ContactTag, error) {
	var contactTag ContactTag
	err := i.db.WithContext(ctx).
		Where("id = ?", id).
		Where("state = ?", customerpb.ContactTag_ACTIVE.String()).
		First(&contactTag).Error
	if err != nil {
		return nil, err
	}

	return &contactTag, nil
}

func (i *impl) ListByCursor(ctx context.Context,
	filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error) {
	var total *int64
	if pagination.ReturnTotalSize {
		count, err := i.getTotalCount(ctx, filter)
		if err != nil {
			return nil, err
		}
		total = &count
	}
	query := i.db.WithContext(ctx).Model(&ContactTag{})
	query = i.applyFilter(query, filter)
	query = i.applyCursor(query, pagination.Cursor, orderBy)
	query = i.applyOrderBy(query, orderBy)

	// 多查一条用于判断是否有下一页
	query = query.Limit(int(pagination.PageSize + 1))

	var tags []*ContactTag
	if err := query.Find(&tags).Error; err != nil {
		return nil, err
	}

	hasNext := len(tags) > int(pagination.PageSize)
	if hasNext {
		tags = tags[:pagination.PageSize]
	}

	return &CursorResult{
		Data:       tags,
		HasNext:    hasNext,
		TotalCount: total,
	}, nil
}

func (i *impl) applyFilter(query *gorm.DB, filter *ListFilter) *gorm.DB {
	if filter == nil {
		return query
	}

	if filter.IDs != nil {
		query = query.Where("id IN (?)", filter.IDs)
	}

	if filter.OrganizationType != customerpb.OrganizationRef_Type(0) {
		query = query.Where("organization_type = ?", filter.OrganizationType.String())
	}

	if filter.OrganizationID != 0 {
		query = query.Where("organization_id = ?", filter.OrganizationID)
	}

	if filter.Name != "" {
		query = query.Where("name LIKE ?", "%"+filter.Name+"%")
	}

	if len(filter.States) > 0 {
		states := make([]string, 0, len(filter.States))
		for _, state := range filter.States {
			states = append(states, state.String())
		}
		query = query.Where("state IN (?)", states)
	}

	return query
}

func (i *impl) applyCursor(query *gorm.DB, cursor *postgres.Cursor, orderBy *OrderBy) *gorm.DB {
	if cursor == nil {
		return query
	}

	if orderBy != nil && orderBy.Direction == 2 { // DESC
		query = query.Where("(created_time, id) < (?, ?)", cursor.CreatedAt, cursor.ID)
	} else {
		query = query.Where("(created_time, id) > (?, ?)", cursor.CreatedAt, cursor.ID)
	}

	return query
}

func (i *impl) applyOrderBy(query *gorm.DB, orderBy *OrderBy) *gorm.DB {
	if orderBy != nil {
		query = query.
			Order(orderBy.Field.String() + " " + orderBy.Direction.String() + ", id " + orderBy.Direction.String())
	} else {
		query = query.Order("created_time DESC, id DESC")
	}

	return query
}

func (i *impl) getTotalCount(ctx context.Context, filter *ListFilter) (int64, error) {
	query := i.db.WithContext(ctx).Model(&ContactTag{})
	query = i.applyFilter(query, filter)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (i *impl) Update(ctx context.Context, contactTag *ContactTag) (*ContactTag, error) {
	err := i.db.WithContext(ctx).Updates(contactTag).Error
	if err != nil {
		return nil, err
	}

	return contactTag, nil
}
