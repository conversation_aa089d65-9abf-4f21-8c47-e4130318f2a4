load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["customer_mock.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer/mock",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/db",
        "//backend/app/customer/repo/db/customer",
        "@org_uber_go_mock//gomock",
    ],
)
