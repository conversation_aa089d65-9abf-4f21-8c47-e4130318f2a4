package customerhistorylog

import (
	"context"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	Create(context context.Context, historyLog *CustomerHistoryLog) error
	List(context context.Context, datum *ListHistoryLogsDatum) ([]*CustomerHistoryLog, int64, error)
	Get(context context.Context, id int64) (*CustomerHistoryLog, error)
	Update(ctx context.Context, historyLog *CustomerHistoryLog) error
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	return &impl{
		db: db.GetDB(),
	}
}

func (i *impl) Create(ctx context.Context, historyLog *CustomerHistoryLog) error {
	if err := i.db.WithContext(ctx).Create(historyLog).Error; err != nil {
		log.Error(ctx, "Create CustomerHistoryLog err", zap.Error(err))

		return err
	}

	return nil
}

func (i *impl) List(ctx context.Context, datum *ListHistoryLogsDatum) ([]*CustomerHistoryLog, int64, error) {
	query := i.db.WithContext(ctx).Table("moe_customer_history_log")
	// filter
	if datum.CustomerID != nil && *datum.CustomerID != 0 {
		query = query.Where("customer_id = ?", datum.CustomerID)
	}
	if datum.HistoryLogType != nil {
		query = query.Where("type = ?", datum.HistoryLogType.String())
	}
	if datum.CompanyID != nil {
		query = query.Where("company_id = ?", *datum.CompanyID)
	}

	var total int64
	var res []*CustomerHistoryLog
	if err := query.Count(&total).
		Offset((datum.PageNum - 1) * (datum.PageSize)).
		Limit(datum.PageSize).
		Order("create_time desc").
		Find(&res).Error; err != nil {
		log.ErrorContextf(ctx, "List CustomerHistoryLog err, err:%+v", err)

		return nil, 0, err
	}

	return res, total, nil
}

func (i *impl) Get(ctx context.Context, id int64) (*CustomerHistoryLog, error) {
	var res *CustomerHistoryLog
	if err := i.db.WithContext(ctx).
		Where("id = ?", id).
		First(&res).Error; err != nil {
		log.ErrorContextf(ctx, "Get CustomerHistoryLog err, id:%d, err:%+v", id, err)

		return nil, err
	}

	return res, nil
}

func (i *impl) Update(ctx context.Context, historyLog *CustomerHistoryLog) error {
	if err := i.db.WithContext(ctx).Updates(historyLog).Error; err != nil {
		log.ErrorContextf(ctx, "Update CustomerHistoryLog err, historyLog:%+v, err:%+v", historyLog, err)

		return err
	}

	return nil
}
