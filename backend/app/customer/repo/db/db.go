package db

import (
	"sync"

	"gorm.io/gorm"

	igorm "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
)

var (
	database *gorm.DB
	once     sync.Once
)

type Pagination struct {
	Offset  int
	Limit   int
	OrderBy *string
	Desc    string
}

func SetDB(db *gorm.DB) {
	database = db
}

func GetDB() *gorm.DB {
	if database == nil {
		NewDB()
	}

	return database
}

func NewDB() {
	once.Do(func() {
		db, err := igorm.NewClientProxy("mysql.moe_customer")
		if err != nil {
			panic(err)
		}
		database = db
	})
}
