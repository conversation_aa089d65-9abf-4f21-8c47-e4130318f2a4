package customerview

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

// ReadWriter defines the interface for CustomerView database operations.
type ReadWriter interface {
	WithTransaction(context.Context, func(api ReadWriter) error) error
	Create(ctx context.Context, customerView *CustomerView) error
	CreateBatch(ctx context.Context, customerViews []*CustomerView) error
	Update(ctx context.Context, customerView *CustomerView) error
	List(ctx context.Context, datum *ListCustomerViewsDatum) ([]*CustomerView, error)
	Delete(ctx context.Context, id int64) error
}

// impl is the implementation of the ReadWriter interface.
type impl struct {
	db *gorm.DB
}

// New creates and returns a new ReadWriter instance.
func New() ReadWriter {
	return &impl{
		db: db.GetDB(), // Assuming db.GetDB() provides the GORM DB instance.
	}
}

// WithTransaction executes the given function within a database transaction.
func (i *impl) WithTransaction(ctx context.Context, fn func(api ReadWriter) error) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		txReadWriter := &impl{db: tx}

		return fn(txReadWriter)
	})
}

// Create creates a new CustomerView record.
func (i *impl) Create(ctx context.Context, customerView *CustomerView) error {
	if err := i.db.WithContext(ctx).Create(customerView).Error; err != nil {
		log.ErrorContextf(ctx, "Create CustomerView err, err:%+v", err)

		return err
	}

	return nil
}

// CreateBatch creates multiple CustomerView records in a batch.
func (i *impl) CreateBatch(ctx context.Context, customerViews []*CustomerView) error {
	if err := i.db.WithContext(ctx).Create(customerViews).Error; err != nil {
		log.ErrorContextf(ctx, "CreateBatch CustomerView err, err:%+v", err)

		return err
	}

	return nil
}

// Update updates a CustomerView record.
func (i *impl) Update(ctx context.Context, customerView *CustomerView) error {
	if err := i.db.WithContext(ctx).Updates(customerView).Error; err != nil {
		log.ErrorContextf(ctx, "Update CustomerView err, err:%+v, customerView:%+v", err, customerView)

		return err
	}

	return nil
}

// ListCustomerViewsDatum represents the parameters for listing CustomerView records.
type ListCustomerViewsDatum struct {
	CompanyID *int64
	StaffID   *int64
	IDs       []int64
	Titles    []string
	Type      *customerpb.CustomerView_Type
}

// List retrieves CustomerView records based on the provided datum.
func (i *impl) List(ctx context.Context, datum *ListCustomerViewsDatum) ([]*CustomerView, error) {
	query := i.db.WithContext(ctx).Model(&CustomerView{}).Where("deleted_at IS NULL") // Filter out soft-deleted records

	if datum.CompanyID != nil {
		query = query.Where("company_id = ?", *datum.CompanyID)
	}
	if datum.StaffID != nil {
		query = query.Where("staff_id = ?", *datum.StaffID)
	}
	if len(datum.IDs) > 0 {
		query = query.Where("id IN (?)", datum.IDs)
	}
	if len(datum.Titles) > 0 {
		query = query.Where("title IN (?)", datum.Titles)
	}
	if datum.Type != nil {
		query = query.Where("type = ?", datum.Type.String())
	}

	var res []*CustomerView
	if err := query.Order("created_at").Find(&res).Error; err != nil {
		log.ErrorContextf(ctx, "List CustomerView err, err:%+v", err)

		return nil, err
	}

	return res, nil
}

// Delete soft-deletes a CustomerView record by setting deleted_by and deleted_at.
func (i *impl) Delete(ctx context.Context, id int64) error {
	// GORM soft delete typically involves updating the deleted_at field.
	// If your table includes a deleted_by field, update that as well.
	if err := i.db.WithContext(ctx).Model(&CustomerView{}).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_at": time.Now(),
	}).Error; err != nil {
		log.ErrorContextf(ctx, "Delete CustomerView err, id:%d, err:%v", id, err)

		return err
	}

	return nil
}
