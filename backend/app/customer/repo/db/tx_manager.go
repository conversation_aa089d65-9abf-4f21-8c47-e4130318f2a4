package db

import (
	"context"

	"gorm.io/gorm"
)

type TransactionManager interface {
	ExecuteInTransaction(ctx context.Context, ops []func(opCtx context.Context, tx *gorm.DB) error) error
	Tx(ctx context.Context, op func(opCtx context.Context, tx *gorm.DB) error) (err error)
}

// TransactionManager 事务管理层
type impl struct {
	db *gorm.DB
}

func NewTxManager() TransactionManager {
	return &impl{
		db: GetDB(),
	}
}

// ExecuteInTransaction 接收多个数据库操作并在一个事务中执行
func (i *impl) ExecuteInTransaction(ctx context.Context,
	ops []func(opCtx context.Context, tx *gorm.DB) error) error {

	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, op := range ops {
			if err := op(ctx, tx); err != nil {
				// 出错时回滚事务
				return err
			}
		}
		// 全部成功时提交事务
		return nil
	})
}

// tx
func (i *impl) Tx(ctx context.Context, op func(opCtx context.Context, tx *gorm.DB) error) (err error) {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return op(ctx, tx)
	})
}
