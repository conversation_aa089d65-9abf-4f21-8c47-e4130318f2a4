// Code generated by MockGen. DO NOT EDIT.
// Source: ./search/search.go
//
// Generated by this command:
//
//	mockgen -source=./search/search.go -destination=./search/mock/search_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
	gomock "go.uber.org/mock/gomock"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// BulkDocument mocks base method.
func (m *MockReadWriter) BulkDocument(ctx context.Context, index, id string, data *structpb.Struct, optType searchpb.OperationType) ([]*searchpb.BulkDocumentResponse_OperationResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkDocument", ctx, index, id, data, optType)
	ret0, _ := ret[0].([]*searchpb.BulkDocumentResponse_OperationResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkDocument indicates an expected call of BulkDocument.
func (mr *MockReadWriterMockRecorder) BulkDocument(ctx, index, id, data, optType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkDocument", reflect.TypeOf((*MockReadWriter)(nil).BulkDocument), ctx, index, id, data, optType)
}

// SearchDocument mocks base method.
func (m *MockReadWriter) SearchDocument(ctx context.Context, request *searchpb.SearchDocumentRequest) ([]*searchpb.SearchDocumentResponse_Hit, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchDocument", ctx, request)
	ret0, _ := ret[0].([]*searchpb.SearchDocumentResponse_Hit)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchDocument indicates an expected call of SearchDocument.
func (mr *MockReadWriterMockRecorder) SearchDocument(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchDocument", reflect.TypeOf((*MockReadWriter)(nil).SearchDocument), ctx, request)
}
