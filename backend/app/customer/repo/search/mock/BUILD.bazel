load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["search_mock.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/search/mock",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/proto/search/v1:search",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_uber_go_mock//gomock",
    ],
)
