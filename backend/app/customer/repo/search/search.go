package search

import (
	"context"

	"google.golang.org/protobuf/types/known/structpb"

	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

type ReadWriter interface {
	BulkDocument(ctx context.Context, index string, id string,
		data *structpb.Struct, optType searchpb.OperationType) ([]*searchpb.BulkDocumentResponse_OperationResult, error)

	SearchDocument(ctx context.Context, request *searchpb.SearchDocumentRequest) (
		[]*searchpb.SearchDocumentResponse_Hit, error)
}

type impl struct {
	search searchpb.SearchServiceClient
}

func New() ReadWriter {
	return &impl{
		search: grpc.NewClient("moego-search", searchpb.NewSearchServiceClient),
	}
}

func (i *impl) BulkDocument(ctx context.Context, index string, id string,
	data *structpb.Struct, optType searchpb.OperationType) ([]*searchpb.BulkDocumentResponse_OperationResult, error) {
	resp, err := i.search.BulkDocument(ctx, &searchpb.BulkDocumentRequest{
		Operations: []*searchpb.BulkDocumentRequest_BulkOperation{
			{
				OperationType: optType,
				Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
					Index: index,
					Id:    &id,
				},
				Document: data,
			},
		},
	})
	if err != nil {
		log.ErrorContextf(ctx, "Search BulkDocument err:%v, data:%+v", err, customerutils.JSONMarshalNoErr(data))

		return nil, err
	}
	log.InfoContextf(ctx, "Search BulkDocument success, index:%s, id:%s, data:%+v, resp:%+v",
		index, id, customerutils.JSONMarshalNoErr(data), customerutils.JSONMarshalNoErr(resp))

	return resp.Results, err
}

func (i *impl) SearchDocument(ctx context.Context, request *searchpb.SearchDocumentRequest) (
	[]*searchpb.SearchDocumentResponse_Hit, error) {
	resp, err := i.search.SearchDocument(ctx, request)
	if err != nil {
		log.ErrorContextf(ctx, "Search SearchDocument err:%v, req:%+v", err, customerutils.JSONMarshalNoErr(request))

		return nil, err
	}
	log.InfoContextf(ctx, "Search SearchDocument success, req:%+v, resp:%+v",
		customerutils.JSONMarshalNoErr(request), customerutils.JSONMarshalNoErr(resp))

	return resp.Hits, err
}

func BuildLeadSearchDocumentRequest(companyID int, status int, keyword string,
	pageSize int32) *searchpb.SearchDocumentRequest {
	return &searchpb.SearchDocumentRequest{
		Index:    CustomerIndexName,
		PageSize: pageSize,
		Strategy: &searchpb.Strategy{
			Strategy: &searchpb.Strategy_Bool{
				Bool: &searchpb.BoolStrategy{
					Must: []*searchpb.Strategy{
						buildTermStrategy("CompanyID", float64(companyID)),
						buildTermStrategy("Status", float64(status)),
					},
					Should: buildLeadSearchShouldStrategies(keyword),
				},
			},
		},
		Sort: []*searchpb.SearchDocumentRequest_Sort{
			{
				Field: "ID",
				Order: searchpb.SearchDocumentRequest_Sort_DESC,
			},
		},
	}
}

func buildTermStrategy(field string, value float64) *searchpb.Strategy {
	return &searchpb.Strategy{
		Strategy: &searchpb.Strategy_Term{
			Term: &searchpb.TermStrategy{
				Field: field,
				Value: structpb.NewNumberValue(value),
			},
		},
	}
}

func buildWildcardStrategy(field, keyword string) *searchpb.Strategy {
	return &searchpb.Strategy{
		Strategy: &searchpb.Strategy_Wildcard{
			Wildcard: &searchpb.WildcardStrategy{
				Field: field,
				Value: "*" + keyword + "*",
			},
		},
	}
}

func buildLeadSearchShouldStrategies(keyword string) *searchpb.BoolStrategy_BoolShouldStrategy {
	return &searchpb.BoolStrategy_BoolShouldStrategy{
		Strategies: []*searchpb.Strategy{
			buildWildcardStrategy("FirstName", keyword),
			buildWildcardStrategy("LastName", keyword),
			buildWildcardStrategy("Email", keyword),
			buildWildcardStrategy("Name.keyword", keyword),
			buildLeadSearchContactWildcardStrategy(keyword),
			buildLeadSearchAddressWildcardStrategy(keyword),
			buildLeadSearchPetWildcardStrategy(keyword),
		},
		MinimumMatch: 1,
	}
}

func buildLeadSearchContactWildcardStrategy(keyword string) *searchpb.Strategy {
	return &searchpb.Strategy{
		Strategy: &searchpb.Strategy_Bool{
			Bool: &searchpb.BoolStrategy{
				Should: &searchpb.BoolStrategy_BoolShouldStrategy{
					Strategies: []*searchpb.Strategy{
						buildWildcardStrategy("Contact.FirstName", keyword),
						buildWildcardStrategy("Contact.LastName", keyword),
						buildWildcardStrategy("Contact.Email", keyword),
						buildWildcardStrategy("Contact.PhoneNumber", keyword),
					},
					MinimumMatch: 1,
				},
			},
		},
	}
}

func buildLeadSearchAddressWildcardStrategy(keyword string) *searchpb.Strategy {
	return &searchpb.Strategy{
		Strategy: &searchpb.Strategy_Bool{
			Bool: &searchpb.BoolStrategy{
				Should: &searchpb.BoolStrategy_BoolShouldStrategy{
					Strategies: []*searchpb.Strategy{
						buildWildcardStrategy("Address.Address1", keyword),
						buildWildcardStrategy("Address.Address2", keyword),
					},
					MinimumMatch: 1,
				},
			},
		},
	}
}

func buildLeadSearchPetWildcardStrategy(keyword string) *searchpb.Strategy {
	return &searchpb.Strategy{
		Strategy: &searchpb.Strategy_Bool{
			Bool: &searchpb.BoolStrategy{
				Should: &searchpb.BoolStrategy_BoolShouldStrategy{
					Strategies: []*searchpb.Strategy{
						buildWildcardStrategy("Pet.Name.keyword", keyword),
					},
					MinimumMatch: 1,
				},
			},
		},
	}
}
