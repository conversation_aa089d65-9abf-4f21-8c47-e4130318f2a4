package sms

import (
	"context"

	smspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/sms/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
)

type ReadWriter interface {
	FormatPhoneNumber(ctx context.Context, businessID uint64, phoneNumber string) (string, error)
}

type impl struct {
	sms smspb.PhoneNumberServiceClient
}

func New() ReadWriter {
	return &impl{
		sms: grpc.NewClient("moego-svc-sms", smspb.NewPhoneNumberServiceClient),
	}
}

func (i *impl) FormatPhoneNumber(ctx context.Context, businessID uint64, phoneNumber string) (string, error) {
	resp, err := i.sms.Format(ctx, &smspb.FormatPhoneNumberRequest{
		BusinessId:  businessID,
		PhoneNumber: phoneNumber,
	})
	if err != nil {
		return "", err
	}

	return resp.GetE164PhoneNumber(), nil
}
