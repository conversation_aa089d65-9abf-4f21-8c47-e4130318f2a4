// Code generated by MockGen. DO NOT EDIT.
// Source: ./sms/sms.go
//
// Generated by this command:
//
//	mockgen -source=./sms/sms.go -destination=./sms/mock/sms_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// FormatPhoneNumber mocks base method.
func (m *MockReadWriter) FormatPhoneNumber(ctx context.Context, businessID uint64, phoneNumber string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FormatPhoneNumber", ctx, businessID, phoneNumber)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FormatPhoneNumber indicates an expected call of FormatPhoneNumber.
func (mr *MockReadWriterMockRecorder) FormatPhoneNumber(ctx, businessID, phoneNumber any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FormatPhoneNumber", reflect.TypeOf((*MockReadWriter)(nil).FormatPhoneNumber), ctx, businessID, phoneNumber)
}
