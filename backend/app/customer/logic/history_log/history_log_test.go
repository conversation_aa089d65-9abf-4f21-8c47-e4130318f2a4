package historylog

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	ggorm "gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
	customerhistorylog "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer_history_log"
	mock_customerhistorylog "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer_history_log/mock"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

func TestLogic_New(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db.SetDB(&ggorm.DB{})
		logic := New()
		assert.NotNil(t, logic)
	})
}

func TestLogic_Create(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()
	mockRepo := mock_customerhistorylog.NewMockReadWriter(ctrl)
	logic := &Logic{historyLogRepo: mockRepo}

	t.Run("note success", func(t *testing.T) {
		mockRepo.EXPECT().Create(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, log *customerhistorylog.CustomerHistoryLog) error {
			require.Equal(t, int64(1), log.CompanyID)
			require.Equal(t, int64(1), log.BusinessID)
			require.Equal(t, int64(1), log.CustomerID)
			require.Equal(t, customerpb.HistoryLog_NOTE, log.Type)
			require.NotNil(t, log.Action)
			require.NotNil(t, log.Action.GetNote())
			require.Equal(t, "aaa", log.Action.GetNote().Text)
			log.ID = 12345
			return nil
		})
		id, err := logic.Create(ctx, &CreateHistoryLogDatum{
			CompanyID:  1,
			BusinessID: 1,
			CustomerID: 1,
			Action: &customerpb.HistoryLog_Action{
				Action: &customerpb.HistoryLog_Action_Note{
					Note: &customerpb.HistoryLog_Note{Text: "aaa"},
				},
			},
		})
		assert.NoError(t, err)
		assert.NotZero(t, id)
	})

	t.Run("message success", func(t *testing.T) {
		mockRepo.EXPECT().Create(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, log *customerhistorylog.CustomerHistoryLog) error {
			require.Equal(t, int64(1), log.CompanyID)
			require.Equal(t, int64(1), log.BusinessID)
			require.Equal(t, int64(1), log.CustomerID)
			require.Equal(t, customerpb.HistoryLog_MESSAGE, log.Type)
			require.NotNil(t, log.Action)
			require.NotNil(t, log.Action.GetMessage())
			require.Equal(t, int64(1), log.Action.GetMessage().MessageId)
			require.Equal(t, "aaa", log.Action.GetMessage().Text)
			log.ID = 12345
			return nil
		})
		id, err := logic.Create(ctx, &CreateHistoryLogDatum{
			CompanyID:  1,
			BusinessID: 1,
			CustomerID: 1,
			Action: &customerpb.HistoryLog_Action{
				Action: &customerpb.HistoryLog_Action_Message{
					Message: &customerpb.HistoryLog_Message{
						MessageId: 1,
						Text:      "aaa",
					},
				},
			},
		})
		assert.NoError(t, err)
		assert.NotZero(t, id)
	})

	t.Run("task success", func(t *testing.T) {
		mockRepo.EXPECT().Create(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, log *customerhistorylog.CustomerHistoryLog) error {
			require.Equal(t, int64(1), log.CompanyID)
			require.Equal(t, int64(1), log.BusinessID)
			require.Equal(t, int64(1), log.CustomerID)
			require.Equal(t, customerpb.HistoryLog_TASK, log.Type)
			require.NotNil(t, log.Action)
			require.NotNil(t, log.Action.GetTask())
			require.Equal(t, customerpb.HistoryLog_Task_Type(1), log.Action.GetTask().Type)
			log.ID = 12345
			return nil
		})
		id, err := logic.Create(ctx, &CreateHistoryLogDatum{
			CompanyID:  1,
			BusinessID: 1,
			CustomerID: 1,
			Action: &customerpb.HistoryLog_Action{
				Action: &customerpb.HistoryLog_Action_Task{
					Task: &customerpb.HistoryLog_Task{
						Type: 1,
					},
				},
			},
		})
		assert.NoError(t, err)
		assert.NotZero(t, id)
	})

	t.Run("invalid params", func(t *testing.T) {
		_, err := logic.Create(ctx, &CreateHistoryLogDatum{
			CompanyID:  0,
			BusinessID: 1,
			CustomerID: 1,
			Action: &customerpb.HistoryLog_Action{
				Action: &customerpb.HistoryLog_Action_Note{
					Note: &customerpb.HistoryLog_Note{Text: "aaa"},
				},
			},
		})
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("invalid action type params", func(t *testing.T) {
		_, err := logic.Create(ctx, &CreateHistoryLogDatum{
			CompanyID:  1,
			BusinessID: 1,
			CustomerID: 1,
			Action:     &customerpb.HistoryLog_Action{},
		})
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("create db err", func(t *testing.T) {
		mockRepo.EXPECT().Create(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, log *customerhistorylog.CustomerHistoryLog) error {
			require.Equal(t, int64(1), log.CompanyID)
			require.Equal(t, int64(1), log.BusinessID)
			require.Equal(t, int64(1), log.CustomerID)
			require.Equal(t, customerpb.HistoryLog_NOTE, log.Type)
			require.NotNil(t, log.Action)
			require.NotNil(t, log.Action.GetNote())
			require.Equal(t, "aaa", log.Action.GetNote().Text)
			return fmt.Errorf("db error")
		})
		_, err := logic.Create(ctx, &CreateHistoryLogDatum{
			CompanyID:  1,
			BusinessID: 1,
			CustomerID: 1,
			Action: &customerpb.HistoryLog_Action{
				Action: &customerpb.HistoryLog_Action_Note{
					Note: &customerpb.HistoryLog_Note{Text: "aaa"},
				},
			},
		})
		assert.Error(t, err)
	})
}

func TestLogic_Update(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()
	mockRepo := mock_customerhistorylog.NewMockReadWriter(ctrl)
	logic := &Logic{historyLogRepo: mockRepo}

	t.Run("invalid params", func(t *testing.T) {
		err := logic.Update(ctx, &UpdateHistoryLogDatum{
			LogID:   0,
			StaffID: 1,
			Action: &customerpb.HistoryLog_Action{
				Action: &customerpb.HistoryLog_Action_Note{
					Note: &customerpb.HistoryLog_Note{Text: "aaa"},
				},
			},
		})
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("get log fail", func(t *testing.T) {
		mockRepo.EXPECT().Get(ctx, int64(1)).DoAndReturn(func(ctx context.Context, logID int64) (*customerhistorylog.CustomerHistoryLog, error) {
			require.Equal(t, int64(1), logID)
			return nil, fmt.Errorf("not found")
		})
		err := logic.Update(ctx, &UpdateHistoryLogDatum{
			LogID:   1,
			StaffID: 1,
			Action: &customerpb.HistoryLog_Action{
				Action: &customerpb.HistoryLog_Action_Note{
					Note: &customerpb.HistoryLog_Note{Text: "aaa"},
				},
			},
		})
		assert.Error(t, err)
	})

	t.Run("invalid action type", func(t *testing.T) {
		mockRepo.EXPECT().Get(ctx, int64(1)).Return(&customerhistorylog.CustomerHistoryLog{
			ID:         1,
			CompanyID:  1,
			BusinessID: 1,
			CustomerID: 1,
			StaffID:    1,
			Type:       customerpb.HistoryLog_CALL, // update call
		}, nil)
		err := logic.Update(ctx, &UpdateHistoryLogDatum{
			LogID:   1,
			StaffID: 1,
			Action:  &customerpb.HistoryLog_Action{},
		})
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("update not note log fail", func(t *testing.T) {
		mockRepo.EXPECT().Get(ctx, int64(1)).Return(&customerhistorylog.CustomerHistoryLog{
			ID:         1,
			CompanyID:  1,
			BusinessID: 1,
			CustomerID: 1,
			StaffID:    1,
			Type:       customerpb.HistoryLog_CALL, // update call
		}, nil)
		err := logic.Update(ctx, &UpdateHistoryLogDatum{
			LogID:   1,
			StaffID: 1,
			Action: &customerpb.HistoryLog_Action{
				Action: &customerpb.HistoryLog_Action_Note{
					Note: &customerpb.HistoryLog_Note{Text: "aaa"},
				},
			},
		})
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("update note to not note log fail", func(t *testing.T) {
		mockRepo.EXPECT().Get(ctx, int64(1)).Return(&customerhistorylog.CustomerHistoryLog{
			ID:         1,
			CompanyID:  1,
			BusinessID: 1,
			CustomerID: 1,
			StaffID:    1,
			Type:       customerpb.HistoryLog_NOTE,
		}, nil)
		err := logic.Update(ctx, &UpdateHistoryLogDatum{
			LogID:   1,
			StaffID: 1,
			Action: &customerpb.HistoryLog_Action{
				Action: &customerpb.HistoryLog_Action_Call{
					Call: &customerpb.HistoryLog_Call{
						CallId: 1,
						Text:   "aaa",
					},
				},
			},
		})
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("update db fail", func(t *testing.T) {
		mockRepo.EXPECT().Get(ctx, int64(1)).Return(&customerhistorylog.CustomerHistoryLog{
			ID:         int64(1),
			CompanyID:  int64(1),
			BusinessID: int64(1),
			CustomerID: int64(1),
			StaffID:    int64(1),
			Type:       customerpb.HistoryLog_NOTE,
		}, nil)
		mockRepo.EXPECT().Update(ctx, gomock.Any()).Return(fmt.Errorf("db error"))
		err := logic.Update(ctx, &UpdateHistoryLogDatum{
			LogID:   int64(1),
			StaffID: int64(1),
			Action: &customerpb.HistoryLog_Action{
				Action: &customerpb.HistoryLog_Action_Note{
					Note: &customerpb.HistoryLog_Note{Text: "aaa"},
				},
			},
		})
		assert.Error(t, err)
	})

	t.Run("success", func(t *testing.T) {
		mockRepo.EXPECT().Get(ctx, int64(1)).DoAndReturn(func(ctx context.Context, logID int64) (*customerhistorylog.CustomerHistoryLog, error) {
			require.Equal(t, int64(1), logID)
			return &customerhistorylog.CustomerHistoryLog{
				ID:         1,
				CompanyID:  1,
				BusinessID: 1,
				CustomerID: 1,
				StaffID:    1,
				Type:       customerpb.HistoryLog_NOTE,
			}, nil
		})
		mockRepo.EXPECT().Update(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, log *customerhistorylog.CustomerHistoryLog) error {
			require.Equal(t, int64(1), log.ID)
			require.Equal(t, int64(1), log.StaffID)
			require.Equal(t, customerpb.HistoryLog_NOTE, log.Type)
			require.NotNil(t, log.Action)
			require.NotNil(t, log.Action.GetNote())
			require.Equal(t, "aaa", log.Action.GetNote().Text)
			return nil
		})
		err := logic.Update(ctx, &UpdateHistoryLogDatum{
			LogID:   1,
			StaffID: 1,
			Action: &customerpb.HistoryLog_Action{
				Action: &customerpb.HistoryLog_Action_Note{
					Note: &customerpb.HistoryLog_Note{Text: "aaa"},
				},
			},
		})
		assert.NoError(t, err)
	})

}

func TestLogic_List(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()
	mockRepo := mock_customerhistorylog.NewMockReadWriter(ctrl)
	logic := &Logic{historyLogRepo: mockRepo}

	t.Run("invalid params", func(t *testing.T) {
		_, _, err := logic.List(ctx, &ListHistoryLogsDatum{
			CustomerID: customerutils.ToPointer(int64(0)),
			PageSize:   0,
			PageNum:    0,
		})
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("list err params", func(t *testing.T) {
		mockRepo.EXPECT().List(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, datum *customerhistorylog.ListHistoryLogsDatum) ([]*customerhistorylog.CustomerHistoryLog, int64, error) {
			require.Equal(t, int64(1), *datum.CustomerID)
			require.Equal(t, int(1), datum.PageSize)
			require.Equal(t, int(10), datum.PageNum)
			return nil, int64(0), fmt.Errorf("error")
		})
		_, _, err := logic.List(ctx, &ListHistoryLogsDatum{
			CustomerID: customerutils.ToPointer(int64(1)),
			PageSize:   1,
			PageNum:    10,
		})
		assert.Error(t, err)
	})

	t.Run("success", func(t *testing.T) {
		mockRepo.EXPECT().List(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, datum *customerhistorylog.ListHistoryLogsDatum) ([]*customerhistorylog.CustomerHistoryLog, int64, error) {
			require.Equal(t, int64(1), *datum.CustomerID)
			return []*customerhistorylog.CustomerHistoryLog{
				{
					ID:         1,
					CompanyID:  123,
					BusinessID: 456,
					CustomerID: 789,
					StaffID:    101112,
					Type:       customerpb.HistoryLog_NOTE,
					Action: &customerpb.HistoryLog_Action{
						Action: &customerpb.HistoryLog_Action_Note{
							Note: &customerpb.HistoryLog_Note{Text: "Sample note text"},
						},
					},
				},
				nil,
			}, int64(10), nil
		})
		logs, total, err := logic.List(ctx, &ListHistoryLogsDatum{
			CustomerID: customerutils.ToPointer(int64(1)),
			PageNum:    0,
			PageSize:   0,
		})
		assert.NoError(t, err)
		assert.NotEmpty(t, logs)
		assert.NotZero(t, total)
	})

	t.Run("create with source", func(t *testing.T) {
		source := customerpb.HistoryLog_STAFF
		mockRepo.EXPECT().Create(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, log *customerhistorylog.CustomerHistoryLog) error {
			require.Equal(t, int64(1), log.CompanyID)
			require.Equal(t, int64(1), log.BusinessID)
			require.Equal(t, int64(1), log.CustomerID)
			require.Equal(t, customerpb.HistoryLog_NOTE, log.Type)
			require.Equal(t, customerpb.HistoryLog_STAFF, log.Source)
			require.NotNil(t, log.Action)
			require.NotNil(t, log.Action.GetNote())
			require.Equal(t, "test with source", log.Action.GetNote().Text)
			log.ID = 12345
			return nil
		})

		_, err := logic.Create(ctx, &CreateHistoryLogDatum{
			CompanyID:  1,
			BusinessID: 1,
			CustomerID: 1,
			Source:     &source,
			Action: &customerpb.HistoryLog_Action{
				Action: &customerpb.HistoryLog_Action_Note{
					Note: &customerpb.HistoryLog_Note{Text: "test with source"},
				},
			},
		})

		require.NoError(t, err)
	})

	t.Run("convert action success", func(t *testing.T) {
		mockRepo.EXPECT().Create(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, log *customerhistorylog.CustomerHistoryLog) error {
			require.Equal(t, int64(1), log.CompanyID)
			require.Equal(t, int64(1), log.BusinessID)
			require.Equal(t, int64(1), log.CustomerID)
			require.Equal(t, customerpb.HistoryLog_CONVERT, log.Type)
			require.NotNil(t, log.Action)
			require.NotNil(t, log.Action.GetConvert())
			log.ID = 12345
			return nil
		})

		_, err := logic.Create(ctx, &CreateHistoryLogDatum{
			CompanyID:  1,
			BusinessID: 1,
			CustomerID: 1,
			Action: &customerpb.HistoryLog_Action{
				Action: &customerpb.HistoryLog_Action_Convert{
					Convert: &customerpb.HistoryLog_Convert{},
				},
			},
		})

		require.NoError(t, err)
	})

	t.Run("create action success", func(t *testing.T) {
		mockRepo.EXPECT().Create(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, log *customerhistorylog.CustomerHistoryLog) error {
			require.Equal(t, int64(1), log.CompanyID)
			require.Equal(t, int64(1), log.BusinessID)
			require.Equal(t, int64(1), log.CustomerID)
			require.Equal(t, customerpb.HistoryLog_CREATE, log.Type)
			require.NotNil(t, log.Action)
			require.NotNil(t, log.Action.GetCreate())
			log.ID = 12345
			return nil
		})

		_, err := logic.Create(ctx, &CreateHistoryLogDatum{
			CompanyID:  1,
			BusinessID: 1,
			CustomerID: 1,
			Action: &customerpb.HistoryLog_Action{
				Action: &customerpb.HistoryLog_Action_Create{
					Create: &customerpb.HistoryLog_Create{},
				},
			},
		})

		require.NoError(t, err)
	})

}
