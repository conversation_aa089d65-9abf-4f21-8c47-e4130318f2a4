package source

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/source"
	utils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

// Logic 逻辑层
type Logic struct {
	sourceRepo source.ReadWriter
}

// New 构造函数
func New() *Logic {
	return &Logic{
		sourceRepo: source.New(),
	}
}

// Create 创建 Source
func (l *Logic) Create(ctx context.Context, datum *CreateSourceDatum) (int64, error) {
	if datum == nil || datum.CompanyID <= 0 || datum.Name == "" {
		log.ErrorContextf(ctx, "Create Source params is invalid, datum:%+v", datum)

		return 0, status.Error(codes.InvalidArgument, "params is invalid")
	}

	// check name
	existNameList, err := l.sourceRepo.List(ctx, &source.ListSourcesDatum{
		CompanyID: &datum.CompanyID,
		Names:     []string{datum.Name},
	})
	if err != nil {
		log.ErrorContextf(ctx, "Create Source List same name err, err: %+v", err)

		return 0, err
	}
	if len(existNameList) > 0 {
		log.InfoContextf(ctx, "Create Source name is exist, datum: %+v", datum)

		return 0, errs.New(customerpb.ErrCode_ERR_CODE_SOURCE_NAME_EXIST)
	}

	dbSource := &source.Source{
		CompanyID:  datum.CompanyID,
		BusinessID: datum.BusinessID,
		Name:       datum.Name,
		Sort:       datum.Sort,
		CreateBy:   datum.StaffID,
		UpdateBy:   datum.StaffID,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}
	if err := l.sourceRepo.Create(ctx, dbSource); err != nil {
		return 0, err
	}

	return dbSource.ID, nil
}

// Update 更新 Source
func (l *Logic) Update(ctx context.Context, datumList []*UpdateSourceDatum, companyID int64) error {
	// check
	if companyID <= 0 || len(datumList) == 0 {
		log.ErrorContextf(ctx, "Update Source params is empty")

		return status.Error(codes.InvalidArgument, "params is empty")
	}

	// conv db entity
	names := make([]string, 0, len(datumList))
	idChangeNameMap := make(map[int64]*UpdateSourceDatum, len(datumList))
	updates := make([]*source.Source, 0, len(datumList))
	for _, datum := range datumList {
		if datum == nil || datum.SourceID <= 0 ||
			(datum.Name == nil && datum.Sort == nil) {
			log.ErrorContextf(ctx, "Update Source params is invalid, datum:%+v", datum)

			return status.Error(codes.InvalidArgument, "params is invalid")
		}
		dbTag := &source.Source{
			ID:         datum.SourceID,
			UpdateBy:   datum.StaffID,
			UpdateTime: time.Now(),
		}
		if datum.Name != nil {
			if *datum.Name == "" {
				log.ErrorContextf(ctx, "Update Source name is empty")

				return status.Error(codes.InvalidArgument, "name is empty")
			}
			dbTag.Name = *datum.Name
			names = append(names, *datum.Name)
			idChangeNameMap[datum.SourceID] = datum
		}
		if datum.Sort != nil {
			if *datum.Sort <= 0 {
				log.ErrorContextf(ctx, "Update Source sort is invalid, sort:%d", *datum.Sort)

				return status.Error(codes.InvalidArgument, "sort is invalid")
			}
			dbTag.Sort = *datum.Sort
		}
		updates = append(updates, dbTag)
	}

	// check names
	if len(names) > 0 {
		existNameList, err := l.sourceRepo.List(ctx, &source.ListSourcesDatum{
			CompanyID: &companyID,
			Names:     names,
		})
		if err != nil {
			log.ErrorContextf(ctx, "Update Source List same name err, err: %+v", err)

			return err
		}
		for _, existName := range existNameList {
			// existName source is the same update id and name, skip
			if idChangeNameMap[existName.ID] != nil && *idChangeNameMap[existName.ID].Name == existName.Name {
				continue
			}
			log.InfoContextf(ctx, "Update Source name is exist, names: %+v", names)

			return errs.New(customerpb.ErrCode_ERR_CODE_SOURCE_NAME_EXIST)
		}
	}

	// save to db
	if err := l.sourceRepo.WithTransaction(ctx, func(api source.ReadWriter) error {
		for _, update := range updates {
			if err := api.Update(ctx, update); err != nil {
				return err
			}
		}

		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "Update Source WithTransaction err, err:%+v", err)

		return err
	}

	return nil
}

// List 查询 Source 列表
func (l *Logic) List(ctx context.Context, datum *ListSourcesDatum) ([]*customerpb.Source, error) {
	if datum == nil || (utils.ToValue(datum.CompanyID) == 0 &&
		utils.ToValue(datum.CustomerID) == 0 &&
		len(datum.IDs) == 0) {
		log.ErrorContextf(ctx, "List Source params is invalid, datum:%+v", datum)

		return nil, status.Error(codes.InvalidArgument, "params is invalid")
	}

	sources, err := l.sourceRepo.List(ctx, &source.ListSourcesDatum{
		CompanyID: datum.CompanyID,
		IDs:       datum.IDs,
	})
	if err != nil {
		return nil, err
	}

	return convSourcesPB(sources), nil
}

// Delete 删除 Source
func (l *Logic) Delete(ctx context.Context, datum *DeleteSourceDatum) error {
	if datum == nil || datum.SourceID <= 0 {
		log.ErrorContextf(ctx, "Delete Source params is invalid, datum:%+v", datum)

		return status.Error(codes.InvalidArgument, "params is invalid")
	}
	if err := l.sourceRepo.Delete(ctx, datum.SourceID, datum.StaffID); err != nil {
		return err
	}

	return nil
}

// convSourcesPB repo Source -> pb Source
func convSourcesPB(sources []*source.Source) []*customerpb.Source {
	res := make([]*customerpb.Source, 0, len(sources))
	for _, s := range sources {
		if s == nil {
			continue
		}
		res = append(res, convSourcePB(s))
	}

	return res
}

func convSourcePB(s *source.Source) *customerpb.Source {
	return &customerpb.Source{
		Id:   s.ID,
		Name: s.Name,
		Sort: s.Sort,
	}
}
