load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "customer_view",
    srcs = [
        "customer_view.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_view",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/db/customer_view",
        "//backend/app/customer/repo/redis",
        "//backend/app/customer/utils",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v1:customer",
        "@com_github_bytedance_sonic//:sonic",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)
