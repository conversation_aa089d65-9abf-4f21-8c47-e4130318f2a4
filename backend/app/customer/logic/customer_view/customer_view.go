package customerview

import (
	"context"
	"strings"

	"github.com/bytedance/sonic"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	customerview "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer_view"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/redis"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

type Logic struct {
	customerViewRepo customerview.ReadWriter
	redisRepo        redis.API
}

func New() *Logic {
	return &Logic{
		customerViewRepo: customerview.New(),
		redisRepo:        redis.New(),
	}
}

func (l *Logic) Create(ctx context.Context, datum *CreateViewDatum) (int64, error) {
	// check
	if datum == nil ||
		datum.CompanyID <= 0 ||
		datum.StaffID <= 0 ||
		datum.Type == customerpb.CustomerView_TYPE_UNSPECIFIED ||
		datum.Title == "" {
		log.InfoContextf(ctx, "Create View params is invalid, datum: %+v", datum)

		return 0, status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// check name
	existNameList, err := l.customerViewRepo.List(ctx, &customerview.ListCustomerViewsDatum{
		CompanyID: &datum.CompanyID,
		StaffID:   &datum.StaffID,
		Titles:    []string{datum.Title},
		Type:      &datum.Type,
	})
	if err != nil {
		log.ErrorContextf(ctx, "Create View List same name err, err: %+v", err)

		return 0, err
	}
	if len(existNameList) > 0 {
		log.InfoContextf(ctx, "Create View name is exist, datum: %+v", datum)

		return 0, errs.New(customerpb.ErrCode_ERR_CODE_VIEW_NAME_EXIST)
	}

	// save to db
	db, err := convViewDB(ctx, datum)
	if err != nil {
		return 0, err
	}
	if err := l.customerViewRepo.Create(ctx, db); err != nil {
		return 0, err
	}

	return db.ID, nil
}

var (
	defaultEmptyFilter = &customerpb.CustomerView_Filter{
		Type:     nil,
		Filters:  []*customerpb.CustomerView_Filter{},
		Operator: nil,
		Property: nil,
		Value:    nil,
		Values:   nil, // 这里为 nil，序列化为 null
	}
)

func convViewDB(ctx context.Context, datum *CreateViewDatum) (*customerview.CustomerView, error) {
	res := &customerview.CustomerView{
		CompanyID: datum.CompanyID,
		StaffID:   datum.StaffID,
		Title:     datum.Title,
	}
	if len(datum.Fields) > 0 {
		res.Fields = customerutils.ToPointer(strings.Join(datum.Fields, ","))
	}
	if datum.OrderBy != nil {
		orderByStr, err := sonic.MarshalString(datum.OrderBy)
		if err != nil {
			log.ErrorContextf(ctx, "convViewDB OrderBy MarshalString err, err:%v, orderBy:%+v", err, datum.OrderBy)

			return nil, err
		}
		res.OrderBy = &orderByStr
	}
	if datum.Filter == nil {
		datum.Filter = defaultEmptyFilter
	}
	filterStr, err := sonic.MarshalString(convertPBToCustomerViewFilter(datum.Filter))
	if err != nil {
		log.ErrorContextf(ctx, "convViewDB OrderBy MarshalString err, err:%v, Filter:%+v", err, datum.Filter)

		return nil, err
	}
	res.Filter = filterStr

	return res, nil
}

func convertPBToCustomerViewFilter(pb *customerpb.CustomerView_Filter) *customerview.CustomerViewFilter {
	if pb == nil {
		return nil
	}

	// 处理 Values 字段
	var valuesPtr *[]string
	if pb.Values != nil {
		tmp := make([]string, len(pb.Values))
		copy(tmp, pb.Values)
		valuesPtr = &tmp
	}

	// 递归处理 Filters
	var filters []*customerview.CustomerViewFilter
	if pb.Filters != nil {
		filters = make([]*customerview.CustomerViewFilter, 0, len(pb.Filters))
		for _, child := range pb.Filters {
			filters = append(filters, convertPBToCustomerViewFilter(child))
		}
	} else {
		filters = []*customerview.CustomerViewFilter{} // 保证是空数组，不是 null
	}

	return &customerview.CustomerViewFilter{
		Type:     pb.Type,
		Filters:  filters,
		Operator: pb.Operator,
		Property: pb.Property,
		Value:    pb.Value,
		Values:   valuesPtr,
	}
}

func (l *Logic) Update(ctx context.Context, datum *UpdateViewDatum) error {
	// check
	if datum == nil || datum.ID <= 0 || datum.CompanyID <= 0 || datum.StaffID <= 0 ||
		(datum.Title == nil && len(datum.Fields) == 0 && datum.Filter == nil && datum.OrderBy == nil) ||
		(datum.Title != nil && *datum.Title == "") {
		log.InfoContextf(ctx, "Update View params is invalid, datum: %+v", datum)

		return status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// check id
	dbList, err := l.customerViewRepo.List(ctx, &customerview.ListCustomerViewsDatum{
		IDs:       []int64{datum.ID},
		StaffID:   &datum.StaffID,
		CompanyID: &datum.CompanyID,
	})
	if err != nil {
		return err
	}
	if len(dbList) == 0 {
		log.InfoContextf(ctx, "Update View id not found, id:%d, staffID:%d, companyID:%d",
			datum.ID, datum.StaffID, datum.CompanyID)

		return status.Errorf(codes.NotFound, "id not found")
	}
	existView := dbList[0]

	// check is_default
	if existView.IsDefault == 1 {
		log.InfoContextf(ctx, "Update View try to update default view, id: %d", datum.ID)

		return status.Errorf(codes.InvalidArgument, "default view is not to update")
	}

	// check title
	if datum.Title != nil {
		existNameList, err := l.customerViewRepo.List(ctx, &customerview.ListCustomerViewsDatum{
			CompanyID: &existView.CompanyID,
			StaffID:   &existView.StaffID,
			Titles:    []string{*datum.Title},
			Type:      &existView.Type,
		})
		if err != nil {
			log.ErrorContextf(ctx, "Update View List same name err, err: %+v", err)

			return err
		}
		if len(existNameList) > 0 {
			log.InfoContextf(ctx, "Update View name is exist, datum: %+v", datum)

			return errs.New(customerpb.ErrCode_ERR_CODE_VIEW_NAME_EXIST)
		}
	}

	// save to db
	updateDB, err := convViewUpdateDB(ctx, datum)
	if err != nil {
		return err
	}
	if err := l.customerViewRepo.Update(ctx, updateDB); err != nil {
		return err
	}

	return nil
}

func convViewUpdateDB(ctx context.Context, datum *UpdateViewDatum) (*customerview.CustomerView, error) {
	res := &customerview.CustomerView{
		ID: datum.ID,
	}
	if datum.Title != nil {
		res.Title = *datum.Title
	}
	if len(datum.Fields) > 0 {
		res.Fields = customerutils.ToPointer(strings.Join(datum.Fields, ","))
	}
	if datum.OrderBy != nil {
		orderByStr, err := sonic.MarshalString(datum.OrderBy)
		if err != nil {
			log.ErrorContextf(ctx, "convViewUpdateDB OrderBy MarshalString err, err:%v, orderBy:%+v",
				err, datum.OrderBy)

			return nil, err
		}
		res.OrderBy = &orderByStr
	}
	if datum.Filter != nil {
		filterStr, err := sonic.MarshalString(convertPBToCustomerViewFilter(datum.Filter))
		if err != nil {
			log.ErrorContextf(ctx, "convViewUpdateDB OrderBy MarshalString err, err:%v, Filter:%+v", err, datum.Filter)

			return nil, err
		}
		res.Filter = filterStr
	}

	return res, nil
}

func (l *Logic) List(ctx context.Context, datum *ListViewsDatum) ([]*customerpb.CustomerView, error) {
	// check
	if datum == nil || datum.CompanyID <= 0 || datum.StaffID <= 0 {
		log.InfoContextf(ctx, "List View params is invalid, datum: %+v", datum)

		return nil, status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// list db
	views, err := l.customerViewRepo.List(ctx, &customerview.ListCustomerViewsDatum{
		CompanyID: &datum.CompanyID,
		StaffID:   &datum.StaffID,
		Type:      datum.Type,
	})
	if err != nil {
		return nil, err
	}

	// TODO: if empty and not init, init default life cycle

	viewsPB, err := convViewsPB(ctx, views)
	if err != nil {
		return nil, err
	}

	return viewsPB, nil
}

func convViewsPB(ctx context.Context, views []*customerview.CustomerView) ([]*customerpb.CustomerView, error) {
	res := make([]*customerpb.CustomerView, 0, len(views))
	for _, view := range views {
		if view == nil {
			continue
		}
		pb, err := convViewPB(ctx, view)
		if err != nil {
			return nil, err
		}
		res = append(res, pb)
	}

	return res, nil
}

func convViewPB(ctx context.Context, view *customerview.CustomerView) (*customerpb.CustomerView, error) {
	res := &customerpb.CustomerView{
		Id:        view.ID,
		CompanyId: view.CompanyID,
		StaffId:   view.StaffID,
		IsDefault: view.IsDefault,
		Title:     view.Title,
		Type:      view.Type,
	}

	if view.Fields != nil {
		res.Fields = strings.Split(*view.Fields, ",")
	}
	if view.OrderBy != nil {
		res.OrderBy = &customerpb.CustomerView_OrderBy{}
		if err := sonic.UnmarshalString(*view.OrderBy, res.OrderBy); err != nil {
			log.ErrorContextf(ctx, "convViewPB OrderBy UnmarshalString err, err:%v, OrderBy:%s", err, *view.OrderBy)

			return nil, err
		}
	}

	filter := &customerview.CustomerViewFilter{}
	if err := sonic.UnmarshalString(view.Filter, filter); err != nil {
		log.ErrorContextf(ctx, "convViewPB Filter UnmarshalString err, err:%v, Filter:%s", err, view.Filter)

		return nil, err
	}
	res.Filter = convertCustomerViewFilterToPB(filter)

	return res, nil
}

// convertCustomerViewFilterToPB 将 customerview.CustomerViewFilter 结构体转换为 customerpb.CustomerView_Filter 结构体
func convertCustomerViewFilterToPB(filter *customerview.CustomerViewFilter) *customerpb.CustomerView_Filter {
	if filter == nil {
		return nil
	}

	// 处理 Values 字段
	var values []string
	if filter.Values != nil {
		values = make([]string, len(*filter.Values))
		copy(values, *filter.Values)
	}

	// 递归处理 Filters
	var filters []*customerpb.CustomerView_Filter
	if filter.Filters != nil {
		filters = make([]*customerpb.CustomerView_Filter, 0, len(filter.Filters))
		for _, child := range filter.Filters {
			filters = append(filters, convertCustomerViewFilterToPB(child))
		}
	}

	return &customerpb.CustomerView_Filter{
		Type:     filter.Type,
		Filters:  filters,
		Operator: filter.Operator,
		Property: filter.Property,
		Value:    filter.Value,
		Values:   values,
	}
}

func (l *Logic) Delete(ctx context.Context, datum *DeleteViewDatum) error {
	// check
	if datum == nil || datum.ID <= 0 || datum.CompanyID <= 0 || datum.StaffID <= 0 {
		log.InfoContext(ctx, "Delete View params is invalid, datum: %+v", datum)

		return status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// check id
	dbList, err := l.customerViewRepo.List(ctx, &customerview.ListCustomerViewsDatum{
		IDs:       []int64{datum.ID},
		CompanyID: &datum.CompanyID,
		StaffID:   &datum.StaffID,
	})
	if err != nil {
		return err
	}
	if len(dbList) == 0 {
		log.InfoContextf(ctx, "Delete View id not found, id:%d, companyID:%d, staffID:%d",
			datum.ID, datum.CompanyID, datum.StaffID)

		return status.Errorf(codes.NotFound, "id not found")
	}
	existView := dbList[0]

	// check is_default
	if existView.IsDefault == 1 {
		log.InfoContextf(ctx, "Delete View try to delete default view, id: %d", datum.ID)

		return status.Errorf(codes.InvalidArgument, "default view is not to delete")
	}

	// delete db
	if err := l.customerViewRepo.Delete(ctx, datum.ID); err != nil {
		return err
	}

	return nil
}
