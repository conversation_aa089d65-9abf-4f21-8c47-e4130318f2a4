package customerview

import customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"

type CreateViewDatum struct {
	Title     string
	Fields    []string
	OrderBy   *customerpb.CustomerView_OrderBy
	Filter    *customerpb.CustomerView_Filter
	StaffID   int64
	CompanyID int64
	Type      customerpb.CustomerView_Type
}

type UpdateViewDatum struct {
	ID        int64
	Title     *string
	Fields    []string
	OrderBy   *customerpb.CustomerView_OrderBy
	Filter    *customerpb.CustomerView_Filter
	StaffID   int64
	CompanyID int64
}

type ListViewsDatum struct {
	CompanyID int64
	StaffID   int64
	Type      *customerpb.CustomerView_Type
}

type DeleteViewDatum struct {
	ID        int64
	CompanyID int64
	StaffID   int64
}
