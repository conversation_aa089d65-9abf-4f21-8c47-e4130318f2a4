package taskv2

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/task"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Logic struct {
	taskRepo task.ReadWriter
}

func New() *Logic {
	return &Logic{
		taskRepo: task.New(),
	}
}

func (l *Logic) Create(ctx context.Context, datum *CreateTaskDatum) (int64, error) {
	// check
	if datum.CustomerID <= 0 || datum.CompanyID <= 0 ||
		datum.Name == "" || datum.State == customerpb.Task_STATE_UNSPECIFIED {
		log.ErrorContextf(ctx, "CreateTask params is invalid, datum:%+v", datum)

		return 0, status.Error(codes.InvalidArgument, "params is invalid")
	}

	// conv db
	db := convTaskDB(ctx, datum)

	// save to db
	if err := l.taskRepo.Create(ctx, db); err != nil {
		return 0, err
	}

	return db.ID, nil
}

func (l *Logic) Update(ctx context.Context, datum *UpdateTaskDatum) error {
	// check
	if datum.TaskID <= 0 || datum.StaffID <= 0 {
		log.ErrorContextf(ctx, "UpdateTask params is invalid, datum:%+v", datum)

		return status.Error(codes.InvalidArgument, "params is invalid")
	}

	// conv update db
	db, err := convUpdateTaskDB(ctx, datum)
	if err != nil {
		return err
	}

	// update to db
	if err := l.taskRepo.Update(ctx, db); err != nil {
		return err
	}

	return nil
}

func (l *Logic) List(ctx context.Context, datum *ListTasksDatum) ([]*customerpb.Task, error) {
	// check
	if datum.CustomerID <= 0 {
		log.ErrorContextf(ctx, "ListTask params is invalid, datum:%+v", datum)

		return nil, status.Error(codes.InvalidArgument, "params is invalid")
	}

	// list db
	tasks, err := l.taskRepo.List(ctx, &task.ListTasksDatum{CustomerID: &datum.CustomerID})
	if err != nil {
		return nil, err
	}

	return convTasksPB(tasks), nil
}

func (l *Logic) Delete(ctx context.Context, datum *DeleteTasksDatum) error {
	// check
	if datum == nil || datum.TaskID <= 0 || datum.StaffID <= 0 {
		log.ErrorContextf(ctx, "DeleteTask params is invalid, datum:%+v", datum)

		return status.Error(codes.InvalidArgument, "params is invalid")
	}

	// delete
	if err := l.taskRepo.Delete(ctx, datum.TaskID, datum.StaffID); err != nil {
		return err
	}

	return nil
}

func convTasksPB(tasks []*task.Task) []*customerpb.Task {
	res := make([]*customerpb.Task, 0, len(tasks))
	for _, task := range tasks {
		if task == nil {
			continue
		}
		res = append(res, convTaskPB(task))
	}

	return res
}

func convTaskPB(task *task.Task) *customerpb.Task {
	res := &customerpb.Task{
		Id:    task.ID,
		Name:  task.Name,
		State: task.State,
	}
	if task.AllocateStaffID != nil {
		res.AllocateStaffId = task.AllocateStaffID
	}
	if task.CompleteTime != nil {
		res.CompleteTime = timestamppb.New(*task.CompleteTime)
	}

	return res
}

func convUpdateTaskDB(ctx context.Context, datum *UpdateTaskDatum) (*task.Task, error) {
	res := &task.Task{
		ID:         datum.TaskID,
		UpdateBy:   datum.StaffID,
		UpdateTime: time.Now(),
	}
	if datum.Name != nil {
		if *datum.Name == "" {
			log.Error(ctx, "convUpdateTaskDB name is empty")

			return nil, status.Error(codes.InvalidArgument, "name is invalid")
		}
		res.Name = *datum.Name
	}
	if datum.AllocateStaffID != nil {
		res.AllocateStaffID = datum.AllocateStaffID
	}
	if datum.CompleteTime != nil {
		res.CompleteTime = customerutils.ToPointer((*datum.CompleteTime).AsTime())
	}
	if datum.State != nil {
		if *datum.State == customerpb.Task_STATE_UNSPECIFIED {
			log.ErrorContextf(ctx, "convUpdateTaskDB State is invalid, state:%s", (*datum.State).String())

			return nil, status.Error(codes.InvalidArgument, "state is invalid")
		}
		res.State = *datum.State
	}

	return res, nil
}

func convTaskDB(_ context.Context, datum *CreateTaskDatum) *task.Task {
	res := &task.Task{
		CompanyID:  datum.CompanyID,
		BusinessID: datum.BusinessID,
		CustomerID: datum.CustomerID,
		Name:       datum.Name,
		State:      datum.State,
		CreateBy:   datum.StaffID,
		UpdateBy:   datum.StaffID,
	}
	if datum.AllocateStaffID != nil && *datum.AllocateStaffID > 0 {
		res.AllocateStaffID = datum.AllocateStaffID
	}
	if datum.CompleteTime != nil {
		completeTime := (*datum.CompleteTime).AsTime()
		res.CompleteTime = &completeTime
	}

	return res
}
