package activitylog

import (
	"context"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	activitylog "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/activity_log"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Logic struct {
	activityLogRepo activitylog.ReadWriter
}

func New() *Logic {
	return &Logic{
		activityLogRepo: activitylog.New(),
	}
}

func (l *Logic) Create(ctx context.Context, datum *CreateActivityLogDatum) (int64, error) {
	// check
	if datum == nil || datum.CompanyID <= 0 || datum.BusinessID <= 0 ||
		datum.CustomerID <= 0 || datum.Action == nil || datum.Source == nil {
		log.ErrorContextf(ctx, "CreateActivityLog params is invalid, datum:%+v", datum)

		return 0, status.Error(codes.InvalidArgument, "params is invalid")
	}

	// conv db
	db, err := convActivityLogDB(ctx, datum)
	if err != nil {
		return 0, err
	}

	// save to db
	if err := l.activityLogRepo.Create(ctx, db); err != nil {
		return 0, err
	}

	return db.ID, nil
}

func (l *Logic) Update(ctx context.Context, datum *UpdateActivityLogDatum) error {
	// check
	if datum == nil || datum.LogID <= 0 || datum.StaffID <= 0 {
		log.ErrorContext(ctx, "UpdateActivityLog params is invalid, datum:%+v", datum)

		return status.Error(codes.InvalidArgument, "params is invalid")
	}

	// get activity log
	activityLog, err := l.activityLogRepo.Get(ctx, datum.LogID)
	if err != nil {
		return err
	}

	// convUpdateDB
	activityLogDB, err := convUpdateActivityLogDB(ctx, datum)
	if err != nil {
		return err
	}

	// only support update note
	if activityLogDB.Type != customerpb.ActivityLog_TYPE_UNSPECIFIED {
		if activityLogDB.Type != customerpb.ActivityLog_NOTE || activityLog.Type != customerpb.ActivityLog_NOTE {
			return status.Error(codes.InvalidArgument, "only support update note")
		}
	}

	// update DB
	if err := l.activityLogRepo.Update(ctx, activityLogDB); err != nil {
		return err
	}

	return nil
}

func (l *Logic) List(ctx context.Context, datum *ListActivityLogsDatum) (
	[]*customerpb.ActivityLog, int32, error) {
	// check
	if datum == nil || (customerutils.ToValue(datum.CustomerID) == 0 && customerutils.ToValue(datum.CompanyID) == 0) {
		log.ErrorContext(ctx, "ListCustomerActivityLogs params is invalid", zap.Any("datum", datum))

		return nil, 0, status.Error(codes.InvalidArgument, "params is invalid")
	}
	if datum.PageNum == 0 {
		datum.PageNum = 1
	}
	if datum.PageSize == 0 {
		datum.PageSize = 10
	}

	// select data form db
	logs, total, err := l.activityLogRepo.List(ctx, convListDB(datum))
	if err != nil {
		return nil, 0, err
	}

	return convActivityLogsPB(logs), int32(total), nil
}

func convListDB(datum *ListActivityLogsDatum) *activitylog.ListActivityLogsDatum {
	return &activitylog.ListActivityLogsDatum{
		CustomerID:      datum.CustomerID,
		ActivityLogType: datum.ActivityLogType,
		PageSize:        datum.PageSize,
		PageNum:         datum.PageNum,
		CompanyID:       datum.CompanyID,
	}
}

func convActivityLogsPB(logs []*activitylog.ActivityLog) []*customerpb.ActivityLog {
	res := make([]*customerpb.ActivityLog, 0, len(logs))
	for _, log := range logs {
		if log == nil {
			continue
		}
		res = append(res, convActivityLogPB(log))
	}

	return res
}

func convActivityLogPB(log *activitylog.ActivityLog) *customerpb.ActivityLog {
	return &customerpb.ActivityLog{
		Id:                  log.ID,
		CustomerId:          log.CustomerID,
		CustomerName:        log.CustomerName,
		CustomerPhoneNumber: log.CustomerPhoneNumber,
		Type:                log.Type,
		Action:              log.Action,
		CreateTime:          timestamppb.New(log.CreateTime),
		Source:              log.Source,
	}
}

func convActivityLogDB(ctx context.Context, datum *CreateActivityLogDatum) (
	*activitylog.ActivityLog, error) {
	res := &activitylog.ActivityLog{
		CompanyID:           datum.CompanyID,
		BusinessID:          datum.BusinessID,
		CustomerID:          datum.CustomerID,
		CustomerName:        datum.CustomerName,
		CustomerPhoneNumber: datum.CustomerPhoneNumber,
		Action:              datum.Action,
		Source:              datum.Source,
	}
	activityLogType, err := getActionType(ctx, datum.Action)
	if err != nil {
		return nil, err
	}
	res.Type = activityLogType

	return res, nil
}

func getActionType(ctx context.Context, action *customerpb.ActivityLog_Action) (
	customerpb.ActivityLog_Type, error) {
	switch action.Action.(type) {
	case *customerpb.ActivityLog_Action_Message:
		return customerpb.ActivityLog_MESSAGE, nil
	case *customerpb.ActivityLog_Action_Call:
		return customerpb.ActivityLog_CALL, nil
	case *customerpb.ActivityLog_Action_Note:
		return customerpb.ActivityLog_NOTE, nil
	case *customerpb.ActivityLog_Action_Task:
		return customerpb.ActivityLog_TASK, nil
	case *customerpb.ActivityLog_Action_Convert:
		return customerpb.ActivityLog_CONVERT, nil
	case *customerpb.ActivityLog_Action_Create:
		return customerpb.ActivityLog_CREATE, nil
	default:
		log.ErrorContextf(ctx, "getActionType action is unsupported, action:%+v", action)

		return customerpb.ActivityLog_TYPE_UNSPECIFIED, status.Error(codes.InvalidArgument, "action is unsupported")
	}
}

func convUpdateActivityLogDB(ctx context.Context, datum *UpdateActivityLogDatum) (
	*activitylog.ActivityLog, error) {
	res := &activitylog.ActivityLog{
		ID:         datum.LogID,
		UpdateTime: time.Now(),
	}
	if datum.Action != nil {
		res.Action = datum.Action
		activityLogType, err := getActionType(ctx, datum.Action)
		if err != nil {
			return nil, err
		}
		res.Type = activityLogType
	}

	return res, nil
}
