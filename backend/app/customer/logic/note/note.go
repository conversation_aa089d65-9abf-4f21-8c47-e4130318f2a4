package note

import (
	"context"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/note"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

// Logic 逻辑层
type Logic struct {
	noteRepo note.ReadWriter
}

// New 构造函数
func New() *Logic {
	return &Logic{
		noteRepo: note.New(),
	}
}

// Create 创建 Note
func (l *Logic) Create(ctx context.Context, datum *CreateNoteDatum) (int64, error) {
	// check
	if datum == nil || datum.CustomerID <= 0 ||
		datum.Note == "" || datum.CreateSource == nil {
		log.Error(ctx, "Create Note params is invalid", zap.Any("datum", datum))

		return 0, status.Error(codes.InvalidArgument, "params is invalid")
	}

	// conv db
	dbNote := &note.Note{
		CompanyID:    datum.CompanyID,
		BusinessID:   datum.BusinessID,
		CustomerID:   datum.CustomerID,
		Note:         datum.Note,
		CreateSource: datum.CreateSource,
		UpdateSource: datum.CreateSource,
		CreateBy:     datum.CreateSource.SourceId,
		UpdateBy:     datum.CreateSource.SourceId,
		CreateTime:   time.Now(),
		UpdateTime:   time.Now(),
	}

	// db insert
	if err := l.noteRepo.Create(ctx, dbNote); err != nil {
		return 0, err
	}

	return dbNote.ID, nil
}

// Update 更新 Note
func (l *Logic) Update(ctx context.Context, datum *UpdateNoteDatum) error {
	// 参数校验
	if datum == nil || datum.NoteID <= 0 || datum.UpdateSource == nil || datum.Note == nil {
		log.Error(ctx, "Update Note params is invalid", zap.Any("datum", datum))

		return status.Error(codes.InvalidArgument, "params is invalid")
	}

	// conv db
	dbNote := &note.Note{
		ID:           datum.NoteID,
		UpdateBy:     datum.UpdateSource.SourceId,
		UpdateTime:   time.Now(),
		UpdateSource: datum.UpdateSource,
	}
	if datum.Note != nil {
		if *datum.Note == "" {
			log.Error(ctx, "Update Note content is empty")

			return status.Error(codes.InvalidArgument, "note is empty")
		}
		dbNote.Note = *datum.Note
	}

	// update
	if err := l.noteRepo.Update(ctx, dbNote); err != nil {
		return err
	}

	return nil
}

// List 查询 Note 列表
func (l *Logic) List(ctx context.Context, datum *ListNotesDatum) ([]*customerpb.Note, error) {
	// 参数校验
	if datum == nil || (customerutils.ToValue(datum.CustomerID) == 0 && len(datum.IDs) == 0) {
		log.Error(ctx, "List Note params is invalid", zap.Any("datum", datum))

		return nil, status.Error(codes.InvalidArgument, "params is invalid")
	}
	notes, err := l.noteRepo.List(ctx, &note.ListNotesDatum{CustomerID: datum.CustomerID, IDs: datum.IDs})
	if err != nil {
		return nil, err
	}

	return convNotesPB(notes), nil
}

// Delete 删除 Note
func (l *Logic) Delete(ctx context.Context, datum *DeleteNoteDatum) error {
	// 参数校验
	if datum == nil || datum.NoteID <= 0 || datum.StaffID <= 0 {
		log.Error(ctx, "Delete Note params is invalid", zap.Any("datum", datum))

		return status.Error(codes.InvalidArgument, "params is invalid")
	}
	if err := l.noteRepo.Delete(ctx, datum.NoteID, datum.StaffID); err != nil {
		return err
	}

	return nil
}

// convNotesPB repo Note -> pb Note
func convNotesPB(notes []*note.Note) []*customerpb.Note {
	res := make([]*customerpb.Note, 0, len(notes))
	for _, n := range notes {
		if n == nil {
			continue
		}
		res = append(res, convNotePB(n))
	}

	return res
}

func convNotePB(n *note.Note) *customerpb.Note {
	return &customerpb.Note{
		Id:           n.ID,
		Text:         n.Note,
		CreateSource: n.CreateSource,
		UpdateSource: n.UpdateSource,
		CreateTime:   timestamppb.New(n.CreateTime),
		UpdateTime:   timestamppb.New(n.UpdateTime),
		CustomerId:   n.CustomerID,
	}
}
