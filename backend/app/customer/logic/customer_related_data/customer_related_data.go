package customerrelateddata

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerrelateddatarepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer_related_data"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Logic struct {
	customerRelatedDataRepo customerrelateddatarepo.Repository
}

func New() *Logic {
	return &Logic{
		customerRelatedDataRepo: customerrelateddatarepo.New(),
	}
}

func NewByParams(
	customerRelatedDataRepo customerrelateddatarepo.Repository,
) *Logic {
	return &Logic{
		customerRelatedDataRepo: customerRelatedDataRepo,
	}
}

func (l *Logic) Create(
	ctx context.Context, data *CustomerRelatedData,
) (*CustomerRelatedData, error) {
	// create customer related data, set output only columns
	now := time.Now().UTC()
	data.CreatedTime = now
	data.UpdatedTime = now
	data.State = customerpb.CustomerRelatedData_ACTIVE

	dbData := data.ToDB()

	dbData, err := l.customerRelatedDataRepo.Create(ctx, dbData)
	if err != nil {
		return nil, errs.New(customerpb.ErrCode_ERR_CODE_CREATE_CUSTOMER_FAILED)
	}
	// convert to logic customer related data, and return
	return convertToCustomerRelatedData(dbData), nil
}

func (l *Logic) Get(ctx context.Context, id int64) (*CustomerRelatedData, error) {
	dbData, err := l.customerRelatedDataRepo.Get(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_CUSTOMER_NOT_FOUND)
		}

		return nil, err
	}

	return convertToCustomerRelatedData(dbData), nil
}

func (l *Logic) GetByCustomerID(
	ctx context.Context, customerID int64,
) (*CustomerRelatedData, error) {
	dbData, err := l.customerRelatedDataRepo.GetByCustomerID(ctx, customerID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_CUSTOMER_NOT_FOUND)
		}

		return nil, err
	}

	return convertToCustomerRelatedData(dbData), nil
}

func (l *Logic) List(
	ctx context.Context, req *ListCustomerRelatedDataRequest,
) (*ListCustomerRelatedDataResponse, error) {
	// 构建过滤器
	filter := &customerrelateddatarepo.ListFilter{
		IDs:         req.Filter.IDs,
		CustomerIDs: req.Filter.CustomerIDs,
		BusinessIDs: req.Filter.BusinessIDs,
		CompanyIDs:  req.Filter.CompanyIDs,
		States:      req.Filter.States,
	}

	// 构建分页
	pagination := &customerrelateddatarepo.Pagination{
		PageSize:        req.Pagination.PageSize,
		ReturnTotalSize: req.Pagination.ReturnTotalSize,
	}

	// 解析游标
	if req.Pagination.Cursor != "" {
		pagination.Cursor = customerutils.DecodeCursor(req.Pagination.Cursor)
	}

	// 构建排序
	orderBy := &customerrelateddatarepo.OrderBy{
		Field:     req.OrderBy.Field,
		Direction: req.OrderBy.Direction,
	}

	// 查询数据
	result, err := l.customerRelatedDataRepo.ListByCursor(ctx, filter, pagination, orderBy)
	if err != nil {
		return nil, err
	}

	// 转换数据
	dataList := make([]*CustomerRelatedData, 0, len(result.Data))
	for _, data := range result.Data {
		dataList = append(dataList, convertToCustomerRelatedData(data))
	}

	// 生成下一页游标
	var nextToken string
	if result.HasNext && len(result.Data) > 0 {
		lastItem := result.Data[len(result.Data)-1]
		cursor := &postgres.Cursor{ID: lastItem.ID, CreatedAt: lastItem.CreatedTime}
		nextToken = cursor.EncodeCursor()
	}

	response := &ListCustomerRelatedDataResponse{
		CustomerRelatedData: dataList,
		HasNext:             result.HasNext,
		NextToken:           nextToken,
		TotalSize:           result.TotalCount,
	}

	return response, nil
}

func (l *Logic) Update(
	ctx context.Context, id int64, updateRef *UpdateCustomerRelatedDataRequest,
) (*CustomerRelatedData, error) {
	// check customer related data exists
	data, err := l.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	now := time.Now().UTC()
	data.UpdatedTime = now
	data.ClientColor = updateRef.ClientColor
	data.IsBlockMessage = updateRef.IsBlockMessage
	data.IsBlockOnlineBooking = updateRef.IsBlockOnlineBooking
	data.LoginEmail = updateRef.LoginEmail
	data.ReferralSourceID = updateRef.ReferralSourceID
	data.ReferralSourceDesc = updateRef.ReferralSourceDesc
	data.SendAutoEmail = updateRef.SendAutoEmail
	data.SendAutoMessage = updateRef.SendAutoMessage
	data.SendAppAutoMessage = updateRef.SendAppAutoMessage
	data.PreferredGroomerID = updateRef.PreferredGroomerID
	data.PreferredFrequencyDay = updateRef.PreferredFrequencyDay
	data.PreferredFrequencyType = updateRef.PreferredFrequencyType
	data.PreferredDay = updateRef.PreferredDay
	data.PreferredTime = updateRef.PreferredTime
	data.IsUnsubscribed = updateRef.IsUnsubscribed
	data.Birthday = updateRef.Birthday
	data.CustomizeLifeCycleID = updateRef.CustomizeLifeCycleID
	data.CustomizeActionStateID = updateRef.CustomizeActionStateID

	updatedData, err := l.customerRelatedDataRepo.Update(ctx, data.ToDB())
	if err != nil {
		return nil, err
	}

	return convertToCustomerRelatedData(updatedData), nil
}

func (l *Logic) Delete(ctx context.Context, id int64, inactivate bool) error {
	dbData, err := l.Get(ctx, id)
	if err != nil {
		return err
	}

	now := time.Now().UTC()
	dbData.UpdatedTime = now
	dbData.State = customerpb.CustomerRelatedData_INACTIVE
	if !inactivate {
		deletedTime := now
		dbData.DeletedTime = &deletedTime
		dbData.State = customerpb.CustomerRelatedData_DELETED
	}
	_, err = l.customerRelatedDataRepo.Update(ctx, dbData.ToDB())
	if err != nil {
		return err
	}

	return nil
}

func convertToCustomerRelatedData(dbData *customerrelateddatarepo.CustomerRelatedData) *CustomerRelatedData {
	return &CustomerRelatedData{
		ID:                     dbData.ID,
		CustomerID:             dbData.CustomerID,
		BusinessID:             dbData.BusinessID,
		CompanyID:              dbData.CompanyID,
		ClientColor:            dbData.ClientColor,
		IsBlockMessage:         dbData.IsBlockMessage,
		IsBlockOnlineBooking:   dbData.IsBlockOnlineBooking,
		LoginEmail:             dbData.LoginEmail,
		ReferralSourceID:       dbData.ReferralSourceID,
		ReferralSourceDesc:     dbData.ReferralSourceDesc,
		SendAutoEmail:          dbData.SendAutoEmail,
		SendAutoMessage:        dbData.SendAutoMessage,
		SendAppAutoMessage:     dbData.SendAppAutoMessage,
		UnconfirmedReminderBy:  dbData.UnconfirmedReminderBy,
		PreferredGroomerID:     dbData.PreferredGroomerID,
		PreferredFrequencyDay:  dbData.PreferredFrequencyDay,
		PreferredFrequencyType: dbData.PreferredFrequencyType,
		LastServiceTime:        dbData.LastServiceTime,
		Source:                 dbData.Source,
		ExternalID:             dbData.ExternalID,
		CreateBy:               dbData.CreateBy,
		UpdateBy:               dbData.UpdateBy,
		IsRecurring:            dbData.IsRecurring,
		ShareApptStatus:        dbData.ShareApptStatus,
		ShareRangeType:         dbData.ShareRangeType,
		ShareRangeValue:        dbData.ShareRangeValue,
		ShareApptJSON:          dbData.ShareApptJSON,
		PreferredDay:           dbData.PreferredDay,
		PreferredTime:          dbData.PreferredTime,
		AccountID:              dbData.AccountID,
		CustomerCode:           dbData.CustomerCode,
		IsUnsubscribed:         dbData.IsUnsubscribed,
		Birthday:               dbData.Birthday,
		ActionState:            dbData.ActionState,
		CustomizeLifeCycleID:   dbData.CustomizeLifeCycleID,
		CustomizeActionStateID: dbData.CustomizeActionStateID,
		State:                  dbData.State,
		DeletedTime:            dbData.DeletedTime,
		CreatedTime:            dbData.CreatedTime,
		UpdatedTime:            dbData.UpdatedTime,
	}
}
