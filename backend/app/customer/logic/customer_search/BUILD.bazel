load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "customer_search",
    srcs = ["customer_search.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_search",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/db/customer",
        "//backend/app/customer/repo/pet",
        "//backend/app/customer/repo/search",
        "//backend/app/customer/utils",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/struct",
        "//backend/proto/pet/v1:pet",
        "//backend/proto/search/v1:search",
    ],
)
