package lifecycle

import (
	"context"
	"fmt"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	lifecycle "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/life_cycle"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/redis"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Logic struct {
	lifeCycleRepo lifecycle.ReadWriter
	redisRepo     redis.API
}

func New() *Logic {
	return &Logic{
		lifeCycleRepo: lifecycle.New(),
		redisRepo:     redis.New(),
	}
}

func (l *Logic) Create(ctx context.Context, datum *CreateLifeCycleDatum) (int64, error) {
	// check
	if datum == nil || datum.CompanyID == 0 || datum.Name == "" || datum.Sort == 0 {
		log.InfoContextf(ctx, "Create LifeCycle params is invalid, datum: %+v", datum)

		return 0, status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// check name
	existNameList, err := l.lifeCycleRepo.List(ctx, &lifecycle.ListLifeCyclesDatum{
		CompanyID: &datum.CompanyID,
		Names:     []string{datum.Name},
	})
	if err != nil {
		log.ErrorContextf(ctx, "Create LifeCycle List same name err, err: %+v", err)

		return 0, err
	}
	if len(existNameList) > 0 {
		log.InfoContextf(ctx, "Create LifeCycle name is exist, datum: %+v", datum)

		return 0, errs.New(customerpb.ErrCode_ERR_CODE_LIFE_CYCLE_NAME_EXIST)
	}

	// save to db
	db := convLifeCycleDB(datum)
	if err := l.lifeCycleRepo.Create(ctx, db); err != nil {
		return 0, err
	}

	return db.ID, nil
}

func convLifeCycleDB(datum *CreateLifeCycleDatum) *lifecycle.CustomerLifeCycle {
	return &lifecycle.CustomerLifeCycle{
		CompanyID:  datum.CompanyID,
		BusinessID: datum.BusinessID,
		CreatedBy:  datum.StaffID,
		UpdatedBy:  datum.StaffID,
		Name:       datum.Name,
		Sort:       datum.Sort,
	}
}

func (l *Logic) Update(ctx context.Context, datumList []*UpdateLifeCycleDatum, staffID, companyID int64) error {
	// check
	if staffID <= 0 || companyID <= 0 {
		log.InfoContextf(ctx, "Update LifeCycle staffID or companyID is invalid, "+
			"staffID: %d, companyID: %d", staffID, companyID)

		return status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// datum check && collect name && collect ids && conv to db entity
	names := make([]string, 0, len(datumList))
	idChangeNameMap := make(map[int64]*UpdateLifeCycleDatum, len(datumList))
	updates := make([]*lifecycle.CustomerLifeCycle, 0, len(datumList))
	for _, datum := range datumList {
		if datum == nil || datum.ID <= 0 ||
			(datum.Name == nil && datum.Sort == nil) ||
			(datum.Name != nil && *datum.Name == "") ||
			(datum.Sort != nil && *datum.Sort <= 0) {
			log.InfoContextf(ctx, "Update LifeCycle params is invalid, datum: %+v", datum)

			return status.Errorf(codes.InvalidArgument, "params is invalid")
		}
		update := &lifecycle.CustomerLifeCycle{
			ID:        datum.ID,
			UpdatedBy: staffID,
		}
		if datum.Name != nil {
			update.Name = *datum.Name
			names = append(names, *datum.Name)
			idChangeNameMap[datum.ID] = datum
		}
		if datum.Sort != nil {
			update.Sort = *datum.Sort
		}
		updates = append(updates, update)
	}
	// check names
	if len(names) > 0 {
		existNameList, err := l.lifeCycleRepo.List(ctx, &lifecycle.ListLifeCyclesDatum{
			CompanyID: &companyID,
			Names:     names,
		})
		if err != nil {
			log.ErrorContextf(ctx, "Update LifeCycle List same name err, err: %+v", err)

			return err
		}
		for _, existName := range existNameList {
			// existName cycle is the same update id and name, skip
			if idChangeNameMap[existName.ID] != nil && *idChangeNameMap[existName.ID].Name == existName.Name {
				continue
			}
			log.InfoContextf(ctx, "Update LifeCycle name is exist, names: %+v", names)

			return errs.New(customerpb.ErrCode_ERR_CODE_LIFE_CYCLE_NAME_EXIST)
		}
	}

	// check is update default cycles
	defaultCycles, err := l.lifeCycleRepo.List(ctx, &lifecycle.ListLifeCyclesDatum{
		CompanyID: &companyID,
		IsDefault: customerutils.ToPointer(int32(1)),
	})
	if err != nil {
		log.ErrorContextf(ctx, "Update LifeCycle List defaultCycles err, err: %+v", err)

		return err
	}
	for _, defaultCycle := range defaultCycles {
		if defaultCycle == nil || defaultCycle.ID <= 0 {
			continue
		}
		if idChangeNameMap[defaultCycle.ID] != nil {
			log.InfoContextf(ctx, "Update LifeCycle params try to update default cycles name, "+
				"defaultID: %d", defaultCycle.ID)

			return status.Errorf(codes.InvalidArgument, "params is invalid")
		}
	}

	// save to db
	if err := l.lifeCycleRepo.WithTransaction(ctx, func(api lifecycle.ReadWriter) error {
		for _, update := range updates {
			if err := api.Update(ctx, update); err != nil {
				return err
			}
		}

		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "Update LifeCycle WithTransaction err, err:%+v", err)

		return err
	}

	return nil
}

func (l *Logic) List(ctx context.Context, datum *ListLifeCyclesDatum) ([]*customerpb.LifeCycle, error) {
	// check
	if datum == nil || datum.CompanyID <= 0 {
		log.InfoContextf(ctx, "List LifeCycle params is invalid, datum: %+v", datum)

		return nil, status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// list db
	cycles, err := l.lifeCycleRepo.List(ctx, &lifecycle.ListLifeCyclesDatum{CompanyID: &datum.CompanyID})
	if err != nil {
		return nil, err
	}

	// if empty and not init, init default life cycle
	if len(cycles) == 0 {
		initCycles, err := l.InitLifeCycle(ctx, datum.CompanyID)
		if err != nil {
			return nil, err
		}
		cycles = initCycles
	}

	return convLifeCyclesPB(cycles), nil
}

func (l *Logic) InitLifeCycle(ctx context.Context, companyID int64) ([]*lifecycle.CustomerLifeCycle, error) {
	// check init before
	initKey := fmt.Sprintf(redis.CompanyInitLifeCycleKey, companyID)
	value, err := l.redisRepo.Get(ctx, initKey)
	if err != nil {
		return nil, err
	}
	if value != "" {
		return []*lifecycle.CustomerLifeCycle{}, nil
	}

	// add lock
	lock := fmt.Sprintf(redis.CompanyInitLifeCycleLockKey, companyID)
	if err := l.redisRepo.Lock(ctx, lock, redis.CompanyInitLifeCycleLockTTL); err != nil {
		return nil, err
	}
	defer func() {
		if err := l.redisRepo.Unlock(ctx, lock); err != nil {
			log.ErrorContextf(ctx, "InitLifeCycle Failed to release lock, lock:%s", lock)
		}
	}()

	// init
	if err := l.lifeCycleRepo.CreateBatch(ctx, buildDefaultLifeCycles(companyID)); err != nil {
		return nil, err
	}

	// set key
	if err := l.redisRepo.Set(ctx, initKey, "1"); err != nil {
		return nil, err
	}

	// get data
	cycles, err := l.lifeCycleRepo.List(ctx, &lifecycle.ListLifeCyclesDatum{CompanyID: &companyID})
	if err != nil {
		return nil, err
	}

	log.InfoContextf(ctx, "InitLifeCycle success, companyID:%d, cycles:%+v", companyID, cycles)

	return cycles, nil
}

var (
	defaultLifeCycleNames = []string{"Lead", "Opportunity", "Trash"}
)

func buildDefaultLifeCycles(companyID int64) []*lifecycle.CustomerLifeCycle {
	res := make([]*lifecycle.CustomerLifeCycle, 0, len(defaultLifeCycleNames))
	for i, name := range defaultLifeCycleNames {
		res = append(res, &lifecycle.CustomerLifeCycle{
			CompanyID:  companyID,
			BusinessID: 0,
			CreatedBy:  0,
			UpdatedBy:  0,
			Name:       name,
			Sort:       int32(i + 1),
			IsDefault:  int32(1),
		})
	}

	return res
}

func convLifeCyclesPB(cycles []*lifecycle.CustomerLifeCycle) []*customerpb.LifeCycle {
	res := make([]*customerpb.LifeCycle, 0, len(cycles))
	for _, cycle := range cycles {
		if cycle == nil {
			continue
		}
		res = append(res, convLifeCyclePB(cycle))
	}

	return res
}

func convLifeCyclePB(cycle *lifecycle.CustomerLifeCycle) *customerpb.LifeCycle {
	return &customerpb.LifeCycle{
		Id:        cycle.ID,
		Name:      cycle.Name,
		Sort:      cycle.Sort,
		IsDefault: cycle.IsDefault,
	}
}

func (l *Logic) Delete(ctx context.Context, datum *DeleteLifeCycleDatum) error {
	// check
	if datum == nil || datum.ID <= 0 {
		log.InfoContextf(ctx, "Delete LifeCycle params is invalid, datum: %+v", datum)

		return status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// check is default cycle
	cycles, err := l.lifeCycleRepo.List(ctx, &lifecycle.ListLifeCyclesDatum{IDs: []int64{datum.ID}})
	if err != nil {
		return err
	}
	if len(cycles) != 1 {
		log.InfoContextf(ctx, "Delete LifeCycle list cycles fail, datum: %+v", datum)

		return status.Errorf(codes.InvalidArgument, "life cycle is not exist")
	}
	if cycles[0].IsDefault == 1 {
		log.InfoContextf(ctx, "Delete LifeCycle cycle is default, datum: %+v", datum)

		return status.Errorf(codes.InvalidArgument, "life cycle is default")
	}

	// delete db
	if err := l.lifeCycleRepo.Delete(ctx, datum.ID, datum.StaffID); err != nil {
		return err
	}

	return nil
}
