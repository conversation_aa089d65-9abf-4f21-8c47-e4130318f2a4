package datadog

import (
	"context"
	"fmt"
	"os"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/entity"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/slack"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
)

var setupTestSvc sync.Once

func setUp() {
	setupTestSvc.Do(func() {
		currentDir, _ := os.Getwd()
		fmt.Printf("currentDir:%v\n", currentDir)
		currentDir = currentDir[:len(currentDir)-len("repo/datadog")]
		rpc.ServerConfigPath = currentDir + "config/testing/config.yaml"
		_ = rpc.NewServer()
	})
}

func TestPageATeam(t *testing.T) {
	t.Skip("manual test")
	setUp()
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	d := &IncidentGatewayImpl{
		slackClient:        slack.NewClient(cfg.CsPageWatcher.SlackToken),
		cfg:                cfg,
		csPageReaderWriter: entity.NewCsPageReaderWriter(),
	}
	err := d.pageATeam(context.Background(),
		cfg.CsPageWatcher.DatadogAPIKey, cfg.CsPageWatcher.DatadogAPPKey, "test page", "oncall-test")
	assert.Nil(t, err)
}

func TestTriggerIncident(t *testing.T) {
	t.Skip("manual test")
	setUp()
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	d := NewIncidentGateway(cfg,
		slack.NewClient(cfg.CsPageWatcher.SlackToken),
		entity.NewCsPageReaderWriter(),
	)
	jiraClient, err := jira.NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.Nil(t, err)

	issue, err := jiraClient.GetIssueDetails("IFRBE-2054")
	assert.Nil(t, err)

	err = d.TriggerIncident(context.Background(), &Incident{
		IssuePriority:      "P0-Block",
		Tier:               "T1 Ticket",
		CustomerStage:      "Go-live Day",
		NeedCreateIncident: true,
		OncallTeam:         "ZihaoTest",
		NeedT1Slack:        true,
		RefUsers: []string{
			issue.Assignee.EmailAddress,
			issue.Reporter.EmailAddress,
			"<EMAIL>",
		},
		Issue: issue,
	})
	assert.Nil(t, err)
}

func TestDatadogIncidentGatewayImpl_oncallTeam2Commander(t *testing.T) {
	t.Skip("manual test")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	d := &IncidentGatewayImpl{}
	for team := range global.TeamSchedules {
		responder, err := d.oncallTeam2Commander(
			cfg.CsPageWatcher.DatadogAPIKey, cfg.CsPageWatcher.DatadogAPPKey, team)
		assert.Nil(t, err)
		assert.NotEqual(t, "", responder.commanderID)
		if team == global.SquadCRM {
			assert.Equal(t, "be-crm", responder.teamHandle)
		}
		t.Logf("team:%v gotCommanderID: %v, gotCommanderEmail:%v, teamHandle:%v",
			team, responder.commanderID, responder.commanderEmail, responder.teamHandle)
	}
}

func TestIncidentGatewayImpl_createIncident(t *testing.T) {
	t.Skip("manual test")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	userID := "c65695fb-b21b-11ef-91b2-dee2953e9407" // zihao
	gotPublicID, err := createIncident(cfg.CsPageWatcher.DatadogAPIKey, cfg.CsPageWatcher.DatadogAPPKey,
		userID, "page-test-incident", "this is a test incident")
	if err != nil {
		t.Fatalf("TestIncidentGatewayImpl_createIncident err:%v", err)
		return
	}
	t.Logf("gotPublicID:%v", gotPublicID)
}
