// Package service 提供jira单监控和通知服务
package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	backoff "github.com/cenkalti/backoff/v4"
	"github.com/samber/lo"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/logic/watcher"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/pkg"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/datadog"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/entity"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/slack"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	pb "github.com/MoeGolibrary/moego/backend/proto/cs_page_watcher/v1"
)

// 确保PageWatcherIns实现了pb.CSPageWatcherServer接口
var _ pb.CSPageWatcherServiceServer = (*PageWatcherIns)(nil)

// PageWatcher 定义页面监控服务的接口
type PageWatcher interface {
	// MonitorAndPage 监控页面并在需要时发送通知
	MonitorAndPage() error
}

// NewPageWatcher 创建并返回一个新的PageWatcher实例
func NewPageWatcher(
	cfg *configloader.Config,
	jiraClient jira.IssueRepository,
	slackClient slack.Client,
	repo entity.CsPageReaderWriter,
	jiraReminder *ScheduledJiraReminder,
) PageWatcher {
	return &PageWatcherIns{
		cfg:          cfg,
		jiraClient:   jiraClient,
		slackClient:  slackClient,
		repo:         repo,
		locker:       pkg.NewLocalLocker(), // TODO: 需要换成分布式锁实现
		jiraReminder: jiraReminder,
	}
}

// PageWatcherIns 实现了PageWatcher接口和pb.CSPageWatcherServer接口
type PageWatcherIns struct {
	// 嵌入未实现的服务器，以获取默认方法实现
	pb.UnimplementedCSPageWatcherServiceServer
	cfg          *configloader.Config
	jiraClient   jira.IssueRepository
	slackClient  slack.Client
	repo         entity.CsPageReaderWriter
	locker       pkg.Locker
	jiraReminder *ScheduledJiraReminder
}

// MonitorAndPage 实现了PageWatcher接口中的方法
// 用于监控页面变化并在需要时发送通知
func (p *PageWatcherIns) MonitorAndPage() error {
	// TODO: 实现页面监控和通知逻辑
	return nil
}

// retryOperation 封装了带有指数退避和最大重试次数的重试逻辑
func (p *PageWatcherIns) retryOperation(
	ctx context.Context, op func() error, errMsgFormat string, args ...interface{}) error {
	err := backoff.Retry(op, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
	if err != nil {
		args = append(args, err)
		err = fmt.Errorf(errMsgFormat, args...)
		log.ErrorContext(ctx, err)
	}

	return err
}

// TriggerPage ...
func (p *PageWatcherIns) TriggerPage(
	ctx context.Context, req *pb.TriggerPageRequest) (*pb.DefaultResponse, error) {
	// 获取相关的jira 单
	log.InfoContextf(ctx, "TriggerPage: %v", req.JiraKey)
	if req.JiraKey == "" {
		return &pb.DefaultResponse{
			Message:   "jira key not found",
			ErrorCode: 0,
		}, nil
	}
	jiraIssue, err := p.jiraClient.GetIssueDetails(req.JiraKey)
	if err != nil {
		log.ErrorContextf(ctx, "TriggerPage: Failed to get jira issue details: %v", err)

		return nil, err
	}
	log.InfoContextf(ctx, "TriggerPage: jira issue details: %s", lo.Must(json.Marshal(jiraIssue)))
	eva := watcher.NewJiraIncidentEvaluator(p.cfg.CsPageWatcher.RefUser, &configloader.GlobalCsPageConfig)
	incident, err := eva.EvaluateBug(jiraIssue)
	if err != nil {
		log.ErrorContextf(ctx, "TriggerPage: EvaluateBug failed: %v", err)

		return nil, err
	}
	if incident == nil {
		log.InfoContextf(ctx, "TriggerPage: no need to create incident: %v", req.JiraKey)

		return &pb.DefaultResponse{
			Message:   "no need to create incident",
			ErrorCode: 0,
		}, nil
	}

	d := datadog.NewIncidentGateway(p.cfg,
		p.slackClient,
		p.repo,
	)

	err = d.TriggerIncident(ctx, incident)
	if err != nil {
		log.ErrorContextf(ctx, "TriggerPage: TriggerIncident failed: %v", err)

		return nil, err
	}

	return &pb.DefaultResponse{
		Message:   "success",
		ErrorCode: 0,
	}, nil
}

// CompleteIncident ...
// completePageTicket handles the common logic for completing a page ticket,
// including fetching Jira details and sending Slack notifications.
func (p *PageWatcherIns) completePageTicket(
	ctx context.Context, dbPageTicket *entity.CsPage, logPrefix string) (*pb.DefaultResponse, error) {
	if dbPageTicket.CsPageJiraTicket == "" {
		log.ErrorContextf(ctx, "%s: db jira ticket not found: %v", logPrefix, dbPageTicket.CsPageJiraTicket)

		return &pb.DefaultResponse{
			Message:   "jira ticket not found",
			ErrorCode: 0,
		}, nil
	}

	// 查询jira的信息
	jiraIssue, err := p.jiraClient.GetIssueDetails(dbPageTicket.CsPageJiraTicket)
	if err != nil {
		log.ErrorContextf(ctx, "%s: jira issue not found: %v, %v",
			logPrefix, dbPageTicket.DatadogIncidentID, dbPageTicket.CsPageJiraTicket)

		return &pb.DefaultResponse{
			Message:   "page not found",
			ErrorCode: 0,
		}, nil
	}

	msg := fmt.Sprintf(`Ticket [%v] has been resolved
Ticket: https://moego.atlassian.net/browse/%v	
Resolution Time: %v
SLA Breach: %v	
`, jiraIssue.Key, jiraIssue.Key, jiraIssue.ResolutionTimeCustom, jiraIssue.SLABreach)

	if dbPageTicket.IncidentSlackChannelID != "" && logPrefix != "CompleteJira" {
		// 如果关联了incident则在单独的incident群里发消息
		_ = p.retryOperation(ctx, func() error {
			_, err = p.slackClient.SendMessage(dbPageTicket.IncidentSlackChannelID, msg)

			return err
		}, "%s: send message to incident channel failed after retries: %v", logPrefix)
	}

	if dbPageTicket.T1NotifyMessageTimestamp != "" {
		// 如果这个page是T1的，关联了相关的t1通知群
		// 在这条消息下添加完成图标
		_ = p.retryOperation(ctx, func() error {
			return p.slackClient.AddEmojiToMessage(
				p.cfg.CsPageWatcher.T1SlackChannelID, dbPageTicket.T1NotifyMessageTimestamp, global.EmojiDone)
		}, "%s: AddEmojiToMessage failed: %v", logPrefix)

		_ = p.retryOperation(ctx, func() error {
			// 在thread下发消息
			return p.slackClient.SendMessageToThread(
				p.cfg.CsPageWatcher.T1SlackChannelID, dbPageTicket.T1NotifyMessageTimestamp, msg)
		}, "%s: SendMessageToThread failed: %v", logPrefix)
	}

	return &pb.DefaultResponse{
		Message:   "success",
		ErrorCode: 0,
	}, nil
}

// CompleteIncident ...
func (p *PageWatcherIns) CompleteIncident(
	ctx context.Context, req *pb.CompleteIncidentRequest) (*pb.DefaultResponse, error) {
	dbPageTicket, err := p.repo.GetCsPage(&entity.CsPage{
		DatadogIncidentID: req.GetId(),
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		log.InfoContextf(ctx, "CompleteIncident: not found: %v", req.GetId())

		return &pb.DefaultResponse{
			Message:   "page not found",
			ErrorCode: 0,
		}, nil
	}
	if err != nil {
		log.ErrorContextf(ctx, "CompleteIncident: GetCsPage failed: %v", err)

		return nil, err
	}
	// 获取分布式锁
	getLock, err := p.locker.TryLock(ctx, dbPageTicket.CsPageJiraTicket, time.Second*10)
	if err != nil {
		log.ErrorContextf(ctx, "CompleteIncident: TryLock failed: %v", err)

		return nil, err
	}
	if !getLock {
		log.WarnContextf(ctx, "CompleteIncident: get lock failed: %v", dbPageTicket.CsPageJiraTicket)

		return &pb.DefaultResponse{
			Message:   "CompleteIncident: get lock failed",
			ErrorCode: 0,
		}, nil
	}

	// 关联关闭jira单
	_ = p.retryOperation(ctx, func() error {
		return p.jiraClient.CloseIssue(dbPageTicket.CsPageJiraTicket)
	}, "CompleteIncident: close jira issue failed after retries: %v")

	// 休息一下，等待automation执行
	time.Sleep(time.Second * 5)

	return p.completePageTicket(ctx, dbPageTicket, "CompleteIncident")
}

func (p *PageWatcherIns) CompleteJira(
	ctx context.Context, req *pb.CompleteJiraRequest) (*pb.DefaultResponse, error) {
	if req.JiraKey == "" {
		log.InfoContextf(ctx, "CompleteJira: key is empty")

		return &pb.DefaultResponse{
			Message:   "jira key not found",
			ErrorCode: 0,
		}, nil
	}
	// 获取分布式锁
	getLock, err := p.locker.TryLock(ctx, req.JiraKey, time.Second*10)
	if err != nil {
		log.ErrorContextf(ctx, "CompleteJira: TryLock failed: %v", err)

		return nil, err
	}
	if !getLock {
		log.WarnContextf(ctx, "CompleteJira: get lock failed: %v", req.JiraKey)

		return &pb.DefaultResponse{
			Message:   "CompleteJira: get lock failed",
			ErrorCode: 0,
		}, nil
	}

	// 这里是另一个场景，如果是p3的流程，没有关联incident，则在jira close的时候触发这个流程
	log.InfoContextf(ctx, "CompleteJira: key: %v", req.GetJiraKey())
	dbPageTicket, err := p.repo.GetCsPage(&entity.CsPage{
		CsPageJiraTicket: req.GetJiraKey(),
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		log.InfoContextf(ctx, "CompleteJira: not found: %v", req.GetJiraKey())

		return &pb.DefaultResponse{
			Message:   "page not found",
			ErrorCode: 0,
		}, nil
	}
	if err != nil {
		log.ErrorContextf(ctx, "CompleteJira: GetCsPage failed: %v", err)

		return nil, err
	}

	return p.completePageTicket(ctx, dbPageTicket, "CompleteJira")
}

// TriggerAdminTask assigns jira ticket to appropriate commander
func (p *PageWatcherIns) TriggerAdminTask(
	ctx context.Context, req *pb.TriggerAdminTaskRequest) (*pb.DefaultResponse, error) {
	log.InfoContextf(ctx, "TriggerAdminTask: %v", req.JiraKey)

	if req.JiraKey == "" {
		return &pb.DefaultResponse{
			Message:   "jira key not found",
			ErrorCode: 1,
		}, nil
	}

	jiraIssue, err := p.jiraClient.GetIssueDetails(req.JiraKey)
	if err != nil {
		log.ErrorContextf(ctx, "TriggerAdminTask: Failed to get jira issue details: %v", err)

		return &pb.DefaultResponse{
			Message:   fmt.Sprintf("failed to get jira issue: %v", err),
			ErrorCode: 2,
		}, err
	}

	if jiraIssue.JiraSquad == "" {
		log.ErrorContextf(ctx, "TriggerAdminTask: No squad found for issue %v", req.JiraKey)

		return &pb.DefaultResponse{
			Message:   "no squad found for this issue",
			ErrorCode: 3,
		}, nil
	}

	d := datadog.NewIncidentGateway(p.cfg, p.slackClient, p.repo)
	commanderEmail, err := d.GetCommander(jiraIssue.JiraSquad)
	if err != nil {
		log.ErrorContextf(ctx, "TriggerAdminTask: Failed to get commander for squad %v: %v", jiraIssue.JiraSquad, err)

		return &pb.DefaultResponse{
			Message:   fmt.Sprintf("failed to get commander: %v", err),
			ErrorCode: 4,
		}, err
	}

	err = p.retryOperation(ctx, func() error {
		return p.jiraClient.SetAssignee(req.JiraKey, commanderEmail)
	}, "TriggerAdminTask: set assignee failed after retries: %v")

	if err != nil {
		log.ErrorContextf(ctx, "TriggerAdminTask: Failed to set assignee: %v", err)

		return &pb.DefaultResponse{
			Message:   fmt.Sprintf("failed to set assignee: %v", err),
			ErrorCode: 5,
		}, err
	}

	log.InfoContextf(ctx, "TriggerAdminTask: Successfully assigned %v to %v", req.JiraKey, commanderEmail)

	return &pb.DefaultResponse{
		Message:   "success",
		ErrorCode: 0,
	}, nil
}

func (p *PageWatcherIns) RunTask(
	ctx context.Context, req *pb.RunTaskRequest) (*pb.DefaultResponse, error) {
	log.InfoContextf(ctx, "RunTask: %v", req.Task)

	if req.Task == "JiraSlaRemind" {
		return p.runJiraSLARemind(ctx)
	}

	return &pb.DefaultResponse{
		Message:   "unknown task name",
		ErrorCode: 1,
	}, nil
}

func (p *PageWatcherIns) runJiraSLARemind(ctx context.Context) (*pb.DefaultResponse, error) {
	log.InfoContextf(ctx, "runJiraSLARemind: Running Jira SLA reminder...")

	p.jiraReminder.Remind()

	return &pb.DefaultResponse{
		Message:   "success",
		ErrorCode: 0,
	}, nil
}
