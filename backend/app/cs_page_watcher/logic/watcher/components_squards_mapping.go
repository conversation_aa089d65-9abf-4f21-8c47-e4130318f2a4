package watcher

const (
	SquadCRM        = "CRM"
	SquadERP        = "ERP"
	SquadFintech    = "Fintech"
	SquadBEPlatform = "BE-Platform"
	SquadFEPlatform = "FE-Platform"
)

var ComponentsSquadsMapping = map[string]string{
	"Workflow":                         SquadCRM,
	"System notification":              SquadCRM,
	"System access":                    SquadBEPlatform,
	"Staff":                            SquadCRM,
	"SS/CACD/Service area/RO/Map view": SquadERP,
	"Service settings":                 SquadERP,
	"Review booster":                   SquadERP,
	"Retail":                           SquadERP,
	"QB":                               SquadFintech,
	"Profile&General biz settings/Nav": SquadCRM,
	"Playgroups":                       SquadERP,
	"Payment":                          SquadFintech,
	"Packages":                         SquadERP,
	"Online booking":                   SquadERP,
	"MoeGo Website":                    SquadBEPlatform,
	"Message/Auto Reminder":            SquadCRM,
	"Memberships":                      SquadCRM,
	"Intake form":                      SquadCRM,
	"Insight/Report":                   SquadCRM,
	"Home/Lodgings":                    SquadERP,
	"Grooming Appointments":            SquadERP,
	"Finance":                          SquadFintech,
	"Email campaign":                   SquadCRM,
	"Dog walking":                      SquadERP,
	"Discount":                         SquadCRM,
	"Customers(client/pet/leads)":      SquadCRM,
	"C App/Client portal":              SquadERP,
	"Calling":                          SquadCRM,
	"Calendar":                         SquadERP,
	"B&D Appointments":                 SquadERP,
	"App Freeze/Crash..":               SquadFEPlatform,
	"Agreement":                        SquadCRM,
	"Account billing":                  SquadFintech,
}
