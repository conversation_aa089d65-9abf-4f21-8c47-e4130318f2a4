load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "service",
    srcs = ["instance.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db/serviceinstance",
        "//backend/proto/fulfillment/v1:fulfillment",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "service_test",
    srcs = ["instance_test.go"],
    embed = [":service"],
    deps = [
        "//backend/app/fulfillment/repo/db/serviceinstance",
        "//backend/proto/fulfillment/v1:fulfillment",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
