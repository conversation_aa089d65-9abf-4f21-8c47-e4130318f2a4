package inner

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/inner"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// 临时的请求和响应结构体，等待proto重新生成后替换
type GetGroomingDetailByAppointmentIDRequest struct {
	AppointmentID    int64
	ServiceItemTypes []fulfillmentpb.ServiceType
}

type GetGroomingDetailByAppointmentIDResponse struct {
	GroomingDetails []*pb.GroomingPetDetailDTO
}

func New() *Logic {
	return &Logic{
		serviceInstanceCli: serviceinstance.New(), // TODO: 需要传入实际的DB实例
		fulfillmentCli:     fulfillment.New(),
	}
}

type Logic struct {
	serviceInstanceCli serviceinstance.ReadWriter
	fulfillmentCli     fulfillment.ReadWriter
}

func (l *Logic) GetGroomingDetailByAppointmentID(ctx context.Context, req *GetGroomingDetailByAppointmentIDRequest) (
	*GetGroomingDetailByAppointmentIDResponse, error) {

	// 参数验证
	if err := l.validateRequest(req); err != nil {
		return nil, err
	}

	// 1. 根据appointment_id查询ServiceInstance列表
	serviceInstances, err := l.serviceInstanceCli.GetByAppointmentID(ctx, int(req.AppointmentID))
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get service instances: %v", err)
	}

	if len(serviceInstances) == 0 {
		return &GetGroomingDetailByAppointmentIDResponse{
			GroomingDetails: []*pb.GroomingPetDetailDTO{},
		}, nil
	}

	// 2. 提取所有serviceIDs
	var serviceIDs []int64
	for _, si := range serviceInstances {
		serviceIDs = append(serviceIDs, int64(si.ServiceFactoryID))
	}

	// 3. 调用offering服务的GetServiceByIds接口
	// TODO: 实现offering服务调用
	services, err := l.getServices(serviceIDs)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get service templates: %v", err)
	}

	// 4. 构建Service的映射，方便查找
	serviceMap := make(map[int64]*offeringpb.Service)
	for _, st := range services {
		serviceMap[st.GetId()] = st
	}

	// 5. 组装GroomingPetDetailDTO列表
	var groomingDetails []*pb.GroomingPetDetailDTO
	for _, si := range serviceInstances {
		service, exists := serviceMap[int64(si.ServiceFactoryID)]
		if !exists {
			// 如果找不到对应的Service，跳过或记录日志
			continue
		}

		fulfillments, err := l.getFulfillmentByServiceInstanceID(ctx, int64(si.ID))
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to get fulfillments: %v", err)
		}

		serviceOptions, err := l.getServiceOperations(int64(si.ID))
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to get service options: %v", err)
		}

		dto := l.buildGroomingPetDetailDTO(si, service, fulfillments, serviceOptions)
		groomingDetails = append(groomingDetails, dto)
	}

	return &GetGroomingDetailByAppointmentIDResponse{
		GroomingDetails: groomingDetails,
	}, nil
}

func (l *Logic) validateRequest(req *GetGroomingDetailByAppointmentIDRequest) error {
	if req.AppointmentID <= 0 {
		return status.Error(codes.InvalidArgument, "appointment_id must be greater than 0")
	}

	return nil
}

func (l *Logic) getServiceOperations(_ int64) ([]*fulfillmentpb.ServiceOption, error) {
	return []*fulfillmentpb.ServiceOption{}, nil
}

func (l *Logic) getFulfillmentByServiceInstanceID(ctx context.Context, serviceInstanceID int64) (
	[]*fulfillment.Fulfillment, error) {
	fulfillments, err := l.fulfillmentCli.GetByServiceInstanceID(ctx, serviceInstanceID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get fulfillments: %v", err)
	}

	return fulfillments, nil
}

// TODO: 实现offering服务调用
func (l *Logic) getServices(_ []int64) ([]*offeringpb.Service, error) {
	// 临时实现，返回空列表
	// 实际应该调用offering服务的GetServiceByIds接口
	return []*offeringpb.Service{}, nil
}

func (l *Logic) buildGroomingPetDetailDTO(si *serviceinstance.ServiceInstance, st *offeringpb.Service,
	fulfillments []*fulfillment.Fulfillment, serviceOptions []*fulfillmentpb.ServiceOption) *pb.GroomingPetDetailDTO {

	// 从第一条fulfillment中获取staffid
	var staffID int64
	var workMode int32
	var lodgingID int64
	var durationOverrideType int32
	var priceOverrideType int32
	var quantityPerDay int32 = 1

	if len(fulfillments) > 0 {
		firstFulfillment := fulfillments[0]
		staffID = int64(firstFulfillment.StaffID)
		workMode = firstFulfillment.WorkMode
		lodgingID = int64(firstFulfillment.LodgingID)
		durationOverrideType = int32(firstFulfillment.DurationOverrideType)
		priceOverrideType = int32(firstFulfillment.PriceOverrideType)
	}

	if len(serviceOptions) > 0 {
		quantityPerDay = int32(serviceOptions[0].QuantityPerDay)
	}

	return &pb.GroomingPetDetailDTO{
		Id:         int64(si.ID),
		GroomingId: int64(si.AppointmentID), // 使用AppointmentID作为grooming_id
		PetId:      int64(si.PetID),
		StaffId:    staffID,
		//ServiceId:  int64(st.GetId()),
		//ServiceTime:  st.GetDuration(),
		//ServicePrice: st.GetPrice(),
		//StartTime: int64((si.StartDate.Hour()+20)*60 + si.StartDate.Minute()),
		//EndTime:   int64((si.StartDate.Hour()+20)*60 + si.StartDate.Minute() + int(st.GetDuration())),
		// ScopeTypePrice: 0, // 这个字段不需要填充
		// ScopeTypeTime:  0, // 这个字段不需要填充
		// StarStaffId:           0, // 这个字段不需要填充
		ServiceName: st.GetName(),
		//ServiceType:   int32(st.GetCareType()),
		//ServiceStatus: l.mapStatusToServiceStatus(st.GetStatus()),
		// StaffFirstName:        "",   // 这个字段不需要填充
		// StaffLastName:         "",   // 这个字段不需要填充
		// PetName:               "",   // 这个字段不需要填充
		// PetLifeStatus:         1,    // 这个字段不需要填充
		// EnableOperation: true, // TODO: 需要从其他地方获取
		ColorCode: st.GetColorCode(),
		WorkMode:  workMode,
		// OperationList:         []*pb.GroomingServiceOperationDTO{}, 这个字段不需要填充
		//ServiceItemType: int32(st.GetCareType()),
		LodgingId: lodgingID,
		StartDate: si.StartDate.Format("2006-01-02"),
		EndDate:   si.EndDate.Format("2006-01-02"),
		//PriceUnit:       int32(st.GetPriceUint()),
		SpecificDates: getSpecificDates(fulfillments),
		// Quantity:             1, // 这个字段不需要填充
		PriceOverrideType:    fulfillmentpb.OverrideType(priceOverrideType),
		DurationOverrideType: fulfillmentpb.OverrideType(durationOverrideType),
		UpdatedAt:            timestamppb.New(si.UpdatedAt),
		QuantityPerDay:       quantityPerDay,
		// RequireDedicatedStaff: false, // 这个字段不需要填充
		DateType: int32(si.DateType),
		// OrderLineItemId: 0, // 这个字段不需要填充
	}
}

//func (l *Logic) mapStatusToServiceStatus(status int32) int32 {
//	// 将Status值映射到ServiceStatus枚举值
//	// Status: 0 -> SERVICE_STATUS_INACTIVE (2)
//	// Status: 1 -> SERVICE_STATUS_ACTIVE (1)
//	switch status {
//	case 0:
//		return 2 // SERVICE_STATUS_INACTIVE
//	case 1:
//		return 1 // SERVICE_STATUS_ACTIVE
//	default:
//		return 0 // SERVICE_STATUS_UNSPECIFIED
//	}
//}

func getSpecificDates(fulfillments []*fulfillment.Fulfillment) string {
	var result string
	for _, fulfillment := range fulfillments {
		result += fulfillment.StartTime.Format("2006-01-02") + ","
	}

	return result
}
