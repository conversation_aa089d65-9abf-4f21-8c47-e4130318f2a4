package inner

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	innerpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/inner"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestLogic_buildGroomingPetDetailDTO_Basic(t *testing.T) {
	serviceInstance := &serviceinstance.ServiceInstance{
		ID:            1,
		AppointmentID: 100,
		PetID:         200,
		StartDate:     time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC),
		EndDate:       time.Date(2024, 1, 15, 12, 0, 0, 0, time.UTC),
		DateType:      1,
		UpdatedAt:     time.Date(2024, 1, 15, 9, 0, 0, 0, time.UTC),
	}

	serviceTemplate := &offeringpb.Service{
		Id:        1,
		Name:      "测试服务",
		ColorCode: "#FF0000",
	}

	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:        1,
			StaffID:   300,
			LodgingID: 400,
			StartTime: time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC),
			EndTime:   time.Date(2024, 1, 15, 12, 0, 0, 0, time.UTC),
		},
	}

	serviceOptions := []*fulfillmentpb.ServiceOption{
		{
			QuantityPerDay: 2,
		},
	}

	expected := &innerpb.GroomingPetDetailDTO{
		Id:                   1,
		GroomingId:           100,
		PetId:                200,
		StaffId:              300,
		ServiceName:          "测试服务",
		ColorCode:            "#FF0000",
		WorkMode:             0,
		LodgingId:            400,
		StartDate:            "2024-01-15",
		EndDate:              "2024-01-15",
		SpecificDates:        "2024-01-15,",
		PriceOverrideType:    fulfillmentpb.OverrideType_OVERRIDE_TYPE_UNSPECIFIED,
		DurationOverrideType: fulfillmentpb.OverrideType_OVERRIDE_TYPE_UNSPECIFIED,
		UpdatedAt:            timestamppb.New(time.Date(2024, 1, 15, 9, 0, 0, 0, time.UTC)),
		QuantityPerDay:       2,
		DateType:             1,
	}

	l := &Logic{}
	result := l.buildGroomingPetDetailDTO(serviceInstance, serviceTemplate, fulfillments, serviceOptions)

	assertGroomingPetDetailDTO(t, expected, result)
}

func TestLogic_buildGroomingPetDetailDTO_EmptyData(t *testing.T) {
	serviceInstance := &serviceinstance.ServiceInstance{
		ID:            2,
		AppointmentID: 101,
		PetID:         201,
		StartDate:     time.Date(2024, 1, 16, 10, 0, 0, 0, time.UTC),
		EndDate:       time.Date(2024, 1, 16, 12, 0, 0, 0, time.UTC),
		DateType:      2,
		UpdatedAt:     time.Date(2024, 1, 16, 9, 0, 0, 0, time.UTC),
	}

	serviceTemplate := &offeringpb.Service{
		Id:        2,
		Name:      "空数据测试服务",
		ColorCode: "#00FF00",
	}

	fulfillments := []*fulfillment.Fulfillment{}
	serviceOptions := []*fulfillmentpb.ServiceOption{}

	expected := &innerpb.GroomingPetDetailDTO{
		Id:                   2,
		GroomingId:           101,
		PetId:                201,
		StaffId:              0,
		ServiceName:          "空数据测试服务",
		ColorCode:            "#00FF00",
		WorkMode:             0,
		LodgingId:            0,
		StartDate:            "2024-01-16",
		EndDate:              "2024-01-16",
		SpecificDates:        "",
		PriceOverrideType:    fulfillmentpb.OverrideType_OVERRIDE_TYPE_UNSPECIFIED,
		DurationOverrideType: fulfillmentpb.OverrideType_OVERRIDE_TYPE_UNSPECIFIED,
		UpdatedAt:            timestamppb.New(time.Date(2024, 1, 16, 9, 0, 0, 0, time.UTC)),
		QuantityPerDay:       1, // 默认值
		DateType:             2,
	}

	l := &Logic{}
	result := l.buildGroomingPetDetailDTO(serviceInstance, serviceTemplate, fulfillments, serviceOptions)

	assertGroomingPetDetailDTO(t, expected, result)
}

func TestLogic_buildGroomingPetDetailDTO_MultipleFulfillments(t *testing.T) {
	serviceInstance := &serviceinstance.ServiceInstance{
		ID:            3,
		AppointmentID: 102,
		PetID:         202,
		StartDate:     time.Date(2024, 1, 17, 10, 0, 0, 0, time.UTC),
		EndDate:       time.Date(2024, 1, 17, 12, 0, 0, 0, time.UTC),
		DateType:      3,
		UpdatedAt:     time.Date(2024, 1, 17, 9, 0, 0, 0, time.UTC),
	}

	serviceTemplate := &offeringpb.Service{
		Id:        3,
		Name:      "多fulfillment测试服务",
		ColorCode: "#0000FF",
	}

	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:        2,
			StaffID:   301,
			LodgingID: 401,
			StartTime: time.Date(2024, 1, 17, 10, 0, 0, 0, time.UTC),
			EndTime:   time.Date(2024, 1, 17, 11, 0, 0, 0, time.UTC),
		},
		{
			ID:        3,
			StaffID:   302,
			LodgingID: 402,
			StartTime: time.Date(2024, 1, 17, 11, 0, 0, 0, time.UTC),
			EndTime:   time.Date(2024, 1, 17, 12, 0, 0, 0, time.UTC),
		},
	}

	serviceOptions := []*fulfillmentpb.ServiceOption{
		{
			QuantityPerDay: 3,
		},
		{
			QuantityPerDay: 4,
		},
	}

	expected := &innerpb.GroomingPetDetailDTO{
		Id:                   3,
		GroomingId:           102,
		PetId:                202,
		StaffId:              301, // 取第一个fulfillment的StaffID
		ServiceName:          "多fulfillment测试服务",
		ColorCode:            "#0000FF",
		WorkMode:             0,
		LodgingId:            401, // 取第一个fulfillment的LodgingID
		StartDate:            "2024-01-17",
		EndDate:              "2024-01-17",
		SpecificDates:        "2024-01-17,2024-01-17,", // 两个fulfillment的日期
		PriceOverrideType:    fulfillmentpb.OverrideType_OVERRIDE_TYPE_UNSPECIFIED,
		DurationOverrideType: fulfillmentpb.OverrideType_OVERRIDE_TYPE_UNSPECIFIED,
		UpdatedAt:            timestamppb.New(time.Date(2024, 1, 17, 9, 0, 0, 0, time.UTC)),
		QuantityPerDay:       3, // 取第一个serviceOption的QuantityPerDay
		DateType:             3,
	}

	l := &Logic{}
	result := l.buildGroomingPetDetailDTO(serviceInstance, serviceTemplate, fulfillments, serviceOptions)

	assertGroomingPetDetailDTO(t, expected, result)
}

func TestLogic_buildGroomingPetDetailDTO_SwimmingClass(t *testing.T) {
	serviceInstance := &serviceinstance.ServiceInstance{
		ID:            36220375,
		AppointmentID: 24472278,
		PetID:         22092610,
		StartDate:     time.Date(2025, 7, 17, 0, 20, 0, 0, time.UTC),
		EndDate:       time.Date(2025, 7, 17, 0, 21, 20, 0, time.UTC),
		DateType:      3,
		UpdatedAt:     time.Date(2025, 7, 17, 12, 21, 42, 0, time.UTC),
	}

	serviceTemplate := &offeringpb.Service{
		Id:        1220610,
		Name:      "swimming class",
		ColorCode: "",
	}

	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:                   36220375,
			StaffID:              125558,
			LodgingID:            0,
			StartTime:            time.Date(2025, 7, 17, 0, 20, 0, 0, time.UTC),
			EndTime:              time.Date(2025, 7, 17, 0, 21, 20, 0, time.UTC),
			WorkMode:             0,
			DurationOverrideType: 0,
			PriceOverrideType:    0,
		},
	}

	serviceOptions := []*fulfillmentpb.ServiceOption{
		{
			QuantityPerDay: 1,
		},
	}

	expected := &innerpb.GroomingPetDetailDTO{
		Id:                   36220375,
		GroomingId:           24472278,
		PetId:                22092610,
		StaffId:              125558,
		ServiceName:          "swimming class",
		ColorCode:            "",
		WorkMode:             0,
		LodgingId:            0,
		StartDate:            "2025-07-17",
		EndDate:              "2025-07-17",
		SpecificDates:        "2025-07-17,",
		PriceOverrideType:    fulfillmentpb.OverrideType_OVERRIDE_TYPE_UNSPECIFIED,
		DurationOverrideType: fulfillmentpb.OverrideType_OVERRIDE_TYPE_UNSPECIFIED,
		UpdatedAt:            timestamppb.New(time.Date(2025, 7, 17, 12, 21, 42, 0, time.UTC)),
		QuantityPerDay:       1,
		DateType:             3,
	}

	l := &Logic{}
	result := l.buildGroomingPetDetailDTO(serviceInstance, serviceTemplate, fulfillments, serviceOptions)

	assertGroomingPetDetailDTO(t, expected, result)
}

func assertGroomingPetDetailDTO(t *testing.T, expected *innerpb.GroomingPetDetailDTO,
	result *innerpb.GroomingPetDetailDTO) {
	assert.Equal(t, expected.Id, result.Id)
	assert.Equal(t, expected.GroomingId, result.GroomingId)
	assert.Equal(t, expected.PetId, result.PetId)
	assert.Equal(t, expected.StaffId, result.StaffId)
	assert.Equal(t, expected.ServiceName, result.ServiceName)
	assert.Equal(t, expected.ColorCode, result.ColorCode)
	assert.Equal(t, expected.WorkMode, result.WorkMode)
	assert.Equal(t, expected.LodgingId, result.LodgingId)
	assert.Equal(t, expected.StartDate, result.StartDate)
	assert.Equal(t, expected.EndDate, result.EndDate)
	assert.Equal(t, expected.SpecificDates, result.SpecificDates)
	assert.Equal(t, expected.PriceOverrideType, result.PriceOverrideType)
	assert.Equal(t, expected.DurationOverrideType, result.DurationOverrideType)
	assert.Equal(t, expected.QuantityPerDay, result.QuantityPerDay)
	assert.Equal(t, expected.DateType, result.DateType)

	// 验证时间戳字段
	if expected.UpdatedAt != nil {
		assert.Equal(t, expected.UpdatedAt.Seconds, result.UpdatedAt.Seconds)
	}
}

func TestGetSpecificDates(t *testing.T) {
	tests := []struct {
		name         string
		fulfillments []*fulfillment.Fulfillment
		expected     string
	}{
		{
			name:         "空fulfillment列表",
			fulfillments: []*fulfillment.Fulfillment{},
			expected:     "",
		},
		{
			name: "单个fulfillment",
			fulfillments: []*fulfillment.Fulfillment{
				{
					StartTime: time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC),
				},
			},
			expected: "2024-01-15,",
		},
		{
			name: "多个fulfillment",
			fulfillments: []*fulfillment.Fulfillment{
				{
					StartTime: time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC),
				},
				{
					StartTime: time.Date(2024, 1, 16, 10, 0, 0, 0, time.UTC),
				},
				{
					StartTime: time.Date(2024, 1, 17, 10, 0, 0, 0, time.UTC),
				},
			},
			expected: "2024-01-15,2024-01-16,2024-01-17,",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getSpecificDates(tt.fulfillments)
			assert.Equal(t, tt.expected, result)
		})
	}
}
