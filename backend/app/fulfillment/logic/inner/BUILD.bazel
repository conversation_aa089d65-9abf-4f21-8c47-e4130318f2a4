load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "inner",
    srcs = ["inner_service.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/inner",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db/fulfillment",
        "//backend/app/fulfillment/repo/db/serviceinstance",
        "//backend/proto/fulfillment/inner",
        "//backend/proto/fulfillment/v1:fulfillment",
        "//backend/proto/offering/v1:offering",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "inner_test",
    srcs = ["inner_service_test.go"],
    embed = [":inner"],
    deps = [
        "//backend/app/fulfillment/repo/db/fulfillment",
        "//backend/app/fulfillment/repo/db/serviceinstance",
        "//backend/proto/fulfillment/inner",
        "//backend/proto/fulfillment/v1:fulfillment",
        "//backend/proto/offering/v1:offering",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
