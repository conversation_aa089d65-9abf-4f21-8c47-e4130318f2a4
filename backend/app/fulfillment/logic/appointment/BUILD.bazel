load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "appointment",
    srcs = ["appointment.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/appointment",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db",
        "//backend/app/fulfillment/repo/db/appointment",
        "//backend/app/fulfillment/repo/db/fulfillment",
        "//backend/app/fulfillment/repo/db/serviceinstance",
        "//backend/common/rpc/framework/log",
        "//backend/proto/fulfillment/v1:fulfillment",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "appointment_test",
    srcs = ["appointment_test.go"],
    embed = [":appointment"],
    deps = [
        "//backend/app/fulfillment/repo/db/appointment",
        "//backend/app/fulfillment/repo/db/appointment/mock",
        "//backend/app/fulfillment/repo/db/fulfillment/mock",
        "//backend/app/fulfillment/repo/db/serviceinstance",
        "//backend/app/fulfillment/repo/db/serviceinstance/mock",
        "//backend/proto/fulfillment/v1:fulfillment",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_mock//gomock",
    ],
)
