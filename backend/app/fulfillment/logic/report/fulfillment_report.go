package report

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
	fulfillmentreportrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment_report"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

func New() *Logic {
	return &Logic{
		templateRepo:       fulfillmentreportrepo.NewFulfillmentReportTemplateRepo(),
		questionRepo:       fulfillmentreportrepo.NewFulfillmentReportQuestionRepo(),
		reportRepo:         fulfillmentreportrepo.NewFulfillmentReportRepo(),
		sendRecordRepo:     fulfillmentreportrepo.NewFulfillmentReportSendRecordRepo(),
		transactionManager: db.NewTxManager(),
	}
}

type Logic struct {
	templateRepo       fulfillmentreportrepo.TemplateRepo
	questionRepo       fulfillmentreportrepo.QuestionRepo
	reportRepo         fulfillmentreportrepo.ReportRepo
	sendRecordRepo     fulfillmentreportrepo.SendRecordRepo
	transactionManager db.TransactionManager
}

func (l *Logic) GetFulfillmentReportTemplate(_ context.Context,
	_ *fulfillmentpb.GetFulfillmentReportTemplateRequest) (*fulfillmentpb.GetFulfillmentReportTemplateResponse, error) {
	// TODO: implement
	return nil, nil
}

func (l *Logic) UpdateFulfillmentReportTemplate(
	_ context.Context,
	_ *fulfillmentpb.UpdateFulfillmentReportTemplateRequest,
) (
	*fulfillmentpb.UpdateFulfillmentReportTemplateResponse, error,
) {
	// TODO: implement
	return nil, nil
}
