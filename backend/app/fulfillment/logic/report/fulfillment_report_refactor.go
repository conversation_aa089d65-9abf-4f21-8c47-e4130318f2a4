package report

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	fulfillmentreportrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment_report"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

// SyncFulfillmentReportTemplate 同步履约报告模板（双写）
func (l *Logic) SyncFulfillmentReportTemplate(
	ctx context.Context,
	req *fulfillmentpb.SyncFulfillmentReportTemplateRequest,
) (
	*fulfillmentpb.SyncFulfillmentReportTemplateResponse, error,
) {
	// 参数验证
	if err := l.validateSyncRequest(req); err != nil {
		log.ErrorContext(ctx, "同步履约报告模板参数验证失败",
			zap.String("operation", req.Operation.String()),
			zap.Error(err))

		return &fulfillmentpb.SyncFulfillmentReportTemplateResponse{
			TemplateId:   0,
			Status:       fulfillmentpb.SyncStatus_FAILED,
			ErrorMessage: err.Error(),
		}, nil
	}

	var templateID int64
	var err error

	switch req.Operation {
	case fulfillmentpb.SyncOperation_CREATE:
		templateID, err = l.handleCreateOperation(ctx, req)
	case fulfillmentpb.SyncOperation_UPDATE:
		templateID, err = l.handleUpdateOperation(ctx, req)
	case fulfillmentpb.SyncOperation_UPSERT:
		templateID, err = l.handleUpsertOperation(ctx, req)
	case fulfillmentpb.SyncOperation_DELETE:
		templateID, err = l.handleDeleteOperation(ctx, req)
	default:
		log.ErrorContext(ctx, "不支持的同步操作类型",
			zap.String("operation", req.Operation.String()))

		return &fulfillmentpb.SyncFulfillmentReportTemplateResponse{
			TemplateId:   0,
			Status:       fulfillmentpb.SyncStatus_FAILED,
			ErrorMessage: "unsupported operation",
		}, nil
	}

	if err != nil {
		log.ErrorContext(ctx, "同步履约报告模板操作失败",
			zap.String("operation", req.Operation.String()),
			zap.Int64("template_id", templateID),
			zap.Error(err))

		return &fulfillmentpb.SyncFulfillmentReportTemplateResponse{
			TemplateId:   templateID,
			Status:       fulfillmentpb.SyncStatus_FAILED,
			ErrorMessage: err.Error(),
		}, nil
	}

	return &fulfillmentpb.SyncFulfillmentReportTemplateResponse{
		TemplateId: templateID,
		Status:     fulfillmentpb.SyncStatus_SUCCESS,
	}, nil
}

// BatchSyncFulfillmentReportQuestions 批量同步履约报告模板问题（双写）
func (l *Logic) BatchSyncFulfillmentReportQuestions(
	ctx context.Context,
	req *fulfillmentpb.BatchSyncFulfillmentReportQuestionsRequest,
) (
	*fulfillmentpb.BatchSyncFulfillmentReportQuestionsResponse, error,
) {
	// 参数验证
	if err := l.validateBatchSyncQuestionsRequest(req); err != nil {
		log.ErrorContext(ctx, "批量同步问题参数验证失败",
			zap.String("operation", req.Operation.String()),
			zap.Error(err))

		return &fulfillmentpb.BatchSyncFulfillmentReportQuestionsResponse{
			Status:       fulfillmentpb.SyncStatus_FAILED,
			TemplateId:   0,
			ErrorMessage: err.Error(),
		}, nil
	}

	// 获取模板ID
	templateID, err := l.getTemplateID(ctx, req.TemplateIdentifier)
	if err != nil {
		log.ErrorContext(ctx, "获取模板ID失败",
			zap.String("operation", req.Operation.String()),
			zap.Error(err))

		return &fulfillmentpb.BatchSyncFulfillmentReportQuestionsResponse{
			Status:       fulfillmentpb.SyncStatus_FAILED,
			TemplateId:   0,
			ErrorMessage: fmt.Sprintf("failed to get template_id: %v", err),
		}, nil
	}

	// 转换问题数据
	questions := l.convertQuestionsProtoToEntity(req.Questions, templateID)

	switch req.Operation {
	case fulfillmentpb.SyncOperation_CREATE:
		err = l.handleBatchCreateQuestions(ctx, questions)
	case fulfillmentpb.SyncOperation_UPDATE:
		err = l.handleBatchUpdateQuestions(ctx, questions)
	case fulfillmentpb.SyncOperation_UPSERT:
		err = l.handleBatchUpsertQuestions(ctx, questions)
	case fulfillmentpb.SyncOperation_DELETE:
		err = l.handleBatchDeleteQuestions(ctx, templateID)
	default:
		log.ErrorContext(ctx, "不支持的批量同步操作类型",
			zap.String("operation", req.Operation.String()))

		return &fulfillmentpb.BatchSyncFulfillmentReportQuestionsResponse{
			Status:       fulfillmentpb.SyncStatus_FAILED,
			TemplateId:   templateID,
			ErrorMessage: "unsupported operation",
		}, nil
	}

	if err != nil {
		log.ErrorContext(ctx, "批量同步问题操作失败",
			zap.String("operation", req.Operation.String()),
			zap.Int64("template_id", templateID),
			zap.Error(err))

		return &fulfillmentpb.BatchSyncFulfillmentReportQuestionsResponse{
			Status:       fulfillmentpb.SyncStatus_FAILED,
			TemplateId:   templateID,
			ErrorMessage: err.Error(),
		}, nil
	}

	return &fulfillmentpb.BatchSyncFulfillmentReportQuestionsResponse{
		Status:     fulfillmentpb.SyncStatus_SUCCESS,
		TemplateId: templateID,
	}, nil
}

// SyncFulfillmentReport 同步履约报告（双写）
func (l *Logic) SyncFulfillmentReport(ctx context.Context,
	req *fulfillmentpb.SyncFulfillmentReportRequest) (*fulfillmentpb.SyncFulfillmentReportResponse, error) {

	// 参数验证
	if err := l.validateSyncReportRequest(req); err != nil {
		return &fulfillmentpb.SyncFulfillmentReportResponse{
			ReportId:     0,
			Status:       fulfillmentpb.SyncStatus_FAILED,
			ErrorMessage: err.Error(),
		}, nil
	}

	var reportID int64
	var err error

	switch req.Operation {
	case fulfillmentpb.SyncOperation_CREATE:
		reportID, err = l.handleCreateReportOperation(ctx, req)
	case fulfillmentpb.SyncOperation_UPDATE:
		reportID, err = l.handleUpdateReportOperation(ctx, req)
	case fulfillmentpb.SyncOperation_UPSERT:
		reportID, err = l.handleUpsertReportOperation(ctx, req)
	case fulfillmentpb.SyncOperation_DELETE:
		reportID, err = l.handleDeleteReportOperation(ctx, req)
	default:
		return &fulfillmentpb.SyncFulfillmentReportResponse{
			ReportId:     0,
			Status:       fulfillmentpb.SyncStatus_FAILED,
			ErrorMessage: "unsupported operation",
		}, nil
	}

	if err != nil {
		return &fulfillmentpb.SyncFulfillmentReportResponse{
			ReportId:     reportID,
			Status:       fulfillmentpb.SyncStatus_FAILED,
			ErrorMessage: err.Error(),
		}, nil
	}

	return &fulfillmentpb.SyncFulfillmentReportResponse{
		ReportId: reportID,
		Status:   fulfillmentpb.SyncStatus_SUCCESS,
	}, nil
}

// SyncFulfillmentReportSendRecord 同步履约报告发送记录（双写）
func (l *Logic) SyncFulfillmentReportSendRecord(
	ctx context.Context,
	req *fulfillmentpb.SyncFulfillmentReportSendRecordRequest,
) (
	*fulfillmentpb.SyncFulfillmentReportSendRecordResponse, error,
) {

	// 参数验证
	if err := l.validateSyncSendRecordRequest(req); err != nil {
		return &fulfillmentpb.SyncFulfillmentReportSendRecordResponse{
			RecordId:     0,
			ReportId:     0,
			Status:       fulfillmentpb.SyncStatus_FAILED,
			ErrorMessage: err.Error(),
		}, nil
	}

	var recordID, reportID int64
	var err error

	switch req.Operation {
	case fulfillmentpb.SyncOperation_CREATE:
		recordID, reportID, err = l.handleCreateSendRecordOperation(ctx, req)
	case fulfillmentpb.SyncOperation_UPDATE:
		recordID, reportID, err = l.handleUpdateSendRecordOperation(ctx, req)
	case fulfillmentpb.SyncOperation_UPSERT:
		recordID, reportID, err = l.handleUpsertSendRecordOperation(ctx, req)
	case fulfillmentpb.SyncOperation_DELETE:
		recordID, reportID, err = l.handleDeleteSendRecordOperation(ctx, req)
	default:
		return &fulfillmentpb.SyncFulfillmentReportSendRecordResponse{
			RecordId:     0,
			ReportId:     0,
			Status:       fulfillmentpb.SyncStatus_FAILED,
			ErrorMessage: "unsupported operation",
		}, nil
	}

	if err != nil {
		return &fulfillmentpb.SyncFulfillmentReportSendRecordResponse{
			RecordId:     recordID,
			ReportId:     reportID,
			Status:       fulfillmentpb.SyncStatus_FAILED,
			ErrorMessage: err.Error(),
		}, nil
	}

	return &fulfillmentpb.SyncFulfillmentReportSendRecordResponse{
		RecordId: recordID,
		ReportId: reportID,
		Status:   fulfillmentpb.SyncStatus_SUCCESS,
	}, nil
}

// validateBatchSyncQuestionsRequest 验证批量同步问题请求参数
func (l *Logic) validateBatchSyncQuestionsRequest(req *fulfillmentpb.BatchSyncFulfillmentReportQuestionsRequest) error {
	if req.Operation == fulfillmentpb.SyncOperation_SYNC_OPERATION_UNSPECIFIED {
		return errors.New("operation is required")
	}

	// 验证模板标识符
	if req.TemplateIdentifier == nil {
		return errors.New("template_identifier is required")
	}

	// 验证问题数量
	if len(req.Questions) == 0 {
		return errors.New("questions list cannot be empty")
	}

	if len(req.Questions) > 50 {
		return errors.New("questions list cannot exceed 50 items")
	}

	// CREATE、UPDATE、UPSERT 操作需要提供问题数据
	if req.Operation != fulfillmentpb.SyncOperation_DELETE && len(req.Questions) == 0 {
		return errors.New("questions data is required for create/update/upsert operations")
	}

	return nil
}

// getTemplateID 根据模板标识符获取模板ID
func (l *Logic) getTemplateID(
	ctx context.Context,
	questionIdentifier *fulfillmentpb.QuestionTemplateIdentifier,
) (
	int64, error,
) {
	switch identifier := questionIdentifier.Identifier.(type) {
	case *fulfillmentpb.QuestionTemplateIdentifier_TemplateId:
		return identifier.TemplateId, nil
	case *fulfillmentpb.QuestionTemplateIdentifier_TemplateUniqueKey:
		// 通过唯一键查找模板
		template, err := l.templateRepo.FindByUniqueKey(ctx,
			identifier.TemplateUniqueKey.CompanyId,
			identifier.TemplateUniqueKey.BusinessId,
			int32(identifier.TemplateUniqueKey.CareType))
		if err != nil {
			return 0, fmt.Errorf("template not found: %w", err)
		}

		return template.ID, nil
	default:
		return 0, errors.New("invalid template identifier")
	}
}

// convertQuestionsProtoToEntity 将问题 Proto 消息转换为数据库实体
func (l *Logic) convertQuestionsProtoToEntity(protoQuestions []*fulfillmentpb.FulfillmentReportQuestionSync,
	templateID int64) []*fulfillmentreportrepo.Question {
	questions := make([]*fulfillmentreportrepo.Question, 0, len(protoQuestions))

	for _, protoQuestion := range protoQuestions {
		question := &fulfillmentreportrepo.Question{
			CompanyID:         protoQuestion.CompanyId,
			BusinessID:        protoQuestion.BusinessId,
			TemplateID:        templateID,
			Category:          int32(protoQuestion.Category),
			Type:              protoQuestion.Type.String(),
			Key:               protoQuestion.Key,
			Title:             protoQuestion.Title,
			ExtraJSON:         protoQuestion.ExtraJson,
			IsDefault:         protoQuestion.IsDefault,
			IsRequired:        protoQuestion.IsRequired,
			IsTypeEditable:    protoQuestion.IsTypeEditable,
			IsTitleEditable:   protoQuestion.IsTitleEditable,
			IsOptionsEditable: protoQuestion.IsOptionsEditable,
			Sort:              protoQuestion.Sort,
			CreateTime:        protoQuestion.CreateTime.AsTime(),
			UpdateTime:        protoQuestion.UpdateTime.AsTime(),
		}

		// 处理可选字段
		if protoQuestion.Id != nil {
			question.ID = *protoQuestion.Id
		}

		questions = append(questions, question)
	}

	return questions
}

// handleBatchCreateQuestions 处理批量创建问题
func (l *Logic) handleBatchCreateQuestions(ctx context.Context, questions []*fulfillmentreportrepo.Question) error {
	// 检查是否已存在问题
	if len(questions) > 0 {
		existing, err := l.questionRepo.FindByTemplateID(ctx, questions[0].TemplateID)
		if err != nil {
			log.ErrorContext(ctx, "检查现有问题失败",
				zap.Int64("template_id", questions[0].TemplateID),
				zap.Error(err))

			return fmt.Errorf("failed to check existing questions: %w", err)
		}
		if len(existing) > 0 {
			log.WarnContext(ctx, "模板下已存在问题，创建操作失败",
				zap.Int64("template_id", questions[0].TemplateID),
				zap.Int("existing_count", len(existing)))

			return errors.New("questions already exist for this template")
		}
	}

	// 批量创建
	if err := l.questionRepo.BatchCreate(ctx, questions); err != nil {
		log.ErrorContext(ctx, "批量创建问题失败",
			zap.Int64("template_id", questions[0].TemplateID),
			zap.Int("questions_count", len(questions)),
			zap.Error(err))

		return fmt.Errorf("failed to batch create questions: %w", err)
	}

	return nil
}

// handleBatchUpdateQuestions 处理批量更新问题
func (l *Logic) handleBatchUpdateQuestions(ctx context.Context, questions []*fulfillmentreportrepo.Question) error {
	// 批量更新问题
	if err := l.questionRepo.BatchUpdate(ctx, questions); err != nil {
		log.ErrorContext(ctx, "批量更新问题失败",
			zap.Int64("template_id", questions[0].TemplateID),
			zap.Int("questions_count", len(questions)),
			zap.Error(err))

		return fmt.Errorf("failed to batch update questions: %w", err)
	}

	return nil
}

// handleBatchUpsertQuestions 处理批量创建或更新问题
func (l *Logic) handleBatchUpsertQuestions(ctx context.Context, questions []*fulfillmentreportrepo.Question) error {
	// 批量 upsert（先删除再创建）
	if err := l.questionRepo.BatchUpsert(ctx, questions); err != nil {
		log.ErrorContext(ctx, "批量创建或更新问题失败",
			zap.Int64("template_id", questions[0].TemplateID),
			zap.Int("questions_count", len(questions)),
			zap.Error(err))

		return fmt.Errorf("failed to batch upsert questions: %w", err)
	}

	return nil
}

// handleBatchDeleteQuestions 处理批量删除问题
func (l *Logic) handleBatchDeleteQuestions(ctx context.Context, templateID int64) error {
	// 删除该模板下的所有问题
	if err := l.questionRepo.BatchDelete(ctx, templateID); err != nil {
		log.ErrorContext(ctx, "批量删除问题失败",
			zap.Int64("template_id", templateID),
			zap.Error(err))

		return fmt.Errorf("failed to batch delete questions: %w", err)
	}

	return nil
}

// validateSyncRequest 验证同步请求参数
func (l *Logic) validateSyncRequest(req *fulfillmentpb.SyncFulfillmentReportTemplateRequest) error {
	if req.Operation == fulfillmentpb.SyncOperation_SYNC_OPERATION_UNSPECIFIED {
		return errors.New("operation is required")
	}

	// 验证定位方式
	switch identifier := req.Identifier.(type) {
	case *fulfillmentpb.SyncFulfillmentReportTemplateRequest_TemplateId:
		if identifier.TemplateId <= 0 {
			return errors.New("invalid template_id")
		}
	case *fulfillmentpb.SyncFulfillmentReportTemplateRequest_UniqueKey:
		if identifier.UniqueKey.CompanyId <= 0 {
			return errors.New("company_id is required")
		}
		if identifier.UniqueKey.BusinessId <= 0 {
			return errors.New("business_id is required")
		}
		if identifier.UniqueKey.CareType == fulfillmentpb.CareType_CARE_TYPE_UNSPECIFIED {
			return errors.New("care_type is required")
		}
	default:
		return errors.New("identifier is required")
	}

	// CREATE、UPDATE、UPSERT 操作需要提供模板数据
	if req.Operation != fulfillmentpb.SyncOperation_DELETE && req.Template == nil {
		return errors.New("template data is required for create/update/upsert operations")
	}

	return nil
}

// handleCreateOperation 处理创建操作
func (l *Logic) handleCreateOperation(
	ctx context.Context, req *fulfillmentpb.SyncFulfillmentReportTemplateRequest) (int64, error) {
	// 检查记录是否已存在
	if uniqueKey, ok := req.Identifier.(*fulfillmentpb.SyncFulfillmentReportTemplateRequest_UniqueKey); ok {
		existing, err := l.templateRepo.FindByUniqueKey(ctx,
			uniqueKey.UniqueKey.CompanyId,
			uniqueKey.UniqueKey.BusinessId,
			int32(uniqueKey.UniqueKey.CareType))
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.ErrorContext(ctx, "检查现有记录失败",
				zap.Int64("company_id", uniqueKey.UniqueKey.CompanyId),
				zap.Int64("business_id", uniqueKey.UniqueKey.BusinessId),
				zap.Error(err))

			return 0, fmt.Errorf("failed to check existing record: %w", err)
		}
		if existing != nil {
			log.WarnContext(ctx, "模板已存在，创建操作失败",
				zap.Int64("company_id", uniqueKey.UniqueKey.CompanyId),
				zap.Int64("business_id", uniqueKey.UniqueKey.BusinessId),
				zap.Int64("template_id", existing.ID))

			return 0, errors.New("template already exists")
		}
	}

	// 转换并创建记录
	template := l.convertProtoToEntity(req.Template)

	if err := l.templateRepo.Create(ctx, template); err != nil {
		log.ErrorContext(ctx, "创建模板失败",
			zap.Int64("company_id", template.CompanyID),
			zap.Int64("business_id", template.BusinessID),
			zap.Error(err))

		return 0, fmt.Errorf("failed to create template: %w", err)
	}

	return template.ID, nil
}

// handleUpdateOperation 处理更新操作
func (l *Logic) handleUpdateOperation(
	ctx context.Context, req *fulfillmentpb.SyncFulfillmentReportTemplateRequest) (int64, error) {
	var existing *fulfillmentreportrepo.Template
	var err error

	// 根据不同的定位方式查找现有记录
	switch identifier := req.Identifier.(type) {
	case *fulfillmentpb.SyncFulfillmentReportTemplateRequest_TemplateId:
		existing, err = l.templateRepo.FindByID(ctx, identifier.TemplateId)
	case *fulfillmentpb.SyncFulfillmentReportTemplateRequest_UniqueKey:
		existing, err = l.templateRepo.FindByUniqueKey(ctx,
			identifier.UniqueKey.CompanyId,
			identifier.UniqueKey.BusinessId,
			int32(identifier.UniqueKey.CareType))
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnContext(ctx, "模板不存在，更新操作失败")

			return 0, errors.New("template not found")
		}
		log.ErrorContext(ctx, "查找模板失败", zap.Error(err))

		return 0, fmt.Errorf("failed to find template: %w", err)
	}

	// 更新记录
	template := l.convertProtoToEntity(req.Template)
	template.ID = existing.ID
	template.CreateTime = existing.CreateTime // 保持原始创建时间

	if err := l.templateRepo.Update(ctx, template); err != nil {
		log.ErrorContext(ctx, "更新模板失败",
			zap.Int64("template_id", template.ID),
			zap.Error(err))

		return 0, fmt.Errorf("failed to update template: %w", err)
	}

	return template.ID, nil
}

// handleUpsertOperation 处理创建或更新操作
func (l *Logic) handleUpsertOperation(
	ctx context.Context, req *fulfillmentpb.SyncFulfillmentReportTemplateRequest) (int64, error) {
	template := l.convertProtoToEntity(req.Template)

	if err := l.templateRepo.Upsert(ctx, template); err != nil {
		log.ErrorContext(ctx, "创建或更新模板失败",
			zap.Int64("company_id", template.CompanyID),
			zap.Int64("business_id", template.BusinessID),
			zap.Error(err))

		return 0, fmt.Errorf("failed to upsert template: %w", err)
	}

	return template.ID, nil
}

// handleDeleteOperation 处理删除操作
func (l *Logic) handleDeleteOperation(
	ctx context.Context, req *fulfillmentpb.SyncFulfillmentReportTemplateRequest) (int64, error) {
	var templateID int64
	var err error

	switch identifier := req.Identifier.(type) {
	case *fulfillmentpb.SyncFulfillmentReportTemplateRequest_TemplateId:
		templateID = identifier.TemplateId
		err = l.templateRepo.Delete(ctx, templateID)
	case *fulfillmentpb.SyncFulfillmentReportTemplateRequest_UniqueKey:
		// 先查找记录获取ID
		existing, findErr := l.templateRepo.FindByUniqueKey(ctx,
			identifier.UniqueKey.CompanyId,
			identifier.UniqueKey.BusinessId,
			int32(identifier.UniqueKey.CareType))
		if findErr != nil {
			if errors.Is(findErr, gorm.ErrRecordNotFound) {
				log.WarnContext(ctx, "模板不存在，删除操作跳过")

				break
			}
			log.ErrorContext(ctx, "查找模板失败", zap.Error(findErr))

			return 0, fmt.Errorf("failed to find template: %w", findErr)
		}
		templateID = existing.ID
		err = l.templateRepo.Delete(ctx, templateID)
	}

	if err != nil {
		log.ErrorContext(ctx, "删除模板失败",
			zap.Int64("template_id", templateID),
			zap.Error(err))

		return 0, fmt.Errorf("failed to delete template: %w", err)
	}

	// 删除 template 时，同步删除关联 question
	err = l.questionRepo.BatchDelete(ctx, templateID)
	if err != nil {
		log.ErrorContext(ctx, "删除模板关联问题失败",
			zap.Int64("template_id", templateID),
			zap.Error(err))

		return 0, fmt.Errorf("failed to batch delete questions: %w", err)
	}

	return templateID, nil
}

// convertProtoToEntity 将 Proto 消息转换为数据库实体
func (l *Logic) convertProtoToEntity(
	protoTemplate *fulfillmentpb.FulfillmentReportTemplateSync,
) *fulfillmentreportrepo.Template {
	template := &fulfillmentreportrepo.Template{
		CompanyID:                     protoTemplate.CompanyId,
		BusinessID:                    protoTemplate.BusinessId,
		CareType:                      int32(protoTemplate.CareType),
		ThankYouMessage:               protoTemplate.ThankYouMessage,
		ThemeColor:                    protoTemplate.ThemeColor,
		LightThemeColor:               protoTemplate.LightThemeColor,
		ShowShowcase:                  protoTemplate.ShowShowcase,
		ShowOverallFeedback:           protoTemplate.ShowOverallFeedback,
		ShowPetCondition:              protoTemplate.ShowPetCondition,
		ShowServiceStaffName:          protoTemplate.ShowServiceStaffName,
		ShowNextAppointment:           protoTemplate.ShowNextAppointment,
		NextAppointmentDateFormatType: int32(protoTemplate.NextAppointmentDateFormatType),
		ShowReviewBooster:             protoTemplate.ShowReviewBooster,
		ShowYelpReview:                protoTemplate.ShowYelpReview,
		ShowGoogleReview:              protoTemplate.ShowGoogleReview,
		ShowFacebookReview:            protoTemplate.ShowFacebookReview,
		LastPublishTime:               protoTemplate.LastPublishTime.AsTime(),
		Title:                         protoTemplate.Title,
		ThemeCode:                     protoTemplate.ThemeCode,
		UpdateBy:                      protoTemplate.UpdateBy,
		CreateTime:                    protoTemplate.CreateTime.AsTime(),
		UpdateTime:                    protoTemplate.UpdateTime.AsTime(),
	}

	// 处理可选字段
	if protoTemplate.Id != nil {
		template.ID = *protoTemplate.Id
	}
	if protoTemplate.LastPublishTime != nil {
		publishTime := protoTemplate.LastPublishTime.AsTime()
		template.LastPublishTime = publishTime
	}
	if protoTemplate.CreateTime != nil {
		template.CreateTime = protoTemplate.CreateTime.AsTime()
	}
	if protoTemplate.UpdateTime != nil {
		template.UpdateTime = protoTemplate.UpdateTime.AsTime()
	}

	return template
}

// validateSyncReportRequest 验证同步报告请求参数
func (l *Logic) validateSyncReportRequest(req *fulfillmentpb.SyncFulfillmentReportRequest) error {
	if req.Operation == fulfillmentpb.SyncOperation_SYNC_OPERATION_UNSPECIFIED {
		return errors.New("operation is required")
	}

	// 验证定位方式
	if req.Identifier == nil {
		return errors.New("identifier is required")
	}

	// CREATE、UPDATE、UPSERT 操作需要提供报告数据
	if req.Operation != fulfillmentpb.SyncOperation_DELETE && req.Report == nil {
		return errors.New("report data is required for create/update/upsert operations")
	}

	return nil
}

// validateSyncSendRecordRequest 验证同步发送记录请求参数
func (l *Logic) validateSyncSendRecordRequest(req *fulfillmentpb.SyncFulfillmentReportSendRecordRequest) error {
	if req.Operation == fulfillmentpb.SyncOperation_SYNC_OPERATION_UNSPECIFIED {
		return errors.New("operation is required")
	}

	// 验证定位方式
	if req.Identifier == nil {
		return errors.New("identifier is required")
	}

	// CREATE、UPDATE、UPSERT 操作需要提供发送记录数据
	if req.Operation != fulfillmentpb.SyncOperation_DELETE && req.SendRecord == nil {
		return errors.New("send record data is required for create/update/upsert operations")
	}

	return nil
}

// handleCreateReportOperation 处理创建报告操作
func (l *Logic) handleCreateReportOperation(
	ctx context.Context,
	req *fulfillmentpb.SyncFulfillmentReportRequest,
) (
	int64, error,
) {
	report := l.convertReportProtoToEntity(req.Report)

	// 检查是否已存在报告
	if req.Identifier != nil {
		if uniqueKey := req.GetUniqueKey(); uniqueKey != nil {
			existing, err := l.reportRepo.FindByUniqueKey(ctx,
				uniqueKey.BusinessId, uniqueKey.AppointmentId, uniqueKey.PetId,
				int32(uniqueKey.CareType), uniqueKey.ServiceDate)
			if err == nil && existing != nil {
				return 0, errors.New("report already exists")
			}
		}
	}

	if err := l.reportRepo.Create(ctx, report); err != nil {
		return 0, fmt.Errorf("failed to create report: %w", err)
	}

	return report.ID, nil
}

// handleUpdateReportOperation 处理更新报告操作
func (l *Logic) handleUpdateReportOperation(
	ctx context.Context,
	req *fulfillmentpb.SyncFulfillmentReportRequest,
) (
	int64, error,
) {
	report := l.convertReportProtoToEntity(req.Report)

	// 检查报告是否存在
	existing, err := l.reportRepo.FindByID(ctx, report.ID)
	if err != nil {
		return 0, fmt.Errorf("report not found: %w", err)
	}

	// 保持原始创建时间
	report.CreateTime = existing.CreateTime

	if err := l.reportRepo.Update(ctx, report); err != nil {
		return 0, fmt.Errorf("failed to update report: %w", err)
	}

	return report.ID, nil
}

// handleUpsertReportOperation 处理创建或更新报告操作
func (l *Logic) handleUpsertReportOperation(ctx context.Context,
	req *fulfillmentpb.SyncFulfillmentReportRequest) (int64, error) {
	report := l.convertReportProtoToEntity(req.Report)

	if err := l.reportRepo.Upsert(ctx, report); err != nil {
		return 0, fmt.Errorf("failed to upsert report: %w", err)
	}

	return report.ID, nil
}

// handleDeleteReportOperation 处理删除报告操作
func (l *Logic) handleDeleteReportOperation(ctx context.Context,
	req *fulfillmentpb.SyncFulfillmentReportRequest) (int64, error) {
	var reportID int64

	switch identifier := req.Identifier.(type) {
	case *fulfillmentpb.SyncFulfillmentReportRequest_ReportId:
		reportID = identifier.ReportId
	case *fulfillmentpb.SyncFulfillmentReportRequest_UniqueKey:
		// 通过唯一键查找报告
		report, err := l.reportRepo.FindByUniqueKey(ctx,
			identifier.UniqueKey.BusinessId, identifier.UniqueKey.AppointmentId, identifier.UniqueKey.PetId,
			int32(identifier.UniqueKey.CareType), identifier.UniqueKey.ServiceDate)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return 0, nil
			}

			return 0, fmt.Errorf("report found err: %w", err)
		}
		reportID = report.ID
	default:
		return 0, errors.New("invalid report identifier")
	}

	if err := l.reportRepo.Delete(ctx, reportID); err != nil {
		return 0, fmt.Errorf("failed to delete report: %w", err)
	}

	return reportID, nil
}

// handleCreateSendRecordOperation 处理创建发送记录操作
func (l *Logic) handleCreateSendRecordOperation(ctx context.Context,
	req *fulfillmentpb.SyncFulfillmentReportSendRecordRequest) (int64, int64, error) {
	record := l.convertSendRecordProtoToEntity(req.SendRecord)

	// 检查是否已存在发送记录
	if req.Identifier != nil {
		if businessIdentifier := req.GetBusinessIdentifier(); businessIdentifier != nil {
			// 通过报告唯一键查找报告ID
			report, err := l.reportRepo.FindByUniqueKey(ctx,
				businessIdentifier.ReportUniqueKey.BusinessId,
				businessIdentifier.ReportUniqueKey.AppointmentId,
				businessIdentifier.ReportUniqueKey.PetId,
				int32(businessIdentifier.ReportUniqueKey.CareType),
				businessIdentifier.ReportUniqueKey.ServiceDate)
			if err != nil {
				return 0, 0, fmt.Errorf("report not found: %w", err)
			}

			record.ReportID = report.ID
			existing, err := l.sendRecordRepo.FindByReportIDAndSendMethod(ctx, report.ID, record.SendMethod)
			if err == nil && existing != nil {
				return 0, 0, errors.New("send record already exists")
			}
		}
	}

	if err := l.sendRecordRepo.Create(ctx, record); err != nil {
		return 0, 0, fmt.Errorf("failed to create send record: %w", err)
	}

	return record.ID, record.ReportID, nil
}

// handleUpdateSendRecordOperation 处理更新发送记录操作
func (l *Logic) handleUpdateSendRecordOperation(ctx context.Context,
	req *fulfillmentpb.SyncFulfillmentReportSendRecordRequest) (int64, int64, error) {
	record := l.convertSendRecordProtoToEntity(req.SendRecord)

	// 检查发送记录是否存在
	switch identifier := req.Identifier.(type) {
	case *fulfillmentpb.SyncFulfillmentReportSendRecordRequest_RecordId:
		record.ID = identifier.RecordId
	case *fulfillmentpb.SyncFulfillmentReportSendRecordRequest_BusinessIdentifier:
		// 通过业务标识符查找发送记录
		report, err := l.reportRepo.FindByUniqueKey(ctx,
			identifier.BusinessIdentifier.ReportUniqueKey.BusinessId,
			identifier.BusinessIdentifier.ReportUniqueKey.AppointmentId,
			identifier.BusinessIdentifier.ReportUniqueKey.PetId,
			int32(identifier.BusinessIdentifier.ReportUniqueKey.CareType),
			identifier.BusinessIdentifier.ReportUniqueKey.ServiceDate)
		if err != nil {
			return 0, 0, fmt.Errorf("report not found: %w", err)
		}

		existRecord, err := l.sendRecordRepo.FindByReportIDAndSendMethod(ctx,
			report.ID, int32(identifier.BusinessIdentifier.SendMethod))
		if err != nil {
			return 0, 0, fmt.Errorf("send reocrd not found: %w", err)
		}
		record.ID = existRecord.ID
	default:
		return 0, 0, errors.New("invalid send record identifier")
	}
	existing, err := l.sendRecordRepo.FindByID(ctx, record.ID)
	if err != nil {
		return 0, 0, fmt.Errorf("send record not found: %w", err)
	}

	// 保持原始创建时间
	record.CreateTime = existing.CreateTime

	if err := l.sendRecordRepo.Update(ctx, record); err != nil {
		return 0, 0, fmt.Errorf("failed to update send record: %w", err)
	}

	return record.ID, record.ReportID, nil
}

// handleUpsertSendRecordOperation 处理创建或更新发送记录操作
func (l *Logic) handleUpsertSendRecordOperation(ctx context.Context,
	req *fulfillmentpb.SyncFulfillmentReportSendRecordRequest) (int64, int64, error) {
	record := l.convertSendRecordProtoToEntity(req.SendRecord)

	// 检查发送记录是否存在
	identifier := req.GetBusinessIdentifier()
	if identifier == nil {
		return 0, 0, errors.New("invalid identifier")
	}
	report, err := l.reportRepo.FindByUniqueKey(ctx,
		identifier.ReportUniqueKey.BusinessId,
		identifier.ReportUniqueKey.AppointmentId,
		identifier.ReportUniqueKey.PetId,
		int32(identifier.ReportUniqueKey.CareType),
		identifier.ReportUniqueKey.ServiceDate)
	if err != nil {
		return 0, 0, fmt.Errorf("report not found: %w", err)
	}

	record.ReportID = report.ID

	if err := l.sendRecordRepo.Upsert(ctx, record); err != nil {
		return 0, 0, fmt.Errorf("failed to upsert send record: %w", err)
	}

	return record.ID, record.ReportID, nil
}

// handleDeleteSendRecordOperation 处理删除发送记录操作
func (l *Logic) handleDeleteSendRecordOperation(ctx context.Context,
	req *fulfillmentpb.SyncFulfillmentReportSendRecordRequest) (int64, int64, error) {
	var recordID, reportID int64

	switch identifier := req.Identifier.(type) {
	case *fulfillmentpb.SyncFulfillmentReportSendRecordRequest_RecordId:
		recordID = identifier.RecordId
		// 查找关联的报告ID
		record, err := l.sendRecordRepo.FindByID(ctx, recordID)
		if err != nil {
			return 0, 0, fmt.Errorf("send record not found: %w", err)
		}
		reportID = record.ReportID
	case *fulfillmentpb.SyncFulfillmentReportSendRecordRequest_BusinessIdentifier:
		// 通过业务标识符查找发送记录
		report, err := l.reportRepo.FindByUniqueKey(ctx,
			identifier.BusinessIdentifier.ReportUniqueKey.BusinessId,
			identifier.BusinessIdentifier.ReportUniqueKey.AppointmentId,
			identifier.BusinessIdentifier.ReportUniqueKey.PetId,
			int32(identifier.BusinessIdentifier.ReportUniqueKey.CareType),
			identifier.BusinessIdentifier.ReportUniqueKey.ServiceDate)
		if err != nil {
			return 0, 0, fmt.Errorf("report not found: %w", err)
		}

		record, err := l.sendRecordRepo.FindByReportIDAndSendMethod(ctx,
			report.ID, int32(identifier.BusinessIdentifier.SendMethod))
		if err != nil {
			return 0, 0, fmt.Errorf("send record not found: %w", err)
		}
		recordID = record.ID
		reportID = record.ReportID
	default:
		return 0, 0, errors.New("invalid send record identifier")
	}

	if err := l.sendRecordRepo.Delete(ctx, recordID); err != nil {
		return 0, 0, fmt.Errorf("failed to delete send record: %w", err)
	}

	return recordID, reportID, nil
}

// convertReportProtoToEntity 将报告 Proto 消息转换为数据库实体
func (l *Logic) convertReportProtoToEntity(
	protoReport *fulfillmentpb.FulfillmentReportSync,
) *fulfillmentreportrepo.FulfillmentReport {
	report := &fulfillmentreportrepo.FulfillmentReport{
		CompanyID:       protoReport.CompanyId,
		BusinessID:      protoReport.BusinessId,
		CustomerID:      protoReport.CustomerId,
		AppointmentID:   protoReport.AppointmentId,
		CareType:        int32(protoReport.CareType),
		PetID:           protoReport.PetId,
		PetTypeID:       protoReport.PetTypeId,
		UUID:            protoReport.Uuid,
		TemplateJSON:    protoReport.TemplateJson,
		ContentJSON:     protoReport.ContentJson,
		Status:          protoReport.Status,
		LinkOpenedCount: protoReport.LinkOpenedCount,
		ServiceDate:     l.parseServiceDate(protoReport.ServiceDate),
		ThemeCode:       protoReport.ThemeCode,
		UpdateBy:        protoReport.UpdateBy,
		CreateTime:      protoReport.CreateTime.AsTime(),
		UpdateTime:      protoReport.UpdateTime.AsTime(),
	}

	// 处理可选字段
	if protoReport.Id != nil {
		report.ID = *protoReport.Id
	}
	if protoReport.TemplateVersion != nil {
		report.TemplateVersion = protoReport.TemplateVersion.AsTime()
	}

	return report
}

// parseServiceDate 解析服务日期字符串为 time.Time
func (l *Logic) parseServiceDate(serviceDateStr string) time.Time {
	serviceDate, err := time.Parse("2006-01-02", serviceDateStr)
	if err != nil {
		// 如果解析失败，返回零值时间
		return time.Time{}
	}

	return serviceDate
}

// convertSendRecordProtoToEntity 将发送记录 Proto 消息转换为数据库实体
func (l *Logic) convertSendRecordProtoToEntity(
	protoRecord *fulfillmentpb.FulfillmentReportSendRecordSync,
) *fulfillmentreportrepo.SendRecord {
	record := &fulfillmentreportrepo.SendRecord{
		ReportID:      protoRecord.ReportId,
		CompanyID:     protoRecord.CompanyId,
		BusinessID:    protoRecord.BusinessId,
		AppointmentID: protoRecord.AppointmentId,
		ContentJSON:   protoRecord.ContentJson,
		PetID:         protoRecord.PetId,
		SendMethod:    int32(protoRecord.SendMethod),
		SentBy:        protoRecord.SentBy,
		ErrorMessage:  protoRecord.ErrorMessage,
		IsSentSuccess: protoRecord.IsSentSuccess,
		CreateTime:    protoRecord.CreateTime.AsTime(),
		UpdateTime:    protoRecord.UpdateTime.AsTime(),
	}

	// 处理可选字段
	if protoRecord.Id != nil {
		record.ID = *protoRecord.Id
	}
	if protoRecord.SentTime != nil {
		record.SentTime = protoRecord.SentTime.AsTime()
	}

	return record
}

// convertQuestionProtoToEntity 将问题 Proto 消息转换为数据库实体
func (l *Logic) convertQuestionProtoToEntity(
	protoQuestion *fulfillmentpb.FulfillmentReportQuestionSync,
) *fulfillmentreportrepo.Question {
	question := &fulfillmentreportrepo.Question{
		CompanyID:         protoQuestion.CompanyId,
		BusinessID:        protoQuestion.BusinessId,
		Category:          int32(protoQuestion.Category),
		Type:              protoQuestion.Type.String(),
		Key:               protoQuestion.Key,
		Title:             protoQuestion.Title,
		ExtraJSON:         protoQuestion.ExtraJson,
		IsDefault:         protoQuestion.IsDefault,
		IsRequired:        protoQuestion.IsRequired,
		IsTypeEditable:    protoQuestion.IsTypeEditable,
		IsTitleEditable:   protoQuestion.IsTitleEditable,
		IsOptionsEditable: protoQuestion.IsOptionsEditable,
		Sort:              protoQuestion.Sort,
		CreateTime:        protoQuestion.CreateTime.AsTime(),
		UpdateTime:        protoQuestion.UpdateTime.AsTime(),
	}

	// 处理可选字段
	if protoQuestion.Id != nil {
		question.ID = *protoQuestion.Id
	}
	if protoQuestion.TemplateId != nil {
		question.TemplateID = *protoQuestion.TemplateId
	}

	return question
}

// convertTemplateEntityToProto 将模板数据库实体转换为 Proto 消息
func (l *Logic) convertTemplateEntityToProto(
	template *fulfillmentreportrepo.Template) *fulfillmentpb.FulfillmentReportTemplateSync {
	protoTemplate := &fulfillmentpb.FulfillmentReportTemplateSync{
		Id:                   &template.ID,
		CompanyId:            template.CompanyID,
		BusinessId:           template.BusinessID,
		CareType:             fulfillmentpb.CareType(template.CareType),
		ThankYouMessage:      template.ThankYouMessage,
		ThemeColor:           template.ThemeColor,
		LightThemeColor:      template.LightThemeColor,
		ShowShowcase:         template.ShowShowcase,
		ShowOverallFeedback:  template.ShowOverallFeedback,
		ShowPetCondition:     template.ShowPetCondition,
		ShowServiceStaffName: template.ShowServiceStaffName,
		ShowNextAppointment:  template.ShowNextAppointment,
		// nolint:lll
		NextAppointmentDateFormatType: fulfillmentpb.NextAppointmentDateFormatType(template.NextAppointmentDateFormatType),
		ShowReviewBooster:             template.ShowReviewBooster,
		ShowYelpReview:                template.ShowYelpReview,
		ShowGoogleReview:              template.ShowGoogleReview,
		ShowFacebookReview:            template.ShowFacebookReview,
		Title:                         template.Title,
		UpdateBy:                      template.UpdateBy,
		CreateTime:                    timestamppb.New(template.CreateTime),
		UpdateTime:                    timestamppb.New(template.UpdateTime),
		ThemeCode:                     template.ThemeCode,
	}

	// 处理可选字段
	if !template.LastPublishTime.IsZero() {
		protoTemplate.LastPublishTime = timestamppb.New(template.LastPublishTime)
	}

	return protoTemplate
}

// convertQuestionEntityToProto 将问题数据库实体转换为 Proto 消息
func (l *Logic) convertQuestionEntityToProto(
	question *fulfillmentreportrepo.Question,
) *fulfillmentpb.FulfillmentReportQuestionSync {
	protoQuestion := &fulfillmentpb.FulfillmentReportQuestionSync{
		Id:                &question.ID,
		CompanyId:         question.CompanyID,
		BusinessId:        question.BusinessID,
		TemplateId:        &question.TemplateID,
		Category:          fulfillmentpb.QuestionCategory(question.Category),
		Type:              fulfillmentpb.QuestionType(fulfillmentpb.QuestionType_value[question.Type]),
		Key:               question.Key,
		Title:             question.Title,
		ExtraJson:         question.ExtraJSON,
		IsDefault:         question.IsDefault,
		IsRequired:        question.IsRequired,
		IsTypeEditable:    question.IsTypeEditable,
		IsTitleEditable:   question.IsTitleEditable,
		IsOptionsEditable: question.IsOptionsEditable,
		Sort:              question.Sort,
		CreateTime:        timestamppb.New(question.CreateTime),
		UpdateTime:        timestamppb.New(question.UpdateTime),
	}

	return protoQuestion
}

// convertReportEntityToProto 将报告数据库实体转换为 Proto 消息
func (l *Logic) convertReportEntityToProto(
	report *fulfillmentreportrepo.FulfillmentReport,
) *fulfillmentpb.FulfillmentReportSync {
	protoReport := &fulfillmentpb.FulfillmentReportSync{
		Id:              &report.ID,
		CompanyId:       report.CompanyID,
		BusinessId:      report.BusinessID,
		CustomerId:      report.CustomerID,
		AppointmentId:   report.AppointmentID,
		CareType:        fulfillmentpb.CareType(report.CareType),
		PetId:           report.PetID,
		Uuid:            report.UUID,
		TemplateJson:    report.TemplateJSON,
		ContentJson:     report.ContentJSON,
		Status:          report.Status,
		LinkOpenedCount: int32(report.LinkOpenedCount),
		ServiceDate:     report.ServiceDate.Format("2006-01-02"),
		UpdateBy:        report.UpdateBy,
		CreateTime:      timestamppb.New(report.CreateTime),
		UpdateTime:      timestamppb.New(report.UpdateTime),
		ThemeCode:       report.ThemeCode,
	}

	// 处理可选字段
	if !report.TemplateVersion.IsZero() {
		protoReport.TemplateVersion = timestamppb.New(report.TemplateVersion)
	}

	return protoReport
}

// convertSendRecordEntityToProto 将发送记录数据库实体转换为 Proto 消息
func (l *Logic) convertSendRecordEntityToProto(
	record *fulfillmentreportrepo.SendRecord,
) *fulfillmentpb.FulfillmentReportSendRecordSync {
	protoRecord := &fulfillmentpb.FulfillmentReportSendRecordSync{
		Id:            &record.ID,
		ReportId:      record.ReportID,
		CompanyId:     record.CompanyID,
		BusinessId:    record.BusinessID,
		AppointmentId: record.AppointmentID,
		ContentJson:   record.ContentJSON,
		PetId:         record.PetID,
		SendMethod:    fulfillmentpb.SendMethod(record.SendMethod),
		SentBy:        record.SentBy,
		ErrorMessage:  record.ErrorMessage,
		IsSentSuccess: record.IsSentSuccess,
		CreateTime:    timestamppb.New(record.CreateTime),
		UpdateTime:    timestamppb.New(record.UpdateTime),
	}

	// 处理可选字段
	if !record.SentTime.IsZero() {
		protoRecord.SentTime = timestamppb.New(record.SentTime)
	}

	return protoRecord
}

// BatchMigrateTemplates 批量迁移模板（数据迁移专用）
func (l *Logic) BatchMigrateTemplates(ctx context.Context,
	req *fulfillmentpb.BatchMigrateTemplatesRequest) (*fulfillmentpb.BatchMigrateTemplatesResponse, error) {
	// 参数验证
	if len(req.Templates) == 0 {
		return &fulfillmentpb.BatchMigrateTemplatesResponse{
			SuccessCount: 0,
			FailedCount:  0,
			SkippedCount: 0,
		}, nil
	}

	// 转换为数据库实体
	templates := make([]*fulfillmentreportrepo.Template, 0, len(req.Templates))
	for _, protoTemplate := range req.Templates {
		template := l.convertProtoToEntity(protoTemplate)
		templates = append(templates, template)
	}

	// 使用事务进行批量插入
	err := l.transactionManager.Tx(ctx, func(txCtx context.Context, tx *gorm.DB) error {
		// 直接批量插入
		if err := tx.WithContext(txCtx).Create(templates).Error; err != nil {
			log.ErrorContext(ctx, "批量插入模板失败",
				zap.Int("templates_count", len(templates)),
				zap.Error(err))

			return fmt.Errorf("failed to batch insert templates: %w", err)
		}

		return nil
	})

	if err != nil {
		// 事务失败，记录错误日志
		log.ErrorContext(ctx, "批量迁移模板失败",
			zap.Int("templates_count", len(req.Templates)),
			zap.Error(err))

		return &fulfillmentpb.BatchMigrateTemplatesResponse{
			SuccessCount: 0,
			FailedCount:  int32(len(req.Templates)),
			SkippedCount: 0,
		}, err
	}

	return &fulfillmentpb.BatchMigrateTemplatesResponse{
		SuccessCount: int32(len(req.Templates)),
		FailedCount:  0,
		SkippedCount: 0,
	}, nil
}

// BatchMigrateQuestions 批量迁移问题（数据迁移专用）
func (l *Logic) BatchMigrateQuestions(ctx context.Context,
	req *fulfillmentpb.BatchMigrateQuestionsRequest) (*fulfillmentpb.BatchMigrateQuestionsResponse, error) {
	// 参数验证
	if len(req.Questions) == 0 {
		return &fulfillmentpb.BatchMigrateQuestionsResponse{
			SuccessCount: 0,
			FailedCount:  0,
			SkippedCount: 0,
		}, nil
	}

	// 转换为数据库实体
	questions := make([]*fulfillmentreportrepo.Question, 0, len(req.Questions))
	for _, protoQuestion := range req.Questions {
		question := l.convertQuestionProtoToEntity(protoQuestion)
		questions = append(questions, question)
	}

	// 使用事务进行批量插入
	err := l.transactionManager.Tx(ctx, func(txCtx context.Context, tx *gorm.DB) error {
		// 直接批量插入
		if err := tx.WithContext(txCtx).Create(questions).Error; err != nil {
			log.ErrorContext(ctx, "批量插入问题失败",
				zap.Int("questions_count", len(questions)),
				zap.Error(err))

			return fmt.Errorf("failed to batch insert questions: %w", err)
		}

		return nil
	})

	if err != nil {
		// 事务失败，记录错误日志
		log.ErrorContext(ctx, "批量迁移问题失败",
			zap.Int("questions_count", len(req.Questions)),
			zap.Error(err))

		return &fulfillmentpb.BatchMigrateQuestionsResponse{
			SuccessCount: 0,
			FailedCount:  int32(len(req.Questions)),
			SkippedCount: 0,
		}, err
	}

	return &fulfillmentpb.BatchMigrateQuestionsResponse{
		SuccessCount: int32(len(req.Questions)),
		FailedCount:  0,
		SkippedCount: 0,
	}, nil
}

// BatchMigrateReports 批量迁移报告（数据迁移专用）
func (l *Logic) BatchMigrateReports(ctx context.Context,
	req *fulfillmentpb.BatchMigrateReportsRequest) (*fulfillmentpb.BatchMigrateReportsResponse, error) {
	// 参数验证
	if len(req.Reports) == 0 {
		return &fulfillmentpb.BatchMigrateReportsResponse{
			SuccessCount: 0,
			FailedCount:  0,
			SkippedCount: 0,
		}, nil
	}

	// 转换为数据库实体
	reports := make([]*fulfillmentreportrepo.FulfillmentReport, 0, len(req.Reports))
	for _, protoReport := range req.Reports {
		report := l.convertReportProtoToEntity(protoReport)
		reports = append(reports, report)
	}

	// 使用事务进行批量插入
	err := l.transactionManager.Tx(ctx, func(txCtx context.Context, tx *gorm.DB) error {
		// 直接批量插入
		if err := tx.WithContext(txCtx).Create(reports).Error; err != nil {
			log.ErrorContext(ctx, "批量插入报告失败",
				zap.Int("reports_count", len(reports)),
				zap.Error(err))

			return fmt.Errorf("failed to batch insert reports: %w", err)
		}

		return nil
	})

	if err != nil {
		// 事务失败，记录错误日志
		log.ErrorContext(ctx, "批量迁移报告失败",
			zap.Int("reports_count", len(req.Reports)),
			zap.Error(err))

		return &fulfillmentpb.BatchMigrateReportsResponse{
			SuccessCount: 0,
			FailedCount:  int32(len(req.Reports)),
			SkippedCount: 0,
		}, err
	}

	return &fulfillmentpb.BatchMigrateReportsResponse{
		SuccessCount: int32(len(req.Reports)),
		FailedCount:  0,
		SkippedCount: 0,
	}, nil
}

// BatchMigrateRecords 批量迁移发送记录（数据迁移专用）
func (l *Logic) BatchMigrateRecords(ctx context.Context,
	req *fulfillmentpb.BatchMigrateRecordsRequest) (*fulfillmentpb.BatchMigrateRecordsResponse, error) {
	// 参数验证
	if len(req.Records) == 0 {
		return &fulfillmentpb.BatchMigrateRecordsResponse{
			SuccessCount: 0,
			FailedCount:  0,
			SkippedCount: 0,
		}, nil
	}

	// 转换为数据库实体
	records := make([]*fulfillmentreportrepo.SendRecord, 0, len(req.Records))
	for _, protoRecord := range req.Records {
		record := l.convertSendRecordProtoToEntity(protoRecord)
		records = append(records, record)
	}

	// 使用事务进行批量插入
	err := l.transactionManager.Tx(ctx, func(txCtx context.Context, tx *gorm.DB) error {
		// 直接批量插入
		if err := tx.WithContext(txCtx).Create(records).Error; err != nil {
			log.ErrorContext(ctx, "批量插入发送记录失败",
				zap.Int("records_count", len(records)),
				zap.Error(err))

			return fmt.Errorf("failed to batch insert records: %w", err)
		}

		return nil
	})

	if err != nil {
		// 事务失败，记录错误日志
		log.ErrorContext(ctx, "批量迁移发送记录失败",
			zap.Int("records_count", len(req.Records)),
			zap.Error(err))

		return &fulfillmentpb.BatchMigrateRecordsResponse{
			SuccessCount: 0,
			FailedCount:  int32(len(req.Records)),
			SkippedCount: 0,
		}, err
	}

	return &fulfillmentpb.BatchMigrateRecordsResponse{
		SuccessCount: int32(len(req.Records)),
		FailedCount:  0,
		SkippedCount: 0,
	}, nil
}

// GetTemplatesByUniqueKeys 通过唯一键批量查询模板
func (l *Logic) GetTemplatesByUniqueKeys(ctx context.Context,
	req *fulfillmentpb.GetTemplatesByUniqueKeysRequest) (*fulfillmentpb.GetTemplatesByUniqueKeysResponse, error) {

	// 参数验证
	if len(req.UniqueKeys) == 0 {
		return &fulfillmentpb.GetTemplatesByUniqueKeysResponse{
			Templates: []*fulfillmentpb.FulfillmentReportTemplateSync{},
		}, nil
	}

	// 转换为 repository 层的唯一键结构
	uniqueKeys := make([]fulfillmentreportrepo.UniqueKey, 0, len(req.UniqueKeys))
	for _, protoUniqueKey := range req.UniqueKeys {
		uniqueKeys = append(uniqueKeys, fulfillmentreportrepo.UniqueKey{
			CompanyID:  protoUniqueKey.CompanyId,
			BusinessID: protoUniqueKey.BusinessId,
			CareType:   int32(protoUniqueKey.CareType),
		})
	}

	// 使用 repository 层查询
	templates, err := l.templateRepo.FindByUniqueKeys(ctx, uniqueKeys)
	if err != nil {
		return nil, err
	}

	// 转换为 Proto 响应
	protoTemplates := make([]*fulfillmentpb.FulfillmentReportTemplateSync, 0, len(templates))
	for _, template := range templates {
		protoTemplate := l.convertTemplateEntityToProto(template)
		protoTemplates = append(protoTemplates, protoTemplate)
	}

	return &fulfillmentpb.GetTemplatesByUniqueKeysResponse{
		Templates: protoTemplates,
	}, nil
}

// GetQuestionsByTemplateKeys 通过模板唯一键批量查询问题
func (l *Logic) GetQuestionsByTemplateKeys(ctx context.Context,
	req *fulfillmentpb.GetQuestionsByTemplateKeysRequest) (*fulfillmentpb.GetQuestionsByTemplateKeysResponse, error) {

	// 参数验证
	if len(req.TemplateKeys) == 0 {
		return &fulfillmentpb.GetQuestionsByTemplateKeysResponse{
			Questions: []*fulfillmentpb.FulfillmentReportQuestionSync{},
		}, nil
	}

	// 转换为 repository 层的唯一键结构
	uniqueKeys := make([]fulfillmentreportrepo.UniqueKey, 0, len(req.TemplateKeys))
	for _, protoUniqueKey := range req.TemplateKeys {
		uniqueKeys = append(uniqueKeys, fulfillmentreportrepo.UniqueKey{
			CompanyID:  protoUniqueKey.CompanyId,
			BusinessID: protoUniqueKey.BusinessId,
			CareType:   int32(protoUniqueKey.CareType),
		})
	}

	// 先查询模板ID
	templates, err := l.templateRepo.FindByUniqueKeys(ctx, uniqueKeys)
	if err != nil {
		return nil, err
	}

	// 提取模板ID
	templateIDs := make([]int64, 0, len(templates))
	for _, template := range templates {
		templateIDs = append(templateIDs, template.ID)
	}

	if len(templateIDs) == 0 {
		return &fulfillmentpb.GetQuestionsByTemplateKeysResponse{
			Questions: []*fulfillmentpb.FulfillmentReportQuestionSync{},
		}, nil
	}

	// 根据模板ID查询问题
	questions, err := l.questionRepo.FindByTemplateIDs(ctx, templateIDs)
	if err != nil {
		return nil, err
	}

	// 转换为 Proto 响应
	protoQuestions := make([]*fulfillmentpb.FulfillmentReportQuestionSync, 0, len(questions))
	for _, question := range questions {
		protoQuestion := l.convertQuestionEntityToProto(question)
		protoQuestions = append(protoQuestions, protoQuestion)
	}

	return &fulfillmentpb.GetQuestionsByTemplateKeysResponse{
		Questions: protoQuestions,
	}, nil
}

// GetGroomingQuestionsByQuestionKeys 通过问题唯一键批量查询问题（grooming迁移专用）
func (l *Logic) GetGroomingQuestionsByQuestionKeys(
	ctx context.Context,
	req *fulfillmentpb.GetGroomingQuestionsByQuestionKeysRequest,
) (
	*fulfillmentpb.GetGroomingQuestionsByQuestionKeysResponse, error,
) {

	// 参数验证
	if len(req.QuestionKeys) == 0 {
		return &fulfillmentpb.GetGroomingQuestionsByQuestionKeysResponse{
			Questions: []*fulfillmentpb.FulfillmentReportQuestionSync{},
		}, nil
	}

	// 收集所有需要查询的模板唯一键
	templateUniqueKeys := make([]fulfillmentreportrepo.UniqueKey, 0, len(req.QuestionKeys))

	for _, questionKey := range req.QuestionKeys {
		// grooming 的 care_type 固定为 1
		templateUniqueKeys = append(templateUniqueKeys, fulfillmentreportrepo.UniqueKey{
			CompanyID:  questionKey.CompanyId,
			BusinessID: questionKey.BusinessId,
			CareType:   1, // grooming
		})
	}

	// 查询模板
	templates, err := l.templateRepo.FindByUniqueKeys(ctx, templateUniqueKeys)
	if err != nil {
		return nil, err
	}

	// 为每个模板ID查询对应的问题
	allQuestions := make([]*fulfillmentreportrepo.Question, 0)
	for _, template := range templates {
		// 收集该模板下需要查询的问题标题
		titles := make([]string, 0)
		for _, questionKey := range req.QuestionKeys {
			if questionKey.CompanyId == template.CompanyID && questionKey.BusinessId == template.BusinessID {
				titles = append(titles, questionKey.Title)
			}
		}

		if len(titles) > 0 {
			questions, err := l.questionRepo.FindByTemplateIDAndTitles(ctx, template.ID, titles)
			if err != nil {
				return nil, err
			}
			allQuestions = append(allQuestions, questions...)
		}
	}

	// 转换为 Proto 响应
	protoQuestions := make([]*fulfillmentpb.FulfillmentReportQuestionSync, 0, len(allQuestions))
	for _, question := range allQuestions {
		protoQuestion := l.convertQuestionEntityToProto(question)
		protoQuestions = append(protoQuestions, protoQuestion)
	}

	return &fulfillmentpb.GetGroomingQuestionsByQuestionKeysResponse{
		Questions: protoQuestions,
	}, nil
}

// GetReportsByUniqueKeys 通过唯一键批量查询报告
func (l *Logic) GetReportsByUniqueKeys(ctx context.Context,
	req *fulfillmentpb.GetReportsByUniqueKeysRequest) (*fulfillmentpb.GetReportsByUniqueKeysResponse, error) {

	// 参数验证
	if len(req.UniqueKeys) == 0 {
		return &fulfillmentpb.GetReportsByUniqueKeysResponse{
			Reports: []*fulfillmentpb.FulfillmentReportSync{},
		}, nil
	}

	// 转换为 repository 层的唯一键结构
	uniqueKeys := make([]fulfillmentreportrepo.ReportUniqueKey, 0, len(req.UniqueKeys))
	for _, protoUniqueKey := range req.UniqueKeys {
		// 将 string 转换为 time.Time
		serviceDate, err := time.Parse("2006-01-02", protoUniqueKey.ServiceDate)
		if err != nil {
			return nil, fmt.Errorf("invalid service_date format: %s", protoUniqueKey.ServiceDate)
		}
		uniqueKeys = append(uniqueKeys, fulfillmentreportrepo.ReportUniqueKey{
			BusinessID:    protoUniqueKey.BusinessId,
			AppointmentID: protoUniqueKey.AppointmentId,
			PetID:         protoUniqueKey.PetId,
			CareType:      int32(protoUniqueKey.CareType),
			ServiceDate:   serviceDate,
		})
	}

	// 使用 repository 层查询
	reports, err := l.reportRepo.FindByUniqueKeys(ctx, uniqueKeys)
	if err != nil {
		return nil, err
	}

	// 转换为 Proto 响应
	protoReports := make([]*fulfillmentpb.FulfillmentReportSync, 0, len(reports))
	for _, report := range reports {
		protoReport := l.convertReportEntityToProto(report)
		protoReports = append(protoReports, protoReport)
	}

	return &fulfillmentpb.GetReportsByUniqueKeysResponse{
		Reports: protoReports,
	}, nil
}

// GetRecordsByReportKeys 通过发送记录唯一键批量查询发送记录
func (l *Logic) GetRecordsByReportKeys(ctx context.Context,
	req *fulfillmentpb.GetRecordsByReportKeysRequest) (*fulfillmentpb.GetRecordsByReportKeysResponse, error) {

	// 参数验证
	if len(req.RecordKeys) == 0 {
		return &fulfillmentpb.GetRecordsByReportKeysResponse{
			Records: []*fulfillmentpb.FulfillmentReportSendRecordSync{},
		}, nil
	}

	// 先查询所有相关的报告ID
	reportUniqueKeys := make([]fulfillmentreportrepo.ReportUniqueKey, 0, len(req.RecordKeys))

	for _, recordKey := range req.RecordKeys {
		// 将 string 转换为 time.Time
		serviceDate, err := time.Parse("2006-01-02", recordKey.ReportUniqueKey.ServiceDate)
		if err != nil {
			return nil, fmt.Errorf("invalid service_date format: %s", recordKey.ReportUniqueKey.ServiceDate)
		}
		reportUniqueKeys = append(reportUniqueKeys, fulfillmentreportrepo.ReportUniqueKey{
			BusinessID:    recordKey.ReportUniqueKey.BusinessId,
			AppointmentID: recordKey.ReportUniqueKey.AppointmentId,
			PetID:         recordKey.ReportUniqueKey.PetId,
			CareType:      int32(recordKey.ReportUniqueKey.CareType),
			ServiceDate:   serviceDate,
		})
	}

	// 查询报告
	reports, err := l.reportRepo.FindByUniqueKeys(ctx, reportUniqueKeys)
	if err != nil {
		return nil, err
	}

	// 构建报告唯一键到报告ID的映射
	reportMap := make(map[string]int64)
	for _, report := range reports {
		key := fmt.Sprintf("%d_%d_%d_%d_%s",
			report.BusinessID,
			report.AppointmentID,
			report.PetID,
			report.CareType,
			report.ServiceDate.Format("2006-01-02"))
		reportMap[key] = report.ID
	}

	// 构建发送记录唯一键
	sendRecordUniqueKeys := make([]fulfillmentreportrepo.SendRecordUniqueKey, 0, len(req.RecordKeys))
	for _, recordKey := range req.RecordKeys {
		// 构建查找键
		key := fmt.Sprintf("%d_%d_%d_%d_%s",
			recordKey.ReportUniqueKey.BusinessId,
			recordKey.ReportUniqueKey.AppointmentId,
			recordKey.ReportUniqueKey.PetId,
			recordKey.ReportUniqueKey.CareType,
			recordKey.ReportUniqueKey.ServiceDate)

		// 从映射中查找报告ID
		if reportID, exists := reportMap[key]; exists {
			sendRecordUniqueKeys = append(sendRecordUniqueKeys, fulfillmentreportrepo.SendRecordUniqueKey{
				ReportID:   reportID,
				SendMethod: int32(recordKey.SendMethod),
			})
		}
	}

	if len(sendRecordUniqueKeys) == 0 {
		return &fulfillmentpb.GetRecordsByReportKeysResponse{
			Records: []*fulfillmentpb.FulfillmentReportSendRecordSync{},
		}, nil
	}

	// 查询发送记录
	records, err := l.sendRecordRepo.FindByUniqueKeys(ctx, sendRecordUniqueKeys)
	if err != nil {
		return nil, err
	}

	// 转换为 Proto 响应
	protoRecords := make([]*fulfillmentpb.FulfillmentReportSendRecordSync, 0, len(records))
	for _, record := range records {
		protoRecord := l.convertSendRecordEntityToProto(record)
		protoRecords = append(protoRecords, protoRecord)
	}

	return &fulfillmentpb.GetRecordsByReportKeysResponse{
		Records: protoRecords,
	}, nil
}
