server:
  filter:
    - opentelemetry
    - debuglog
    - recovery
  service:
    - name: backend.proto.fulfillment.v1.FulfillmentService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.fulfillment.v1.AppointmentService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.fulfillment.v1.FulfillmentReportService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 5000
  service:
    - callee: postgres.moego_fulfillment
      target: dsn://postgresql://moego_developer_240310_eff7a0dc:<EMAIL>:40132/moego_fulfillment?sslmode=disable
      protocol: gorm
      transport: gorm
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres.moego_fulfillment
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  telemetry:
    opentelemetry:
      traces:
        disable_trace_body: false
