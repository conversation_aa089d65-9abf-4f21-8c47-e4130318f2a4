package appointment

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func (i *impl) Count(ctx context.Context, param *BaseParam, filter *Filter) (int64, error) {
	if param == nil {
		return 0, nil
	}
	query := i.buildQuery(ctx, param, filter)
	var count int64
	if err := query.Count(&count).Error; err != nil {
		log.ErrorContextf(ctx, "GetAppointmentListCount err, err:%+v", err)

		return 0, err
	}

	return count, nil
}

func (i *impl) List(ctx context.Context, param *BaseParam, filter *Filter) ([]*Appointment, error) {
	if param == nil {
		return []*Appointment{}, nil
	}
	query := i.buildQuery(ctx, param, filter)
	var appointmentList []*Appointment
	offset, limit := buildPaginationInfo(param.PaginationInfo)
	if err := query.
		Offset(int(offset)).
		Limit(int(limit)).
		Clauses(clause.OrderBy{Columns: []clause.OrderByColumn{
			{Column: clause.Column{Name: ColumnCreatedAt}, Desc: true},
		}}).
		Find(&appointmentList).Error; err != nil {
		log.ErrorContextf(ctx, "GetAppointmentList err, err:%+v", err)

		return nil, err
	}

	return appointmentList, nil
}

func (i *impl) GetByID(ctx context.Context, id int) (*Appointment, error) {
	var appointment Appointment
	if err := i.db.WithContext(ctx).Clauses(clause.Eq{
		Column: ColumnID,
		Value:  id,
	}).First(&appointment).Error; err != nil {
		return nil, err
	}

	return &appointment, nil
}

func (i *impl) GetByIDs(ctx context.Context, ids []int64) ([]*Appointment, error) {
	if len(ids) == 0 {
		return []*Appointment{}, nil
	}

	var appointments []*Appointment
	if err := i.db.WithContext(ctx).Clauses(clause.IN{
		Column: ColumnID,
		Values: convertToInterface(ids),
	}).Find(&appointments).Error; err != nil {
		log.ErrorContextf(ctx, "GetAppointmentByIDs err, err:%+v", err)

		return nil, err
	}

	return appointments, nil
}

func buildPaginationInfo(paginationInfo *PaginationInfo) (int32, int32) {
	if paginationInfo == nil {
		return defaultOffset, defaultLimit
	}

	return paginationInfo.Offset, paginationInfo.Limit
}

func (i *impl) buildQuery(ctx context.Context, param *BaseParam, filter *Filter) *gorm.DB {
	query := i.db.WithContext(ctx).Model(&Appointment{})
	query = i.applyBaseParamFilters(query, param)
	if filter != nil {
		query = i.applyFilterConditions(query, filter)
	}

	return query
}

// applyBaseParamFilters 应用BaseParam过滤条件
func (i *impl) applyBaseParamFilters(query *gorm.DB, param *BaseParam) *gorm.DB {
	clauses := []clause.Expression{}
	if param.BusinessID != 0 {
		clauses = append(clauses, clause.Eq{Column: ColumnBusinessID, Value: param.BusinessID})
	}
	if param.CompanyID != 0 {
		clauses = append(clauses, clause.Eq{Column: ColumnCompanyID, Value: param.CompanyID})
	}
	// 时间范围查询 - 使用Gte和Lte组合实现BETWEEN功能
	if !param.StartTime.IsZero() {
		clauses = append(clauses, clause.Gte{Column: ColumnStartTime, Value: param.StartTime})
	}
	if !param.EndTime.IsZero() {
		clauses = append(clauses, clause.Lte{Column: ColumnEndTime, Value: param.EndTime})
	}

	return query.Clauses(clauses...)
}

// applyFilterConditions 应用Filter过滤条件
func (i *impl) applyFilterConditions(query *gorm.DB, filter *Filter) *gorm.DB {
	clauses := []clause.Expression{}

	if len(filter.CustomerIDs) > 0 {
		clauses = append(clauses, clause.IN{Column: ColumnCustomerID, Values: convertToInterface(filter.CustomerIDs)})
	}
	if len(filter.Statuses) > 0 {
		clauses = append(clauses, clause.IN{Column: ColumnStatus, Values: convertToInterface(filter.Statuses)})
	}
	if len(filter.ServiceItemTypes) > 0 {
		clauses = append(clauses, clause.IN{Column: ColumnServiceItemType,
			Values: convertToInterface(filter.ServiceItemTypes)})
	}
	if len(filter.BusinessIDs) > 0 {
		clauses = append(clauses, clause.IN{Column: ColumnBusinessID, Values: convertToInterface(filter.BusinessIDs)})
	}
	if len(filter.AppointmentIDs) > 0 {
		clauses = append(clauses, clause.IN{Column: ColumnID, Values: convertToInterface(filter.AppointmentIDs)})
	}

	if len(clauses) > 0 {
		query = query.Clauses(clauses...)
	}

	return query
}

// convertToInterface 将各种类型的切片转换为[]interface{}
func convertToInterface[T any](slice []T) []interface{} {
	if len(slice) == 0 {
		return nil
	}
	result := make([]interface{}, len(slice))
	for i, v := range slice {
		result[i] = v
	}

	return result
}
