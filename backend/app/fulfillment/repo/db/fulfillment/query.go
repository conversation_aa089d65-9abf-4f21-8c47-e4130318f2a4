package fulfillment

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func (i *impl) Count(ctx context.Context, param *BaseParam, filter *Filter) (int64, error) {
	if param == nil {
		return 0, nil
	}
	query := i.buildQuery(ctx, param, filter)
	var count int64
	if err := query.Count(&count).Error; err != nil {
		log.ErrorContextf(ctx, "GetFulfillmentListCount err, err:%+v", err)

		return 0, err
	}

	return count, nil
}

func (i *impl) List(ctx context.Context, param *BaseParam, filter *Filter) ([]*Fulfillment, error) {
	if param == nil {
		return []*Fulfillment{}, nil
	}
	query := i.buildQuery(ctx, param, filter)
	var fulfillmentList []*Fulfillment
	offset, limit := buildPaginationInfo(param.PaginationInfo)
	if err := query.
		Offset(int(offset)).
		Limit(int(limit)).
		Clauses(clause.OrderBy{Columns: []clause.OrderByColumn{
			{Column: clause.Column{Name: ColumnCreateTime}, Desc: true},
		}}).
		Find(&fulfillmentList).Error; err != nil {
		log.ErrorContextf(ctx, "GetFulfillmentList err, err:%+v", err)

		return nil, err
	}

	return fulfillmentList, nil
}

func buildPaginationInfo(paginationInfo *PaginationInfo) (int32, int32) {
	if paginationInfo == nil {
		return defaultOffset, defaultLimit
	}

	return paginationInfo.Offset, paginationInfo.Limit
}

func (i *impl) buildQuery(ctx context.Context, param *BaseParam, filter *Filter) *gorm.DB {
	// 使用Model方法，让GORM自动处理表名和字段映射
	query := i.db.WithContext(ctx).Model(&Fulfillment{})

	// 应用BaseParam过滤条件
	query = i.applyBaseParamFilters(query, param)

	// 应用Filter过滤条件
	if filter != nil {
		query = i.applyFilterConditions(query, filter)
	}

	return query
}

// applyBaseParamFilters 应用BaseParam过滤条件
func (i *impl) applyBaseParamFilters(query *gorm.DB, param *BaseParam) *gorm.DB {
	clauses := []clause.Expression{}
	if param.BusinessID != 0 {
		clauses = append(clauses, clause.Eq{Column: ColumnBusinessID, Value: param.BusinessID})
	}
	if param.CompanyID != 0 {
		clauses = append(clauses, clause.Eq{Column: ColumnCompanyID, Value: param.CompanyID})
	}
	// 时间范围查询 - 使用Gte和Lte组合实现BETWEEN功能
	if !param.StartTime.IsZero() {
		clauses = append(clauses, clause.Gte{Column: ColumnStartTime, Value: param.StartTime})
	}
	if !param.EndTime.IsZero() {
		clauses = append(clauses, clause.Lte{Column: ColumnEndTime, Value: param.EndTime})
	}

	return query.Clauses(clauses...)
}

// applyFilterConditions 应用Filter过滤条件
func (i *impl) applyFilterConditions(query *gorm.DB, filter *Filter) *gorm.DB {
	clauses := []clause.Expression{}

	if len(filter.CareTypes) > 0 {
		clauses = append(clauses, clause.IN{Column: ColumnCareType, Values: convertToInterface(filter.CareTypes)})
	}
	if len(filter.PetIDs) > 0 {
		clauses = append(clauses, clause.IN{Column: ColumnPetID, Values: convertToInterface(filter.PetIDs)})
	}
	if filter.StaffID > 0 {
		clauses = append(clauses, clause.Eq{Column: ColumnStaffID, Value: filter.StaffID})
	}
	if len(filter.States) > 0 {
		clauses = append(clauses, clause.IN{Column: ColumnState, Values: convertToInterface(filter.States)})
	}
	if len(filter.CustomerIDs) > 0 {
		clauses = append(clauses, clause.IN{Column: ColumnCustomerID, Values: convertToInterface(filter.CustomerIDs)})
	}

	if len(clauses) > 0 {
		query = query.Clauses(clauses...)
	}

	return query
}

// convertToInterface 将各种类型的切片转换为[]interface{}
func convertToInterface[T any](slice []T) []interface{} {
	if len(slice) == 0 {
		return nil
	}
	result := make([]interface{}, len(slice))
	for i, v := range slice {
		result[i] = v
	}

	return result
}

func (i *impl) DeleteByServiceInstanceID(ctx context.Context, serviceInstanceID int64) error {
	return i.db.WithContext(ctx).Clauses(clause.Eq{
		Column: ColumnServiceInstanceID,
		Value:  serviceInstanceID,
	}).Delete(&Fulfillment{}).Error
}

func (i *impl) GetByServiceInstanceID(ctx context.Context, serviceInstanceID int64) ([]*Fulfillment, error) {
	var fulfillments []*Fulfillment
	query := i.db.WithContext(ctx).Clauses(clause.Eq{
		Column: ColumnServiceInstanceID,
		Value:  serviceInstanceID,
	})
	if err := query.Find(&fulfillments).Error; err != nil {
		return nil, err
	}

	return fulfillments, nil
}
