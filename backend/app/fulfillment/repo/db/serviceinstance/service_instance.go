package serviceinstance

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
)

type ReadWriter interface {
	Create(ctx context.Context, si *ServiceInstance) (int, error)
	BatchCreate(ctx context.Context, serviceInstances []*ServiceInstance) error
	Update(ctx context.Context, si *ServiceInstance) error
	List(ctx context.Context, baseParam *BaseParam, filter *Filter) ([]*ServiceInstance, error)
	GetByID(ctx context.Context, id int) (*ServiceInstance, error)
	GetByIDs(ctx context.Context, ids []int64) ([]*ServiceInstance, error)
	Delete(ctx context.Context, id int) error
	GetByAppointmentID(ctx context.Context, appointmentID int) ([]*ServiceInstance, error)
}

type impl struct {
	db                 *gorm.DB
	transactionManager db.TransactionManager
}

func New() ReadWriter {
	database := db.GetDB()

	return &impl{
		db:                 database,
		transactionManager: db.NewTxManager(),
	}
}
