package fulfillmentreport

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
)

type SendRecordRepo interface {
	Create(ctx context.Context, record *SendRecord) error
	Update(ctx context.Context, record *SendRecord) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (*SendRecord, error)
	FindByReportIDAndSendMethod(
		ctx context.Context, reportID int64, sendMethod int32) (*SendRecord, error)
	Upsert(ctx context.Context, record *SendRecord) error
	FindByUniqueKeys(ctx context.Context, uniqueKeys []SendRecordUniqueKey) ([]*SendRecord, error)
}

// SendRecordUniqueKey 发送记录唯一键结构
type SendRecordUniqueKey struct {
	ReportID   int64
	SendMethod int32
}

type sendRecordImpl struct {
	db                 *gorm.DB
	transactionManager db.TransactionManager
}

func NewFulfillmentReportSendRecordRepo() SendRecordRepo {
	database := db.GetDB()

	return &sendRecordImpl{
		db:                 database,
		transactionManager: db.NewTxManager(),
	}
}

func (i *sendRecordImpl) Create(ctx context.Context, record *SendRecord) error {
	return i.db.WithContext(ctx).Create(record).Error
}

func (i *sendRecordImpl) Update(ctx context.Context, record *SendRecord) error {
	result := i.db.WithContext(ctx).Save(record)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	return nil
}

func (i *sendRecordImpl) Delete(ctx context.Context, id int64) error {
	result := i.db.WithContext(ctx).Delete(&SendRecord{}, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	return nil
}

func (i *sendRecordImpl) FindByID(ctx context.Context, id int64) (*SendRecord, error) {
	var record SendRecord
	err := i.db.WithContext(ctx).First(&record, id).Error
	if err != nil {
		return nil, err
	}

	return &record, nil
}

func (i *sendRecordImpl) FindByReportIDAndSendMethod(
	ctx context.Context, reportID int64, sendMethod int32) (*SendRecord, error) {
	var record SendRecord
	clauses := []clause.Expression{
		clause.Eq{Column: ColumnSendRecordReportID, Value: reportID},
		clause.Eq{Column: ColumnSendRecordSendMethod, Value: sendMethod},
	}
	err := i.db.WithContext(ctx).
		Clauses(clauses...).
		First(&record).Error
	if err != nil {
		return nil, err
	}

	return &record, nil
}

func (i *sendRecordImpl) Upsert(ctx context.Context, record *SendRecord) error {
	// 尝试根据 report_id + send_method 查找现有记录
	existing, err := i.FindByReportIDAndSendMethod(ctx, record.ReportID, record.SendMethod)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 记录不存在，创建新记录
			return i.Create(ctx, record)
		}

		return err
	}

	// 记录存在，更新现有记录
	record.ID = existing.ID
	record.CreateTime = existing.CreateTime // 保持原始创建时间

	return i.Update(ctx, record)
}

func (i *sendRecordImpl) FindByUniqueKeys(ctx context.Context,
	uniqueKeys []SendRecordUniqueKey) ([]*SendRecord, error) {
	if len(uniqueKeys) == 0 {
		return []*SendRecord{}, nil
	}

	var records []*SendRecord
	query := i.db.WithContext(ctx)

	// 构建 OR 条件查询
	var orClauses []clause.Expression

	for _, uniqueKey := range uniqueKeys {
		var andClauses []clause.Expression
		if uniqueKey.SendMethod != 0 {
			andClauses = []clause.Expression{
				clause.Eq{Column: ColumnSendRecordReportID, Value: uniqueKey.ReportID},
				clause.Eq{Column: ColumnSendRecordSendMethod, Value: uniqueKey.SendMethod},
			}
		} else {
			andClauses = []clause.Expression{
				clause.Eq{Column: ColumnSendRecordReportID, Value: uniqueKey.ReportID},
			}
		}
		orClauses = append(orClauses, clause.And(andClauses...))
	}

	if len(orClauses) > 0 {
		query = query.Clauses(clause.Or(orClauses...))
	}

	// 执行查询
	if err := query.Find(&records).Error; err != nil {
		return nil, fmt.Errorf("failed to query records by unique keys: %w", err)
	}

	return records, nil
}
