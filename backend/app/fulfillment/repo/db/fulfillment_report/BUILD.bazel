load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "fulfillment_report",
    srcs = [
        "entity.go",
        "fulfillment_report.go",
        "fulfillment_report_question.go",
        "fulfillment_report_send_record.go",
        "fulfillment_report_template.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment_report",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
    ],
)
