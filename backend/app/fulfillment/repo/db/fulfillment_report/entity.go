package fulfillmentreport

import (
	"time"
)

const (
	ColumnTemplateID                            = "id"
	ColumnTemplateCompanyID                     = "company_id"
	ColumnTemplateBusinessID                    = "business_id"
	ColumnTemplateCareType                      = "care_type"
	ColumnTemplateThankYouMessage               = "thank_you_message"
	ColumnTemplateThemeColor                    = "theme_color"
	ColumnTemplateLightThemeColor               = "light_theme_color"
	ColumnTemplateShowShowcase                  = "show_showcase"
	ColumnTemplateShowOverallFeedback           = "show_overall_feedback"
	ColumnTemplateShowPetCondition              = "show_pet_condition"
	ColumnTemplateShowServiceStaffName          = "show_service_staff_name"
	ColumnTemplateShowNextAppointment           = "show_next_appointment"
	ColumnTemplateNextAppointmentDateFormatType = "next_appointment_date_format_type"
	ColumnTemplateShowReviewBooster             = "show_review_booster"
	ColumnTemplateShowYelpReview                = "show_yelp_review"
	ColumnTemplateShowGoogleReview              = "show_google_review"
	ColumnTemplateShowFacebookReview            = "show_facebook_review"
	ColumnTemplateLastPublishTime               = "last_publish_time"
	ColumnTemplateTitle                         = "title"
	ColumnTemplateThemeCode                     = "theme_code"
	ColumnTemplateUpdateBy                      = "update_by"
	ColumnTemplateCreateTime                    = "create_time"
	ColumnTemplateUpdateTime                    = "update_time"
)

//nolint:lll
type Template struct {
	ID                            int64     `gorm:"column:id;primaryKey;autoIncrement"`
	CompanyID                     int64     `gorm:"column:company_id;not null;default:0"`
	BusinessID                    int64     `gorm:"column:business_id;not null;uniqueIndex:udx_biz_care"`
	CareType                      int32     `gorm:"column:care_type;not null;default:1;uniqueIndex:udx_biz_care"`
	ThankYouMessage               string    `gorm:"column:thank_you_message;not null;default:'Thanks for your trust and support'"`
	ThemeColor                    string    `gorm:"column:theme_color;not null;default:'#F96B18'"`
	LightThemeColor               string    `gorm:"column:light_theme_color;not null;default:'#FEEFE6'"`
	ShowShowcase                  bool      `gorm:"column:show_showcase;not null;default:true"`
	ShowOverallFeedback           bool      `gorm:"column:show_overall_feedback;not null;default:true"`
	ShowPetCondition              bool      `gorm:"column:show_pet_condition;not null;default:true"`
	ShowServiceStaffName          bool      `gorm:"column:show_service_staff_name;not null;default:false"`
	ShowNextAppointment           bool      `gorm:"column:show_next_appointment;not null;default:true"`
	NextAppointmentDateFormatType int32     `gorm:"column:next_appointment_date_format_type;not null;default:2"`
	ShowReviewBooster             bool      `gorm:"column:show_review_booster;not null;default:true"`
	ShowYelpReview                bool      `gorm:"column:show_yelp_review;not null;default:false"`
	ShowGoogleReview              bool      `gorm:"column:show_google_review;not null;default:false"`
	ShowFacebookReview            bool      `gorm:"column:show_facebook_review;not null;default:false"`
	LastPublishTime               time.Time `gorm:"column:last_publish_time"`
	Title                         string    `gorm:"column:title;not null;default:'Service Report'"`
	ThemeCode                     string    `gorm:"column:theme_code"`
	UpdateBy                      int64     `gorm:"column:update_by;not null;default:0"`
	CreateTime                    time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP"`
	UpdateTime                    time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP"`
}

func (t *Template) TableName() string {
	return "fulfillment_report_template"
}

const (
	ColumnQuestionID                = "id"
	ColumnQuestionCompanyID         = "company_id"
	ColumnQuestionBusinessID        = "business_id"
	ColumnQuestionTemplateID        = "template_id"
	ColumnQuestionCategory          = "category"
	ColumnQuestionType              = "type"
	ColumnQuestionKey               = "key"
	ColumnQuestionTitle             = "title"
	ColumnQuestionExtraJSON         = "extra_json"
	ColumnQuestionIsDefault         = "is_default"
	ColumnQuestionIsRequired        = "is_required"
	ColumnQuestionIsTypeEditable    = "is_type_editable"
	ColumnQuestionIsTitleEditable   = "is_title_editable"
	ColumnQuestionIsOptionsEditable = "is_options_editable"
	ColumnQuestionSort              = "sort"
	ColumnQuestionCreateTime        = "create_time"
	ColumnQuestionUpdateTime        = "update_time"
)

type Question struct {
	ID                int64     `gorm:"column:id;primaryKey;autoIncrement"`
	CompanyID         int64     `gorm:"column:company_id;not null;default:0"`
	BusinessID        int64     `gorm:"column:business_id;not null"`
	TemplateID        int64     `gorm:"column:template_id;not null;index:idx_question_template"`
	Category          int32     `gorm:"column:category;not null;default:1"`
	Type              string    `gorm:"column:type;not null"`
	Key               string    `gorm:"column:key"`
	Title             string    `gorm:"column:title;not null"`
	ExtraJSON         string    `gorm:"column:extra_json;type:jsonb"`
	IsDefault         bool      `gorm:"column:is_default;not null;default:false"`
	IsRequired        bool      `gorm:"column:is_required;not null;default:true"`
	IsTypeEditable    bool      `gorm:"column:is_type_editable;not null;default:true"`
	IsTitleEditable   bool      `gorm:"column:is_title_editable;not null;default:true"`
	IsOptionsEditable bool      `gorm:"column:is_options_editable;not null;default:true"`
	Sort              int32     `gorm:"column:sort;not null;default:0;index:idx_question_template"`
	CreateTime        time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP"`
	UpdateTime        time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP"`
}

func (q *Question) TableName() string {
	return "fulfillment_report_question"
}

const (
	ColumnID              = "id"
	ColumnBusinessID      = "business_id"
	ColumnCompanyID       = "company_id"
	ColumnCustomerID      = "customer_id"
	ColumnAppointmentID   = "appointment_id"
	ColumnPetID           = "pet_id"
	ColumnCareType        = "care_type"
	ColumnServiceDate     = "service_date"
	ColumnUUID            = "uuid"
	ColumnTemplateJSON    = "template_json"
	ColumnContentJSON     = "content_json"
	ColumnStatus          = "status"
	ColumnLinkOpenedCount = "link_opened_count"
	ColumnTemplateVersion = "template_version"
	ColumnThemeCode       = "theme_code"
	ColumnUpdateBy        = "update_by"
	ColumnCreateTime      = "create_time"
	ColumnUpdateTime      = "update_time"
)

type FulfillmentReport struct {
	ID              int64     `gorm:"column:id;primaryKey;autoIncrement"`
	CompanyID       int64     `gorm:"column:company_id;not null;default:0"`
	BusinessID      int64     `gorm:"column:business_id;not null"`
	CustomerID      int64     `gorm:"column:customer_id;not null"`
	AppointmentID   int64     `gorm:"column:appointment_id;not null"`
	CareType        int32     `gorm:"column:care_type;not null;default:1"`
	PetID           int64     `gorm:"column:pet_id;not null"`
	PetTypeID       int64     `gorm:"column:pet_type_id;not null"`
	UUID            string    `gorm:"column:uuid;unique"`
	TemplateVersion time.Time `gorm:"column:template_version"`
	TemplateJSON    string    `gorm:"column:template_json;type:jsonb;default:'{}'"`
	ContentJSON     string    `gorm:"column:content_json;type:jsonb;default:'{}'"`
	Status          string    `gorm:"column:status;not null;default:'draft'"`
	LinkOpenedCount int32     `gorm:"column:link_opened_count;not null;default:0"`
	ServiceDate     time.Time `gorm:"column:service_date;not null;type:date"`
	ThemeCode       string    `gorm:"column:theme_code"`
	UpdateBy        int64     `gorm:"column:update_by;not null;default:0"`
	CreateTime      time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP"`
	UpdateTime      time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP"`
}

func (r *FulfillmentReport) TableName() string {
	return "fulfillment_report"
}

const (
	ColumnSendRecordID            = "id"
	ColumnSendRecordReportID      = "report_id"
	ColumnSendRecordCompanyID     = "company_id"
	ColumnSendRecordBusinessID    = "business_id"
	ColumnSendRecordAppointmentID = "appointment_id"
	ColumnSendRecordContentJSON   = "content_json"
	ColumnSendRecordPetID         = "pet_id"
	ColumnSendRecordSendMethod    = "send_method"
	ColumnSendRecordSentBy        = "sent_by"
	ColumnSendRecordErrorMessage  = "error_message"
	ColumnSendRecordIsSentSuccess = "is_sent_success"
	ColumnSendRecordSentTime      = "sent_time"
	ColumnSendRecordCreateTime    = "create_time"
	ColumnSendRecordUpdateTime    = "update_time"
)

type SendRecord struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement"`
	ReportID      int64     `gorm:"column:report_id;not null"`
	CompanyID     int64     `gorm:"column:company_id;not null;default:0"`
	BusinessID    int64     `gorm:"column:business_id;not null"`
	AppointmentID int64     `gorm:"column:appointment_id;not null"`
	ContentJSON   string    `gorm:"column:content_json;type:jsonb;default:'{}'"`
	PetID         int64     `gorm:"column:pet_id;not null"`
	SendMethod    int32     `gorm:"column:send_method;not null;default:1"`
	SentTime      time.Time `gorm:"column:sent_time"`
	SentBy        int64     `gorm:"column:sent_by;not null;default:0"`
	ErrorMessage  string    `gorm:"column:error_message;not null;default:''"`
	IsSentSuccess bool      `gorm:"column:is_sent_success;not null;default:false"`
	CreateTime    time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP"`
	UpdateTime    time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP"`
}

func (r *SendRecord) TableName() string {
	return "fulfillment_report_send_record"
}
