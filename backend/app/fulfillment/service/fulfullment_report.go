package service

import (
	"context"

	report "github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/report"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

type FulfillmentReportService struct {
	fulfillmentReportLogic                         *report.Logic
	pb.UnimplementedFulfillmentReportServiceServer // TODO: 实现所有接口后删除
}

func NewFulfillmentReportService() *FulfillmentReportService {
	return &FulfillmentReportService{
		fulfillmentReportLogic: report.New(),
	}
}

func (s *FulfillmentReportService) GetFulfillmentReportTemplate(ctx context.Context,
	req *pb.GetFulfillmentReportTemplateRequest) (*pb.GetFulfillmentReportTemplateResponse, error) {
	return s.fulfillmentReportLogic.GetFulfillmentReportTemplate(ctx, req)
}

func (s *FulfillmentReportService) UpdateFulfillmentReportTemplate(ctx context.Context,
	req *pb.UpdateFulfillmentReportTemplateRequest) (*pb.UpdateFulfillmentReportTemplateResponse, error) {
	return s.fulfillmentReportLogic.UpdateFulfillmentReportTemplate(ctx, req)
}

// ------------------------------------------- report card 重构双写------------------------------------------------------

// SyncFulfillmentReportTemplate 同步履约报告模板（双写）
func (s *FulfillmentReportService) SyncFulfillmentReportTemplate(ctx context.Context,
	req *pb.SyncFulfillmentReportTemplateRequest) (*pb.SyncFulfillmentReportTemplateResponse, error) {
	return s.fulfillmentReportLogic.SyncFulfillmentReportTemplate(ctx, req)
}

// BatchSyncFulfillmentReportQuestions 批量同步履约报告模板问题（双写）
func (s *FulfillmentReportService) BatchSyncFulfillmentReportQuestions(ctx context.Context,
	req *pb.BatchSyncFulfillmentReportQuestionsRequest) (*pb.BatchSyncFulfillmentReportQuestionsResponse, error) {
	return s.fulfillmentReportLogic.BatchSyncFulfillmentReportQuestions(ctx, req)
}

// SyncFulfillmentReport 同步履约报告（双写）
func (s *FulfillmentReportService) SyncFulfillmentReport(ctx context.Context,
	req *pb.SyncFulfillmentReportRequest) (*pb.SyncFulfillmentReportResponse, error) {
	return s.fulfillmentReportLogic.SyncFulfillmentReport(ctx, req)
}

// SyncFulfillmentReportSendRecord 同步履约报告发送记录（双写）
func (s *FulfillmentReportService) SyncFulfillmentReportSendRecord(ctx context.Context,
	req *pb.SyncFulfillmentReportSendRecordRequest) (*pb.SyncFulfillmentReportSendRecordResponse, error) {
	return s.fulfillmentReportLogic.SyncFulfillmentReportSendRecord(ctx, req)
}

// BatchMigrateTemplates 批量迁移模板（数据迁移专用）
func (s *FulfillmentReportService) BatchMigrateTemplates(ctx context.Context,
	req *pb.BatchMigrateTemplatesRequest) (*pb.BatchMigrateTemplatesResponse, error) {
	return s.fulfillmentReportLogic.BatchMigrateTemplates(ctx, req)
}

// BatchMigrateQuestions 批量迁移问题（数据迁移专用）
func (s *FulfillmentReportService) BatchMigrateQuestions(ctx context.Context,
	req *pb.BatchMigrateQuestionsRequest) (*pb.BatchMigrateQuestionsResponse, error) {
	return s.fulfillmentReportLogic.BatchMigrateQuestions(ctx, req)
}

// BatchMigrateReports 批量迁移报告（数据迁移专用）
func (s *FulfillmentReportService) BatchMigrateReports(ctx context.Context,
	req *pb.BatchMigrateReportsRequest) (*pb.BatchMigrateReportsResponse, error) {
	return s.fulfillmentReportLogic.BatchMigrateReports(ctx, req)
}

// BatchMigrateRecords 批量迁移发送记录（数据迁移专用）
func (s *FulfillmentReportService) BatchMigrateRecords(ctx context.Context,
	req *pb.BatchMigrateRecordsRequest) (*pb.BatchMigrateRecordsResponse, error) {
	return s.fulfillmentReportLogic.BatchMigrateRecords(ctx, req)
}

// GetTemplatesByUniqueKeys 通过唯一键批量查询模板
func (s *FulfillmentReportService) GetTemplatesByUniqueKeys(ctx context.Context,
	req *pb.GetTemplatesByUniqueKeysRequest) (*pb.GetTemplatesByUniqueKeysResponse, error) {
	return s.fulfillmentReportLogic.GetTemplatesByUniqueKeys(ctx, req)
}

// GetQuestionsByTemplateKeys 通过模板唯一键批量查询问题
func (s *FulfillmentReportService) GetQuestionsByTemplateKeys(ctx context.Context,
	req *pb.GetQuestionsByTemplateKeysRequest) (*pb.GetQuestionsByTemplateKeysResponse, error) {
	return s.fulfillmentReportLogic.GetQuestionsByTemplateKeys(ctx, req)
}

// GetGroomingQuestionsByQuestionKeys 通过问题唯一键批量查询问题（grooming迁移专用）
func (s *FulfillmentReportService) GetGroomingQuestionsByQuestionKeys(ctx context.Context,
	req *pb.GetGroomingQuestionsByQuestionKeysRequest) (*pb.GetGroomingQuestionsByQuestionKeysResponse, error) {
	return s.fulfillmentReportLogic.GetGroomingQuestionsByQuestionKeys(ctx, req)
}

// GetReportsByUniqueKeys 通过唯一键批量查询报告
func (s *FulfillmentReportService) GetReportsByUniqueKeys(ctx context.Context,
	req *pb.GetReportsByUniqueKeysRequest) (*pb.GetReportsByUniqueKeysResponse, error) {
	return s.fulfillmentReportLogic.GetReportsByUniqueKeys(ctx, req)
}

// GetRecordsByReportKeys 通过报告唯一键批量查询发送记录
func (s *FulfillmentReportService) GetRecordsByReportKeys(ctx context.Context,
	req *pb.GetRecordsByReportKeysRequest) (*pb.GetRecordsByReportKeysResponse, error) {
	return s.fulfillmentReportLogic.GetRecordsByReportKeys(ctx, req)
}
