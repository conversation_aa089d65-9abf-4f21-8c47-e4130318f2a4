package service

import (
	"context"

	inner "github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/inner"
)

type FulfillmentInnerService struct {
	inner *inner.Logic
}

func NewFulfillmentInnerService() *FulfillmentInnerService {
	return &FulfillmentInnerService{
		inner: inner.New(),
	}
}

func (s *FulfillmentInnerService) GetGroomingDetailByAppointmentID(ctx context.Context,
	req *inner.GetGroomingDetailByAppointmentIDRequest) (
	*inner.GetGroomingDetailByAppointmentIDResponse, error) {
	return s.inner.GetGroomingDetailByAppointmentID(ctx, req)
}
