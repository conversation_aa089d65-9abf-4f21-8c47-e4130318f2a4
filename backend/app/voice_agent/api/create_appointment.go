package api

import (
	"context"
	"log"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/protobuf/proto"

	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	appointmentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1"
)

type CreateAppointmentRequest struct {
	CompanyID  int64
	BusinessID int64
	CustomerID int64
	PetID      int64
	ServiceID  int64
	StartDate  string
	StartTime  int32
	StaffID    int64
}

type CreateAppointmentResponse struct {
	AppointmentID int64 `json:"appointmentId"`
}

func CreateAppointment(ctx context.Context, req CreateAppointmentRequest) (CreateAppointmentResponse, error) {
	log.Println("create appointment", req)
	conn, err := grpc.NewClient("moego-svc-appointment:9090", grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return CreateAppointmentResponse{}, err
	}
	defer conn.Close()

	c := appointmentsvcpb.NewAppointmentServiceClient(conn)
	resp, err := c.CreateAppointment(ctx, &appointmentsvcpb.CreateAppointmentRequest{
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: req.CustomerID,
			Source:     appointmentpb.AppointmentSource_OPEN_API,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: req.PetID,
				Services: []*appointmentpb.SelectedServiceDef{
					{
						DateType:  appointmentpb.PetDetailDateType_PET_DETAIL_DATE_DATE_POINT.Enum(),
						ServiceId: req.ServiceID,
						StartDate: req.StartDate,
						StartTime: proto.Int32(req.StartTime),
						StaffId:   proto.Int64(req.StaffID),
					},
				},
			},
		},
		CompanyId:  req.CompanyID,
		BusinessId: req.BusinessID,
		StaffId:    req.StaffID,
	})
	if err != nil {
		return CreateAppointmentResponse{}, err
	}

	return CreateAppointmentResponse{
		AppointmentID: resp.GetAppointmentId(),
	}, nil
}
