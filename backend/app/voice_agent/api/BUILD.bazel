load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "api",
    srcs = [
        "create_appointment.go",
        "get_pet.go",
        "get_service.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/voice_agent/api",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/offering/v1:offering",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/offering/v1:offering",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/utils/v2:utils",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials/insecure",
        "@org_golang_google_protobuf//proto",
    ],
)
