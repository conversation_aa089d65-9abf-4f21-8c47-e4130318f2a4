package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"

	"github.com/gorilla/websocket"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	"github.com/MoeGolibrary/moego/backend/app/voice_agent/api"
	"github.com/MoeGolibrary/moego/backend/app/voice_agent/entity/twilio"
	"github.com/MoeGolibrary/moego/backend/app/voice_agent/logic/agent/converter"
	"github.com/MoeGolibrary/moego/backend/app/voice_agent/repo/customer"
	"github.com/MoeGolibrary/moego/backend/app/voice_agent/repo/openai"
)

type Agent struct {
	openaiConn *websocket.Conn
	twilioConn *websocket.Conn

	staticCtx *StaticContext
}

type StaticContext struct {
	CompanyID    int64
	BusinessInfo *organizationpb.LocationModel
	Customer     *customer.Customer

	StreamSid string
}

func New(twilioConn *websocket.Conn,
	staticCtx *StaticContext) *Agent {
	return &Agent{
		twilioConn: twilioConn,
		openaiConn: openai.NewConn(),

		staticCtx: staticCtx,
	}
}

func (a *Agent) Process(ctx context.Context) {
	a.init(ctx)

	// 让 agent 先打招呼
	a.CreateResponse()

	wg := sync.WaitGroup{}

	wg.Add(1)
	go func() {
		defer wg.Done()
		a.readFromTwilio()
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		a.readRealtime()
	}()

	wg.Wait()
}

func (a *Agent) init(ctx context.Context) error {
	instructions, err := a.buildInstructions(ctx)
	if err != nil {
		log.Println("build instructions failed", err)
		return err
	}

	if err := a.openaiConn.WriteJSON(map[string]any{
		"type": "session.update",
		"session": map[string]any{
			"instructions":        instructions,
			"input_audio_format":  "g711_ulaw",
			"output_audio_format": "g711_ulaw",
			"tools":               getAvailableTools(),
			"turn_detection": map[string]any{
				"type": "server_vad",
			},
			"speed": 1.1,
			"voice": "sage",
		},
	}); err != nil {
		log.Println("write session update failed", err)
		return err
	}

	return nil
}

func (a *Agent) buildInstructions(ctx context.Context) (string, error) {
	services, err := api.GetServiceList(ctx, &api.GetServiceListRequest{
		CompanyID: a.staticCtx.CompanyID,
	})
	if err != nil {
		return "", fmt.Errorf("get service list failed: %w", err)
	}
	servicesStr, err := json.Marshal(services)
	if err != nil {
		return "", fmt.Errorf("marshal service list failed: %w", err)
	}

	petList, err := api.GetPetList(ctx, a.staticCtx.Customer.ID)
	if err != nil {
		return "", fmt.Errorf("get pet list failed: %w", err)
	}
	petListStr, err := json.Marshal(petList)
	if err != nil {
		return "", fmt.Errorf("marshal pet list failed: %w", err)
	}

	instruction, err := BuildInstruction(
		converter.ConvertBusiness(a.staticCtx.BusinessInfo),
		string(servicesStr),
		converter.ConvertCustomer(a.staticCtx.Customer),
		string(petListStr),
	)
	if err != nil {
		return "", fmt.Errorf("build instruction failed: %w", err)
	}
	return instruction, nil
}

func (a *Agent) CreateResponse() error {
	return a.openaiConn.WriteJSON(map[string]any{
		"type": "response.create",
	})
}

func (a *Agent) Close() {
	a.openaiConn.Close()
	a.twilioConn.Close()
}

func (a *Agent) readFromTwilio() {
	for {
		messageType, message, err := a.twilioConn.ReadMessage()
		if err != nil {
			log.Println("read message failed", err)
			break
		}

		if messageType != websocket.TextMessage {
			log.Println("message type is not text message")
			continue
		}

		var twilioEvent twilio.TwilioEvent
		if err := json.Unmarshal(message, &twilioEvent); err != nil {
			log.Println("unmarshal message failed", err)
			break
		}

		if twilioEvent.Event == "media" {
			a.openaiConn.WriteJSON(map[string]any{
				"type":  "input_audio_buffer.append",
				"audio": twilioEvent.Media.Payload,
			})
		} else if twilioEvent.Event == "stop" {
			break
		} else {
			log.Println("unsupported twilio event", string(message))
		}
	}

	a.Close()
	log.Println("read from twilio done")
}

func (a *Agent) readRealtime() {
	for {
		messageType, message, err := a.openaiConn.ReadMessage()
		if err != nil {
			log.Println("read realtime message failed", err)
			break
		}

		if messageType != websocket.TextMessage {
			log.Println("message type is not text message")
			continue
		}

		var realtimeEvent RealtimeEvent
		if err := json.Unmarshal(message, &realtimeEvent); err != nil {
			log.Println("unmarshal realtime message failed", err)
			continue
		}

		switch realtimeEvent.Type {
		case "response.audio.delta":
			a.twilioConn.WriteJSON(map[string]any{
				"event":     "media",
				"streamSid": a.staticCtx.StreamSid,
				"media": map[string]any{
					"payload": realtimeEvent.Delta,
				},
			})
		case "session.created":
			// log.Println("session.created", string(message))
		case "response.audio_transcript.done":
			// log.Println("response.audio_transcript.done", string(message))
		case "response.audio_transcript.delta":
			// log.Println("response.audio_transcript.delta", string(message))
		case "response.created":
			log.Println(string(message))
		case "response.output_item.added":
			// log.Println("response.output_item.added", string(message))
		case "response.done":
			for _, output := range realtimeEvent.Response.Output {
				if output.Type == "function_call" {
					if result, err := a.doFunctionCall(context.Background(), output.Name, output.Arguments); err != nil {
						log.Println("do function call failed", err)
					} else {
						a.openaiConn.WriteJSON(map[string]any{
							"type": "conversation.item.create",
							"item": map[string]any{
								"type":    "function_call_output",
								"call_id": output.CallID,
								"output":  result,
							},
						})
						a.CreateResponse()
					}
				}
			}
		default:
			// log.Println("unsupported realtime event", string(message))
		}
	}
}

func (a *Agent) doFunctionCall(ctx context.Context, name string, arguments string) (string, error) {
	switch name {
	case "create_appointment":
		return a.createAppointment(ctx, arguments)
	case "check_availability":
		return "8:00 am ~ 6:00pm", nil
	case "get_grooming_status":
		return "In Progress", nil
	}
	return "", fmt.Errorf("unknown function call: %s", name)
}

func (c *Agent) createAppointment(ctx context.Context, arguments string) (string, error) {
	type Parms struct {
		PetID     int64  `json:"pet_id"`
		ServiceID int64  `json:"service_id"`
		StartDate string `json:"start_date"`
		StartTime int32  `json:"start_time"`
	}

	var params Parms
	if err := json.Unmarshal([]byte(arguments), &params); err != nil {
		return "", fmt.Errorf("unmarshal arguments failed: %w", err)
	}

	resp, err := api.CreateAppointment(ctx, api.CreateAppointmentRequest{
		CompanyID:  c.staticCtx.CompanyID,
		BusinessID: c.staticCtx.BusinessInfo.Id,
		CustomerID: c.staticCtx.Customer.ID,
		PetID:      params.PetID,
		ServiceID:  params.ServiceID,
		StartDate:  params.StartDate,
		StartTime:  params.StartTime,
		StaffID:    142031, // TODO
	})
	if err != nil {
		return "", fmt.Errorf("create appointment failed: %w", err)
	}
	return fmt.Sprintf("Appointment created successfully: %d", resp.AppointmentID), nil
}
