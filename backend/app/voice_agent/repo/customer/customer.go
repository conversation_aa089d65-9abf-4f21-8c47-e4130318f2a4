package customer

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
)

type Client interface {
	GetByPhoneNumber(ctx context.Context, businessID int64, phoneNumber string) (*Customer, error)
}

func New() Client {
	return &impl{
		client: http.DefaultClient,
	}
}

type impl struct {
	client *http.Client
}

const serviceName = "http://moego-service-customer:9201"

func (i *impl) GetByPhoneNumber(ctx context.Context, businessID int64, phoneNumber string) (*Customer, error) {
	path := "/service/customer/customer/getCustomerInfoByPhoneNumberForNewOB"

	params := url.Values{}
	params.Set("businessId", strconv.FormatInt(businessID, 10))
	params.Set("phoneNumber", phoneNumber)

	requestURL := fmt.Sprintf("%s?%s", fmt.Sprintf("%s%s", serviceName, path), params.Encode())

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, requestURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := i.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}
	customerInfo := &Customer{}
	if err := json.NewDecoder(resp.Body).Decode(customerInfo); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return customerInfo, nil
}
