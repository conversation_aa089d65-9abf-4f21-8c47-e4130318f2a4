load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "aistudio_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/aistudio",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/aistudio/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "//backend/proto/aistudio/v1:aistudio",
    ],
)

go_binary(
    name = "aistudio",
    embed = [":aistudio_lib"],
    visibility = ["//visibility:public"],
)
