package service

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

// GetAiStudioTask retrieves an AI Studio task.
func (s *AiStudioService) GetAiStudioTask(ctx context.Context,
	req *toolspb.GetAiStudioTaskRequest) (*toolspb.AiStudioTask, error) {
	repo := aistudio.New()
	task, err := repo.GetTask(ctx, req.Id)
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	if task == nil {
		return nil, fmt.Errorf("task not found with id: %d", req.Id)
	}

	return &toolspb.AiStudioTask{
		Status: 0,
		Msg:    "Success",
		Data: &toolspb.AiStudioTaskData{
			Id:         task.ID,
			Task:       task.TaskName,
			TemplateId: task.TemplateID,
			Envs:       task.Envs,
			Ai<PERSON>ey:      task.AIKey,
			ImChannel:  task.IMChannel,
			Spec:       task.Spec,
		},
	}, nil
}
