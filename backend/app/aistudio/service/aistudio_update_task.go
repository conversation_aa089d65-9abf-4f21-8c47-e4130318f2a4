package service

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

// UpdateAiStudioTask updates an AI Studio task.
func (s *AiStudioService) UpdateAiStudioTask(ctx context.Context,
	req *toolspb.UpdateAiStudioTaskRequest) (*toolspb.AiStudioTask, error) {
	repo := aistudio.New()
	task, err := repo.GetTask(ctx, req.Id)
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	if task == nil {
		return nil, fmt.Errorf("task not found with id: %d", req.Id)
	}

	task.TaskName = req.Task
	task.TemplateID = req.TemplateId
	task.Envs = req.Envs
	task.AIKey = req.AiKey
	task.IMChannel = req.ImChannel
	task.Spec = req.Spec

	err = repo.UpdateTask(ctx, task)
	if err != nil {
		return nil, fmt.Errorf("failed to update task: %w", err)
	}

	s.addCronJob(req.Spec, task)

	return &toolspb.AiStudioTask{
		Status: 0,
		Msg:    "Success",
		Data: &toolspb.AiStudioTaskData{
			Id:         task.ID,
			Task:       task.TaskName,
			TemplateId: task.TemplateID,
			Envs:       task.Envs,
			AiKey:      task.AIKey,
			ImChannel:  task.IMChannel,
			Spec:       task.Spec,
		},
	}, nil
}
