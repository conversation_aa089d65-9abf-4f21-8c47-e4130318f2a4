package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

func (s *AiStudioService) UpdateAiStudioTemplate(ctx context.Context,
	req *toolspb.UpdateAiStudioTemplateRequest) (*toolspb.AiStudioTemplate, error) {
	id := req.Id
	model := req.Model
	prompt := req.Prompt
	mcps := req.Mcps
	envKeys := req.EnvKeys

	log.InfoContextf(ctx, "UpdateAiStudioTemplate id: %d, model: %s, prompt: %s, mcpList: %s, env: %s",
		id, model, prompt, mcps, envKeys)

	repo := aistudio.New()
	willUpdate, err := repo.GetTemplate(ctx, id)
	if err != nil {
		log.ErrorContextf(ctx, "UpdateAiStudioTemplate repo.Get error: %v", err)

		return &toolspb.AiStudioTemplate{
			Status: 1,
			Msg:    err.Error(),
		}, nil
	}

	willUpdate.Model = model
	willUpdate.Prompt = prompt
	willUpdate.Mcps = mcps
	willUpdate.EnvKeys = envKeys
	willUpdate.Name = req.Name

	err = repo.UpdateTemplate(ctx, willUpdate)
	if err != nil {
		log.ErrorContextf(ctx, "UpdateAiStudioTemplate repo.Update error: %v", err)

		return &toolspb.AiStudioTemplate{
			Status: 1,
			Msg:    err.Error(),
		}, nil
	}

	return &toolspb.AiStudioTemplate{
		Status: 0,
		Msg:    "Success",
		Data: &toolspb.AiStudioTemplateData{
			Id:      willUpdate.ID,
			Model:   willUpdate.Model,
			Prompt:  willUpdate.Prompt,
			Mcps:    willUpdate.Mcps,
			EnvKeys: willUpdate.EnvKeys,
			Name:    willUpdate.Name,
		},
	}, nil
}
