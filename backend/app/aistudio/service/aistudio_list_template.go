package service

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

// ListAiStudioTemplate implements tools.AIStudioServiceServer.
func (s *AiStudioService) ListAiStudioTemplate(ctx context.Context,
	req *toolspb.ListAiStudioTemplateRequest) (*toolspb.ListAiStudioTemplateResponse, error) {
	log.InfoContextf(ctx, "ListAiStudioTemplate called")

	repo := aistudio.New()

	templates, total, err := repo.ListTemplate(ctx, req.PerPage, req.Page)
	if err != nil {
		return &toolspb.ListAiStudioTemplateResponse{
			Status: 1,
			Msg:    fmt.Sprintf("Failed to list AI Studio templates: %v", err),
		}, nil
	}

	var items []*toolspb.AiStudioTemplateData
	for _, template := range templates {
		items = append(items, &toolspb.AiStudioTemplateData{
			Id:      template.ID,
			Model:   template.Model,
			Prompt:  template.Prompt,
			Mcps:    template.Mcps,
			EnvKeys: template.EnvKeys,
			Name:    template.Name,
		})
	}

	return &toolspb.ListAiStudioTemplateResponse{
		Status: 0,
		Msg:    "Successfully listed AI Studio templates",
		Data: &toolspb.ListAiStudioTemplateData{
			Items: items,
			Total: int32(total),
		},
	}, nil
}
