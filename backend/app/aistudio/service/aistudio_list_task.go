package service

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

// ListAiStudioTask lists AI Studio tasks.
func (s *AiStudioService) ListAiStudioTask(ctx context.Context,
	req *toolspb.ListAiStudioTaskRequest) (*toolspb.ListAiStudioTaskResponse, error) {
	repo := aistudio.New()
	tasks, total, err := repo.ListTasks(ctx, req.PerPage, req.Page)
	if err != nil {
		return nil, fmt.Errorf("failed to list tasks: %w", err)
	}

	var pbTasks []*toolspb.AiStudioTaskData
	for _, task := range tasks {
		pbTasks = append(pbTasks, &toolspb.AiStudioTaskData{
			Id:         task.ID,
			Task:       task.TaskName,
			TemplateId: task.TemplateID,
			Envs:       task.Envs,
			AiKey:      task.AIKey,
			ImChannel:  task.IMChannel,
			Spec:       task.Spec,
		})
	}

	return &toolspb.ListAiStudioTaskResponse{
		Status: 0,
		Msg:    "Success",
		Data: &toolspb.ListAiStudioTaskData{
			Items: pbTasks,
			Total: total,
		},
	}, nil
}
