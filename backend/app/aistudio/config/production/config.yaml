secrets:
  - name: "moego/testing/datasource"
    prefix: "secret.datasource."
server:
  filter:
    - recovery
    - debuglog
  service:
    - name: backend.proto.aistudio.v1.AIStudioService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
client:
  network: tcp
  protocol: grpc
  filter:
    - debuglog
  transport: grpc
  timeout: 600000
  service:
    - callee: postgres
      target: dsn://postgresql://${secret.datasource.postgres.moego_tools.username}:${secret.datasource.postgres.moego_tools.password}@${secret.datasource.postgres.url.master}:${secret.datasource.postgres.port}/moego_aistudio?sslmode=disable
      # target: dsn://postgresql://moego_developer_240310_eff7a0dc:<EMAIL>:40132/moego_tools?sslmode=disable
      protocol: gorm
      transport: gorm
      timeout: 600000
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
