package aistudio

import (
	"sync"

	"github.com/samber/lo"
)

type MCPConfigBuilder struct {
}

var onceReg sync.Once

func init() {
	onceReg.Do(func() {
		RegisterMCPEnv("mcp-atlassian", []string{
			"jira-username",
			"jira-token",
		})
		RegisterMCPEnv("mcp-slack", []string{
			"slack-bot-token",
		})
		RegisterMCPEnv("datadog", []string{
			"datadog-api-key",
			"datadog-app-key",
		})
	})
}

func NewMCPConfigBuilder() *MCPConfigBuilder {

	return &MCPConfigBuilder{}
}

var mcpEnvs = map[string]map[string]struct{}{}

var mcpEnvsLk = sync.Mutex{}

func RegisterMCPEnv(mcpName string, envs []string) {
	mcpEnvsLk.Lock()
	defer mcpEnvsLk.Unlock()
	_, ok := mcpEnvs[mcpName]
	if !ok {
		mcpEnvs[mcpName] = map[string]struct{}{}
	}
	for _, env := range envs {
		mcpEnvs[mcpName][env] = struct{}{}
	}
}

func LoadMCPEnvs() (ret []string) {
	mcpEnvsLk.Lock()
	defer mcpEnvsLk.Unlock()

	for _, mcpKeys := range mcpEnvs {
		ret = append(ret, lo.Keys(mcpKeys)...)
	}

	return
}

func LoadMCPEnvsByMCPName(mcpName string) (ret []string) {
	mcpEnvsLk.Lock()
	defer mcpEnvsLk.Unlock()
	v, ok := mcpEnvs[mcpName]
	if ok {
		return lo.Keys(v)
	}

	return []string{}
}

func (m *MCPConfigBuilder) Build(mcpName string, replacement map[string]string) *MCPConfig {
	switch mcpName {
	case "mcp-atlassian":
		return m.buildMCPAtlassian(replacement)
	case "mcp-slack":
		return m.buildMCPSlack(replacement)
	case "datadog":
		return m.buildDatadog(replacement)
	case "unix_timestamps_mcp":
		return m.buildUnixTimestamps(replacement)
	default:
		return nil
	}
}

func (m *MCPConfigBuilder) buildMCPAtlassian(replacement map[string]string) *MCPConfig {
	return NewMCPConfig(
		"mcp-atlassian",
		"uvx",
		"stdio",
		[]string{
			"mcp-atlassian",
			"--jira-url=https://moego.atlassian.net",
			"--jira-username={{jira-username}}",
			"--jira-token={{jira-token}}",
		},
		map[string]string{},
		replacement,
	)
}

func (m *MCPConfigBuilder) buildMCPSlack(replacement map[string]string) *MCPConfig {
	return NewMCPConfig(
		"slack",
		"npx",
		"stdio",
		[]string{
			"-y",
			"@modelcontextprotocol/server-slack",
		},
		map[string]string{
			"SLACK_BOT_TOKEN": "{{slack-bot-token}}",
			"SLACK_TEAM_ID":   "T011CF3CMJN",
		},
		replacement,
	)
}

func (m *MCPConfigBuilder) buildDatadog(replacement map[string]string) *MCPConfig {
	return NewMCPConfig(
		"datadog",
		"npx",
		"stdio",
		[]string{
			"-y",
			"@winor30/mcp-server-datadog",
		},
		map[string]string{
			"DATADOG_API_KEY": "{{datadog-api-key}}",
			"DATADOG_APP_KEY": "{{datadog-app-key}}",
			"DATADOG_SITE":    "us5.datadoghq.com",
		},
		replacement,
	)
}

func (m *MCPConfigBuilder) buildUnixTimestamps(replacement map[string]string) *MCPConfig {
	return NewMCPConfig(
		"unix_timestamps_mcp",
		"npx",
		"stdio",
		[]string{
			"-y",
			"github:Ivor/unix-timestamps-mcp",
		},
		map[string]string{},
		replacement,
	)
}
