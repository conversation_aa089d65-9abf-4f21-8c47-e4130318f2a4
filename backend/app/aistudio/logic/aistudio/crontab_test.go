package aistudio

import "testing"

// TestStringIDCron 测试使用字符串ID的cron任务
func TestStringIDCron(t *testing.T) {
	c := NewStringIDCron()

	// 启动调度器
	c.Start()
	defer c.Stop()

	// 添加一个带有字符串ID的任务
	err := c.AddFunc("id_task_my_log", "@every 1s", func() {
		// 任务逻辑
	})

	if err != nil {
		t.Fatalf("添加任务失败: %v", err)
	}

	// 验证任务是否被添加
	if _, exists := c.idMapper["id_task_my_log"]; !exists {
		t.<PERSON><PERSON><PERSON>("期望任务被添加，但未找到")
	}

	// 测试Entries方法
	entries := c.Entries()
	if len(entries) != 1 {
		t.<PERSON>rrorf("期望有1个任务条目，但得到%d个", len(entries))
	}

	// 测试移除任务
	removed := c.<PERSON>move("id_task_my_log")
	if !removed {
		t.<PERSON><PERSON>rf("期望任务被移除，但移除失败")
	}

	// 验证任务已被移除
	entries = c.Entries()
	if len(entries) != 0 {
		t.<PERSON><PERSON><PERSON>("期望任务被移除后没有条目，但得到%d个", len(entries))
	}

	// 测试移除不存在的任务
	removed = c.Remove("non_existent_id")
	if removed {
		t.Errorf("期望移除不存在的任务返回false，但返回了true")
	}
}
