package aistudio

import (
	"encoding/json"
	"strings"
)

// NewMCPConfig creates a new MCPConfig instance.
// It replaces variables in command, args, and env using the replacement map.
func NewMCPConfig(mcpName, command, transportType string,
	args []string, env map[string]string, replacement map[string]string) *MCPConfig {

	config := &MCPConfig{
		mcpName:       mcpName,
		command:       command,
		transportType: transportType,
		args:          args,
		env:           env,
		replacement:   replacement,
	}

	config.command = ReplaceVariables(config.command, replacement)
	for i := range config.args {
		config.args[i] = ReplaceVariables(config.args[i], replacement)
	}
	for key, value := range config.env {
		config.env[key] = ReplaceVariables(value, replacement)
	}

	return config
}

type MCPConfig struct {
	mcpName       string
	command       string
	transportType string
	args          []string
	env           map[string]string
	replacement   map[string]string
}

func (m *MCPConfig) GetMCPName() string {
	return m.mcpName
}

// String returns the JSON string representation of the MCPConfig.
func (m *MCPConfig) String() string {
	tp := McpConfigTemplate{
		Timeout:       60,
		Command:       m.command,
		Args:          m.args,
		Env:           m.env,
		TransportType: m.transportType,
		// AutoApprove:   []string{"true"},
	}
	v, err := json.Marshal(tp)
	if err != nil {
		return ""
	}

	return string(v)
}

// MarshalJSON implements the Marshaler interface for MCPConfig.
func (m *MCPConfig) MarshalJSON() ([]byte, error) {
	return []byte(m.String()), nil
}

type McpConfigTemplate struct {
	Timeout       int               `json:"timeout"`
	Command       string            `json:"command"`
	Args          []string          `json:"args"`
	Env           map[string]string `json:"env"`
	TransportType string            `json:"transportType"`
	// AutoApprove   []string `json:"autoApprove"`
}

// ReplaceVariables replaces variables in a string using the replacement map.
// Variables are in the format {{key}} and are replaced with replacement[key].
// If the key is not found in the replacement map, the variable is replaced with an empty string.
func ReplaceVariables(s string, replacement map[string]string) string {
	for key, value := range replacement {
		s = strings.ReplaceAll(s, "{{"+key+"}}", value)
	}

	return s
}
