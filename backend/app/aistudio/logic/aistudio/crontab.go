package aistudio

import (
	"sync"
	"time"

	cron "github.com/robfig/cron/v3"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

const (
	// CronSpecDaily10AM 每天10点执行 (东8区)
	CronSpecDaily10AM = "0 10 * * *"
	// CronSpecDaily6PM 每天18点执行 (东8区)
	CronSpecDaily6PM = "0 18 * * *"
	// CronSpecEvery30Min 每30分钟执行
	CronSpecEvery30Min = "*/30 * * * *"
	// CronSpecEvery5Min 每5分钟执行
	CronSpecEvery5Min = "*/5 * * * *"
	// CronSpecDaily3PM 每天15点执行 (东8区)
	CronSpecDaily3PM = "0 15 * * *"
	// CronSpecWeeklyFriday10AM 每周五上午10点执行 (东8区)
	CronSpecWeeklyFriday10AM = "0 10 * * 5"
)

// StringIDCron 是一个包装器，允许使用字符串ID来管理cron任务
type StringIDCron struct {
	cron     *cron.Cron
	idMapper map[string]cron.EntryID
	mu       sync.RWMutex
}

// NewStringIDCron 创建一个新的StringIDCron实例
func NewStringIDCron() *StringIDCron {
	// 尝试加载"Asia/Shanghai"时区，如果失败，则使用固定的UTC+8时区。
	// 这样做可以避免在没有时区数据库的Docker镜像中运行时出现问题。
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.Fatalf("Failed to load Asia/Shanghai timezone: %v", err)
	}

	return &StringIDCron{
		cron:     cron.New(cron.WithLocation(loc)),
		idMapper: make(map[string]cron.EntryID),
		mu:       sync.RWMutex{},
	}
}

// AddFunc 添加一个带有字符串ID的定时任务
func (s *StringIDCron) AddFunc(stringID string, spec string, cmd func()) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 检查ID是否已存在，如果存在则先移除旧任务
	if oldID, exists := s.idMapper[stringID]; exists {
		s.cron.Remove(oldID)
	}

	id, err := s.cron.AddFunc(spec, cmd)
	if err != nil {
		return err
	}
	s.idMapper[stringID] = id

	return nil
}

// Remove 通过字符串ID移除一个定时任务
func (s *StringIDCron) Remove(stringID string) bool {
	s.mu.Lock()
	defer s.mu.Unlock()

	id, exists := s.idMapper[stringID]
	if !exists {
		return false
	}
	s.cron.Remove(id)
	delete(s.idMapper, stringID)

	return true
}

// Start 启动cron调度器
func (s *StringIDCron) Start() {
	s.cron.Start()
}

// Stop 停止cron调度器
func (s *StringIDCron) Stop() {
	s.cron.Stop()
}

// Entries 返回所有已注册的任务条目
func (s *StringIDCron) Entries() []cron.Entry {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return s.cron.Entries()
}
