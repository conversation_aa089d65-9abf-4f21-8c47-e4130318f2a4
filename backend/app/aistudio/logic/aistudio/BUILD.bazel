load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "aistudio",
    srcs = [
        "crontab.go",
        "mcp_builder.go",
        "mcp_client.go",
        "mcp_config.go",
        "mcp_request_builder.go",
        "task_runner.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/aistudio/logic/aistudio",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/aistudio/repo/aistudio",
        "//backend/app/aistudio/repo/aistudio/entity",
        "//backend/common/rpc/framework/log",
        "@com_github_robfig_cron_v3//:cron",
        "@com_github_samber_lo//:lo",
    ],
)

go_test(
    name = "aistudio_test",
    srcs = [
        "crontab_test.go",
        "mcp_builder_test.go",
        "mcp_client_test.go",
        "mcp_config_test.go",
        "task_runner_test.go",
    ],
    embed = [":aistudio"],
    deps = [
        "//backend/app/aistudio/repo/aistudio/entity",
        "//backend/app/aistudio/repo/aistudio/mocks",
        "@com_github_stretchr_testify//mock",
    ],
)
