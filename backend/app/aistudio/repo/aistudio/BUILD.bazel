load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "aistudio",
    srcs = ["aistudio.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/aistudio/repo/aistudio/entity",
        "//backend/common/rpc/database/gorm",
        "@io_gorm_gorm//:gorm",
    ],
)

go_test(
    name = "aistudio_test",
    srcs = ["aistudio_test.go"],
    embed = [":aistudio"],
    deps = [
        "//backend/app/aistudio/repo/aistudio/entity",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/framework",
        "@com_github_samber_lo//:lo",
        "@com_github_stretchr_testify//suite",
    ],
)
