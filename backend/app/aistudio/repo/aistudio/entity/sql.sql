CREATE TABLE aistudio_template (
    id SERIAL PRIMARY KEY,
    model TEXT,
    prompt TEXT,
    mcps TEXT,
    env_keys TEXT,
    name TEXT
);

ALTER TABLE public.aistudio_template
ADD CONSTRAINT unique_aistudio_template_name UNIQUE (name);

CREATE TABLE aistudio_task (
    id SERIAL PRIMARY KEY,
    task_name TEXT,
    template_id INTEGER,
    ai_key TEXT,
    envs TEXT,
    im_channel TEXT,
    spec TEXT not null default ""
);

ALTER TABLE public.aistudio_task
ADD CONSTRAINT unique_aistudio_task_name UNIQUE (task_name);

CREATE TABLE aistudio_task_log (
    id SERIAL PRIMARY KEY,
    task_id INTEGER,
    prompt TEXT,
    envs TEXT,
    dialogues TEXT,
    create_time TIMESTAMP WITH TIME ZONE
);

CREATE TABLE aistudio_cron_job (
    id SERIAL PRIMARY KEY,
    job_id TEXT NOT NULL,
    task_id INTEGER,
    last_run_time TIMESTAMP WITH TIME ZONE,
    cron_spec TEXT
);

ALTER TABLE public.aistudio_cron_job
ADD CONSTRAINT unique_aistudio_cron_job_id UNIQUE (job_id);