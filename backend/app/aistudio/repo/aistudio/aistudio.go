package aistudio

import (
	"context"
	"sync"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio/entity"
	igorm "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
)

type ReadWriter interface {
	CreateTemplate(ctx context.Context, template *entity.AiStudioTemplate) error
	GetTemplate(ctx context.Context, id int64) (*entity.AiStudioTemplate, error)
	ListTemplate(ctx context.Context,
		pageSize int32, currentPage int32) (items []*entity.AiStudioTemplate, total int32, err error)
	UpdateTemplate(ctx context.Context, template *entity.AiStudioTemplate) error
	DeleteTemplate(ctx context.Context, id int64) error

	CreateTask(ctx context.Context, task *entity.AiStudioTask) error
	GetTask(ctx context.Context, id int64) (*entity.AiStudioTask, error)
	GetTasks(ctx context.Context, searchBean *entity.AiStudioTask) ([]*entity.AiStudioTask, error)
	GetTaskByName(ctx context.Context, name string) (*entity.AiStudioTask, error)
	UpdateTask(ctx context.Context, task *entity.AiStudioTask) error
	DeleteTask(ctx context.Context, id int64) error
	ListTasks(ctx context.Context,
		pageSize int32, currentPage int32) (items []*entity.AiStudioTask, total int32, err error)

	CreateTaskLog(ctx context.Context, taskLog *entity.AiStudioTaskLog) error
	GetTaskLog(ctx context.Context, id int64) (*entity.AiStudioTaskLog, error)
	DeleteTaskLog(ctx context.Context, id int64) error
	ListTaskLogs(ctx context.Context,
		pageSize int32, currentPage int32) (items []*entity.AiStudioTaskLog, total int32, err error)

	CreateAiStudioCronJob(ctx context.Context, cronJob *entity.AiStudioCronJob) error
	GetAiStudioCronJob(ctx context.Context, jobID string) (*entity.AiStudioCronJob, error)
	ListAiStudioCronJobs(ctx context.Context, pageSize int32,
		currentPage int32) (items []*entity.AiStudioCronJob, total int32, err error)
}

type impl struct {
	// 这里可以添加数据库连接或其他依赖
	db *gorm.DB
}

var globalDB *gorm.DB

var initDB sync.Once

func New() ReadWriter {
	// 这个名字是自定义的，要和config.yaml中的数据库配置名字一致
	// 得到的 db 是一个原生的 *gorm.DB
	initDB.Do(func() {
		db, err := igorm.NewClientProxy("postgres")
		if err != nil {
			panic(err)
		}
		globalDB = db
	})

	return &impl{
		db: globalDB,
	}
}

func (i *impl) CreateTemplate(ctx context.Context, template *entity.AiStudioTemplate) error {
	return i.db.WithContext(ctx).Create(template).Error
}

func (i *impl) GetTemplate(ctx context.Context, id int64) (*entity.AiStudioTemplate, error) {
	template := &entity.AiStudioTemplate{}
	if err := i.db.WithContext(ctx).First(template, id).Error; err != nil {
		return nil, err
	}

	return template, nil
}

func (i *impl) UpdateTemplate(ctx context.Context, template *entity.AiStudioTemplate) error {
	return i.db.WithContext(ctx).Save(template).Error
}

func (i *impl) DeleteTemplate(ctx context.Context, id int64) error {
	return i.db.WithContext(ctx).Delete(&entity.AiStudioTemplate{}, id).Error
}

func (i *impl) CreateTask(ctx context.Context, task *entity.AiStudioTask) error {
	return i.db.WithContext(ctx).Create(task).Error
}

func (i *impl) GetTask(ctx context.Context, id int64) (*entity.AiStudioTask, error) {
	task := &entity.AiStudioTask{}
	if err := i.db.WithContext(ctx).Preload("Template").First(task, id).Error; err != nil {
		return nil, err
	}

	return task, nil
}

func (i *impl) GetTaskByName(ctx context.Context, name string) (*entity.AiStudioTask, error) {
	task := &entity.AiStudioTask{}
	if err := i.db.WithContext(ctx).Where("task_name = ?", name).Preload("Template").First(task).Error; err != nil {
		return nil, err
	}

	return task, nil
}

func (i *impl) UpdateTask(ctx context.Context, task *entity.AiStudioTask) error {
	return i.db.WithContext(ctx).Save(task).Error
}

func (i *impl) DeleteTask(ctx context.Context, id int64) error {
	err := i.db.WithContext(ctx).Delete(&entity.AiStudioTask{}, id).Error
	if err != nil {
		return err
	}
	err = i.db.WithContext(ctx).Where("task_id = ?", id).Delete(&entity.AiStudioTaskLog{}).Error

	return err
}

func (i *impl) CreateTaskLog(ctx context.Context, taskLog *entity.AiStudioTaskLog) error {
	return i.db.WithContext(ctx).Create(taskLog).Error
}

func (i *impl) GetTaskLog(ctx context.Context, id int64) (*entity.AiStudioTaskLog, error) {
	taskLog := &entity.AiStudioTaskLog{}
	if err := i.db.WithContext(ctx).Preload("Task").First(taskLog, id).Error; err != nil {
		return nil, err
	}

	return taskLog, nil
}

func (i *impl) DeleteTaskLog(ctx context.Context, id int64) error {
	return i.db.WithContext(ctx).Delete(&entity.AiStudioTaskLog{}, id).Error
}

func (i *impl) ListTasks(ctx context.Context,
	pageSize int32, currentPage int32) (items []*entity.AiStudioTask, total int32, err error) {
	offset := (currentPage - 1) * pageSize

	err = i.db.WithContext(ctx).
		Offset(int(offset)).
		Limit(int(pageSize)).
		Preload("Template").
		Find(&items).Error

	if err != nil {
		return nil, 0, err
	}

	var count int64
	err = i.db.WithContext(ctx).Model(&entity.AiStudioTask{}).Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	total = int32(count)

	return
}

func (i *impl) ListTemplate(ctx context.Context,
	pageSize int32, currentPage int32) (items []*entity.AiStudioTemplate, total int32, err error) {
	// if pageSize==-1,then get all
	if pageSize == -1 {
		err = i.db.WithContext(ctx).
			Find(&items).Error
		if err != nil {
			return nil, 0, err
		}
		var count int64
		err = i.db.WithContext(ctx).Model(&entity.AiStudioTemplate{}).Count(&count).Error
		if err != nil {
			return nil, 0, err
		}
		total = int32(count)

		return
	}

	offset := (currentPage - 1) * pageSize

	err = i.db.WithContext(ctx).
		Offset(int(offset)).
		Limit(int(pageSize)).
		Find(&items).Error

	if err != nil {
		return nil, 0, err
	}

	var count int64
	err = i.db.WithContext(ctx).Model(&entity.AiStudioTemplate{}).Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	total = int32(count)

	return
}

func (i *impl) ListTaskLogs(ctx context.Context,
	pageSize int32, currentPage int32) (items []*entity.AiStudioTaskLog, total int32, err error) {
	offset := (currentPage - 1) * pageSize

	err = i.db.WithContext(ctx).
		Offset(int(offset)).
		Limit(int(pageSize)).
		Order("id DESC").
		Preload("Task").
		Find(&items).Error

	if err != nil {
		return nil, 0, err
	}

	var count int64
	err = i.db.WithContext(ctx).Model(&entity.AiStudioTaskLog{}).Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	total = int32(count)

	return
}

func (i *impl) GetTasks(ctx context.Context, searchBean *entity.AiStudioTask) ([]*entity.AiStudioTask, error) {
	var tasks []*entity.AiStudioTask
	db := i.db.WithContext(ctx).Preload("Template")

	if searchBean.TaskName != "" {
		db = db.Where("task_name = ?", searchBean.TaskName)
	}
	if searchBean.TemplateID != 0 {
		db = db.Where("template_id = ?", searchBean.TemplateID)
	}

	if searchBean.Spec == "all" {
		db = db.Where("spec != ?", "")
	} else if searchBean.Spec != "" {
		db = db.Where("spec == ?", searchBean.Spec)
	}

	if err := db.Find(&tasks).Error; err != nil {
		return nil, err
	}

	return tasks, nil
}

func (i *impl) CreateAiStudioCronJob(ctx context.Context, cronJob *entity.AiStudioCronJob) error {
	return i.db.WithContext(ctx).Create(cronJob).Error
}

func (i *impl) GetAiStudioCronJob(ctx context.Context, jobID string) (*entity.AiStudioCronJob, error) {
	cronJob := &entity.AiStudioCronJob{}
	if err := i.db.WithContext(ctx).Where("job_id = ?", jobID).Preload("Task").First(cronJob).Error; err != nil {
		return nil, err
	}

	return cronJob, nil
}

func (i *impl) ListAiStudioCronJobs(ctx context.Context,
	pageSize int32, currentPage int32) (items []*entity.AiStudioCronJob, total int32, err error) {
	offset := (currentPage - 1) * pageSize

	err = i.db.WithContext(ctx).
		Offset(int(offset)).
		Limit(int(pageSize)).
		Preload("Task").
		Find(&items).Error

	if err != nil {
		return nil, 0, err
	}

	var count int64
	err = i.db.WithContext(ctx).Model(&entity.AiStudioCronJob{}).Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	total = int32(count)

	return
}
