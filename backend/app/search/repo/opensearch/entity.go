package opensearch

type Strategy interface {
	BuildParams() map[string]any
	Validate() error
}

// SearchRequest 定义搜索请求
type SearchRequest struct {
	Query       map[string]any   `json:"query"`
	Sort        []map[string]any `json:"sort"`
	SearchAfter []any            `json:"search_after,omitempty"`
	Size        int32            `json:"size"`
}

// SearchHit 定义单个搜索结果的结构
type SearchHit struct {
	Index  string         `json:"_index"`
	ID     string         `json:"_id"`
	Score  float64        `json:"_score"`
	Source map[string]any `json:"_source"`
	Sort   []any          `json:"sort"`
}

type SearchResponse struct {
	Took     int   `json:"took"`
	TimedOut bool  `json:"timed_out"`
	Hits     *Hits `json:"hits"`
}

type SearchHitsTotal struct {
	Value    int    `json:"value"`
	Relation string `json:"relation"`
}

type Hits struct {
	Total    *SearchHitsTotal `json:"total"`
	MaxScore float64          `json:"max_score"`
	Hits     []*SearchHit     `json:"hits"`
}
