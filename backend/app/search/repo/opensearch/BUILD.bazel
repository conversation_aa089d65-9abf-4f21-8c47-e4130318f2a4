load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "opensearch",
    srcs = [
        "document.go",
        "entity.go",
        "index.go",
        "opensearch.go",
        "search.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/search/repo/opensearch",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/config",
        "//backend/common/rpc/framework/log",
        "@com_github_aws_aws_sdk_go_v2_config//:config",
        "@com_github_aws_aws_sdk_go_v2_credentials//:credentials",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_opensearch_project_opensearch_go//opensearchapi",
        "@com_github_opensearch_project_opensearch_go_v4//:opensearch-go",
        "@com_github_opensearch_project_opensearch_go_v4//signer/awsv2",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_uber_go_zap//:zap",
    ],
)
