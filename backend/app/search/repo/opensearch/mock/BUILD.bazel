load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["opensearch_mock.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/search/repo/opensearch/mock",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/search/repo/opensearch",
        "@com_github_opensearch_project_opensearch_go//opensearchapi",
        "@org_uber_go_mock//gomock",
    ],
)
