package search

import (
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/app/search/repo/opensearch"
)

// 精确匹配
type TermStrategy struct {
	Field string
	Value interface{}
}

func (t *TermStrategy) BuildParams() map[string]interface{} {
	return map[string]interface{}{
		"term": map[string]interface{}{
			t.Field: t.Value,
		},
	}
}

func (t *TermStrategy) Validate() error {
	if t.Field == "" {
		return status.Errorf(codes.InvalidArgument, "term strategy requires field name")
	}

	return nil
}

// 模糊匹配
type MatchStrategy struct {
	Field    string
	Query    string
	Operator MatchOperator // 支持 AND/OR
	Boost    float32       // 权重
}

type MatchOperator string

const (
	MatchOperatorAnd MatchOperator = "AND"
	MatchOperatorOr  MatchOperator = "OR"
)

func (m *MatchStrategy) BuildParams() map[string]interface{} {
	return map[string]interface{}{
		"match": map[string]interface{}{
			m.Field: map[string]interface{}{
				"query":    m.Query,
				"operator": m.Operator,
				"boost":    m.Boost,
			},
		},
	}
}

func (m *MatchStrategy) Validate() error {
	if m.Field == "" || m.Query == "" {
		return status.Errorf(codes.InvalidArgument, "match strategy requires field and query")
	}

	return nil
}

// 范围查询
// 支持数字、日期、时间范围查询
type RangeStrategy struct {
	Field string
	Gte   interface{} // 大于等于
	Lte   interface{} // 小于等于
}

func (r *RangeStrategy) BuildParams() map[string]interface{} {
	return map[string]interface{}{
		"range": map[string]interface{}{
			r.Field: map[string]interface{}{
				"gte": r.Gte,
				"lte": r.Lte,
			},
		},
	}
}

func (r *RangeStrategy) Validate() error {
	if r.Field == "" {
		return status.Errorf(codes.InvalidArgument, "range strategy requires field name")
	}

	return nil
}

// 通配符查询
// 支持 * 和 ? 通配符
type WildcardStrategy struct {
	Field string
	Value string
}

func (w *WildcardStrategy) BuildParams() map[string]interface{} {
	return map[string]interface{}{
		"wildcard": map[string]interface{}{
			w.Field: w.Value,
		},
	}
}

func (w *WildcardStrategy) Validate() error {
	if w.Field == "" || w.Value == "" {
		return status.Errorf(codes.InvalidArgument, "wildcard strategy requires field and value")
	}

	return nil
}

// 精确多值查询
type TermsStrategy struct {
	Field  string
	Values []interface{} // 精确匹配多个值
}

func (t *TermsStrategy) BuildParams() map[string]interface{} {
	return map[string]interface{}{
		"terms": map[string]interface{}{
			t.Field: t.Values,
		},
	}
}

func (t *TermsStrategy) Validate() error {
	if t.Field == "" || len(t.Values) == 0 {
		return status.Errorf(codes.InvalidArgument, "terms strategy requires field and values")
	}

	return nil
}

// 布尔查询, 组合查询
type BoolStrategy struct {
	Must    []opensearch.Strategy // AND 条件
	MustNot []opensearch.Strategy // NOT 条件

	Filter []opensearch.Strategy // 过滤条件
	Should *BoolStrategyShould
}

type BoolStrategyShould struct {
	Strategies   []opensearch.Strategy
	MinimumMatch int // 至少满足的 should 条件数
}

func (b *BoolStrategy) BuildParams() map[string]interface{} {
	boolQuery := make(map[string]interface{})

	if len(b.Must) > 0 {
		boolQuery["must"] = b.buildClauses(b.Must)
	}

	if b.Should != nil && len(b.Should.Strategies) > 0 {
		boolQuery["should"] = b.buildClauses(b.Should.Strategies)
		boolQuery["minimum_should_match"] = b.Should.MinimumMatch
	}

	if len(b.MustNot) > 0 {
		boolQuery["must_not"] = b.buildClauses(b.MustNot)
	}

	if len(b.Filter) > 0 {
		boolQuery["filter"] = b.buildClauses(b.Filter)
	}

	return map[string]interface{}{
		"bool": boolQuery,
	}
}

func (b *BoolStrategy) buildClauses(strategies []opensearch.Strategy) []map[string]interface{} {
	var clauses []map[string]interface{}
	for _, s := range strategies {
		clauses = append(clauses, s.BuildParams())
	}

	return clauses
}

func checkShould(b *BoolStrategy) bool {
	if b.Should == nil {
		return false
	}
	if b.Should.MinimumMatch == 0 && len(b.Should.Strategies) == 0 {
		return false
	}

	return true
}

func validateStrategies(strategies []opensearch.Strategy) error {
	for _, s := range strategies {
		if err := s.Validate(); err != nil {
			return err
		}
	}

	return nil
}

func (b *BoolStrategy) Validate() error {
	// 至少要有一个条件
	if len(b.Must) == 0 && !checkShould(b) && len(b.MustNot) == 0 && len(b.Filter) == 0 {
		return status.Errorf(codes.InvalidArgument, "bool strategy requires at least one condition")
	}

	// 检查所有条件是否满足
	if err := validateStrategies(b.Filter); err != nil {
		return err
	}

	if err := validateStrategies(b.Must); err != nil {
		return err
	}

	if err := validateStrategies(b.MustNot); err != nil {
		return err
	}

	if b.Should != nil {
		if err := validateStrategies(b.Should.Strategies); err != nil {
			return err
		}
	}

	return nil
}

// MultiMatchStrategy 多字段匹配策略
type MultiMatchStrategy struct {
	// 查询文本
	Query string
	// 匹配类型
	Type string
	// 要搜索的字段列表，可以包含权重，如 "name^3"
	Fields []string
	// 模糊匹配参数，如 "AUTO" 或 "AUTO:3,6" 或具体数字
	Fuzziness string
	// 前缀长度（用于模糊匹配时保持前缀不变）
	PrefixLength int
	// 查询权重
	Boost float32
}

// BuildParams 构建查询参数
func (m *MultiMatchStrategy) BuildParams() map[string]any {
	params := map[string]any{
		"query":  m.Query,
		"type":   m.Type,
		"fields": m.Fields,
	}

	// 添加可选参数
	if m.Fuzziness != "" {
		params["fuzziness"] = m.Fuzziness
	}
	if m.PrefixLength > 0 {
		params["prefix_length"] = m.PrefixLength
	}
	if m.Boost > 0 {
		params["boost"] = m.Boost
	}

	return map[string]any{
		"multi_match": params,
	}
}

// Validate 验证参数
func (m *MultiMatchStrategy) Validate() error {
	if m.Query == "" {
		return status.Errorf(codes.InvalidArgument, "multi_match query is required")
	}
	if len(m.Fields) == 0 {
		return status.Errorf(codes.InvalidArgument, "multi_match fields is required")
	}
	if m.Type == "" {
		return status.Errorf(codes.InvalidArgument, "multi_match type is required")
	}

	return nil
}

// QueryStringStrategy 查询字符串策略
type QueryStringStrategy struct {
	// 查询字符串，支持高级语法
	Query string
	// 要搜索的字段列表
	Fields []string
	// 是否分析通配符
	AnalyzeWildcard bool
	// 默认操作符
	DefaultOperator string
	// 查询权重
	Boost float32
}

// BuildParams 构建查询参数
func (q *QueryStringStrategy) BuildParams() map[string]any {
	params := map[string]any{
		"query": q.Query,
	}

	// 添加可选参数
	if len(q.Fields) > 0 {
		params["fields"] = q.Fields
	}
	if q.AnalyzeWildcard {
		params["analyze_wildcard"] = q.AnalyzeWildcard
	}
	if q.DefaultOperator != "" {
		params["default_operator"] = q.DefaultOperator
	}
	if q.Boost > 0 {
		params["boost"] = q.Boost
	}

	return map[string]any{
		"query_string": params,
	}
}

// Validate 验证参数
func (q *QueryStringStrategy) Validate() error {
	if q.Query == "" {
		return status.Errorf(codes.InvalidArgument, "query_string query is required")
	}

	return nil
}

// MatchPhraseStrategy 短语匹配策略
type MatchPhraseStrategy struct {
	// 字段名
	Field string
	// 查询短语
	Query string
	// 词之间允许的最大间隔
	Slop int
	// 查询权重
	Boost float32
}

// BuildParams 构建查询参数
func (m *MatchPhraseStrategy) BuildParams() map[string]any {
	params := map[string]any{
		"query": m.Query,
	}

	// 添加可选参数
	if m.Slop > 0 {
		params["slop"] = m.Slop
	}
	if m.Boost > 0 {
		params["boost"] = m.Boost
	}

	return map[string]any{
		"match_phrase": map[string]any{
			m.Field: params,
		},
	}
}

// Validate 验证参数
func (m *MatchPhraseStrategy) Validate() error {
	if m.Field == "" {
		return status.Errorf(codes.InvalidArgument, "match_phrase field is required")
	}
	if m.Query == "" {
		return status.Errorf(codes.InvalidArgument, "match_phrase query is required")
	}

	return nil
}
