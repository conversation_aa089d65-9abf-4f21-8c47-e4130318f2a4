package search_test

import (
	"testing"

	"github.com/stretchr/testify/require"

	entity "github.com/MoeGolibrary/moego/backend/app/search/logic/search"
	"github.com/MoeGolibrary/moego/backend/app/search/repo/opensearch"
)

func TestTermStrategy_BuildParams(t *testing.T) {
	t.Run("test term strategy build params", func(t *testing.T) {
		strategy := &entity.TermStrategy{
			Field: "name",
			Value: "jett",
		}
		params := strategy.BuildParams()

		require.Equal(t, params["term"], map[string]interface{}{
			"name": "jett",
		})
	})

}

func TestTermStrategy_Validate(t *testing.T) {
	t.Run("test term strategy validate success", func(t *testing.T) {
		strategy := &entity.TermStrategy{
			Field: "name",
			Value: "jett",
		}
		err := strategy.Validate()
		require.NoError(t, err)
	})

	t.Run("test term strategy validate failed", func(t *testing.T) {
		strategy := &entity.TermStrategy{
			Field: "",
			Value: "jett",
		}
		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "term strategy requires field name")
	})
}

func TestMatchStrategy_BuildParams(t *testing.T) {
	t.Run("test match strategy build params", func(t *testing.T) {
		strategy := &entity.MatchStrategy{
			Field:    "name",
			Query:    "jett",
			Operator: entity.MatchOperatorAnd,
		}
		params := strategy.BuildParams()

		require.Equal(t, params["match"], map[string]interface{}{
			"name": map[string]interface{}{
				"query":    "jett",
				"operator": entity.MatchOperatorAnd,
				"boost":    float32(0),
			},
		})
	})
}

func TestMatchStrategy_Validate(t *testing.T) {
	t.Run("test match strategy validate success", func(t *testing.T) {
		strategy := &entity.MatchStrategy{
			Field:    "name",
			Query:    "jett",
			Operator: entity.MatchOperatorAnd,
		}
		err := strategy.Validate()
		require.NoError(t, err)
	})

	t.Run("test match strategy validate failed with empty field", func(t *testing.T) {
		strategy := &entity.MatchStrategy{
			Field:    "",
			Query:    "jett",
			Operator: entity.MatchOperatorAnd,
		}
		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "match strategy requires field and query")
	})

	t.Run("test match strategy validate failed with empty query", func(t *testing.T) {
		strategy := &entity.MatchStrategy{
			Field:    "name",
			Query:    "",
			Operator: entity.MatchOperatorAnd,
		}
		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "match strategy requires field and query")
	})
}

func TestRangeStrategy_BuildParams(t *testing.T) {
	t.Run("test range strategy build params", func(t *testing.T) {
		strategy := &entity.RangeStrategy{
			Field: "age",
			Gte:   18,
			Lte:   30,
		}
		params := strategy.BuildParams()

		require.Equal(t, params["range"], map[string]interface{}{
			"age": map[string]interface{}{
				"gte": 18,
				"lte": 30,
			},
		})
	})
}

func TestRangeStrategy_Validate(t *testing.T) {
	t.Run("test range strategy validate success", func(t *testing.T) {
		strategy := &entity.RangeStrategy{
			Field: "age",
			Gte:   18,
			Lte:   30,
		}
		err := strategy.Validate()
		require.NoError(t, err)
	})

	t.Run("test range strategy validate failed", func(t *testing.T) {
		strategy := &entity.RangeStrategy{
			Field: "",
			Gte:   18,
			Lte:   30,
		}
		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "range strategy requires field name")
	})
}

func TestWildcardStrategy_BuildParams(t *testing.T) {
	t.Run("test wildcard strategy build params", func(t *testing.T) {
		strategy := &entity.WildcardStrategy{
			Field: "name",
			Value: "je*",
		}
		params := strategy.BuildParams()

		require.Equal(t, params["wildcard"], map[string]interface{}{
			"name": "je*",
		})
	})
}

func TestWildcardStrategy_Validate(t *testing.T) {
	t.Run("test wildcard strategy validate success", func(t *testing.T) {
		strategy := &entity.WildcardStrategy{
			Field: "name",
			Value: "je*",
		}
		err := strategy.Validate()
		require.NoError(t, err)
	})

	t.Run("test wildcard strategy validate failed with empty field", func(t *testing.T) {
		strategy := &entity.WildcardStrategy{
			Field: "",
			Value: "je*",
		}
		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "wildcard strategy requires field and value")
	})

	t.Run("test wildcard strategy validate failed with empty value", func(t *testing.T) {
		strategy := &entity.WildcardStrategy{
			Field: "name",
			Value: "",
		}
		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "wildcard strategy requires field and value")
	})
}

func TestTermsStrategy_BuildParams(t *testing.T) {
	t.Run("test terms strategy build params", func(t *testing.T) {
		strategy := &entity.TermsStrategy{
			Field:  "name",
			Values: []interface{}{"jett", "sage"},
		}
		params := strategy.BuildParams()

		require.Equal(t, params["terms"], map[string]interface{}{
			"name": []interface{}{"jett", "sage"},
		})
	})
}

func TestTermsStrategy_Validate(t *testing.T) {
	t.Run("test terms strategy validate success", func(t *testing.T) {
		strategy := &entity.TermsStrategy{
			Field:  "name",
			Values: []interface{}{"jett", "sage"},
		}
		err := strategy.Validate()
		require.NoError(t, err)
	})

	t.Run("test terms strategy validate failed with empty field", func(t *testing.T) {
		strategy := &entity.TermsStrategy{
			Field:  "",
			Values: []interface{}{"jett", "sage"},
		}
		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "terms strategy requires field and values")
	})

	t.Run("test terms strategy validate failed with empty values", func(t *testing.T) {
		strategy := &entity.TermsStrategy{
			Field:  "name",
			Values: []interface{}{},
		}
		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "terms strategy requires field and values")
	})
}

func TestBoolStrategy_BuildParams(t *testing.T) {
	t.Run("test bool strategy build params", func(t *testing.T) {
		must := &entity.TermStrategy{
			Field: "name",
			Value: "jett",
		}
		mustNot := &entity.TermStrategy{
			Field: "age",
			Value: 20,
		}
		filter := &entity.RangeStrategy{
			Field: "score",
			Gte:   60,
			Lte:   100,
		}
		should := &entity.MatchStrategy{
			Field:    "description",
			Query:    "good",
			Operator: entity.MatchOperatorAnd,
		}

		strategy := &entity.BoolStrategy{
			Must:    []opensearch.Strategy{must},
			MustNot: []opensearch.Strategy{mustNot},
			Filter:  []opensearch.Strategy{filter},
			Should: &entity.BoolStrategyShould{
				Strategies:   []opensearch.Strategy{should},
				MinimumMatch: 1,
			},
		}

		params := strategy.BuildParams()
		boolQuery := params["bool"].(map[string]interface{})

		require.NotNil(t, boolQuery["must"])
		require.NotNil(t, boolQuery["must_not"])
		require.NotNil(t, boolQuery["filter"])
		require.NotNil(t, boolQuery["should"])
		require.Equal(t, boolQuery["minimum_should_match"], 1)
	})
}

func TestBoolStrategy_Validate(t *testing.T) {
	t.Run("test bool strategy validate success", func(t *testing.T) {
		must := &entity.TermStrategy{
			Field: "name",
			Value: "jett",
		}
		strategy := &entity.BoolStrategy{
			Must: []opensearch.Strategy{must},
		}
		err := strategy.Validate()
		require.NoError(t, err)
	})

	t.Run("test bool strategy validate failed with no conditions", func(t *testing.T) {
		strategy := &entity.BoolStrategy{}
		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "bool strategy requires at least one condition")
	})

	t.Run("test bool strategy validate failed with invalid must strategy", func(t *testing.T) {
		must := &entity.TermStrategy{
			Field: "",
			Value: "jett",
		}
		strategy := &entity.BoolStrategy{
			Must: []opensearch.Strategy{must},
		}
		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "term strategy requires field name")
	})

	t.Run("test bool strategy validate failed with invalid must_not strategy", func(t *testing.T) {
		mustNot := &entity.TermStrategy{
			Field: "",
			Value: "jett",
		}
		strategy := &entity.BoolStrategy{
			MustNot: []opensearch.Strategy{mustNot},
		}
		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "term strategy requires field name")
	})

	t.Run("test bool strategy validate failed with invalid filter strategy", func(t *testing.T) {
		filter := &entity.RangeStrategy{
			Field: "",
			Gte:   60,
			Lte:   100,
		}
		strategy := &entity.BoolStrategy{
			Filter: []opensearch.Strategy{filter},
		}
		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "range strategy requires field name")
	})

	t.Run("test bool strategy validate failed with invalid should strategy", func(t *testing.T) {
		should := &entity.MatchStrategy{
			Field:    "",
			Query:    "good",
			Operator: entity.MatchOperatorAnd,
		}
		strategy := &entity.BoolStrategy{
			Should: &entity.BoolStrategyShould{
				Strategies:   []opensearch.Strategy{should},
				MinimumMatch: 1,
			},
		}
		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "match strategy requires field and query")
	})

	t.Run("test bool strategy validate failed with invalid minimum_should_match", func(t *testing.T) {
		strategy := &entity.BoolStrategy{
			Should: &entity.BoolStrategyShould{
				Strategies:   []opensearch.Strategy{},
				MinimumMatch: 0,
			},
		}
		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "bool strategy requires at least one condition")
	})

}

func TestMultiMatchStrategy(t *testing.T) {
	t.Run("测试多字段匹配策略构建参数", func(t *testing.T) {
		strategy := &entity.MultiMatchStrategy{
			Query:        "测试查询",
			Type:         "best_fields",
			Fields:       []string{"title^3", "description"},
			Fuzziness:    "AUTO",
			PrefixLength: 2,
			Boost:        1.5,
		}

		params := strategy.BuildParams()
		multiMatch, ok := params["multi_match"].(map[string]interface{})
		require.True(t, ok)

		require.Equal(t, "测试查询", multiMatch["query"])
		require.Equal(t, "best_fields", multiMatch["type"])
		require.Equal(t, []string{"title^3", "description"}, multiMatch["fields"])
		require.Equal(t, "AUTO", multiMatch["fuzziness"])
		require.Equal(t, 2, multiMatch["prefix_length"])
		require.Equal(t, float32(1.5), multiMatch["boost"])
	})

	t.Run("测试多字段匹配策略验证成功", func(t *testing.T) {
		strategy := &entity.MultiMatchStrategy{
			Query:  "测试查询",
			Type:   "best_fields",
			Fields: []string{"title", "description"},
		}

		err := strategy.Validate()
		require.NoError(t, err)
	})

	t.Run("测试多字段匹配策略验证失败 - 空查询", func(t *testing.T) {
		strategy := &entity.MultiMatchStrategy{
			Query:  "",
			Type:   "best_fields",
			Fields: []string{"title", "description"},
		}

		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "multi_match query is required")
	})

	t.Run("测试多字段匹配策略验证失败 - 空字段", func(t *testing.T) {
		strategy := &entity.MultiMatchStrategy{
			Query:  "测试查询",
			Type:   "best_fields",
			Fields: []string{},
		}

		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "multi_match fields is required")
	})

	t.Run("测试多字段匹配策略验证失败 - 空类型", func(t *testing.T) {
		strategy := &entity.MultiMatchStrategy{
			Query:  "测试查询",
			Type:   "",
			Fields: []string{"title", "description"},
		}

		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "multi_match type is required")
	})
}

func TestMatchPhraseStrategy(t *testing.T) {
	t.Run("测试短语匹配策略构建参数", func(t *testing.T) {
		strategy := &entity.MatchPhraseStrategy{
			Field: "content",
			Query: "测试短语",
			Slop:  2,
			Boost: 1.5,
		}

		params := strategy.BuildParams()
		matchPhrase, ok := params["match_phrase"].(map[string]interface{})
		require.True(t, ok)

		content, ok := matchPhrase["content"].(map[string]interface{})
		require.True(t, ok)

		require.Equal(t, "测试短语", content["query"])
		require.Equal(t, 2, content["slop"])
		require.Equal(t, float32(1.5), content["boost"])
	})

	t.Run("测试短语匹配策略验证成功", func(t *testing.T) {
		strategy := &entity.MatchPhraseStrategy{
			Field: "content",
			Query: "测试短语",
		}

		err := strategy.Validate()
		require.NoError(t, err)
	})

	t.Run("测试短语匹配策略验证失败 - 空字段", func(t *testing.T) {
		strategy := &entity.MatchPhraseStrategy{
			Field: "",
			Query: "测试短语",
		}

		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "match_phrase field is required")
	})

	t.Run("测试短语匹配策略验证失败 - 空查询", func(t *testing.T) {
		strategy := &entity.MatchPhraseStrategy{
			Field: "content",
			Query: "",
		}

		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "match_phrase query is required")
	})
}

func TestQueryStringStrategy(t *testing.T) {
	t.Run("测试查询字符串策略构建参数", func(t *testing.T) {
		strategy := &entity.QueryStringStrategy{
			Query:           "title:测试 AND (content:查询 OR content:搜索)",
			Fields:          []string{"title^2", "content"},
			AnalyzeWildcard: true,
			DefaultOperator: "AND",
			Boost:           1.5,
		}

		params := strategy.BuildParams()
		queryString, ok := params["query_string"].(map[string]interface{})
		require.True(t, ok)

		require.Equal(t, "title:测试 AND (content:查询 OR content:搜索)", queryString["query"])
		require.Equal(t, []string{"title^2", "content"}, queryString["fields"])
		require.Equal(t, true, queryString["analyze_wildcard"])
		require.Equal(t, "AND", queryString["default_operator"])
		require.Equal(t, float32(1.5), queryString["boost"])
	})

	t.Run("测试查询字符串策略验证成功", func(t *testing.T) {
		strategy := &entity.QueryStringStrategy{
			Query: "测试查询",
		}

		err := strategy.Validate()
		require.NoError(t, err)
	})

	t.Run("测试查询字符串策略验证失败 - 空查询", func(t *testing.T) {
		strategy := &entity.QueryStringStrategy{
			Query: "",
		}

		err := strategy.Validate()
		require.Error(t, err)
		require.Contains(t, err.Error(), "query_string query is required")
	})
}
