package search

import (
	"context"
	"fmt"
	"reflect"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/MoeGolibrary/moego/backend/app/search/repo/opensearch"
	searchutils "github.com/MoeGolibrary/moego/backend/app/search/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

type Converter struct {
}

func (c *Converter) Convert(_ context.Context,
	resp *opensearch.SearchResponse) (*searchpb.SearchDocumentResponse, error) {
	if resp == nil {
		return nil, status.Errorf(codes.InvalidArgument, "search response is nil")
	}
	hits := resp.Hits.Hits
	protoHits := make([]*searchpb.SearchDocumentResponse_Hit, 0, len(hits))
	for _, hit := range hits {
		protoHit, err := c.convertHit(hit)
		if err != nil {
			return nil, err
		}
		protoHits = append(protoHits, protoHit)
	}

	return &searchpb.SearchDocumentResponse{
		Took:     int32(resp.Took),
		TimedOut: resp.TimedOut,
		Total: &searchpb.SearchDocumentResponse_Total{
			Value:    int32(resp.Hits.Total.Value),
			Relation: resp.Hits.Total.Relation,
		},
		MaxScore: resp.Hits.MaxScore,
		Hits:     protoHits,
	}, nil
}

func (c *Converter) convertHit(hit *opensearch.SearchHit) (*searchpb.SearchDocumentResponse_Hit, error) {
	convertedSource, err := c.toProtoCompatible(hit.Source)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "failed to convert source to proto compatible format: %v", err)
	}
	if convertedSource, ok := convertedSource.(map[string]any); ok {
		source, err := structpb.NewStruct(convertedSource)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument,
				"failed to create proto struct from converted source: %v", err)
		}

		// 转换 Sort 值
		sortValues := make([]*structpb.Value, len(hit.Sort))
		for i, v := range hit.Sort {
			value, err := structpb.NewValue(v)
			if err != nil {
				return nil, status.Errorf(codes.InvalidArgument, "failed to convert sort value: %v", err)
			}
			sortValues[i] = value
		}

		return &searchpb.SearchDocumentResponse_Hit{
			Index:  hit.Index,
			Id:     hit.ID,
			Score:  hit.Score,
			Source: source,
			Sort:   sortValues,
		}, nil
	}

	return nil, status.Errorf(codes.InvalidArgument, "converted source is not a map[string]any")
}

// toProtoCompatible 将任意类型转换为 protobuf 兼容的类型
func (c *Converter) toProtoCompatible(v any) (any, error) {
	switch val := v.(type) {
	case nil:
		return nil, nil
	case map[string]any:
		if val == nil {
			return nil, nil
		}

		return c.convertMap(val)

	case []any:
		return c.convertSlice(val)

	case []string, []int, []int32, []int64, []float32, []float64, []bool:
		return c.convertTypedSlice(val)

	// 基本类型直接返回
	case string, bool, int, int32, int64, float32, float64:
		return val, nil

	default:
		return nil, fmt.Errorf("unsupported type: %T", v)
	}
}

// convertMap 转换 map
func (c *Converter) convertMap(m map[string]any) (map[string]any, error) {
	result := make(map[string]any, len(m))
	for k, v := range m {
		converted, err := c.toProtoCompatible(v)
		if err != nil {
			return nil, fmt.Errorf("convert map value error: %w", err)
		}
		result[k] = converted
	}

	return result, nil
}

// convertSlice 转换 any 切片
func (c *Converter) convertSlice(s []any) ([]any, error) {
	result := make([]any, len(s))
	for i, v := range s {
		converted, err := c.toProtoCompatible(v)
		if err != nil {
			return nil, fmt.Errorf("convert slice element error: %w", err)
		}
		result[i] = converted
	}

	return result, nil
}

// convertTypedSlice 转换类型化的切片
func (c *Converter) convertTypedSlice(v any) ([]any, error) {
	value := reflect.ValueOf(v)
	result := make([]any, value.Len())
	for i := 0; i < value.Len(); i++ {
		result[i] = value.Index(i).Interface()
	}

	return result, nil
}

func (c *Converter) BuildRequest(ctx context.Context,
	req *searchpb.SearchDocumentRequest) (*opensearch.SearchRequest, error) {
	st := match(ctx, req.GetStrategy())
	if st == nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid strategy")
	}
	if err := st.Validate(); err != nil {
		return nil, err
	}

	query := st.BuildParams()

	request := &opensearch.SearchRequest{
		Query: query,
		Sort:  buildSort(req.GetSort()),
		Size:  req.GetPageSize(),
	}

	if req.GetSearchAfter() != nil {
		searchAfter := make([]any, 0, len(req.GetSearchAfter()))
		for _, v := range req.GetSearchAfter() {
			searchAfter = append(searchAfter, searchutils.GetValue(v))
		}
		request.SearchAfter = searchAfter
	}

	return request, nil
}

func buildSort(sort []*searchpb.SearchDocumentRequest_Sort) []map[string]any {
	result := make([]map[string]any, 0, len(sort))
	for _, s := range sort {
		result = append(result, map[string]any{s.GetField(): s.GetOrder().String()})
	}

	return result
}

func match(ctx context.Context, strategy *searchpb.Strategy) opensearch.Strategy {
	// strategyHandler 是一个处理策略创建的函数类型
	type strategyHandler func(ctx context.Context, strategy *searchpb.Strategy) opensearch.Strategy

	// strategyHandlers 将策略类型映射到对应的处理函数
	var strategyHandlers = map[string]strategyHandler{
		"*searchpb.Strategy_Term":        handleTermStrategy,
		"*searchpb.Strategy_Match":       handleMatchStrategy,
		"*searchpb.Strategy_Range":       handleRangeStrategy,
		"*searchpb.Strategy_Wildcard":    handleWildcardStrategy,
		"*searchpb.Strategy_Terms":       handleTermsStrategy,
		"*searchpb.Strategy_Bool":        handleBoolStrategy,
		"*searchpb.Strategy_MultiMatch":  handleMultiMatchStrategy,
		"*searchpb.Strategy_MatchPhrase": handleMatchPhraseStrategy,
		"*searchpb.Strategy_QueryString": handleQueryStringStrategy,
	}

	strategyType := fmt.Sprintf("%T", strategy.GetStrategy())
	handler, exists := strategyHandlers[strategyType]
	if !exists {
		log.ErrorContext(ctx, "invalid strategy", zap.Any("strategy", strategy))

		return nil
	}

	return handler(ctx, strategy)
}
