package search

import (
	"context"
	"strings"

	"github.com/MoeGolibrary/moego/backend/app/search/repo/opensearch"
	searchutils "github.com/MoeGolibrary/moego/backend/app/search/utils"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

// 以下是各个策略类型的处理函数

func handleTermStrategy(_ context.Context, strategy *searchpb.Strategy) opensearch.Strategy {
	return &TermStrategy{
		Field: strategy.GetTerm().GetField(),
		Value: searchutils.GetValue(strategy.GetTerm().GetValue()),
	}
}

func handleMatchStrategy(_ context.Context, strategy *searchpb.Strategy) opensearch.Strategy {
	return &MatchStrategy{
		Field:    strategy.GetMatch().GetField(),
		Query:    strategy.GetMatch().GetQuery(),
		Operator: MatchOperator(strategy.GetMatch().GetOperator().String()),
		Boost:    strategy.GetMatch().GetBoost(),
	}
}

func handleRangeStrategy(_ context.Context, strategy *searchpb.Strategy) opensearch.Strategy {
	return &RangeStrategy{
		Field: strategy.GetRange().GetField(),
		Gte:   searchutils.GetValue(strategy.GetRange().GetGte()),
		Lte:   searchutils.GetValue(strategy.GetRange().GetLte()),
	}
}

func handleWildcardStrategy(_ context.Context, strategy *searchpb.Strategy) opensearch.Strategy {
	return &WildcardStrategy{
		Field: strategy.GetWildcard().GetField(),
		Value: strategy.GetWildcard().GetValue(),
	}
}

func handleTermsStrategy(_ context.Context, strategy *searchpb.Strategy) opensearch.Strategy {
	return &TermsStrategy{
		Field: strategy.GetTerms().GetField(),
		Values: func() []any {
			values := strategy.GetTerms().GetValues()
			result := make([]any, len(values))
			for i, v := range values {
				result[i] = searchutils.GetValue(v)
			}

			return result
		}(),
	}
}

func handleBoolStrategy(ctx context.Context, strategy *searchpb.Strategy) opensearch.Strategy {
	convert := func(strategies []*searchpb.Strategy) []opensearch.Strategy {
		result := make([]opensearch.Strategy, len(strategies))
		for i, v := range strategies {
			result[i] = match(ctx, v)
		}

		return result
	}

	boolStrategy := &BoolStrategy{
		Must:    convert(strategy.GetBool().GetMust()),
		MustNot: convert(strategy.GetBool().GetMustNot()),
		Filter:  convert(strategy.GetBool().GetFilter()),
	}

	if strategy.GetBool().GetShould() != nil {
		boolStrategy.Should = &BoolStrategyShould{
			Strategies:   convert(strategy.GetBool().GetShould().GetStrategies()),
			MinimumMatch: int(strategy.GetBool().GetShould().GetMinimumMatch()),
		}
	}

	return boolStrategy
}

func handleMultiMatchStrategy(_ context.Context, strategy *searchpb.Strategy) opensearch.Strategy {
	return &MultiMatchStrategy{
		Query:     strategy.GetMultiMatch().GetQuery(),
		Type:      strings.ToLower(strategy.GetMultiMatch().GetType().String()),
		Fields:    strategy.GetMultiMatch().GetFields(),
		Fuzziness: strategy.GetMultiMatch().GetFuzziness(),
		Boost:     strategy.GetMultiMatch().GetBoost(),
	}
}

func handleMatchPhraseStrategy(_ context.Context, strategy *searchpb.Strategy) opensearch.Strategy {
	return &MatchPhraseStrategy{
		Field: strategy.GetMatchPhrase().GetField(),
		Query: strategy.GetMatchPhrase().GetQuery(),
		Slop:  int(strategy.GetMatchPhrase().GetSlop()),
		Boost: strategy.GetMatchPhrase().GetBoost(),
	}
}

func handleQueryStringStrategy(_ context.Context, strategy *searchpb.Strategy) opensearch.Strategy {
	return &QueryStringStrategy{
		Query:           strategy.GetQueryString().GetQuery(),
		Fields:          strategy.GetQueryString().GetFields(),
		AnalyzeWildcard: strategy.GetQueryString().GetAnalyzeWildcard(),
		Boost:           strategy.GetQueryString().GetBoost(),
	}
}
