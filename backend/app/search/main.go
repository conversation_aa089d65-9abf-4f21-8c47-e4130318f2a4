package main

import (
	"github.com/MoeGolibrary/moego/backend/app/search/service"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	search "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

func main() {
	s := rpc.NewServer()

	// 这里需要注册grpc
	grpc.Register(s, &search.SearchService_ServiceDesc, service.NewSearch())

	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
