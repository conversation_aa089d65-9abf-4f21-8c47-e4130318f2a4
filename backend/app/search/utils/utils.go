package searchutils

import (
	"time"

	"google.golang.org/protobuf/types/known/structpb"
)

// ToDoUtils is a function that returns a string
func ToDoUtils(password string) string {
	return password
}

func ConvertProtoValue(value interface{}) *structpb.Value {
	switch v := value.(type) {
	case time.Time:
		return structpb.NewStringValue(v.Format(time.RFC3339))
	case string:
		return structpb.NewStringValue(v)
	case int:
		return structpb.NewNumberValue(float64(v))
	case float64:
		return structpb.NewNumberValue(v)
	case bool:
		return structpb.NewBoolValue(v)
	}

	return nil
}

func ConvertProtoValues(values []interface{}) []*structpb.Value {
	protoValues := make([]*structpb.Value, 0, len(values))
	for _, value := range values {
		protoValues = append(protoValues, ConvertProtoValue(value))
	}

	return protoValues
}

func GetValue(s *structpb.Value) interface{} {
	switch s.GetKind().(type) {
	case *structpb.Value_StringValue:
		return s.GetStringValue()
	case *structpb.Value_NumberValue:
		return s.GetNumberValue()
	case *structpb.Value_BoolValue:
		return s.GetBoolValue()
	case *structpb.Value_ListValue:
		return s.GetListValue()
	case *structpb.Value_StructValue:
		return s.GetStructValue()
	case *structpb.Value_NullValue:
		return nil
	}

	return nil
}
