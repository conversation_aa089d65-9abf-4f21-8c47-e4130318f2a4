import asyncio
import json
import os
from datetime import datetime
from google import genai
from google.genai import types
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from tools import remove_properties_default

client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))

# Create server parameters for stdio connection
server_params = StdioServerParameters(
    command="uvx",
    args=[
        "mcp-atlassian",
        "--jira-url=https://moego.atlassian.net",
        "--jira-username=<EMAIL>",
        f"--jira-token={os.getenv("JIRA_TOKEN")}",
    ],
    env=None,
)
# server_params = StdioServerParameters(
#     command="uvx",
#     args=["mcp-server-time", "--local-timezone=Asia/Shanghai"],
#     env={
#         "http_proxy": os.getenv("http_proxy"),
#         "https_proxy": os.getenv("https_proxy"),
#     },
# )


model_str = "gemini-2.0-flash-exp"


async def run():
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Prompt to get the weather for the current day in London.
            prompt = f"<NAME_EMAIL> please get the latest jira ticket related(assignee/reporter) to me"
            # Initialize the connection between client and server
            await session.initialize()

            # Get tools from MCP session and convert to Gemini Tool objects
            mcp_tools = await session.list_tools()
            tools = [
                types.Tool(
                    function_declarations=[
                        {
                            "name": tool.name,
                            "description": tool.description,
                            "parameters": {
                                k: remove_properties_default(k, v)
                                for k, v in tool.inputSchema.items()
                                if k not in ["additionalProperties", "$schema"]
                            },
                        }
                    ]
                )
                for tool in mcp_tools.tools
            ]
            # print(f"tools:{tools}")
            # with open("./mcp_jira2.json", "w+") as f:
            #     content = {}
            #     for tool in mcp_tools.tools:
            #         content[tool.name] = {}
            #         for k, v in tool.inputSchema.items():
            #             if k not in ["additionalProperties", "$schema"]:
            #                 content[tool.name][k] = remove_properties_default(
            #                     k, v)
            #     content = json.dumps(content)
            #     f.write(content)
            #     return

            # Send request to the model with MCP function declarations
            response = client.models.generate_content(
                model=model_str,
                contents=prompt,
                config=types.GenerateContentConfig(
                    temperature=0,
                    tools=tools,
                ),
            )
            print(f"response:{response.text}")

            # Check for a function call
            if response.candidates[0].content.parts[0].function_call:
                function_call = response.candidates[0].content.parts[0].function_call
                print(f"fuction_call:{function_call}")
                # Call the MCP server with the predicted tool
                result = await session.call_tool(
                    function_call.name, arguments=function_call.args
                )
                print(result.content[0].text)
                # Continue as shown in step 4 of "How Function Calling Works"
                # and create a user friendly response
            else:
                print("No function call found in the response.")
                print(response.text)


# Start the asyncio event loop and run the main function
asyncio.run(run())
