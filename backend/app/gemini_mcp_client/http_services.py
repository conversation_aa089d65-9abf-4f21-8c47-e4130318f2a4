from flask import Flask, request, jsonify
import logging
from mcp_logic import <PERSON><PERSON>unner
from mcp_manage_session import MCPSessionManager
from google import genai
import logging_cfg
from mcp.client.stdio import StdioServerParameters
import asyncio

app = Flask(__name__)

# 配置日志
logging_cfg.log_setting(
    log_file="", log_level=logging.INFO, backup_cnt=5, is_init=False
)


@app.route("/api/v1/gemini_mcp", methods=["POST"])
async def gemini_mcp():
    """
    调用 Gemini LLM 和 MCP，并返回结果。moego-tools调用它。
    """
    data = request.get_json()
    model_type = data.get("model_type")
    init_prompt = data.get("init_prompt")
    mcp_servers_config_from_request = data.get("mcp_servers_config")
    api_key = data.get("api_key")

    if not all([model_type, init_prompt, api_key, mcp_servers_config_from_request]):
        return jsonify({"error": "Missing parameters"}), 400

    mcp_servers_config = {}
    for server_name, config in mcp_servers_config_from_request.items():
        mcp_servers_config[server_name] = StdioServerParameters(
            command=config["command"], args=config["args"], env=config.get("env")
        )

    try:
        client = genai.Client(api_key=api_key)
        logic_runner = LogicRunner(model_type, init_prompt, client)

        session_manager = MCPSessionManager(mcp_servers_config, logic_runner)
        dialogue_history = await session_manager.start_sessions_and_run()
        # print the last dialogue history
        if len(dialogue_history) >= 1:
            final_dialogue_history = dialogue_history[len(dialogue_history) - 1]
        else:
            final_dialogue_history = "Cannot find any dialogues"
        logging.info(f"final text responsed by gemini: {final_dialogue_history}")
        return jsonify(
            {"result": final_dialogue_history, "dialogues": dialogue_history}
        )
    except asyncio.TimeoutError:
        logging.error("Gemini MCP timed out")
        return jsonify({"error": "Gemini MCP timed out"}), 500
    except Exception as e:
        logging.error(f"Error in gemini_mcp: {e}")
        return jsonify({"error": str(e)}), 500


if __name__ == "__main__":
    app.run(debug=True, host="0.0.0.0", port=8080)
