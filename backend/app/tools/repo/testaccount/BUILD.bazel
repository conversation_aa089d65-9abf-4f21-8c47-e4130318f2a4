load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "testaccount",
    srcs = ["test_account.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/tools/repo/testaccount",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/tools/repo/testaccount/entity",
        "//backend/common/rpc/database/gorm",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
    ],
)
