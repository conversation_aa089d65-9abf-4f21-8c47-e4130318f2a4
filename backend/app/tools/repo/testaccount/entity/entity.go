package testaccount

import (
	"time"
)

type TestAccount struct {
	ID         int64  `json:"id" gorm:"column:id"`
	Email      string `json:"email" gorm:"column:email"`
	Password   string `json:"password" gorm:"column:password"`
	Occupied   bool   `json:"occupied" gorm:"column:occupied"`
	Owner      string `json:"owner" gorm:"column:owner"`
	Attributes *JSONB `json:"attributes" gorm:"column:attributes;type:jsonb"`
}

func (TestAccount) TableName() string {
	return "moego_tools.public.test_account"
}

type LeaseContract struct {
	ID            int64      `json:"id" gorm:"column:id"`
	TestAccountID int64      `json:"test_account_id" gorm:"column:test_account_id"`
	Borrower      string     `json:"borrower" gorm:"column:borrower"`
	BorrowTime    time.Time  `json:"borrow_time" gorm:"column:borrow_time"`
	ReturnTime    *time.Time `json:"return_time" gorm:"column:return_time"`
	DueTime       *time.Time `json:"due_time" gorm:"column:due_time"`
	Shared        bool       `json:"shared" gorm:"column:shared"`
}

func (LeaseContract) TableName() string {
	return "moego_tools.public.lease_contract"
}
