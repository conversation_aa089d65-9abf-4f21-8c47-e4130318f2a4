package testaccount

import (
	"fmt"

	"github.com/bytedance/sonic"
	"gorm.io/gorm"

	toolspb "github.com/MoeGolibrary/moego/backend/proto/tools/v1"
)

type Attributes struct {
	RegionCode            *string `json:"region_code,omitempty"`
	EnableBoardingDaycare *bool   `json:"enable_boarding_daycare,omitempty"`
	EnableOnlineBooking   *bool   `json:"enable_online_booking,omitempty"`
	EnableStripe          *bool   `json:"enable_stripe,omitempty"`
	HasSmsCredit          *bool   `json:"has_sms_credit,omitempty"`
	HasEmailCredit        *bool   `json:"has_email_credit,omitempty"`
}

func FromProto(attributes *toolspb.Attributes) *Attributes {
	if attributes == nil {
		return &Attributes{}
	}

	data, err := sonic.Marshal(attributes)
	if err != nil {
		// FIXME: use log
		fmt.Println("failed to marshal attributes from proto", err)

		return nil
	}

	var attrs Attributes
	if err := sonic.Unmarshal(data, &attrs); err != nil {
		// FIXME: use log
		fmt.Println("failed to unmarshal attributes to struct", err)

		return nil
	}

	return &attrs
}

func ToProto(attributes *Attributes) *toolspb.Attributes {
	data, err := sonic.Marshal(attributes)
	if err != nil {
		// FIXME: use log
		fmt.Println("failed to marshal attributes to proto", err)

		return nil
	}

	var protoAttrs toolspb.Attributes
	if err := sonic.Unmarshal(data, &protoAttrs); err != nil {
		// FIXME: use log
		fmt.Println("failed to unmarshal attributes to proto struct", err)

		return nil
	}

	return &protoAttrs
}

func FromJSONB(attributes *JSONB) *Attributes {
	json, err := sonic.Marshal(attributes)

	if err != nil {
		// FIXME: use log
		fmt.Println("failed to marshal attributes", err)

		return nil
	}

	a := &Attributes{}
	if err := sonic.Unmarshal(json, a); err != nil {
		// FIXME: use log
		fmt.Println("failed to unmarshal attributes", err)

		return nil
	}

	return a
}

func (a *Attributes) GetRegionCode() string {
	if a != nil && a.RegionCode != nil {
		return *a.RegionCode
	}

	return ""
}

func (a *Attributes) GetEnableBoardingDaycare() bool {
	if a != nil && a.EnableBoardingDaycare != nil {
		return *a.EnableBoardingDaycare
	}

	return false
}

func (a *Attributes) GetEnableOnlineBooking() bool {
	if a != nil && a.EnableOnlineBooking != nil {
		return *a.EnableOnlineBooking
	}

	return false
}

func (a *Attributes) GetEnableStripe() bool {
	if a != nil && a.EnableStripe != nil {
		return *a.EnableStripe
	}

	return false
}

func (a *Attributes) GetHasSmsCredit() bool {
	if a != nil && a.HasSmsCredit != nil {
		return *a.HasSmsCredit
	}

	return false
}

func (a *Attributes) GetHasEmailCredit() bool {
	if a != nil && a.HasEmailCredit != nil {
		return *a.HasEmailCredit
	}

	return false
}

func (a *Attributes) Filter(tx *gorm.DB) *gorm.DB {
	if a == nil {
		return tx
	}

	tx = a.stringFilter(tx, "region_code", a.RegionCode)
	tx = a.booleanFilter(tx, "enable_boarding_daycare", a.EnableBoardingDaycare)
	tx = a.booleanFilter(tx, "enable_online_booking", a.EnableOnlineBooking)
	tx = a.booleanFilter(tx, "enable_stripe", a.EnableStripe)
	tx = a.booleanFilter(tx, "has_sms_credit", a.HasSmsCredit)
	tx = a.booleanFilter(tx, "has_email_credit", a.HasEmailCredit)

	return tx
}

func (a *Attributes) booleanFilter(tx *gorm.DB, key string, value *bool) *gorm.DB {
	if value == nil {
		return tx
	}
	v := *value
	// when value is true:  attributes->>'xxx' = true
	// when value is false: attributes->>'xxx' = false OR attributes->'xxx' IS NULL (equals false or not exists)
	query := fmt.Sprintf("attributes->>'%s' = '%t'", key, v)
	if !v {
		query += fmt.Sprintf(" OR attributes->'%s' IS NULL", key)
	}

	return tx.Where(query)
}

func (a *Attributes) stringFilter(tx *gorm.DB, key string, value *string) *gorm.DB {
	if value == nil {
		return tx
	}
	v := *value
	// when value is not empty:  attributes->>'xxx' = 'yyy'
	// when value is empty:     attributes->>'xxx' = '' OR attributes->'xxx' IS NULL (equals empty or not exists)
	query := fmt.Sprintf("attributes->>'%s' = '%s'", key, v)
	if len(v) == 0 {
		query += fmt.Sprintf(" OR attributes->'%s' IS NULL", key)
	}

	return tx.Where(query)
}
