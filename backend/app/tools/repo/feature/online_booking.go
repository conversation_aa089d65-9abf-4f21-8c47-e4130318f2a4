package feature

import (
	"context"
	"io"
	"net/http"

	"github.com/bytedance/sonic"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type OnlineBookingParams struct {
	Ctx     context.Context
	Cookies []*http.Cookie
}

func (ob *OnlineBookingParams) Do() error {
	// 1. get setting info, 触发懒加载初始化
	if err := ob.getSettingInfo(); err != nil {
		return err
	}

	// 2. set published
	if err := ob.setPublished(); err != nil {
		return err
	}

	// 3. set enable
	if err := ob.setEnable(); err != nil {
		return err
	}

	return nil
}

func (ob *OnlineBookingParams) getSettingInfo() error {
	resp, err := NewHTTPRequest(
		http.MethodGet,
		"/api/grooming/bookOnline/setting/info?withoutClientNotification=true",
		nil,
		ob.Cookies,
	).Send(ob.Ctx)

	if err != nil {
		return err
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	log.Infof("ob setting response: %s", string(bodyBytes))
	// todo: response.code 400 check
	return nil
}

func (ob *OnlineBookingParams) setEnable() error {
	// 构建请求体
	params := map[string]interface{}{"isEnable": "1"}
	body, err := sonic.Marshal(params)
	if err != nil {
		return err
	}

	resp, err := NewHTTPRequest(
		http.MethodPut,
		"/api/grooming/bookOnline/setting/info",
		body,
		ob.Cookies,
	).Send(ob.Ctx)

	if err != nil {
		return err
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	log.Infof("ob enable response: %s", string(bodyBytes))

	return nil
}

func (ob *OnlineBookingParams) setPublished() error {
	// 构建请求体
	params := map[string]interface{}{"isPublished": "true"}
	body, err := sonic.Marshal(params)
	if err != nil {
		return err
	}

	resp, err := NewHTTPRequest(
		http.MethodPost,
		"/api/grooming/ob/v2/business/config",
		body,
		ob.Cookies,
	).Send(ob.Ctx)

	if err != nil {
		return err
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	log.Infof("ob published response: %s", string(bodyBytes))

	return nil
}
