package feature

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"strconv"

	toolsutils "github.com/MoeGolibrary/moego/backend/app/tools/utils"
)

type HTTPRequest struct {
	method  string
	path    string
	body    []byte
	cookies []*http.Cookie
}

func NewHTTPRequest(method, path string, body []byte, cookies []*http.Cookie) *HTTPRequest {
	return &HTTPRequest{
		method:  method,
		path:    path,
		body:    body,
		cookies: cookies,
	}
}

func (r *HTTPRequest) Send(ctx context.Context) (*http.Response, error) {

	url := "https://go.t2.moego.dev" + r.path

	req, err := http.NewRequest(r.method, url, bytes.NewBuffer(r.body))
	if err != nil {
		return nil, err
	}

	// set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", toolsutils.GetUserAgent(ctx))

	// set cookies
	for _, cookie := range r.cookies {
		req.AddCookie(cookie)
	}

	resp, err := toolsutils.HTTPClient.Do(req)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	moeStatusCode, _ := strconv.Atoi(resp.Header.Get("X-MOE-STATUS"))
	if moeStatusCode != 0 {
		return nil, fmt.Errorf("unexpected moe status code: %d", moeStatusCode)
	}

	return resp, nil
}
