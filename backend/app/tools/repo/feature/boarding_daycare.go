package feature

import (
	"context"
	"fmt"
	"io"
	"net/http"

	"google.golang.org/protobuf/encoding/protojson"

	offeringapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1"
	organizationapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/organization/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	metadatasvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/metadata/v1"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
)

const (
	boardingDaycare string = "allow_boarding_and_daycare"
)

var protoUnmarshaler = protojson.UnmarshalOptions{DiscardUnknown: true}

type BoadingDaycareParams struct {
	Ctx            context.Context
	CompanyID      int64
	BusinessID     int64
	Cookies        []*http.Cookie
	MetadataClient metadatasvcpb.MetadataServiceClient
}

func (bd *BoadingDaycareParams) Do() error {
	// 1. add whitelist
	if err := bd.AddWhiteList(); err != nil {
		return err
	}

	// 2. create logging type
	loggingTypeID, err := bd.CreateLoggingType()
	if err != nil {
		return err
	}

	// 3. create lodging unit
	if err := bd.CreateLodgingUnit(loggingTypeID); err != nil {
		return err
	}

	// 4. get taxID
	taxID, err := bd.GetTaxID()
	if err != nil {
		return err
	}

	// 5. create services
	if err := bd.CreateServices(taxID); err != nil {
		return err
	}

	return nil
}

func (bd *BoadingDaycareParams) AddWhiteList() error {

	// get keyID
	getReq := &metadatasvcpb.GetKeyRequest{
		Identifier: &metadatasvcpb.GetKeyRequest_Name{Name: boardingDaycare},
	}

	getResp, err := bd.MetadataClient.GetKey(bd.Ctx, getReq)
	if err != nil {
		return err
	}
	keyID := getResp.GetKey().GetId()

	// update value
	updateReq := &metadatasvcpb.UpdateValueRequest{
		KeyId:   keyID,
		OwnerId: bd.CompanyID,
		Value:   pointer.Get("true"),
		OperatorIdentifier: &metadatasvcpb.UpdateValueRequest_InternalOperatorId{
			InternalOperatorId: "platform-tools",
		},
	}

	_, err = bd.MetadataClient.UpdateValue(bd.Ctx, updateReq)

	return err
}

func (bd *BoadingDaycareParams) CreateLoggingType() (int64, error) {

	params := &offeringapipb.CreateLodgingTypeParams{
		Name:          "Lodging type1",
		Description:   "",
		MaxPetNum:     3, //nolint:mnd
		PetSizeFilter: false,
	}

	body, err := protojson.Marshal(params)
	if err != nil {
		return 0, err
	}

	resp, err := NewHTTPRequest(
		http.MethodPost,
		"/moego.api.offering.v1.LodgingTypeService/CreateLodgingType",
		body,
		bd.Cookies,
	).Send(bd.Ctx)

	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, err
	}

	result := &offeringapipb.CreateLodgingTypeResult{}
	if err := protoUnmarshaler.Unmarshal(respBody, result); err != nil {
		return 0, err
	}

	id := result.GetLodgingType().GetId()

	return id, nil
}

func (bd *BoadingDaycareParams) CreateLodgingUnit(loggingTypeID int64) error {

	params := &offeringapipb.CreateLodgingUnitParams{
		LodgingTypeId: loggingTypeID,
		Name:          []string{"Room 1", "Room 2", "Room 3"},
		BusinessId:    bd.BusinessID,
	}

	body, err := protojson.Marshal(params)
	if err != nil {
		return err
	}

	resp, err := NewHTTPRequest(
		http.MethodPost,
		"/moego.api.offering.v1.LodgingUnitService/CreateLodgingUnit",
		body,
		bd.Cookies,
	).Send(bd.Ctx)

	if err != nil {
		return err
	}
	defer resp.Body.Close()

	return nil
}

func (bd *BoadingDaycareParams) GetTaxID() (int64, error) {

	resp, err := NewHTTPRequest(
		http.MethodPost,
		"/moego.api.organization.v1.CompanyService/GetTaxRuleList",
		nil,
		bd.Cookies,
	).Send(bd.Ctx)

	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, err
	}

	result := &organizationapipb.GetTaxRuleListResult{}
	if err := protojson.Unmarshal(respBody, result); err != nil {
		return 0, err
	}

	taxList := result.GetTaxRule()
	if len(taxList) == 0 {
		return 0, fmt.Errorf("tax list is empty")
	}

	return taxList[0].GetId(), nil
}

type service struct {
	name                    string
	description             string
	price                   float64
	priceUnit               offeringpb.ServicePriceUnit
	serviceItemType         offeringpb.ServiceItemType
	color                   string
	maxDuration             *int32
	requireDedicatedLodging bool
}

//nolint:mnd
var services = []service{
	{
		name:                    "Boarding service - small",
		description:             "Boarding service description.",
		price:                   90,
		priceUnit:               offeringpb.ServicePriceUnit_PER_NIGHT,
		serviceItemType:         offeringpb.ServiceItemType_BOARDING,
		color:                   "#7AC5FF",
		requireDedicatedLodging: true,
	},
	{
		name:                    "Boarding service - medium",
		description:             "Boarding service description.",
		price:                   100,
		priceUnit:               offeringpb.ServicePriceUnit_PER_NIGHT,
		serviceItemType:         offeringpb.ServiceItemType_BOARDING,
		color:                   "#7AC5FF",
		requireDedicatedLodging: true,
	},
	{
		name:                    "Boarding service - large",
		description:             "Boarding service description.",
		price:                   120,
		priceUnit:               offeringpb.ServicePriceUnit_PER_NIGHT,
		serviceItemType:         offeringpb.ServiceItemType_BOARDING,
		color:                   "#7AC5FF",
		requireDedicatedLodging: true,
	},
	{
		name:                    "Daycare service - small",
		description:             "Daycare service description.",
		price:                   90,
		priceUnit:               offeringpb.ServicePriceUnit_PER_SESSION,
		serviceItemType:         offeringpb.ServiceItemType_DAYCARE,
		color:                   "#FFB865",
		maxDuration:             pointer.Get(int32(360)),
		requireDedicatedLodging: false,
	},
	{
		name:                    "Daycare service - medium",
		description:             "Daycare service description.",
		price:                   100,
		priceUnit:               offeringpb.ServicePriceUnit_PER_SESSION,
		serviceItemType:         offeringpb.ServiceItemType_DAYCARE,
		color:                   "#FFB865",
		maxDuration:             pointer.Get(int32(540)),
		requireDedicatedLodging: false,
	},
	{
		name:                    "Daycare service - large",
		description:             "Daycare service description.",
		price:                   120,
		priceUnit:               offeringpb.ServicePriceUnit_PER_SESSION,
		serviceItemType:         offeringpb.ServiceItemType_DAYCARE,
		color:                   "#FFB865",
		maxDuration:             pointer.Get(int32(720)),
		requireDedicatedLodging: false,
	},
}

func (bd *BoadingDaycareParams) CreateServices(taxID int64) error {
	serviceType := offeringpb.ServiceType_SERVICE

	for _, service := range services {
		params := &offeringapipb.CreateServiceParams{
			Service: &offeringpb.CreateServiceDef{
				Name:                    service.name,
				Description:             &service.description,
				TaxId:                   taxID,
				Inactive:                false,
				IsAllLocation:           true,
				AvailableForAllStaff:    true,
				CategoryId:              pointer.Get(int64(0)),
				Type:                    &serviceType,
				ColorCode:               &service.color,
				RequireDedicatedLodging: service.requireDedicatedLodging,
				Price:                   service.price,
				PriceUnit:               service.priceUnit,
				PetCodeFilter: &offeringpb.CreateServiceDef_PetCodeFilter{
					IsAllPetCode: true,
					IsWhiteList:  true,
				},
				ServiceItemType: &service.serviceItemType,
				MaxDuration:     service.maxDuration,
			},
		}

		body, err := protojson.Marshal(params)
		if err != nil {
			return err
		}
		resp, err := NewHTTPRequest(
			http.MethodPost,
			"/moego.api.offering.v1.ServiceManagementService/CreateService",
			body,
			bd.Cookies,
		).Send(bd.Ctx)
		if err != nil {
			return err
		}
		defer resp.Body.Close()
	}

	return nil
}
