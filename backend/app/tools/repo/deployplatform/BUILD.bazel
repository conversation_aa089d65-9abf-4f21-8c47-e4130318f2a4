load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "deployplatform",
    srcs = ["deploy_platform.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/tools/repo/deployplatform/entity",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/framework/log",
        "@io_gorm_gorm//:gorm",
    ],
)

go_test(
    name = "deployplatform_test",
    srcs = ["deploy_platform_test.go"],
    embed = [":deployplatform"],
    deps = [
        "//backend/app/tools/repo/deployplatform/entity",
        "//backend/app/tools/utils",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/framework",
        "@com_github_samber_lo//:lo",
        "@com_github_stretchr_testify//suite",
        "@io_gorm_gorm//:gorm",
    ],
)
