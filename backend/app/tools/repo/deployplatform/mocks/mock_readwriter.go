// Code generated by mockery v2.53.0. DO NOT EDIT.

package mocks

import (
	context "context"

	entity "github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/entity"

	mock "github.com/stretchr/testify/mock"

	time "time"
)

// ReadWriter is an autogenerated mock type for the ReadWriter type
type ReadWriter struct {
	mock.Mock
}

// CreateDeployLog provides a mock function with given fields: ctx, taskID, logType, state, phaseType, message
func (_m *ReadWriter) CreateDeployLog(ctx context.Context, taskID int64, logType string, state string, phaseType string, message string) error {
	ret := _m.Called(ctx, taskID, logType, state, phaseType, message)

	if len(ret) == 0 {
		panic("no return value specified for CreateDeployLog")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, string, string, string, string) error); ok {
		r0 = rf(ctx, taskID, logType, state, phaseType, message)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreatePhaseAndUpdateTask provides a mock function with given fields: ctx, task, phaseType, phaseName, phaseState, message
func (_m *ReadWriter) CreatePhaseAndUpdateTask(ctx context.Context, task *entity.DeployTask, phaseType string, phaseName string, phaseState string, message string) error {
	ret := _m.Called(ctx, task, phaseType, phaseName, phaseState, message)

	if len(ret) == 0 {
		panic("no return value specified for CreatePhaseAndUpdateTask")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.DeployTask, string, string, string, string) error); ok {
		r0 = rf(ctx, task, phaseType, phaseName, phaseState, message)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteDeployTask provides a mock function with given fields: ctx, taskID
func (_m *ReadWriter) DeleteDeployTask(ctx context.Context, taskID int64) error {
	ret := _m.Called(ctx, taskID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteDeployTask")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, taskID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EndDeployPhase provides a mock function with given fields: ctx, phase, message
func (_m *ReadWriter) EndDeployPhase(ctx context.Context, phase *entity.DeployPhase, message string) error {
	ret := _m.Called(ctx, phase, message)

	if len(ret) == 0 {
		panic("no return value specified for EndDeployPhase")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.DeployPhase, string) error); ok {
		r0 = rf(ctx, phase, message)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EndDeployTask provides a mock function with given fields: ctx, taskID, message
func (_m *ReadWriter) EndDeployTask(ctx context.Context, taskID int64, message string) error {
	ret := _m.Called(ctx, taskID, message)

	if len(ret) == 0 {
		panic("no return value specified for EndDeployTask")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, string) error); ok {
		r0 = rf(ctx, taskID, message)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetDeployPhase provides a mock function with given fields: ctx, taskID, phaseType
func (_m *ReadWriter) GetDeployPhase(ctx context.Context, taskID int64, phaseType string) (*entity.DeployPhase, error) {
	ret := _m.Called(ctx, taskID, phaseType)

	if len(ret) == 0 {
		panic("no return value specified for GetDeployPhase")
	}

	var r0 *entity.DeployPhase
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, string) (*entity.DeployPhase, error)); ok {
		return rf(ctx, taskID, phaseType)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, string) *entity.DeployPhase); ok {
		r0 = rf(ctx, taskID, phaseType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.DeployPhase)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, string) error); ok {
		r1 = rf(ctx, taskID, phaseType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDeployTaskByID provides a mock function with given fields: ctx, taskID
func (_m *ReadWriter) GetDeployTaskByID(ctx context.Context, taskID int64) (*entity.DeployTask, error) {
	ret := _m.Called(ctx, taskID)

	if len(ret) == 0 {
		panic("no return value specified for GetDeployTaskByID")
	}

	var r0 *entity.DeployTask
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*entity.DeployTask, error)); ok {
		return rf(ctx, taskID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *entity.DeployTask); ok {
		r0 = rf(ctx, taskID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.DeployTask)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, taskID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDeployTaskByName provides a mock function with given fields: ctx, name
func (_m *ReadWriter) GetDeployTaskByName(ctx context.Context, name string) (*entity.DeployTask, error) {
	ret := _m.Called(ctx, name)

	if len(ret) == 0 {
		panic("no return value specified for GetDeployTaskByName")
	}

	var r0 *entity.DeployTask
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entity.DeployTask, error)); ok {
		return rf(ctx, name)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entity.DeployTask); ok {
		r0 = rf(ctx, name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.DeployTask)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDeployTasksNotEnded provides a mock function with given fields: ctx, after
func (_m *ReadWriter) GetDeployTasksNotEnded(ctx context.Context, after time.Time) ([]*entity.DeployTask, error) {
	ret := _m.Called(ctx, after)

	if len(ret) == 0 {
		panic("no return value specified for GetDeployTasksNotEnded")
	}

	var r0 []*entity.DeployTask
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Time) ([]*entity.DeployTask, error)); ok {
		return rf(ctx, after)
	}
	if rf, ok := ret.Get(0).(func(context.Context, time.Time) []*entity.DeployTask); ok {
		r0 = rf(ctx, after)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.DeployTask)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, time.Time) error); ok {
		r1 = rf(ctx, after)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetOrCreateDeployTask provides a mock function with given fields: ctx, name, title, description, createdBy, phaseType, phaseName, message
func (_m *ReadWriter) GetOrCreateDeployTask(ctx context.Context, name string, title string, description string, createdBy string, phaseType string, phaseName string, message string) (*entity.DeployTask, error) {
	ret := _m.Called(ctx, name, title, description, createdBy, phaseType, phaseName, message)

	if len(ret) == 0 {
		panic("no return value specified for GetOrCreateDeployTask")
	}

	var r0 *entity.DeployTask
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string, string, string, string) (*entity.DeployTask, error)); ok {
		return rf(ctx, name, title, description, createdBy, phaseType, phaseName, message)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string, string, string, string) *entity.DeployTask); ok {
		r0 = rf(ctx, name, title, description, createdBy, phaseType, phaseName, message)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.DeployTask)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string, string, string, string) error); ok {
		r1 = rf(ctx, name, title, description, createdBy, phaseType, phaseName, message)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPhasesAndLogs provides a mock function with given fields: ctx, task
func (_m *ReadWriter) GetPhasesAndLogs(ctx context.Context, task *entity.DeployTask) ([]*entity.DeployPhase, []*entity.DeployLog, error) {
	ret := _m.Called(ctx, task)

	if len(ret) == 0 {
		panic("no return value specified for GetPhasesAndLogs")
	}

	var r0 []*entity.DeployPhase
	var r1 []*entity.DeployLog
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.DeployTask) ([]*entity.DeployPhase, []*entity.DeployLog, error)); ok {
		return rf(ctx, task)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.DeployTask) []*entity.DeployPhase); ok {
		r0 = rf(ctx, task)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.DeployPhase)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.DeployTask) []*entity.DeployLog); ok {
		r1 = rf(ctx, task)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]*entity.DeployLog)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context, *entity.DeployTask) error); ok {
		r2 = rf(ctx, task)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// ListDeployTasks provides a mock function with given fields: ctx, pageSize, pageNum
func (_m *ReadWriter) ListDeployTasks(ctx context.Context, pageSize int, pageNum int) ([]*entity.DeployTask, int64, int64, error) {
	ret := _m.Called(ctx, pageSize, pageNum)

	if len(ret) == 0 {
		panic("no return value specified for ListDeployTasks")
	}

	var r0 []*entity.DeployTask
	var r1 int64
	var r2 int64
	var r3 error
	if rf, ok := ret.Get(0).(func(context.Context, int, int) ([]*entity.DeployTask, int64, int64, error)); ok {
		return rf(ctx, pageSize, pageNum)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, int) []*entity.DeployTask); ok {
		r0 = rf(ctx, pageSize, pageNum)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.DeployTask)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, int) int64); ok {
		r1 = rf(ctx, pageSize, pageNum)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int, int) int64); ok {
		r2 = rf(ctx, pageSize, pageNum)
	} else {
		r2 = ret.Get(2).(int64)
	}

	if rf, ok := ret.Get(3).(func(context.Context, int, int) error); ok {
		r3 = rf(ctx, pageSize, pageNum)
	} else {
		r3 = ret.Error(3)
	}

	return r0, r1, r2, r3
}

// UpdatePhase provides a mock function with given fields: ctx, phase, message
func (_m *ReadWriter) UpdatePhase(ctx context.Context, phase *entity.DeployPhase, message string) error {
	ret := _m.Called(ctx, phase, message)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePhase")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.DeployPhase, string) error); ok {
		r0 = rf(ctx, phase, message)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdatePhaseAndTaskState provides a mock function with given fields: ctx, phase, state, message
func (_m *ReadWriter) UpdatePhaseAndTaskState(ctx context.Context, phase *entity.DeployPhase, state string, message string) error {
	ret := _m.Called(ctx, phase, state, message)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePhaseAndTaskState")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.DeployPhase, string, string) error); ok {
		r0 = rf(ctx, phase, state, message)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateTask provides a mock function with given fields: ctx, task, message
func (_m *ReadWriter) UpdateTask(ctx context.Context, task *entity.DeployTask, message string) error {
	ret := _m.Called(ctx, task, message)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTask")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.DeployTask, string) error); ok {
		r0 = rf(ctx, task, message)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewReadWriter creates a new instance of ReadWriter. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewReadWriter(t interface {
	mock.TestingT
	Cleanup(func())
}) *ReadWriter {
	mock := &ReadWriter{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
