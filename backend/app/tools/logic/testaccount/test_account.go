package testaccount

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"time"

	metadatasvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/metadata/v1"
	logicentity "github.com/MoeGolibrary/moego/backend/app/tools/logic/testaccount/entity"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/account"
	accountentity "github.com/MoeGolibrary/moego/backend/app/tools/repo/account/entity"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/feature"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/organization"
	repo "github.com/MoeGolibrary/moego/backend/app/tools/repo/testaccount"
	repoentity "github.com/MoeGolibrary/moego/backend/app/tools/repo/testaccount/entity"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
)

const (
	DefaultRegionCode = "US"
)

var (
	SmsSupportedRegionCodes = []string{"US", "CA", "GB", "AU"}
)

type Logic struct {
	testAccount  repo.ReadWriter
	account      account.ReadWriter
	organization organization.ReadWriter
	metadata     metadatasvcpb.MetadataServiceClient
}

func NewLogic() *Logic {
	metadata := grpc.NewClient("moego-svc-metadata", metadatasvcpb.NewMetadataServiceClient)

	return &Logic{
		testAccount:  repo.New(),
		account:      account.New(),
		organization: organization.New(),
		metadata:     metadata,
	}
}

func (l *Logic) BorrowTestAccount(ctx context.Context,
	params *logicentity.BorrowParams) (*logicentity.BorrowResult, error) {
	var testAccount *repoentity.TestAccount
	var contract *repoentity.LeaseContract

	err := l.testAccount.Tx(ctx, func(txRW repo.ReadWriter) error {
		var err error
		switch {
		case len(params.Email) > 0:
			testAccount, err = txRW.GetForUpdateByEmail(ctx, params.Email)
		case params.ID > 0:
			testAccount, err = txRW.GetForUpdateByID(ctx, params.ID)
		default:
			testAccount, err = txRW.GetForUpdate(ctx, params.Attributes)
		}

		if err != nil {
			return err
		}

		// check owner
		if len(testAccount.Owner) > 0 && testAccount.Owner != params.Borrower {
			return errors.New("the account does not belong to the borrower")
		}

		contract, err = txRW.Occupy(ctx, testAccount.ID, params.Borrower, params.Shared)

		return err
	})

	if err != nil {
		return nil, err
	}

	return &logicentity.BorrowResult{
		ID:         testAccount.ID,
		Email:      testAccount.Email,
		Password:   testAccount.Password,
		ContractID: contract.ID,
	}, nil
}

func (l *Logic) ReturnTestAccount(ctx context.Context, contractID int64) error {
	return l.testAccount.Return(ctx, contractID)
}

func (l *Logic) CreateTestAccount(ctx context.Context,
	params *logicentity.CreateParams) (*logicentity.TestAccount, error) {
	// 0. check attributes
	if err := l.checkAttributes(params.Attributes); err != nil {
		return nil, err
	}

	// 1. create account
	email := params.Email
	if len(email) == 0 {
		email = fmt.Sprintf("<EMAIL>", timestampString())
	}

	password := params.Password
	if len(password) == 0 {
		// TODO: use random password
		password = "AutoTest!123"
	}

	accountModel := &accountentity.Account{
		Email:     email,
		Password:  password,
		FirstName: "Auto",
		LastName:  "Test",
	}
	if err := l.account.CreateAccount(ctx, accountModel); err != nil {
		return nil, err
	}

	// 2. init attribute
	if err := l.initAttributes(ctx, params.Attributes, accountModel); err != nil {
		return nil, err
	}

	// 3. save as test account
	if !params.Disposable {
		attributes := &repoentity.JSONB{}
		if err := attributes.Marshal(params.Attributes); err != nil {
			return nil, err
		}

		if err := l.testAccount.Save(ctx, &repoentity.TestAccount{
			ID:         accountModel.ID,
			Email:      accountModel.Email,
			Password:   accountModel.Password,
			Owner:      params.Owner,
			Attributes: attributes,
		}); err != nil {
			return nil, err
		}
	}

	return &logicentity.TestAccount{
		ID:         accountModel.ID,
		Email:      accountModel.Email,
		Password:   accountModel.Password,
		Attributes: params.Attributes,
		Owner:      params.Owner,
		Occupied:   false,
	}, nil
}

func (l *Logic) ReleaseOverdueTestAccounts(ctx context.Context) error {
	return l.testAccount.ReleaseOverdueTestAccounts(ctx)
}

func (l *Logic) ListTestAccounts(ctx context.Context, pageSize int32, pageToken string) (
	[]*logicentity.TestAccount, *string, int64, error) {

	accounts, nextPageToken, total, err := l.testAccount.ListTestAccounts(ctx, pageSize, pageToken)
	if err != nil {
		return nil, nil, 0, err
	}

	list := make([]*logicentity.TestAccount, 0, len(accounts))
	for _, account := range accounts {
		a := &logicentity.TestAccount{}
		a.UnmarshalFromEntity(account)
		list = append(list, a)
	}

	return list, nextPageToken, total, nil
}

func (l *Logic) checkAttributes(attributes *repoentity.Attributes) error {
	// get region code, default to US
	regionCode := attributes.GetRegionCode()
	if len(regionCode) == 0 {
		// default to US
		regionCode = DefaultRegionCode
		attributes.RegionCode = &regionCode
	}

	if attributes.GetHasSmsCredit() && !slices.Contains(SmsSupportedRegionCodes, regionCode) {
		return fmt.Errorf("sms credit is not supported in %s", regionCode)
	}

	return nil
}

func (l *Logic) initAttributes(ctx context.Context,
	attributes *repoentity.Attributes, account *accountentity.Account) error {

	// init company
	companyID, businessID, err := l.organization.InitCompany(ctx, account, attributes.GetRegionCode())
	if err != nil {
		return err
	}

	// update credits
	if err := l.updateCredits(ctx, companyID, attributes); err != nil {
		return err
	}

	cookies, err := l.account.Login(ctx, account)
	if err != nil {
		return err
	}

	featureCtx := &feature.Context{
		Ctx:        ctx,
		CompanyID:  companyID,
		BusinessID: businessID,
		Cookies:    cookies,
	}

	// enable boarding daycare feature
	if err := l.enableBoardingDaycare(featureCtx, attributes); err != nil {
		return err
	}

	// enable online booking
	if err := l.enableOnlineBooking(featureCtx, attributes); err != nil {
		return err
	}

	return nil
}

func (l *Logic) updateCredits(ctx context.Context, companyID int64, attributes *repoentity.Attributes) error {
	hasSmsCredit := attributes.GetHasSmsCredit()
	hasEmailCredit := attributes.GetHasEmailCredit()
	if !hasSmsCredit && !hasEmailCredit {
		return nil
	}

	return l.organization.UpdateSmsAndEmailCredit(ctx, companyID, hasSmsCredit, hasEmailCredit)
}

func (l *Logic) enableBoardingDaycare(featureCtx *feature.Context, attributes *repoentity.Attributes) error {
	if !attributes.GetEnableBoardingDaycare() {
		return nil
	}

	params := &feature.BoadingDaycareParams{
		Ctx:            featureCtx.Ctx,
		CompanyID:      featureCtx.CompanyID,
		BusinessID:     featureCtx.BusinessID,
		Cookies:        featureCtx.Cookies,
		MetadataClient: l.metadata,
	}

	return params.Do()
}

func (l *Logic) enableOnlineBooking(featureCtx *feature.Context, attributes *repoentity.Attributes) error {
	if !attributes.GetEnableOnlineBooking() {
		return nil
	}

	params := &feature.OnlineBookingParams{
		Ctx:     featureCtx.Ctx,
		Cookies: featureCtx.Cookies,
	}

	return params.Do()
}

func timestampString() string {
	return time.Now().Format("20060102150405")
}
