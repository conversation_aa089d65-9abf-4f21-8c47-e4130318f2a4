package state

import (
	"database/sql"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/entity"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/mocks"
)

type CanaryPhaseTestSuite struct {
	suite.Suite
}

func TestCanaryPhaseSuite(t *testing.T) {
	suite.Run(t, new(CanaryPhaseTestSuite))
}

func (t *CanaryPhaseTestSuite) TestNewCanaryPhase() {
	manager := &mocks.ReadWriter{}
	phase := t.getPhase(deployplatform.Init, deployplatform.Init, deployplatform.PhaseCanary, "{}", manager)

	manager.On("CreatePhaseAndUpdateTask", mock.Anything, mock.Anything,
		mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("CreatePhaseAndUpdateTask run")
	})
	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.CurrentPhase = deployplatform.PhaseCanary
		phase.Task.State = deployplatform.Init
	}).Return(phase.Task, nil)
	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("GetPhasesAndLogs run")
	}).Return(phase.Phases, phase.Logs, nil)

	p := &CanaryPhase{
		Phase: phase,
	}
	t.NotNil(p)
	t.Equal(phase.Task.CurrentPhase, p.Task.CurrentPhase)
	t.Equal(phase.Task.ID, p.Task.ID)
	t.Equal(phase.Task.State, p.Task.State)
}

func (t *CanaryPhaseTestSuite) getPhase(taskType, phaseType, currentPhase string, parameters string, repo deployplatform.ReadWriter) Phase {
	return Phase{
		Task: &entity.DeployTask{
			ID:           112233,
			State:        taskType,
			CurrentPhase: currentPhase,
			Name:         "test",
			Title:        "123456",
			CreatedBy:    "test",
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
			Parameters:   entity.JSONB(`{}`),
		},
		Phases: []*entity.DeployPhase{
			&entity.DeployPhase{
				ID:           112233,
				DeployTaskID: 112233,
				Type:         deployplatform.PhaseCI,
				State:        deployplatform.Succeeded,
				StartedAt:    time.Now(),
				EndedAt:      sql.NullTime{Time: time.Now(), Valid: true},
				Parameters:   entity.JSONB(`{}`),
			},
			&entity.DeployPhase{
				ID:           112233,
				DeployTaskID: 112233,
				Type:         currentPhase,
				State:        phaseType,
				StartedAt:    time.Now(),
				EndedAt:      sql.NullTime{Time: time.Now(), Valid: false},
				Parameters:   entity.JSONB(parameters),
			},
		},
		Logs: []*entity.DeployLog{
			&entity.DeployLog{
				ID:           112233,
				DeployTaskID: 112233,
				LogTime:      time.Now(),
				Type:         "CIInit",
				Message:      entity.JSONB(`{}`),
				State:        deployplatform.Init,
				Phase:        deployplatform.PhaseCI,
			},
			&entity.DeployLog{
				ID:           112233,
				DeployTaskID: 112233,
				LogTime:      time.Now(),
				Type:         "CISuccess",
				Message:      entity.JSONB(`{}`),
				State:        deployplatform.Succeeded,
				Phase:        deployplatform.PhaseCanary,
			},
			&entity.DeployLog{
				ID:           112233,
				DeployTaskID: 112233,
				LogTime:      time.Now(),
				Type:         "CanaryInit",
				Message:      entity.JSONB(`{}`),
				State:        deployplatform.Init,
				Phase:        deployplatform.PhaseCanary,
			},
		},
		Repo: repo,
	}
}

func (t *CanaryPhaseTestSuite) getNewPhasesAndLogs(phase Phase) (newPhases []*entity.DeployPhase, newLogs []*entity.DeployLog) {
	newPhases = append(newPhases, phase.Phases...)
	newPhases = append(newPhases, &entity.DeployPhase{
		DeployTaskID: phase.Task.ID,
		Type:         deployplatform.PhaseFullDeployment,
		State:        deployplatform.Init,
		Parameters:   entity.JSONB(`{}`),
		StartedAt:    time.Now(),
	})
	newLogs = append(newLogs, phase.Logs...)
	newLogs = append(newLogs, &entity.DeployLog{
		DeployTaskID: phase.Task.ID,
		LogTime:      time.Now(),
		Type:         "CreatePhase",
		State:        deployplatform.Init,
		Phase:        deployplatform.PhaseFullDeployment,
		Message: entity.JSONB(fmt.Sprintf(`{
			"taskID": %v,
			"phase": "%v",
			"status": "%v"
		}`, phase.Task.ID, deployplatform.PhaseFullDeployment, deployplatform.Init)),
	})
	return newPhases, newLogs
}

func (t *CanaryPhaseTestSuite) TestSuccess2FullDeploymentInit() {
	manager := &mocks.ReadWriter{}
	phase := t.getPhase(deployplatform.Succeeded, deployplatform.Succeeded, deployplatform.PhaseCanary, "{}", manager)

	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.CurrentPhase = deployplatform.PhaseCanary
		phase.Task.State = deployplatform.Succeeded
	}).Return(phase.Task, nil).Once()

	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("GetPhasesAndLogs run")
		phase.Phases[1].State = deployplatform.Succeeded
	}).Return(phase.Phases, phase.Logs, nil).Once()

	manager.On("EndDeployPhase", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Phases[1].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
	}).Return(nil)

	manager.On("CreatePhaseAndUpdateTask", mock.Anything, mock.Anything,
		mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("CreatePhaseAndUpdateTask run")
	}).Return(nil)

	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.CurrentPhase = deployplatform.PhaseFullDeployment
		phase.Task.State = deployplatform.Init
	}).Return(phase.Task, nil)
	newPhases, newLogs := t.getNewPhasesAndLogs(phase)

	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("GetPhasesAndLogs run")
	}).Return(newPhases, newLogs, nil)

	p := &CanaryPhase{
		Phase: phase,
	}
	got, err := p.Change(nil)
	t.NotNil(got)
	t.T().Logf("task:%v", got)
	t.Nil(err)
	currentPhase, err := got.GetCurrentPhase()
	t.T().Logf("currentPhase:%v", currentPhase)
	t.Nil(err)
	t.Equal(deployplatform.PhaseFullDeployment, currentPhase.Type)
	t.Equal(deployplatform.Init, got.StateType())
}

func (t *CanaryPhaseTestSuite) TestChange() {
	tests := []struct {
		name      string
		state     string
		params    []string
		wantState string
		wantErr   bool
	}{
		{
			name:      "init to running",
			state:     deployplatform.Init,
			params:    []string{"{}"},
			wantState: deployplatform.Running,
			wantErr:   false,
		},
		{
			name:  "running to failed",
			state: deployplatform.Running,
			params: []string{
				"{\"phase\": \"Failed\"}",
			},
			wantState: deployplatform.Failed,
			wantErr:   false,
		},
		{
			name:  "running to succeeded",
			state: deployplatform.Running,
			params: []string{
				"{\"phase\": \"Succeeded\"}",
			},
			wantState: deployplatform.Succeeded,
			wantErr:   false,
		},
		{
			name:  "running to end",
			state: deployplatform.Running,
			params: []string{
				"{\"phase\": \"Terminating\"}",
				"{\"phase\": \"Terminated\"}",
			},
			wantState: deployplatform.Rollback,
			wantErr:   false,
		},
		{
			name:  "running to waiting",
			state: deployplatform.Running,
			params: []string{
				"{\"phase\": \"Waiting\"}",
				"{\"phase\": \"WaitingPromotion\"}",
			},
			wantState: deployplatform.Waiting,
			wantErr:   false,
		},
		{
			name:  "waiting to running",
			state: deployplatform.Waiting,
			params: []string{
				"{\"phase\": \"Progressing\"}",
				"{\"phase\": \"Promoting\"}",
				"{\"phase\": \"Finalising\"}",
			},
			wantState: deployplatform.Running,
			wantErr:   false,
		},
		{
			name:  "running to skipped",
			state: deployplatform.Running,
			params: []string{
				"{\"rollback\": true}",
			},
			wantState: deployplatform.Skipped,
			wantErr:   false,
		},
		{
			name:  "running to rollback",
			state: deployplatform.Running,
			params: []string{
				"{\"skip\": true}",
			},
			wantState: deployplatform.Rollback,
			wantErr:   false,
		},
		{
			name:      "rollback to end",
			state:     deployplatform.Rollback,
			params:    []string{},
			wantState: deployplatform.End,
			wantErr:   false,
		},
		{
			name:      "failed to end",
			state:     deployplatform.Failed,
			params:    []string{},
			wantState: deployplatform.End,
			wantErr:   false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func() {
			for _, param := range tt.params {
				manager := &mocks.ReadWriter{}
				phase := t.getPhase(tt.state, tt.state, deployplatform.PhaseCanary, param, manager)

				manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					phase.Task.CurrentPhase = deployplatform.PhaseCanary
					phase.Task.State = tt.state
				}).Return(phase.Task, nil).Once()

				manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					t.T().Logf("GetPhasesAndLogs run")
					phase.Phases[1].State = tt.state
				}).Return(phase.Phases, phase.Logs, nil).Once()

				manager.On("EndDeployTask", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					phase.Task.EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
					phase.Phases[1].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
				}).Return(nil)
				manager.On("EndDeployPhase", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					phase.Phases[1].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
				}).Return(nil)

				manager.On("UpdatePhaseAndTaskState", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Run(func(args mock.Arguments) {}).Return(nil)

				manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					phase.Task.CurrentPhase = deployplatform.PhaseCanary
					phase.Task.State = tt.wantState
				}).Return(phase.Task, nil)

				manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					t.T().Logf("GetPhasesAndLogs run")
					phase.Phases[1].State = tt.wantState
				}).Return(phase.Phases, phase.Logs, nil)

				p := &CanaryPhase{
					Phase: phase,
				}
				got, err := p.Change(nil)
				if (err != nil) != tt.wantErr {
					t.T().Errorf("Change() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if got.StateType() != tt.wantState {
					t.T().Errorf("Change() got = %v, want %v", got.StateType(), tt.wantState)
				}
				if tt.wantState == deployplatform.End {
					t.True(phase.Phases[1].EndedAt.Valid)
				} else {
					t.False(phase.Phases[1].EndedAt.Valid)
				}
			}
		})
	}
}

func (t *CanaryPhaseTestSuite) TestInitState() {
	manager := &mocks.ReadWriter{}
	phase := t.getPhase(deployplatform.Init, deployplatform.Init, deployplatform.PhaseCanary, "{}", manager)

	manager.On("CreatePhaseAndUpdateTask", mock.Anything, mock.Anything,
		mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("CreatePhaseAndUpdateTask run")
	})
	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.CurrentPhase = deployplatform.PhaseCanary
		phase.Task.State = deployplatform.Init
	}).Return(phase.Task, nil)
	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("GetPhasesAndLogs run")
	}).Return(phase.Phases, phase.Logs, nil)
	p := &CanaryPhase{
		Phase: phase,
	}
	err := p.InitState()
	t.Nil(err)
}

func (t *CanaryPhaseTestSuite) TestSetState() {
	manager := &mocks.ReadWriter{}
	phase := t.getPhase(deployplatform.Init, deployplatform.Init, deployplatform.PhaseCanary, "{}", manager)

	manager.On("UpdatePhaseAndTaskState", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Return(phase.Task, nil)
	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Return(phase.Phases, phase.Logs, nil)

	p := &CanaryPhase{
		Phase: phase,
	}
	err := p.SetState(deployplatform.Running)
	t.Nil(err)
}

func (t *CanaryPhaseTestSuite) TestEnd() {
	manager := &mocks.ReadWriter{}
	phase := t.getPhase(deployplatform.End, deployplatform.End, deployplatform.PhaseCanary, "{}", manager)

	manager.On("EndDeployTask", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
		phase.Phases[1].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
	}).Return(nil)
	manager.On("EndDeployPhase", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Phases[1].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
	}).Return(nil)

	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.CurrentPhase = deployplatform.PhaseCanary
		phase.Task.State = deployplatform.End
	}).Return(phase.Task, nil)

	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("GetPhasesAndLogs run")
	}).Return(phase.Phases, phase.Logs, nil)

	p := &CanaryPhase{
		Phase: phase,
	}
	got, err := p.Change(nil)
	t.Nil(err)
	t.Equal(deployplatform.End, got.StateType())
	t.True(phase.Task.EndedAt.Valid)
	t.True(phase.Phases[1].EndedAt.Valid)
}
