load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "tools_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/tools",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/tools/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/config/nacos",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/framework",
        "//backend/proto/tools/v1:tools",
    ],
)

go_binary(
    name = "tools",
    embed = [":tools_lib"],
    visibility = ["//visibility:public"],
)
