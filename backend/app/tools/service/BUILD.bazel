load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = [
        "deploy_platform.go",
        "test_account.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/tools/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/tools/logic/deployplatform/state",
        "//backend/app/tools/logic/testaccount",
        "//backend/app/tools/logic/testaccount/entity",
        "//backend/app/tools/repo/deployplatform",
        "//backend/app/tools/repo/deployplatform/entity",
        "//backend/app/tools/repo/testaccount/entity",
        "//backend/common/rpc/framework/config",
        "//backend/common/rpc/framework/log",
        "//backend/proto/tools/v1:tools",
        "@com_github_slack_go_slack//:slack",
        "@in_gopkg_yaml_v3//:yaml_v3",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
    ],
)
