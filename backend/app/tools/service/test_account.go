package service

import (
	"context"
	"fmt"
	"time"

	"github.com/MoeGolibrary/moego/backend/app/tools/logic/testaccount"
	entity "github.com/MoeGolibrary/moego/backend/app/tools/logic/testaccount/entity"
	repoentity "github.com/MoeGolibrary/moego/backend/app/tools/repo/testaccount/entity"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/tools/v1"
)

const (
	releaseOverdueTaskInterval = 5 * time.Minute
)

type TestAccountService struct {
	ta *testaccount.Logic

	toolspb.UnimplementedTestAccountServiceServer
}

func NewTestAccountService() *TestAccountService {
	service := &TestAccountService{
		ta: testaccount.NewLogic(),
	}
	service.startReleaseOverdueTask()

	return service
}

func (s TestAccountService) BorrowTestAccount(ctx context.Context, req *toolspb.BorrowTestAccountRequest) (
	*toolspb.BorrowTestAccountResponse, error) {

	params := &entity.BorrowParams{
		Borrower:   req.GetBorrower(),
		Attributes: repoentity.FromProto(req.GetAttributes()),
		Shared:     req.GetShared(),
	}
	switch req.GetIdentifier().(type) {
	case *toolspb.BorrowTestAccountRequest_Id:
		params.ID = req.GetId()
	case *toolspb.BorrowTestAccountRequest_Email:
		params.Email = req.GetEmail()
	}

	result, err := s.ta.BorrowTestAccount(ctx, params)
	if err != nil {
		return nil, err
	}

	return &toolspb.BorrowTestAccountResponse{
		Id:         result.ID,
		Email:      result.Email,
		Password:   result.Password,
		ContractId: result.ContractID,
	}, nil
}

func (s TestAccountService) ReturnTestAccount(ctx context.Context, req *toolspb.ReturnTestAccountRequest) (
	*toolspb.ReturnTestAccountResponse, error) {

	err := s.ta.ReturnTestAccount(ctx, req.ContractId)
	if err != nil {
		return nil, err
	}

	return &toolspb.ReturnTestAccountResponse{}, nil
}

func (s TestAccountService) CreateTestAccount(ctx context.Context, req *toolspb.CreateTestAccountRequest) (
	*toolspb.TestAccount, error) {

	result, err := s.ta.CreateTestAccount(ctx, &entity.CreateParams{
		Owner:      req.GetTestAccount().GetOwner(),
		Email:      req.GetTestAccount().GetEmail(),
		Password:   req.GetTestAccount().GetPassword(),
		Attributes: repoentity.FromProto(req.GetTestAccount().GetAttributes()),
		Disposable: req.GetTestAccount().GetDisposable(),
	})
	if err != nil {
		return nil, err
	}

	return &toolspb.TestAccount{
		Id:         result.ID,
		Email:      result.Email,
		Password:   result.Password,
		Disposable: req.GetTestAccount().GetDisposable(),
		Attributes: repoentity.ToProto(result.Attributes),
		Owner:      result.Owner,
	}, nil
}

func (s TestAccountService) ReleaseTestAccounts(ctx context.Context,
	req *toolspb.ReleaseTestAccountsRequest) (*toolspb.ReleaseTestAccountsResponse, error) {

	// 暂时只支持释放 overdue 的测试账号
	if !req.GetOverdue() {
		return &toolspb.ReleaseTestAccountsResponse{}, fmt.Errorf("overdue must be true")
	}

	err := s.ta.ReleaseOverdueTestAccounts(ctx)
	if err != nil {
		return nil, err
	}

	return &toolspb.ReleaseTestAccountsResponse{}, nil
}

func (s TestAccountService) ListTestAccounts(ctx context.Context,
	req *toolspb.ListTestAccountsRequest) (*toolspb.ListTestAccountsResponse, error) {

	accounts, nextPageToken, total, err := s.ta.ListTestAccounts(ctx, req.GetPageSize(), req.GetPageToken())
	if err != nil {
		return nil, err
	}

	result := make([]*toolspb.TestAccount, 0, len(accounts))
	for _, a := range accounts {
		result = append(result, a.ToProto())
	}

	return &toolspb.ListTestAccountsResponse{
		TestAccounts:  result,
		NextPageToken: nextPageToken,
		TotalSize:     int32(total),
	}, nil
}

func (s TestAccountService) startReleaseOverdueTask() {
	ticker := time.NewTicker(releaseOverdueTaskInterval)
	go func() {
		defer ticker.Stop() // 确保在退出时停止 ticker
		for range ticker.C {
			fmt.Println("start release overdue test accounts")
			if err := s.ta.ReleaseOverdueTestAccounts(context.Background()); err != nil {
				fmt.Printf("Error releasing overdue test accounts: %v\n", err)
			}
		}
	}()
}
