package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"time"

	"github.com/slack-go/slack"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	yaml "gopkg.in/yaml.v3"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/tools/logic/deployplatform/state"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/entity"
	rconfig "github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/tools/v1"
)

type DeployPlatformService struct {
	toolspb.UnimplementedDeployPlatformServiceServer
	repo   deployplatform.ReadWriter
	client *slack.Client
}

type SlackConfig struct {
	Slack struct {
		Token string `json:"token"`
	} `json:"slack"`
}

// NewDeployPlatformService creates a new DeployPlatformService.
func NewDeployPlatformService() *DeployPlatformService {
	c := &SlackConfig{}
	r, _ := rconfig.Get("nacos").Get(context.Background(), "deploy-platform.yaml")
	// 反序列化为对应格式（json/toml等等）的struct
	if err := yaml.Unmarshal([]byte(r.Value()), c); err != nil {
		log.Errorf("load config error:%s", err.Error())
		panic(err)
	}
	if c.Slack.Token == "" {
		log.Fatal("slack token is empty")
	}
	repo := deployplatform.New()
	client := slack.New(c.Slack.Token)

	return &DeployPlatformService{repo: repo, client: client}
}

func (s *DeployPlatformService) SkipDeployTask(
	ctx context.Context, req *toolspb.SkipDeployTaskRequest,
) (
	*toolspb.SkipDeployTaskResponse, error,
) {
	deployTask, err := s.getOrCreateDeployTask(ctx, req.Name, req.Name, req.Name, "")
	if err != nil {
		return nil, err
	}

	if deployTask.IsEnd() {
		return &toolspb.SkipDeployTaskResponse{}, nil
	}

	err = s.updateSkipOrRollbackTask(ctx, deployTask, req)
	if err != nil {
		return nil, err
	}

	return &toolspb.SkipDeployTaskResponse{}, nil
}

func (s *DeployPlatformService) RollbackDeployTask(
	ctx context.Context, req *toolspb.RollbackDeployTaskRequest,
) (
	*toolspb.RollbackDeployTaskResponse, error,
) {
	deployTask, err := s.getOrCreateDeployTask(ctx, req.Name, req.Name, req.Name, "")
	if err != nil {
		return nil, err
	}

	if deployTask.IsEnd() {
		return &toolspb.RollbackDeployTaskResponse{}, nil
	}

	err = s.updateSkipOrRollbackTask(ctx, deployTask, req)
	if err != nil {
		return nil, err
	}

	return &toolspb.RollbackDeployTaskResponse{}, nil
}

func (s *DeployPlatformService) updateSkipOrRollbackTask(
	ctx context.Context, deployTask *entity.DeployTask, req proto.Message,
) error {
	var params state.CanaryParameters
	if err := UnmarshalJSONB(deployTask.Parameters, &params); err != nil {
		return err
	}

	switch req.(type) {
	case *toolspb.RollbackDeployTaskRequest:
		params.Rollback = true
	case *toolspb.SkipDeployTaskRequest:
		params.Skip = true
	default:
		return status.Errorf(codes.InvalidArgument, "unsupported request type")
	}

	deployTask.Parameters, _ = MarshalJSONB(params)

	message, err := marshalProtoJSON(req)
	if err != nil {
		return err
	}

	return s.repo.UpdateTask(ctx, deployTask, message)
}

// HandleCanaryEventWebhookEvent handles canary event webhook event
func (s *DeployPlatformService) HandleCanaryEventWebhookEvent(ctx context.Context,
	req *toolspb.HandleCanaryEventWebhookEventRequest) (
	*toolspb.HandleCanaryEventWebhookEventResponse, error,
) {
	log.Debugf("HandleCanaryEventWebhookEvent: %+v", req)

	taskName := req.Checksum
	taskTitle := fmt.Sprintf("%s | %s", req.Namespace, req.Name)
	phaseName := fmt.Sprintf("%s-%s", req.Namespace, req.Name)
	message, err := marshalProtoJSON(req)
	if err != nil {
		return nil, err
	}

	deployTask, err := s.getOrCreateDeployTask(ctx, taskName, taskTitle, phaseName, message)
	if err != nil {
		return nil, err
	}

	if deployTask.State != deployplatform.Running &&
		deployTask.State != deployplatform.Waiting &&
		deployTask.State != deployplatform.Init {
		log.Debugf("deployTask is not running, taskID: %d", deployTask.ID)

		return &toolspb.HandleCanaryEventWebhookEventResponse{}, nil
	}

	phase, err := s.getOrCreateCanaryDeployPhase(ctx, deployTask, phaseName)
	if err != nil {
		return nil, err
	}

	var canaryParams state.CanaryParameters
	if err := json.Unmarshal(phase.Parameters, &canaryParams); err != nil {
		log.Errorf("failed to unmarshal Canary parameters, taskID: %d", deployTask.ID)

		return nil, err
	}

	if canaryParams.IsCompleted() {
		return &toolspb.HandleCanaryEventWebhookEventResponse{}, nil
	}

	go func() {
		_ = s.repo.CreateDeployLog(context.Background(),
			deployTask.ID, "flagger", phase.State, deployplatform.PhaseCanary, message)
	}()

	log.Debugf("phase: %s, state: %s", phase.Type, phase.State)
	params := &state.CanaryParameters{
		Name:          req.GetName(),
		Namespace:     req.GetNamespace(),
		Checksum:      req.GetChecksum(),
		BuildID:       req.GetBuildId(),
		Phase:         req.GetPhase(),
		Metadata:      req.GetMetadata(),
		CanaryWeight:  req.GetCanaryWeight(),
		FailedChecks:  req.GetFailedChecks(),
		Iterations:    req.GetIterations(),
		RemainingTime: time.Duration(req.GetRemaining()),
	}

	phase.Parameters, _ = MarshalJSONB(params)

	if err = s.repo.UpdatePhase(ctx, phase, message); err != nil {
		return nil, status.Errorf(codes.Internal, "failed to update phase: %v", err)
	}

	return &toolspb.HandleCanaryEventWebhookEventResponse{}, nil
}

// HandleCanaryWebhookEvent handles canary webhook event
func (s *DeployPlatformService) HandleCanaryWebhookEvent(
	ctx context.Context, req *toolspb.HandleCanaryWebhookEventRequest) (
	*toolspb.HandleCanaryWebhookEventResponse, error,
) {
	taskName := req.Checksum
	taskTitle := fmt.Sprintf("%s | %s", req.Namespace, req.Name)
	phaseName := fmt.Sprintf("%s-%s", req.Namespace, req.Name)
	message, err := marshalProtoJSON(req)
	if err != nil {
		return nil, err
	}

	deployTask, err := s.getOrCreateDeployTask(ctx, taskName, taskTitle, phaseName, message)
	if err != nil {
		return nil, err
	}

	phase, err := s.getOrCreateCanaryDeployPhase(ctx, deployTask, phaseName)
	if err != nil {
		return nil, err
	}

	log.Debugf("phase: %s, state: %s", phase.Type, phase.State)
	resp, err := s.handleCanaryStatus(req, deployTask, phase)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

const skipCanary = "skip_canary"

const rollbackCanary = "rollback_canary"

const channelID = "A07NLD4531C"

// HandleSlackInteractionsEvent handles slack event
func (s *DeployPlatformService) HandleSlackInteractionsEvent(
	ctx context.Context, req *toolspb.HandleSlackInteractionsEventRequest) (
	*toolspb.HandleSlackInteractionsEventResponse, error,
) {
	interaction, err := s.parseAndValidateInteraction(req.Payload)
	if err != nil {
		return nil, err
	}
	channel := interaction.Channel.ID
	ts := interaction.Message.Timestamp

	for _, action := range interaction.ActionCallback.BlockActions {
		if action.ActionID != skipCanary && action.ActionID != rollbackCanary {
			continue
		}

		taskName := action.Value
		deployTask, err := s.getOrCreateDeployTask(ctx, taskName, taskName, taskName, "")
		if err != nil {
			return nil, err
		}

		if deployTask.IsEnd() {
			s.sendSlackMessage(channel,
				fmt.Sprintf("Task %s is already ended.", deployTask.Title),
				ts)

			return &toolspb.HandleSlackInteractionsEventResponse{}, nil
		}

		var params state.TaskParameters
		if err := UnmarshalJSONB(deployTask.Parameters, &params); err != nil {
			return nil, err
		}

		if params.Rollback || params.Skip {
			s.sendSlackMessage(channel,
				fmt.Sprintf("Task %s is already set %s.", deployTask.Title, action.ActionID),
				ts)

			return &toolspb.HandleSlackInteractionsEventResponse{}, nil
		}

		err = s.handleAction(ctx, deployTask, action, interaction, params)
		if err != nil {
			return nil, err
		}
	}

	return &toolspb.HandleSlackInteractionsEventResponse{}, nil
}

func (s *DeployPlatformService) parseAndValidateInteraction(payload string) (slack.InteractionCallback, error) {
	payload, err := url.PathUnescape(payload)
	if err != nil {
		log.Errorf("Failed to unescape payload: %v", err)

		return slack.InteractionCallback{}, err
	}

	var interaction slack.InteractionCallback
	err = interaction.UnmarshalJSON([]byte(payload))
	if err != nil {
		log.Errorf("Failed to unmarshal interaction: %v", err)

		return slack.InteractionCallback{}, err
	}

	if interaction.Type != slack.InteractionTypeBlockActions || interaction.APIAppID != channelID {
		log.Warnf("Invalid interaction type or APIAppID")

		return slack.InteractionCallback{}, status.Errorf(codes.InvalidArgument, "invalid interaction type or APIAppID")
	}

	return interaction, nil
}

func (s *DeployPlatformService) handleAction(
	ctx context.Context, deployTask *entity.DeployTask,
	action *slack.BlockAction, interaction slack.InteractionCallback,
	params state.TaskParameters,
) error {
	username := interaction.User.Name
	taskName := action.Value

	switch action.ActionID {
	case skipCanary:
		params.Skip = true
	case rollbackCanary:
		params.Rollback = true
	default:
		log.Warnf("Unknown action ID, actionID: %s, taskName: %s", action.ActionID, taskName)

		return nil
	}

	params.Operator = username
	deployTask.Parameters, _ = MarshalJSONB(params)

	message, err := json.Marshal(interaction)
	if err != nil {
		return err
	}

	err = s.repo.UpdateTask(ctx, deployTask, string(message))
	if err != nil {
		return err
	}

	s.sendSlackMessage(interaction.Channel.ID,
		":white_check_mark: "+username+" has requested to "+action.ActionID+" for "+deployTask.Title,
		interaction.Message.Timestamp)

	return nil
}

// sendSlackMessage sends a message to Slack.
func (s *DeployPlatformService) sendSlackMessage(channelID, message, ts string) {
	// 回复消息到这个Thread
	log.Debugf("Sending message to Slack, channelID: %s, ts: %s", channelID, ts)
	if _, _, err := s.client.PostMessage(
		channelID,
		slack.MsgOptionText(message, false),
		slack.MsgOptionTS(ts),
	); err != nil {
		log.Errorf("Failed to send message to Slack, channelID: %s, error: %v", channelID, err)
	}
}

// HandleCanaryStatus handles the status update for canary deployment.
func (s *DeployPlatformService) handleCanaryStatus(
	req *toolspb.HandleCanaryWebhookEventRequest, task *entity.DeployTask, phase *entity.DeployPhase,
) (
	*toolspb.HandleCanaryWebhookEventResponse, error,
) {
	var params state.CanaryParameters
	if err := json.Unmarshal(phase.Parameters, &params); err != nil {
		return nil, err
	}
	if err := json.Unmarshal(task.Parameters, &params); err != nil {
		return nil, err
	}

	resp, err := s.handleHookType(req.Type, params)
	if err != nil {
		log.Errorf("failed to handle hook type: %v", err)

		return nil, status.Errorf(codes.InvalidArgument, "failed to handle hook type: %s", err)
	}

	return resp, nil
}

// handleHookType handles different types of canary hooks.
func (s *DeployPlatformService) handleHookType(
	hookType string, parameters state.CanaryParameters,
) (
	*toolspb.HandleCanaryWebhookEventResponse, error,
) {
	if handler, ok := state.CanaryHooks[hookType]; ok {
		if handler(parameters) {
			return &toolspb.HandleCanaryWebhookEventResponse{}, nil
		}
	} else {
		return nil, status.Errorf(codes.InvalidArgument, "unknown hook type: %s", hookType)
	}

	return nil, status.Errorf(codes.InvalidArgument, "hook not enabled for type: %s", hookType)
}

// getOrCreateDeployTask retrieves the deploy task by task name.
func (s *DeployPlatformService) getOrCreateDeployTask(
	ctx context.Context, name, title, phaseName, message string,
) (*entity.DeployTask, error) {
	deployTask, err := s.repo.GetOrCreateDeployTask(ctx, name, title, title, "flagger",
		deployplatform.PhaseCanary, phaseName, message)
	if err != nil {
		log.Errorf("failed to create DeployTask: %v", err)

		return nil, status.Errorf(codes.Internal, "failed to create DeployTask")
	}

	return deployTask, nil
}

// getOrCreateCanaryDeployPhase
func (s *DeployPlatformService) getOrCreateCanaryDeployPhase(
	ctx context.Context, task *entity.DeployTask, phaseName string,
) (
	*entity.DeployPhase, error,
) {
	phase, err := s.repo.GetDeployPhase(ctx, task.ID, deployplatform.PhaseCanary)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Errorf("failed to get current phase: %v", err)

			return nil, status.Errorf(codes.Internal, "failed to get phase")
		}

		err = s.repo.CreatePhaseAndUpdateTask(
			ctx, task, deployplatform.PhaseCanary, phaseName, deployplatform.Init, "{}")
		if err != nil {
			log.Errorf("failed to create phase and update task, taskID: %d", task.ID)

			return nil, err
		}

		return s.repo.GetDeployPhase(ctx, task.ID, deployplatform.PhaseCanary)
	}

	return phase, nil
}

// marshalProtoJSON marshals a proto message to JSON.
func marshalProtoJSON[T proto.Message](msg T) (string, error) {
	bytes, err := protojson.Marshal(msg)
	if err != nil {
		log.Errorf("failed to marshal proto message to JSON, message: %+v", msg)

		return "", err
	}

	return string(bytes), nil
}

// UnmarshalJSONB converts JSONB to CanaryParameters.
func UnmarshalJSONB(data entity.JSONB, target interface{}) error {
	return json.Unmarshal([]byte(data), target)
}

// MarshalJSONB converts CanaryParameters to JSONB.
func MarshalJSONB(target interface{}) (entity.JSONB, error) {
	data, err := json.Marshal(target)
	if err != nil {
		return nil, err
	}

	return entity.JSONB(data), nil
}
