package service

import (
	"context"
	"errors"
	"fmt"
	"strings"

	corev3 "github.com/envoyproxy/go-control-plane/envoy/config/core/v3"
	authv3 "github.com/envoyproxy/go-control-plane/envoy/service/auth/v3"
	jwt "github.com/golang-jwt/jwt/v4"
	"google.golang.org/genproto/googleapis/rpc/status"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/wrapperspb"

	"github.com/MoeGolibrary/moego/backend/app/devops-auth/configinit"
)

// AuthService is the gRPC server.
type AuthService struct {
	authv3.UnimplementedAuthorizationServer
	cfg *configinit.Config
}

// NewAuthService creates a new gRPC server.
func NewAuthService(cfg *configinit.Config) *AuthService {
	return &AuthService{cfg: cfg}
}

func extractTokenFromRequest(req *authv3.CheckRequest) (string, error) {
	cookie, ok := req.Attributes.Request.Http.Headers["cookie"]
	if !ok {
		return "", errors.New("missing cookie header")
	}

	for _, c := range strings.Split(cookie, ";") {
		parts := strings.Split(strings.TrimSpace(c), "=")
		if len(parts) == 2 && parts[0] == "auth_token" {
			if parts[1] == "" {
				return "", errors.New("auth_token cookie is empty")
			}

			return parts[1], nil
		}
	}

	return "", errors.New("missing auth_token cookie")
}

func validateAndParseToken(tokenString string, secret string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		return []byte(secret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}

	return claims, nil
}

// Check is the gRPC check method.
func (s *AuthService) Check(_ context.Context, req *authv3.CheckRequest) (*authv3.CheckResponse, error) {
	tokenString, err := extractTokenFromRequest(req)
	if err != nil {
		return &authv3.CheckResponse{
			Status: &status.Status{Code: int32(codes.Unauthenticated), Message: err.Error()},
		}, nil
	}

	claims, err := validateAndParseToken(tokenString, s.cfg.JWT.Secret)
	if err != nil {
		return &authv3.CheckResponse{
			Status: &status.Status{Code: int32(codes.Unauthenticated), Message: err.Error()},
		}, nil
	}

	email, ok := claims["email"].(string)
	if !ok {
		return &authv3.CheckResponse{
			Status: &status.Status{Code: int32(codes.Unauthenticated), Message: "invalid email claim"},
		}, nil
	}
	sub, ok := claims["sub"].(string)
	if !ok {
		return &authv3.CheckResponse{
			Status: &status.Status{Code: int32(codes.Unauthenticated), Message: "invalid sub claim"},
		}, nil
	}

	return &authv3.CheckResponse{
		Status: &status.Status{Code: int32(codes.OK)},
		HttpResponse: &authv3.CheckResponse_OkResponse{
			OkResponse: &authv3.OkHttpResponse{
				Headers: []*corev3.HeaderValueOption{
					{
						Header: &corev3.HeaderValue{
							Key:   "X-MOE-USER-EMAIL",
							Value: email,
						},
						Append: wrapperspb.Bool(false),
					},
					{
						Header: &corev3.HeaderValue{
							Key:   "X-MOE-USER-SUB",
							Value: sub,
						},
						Append: wrapperspb.Bool(false),
					},
				},
			},
		},
	}, nil
}
