package gorm

import (
	"context"

	"gorm.io/gorm"

	igorm "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
)

// unit test
// mockgen -destination=./mock/gorm_mock.go -package=mock -source=gorm.go
type ReadWriter interface {
	Get(ctx context.Context, id int) error
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	// 这个名字是自定义的，要和config.yaml中的数据库配置名字一致
	// 得到的 db 是一个原生的 *gorm.DB
	db, err := igorm.NewClientProxy("postgres.xxxx")
	if err != nil {
		panic(err)
	}

	return &impl{
		db: db,
	}
}

func (i *impl) Get(ctx context.Context, id int) error {
	return i.db.WithContext(ctx).Where("id = ?", id).First(&struct{}{}).Error
}
