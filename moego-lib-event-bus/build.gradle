apply from: "https://moego.s3.us-west-2.amazonaws.com/ops/sbc/${System.getProperty('sbc.scriptBranch', '')}SourceBranchControl.gradle"
applySourceBranchControlDependencies()

dependencies {
  api(project(':moego-lib-common'))

  testImplementation 'org.junit.jupiter:junit-jupiter'
  compileOnly("com.github.spotbugs:spotbugs-annotations:${spotbugsAnnotationsVersion}")

  api 'org.springframework.boot:spring-boot-starter'
  annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

  // kafka
  api('org.springframework.kafka:spring-kafka')
  api('software.amazon.msk:aws-msk-iam-auth:2.2.0')

  // uuidv7
  api('com.fasterxml.uuid:java-uuid-generator:5.1.0')
}
