syntax = "proto3";
package moego.models.engagement.v1;

import "moego/models/engagement/v1/setting.proto";
import "moego/utils/v1/time_of_day_interval.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/engagement/v1;engagementpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.engagement.v1";

//update setting def
message UpdateSettingDef {
  // dispatching_type
  optional models.engagement.v1.Setting.DispatchingType dispatching_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // business_hours start and business_hours end
  optional utils.v1.TimeOfDayInterval business_hours_range = 2;

  // auto_recording
  optional bool auto_recording = 3;

  // voice_mail
  optional string voice_mail = 4 [(validate.rules).string = {
    ignore_empty: false
    min_len: 1
    max_len: 1000
  }];

  // 是否启用 engagement center
  optional bool enable_moego_call = 5;
  // 是否启用 follow up 短信
  optional bool enable_follow_up_sms = 6;
  // follow up 短信内容
  optional string follow_up_sms_content = 7;
  // call_ring_duration
  optional int32 call_ring_duration = 8;
}

// update seats setting def
message UpdateSeatsSettingDef {
  // update seat
  repeated UpdateSeatDef update_seat = 1;
}

// update seat def
message UpdateSeatDef {
  // staff_id
  int64 staff_id = 1 [(validate.rules).int64.gt = 0];
  // phone_number
  optional string phone_number = 2;
}
