syntax = "proto3";
package moego.models.engagement.v1;

import "moego/models/organization/v1/staff_models.proto";
import "moego/models/permission/v1/permission_models.proto";
import "moego/utils/v1/time_of_day_interval.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/engagement/v1;engagementpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.engagement.v1";

//calling setting
message Setting {
  //dispatching type
  enum DispatchingType {
    //TYPE_UNSPECIFIED
    DISPATCH_TYPE_UNSPECIFIED = 0;
    //ALL_AT_ONCE
    ALL_AT_ONCE = 1;
    //WEB_CALL_FIRST
    WEB_CALL_FIRST = 2;
    //MOBILE_CALL_FIRST
    MOBILE_CALL_FIRST = 3;
    //MOBILE_ONLY
    MOBILE_ONLY = 4;
    //WEB_ONLY
    WEB_ONLY = 5;
  }
  // dispatching type
  DispatchingType dispatching_type = 1;
  // business hours range
  utils.v1.TimeOfDayInterval business_hours_range = 2;
  // auto recording
  bool auto_recording = 3;
  // phone number
  string phone_number = 4;
  // voice mail
  string voice_mail = 5;
  // 是否启用 engagement center
  bool enable_moego_call = 6;
  // 不启用 engagement center 时，incoming 电话转拨的号码
  string call_forwarding_number = 7;
  // 是否启用 follow up 短信
  bool enable_follow_up_sms = 8;
  // follow up 短信内容
  string follow_up_sms_content = 9;
  // call ring duration(秒)
  int32 call_ring_duration = 10;
}

// seat
message SeatsSetting {
  // seats
  repeated moego.models.engagement.v1.Seat seats = 1;
  // capacity
  int64 capacity = 2;
}

// seat
message Seat {
  // staff
  models.organization.v1.StaffModel staff = 1;
  // deletable
  bool deletable = 2;
}

// tmp seat
message TmpSeat {
  // staff
  moego.models.organization.v1.StaffModel staff = 1;
  // role
  moego.models.permission.v1.RoleModel role = 2;
  // device type
  repeated DeviceType device_types = 3;
}

// device type
enum DeviceType {
  // unspecified
  DEVICE_TYPE_UNSPECIFIED = 0;
  // web
  DEVICE_TYPE_WEB = 1;
  // mobile
  DEVICE_TYPE_MOBILE_APP = 2;
}
