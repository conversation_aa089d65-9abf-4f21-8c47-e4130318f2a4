// @since 2024-08-22 09:37:37
// <AUTHOR> Le<PERSON> <<EMAIL>>

syntax = "proto3";

package moego.models.engagement.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/engagement/v1;engagementpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.engagement.v1";

// 作为 Calling 属主的 Customer 信息
message Customer {
  // Type 表示客户类型
  enum Type {
    // 未指定的状态
    TYPE_UNSPECIFIED = 0;
    // 默认类型
    CUSTOMER = 1;
    // 潜客
    LEAD = 2;
  }

  // the customer id
  int64 id = 1;
  // the customer name
  string first_name = 2;
  // the customer name
  string last_name = 3;
  // the customer phone
  string phone = 4;
  // color code
  string color_code = 5;
  // avatar path
  string avatar_path = 6;
  // type
  Type type = 7;
}

// calling client
message CallingClient {
  // id
  int64 id = 1;
  // first name
  string name = 2;
  // color code
  string color_code = 3;
  // avatar path
  string avatar_path = 4;
}

// 归属于属主 Customer 的宠物信息
message Pet {
  // the pet id
  int64 id = 1;
  // the pet name
  string name = 2;
  // the pet breed
  string breed = 3;
}

// calling direction
enum CallingDirection {
  // unspecified value
  CALLING_DIRECTION_UNSPECIFIED = 0;
  // incoming call
  INCOMING = 1;
  // outgoing call
  OUTGOING = 2;
}

// 当前 staff 的权限信息
enum StaffPermission {
  // unspecified value
  STAFF_PERMISSION_UNSPECIFIED = 0;
  // 能拨打电话
  CALLING = 1;
  // 能接听电话
  ANSWERING = 2;
}

// 呼叫来源
enum CallingSource {
  // unspecified value
  CALLING_SOURCE_UNSPECIFIED = 0;
  // IOS APP
  IOS_APP = 1;
  // ANDROID APP
  ANDROID_APP = 2;
  // WEB
  WEB = 3;
}
