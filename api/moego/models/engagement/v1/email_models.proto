syntax = "proto3";

package moego.models.engagement.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/engagement/v1;engagementpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.engagement.v1";

// Sender email usage type
enum SenderEmailUsageType {
  // SenderEmailUsageType_UNSPECIFIED 无效值.
  SENDER_EMAIL_USAGE_TYPE_UNSPECIFIED = 0;
  // default
  SENDER_EMAIL_USAGE_TYPE_DEFAULT = 1;
  // customize
  SENDER_EMAIL_USAGE_TYPE_CUSTOMIZE = 2;
}

// SenderEmail is a model for sender email.
message SenderEmailModel {
  // ID is the unique identifier for the sender email.
  int64 id = 1;
  // Email is the email address of the sender.
  string email = 2;
  // Name is the name of the sender.
  string name = 3;
  // VerifyStatus is the status of the email verification.
  DomainVerifyStatus verify_status = 4;
  // dns records
  repeated DNSRecordModel dns_records = 5;
  // business id
  int64 business_id = 7;
}

// VerifyStatus is the status of the email verification.
enum DomainVerifyStatus {
  // VERIFY_STATUS_UNSPECIFIED 无效值.
  VERIFY_STATUS_UNSPECIFIED = 0;
  // VERIFY_STATUS_INIT 此面向敌，初始状态，需要通过发送确认（码）邮件来进行确认.
  VERIFY_STATUS_INIT = 1;
  // VERIFY_STATUS_CONFIRMED 已确认，但仍旧无法使用，需要配置 CNAME 进行验证。
  VERIFY_STATUS_CONFIRMED = 2;
  // VERIFY_STATUS_VERIFIED 已验证，可以使用.
  VERIFY_STATUS_VERIFIED = 3;
}

// DNSRecord is a model for DNS record.
message DNSRecordModel {
  // type is the type of the DNS record.
  DNSRecordType type = 1;
  // host 记录主机, 当 record 直接解析为根域名时没有 host
  optional string host = 2;
  // value 记录值
  string value = 3;
}

// DNSRecordType is the type of the DNS record.
enum DNSRecordType {
  // DNS_RECORD_TYPE_UNSPECIFIED 无效值.
  DNS_RECORD_TYPE_UNSPECIFIED = 0;
  // DNS_RECORD_TYPE_CNAME CNAME 记录.
  DNS_RECORD_TYPE_CNAME = 1;
  // DNS_RECORD_TYPE_TXT TXT 记录.
  DNS_RECORD_TYPE_TXT = 2;
}
