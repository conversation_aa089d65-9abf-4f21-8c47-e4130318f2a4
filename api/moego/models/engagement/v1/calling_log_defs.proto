syntax = "proto3";

package moego.models.engagement.v1;

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/engagement/v1/calling_log_models.proto";
import "moego/models/engagement/v1/voice_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/engagement/v1;engagementpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.engagement.v1";

// create calling log definition
message CreateCallingLogDef {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // client id
  int64 client_id = 3 [(validate.rules).int64 = {gt: 0}];
  // staff id
  optional int64 staff_id = 4 [(validate.rules).int64 = {gt: 0}];
  // summary
  optional string summary = 5;
  // phone number
  string phone_number = 6;
  // record transcript
  optional string record_transcript = 7;
  // record ai summary
  optional string record_ai_summary = 9;
  // record url
  optional string record_url = 10;
  // record type
  RecordType record_type = 11 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // direction
  models.engagement.v1.CallingDirection direction = 12 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // status
  models.engagement.v1.Status status = 13 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // categories
  repeated models.engagement.v1.Category categories = 14;
  // init time
  google.protobuf.Timestamp init_time = 15;
  // record duration
  optional google.protobuf.Duration record_duration = 16;
  // twilio call sid
  string twilio_call_sid = 17;
}

// update calling log definition
message UpdateCallingLogDef {
  // client id
  optional int64 client_id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff id
  optional int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
  // summary
  optional string summary = 3;
  // phone number
  optional string phone_number = 4;
  // record transcript
  optional string record_transcript = 5;
  // record ai summary
  optional string record_ai_summary = 6;
  // record url
  optional string record_url = 7;
  // record type
  optional models.engagement.v1.RecordType record_type = 8 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // id of the engagement
  optional models.engagement.v1.CallingDirection direction = 9 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // status
  optional models.engagement.v1.Status status = 10 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // categories
  repeated models.engagement.v1.Category categories = 11;
  // init time
  optional google.protobuf.Timestamp init_time = 12;
  // duration
  optional google.protobuf.Duration record_duration = 13;
}

// Trend type
enum Trend {
  // Unspecified trend
  TREND_UNSPECIFIED = 0;
  // benefit matrix type
  BENEFIT = 1;
  // harmful matrix type
  HARMFUL = 2;
  // neutral matrix type
  NEUTRAL = 3;
}

// indicator
message IndicatorDef {
  // value
  double value = 1;
  // percent
  double percent = 2;
  // direction
  Trend trend = 3;
}
