syntax = "proto3";

package moego.models.engagement.v1;

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/interval.proto";
import "moego/models/engagement/v1/voice_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/engagement/v1;engagementpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.engagement.v1";

// status
enum Status {
  // unspecified
  CALLING_LOG_STATUS_UNSPECIFIED = 0;
  // answered
  ANSWERED = 1;
  // no answer
  NO_ANSWER = 2;
}

// category
enum Category {
  // unspecified
  CALLING_LOG_CATEGORY_UNSPECIFIED = 0;
  // new booking
  NEW_BOOKING = 1;
  // rescheduled
  RESCHEDULED = 2;
  // cancel
  CANCEL = 3;
  // inquiry
  INQUIRY = 4;
}

// record type
enum RecordType {
  // unspecified
  RECORD_TYPE_UNSPECIFIED = 0;
  // voice mail
  VOICE_MAIL = 1;
  // all record
  CALL_RECORDING = 2;
}

// order by
message CallingLogOrderBy {
  // field
  enum Field {
    // unspecified
    SERVICE_ORDER_BY_TYPE_UNSPECIFIED = 0;
    // id
    ID = 1;
    // init time
    INIT_TIME = 2;
  }
  // field
  Field field = 1;
  // asc
  optional bool asc = 2;
}

// calling log
message CallingLogView {
  // staff
  message StaffView {
    // id
    int64 id = 1;
    // first name
    string first_name = 2;
    // last name
    string last_name = 3;
    // color code
    string color_code = 4;
    // avatar path
    string avatar_path = 5;
  }
  // id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  //business id
  int64 business_id = 3;
  // summary
  string summary = 4;
  // phone number
  string phone_number = 5;
  // record transcript
  string record_transcript = 6;
  // record ai summary
  string record_ai_summary = 7;
  // record url
  string record_url = 8;
  // record type
  RecordType record_type = 9;
  // direction
  models.engagement.v1.CallingDirection direction = 10;
  // status
  Status status = 11;
  // category
  repeated Category categories = 12;
  // staff
  StaffView staff = 13;
  // calling client
  CallingClient calling_client = 14;
  // customer
  Customer customer = 15;
  // pet
  repeated Pet pets = 16;
  // duration
  google.protobuf.Duration record_duration = 17;
  // init time
  google.protobuf.Timestamp init_time = 18;
  // is expire
  bool is_expire = 19;
  // is resolved
  bool is_resolved = 20;
}

// filter
message CallingLogFilter {
  // filter client ids
  repeated int64 client_ids = 1 [(validate.rules).repeated = {
    max_items: 500
    items: {
      int64: {gt: 0}
    }
  }];
  // filter customer ids
  repeated int64 customer_ids = 2 [(validate.rules).repeated = {
    max_items: 500
    items: {
      int64: {gt: 0}
    }
  }];
  // direction
  repeated models.engagement.v1.CallingDirection directions = 3;
  // statuses
  repeated models.engagement.v1.Status statuses = 4;
  // categories
  repeated models.engagement.v1.Category categories = 5;
  // record types
  repeated models.engagement.v1.RecordType record_types = 6;
  // business ids
  repeated int64 business_ids = 7;
  // init time period
  optional google.type.Interval init_time_period = 8;
  // is resolved
  optional bool is_resolved = 9;
  // log ids
  repeated int64 log_ids = 10 [(validate.rules).repeated = {
    max_items: 50
    items: {
      int64: {gt: 0}
    }
  }];
}
