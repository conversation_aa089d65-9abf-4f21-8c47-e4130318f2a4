// @since 2023-09-11 14:34:19
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.marketing.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/marketing/v1/discount_code_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/marketing/v1;marketingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.marketing.v1";

// discountCode model
message DiscountCodeModel {
  // unique id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // code
  string discount_code = 3;
  // description
  string description = 4;
  // discount amount
  double amount = 5;
  // discount type
  DiscountCodeType type = 6;
  // start date
  string start_date = 7;
  // end date
  string end_date = 8;

  // allowed all thing
  bool allowed_all_thing = 9;
  // allowed all services
  bool allowed_all_services = 10;
  // service ids
  repeated int64 service_ids = 11;
  // add on ids
  repeated int64 add_on_ids = 12;
  // allowed all products
  bool allowed_all_products = 13;
  // allowed products
  repeated int64 product_ids = 14;

  // allowed all clients
  bool allowed_all_clients = 15;
  // allowed new clients
  bool allowed_new_clients = 16;
  // clients group
  string clients_group = 17;
  // allowed clients
  repeated int64 client_ids = 18;

  // limit usage
  int32 limit_usage = 19;
  // limit number per client
  int32 limit_number_per_client = 20;
  // limit budget
  int32 limit_budget = 21;

  // auto apply association
  bool auto_apply_association = 22;
  // enable online booking
  bool enable_online_booking = 23;

  // discount sales
  double discount_sales = 24;
  // total usage
  int32 total_usage = 25;

  // status
  moego.models.marketing.v1.DiscountCodeStatus status = 26;
  // create by
  int32 create_by = 27;
  // update by
  int32 update_by = 28;
  // the create time
  google.protobuf.Timestamp create_time = 29;
  // the update time
  google.protobuf.Timestamp update_time = 30;

  // allowed locations
  repeated int64 location_ids = 31;
  // expired type
  moego.models.marketing.v1.ExpiryType expiry_type = 32;
  // expired time
  google.protobuf.Timestamp expiry_time = 33;
}

// discountCode model
message DiscountCodeLogModel {
  // the unique id
  int64 id = 1;
  // code id
  int64 code_id = 2;
  // redeem type
  moego.models.marketing.v1.RedeemType redeem_type = 3;
  // client id
  int64 client_id = 4;
  // pet id
  repeated int64 pet_ids = 5;
  // create by
  int32 create_by = 6;
  // redeem time
  google.protobuf.Timestamp redeem_time = 7;
  // redeem id
  int64 redeem_by = 8;
}

// discount code log composite view
message DiscountCodeLogCompositeView {
  // the unique id
  int64 id = 1;
  // redeem type
  moego.models.marketing.v1.RedeemType redeem_type = 2;
  // client name
  string client_name = 3;
  // pet name
  string pet_name = 4;
  // staff name
  string staff_name = 5;
  // redeem time
  google.protobuf.Timestamp redeem_time = 6;
  // redeem id
  int64 redeem_id = 7;
  // location name
  string location_name = 8;
  // business id
  int64 business_id = 9;
  // order id
  int64 order_id = 10;
}

// discountCode model online booking view
message DiscountCodeModelOnlineBookingView {
  // unique id
  int64 id = 1;
  // code
  string discount_code = 2;
  // description
  string description = 3;
  // discount amount
  double amount = 4;
  // discount type
  DiscountCodeType type = 5;
}

// discountCode model
message DiscountCodeCompositeView {
  // unique id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // code
  string discount_code = 3;
  // description
  string description = 4;
  // discount amount
  double amount = 5;
  // discount type
  DiscountCodeType type = 6;
  // start date
  string start_date = 7;
  // end date
  string end_date = 8;

  // allowed all thing
  bool allowed_all_thing = 9;
  // allowed all services
  bool allowed_all_services = 10;
  // service ids
  repeated int64 service_ids = 11;
  // add on ids
  repeated int64 add_on_ids = 12;
  // allowed all products
  bool allowed_all_products = 13;
  // allowed products
  repeated int64 product_ids = 14;

  // allowed all clients
  bool allowed_all_clients = 15;
  // allowed new clients
  bool allowed_new_clients = 16;
  // clients group
  string clients_group = 17;
  // allowed clients
  repeated int64 client_ids = 18;

  // limit usage
  int32 limit_usage = 19;
  // limit number per client
  int32 limit_number_per_client = 20;
  // limit budget
  int32 limit_budget = 21;

  // auto apply association
  bool auto_apply_association = 22;
  // enable online booking
  bool enable_online_booking = 23;

  // discount sales
  double discount_sales = 24;
  // total usage
  int32 total_usage = 25;

  // status
  moego.models.marketing.v1.DiscountCodeStatus status = 26;

  // service name
  repeated string service_names = 27;
  // product name
  repeated string product_names = 28;

  // allowed locations
  repeated int64 location_ids = 29;

  // expired type
  moego.models.marketing.v1.ExpiryType expiry_type = 30;
  // expired time
  google.protobuf.Timestamp expiry_time = 31;
}
