syntax = "proto3";

package moego.models.finance_gw.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/finance_gw/v1;financegwpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.finance_gw.v1";

// Request for HandleEvent. Ideally, most HTTP data should be passed in this request.
message HttpWebhookEvent {
  // A list of values for a header key.
  message HeaderValues {
    // The values
    repeated string values = 1;
  }

  // The path of the HTTP event.
  string path = 1;
  // headers
  map<string, HeaderValues> headers = 2;
  // The raw HTTP event body.
  bytes event_body = 3;
}
