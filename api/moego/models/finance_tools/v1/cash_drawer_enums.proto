syntax = "proto3";

package moego.models.finance_tools.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/finance_tools/v1;financetoolspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.finance_tools.v1";

// The type of the cash drawer adjustment
enum CashDrawerAdjustmentType {
  // Unspecified
  CASH_DRAWER_ADJUSTMENT_TYPE_UNSPECIFIED = 0;
  // Cash in
  IN = 1;
  // Cash out
  OUT = 2;
}
