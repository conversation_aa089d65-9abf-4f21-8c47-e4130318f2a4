// @since 2-23-10-20
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.platform_care.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/platform_care/v1;platformcarepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.platform_care.v1";

/**
 * PlatformCareModel is the model of platform care.
 */
message PlatformCareModel {
  // Primary Key
  int64 id = 1;

  // Link code
  string code = 2;

  // Agreement record UUID
  string agreement_record_uuid = 3;

  // Account ID
  int32 account_id = 4;

  // Email associated with the account
  string email = 5;

  // Status
  int32 status = 6;

  // Signed time
  int64 signed_time = 7;

  // Create time
  int64 create_time = 8;

  // Update time
  int64 update_time = 9;

  // Is deleted
  bool is_deleted = 10;
}
