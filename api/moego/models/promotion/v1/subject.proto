syntax = "proto3";

package moego.models.promotion.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/promotion/v1;promotionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.promotion.v1";

// DiscountSubject 折扣主体
message DiscountSubject {
  // id 折扣ID
  int64 id = 1;
}

// PackageSubject 套餐服务主体
message PackageSubject {
  // id 套餐服务ID
  int64 id = 1;
}

// MembershipSubject 会员权益主体
message MembershipSubject {
  // id 会员权益ID
  int64 id = 1;
}
