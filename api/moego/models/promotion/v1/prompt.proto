syntax = "proto3";

package moego.models.promotion.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/promotion/v1;promotionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.promotion.v1";

//  slot
message SlotModel {
  // slot id, represent to a specific place
  string id = 1;
  // resource
  ResourceModel resource = 2;
}

// resource
message ResourceModel {
  // id
  string id = 1;
  // title
  string title = 2;
  // description
  string description = 3;
  // metadata, json string
  string metadata = 4;
}
