syntax = "proto3";

package moego.models.map.v1;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/type/latlng.proto";
import "google/type/postal_address.proto";
import "moego/models/map/v1/map_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/map/v1;mappb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.map.v1";

//==============================================================================
// Map Model Summary
//==============================================================================
// I divide the location information on the map into two types: Point and Area.
//    Point: a precise location expressed by latitude and longitude. See {@link google.type.LatLng}
//    Area:
//       - Boundary area expressed by two vertices(northeast and southwest points). See {@link Bounds}
//       - A closed region expressed by a set of points. See {@link Polygon}
//       - Named areas divided by organizations or institutions. See {@link RegionModel}
// All other location information is defined based on the above infrastructure

// Bounds
message Bounds {
  // The northeast corner of the bounding box.
  google.type.LatLng northeast = 1;
  // The southwest corner of the bounding box.
  google.type.LatLng southwest = 2;
}

// Polygon
message Polygon {
  // points
  repeated google.type.LatLng points = 1;
}

// Circle
message Circle {
  // center of circle
  google.type.LatLng center = 1;
  // radius of circle
  double radius = 2;
}

// LocationBias
message LocationBias {
  // location bias
  oneof location_bias {
    // A circle defined by a center point and radius.
    Circle circle = 1;
    // A viewport defined by a northeast and a southwest corner.
    Bounds viewport = 2;
  }
}

// LocationRestriction
message LocationRestriction {
  // location restriction
  oneof location_restriction {
    // A circle defined by a center point and radius.
    Circle circle = 1;
    // A viewport defined by a northeast and a southwest corner.
    Bounds viewport = 2;
  }
}

// SuggestedAddress
message SuggestedAddress {
  // address source
  AddressSource address_source = 1;
  // formatted address text
  string formatted_address = 2;
  // country name
  string country = 3;
  // the additional information of the address, for example:
  //   county: "Santa Clara County"
  //   state: "California"
  //   city: "San Jose"
  //   url: "/get/NDg5YmQ5NzY5Zjk0YmI5IDUxMTQ3MTI1"
  //   distance: 1248.79
  //   plusCode: "7MJCH93V+7F"
  //   labels: ["school", "restaurant", "museum", ...]
  optional google.protobuf.Struct additional = 4;
}

// RegionModel
message RegionModel {
  // id in MoeGo
  int64 id = 1;
  // parent id in MoeGo
  optional int64 parent_id = 2;
  // code of region, for country, this is ISO-3166-1 numeric code
  optional string code = 3;
  // code type, e.g. ISO-3166-1/numeric, ISO-3166-2, local_code
  optional string code_type = 4;
  // ISO-3166-1 alpha-2 code
  string country = 5;
  // the short name of the region
  string short_name = 6;
  // the long name of the region
  string long_name = 7;
  // the formatted name of the region
  string formatted_region = 8;
  // the formatted local text of the region
  string formatted_local = 9;
  // zip/postal code
  optional string postal_code = 10;
  // the level of the region
  moego.models.map.v1.RegionLevel level = 11;
  // the coordinate of the region
  google.type.LatLng coordinate = 12;
  // boundary area of the region
  Bounds bounds = 13;
  // the labels of the region, e.g. province, county, municipality etc.
  repeated string labels = 14;
  // the additional information of the region, etc. ISO-3166 code
  optional google.protobuf.Struct additional = 15;
  // the status of the region
  moego.models.map.v1.RegionStatus status = 16;
  // code of parent
  optional string parent_code = 17;
  // name of parent
  optional string parent_name = 18;
  // level of parent
  optional moego.models.map.v1.RegionLevel parent_level = 19;

  // reserved 20 to 29;

  // polygon points used to display the region
  repeated Polygon polygons = 30;
  // the created time of the region data
  google.protobuf.Timestamp created_at = 31;
  // the updated time of the region data
  google.protobuf.Timestamp updated_at = 32;
}

// AddressSource
// Note: the source id and type must be returned by map service
message AddressSource {
  // the id of the address
  string source_id = 1;
  // the source of the address, e.g. GOOGLE_MAP, PLUS_CODE, BING_MAP, OSM, BAIDU_MAP, AMAP, MOEGO etc.
  string source_type = 2;
}

// AddressModel
message AddressModel {
  // address id
  optional int64 id = 1;
  // formatted address text
  string formatted_address = 2;
  // formatted local text
  string formatted_local = 3;
  // the coordinates of the address
  google.type.LatLng coordinate = 4;
  // boundary area of the address
  Bounds bounds = 5;
  // the country where this address is located, ISO-3166-1 alpha-2 code
  string country = 6;
  // the name of region level1 for the address
  optional string level1 = 7;
  // the name of region level2 for the address
  optional string level2 = 8;
  // the name of region level3 for the address
  optional string level3 = 9;
  // zip/postal code
  optional string postal_code = 10;
  // the labels of the address, e.g. park, school, hospital, etc.
  repeated string labels = 11;
  // the additional information of the address, for example:
  //   state: "California"
  //   county: "Santa Clara County"
  //   city: "San Jose"
  optional google.protobuf.Struct additional = 12;
  // source id
  optional string source_id = 13;
  // source type
  optional string source_type = 14;

  // reserved 15 to 30;

  // the created time of the address data
  google.protobuf.Timestamp created_at = 31;
  // the updated time of the address data
  google.protobuf.Timestamp updated_at = 32;
}

// PostalCodeRegion
message PostalCodeRegion {
  // address id
  int64 id = 1;
  // the country where this address is located, ISO-3166-1 alpha-2 code
  string country = 2;
  // zip/postal code
  string postal_code = 3;
  // formatted address text
  string formatted_address = 4;
  // formatted local text
  string formatted_local = 5;
  // the coordinates of the address
  google.type.LatLng coordinate = 6;
  // boundary area of the address
  Bounds bounds = 7;
  // the name of region level1 for the address
  optional string level1 = 8;
  // the name of region level2 for the address
  optional string level2 = 9;
  // the name of region level3 for the address
  optional string level3 = 10;
  // the labels of the address, e.g. park, school, hospital, etc.
  repeated string labels = 11;
  // the additional information of the address, for example:
  //   state: "California"
  //   county: "Santa Clara County"
  //   city: "San Jose"
  optional google.protobuf.Struct additional = 12;
  // source id
  optional string source_id = 13;
  // source type
  optional string source_type = 14;

  // reserved 15 to 30;

  // the created time of the address data
  google.protobuf.Timestamp created_at = 31;
  // the updated time of the address data
  google.protobuf.Timestamp updated_at = 32;
}

// Country
message CountryModel {
  // name of the country
  string name = 1;
  // official name of the country
  string official_name = 2;
  // local name of the country
  string local_name = 3;
  // whether the country is independent
  bool independent = 4;
  // ISO-3166-1 numeric code
  string numeric_code = 5;
  // ISO-3166-1 alpha-2 code
  string alpha2_code = 6;
  // ISO-3166-1 alpha-3 code
  string alpha3_code = 7;
  // capital of country
  optional string capital = 8;
  // the continent where the country is located
  optional string region = 9;
  // the continent code where the country is located
  optional string region_code = 10;
  // official language code
  optional string language_code = 11;
  // ISO-3166-1 ccTLD
  repeated string cc_tld = 12;
  // calling code of country
  repeated string calling_codes = 13;
  // timezones of country, ISO-8601 offset format, e.g. UTC+08:00
  // see: https://en.wikipedia.org/wiki/ISO_8601#Time_zone_designators
  // Note: this field can contain one or two values.
  // If there is only one value, it indicates that this country has only this time zone.
  // If there are two values, it expresses the minimum and maximum time zones.
  repeated string timezones = 14;
  // ISO-4217 currency code
  repeated Currency currencies = 15;
  // some territories that are sovereignty under the jurisdiction of this country, ISO-3166-1/alpha-2 code list
  // For example:
  //   for United States: Guam, Puerto Rico, American Samoa, Northern Mariana Islands, U.S. Virgin Islands
  //   for United Kingdom: Anguilla, Bermuda, British Virgin Islands, Cayman Islands ...
  //   for China: Hong Kong, Macao, Taiwan
  repeated string territories = 16;
  // specify whether postal codes are supported
  bool support_postal_code = 17;

  // Currency
  message Currency {
    // ISO-4217 currency code
    string code = 1;
    // ISO-4217 number code
    string number = 2;
    // the name of the currency
    string name = 3;
    // the symbol of the currency
    string symbol = 4;
  }
}

// models carrying postal addresses
// optimized based on fields in AddressModel
message PostalAddressModel {
  // id
  optional int64 id = 1;
  // formatted address
  string formatted_address = 2;
  // local
  string formatted_local = 3;
  // lat lng
  google.type.LatLng lat_lng = 4;
  // bounds
  Bounds bounds = 5;
  // address
  google.type.PostalAddress postal_address = 6;
  // labels
  repeated string labels = 11;
  // additional
  optional google.protobuf.Struct additional = 12;
  // source id
  optional string source_id = 13;
  // source type
  optional string source_type = 14;
  // reserved 15 to 30;

  // created time
  google.protobuf.Timestamp created_at = 31;
  // updated time
  google.protobuf.Timestamp updated_at = 32;
}
