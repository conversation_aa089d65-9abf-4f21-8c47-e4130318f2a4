syntax = "proto3";

package moego.models.billing.v1;

import "google/type/money.proto";
import "moego/utils/v1/time_period.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/billing/v1;billingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.billing.v1";

// product object
message ProductModel {
  // product id
  int64 id = 1;
  // product relation id
  string vendor_product_id = 2;
  // vendor
  string vendor = 3;
  // product name
  string name = 4;
  // product description
  string description = 5;
  // active
  bool active = 6;
}

// price object
message PriceModel {
  // price relation id
  int64 id = 1;
  // price object id
  string vendor_price_id = 2;
  // product relation id
  int64 product_rel_id = 3;
  // price amount with units
  google.type.Money unit_amount = 4;
  // one off price if null
  utils.v1.TimePeriod recurring = 5;
}
