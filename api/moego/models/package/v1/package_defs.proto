// @since 2025-03-15 21:57:06
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.models.package.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/package/v1;packagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.pkg.v1";

// The Package Full Definition
message PackageCreateDef {
  // name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
}

// The Package Partial Definition
message PackageUpdateDef {
  // name
  optional string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
}
