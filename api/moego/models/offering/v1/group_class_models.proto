syntax = "proto3";

package moego.models.offering.v1;

import "google/protobuf/duration.proto";
import "google/type/datetime.proto";
import "google/type/dayofweek.proto";
import "google/type/interval.proto";
import "google/type/money.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// training group class instance
message GroupClassInstance {
  // occurrence
  message Occurrence {
    // repeat type
    enum IntervalType {
      // the type is unspecified
      REPEAT_TYPE_UNSPECIFIED = 0;
      // day
      DAY = 1;
      // week
      WEEK = 2;
      // month
      MONTH = 3;
    }

    // day
    message Day {}

    // week
    message Week {
      // day of week
      repeated google.type.DayOfWeek day_of_weeks = 1;
    }

    // month
    // 为了前端快速上线，暂时抛弃通用性，
    // 但同时预留后续扩展能力
    message Month {
      // selection type
      enum SelectionType {
        // the type is unspecified
        SELECTION_TYPE_UNSPECIFIED = 0;
        // on day 25
        ON_25TH = 1;
        // on the third Wednesday
        ON_THIRD_WEDNESDAY = 2;
      }
      // selection type
      SelectionType selection_type = 1;
    }

    // The type of occurrence
    IntervalType type = 1;
    // interval, repeat every *interval*
    int32 interval = 2;
    // the detail
    oneof interval_unit {
      // day
      Day day = 3;
      // week
      Week week = 4;
      // month
      Month month = 5;
    }
  }

  // status
  enum Status {
    // The status is unspecified
    STATUS_UNSPECIFIED = 0;
    // upcoming
    UPCOMING = 1;
    // in progress
    IN_PROGRESS = 2;
    // past
    PAST = 3;
  }

  // The unique ID
  int64 id = 1;
  // company ID
  int64 company_id = 2;
  // business ID
  int64 business_id = 3;
  // The ID of the training group class
  int64 group_class_id = 4;
  // The name
  string name = 5;
  // staff id
  int64 staff_id = 6;
  // The start time
  google.type.DateTime start_time = 7;
  // capacity
  int32 capacity = 8;
  // occurrence
  Occurrence occurrence = 9;
  // status
  Status status = 10;
  // price
  google.type.Money price = 11;
}

// training session
message GroupClassSession {
  // status
  enum Status {
    // The status is unspecified
    STATUS_UNSPECIFIED = 0;
    // upcoming
    UPCOMING = 1;
    // in progress
    IN_PROGRESS = 2;
    // past
    PAST = 3;
  }

  // The unique ID
  int64 id = 1;
  // company ID
  int64 company_id = 2;
  // business ID
  int64 business_id = 3;
  // training instance id
  int64 group_class_instance_id = 4;
  // interval
  google.type.Interval interval = 5;
  // duration
  google.protobuf.Duration duration = 6;
  // is modified
  bool is_modified = 7;
  // status
  Status status = 8;
}

// The group class model
message GroupClassModel {
  // The class id
  int64 id = 1;
  // The class name
  string name = 2;
  // description
  string description = 3;
  // price
  double price = 4;
  // number of sessions
  int32 num_sessions = 5;
  // duration in minutes
  int32 duration_session_min = 6;
}
