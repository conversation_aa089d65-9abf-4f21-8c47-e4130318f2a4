syntax = "proto3";

package moego.models.offering.v1;

import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/offering/v1/auto_rollover_rule_defs.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/offering/v1/service_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// create service def
message CreateServiceDef {
  reserved "is_all_staff"; // due to the type compatibility issue
  reserved 36;
  // name of the service
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // description of the service
  optional string description = 2 [(validate.rules).string = {max_len: 1500}];
  // category of the service
  optional int64 category_id = 3 [(validate.rules).int64.gte = 0];
  // is the service inactive
  bool inactive = 4;
  // images of this service
  repeated string images = 5 [(validate.rules).repeated = {
    min_items: 0
    max_items: 10
  }];
  // color code
  optional string color_code = 6 [(validate.rules).string.len = 7];
  // override by location
  repeated LocationOverrideRule location_override_list = 8 [deprecated = true];
  // service item type
  optional ServiceItemType service_item_type = 9;
  // price
  double price = 10 [(validate.rules).double.gte = 0];
  // price unit
  ServicePriceUnit price_unit = 11;
  // tax id
  int64 tax_id = 12 [(validate.rules).int64.gt = 0];
  // duration
  int32 duration = 13 [(validate.rules).int32.gte = 0];
  // whether add to commission base
  bool add_to_commission_base = 14;
  // whether can tip
  bool can_tip = 15;
  // whether the service is available for all locations
  bool is_all_location = 17;
  // whether the service is available for all pet type & breed
  bool breed_filter = 19;
  // available pet type with pet breed (only if is_available_for_all_pet_type_and_breed is false)
  repeated CustomizedBreed customized_breed = 20;
  // available for all pet size
  bool pet_size_filter = 21;
  // available pet size (only if is_available_for_all_pet_size is false)
  repeated int64 customized_pet_sizes = 22;
  // weight filter only for compatible with old version, use pet_size in new version
  bool weight_filter = 23 [deprecated = true];
  // weight range (only if weight_filter is true)
  repeated double weight_range = 24 [deprecated = true];
  // available for all pet coat type
  bool coat_filter = 25;
  // available pet coat type (only if is_available_for_all_pet_coat_type is false)
  repeated int64 customized_coat = 26;
  // required dedicated lodging
  bool require_dedicated_lodging = 27;
  // whether the service is available for all lodging(only if require_dedicated_lodging is true)
  bool lodging_filter = 28;
  // available lodging ids(only if require_dedicated_lodging is true and available_for_all_lodgings is false)
  repeated int64 customized_lodgings = 29;
  // whether the add on is available for all services(only for add on)
  optional bool service_filter = 30;
  // service filters(only for add on)
  repeated ServiceFilter service_filter_list = 31;
  // service type
  optional ServiceType type = 32;
  // whether the service require dedicated staff
  bool require_dedicated_staff = 33;
  // max duration
  optional int32 max_duration = 34 [(validate.rules).int32.gte = 0];
  // auto rollover rule
  optional AutoRolloverRuleDef auto_rollover_rule = 35;
  // available staffs
  repeated int64 available_staff_id_list = 37;
  // location staff override rules
  repeated LocationStaffOverrideRule location_staff_override_list = 38;
  // available business ids
  repeated int64 available_business_id_list = 39;
  // whether the service is available for all staff
  bool available_for_all_staff = 40;

  // pet code filter
  message PetCodeFilter {
    // whether to filter by white list or black list
    bool is_white_list = 1;
    // whether it applies to all pet codes.
    bool is_all_pet_code = 2;
    // pet code list, only valid when is_all_pet_code is false
    repeated int64 pet_code_ids = 3 [(validate.rules).repeated = {
      unique: true
      max_items: 1000
    }];
  }
  // pet code filter
  optional PetCodeFilter pet_code_filter = 41;

  // bundle services
  repeated int64 bundle_service_ids = 42 [(validate.rules).repeated = {
    unique: true
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];

  // source default is MoeGo Platform
  optional ServiceModel.Source source = 43 [(validate.rules).enum = {defined_only: true}];

  // number of sessions, only for training
  optional int32 num_sessions = 44 [(validate.rules).int32.gte = 0];

  // duration of each session in minutes, only for training
  optional int32 duration_session_min = 45 [(validate.rules).int32.gte = 0];

  // capacity of group class, zero means unlimited, only for training
  optional int32 capacity = 46 [(validate.rules).int32.gte = 0];

  // whether it require a prerequisite class
  bool is_require_prerequisite_class = 48;

  // prerequisite class ids of training group class, only valid when is_require_prerequisite_class is ture
  repeated int64 prerequisite_class_ids = 49;
  // whether evaluation is required
  optional bool is_evaluation_required = 50;
  // whether evaluation is required before online booking
  optional bool is_evaluation_required_for_ob = 51;
  // evaluation id
  optional int64 evaluation_id = 52;

  // additional service rule
  optional AdditionalServiceRule additional_service_rule = 53;
}

// update service def
message UpdateServiceDef {
  reserved "is_all_staff"; // due to the type compatibility issue
  reserved 36;
  // id
  int64 service_id = 1 [(validate.rules).int64.gt = 0];
  // name of the service
  string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // description of the service
  string description = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 1500
  }];
  // category of the service
  int64 category_id = 4 [(validate.rules).int64.gte = 0];
  // is the service inactive
  bool inactive = 5;
  // images of this service
  repeated string images = 6;
  // color code
  string color_code = 7 [(validate.rules).string.len = 7];
  // override by location
  repeated LocationOverrideRule location_override_list = 9 [deprecated = true];
  // price
  double price = 10 [(validate.rules).double.gte = 0];
  // price unit
  ServicePriceUnit price_unit = 11;
  // tax id
  int64 tax_id = 12 [(validate.rules).int64.gt = 0];
  // duration
  int32 duration = 13 [(validate.rules).int32.gte = 0];
  // whether add to commission base
  bool add_to_commission_base = 14;
  // whether can tip
  bool can_tip = 15;
  // whether the service is available for all locations
  bool is_all_location = 16;
  // whether the service is available for all pet type & breed
  bool breed_filter = 17;
  // available pet type with pet breed (only if is_available_for_all_pet_type_and_breed is false)
  repeated CustomizedBreed customized_breed = 18;
  // available for all pet size
  bool pet_size_filter = 19;
  // available pet size (only if is_available_for_all_pet_size is false)
  repeated int64 customized_pet_sizes = 20;
  // weight filter only for compatible with old version, use pet_size in new version
  bool weight_filter = 21 [deprecated = true];
  // weight range (only if weight_filter is true)
  repeated double weight_range = 22 [deprecated = true];
  // available for all pet coat type
  bool coat_filter = 23;
  // available pet coat type (only if is_available_for_all_pet_coat_type is false)
  repeated int64 customized_coat = 24;
  // required dedicated lodging
  bool require_dedicated_lodging = 25;
  // whether the service is available for all lodging(only if require_dedicated_lodging is true)
  bool lodging_filter = 26;
  // available lodging ids(only if require_dedicated_lodging is true and available_for_all_lodgings is false)
  repeated int64 customized_lodgings = 27;
  // whether the add on is available for all services(only for add on)
  bool service_filter = 29;
  // service filters(only for add on)
  repeated ServiceFilter service_filter_list = 30;
  // whether the service require dedicated staff
  bool require_dedicated_staff = 31;
  // max duration
  optional int32 max_duration = 32 [(validate.rules).int32.gte = 0];
  // auto rollover rule
  optional AutoRolloverRuleDef auto_rollover_rule = 35;
  // available staffs
  repeated int64 available_staff_id_list = 37;
  // location staff override rules
  repeated LocationStaffOverrideRule location_staff_override_list = 38;
  // available business ids
  repeated int64 available_business_id_list = 39;
  // whether the service is available for all staff
  bool available_for_all_staff = 40;

  // pet code filter
  message PetCodeFilter {
    // whether to filter by white list or black list
    bool is_white_list = 1;
    // whether it applies to all pet codes.
    bool is_all_pet_code = 2;
    // pet code list, only valid when is_all_pet_code is false
    repeated int64 pet_code_ids = 3 [(validate.rules).repeated = {
      unique: true
      max_items: 1000
    }];
  }
  // pet code filter
  optional PetCodeFilter pet_code_filter = 41;

  // bundle services
  repeated int64 bundle_service_ids = 42 [(validate.rules).repeated = {
    unique: true
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];

  // source
  optional ServiceModel.Source source = 43 [(validate.rules).enum = {defined_only: true}];

  // number of sessions, only for training
  optional int32 num_sessions = 44 [(validate.rules).int32.gte = 0];

  // duration of each session in minutes, only for training
  optional int32 duration_session_min = 45 [(validate.rules).int32.gte = 0];

  // capacity of group class, zero means unlimited, only for training
  optional int32 capacity = 46 [(validate.rules).int32.gte = 0];

  // whether it require a prerequisite class
  bool is_require_prerequisite_class = 48;

  // prerequisite class ids of training group class, only valid when is_require_prerequisite_class is ture
  repeated int64 prerequisite_class_ids = 49;

  // whether evaluation is required
  optional bool is_evaluation_required = 50;

  // whether evaluation is required before online booking
  optional bool is_evaluation_required_for_ob = 51;

  // evaluation id
  optional int64 evaluation_id = 52;
  // additional service rule
  optional AdditionalServiceRule additional_service_rule = 53;
}

// service customized info by pet
message ServiceWithPetCustomizedInfo {
  // id
  int64 service_id = 1;
  // name
  string name = 2;
  // price, service price in company level
  double price = 3;
  // price unit
  ServicePriceUnit price_unit = 4;
  // duration, service duration in company level
  int32 duration = 5;
  // is save price
  bool is_save_price = 6;
  // is save duration
  bool is_save_duration = 7;
  // create time
  int64 create_time = 8;
  // update time
  int64 update_time = 9;
}

// service applicable filter
message ServiceApplicableFilter {
  // filter by pet
  ServiceFilterByPet filter_by_pet = 1;
  // filter by selected service
  ServiceFilterByService filter_by_service = 2;
  // filter by selected lodging
  ServiceFilterByLodging filter_by_lodging = 3;
}

// service filter by pet
message ServiceFilterByPet {
  // pet type
  models.customer.v1.PetType pet_type = 1;
  // pet breed
  optional string pet_breed = 2;
  // pet size
  optional int64 pet_size_id = 3;
  // weight, should be deprecated, use pet size
  optional double pet_weight = 4;
  // pet coat type id
  optional int64 pet_coat_type_id = 5;
  // pet code ids
  repeated int64 pet_code_ids = 6;
}

// service filter by selected service
message ServiceFilterByService {
  // service id list
  repeated int64 service_ids = 1;
  // service item type, only support one item type in one filter
  optional ServiceItemType service_item_type = 2;
}

// service filter by selected lodging
message ServiceFilterByLodging {
  // lodging id list
  repeated int64 lodging_type_ids = 1;
}
