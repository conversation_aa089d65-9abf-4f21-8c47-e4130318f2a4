// @since 2024-07-30 11:14:10
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.offering.v1;

import "moego/models/offering/v1/pricing_rule_enums.proto";
import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// pricing rule upsert definition
message PricingRuleUpsertDef {
  // the unique id
  optional int64 id = 1;

  // apply service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // apply service type
  moego.models.offering.v1.ServiceType service_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // rule name
  string rule_name = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];

  // apply to all service
  bool is_all_service_applicable = 6;
  // selected service ids, only effective when all_service is false
  repeated int64 selected_services = 7 [(validate.rules).repeated = {
    ignore_empty: true
    unique: true
    items: {
      int64: {gt: 0}
    }
    max_items: 500
  }];

  // rule configuration
  PricingRuleConfigurationDef rule_configuration = 8 [(validate.rules).message = {required: true}];
  // active, true means active, false means inactive
  bool is_active = 9;

  // rule group type
  optional moego.models.offering.v1.RuleGroupType rule_group_type = 10 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // rule apply choice type
  optional moego.models.offering.v1.RuleApplyChoiceType rule_apply_choice_type = 11 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// pricing rule configuration definition
message PricingRuleConfigurationDef {
  // pricing rule items
  repeated PricingRuleItemDef pricing_rule_item_defs = 1 [(validate.rules).repeated = {min_items: 1}];

  // pricing rule item definition
  message PricingRuleItemDef {
    // item
    oneof item {
      option (validate.required) = true;
      // multiple pets
      moego.models.offering.v1.MultiPetDef multi_pet_def = 1;
      // multiple nights/days
      moego.models.offering.v1.MultiNightDef multi_night_def = 2;
      // peak date
      moego.models.offering.v1.PeakDateDef peak_date_def = 3;
    }
  }
}

// pricing rule item definition
message MultiPetDef {
  // item def
  repeated MultiPetItemDef item_defs = 1 [(validate.rules).repeated = {min_items: 1}];

  // Multi Pet Item Model
  message MultiPetItemDef {
    // price detail
    PriceItemDef price_item_def = 1 [(validate.rules).message = {required: true}];
    // number above which the rule is effective
    int32 effective_number = 2 [(validate.rules).int32 = {gt: 0}];
  }

  // rule apply type, apply to each one/apply to additional
  moego.models.offering.v1.RuleApplyType rule_apply_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // same lodging unit, only effective when rule_item_type is multiple pet
  bool need_in_same_lodging = 3;
}

// pricing rule item definition
message MultiNightDef {
  // item def
  repeated MultiNightItemDef item_defs = 1 [(validate.rules).repeated = {min_items: 1}];

  // Multi Pet Item Model
  message MultiNightItemDef {
    // price detail
    PriceItemDef price_item_def = 1 [(validate.rules).message = {required: true}];
    // number above which the rule is effective
    int32 effective_number = 2 [(validate.rules).int32 = {gt: 0}];
  }
}

// pricing rule item definition
message PeakDateDef {
  // item def
  repeated PeakDateItemDef item_defs = 1 [(validate.rules).repeated = {min_items: 1}];

  // Peak date Item Model
  message PeakDateItemDef {
    // price detail
    PriceItemDef price_item_def = 1 [(validate.rules).message = {required: true}];
    // start date which the rule is effective
    string start_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
    // end date which the rule is effective
    string end_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  }
}

// pricing rule item definition
message PriceItemDef {
  // rule price type, fixed price/percentage price
  moego.models.offering.v1.RulePriceType price_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // rule price value
  double price_value = 2 [(validate.rules).double = {gt: 0}];
}

// pet detail calculate definition, used for pricing rule calculation
message PetDetailCalculateDef {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service id, not evaluation id
  int64 service_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service price
  double service_price = 4 [(validate.rules).double = {gte: 0}];
  // night number
  optional int32 night_number = 3 [(validate.rules).int32 = {gte: 0}];
  // lodging unit id
  optional int64 lodging_unit_id = 5 [(validate.rules).int64 = {gte: 0}];
  // scope type price, apply to other services
  optional models.offering.v1.ServiceScopeType scope_type_price = 11 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service date, for boarding service
  optional string service_date = 12 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
}

// pet detail calculate result definition, used for pricing rule calculation
message PetDetailCalculateResultDef {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service id
  int64 service_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service price after calculation
  double service_price = 4 [(validate.rules).double = {gte: 0}];
  // used pricing rule id, empty means for preview, deprecated, use applied_rule_ids
  optional int64 id = 5 [deprecated = true];
  // service date
  optional string service_date = 6 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // applied rule ids,  empty means for preview
  repeated int64 applied_rule_ids = 7 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
}

// list pricing rule filter
message ListPricingRuleFilter {
  // service item: grooming/boarding/daycare
  optional moego.models.offering.v1.ServiceItemType service_item_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service type: service/addon
  optional moego.models.offering.v1.ServiceType service_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // active
  optional bool is_active = 3;
  // rule group type
  optional moego.models.offering.v1.RuleGroupType rule_group_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // include deleted
  optional bool include_deleted = 5;
}
