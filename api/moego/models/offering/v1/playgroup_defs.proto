syntax = "proto3";

package moego.models.offering.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// create playgroup def
message CreatePlaygroupDef {
  // name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 20
  }];
  // color code
  string color_code = 2 [(validate.rules).string.pattern = "^#([A-Fa-f0-9]{6})$"];
  // max pet capacity
  int32 max_pet_capacity = 3 [(validate.rules).int32.gt = 0];
  // description
  string description = 4 [(validate.rules).string = {
    min_len: 0
    max_len: 100
  }];
}

// update playgroup def
message UpdatePlaygroupDef {
  // id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // name
  string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 20
  }];
  // color code
  string color_code = 3 [(validate.rules).string.pattern = "^#([A-Fa-f0-9]{6})$"];
  // max pet capacity
  int32 max_pet_capacity = 4 [(validate.rules).int32.gt = 0];
  // description
  string description = 5 [(validate.rules).string = {
    min_len: 0
    max_len: 100
  }];
}
