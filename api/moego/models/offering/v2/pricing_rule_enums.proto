// @since 2025-02-26 16:46:24
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.offering.v2;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v2";

// rule type
enum RuleType {
  // unspecified value
  RULE_TYPE_UNSPECIFIED = 0;
  // for multiple pet
  MULTIPLE_PET = 1;
  // for multiple stay
  MULTIPLE_STAY = 2;
  // for peak date
  PEAK_DATE = 3;
}

// ConditionType defines what attribute a condition will evaluate
enum ConditionType {
  // unspecified value
  CONDITION_TYPE_UNSPECIFIED = 0;
  // pet count
  PET_COUNT = 1;
  // stay length
  STAY_LENGTH = 2;
  // date range
  DATE_RANGE = 3;
  // repeat dates
  REPEAT_DATES = 4;
}

// EffectType defines how a price is modified
enum EffectType {
  // unspecified value
  EFFECT_TYPE_UNSPECIFIED = 0;
  // fixed discount, e.g. -$100
  FIXED_DISCOUNT = 1;
  // percentage discount, e.g. -10%
  PERCENTAGE_DISCOUNT = 2;
  // fixed increase, e.g. +$100
  FIXED_INCREASE = 3;
  // percentage increase, e.g. +10%
  PERCENTAGE_INCREASE = 4;
}

// rule apply type
enum RuleApplyType {
  // unspecified value
  RULE_APPLY_TYPE_UNSPECIFIED = 0;
  // apply to each one
  APPLY_TO_EACH = 1;
  // apply to additional
  APPLY_TO_ADDITIONAL = 2;
  // apply to all pets, deprecated
  APPLY_TO_ALL_PETS = 3 [deprecated = true];
  // apply to first pet
  APPLY_TO_FIRST_PET = 4;
}
