// @since 2025-02-26 16:46:24
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.offering.v2;

import "google/protobuf/timestamp.proto";
import "moego/models/offering/v2/pricing_rule_defs.proto";
import "moego/models/offering/v2/pricing_rule_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v2";

// PricingRule model
message PricingRule {
  // the unique id
  int64 id = 1;

  // company id
  int64 company_id = 2;
  // rule type
  RuleType type = 3;
  // rule name
  string rule_name = 4;
  // active, true means active, false means inactive
  bool is_active = 5;

  // all boarding applicable
  bool all_boarding_applicable = 6;
  // selected service ids, only effective when all_service is false
  repeated int64 selected_boarding_services = 7;
  // all daycare applicable
  bool all_daycare_applicable = 8;
  // selected service ids, only effective when all_service is false
  repeated int64 selected_daycare_services = 9;

  // rule apply type, apply to each one/apply to additional
  moego.models.offering.v2.RuleApplyType rule_apply_type = 10;

  // same lodging unit, only effective when rule_item_type is multiple pet
  bool need_in_same_lodging = 11;

  // rule configuration
  moego.models.offering.v2.PricingRuleConfiguration rule_configuration = 12;

  // updated staff id
  int64 updated_by = 13;
  // the create time
  google.protobuf.Timestamp created_at = 14;
  // the update time
  google.protobuf.Timestamp updated_at = 15;
  // the delete time, non-null means is deleted
  optional google.protobuf.Timestamp deleted_at = 16;
  // is charge per lodging, only effective when rule_item_type is peak date
  bool is_charge_per_lodging = 17;
}
