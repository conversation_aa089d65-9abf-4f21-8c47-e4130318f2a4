syntax = "proto3";

package moego.models.order.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/order/v1/service_charge_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// service charge charge
message ServiceCharge {
  // id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // name
  string name = 3;
  // split method
  string description = 4;
  // price
  double price = 5;
  // customize config
  int32 tax_id = 6;
  // sort
  int32 sort = 7;
  // auto apply status
  AutoApplyStatus auto_apply_status = 8;
  // auto apply condition
  AutoApplyCondition auto_apply_condition = 9;
  // auto apply time, unit: minute
  // Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
  int32 auto_apply_time = 10;

  // is mandatory, preserved 8-29 for future use
  // deprecated by freeman since /2024/9/24, use auto_apply_status instead
  bool is_mandatory = 30 [deprecated = true];
  // is active
  bool is_active = 31;
  // is deleted
  bool is_deleted = 32;
  // created by, staff id
  int64 created_by = 33;
  // updated by, staff id
  int64 updated_by = 34;
  // created at
  google.protobuf.Timestamp created_at = 35;
  // updated at
  google.protobuf.Timestamp updated_at = 36;
  //service charge location override data
  repeated ServiceChargeLocationOverride location_override_list = 37;
  // is all location
  bool is_all_location = 38;
  // apply type
  ApplyType apply_type = 39;

  // Auto apply status
  enum AutoApplyStatus {
    // Unspecified
    AUTO_APPLY_STATUS_UNSPECIFIED = 0;
    // No auto apply
    AUTO_APPLY_DISABLED = 1;
    // Auto apply for all
    AUTO_APPLY_ENABLED = 2;
    // Auto apply with condition
    AUTO_APPLY_ENABLED_WITH_CONDITION = 3;
  }
  // auto support service
  repeated models.offering.v1.ServiceItemType service_item_types = 13;
  // auto apply time
  optional AutoApplyTimeType auto_apply_time_type = 14;

  // Auto apply condition
  enum AutoApplyCondition {
    // Unspecified
    AUTO_APPLY_CONDITION_UNSPECIFIED = 0;
    // Boarding/Daycare late pickup
    BD_LATE_PICKUP = 1;
    // Boarding/Daycare early drop off
    BD_EARLY_DROP_OFF = 2;
  }

  // Auto apply time type
  enum AutoApplyTimeType {
    // Unspecified
    AUTO_APPLY_TIME_TYPE_UNSPECIFIED = 0;
    // business hours
    BUSINESS_HOUR = 1;
    // certain time
    CERTAIN_TIME = 2;
  }

  // apply type
  enum ApplyType {
    // Unspecified
    APPLY_TYPE_UNSPECIFIED = 0;
    // Per Appointment
    PER_APPOINTMENT = 1;
    // Per Pet
    PER_PET = 2;
    // Per Pricing Unit
    PER_PRICING_UNIT = 3;
  }

  // surcharge type
  optional moego.models.order.v1.SurchargeType surcharge_type = 15;

  // charge method
  moego.models.order.v1.ChargeMethod charge_method = 16;
  // food source
  optional FoodSource food_source = 17;

  // food source model
  message FoodSource {
    // food source ids
    repeated int64 food_source_ids = 1;
    // is all food source
    bool is_all_food_source = 2;
  }

  // Exceed 24-hours period rule. Only applicable when surcharge_type == CHARGE_24_HOUR
  // Type of time-based charge calculation (e.g., exceed certain hours)
  enum TimeBasedPricingType {
    // Unspecified
    TIME_BASED_PRICING_TYPE_UNSPECIFIED = 0;
    // A flat rate applied regardless of extra hours or pet count.
    FLAT_RATE = 1;
    // Tiered pricing based on how many hours the service exceeds.
    HOURLY_EXCEED_TIERED_RATE = 2;
  }

  // How to apply the charge when multiple pets are involved (only applicable if time-based tiered rate is used)
  enum MultiplePetsChargeType {
    // Unspecified
    MULTIPLE_PETS_CHARGE_TYPE_UNSPECIFIED = 0;
    // Each pet is charged the same amount.
    SAME_CHARGE_PER_PET = 1;
    // First pet full price, others different.
    DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS = 2;
  }

  // Only applicable when surcharge_type == CHARGE_24_HOUR
  optional TimeBasedPricingType time_based_pricing_type = 18;

  // Applicable only if time_based_pricing_type == HOURLY_EXCEED_TIERED_RATE
  optional MultiplePetsChargeType multiple_pets_charge_type = 19;

  // List of rules for hourly exceed charges, applicable only if time_based_pricing_type == HOURLY_EXCEED_TIERED_RATE
  repeated ServiceChargeExceedHourRule hourly_exceed_rules = 20;

  // whether the service charge is available for all services
  optional bool enable_service_filter = 21;

  // service filters
  repeated ServiceFilter service_filter_rules = 22;
}

// service filter
message ServiceFilter {
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 1;
  // whether the service charge is available for all services
  bool available_for_all_services = 2;
  // available service ids (only if available_for_all_services is false)
  repeated int64 available_service_id_list = 3;
}

//service charge location override
message ServiceChargeLocationOverride {
  // location id
  int64 business_id = 1;
  // price
  optional double price = 2;
  //tax id
  optional int32 tax_id = 3;
}

// service charge in online booking view
message ServiceChargeOnlineBookingView {
  // id
  int64 id = 1;
  // name
  string name = 3;
  // price
  double price = 5;
  // apply quantity
  int32 apply_quantity = 6;
  // total price
  double total_price = 7;
}

// feeding medication charge view
message FeedingMedicationChargeView {
  // id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // name
  string name = 3;
  // price
  double price = 4;
  // charge method
  moego.models.order.v1.ChargeMethod charge_method = 5;
  // food source ids
  repeated int64 food_source_ids = 6;
  // is all food source
  bool is_all_food_source = 7;
  // associated food source
  repeated FoodSource associated_food_source = 8;

  // food source model
  message FoodSource {
    // food source id
    int64 food_source_id = 1;
    // food source name
    string food_source_name = 2;
  }
}

// Represents a fee that applies when the service duration exceeds a certain number of hours.
message ServiceChargeExceedHourRule {
  // Name of the fee (e.g., "Peak Hour Fee").
  string fee_name = 1;

  // hour (0–24)
  int32 hour = 2;

  // Base price for one pet.
  double base_price = 3;

  // Optional additional price for each extra pet.
  // Only used if multiple_pets_charge_type == DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS.
  optional double additional_pet_price = 4;
}
