syntax = "proto3";

package moego.models.order.v1;

import "google/type/decimal.proto";
import "google/type/money.proto";
import "moego/models/order/v1/deposit_rule_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// Create a deposit rule
message CreateDepositRuleDef {
  // Name
  string name = 1;
  // filters
  repeated DepositFilter filters = 2;
  // Deposit config
  oneof deposit_config {
    // By amount
    google.type.Money deposit_by_amount = 3;
    // By percentage (1 for 1%)
    google.type.Decimal deposit_by_percentage = 4;
  }
}

// Update a deposit rule
message UpdateDepositRuleDef {
  // Name
  string name = 1;
  // filters
  repeated DepositFilter filters = 2;
  // Deposit config
  oneof deposit_config {
    // By amount
    google.type.Money deposit_by_amount = 3;
    // By percentage (1 for 1%)
    google.type.Decimal deposit_by_percentage = 4;
  }
}
