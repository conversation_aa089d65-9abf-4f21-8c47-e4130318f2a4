syntax = "proto3";

package moego.models.order.v1;

import "google/type/decimal.proto";
import "google/type/money.proto";
import "moego/models/order/v1/order_enums.proto";
import "moego/models/order/v1/refund_order_enums.proto";
import "moego/models/payment/v1/payment_method_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// Refund Order.
// 与 Order 对应.
message RefundOrderModel {
  // Refund order ID.
  int64 id = 1;
  // 关联的 Order 的 ID.
  int64 order_id = 2;
  // 关联的 Order 的 company ID.
  int64 company_id = 3;
  // 关联的 Order 的 Business ID.
  int64 business_id = 4;
  // 关联的 Order 的 Customer ID.
  int64 customer_id = 5;
  // 发起退款的 staff ID.
  int64 staff_id = 6;

  // 退款模式.
  RefundMode refund_mode = 11;
  // 退款原因.
  string refund_reason = 12;
  // 退款单创建时 Order 的状态.
  OrderStatus order_status_snapshot = 13;

  // 货币，与关联的 Order 中的 Currency 相同.
  string currency_code = 21;
  // 退款总金额
  //     refund_total_amount =   refund_item_sub_total
  //                           - refund_item_discount_total
  //                           + refund_tips
  //                           + refund_convenience_fee
  //                           + refund_tax_fee
  //                           - refund_deposit
  // 内部的 currency_code 需要与上面的 currency_code 相同.
  google.type.Money refund_total_amount = 22;
  // 关联的 Refund order items 的 refund_sub_total 总和.
  // 包含在 refund_total_amount 中.
  // 内部的 currency_code 需要与上面的 currency_code 相同.
  google.type.Money refund_item_sub_total = 23;
  // 关联的 Refund order items 的 refund_discount 总和.
  // 包含在 refund_total_amount 中.
  // 内部的 currency_code 需要与上面的 currency_code 相同.
  google.type.Money refund_item_discount_total = 24;
  // Tips 退款.
  // 包含在 refund_total_amount 中.
  // 内部的 currency_code 需要与上面的 currency_code 相同.
  // 与 Order 对齐.
  google.type.Money refund_tips = 25;
  // Convenience fee 退款.
  // 包含在 refund_total_amount 中.
  // 内部的 currency_code 需要与上面的 currency_code 相同.
  // 与 Order 对齐.
  google.type.Money refund_convenience_fee = 26;
  // Tax 退款，关联的 Refund order items 的 refund_tax_fee 总和.
  // 包含在 refund_total_amount 中.
  // 内部的 currency_code 需要与上面的 currency_code 相同.
  // 与 Order 对齐.
  google.type.Money refund_tax_fee = 27;
  // 退回的被抵扣的订金金额.
  // 包含在 refund_total_amount 中.
  // 内部的 currency_code 需要与上面的 currency_code 相同.
  google.type.Money refund_deposit = 28;

  // 多预留一点空位给退款明细

  // Refund order status.
  RefundOrderStatus refund_order_status = 41;
  // 创建时间，单位秒.
  int64 create_time = 42;
  // 退款时间，单位秒.
  int64 refund_time = 43;
  // 更新时间，单位秒.
  int64 update_time = 44;
}

// Refund Order Item.
// 与 Order Item 对应.
message RefundOrderItemModel {
  // Refund order item ID.
  int64 id = 1;
  // 关联的 Order 的 ID.
  int64 order_id = 2;
  // 关联的 Order Item 的 ID.
  int64 order_item_id = 3;
  // 关联的 Refund Order 的 ID.
  int64 refund_order_id = 4;
  // 关联的 Order 的 company ID.
  int64 company_id = 5;
  // 关联的 Order 的 Business ID.
  int64 business_id = 6;
  // 关联的 Order 的 Customer ID.
  int64 customer_id = 7;
  // 关联的 Order Item 的 StaffID.
  int64 staff_id = 8;
  // 关联的 Order Item 的 StaffIDs.
  repeated int64 staff_ids = 9;

  // 关联的 Order Item 的 object id.
  int64 item_id = 11;
  // 关联的 Order Item 的 item type.
  string item_type = 12;
  // 关联的 Order Item 的 item name.
  string item_name = 13;
  // 关联的 Order Item 的 item description.
  string item_description = 14;
  // 关联的 Order Item 的 unit price.
  // 内部的 currency_code 需要与下面的 currency_code 相同.
  google.type.Money item_unit_price = 15;
  // Tax ID.
  int64 tax_id = 16;
  // Tax Rate.
  google.type.Decimal tax_rate = 17;
  // Tax name.
  string tax_name = 18;
  // Pet ID.
  int64 pet_id = 19;

  // Refund Order Item 的退款模式.
  RefundItemMode refund_order_item_mode = 21;
  // 货币，与关联的 Order 中的 Currency 相同.
  string currency_code = 22;
  // 该 Item 的总的退款金额.
  //     refund_total_amount =  (unit_price * refund_quantity)
  //                           + refund_amount
  //                           - refund_discount
  //                           + refund_tax
  // 特别的, 当退款模式为 ByAmount 时, RefundTotalAmount 的值等于指定的值.
  //
  // 内部的 currency_code 需要与下面的 currency_code 相同.
  google.type.Money refund_total_amount = 23;
  // Refund 数量.
  // 仅当 RefundItemMode 为 ByQuantity 时有效，其他模式时保持为 0.
  int64 refund_quantity = 24;
  // Refund 金额.
  // 仅当 RefundItemMode 为 ByAmount 时有效，其他模式时保持为 0.
  // 内部的 currency_code 需要与上面的 currency_code 相同.
  google.type.Money refund_amount = 25;
  // Refund discount 金额.
  // 由本次退款的金额占原 Order item 的比例计算得出.
  // 如果本次退款导致原 Order item 全部退款，需要轧差.
  // 内部的 currency_code 需要与上面的 currency_code 相同.
  google.type.Money refund_discount = 26;
  // Refund tax.
  // 由本次退款的金额占原 Order item 的比例计算得出.
  // 如果本次退款导致原 Order item 全部退款，需要轧差.
  // 内部的 currency_code 需要与上面的 currency_code 相同.
  google.type.Money refund_tax = 27;

  // 多预留一点空位给退款明细

  // 创建时间，单位秒.
  int64 create_time = 41;
}

// Refund Order Payment.
// 与 Order Payment 对齐.
message RefundOrderPaymentModel {
  // Refund order payment ID.
  int64 id = 1;
  // 关联的 Order 的 ID.
  int64 order_id = 2;
  // 关联的 Order payment 的 ID.
  int64 order_payment_id = 3;
  // 关联的 Refund Order 的 ID.
  int64 refund_order_id = 4;
  // 关联的 Order 的 company ID.
  int64 company_id = 5;
  // 关联的 Order 的 Business ID.
  int64 business_id = 6;
  // 关联的 Order 的 Customer ID.
  int64 customer_id = 7;
  // 发起退款的 staff ID.
  int64 staff_id = 8;
  // 关联的 Refund Payment 的 ID.
  // 在进入 TransactionCreated 状态时写入.
  int64 refund_payment_id = 9;

  // 退款方式的 ID.
  // **通常** 与关联的 order_payment.payment_method_id 一致.
  int64 refund_payment_method_id = 11;
  // 退款方式的名字.
  string refund_payment_method = 12;
  // 退款方式用于展示的名字.
  string refund_payment_method_display_name = 13;
  // 退款方式的扩展字段，不同的退款方式内容不同.
  payment.v1.RefundPaymentMethodExtra refund_payment_method_extra = 14;
  // 退款方式的供应商.
  string refund_payment_method_vendor = 15;

  // 退款的货币，与 order payment 相同.
  string currency_code = 21;
  // 总的退款金额.
  google.type.Money refund_total_amount = 22;
  // 总的退款金额中包含的 ConvenienceFee.
  google.type.Money refund_payment_convenience_fee = 23;

  // Refund order payment 状态.
  RefundOrderPaymentStatus refund_order_payment_status = 31;
  // 搭配 Refund order payment status 展示，进入该的原因:
  //     Failed -> 失败的原因
  //     Canceled -> 取消的原因
  string payment_status_reason = 32;
  // 创建时间.
  int64 create_time = 33;
  // 退款时间，单位秒.
  int64 refund_time = 34;
  // 退款失败的时间，单位秒.
  int64 fail_time = 35;
  // 取消退款的时间，单位秒.
  int64 cancel_time = 36;
  // 更新时间，单位秒.
  int64 update_time = 37;
}

// Refund order detail.
message RefundOrderDetailModel {
  // Refund order.
  RefundOrderModel refund_order = 1;
  // Refund order items.
  repeated RefundOrderItemModel refund_order_items = 2;
  // Refund order payments.
  repeated RefundOrderPaymentModel refund_order_payments = 3;
}
