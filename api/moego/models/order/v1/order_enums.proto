syntax = "proto3";

package moego.models.order.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// OrderStatus is the status of an order.
enum OrderStatus {
  // created
  CREATED = 0;
  // processing
  PROCESSING = 1;
  // completed
  COMPLETED = 2;
  // removed
  REMOVED = 3;
}

// OrderSourceType is the source type of an order.
enum OrderSourceType {
  // unspecified
  ORDER_SOURCE_TYPE_UNSPECIFIED = 0;
  // unknown
  UNKNOWN = 1;
  // appointment
  APPOINTMENT = 2;
  // no show
  NO_SHOW = 3;
  // product
  PRODUCT = 4;
  // package
  PACKAGE = 5;
  // booking request
  BOOKING_REQUEST = 6;
  // fulfillment
  FULFILLMENT = 7;
  // Membership.
  MEMBERSHIP = 8;
}

// event type
// Deprecated: 统一使用 moego.models.event_bus.v1.EventType
enum EventType {
  // unspecified
  EVENT_TYPE_UNSPECIFIED = 0;
  // order completed
  ORDER_COMPLETED = 1;
  // refund order completed
  REFUND_ORDER_COMPLETED = 2;
  // order cancelled
  ORDER_CANCELLED = 3;
}

// event delivery status
enum EventDeliveryStatus {
  // unspecified
  EVENT_DELIVERY_STATUS_UNSPECIFIED = 0;
  // pending
  PENDING = 1;
  // sent, 发送成功
  SENT = 2;
  // failed
  FAILED = 3;
  // canceled
  CANCELED = 4;
}

// Order Payment 的状态.
enum OrderPaymentStatus {
  // 未指定状态.
  ORDER_PAYMENT_STATUS_UNSPECIFIED = 0;
  // 已创建.
  ORDER_PAYMENT_STATUS_CREATED = 100;
  // 支付交易已发起.
  ORDER_PAYMENT_STATUS_TRANSACTION_CREATED = 200;
  // 终态 - 已支付.
  ORDER_PAYMENT_STATUS_PAID = 300;
  // 终态 - 支付失败.
  ORDER_PAYMENT_STATUS_FAILED = 400;
  // 终态 - 已取消.
  ORDER_PAYMENT_STATUS_CANCELED = 500;
}

// Item type.
// 目前写入订单的都是不包含 “ITEM_TYPE_” 前缀的，转换成小写的字符串。其中 NO_SHOW 写入的是 noshow.
// 在 moego-svc-order 下线后会统一刷数据完成转换.
enum ItemType {
  // Unspecified.
  ITEM_TYPE_UNSPECIFIED = 0;
  // Service: service.
  ITEM_TYPE_SERVICE = 1;
  // Product: product.
  ITEM_TYPE_PRODUCT = 2;
  // Package: package.
  ITEM_TYPE_PACKAGE = 3;
  // No show: noshow.
  ITEM_TYPE_NO_SHOW = 4;
  // Service charge: service_charge.
  ITEM_TYPE_SERVICE_CHARGE = 5;
  // Evaluation service: evaluation_service.
  ITEM_TYPE_EVALUATION_SERVICE = 6;
  // Cancellation fee: cancellation_fee.
  ITEM_TYPE_CANCELLATION_FEE = 7;
  // Membership product: membership_product.
  ITEM_TYPE_MEMBERSHIP_PRODUCT = 8;
  // Deposit: deposit.
  ITEM_TYPE_DEPOSIT = 9;
}

// Discount Code Type
// 与 Marketing.DiscountCodeType 相同。
// 但是这里只定义了订单已经支持的类型。
enum DiscountCodeType {
  // unspecified value
  DISCOUNT_CODE_TYPE_UNSPECIFIED = 0;
  // discount code percentage
  DISCOUNT_CODE_TYPE_PERCENTAGE = 1;
  // discount code fixed amount
  DISCOUNT_CODE_TYPE_FIXED_AMOUNT = 2;
  // discount code credit
  DISCOUNT_CODE_TYPE_CREDIT = 3;
}

// 折扣应用类型
// TODO(yunxiang)： 确认这里到底是什么含义.
enum DiscountApplyType {
  // 未指定.
  DISCOUNT_APPLY_TYPE_UNSPECIFIED = 0;
  // TYPE_ALL.
  DISCOUNT_APPLY_TYPE_ALL = 1;
  // TYPE_ITEM.
  DISCOUNT_APPLY_TYPE_ITEM = 2;
  // 可应用到 Product 类型的 Item.
  DISCOUNT_APPLY_TYPE_PRODUCT = 3;
  // 可应用到 Package 类型的 Item.
  DISCOUNT_APPLY_TYPE_PACKAGE = 4;
  // 可应用到 Service 类型的 Item.
  DISCOUNT_APPLY_TYPE_SERVICE = 5;
}
