syntax = "proto3";

package moego.models.order.v1;

import "google/type/decimal.proto";
import "google/type/money.proto";
import "moego/models/order/v1/order_enums.proto";
import "moego/models/order/v1/order_line_discount_models.proto";
import "moego/models/order/v1/order_line_extra_fee_models.proto";
import "moego/models/order/v1/order_line_tax_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

/**
 * line item info
 */
message OrderLineItemModel {
  // id
  optional int64 id = 1;
  // business id
  int64 business_id = 2;
  // object id
  int64 object_id = 3;
  // item type
  string type = 4;
  // item name
  string name = 5;
  // item unit price
  double unit_price = 6;
  // total quantity
  int32 quantity = 7;
  // staff id
  optional int64 staff_id = 8;
  // order id
  optional int64 order_id = 9;
  // is deleted
  optional bool is_deleted = 10;
  // item description
  optional string description = 11;
  // quantity of using package
  optional int32 purchased_quantity = 12;
  // tips distribute on this item
  optional double tips_amount = 13;
  // tax of this item
  optional double tax_amount = 14;
  // discount distribute on this item
  optional double discount_amount = 15;
  // extra fee distribute on this item
  optional double extra_fee_amount = 16;
  // subTotal amount
  optional double sub_total_amount = 17;
  // total amount
  optional double total_amount = 18;
  // create time
  optional int64 create_time = 19;
  // update time
  optional int64 update_time = 20;

  // tax applied on this item
  repeated OrderLineTaxModel line_taxes = 21;
  // discount applied on this item
  repeated OrderLineDiscountModel line_discounts = 22;
  // extra fee applied on this item
  repeated OrderLineExtraFeeModel line_extra_fees = 23;

  // pet ID.
  int64 pet_id = 24;
  // pet detail ID.
  int64 pet_detail_id = 25;

  // Tax ID
  int64 tax_id = 26;
  // Tax Rate
  google.type.Decimal tax_rate = 27;
  // Tax name
  string tax_name = 28;

  // currency code.
  string currency_code = 30;
  // 已退数量.
  int32 refunded_quantity = 31;
  // 已退总金额.
  google.type.Money refunded_amount = 32;
  // 已退的税.
  google.type.Money refunded_tax_amount = 33;
  // 已退的 discount.
  google.type.Money refunded_discount_amount = 34;
}

// Order item. 与原有的 OrderLineItemModel 兼容.
// 与 RefundOrderItem 对应.
message OrderItemModel {
  // id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // object id
  int64 object_id = 3;
  // item type
  string type = 4;
  // item name
  string name = 5;
  // item unit price
  // 存在 SubtotalDetail 时，会保证 UnitPrice 是由 Detail / quantity 计算得出.
  google.type.Money unit_price = 6;
  // total quantity
  int32 quantity = 7;
  // staff id
  // 当 staff id != 0 时，也会包含在 staff_ids 字段中
  int64 staff_id = 8;
  // order id
  int64 order_id = 9;
  // is deleted
  bool is_deleted = 10;
  // item description
  string description = 11;
  // quantity of using package
  int32 purchased_quantity = 12;
  // tips distribute on this item
  google.type.Money tips_amount = 13;
  // tax of this item
  google.type.Money tax_amount = 14;
  // discount distribute on this item
  google.type.Money discount_amount = 15;
  // 当前没有分摊 convenience fee 到 item 上, 先移除.
  //  // convenience fee distribute on this item
  //  google.type.Money convenience_fee = 16;
  // subTotal amount
  google.type.Money sub_total_amount = 17;
  // total amount
  google.type.Money total_amount = 18;
  // create time
  int64 create_time = 19;
  // update time
  int64 update_time = 20;

  // discount applied on this item
  repeated OrderLineDiscountModelV1 line_discounts = 22;

  // pet ID.
  int64 pet_id = 24;
  // pet detail ID.
  int64 pet_detail_id = 25;

  // Tax ID
  int64 tax_id = 26;
  // Tax Rate
  google.type.Decimal tax_rate = 27;
  // Tax name
  string tax_name = 28;

  // currency code.
  string currency_code = 30;
  // 已退数量.
  int32 refunded_quantity = 31;
  // 已退总金额.
  google.type.Money refunded_amount = 32;
  // 已退的税.
  google.type.Money refunded_tax_amount = 33;
  // 已退的 discount.
  google.type.Money refunded_discount_amount = 34;
  // 关联的 Staff 的 ID.
  repeated int64 staff_ids = 35;

  // SubTotal 详情. 存在详情时，会保证 UnitPrice 是由 PriceDetail 除以 Quantity 计算得出.
  PriceDetailModel subtotal_detail = 36;
  // External UUID 外部的 UUID，常见场景是 petDetailID，或者 fulfillment ID 等.
  string external_uuid = 37;
}

// 支持计算得到的 Price.
message PriceDetailModel {
  // 每一个条用于计算 UnitPrice 的项目.
  // 按顺序依次计算.
  repeated PriceItem price_items = 1;

  // 单条影响单价的条目.
  message PriceItem {
    // 名字.
    string name = 1;
    // 计算方式.
    Operator operator = 2;
    // 影响次数.
    int32 quantity = 3;
    // 影响的金额.
    google.type.Money unit_price = 4;
    // 影响的总金额.
    google.type.Money sub_total = 5;
    // Object type that contributes to this price item.
    ItemType object_type = 6;
    // Object id that contributes to this price item.
    int64 object_id = 7;

    // 计算方式.
    enum Operator {
      // 无.
      OPERATOR_UNSPECIFIED = 0;
      // 加.
      ADD = 1;
      // 降.
      SUBTRACT = 2;
    }
  }
}
