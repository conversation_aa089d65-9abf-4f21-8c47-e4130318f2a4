// @since 2023-09-05 17:03:16
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.risk_control.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/risk_control/v1;riskcontrolpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.risk_control.v1";

// recaptcha version
enum RecaptchaVersion {
  // unspecified
  RECAPTCHA_VERSION_UNSPECIFIED = 0;
  // V2
  RECAPTCHA_VERSION_V2 = 2;
  // V3
  RECAPTCHA_VERSION_V3 = 3;
}

// recaptcha v3 action
enum RecaptchaAction {
  // unspecified
  RECAPTCHA_ACTION_UNSPECIFIED = 0;
  // intake form submit
  RECAPTCHA_ACTION_IF_SUBMIT = 1;
  // online booking 2.0 submit
  RECAPTCHA_ACTION_OB_V2_SUBMIT = 2;
  // online booking 3.0 submit
  RECAPTCHA_ACTION_OB_V3_SUBMIT = 3;
  // online booking 3.0 send verification code
  RECAPTCHA_ACTION_OB_V3_SEND_CODE = 4;
}
