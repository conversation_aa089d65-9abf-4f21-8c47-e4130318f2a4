syntax = "proto3";

package moego.models.account.v1;

import "moego/models/account/v1/account_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1;accountpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.account.v1";

// namespace def
message NamespaceDef {
  // namespace type, default is MOEGO
  models.account.v1.AccountNamespaceType type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // namespace id, default is 0
  int64 id = 2 [(validate.rules).int64 = {gte: 0}];
}
