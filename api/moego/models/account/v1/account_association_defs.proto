syntax = "proto3";

package moego.models.account.v1;

import "google/protobuf/struct.proto";
import "moego/models/account/v1/account_association_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1;accountpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.account.v1";

// create def for account association
message AccountAssociationCreateDef {
  // account id
  int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];

  // platform
  models.account.v1.AccountAssociationPlatform platform = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // platform account id
  string platform_account_id = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];

  // if this association is visible for the user, default is false
  bool visible = 4;

  // platform data
  google.protobuf.Struct platform_data = 5;
}

// query def for account and platform
message AccountAndPlatformQueryDef {
  // account id
  int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];

  // platform
  models.account.v1.AccountAssociationPlatform platform = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// query def for platform account
message PlatformAccountQueryDef {
  // platform
  models.account.v1.AccountAssociationPlatform platform = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // platform account id
  string platform_account_id = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
}
