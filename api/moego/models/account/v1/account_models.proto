syntax = "proto3";

package moego.models.account.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/account/v1/account_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1;accountpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.account.v1";

// account model
message AccountModel {
  // account id
  int64 id = 1;

  // email
  string email = 2;

  // phone number
  string phone_number = 3;

  // first name
  string first_name = 4;

  // last name
  string last_name = 5;

  // avatar path
  string avatar_path = 6;

  // status
  AccountStatus status = 7;

  // namespace
  Namespace namespace = 8;

  // created at
  google.protobuf.Timestamp created_at = 10;

  // updated at
  google.protobuf.Timestamp updated_at = 11;
}

// namespace
message Namespace {
  // namespace type
  models.account.v1.AccountNamespaceType type = 1;

  // namespace id
  int64 id = 2;
}

// account model search view
message AccountModelSearchView {
  // account id
  int64 id = 1;

  // email
  string email = 2;

  // first name
  string first_name = 4;

  // last name
  string last_name = 5;
}

// account model relevant view
message AccountModelRelevantView {
  // account id
  int64 id = 1;
  // email
  string email = 2;
  // first name
  string first_name = 3;
  // last name
  string last_name = 4;
}

// account security model
message AccountSecurityModel {
  // password last update time
  google.protobuf.Timestamp password_last_update_time = 1;
}
