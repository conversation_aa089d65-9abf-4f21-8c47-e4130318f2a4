syntax = "proto3";

package moego.models.account.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1;accountpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.account.v1";

// account association platform
enum AccountAssociationPlatform {
  // unspecified
  ACCOUNT_ASSOCIATION_PLATFORM_UNSPECIFIED = 0;
  // 1~10 is invisible platform for user
  // hubspot_contact
  HUBSPOT_CONTACT = 1;
  // intercom
  INTERCOM = 2;

  // 11~20 is visible platform for user
  // google
  GOOGLE = 11;
}
