syntax = "proto3";

package moego.models.account.v1;

import "google/protobuf/duration.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/account/v1/session_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1;accountpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.account.v1";

// session model
message SessionModel {
  // session id
  int64 id = 1;

  // account id
  int64 account_id = 2;

  // referer link
  string referer_link = 3;

  // referer session id
  int64 referer_session_id = 4;

  // ip
  string ip = 5;

  // user agent
  string user_agent = 6;

  // device id
  string device_id = 16;

  // source
  string source = 7;

  // impersonator
  string impersonator = 8;

  // session data
  google.protobuf.Struct session_data = 9;

  // created at
  google.protobuf.Timestamp created_at = 10;

  // updated at
  google.protobuf.Timestamp updated_at = 11;

  // last accessed at
  google.protobuf.Timestamp last_accessed_at = 12;

  // max age
  google.protobuf.Duration max_age = 13;

  // status
  SessionStatus status = 14;

  // renewable
  bool renewable = 15;
}
