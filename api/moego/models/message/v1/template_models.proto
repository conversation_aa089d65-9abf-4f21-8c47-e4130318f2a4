// @since 2023-12-07 11:36:34
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.message.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// template model
message TemplateModel {
  // email template id
  int64 id = 1;
  // subject for email
  string subject = 2;
  // body for message
  string body = 3;
}
