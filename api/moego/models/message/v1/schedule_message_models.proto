syntax = "proto3";

package moego.models.message.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/message/v1/message_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// Schedule message model, for management of scheduled tasks
message ScheduleMessageModel {
  // schedule message id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
  // receipt customer id
  int64 customer_id = 4;
  // receipt customer contact id
  int64 contact_id = 5;
  // the staff id of created this record
  int64 creator_id = 6;
  // the staff id of updated this record
  int64 updater_id = 7;
  // the staff id of sent this record
  int64 sender_id = 8;
  // message content
  string content = 9;
  // auto message type
  AutoMessageType auto_message_type = 10;
  // auto message resource id, such as appointment id
  int64 auto_message_resource_id = 11;
  // status
  ScheduleMessageStatus status = 12;
  // send out at, preset sending time
  google.protobuf.Timestamp send_out_at = 13;
  // actual successful sent time
  google.protobuf.Timestamp sent_at = 14;
  // created at
  google.protobuf.Timestamp created_at = 15;
  // updated at
  google.protobuf.Timestamp updated_at = 16;
  // deleted at
  google.protobuf.Timestamp deleted_at = 17;
  // method
  models.message.v1.Method method = 18;
}

// Schedule message model public view
message ScheduleMessagePublicView {
  // schedule message id
  int64 id = 1;
  // receipt customer id
  int64 customer_id = 2;
  // the staff id of created this record
  int64 creator_id = 3;
  // the staff id of updated this record
  int64 updater_id = 4;
  // the staff id of sent this record
  int64 sender_id = 5;
  // message content
  string content = 6;
  // receipt contact id
  int64 contact_id = 7;
  // auto message type
  AutoMessageType auto_message_type = 8;
  // auto message resource id, such as appointment id
  int64 auto_message_resource_id = 9;
  // status
  ScheduleMessageStatus status = 10;
  // send out at, preset sending time
  google.protobuf.Timestamp send_out_at = 11;
  // actual successful sent time
  google.protobuf.Timestamp sent_at = 12;
  // method
  models.message.v1.Method method = 13;
}
