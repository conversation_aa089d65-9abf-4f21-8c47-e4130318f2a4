// @since 2023-12-07 11:36:34
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.message.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/message/v1/message_template_defs.proto";
import "moego/models/message/v1/message_template_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// The MessageTemplate model
message MessageTemplateModel {
  // the unique id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
  // template name
  string template_name = 4;
  // template content
  string template_content = 5;
  // deleted
  bool deleted = 6;
  // creator id
  int64 creator_id = 7;
  // updater id
  int64 updater_id = 8;
  // create time
  google.protobuf.Timestamp created_at = 14;
  // update time
  google.protobuf.Timestamp updated_at = 15;
  // deleted time
  google.protobuf.Timestamp deleted_at = 16;
}

// The MessageTemplate placeholder model
message MessageTemplatePlaceHolderModel {
  // deprecated
  option deprecated = true;
  // the unique id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
  // placeholder group
  string placeholder_group = 4;
  // placeholder name
  string placeholder_name = 5;
  // placeholder content
  string placeholder_text = 6;
  // deleted
  bool deleted = 7;
  // creator id
  int64 creator_id = 8;
  // updater id
  int64 updater_id = 9;
  // create time
  google.protobuf.Timestamp created_at = 10;
  // update time
  google.protobuf.Timestamp updated_at = 11;
  // deleted time
  google.protobuf.Timestamp deleted_at = 12;
}

// message template simple view
message MessageTemplateSimpleView {
  // the unique id
  int64 id = 1;
  // template name
  string template_name = 2;
  // template content
  string template_content = 3;
  // update time
  google.protobuf.Timestamp updated_at = 4;
  // updater name
  string updater_name = 5;
  // is system
  bool is_system = 6;
  // company id
  int64 company_id = 7;
  // business id
  int64 business_id = 8;
  // template use case. 目前仅用于拉取模版变量
  moego.models.message.v1.MessageTemplateUseCase use_case = 9;
  // template type
  moego.models.message.v1.MessageTemplateType type = 10;
  // enterprise id
  int64 enterprise_id = 11;
}

// message template detail view
message MessageTemplateDetailView {
  // the unique id
  int64 id = 1;
  // template name
  string template_name = 2;
  // template content
  string template_content = 3;
  // update time
  google.protobuf.Timestamp updated_at = 4;
  // updater name
  string updater_name = 5;
  // enterprise id
  int64 enterprise_id = 6;
}

// message template placeholder simple view
message MessageTemplatePlaceholderSimpleView {
  // placeholder group
  string placeholder_group = 1;
  // placeholder name
  string placeholder_name = 2;
  // placeholder content
  string placeholder_text = 3;
  // example text
  string example_text = 4;
  // result text
  string result_text = 5;
}
