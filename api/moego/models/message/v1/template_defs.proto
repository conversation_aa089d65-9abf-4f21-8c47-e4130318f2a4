// @since 2023-12-07 11:36:34
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.message.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// template batch create item def
message BatchCreateTemplateItemDef {
  // seq num
  int64 seq_num = 1;
  // subject for template
  string subject = 2 [(validate.rules).string = {max_len: 256}];
  // body for template
  string body = 3 [(validate.rules).string = {max_len: 131072}];
}
