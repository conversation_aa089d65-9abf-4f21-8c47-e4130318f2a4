syntax = "proto3";

package moego.models.message.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// marketing email model simple view
message EmailDef {
  // email subject
  string subject = 1 [(validate.rules).string.max_len = 200];
  // email content
  string content = 2 [(validate.rules).string.max_len = 100000];
  // for schedule emails, the send time
  google.protobuf.Timestamp send_at = 3;
  // recipients
  repeated RecipientDef recipients = 4;
  // email attachments
  repeated AttachmentDef attachment_urls = 5;
}

// recipient filter
message RecipientFilterDef {
  // client filter, JSON string
  string client_filter = 1 [(validate.rules).string.max_len = 10000];
  // exclude customer ids
  repeated int64 exclude_customer_ids = 2;
  // include customer ids
  repeated int64 include_customer_ids = 3;
}

// marketing email attachment
message AttachmentDef {
  // file name
  string name = 1 [(validate.rules).string.max_len = 128];
  // file mime_type
  string type = 2 [(validate.rules).string.max_len = 256];
  // file content/url
  oneof file {
    option (validate.required) = true;
    // file content
    bytes content = 3 [(validate.rules).bytes.max_len = 1000000];
    // url on s3
    string url = 4 [(validate.rules).string.uri = true];
  }
}

// marketing email recipient
message RecipientDef {
  // customer id
  int64 customer_id = 1 [(validate.rules).int64.gt = 0];
  // name
  string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 128
  }];
  // email
  string email = 3 [(validate.rules).string.email = true];
}
