syntax = "proto3";

package moego.models.message.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// notification unread count info def
message NotificationUnreadCountInfoDef {
  // activity unread count
  int32 activity_count = 1;
  // system unread count
  int32 system_count = 2;
  // pending review count
  int32 pending_review_count = 3;
}
