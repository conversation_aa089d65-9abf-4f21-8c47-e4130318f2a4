syntax = "proto3";

package moego.models.message.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/message/v1/message_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// Auto message template model for business
message AutoMessageTemplateModel {
  // auto message template id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
  // auto message type
  AutoMessageType type = 4;
  // message content
  string body = 5;
  // is enabled
  bool is_enabled = 6;
  // created at
  google.protobuf.Timestamp created_at = 7;
  // updated at
  google.protobuf.Timestamp updated_at = 8;
}

// Auto message template public view
message AutoMessageTemplatePublicView {
  // auto message type
  AutoMessageType type = 4;
  // message content
  string body = 5;
  // is enabled
  bool is_enabled = 6;
}
