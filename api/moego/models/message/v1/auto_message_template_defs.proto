syntax = "proto3";

package moego.models.message.v1;

import "moego/models/message/v1/message_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// Auto message template definition for appointment
message AutoMessageAppointmentDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64.gt = 0];
  // auto message template type
  models.message.v1.AutoMessageType type = 2 [(validate.rules).enum = {
    defined_only: true
    in: [
      1,
      2,
      3,
      4,
      10
    ]
  }];
}
