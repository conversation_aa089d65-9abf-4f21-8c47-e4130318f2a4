syntax = "proto3";

package moego.models.message.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// notification tab type
enum NotificationTabType {
  // unspecified
  NOTIFICATION_TAB_TYPE_UNSPECIFIED = 0;
  // all
  NOTIFICATION_TAB_TYPE_ALL = 1;
  // activity
  NOTIFICATION_TAB_TYPE_ACTIVITY = 2;
  // system
  NOTIFICATION_TAB_TYPE_SYSTEM = 3;
  // pending review
  NOTIFICATION_TAB_TYPE_PENDING_REVIEW = 4;
}

// notification record send type
enum NotificationRecordSendType {
  // unspecified
  NOTIFICATION_RECORD_SEND_TYPE_UNSPECIFIED = 0;
  // succeed
  NOTIFICATION_RECORD_SEND_TYPE_SUCCEED = 1;
  // failed
  NOTIFICATION_RECORD_SEND_TYPE_FAILED = 2;
}

// notification record status
enum NotificationRecordStatus {
  // unspecified
  NOTIFICATION_RECORD_STATUS_UNSPECIFIED = 0;
  // normal
  NOTIFICATION_RECORD_STATUS_NORMAL = 1;
  // dismiss
  NOTIFICATION_RECORD_STATUS_DISMISS = 2;
}
