syntax = "proto3";

package moego.models.message.v1;

import "moego/models/message/v1/message_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// phone user
message PhoneUser {
  // user phone number
  string phone_number = 1;
  // user name
  optional string name = 2;
}

// email user
message EmailUser {
  // user email
  string email = 1;
  // user name
  optional string name = 2;
}

// message model
message MessageModel {
  // message id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // customer id
  int64 customer_id = 3;
  // staff id
  int64 staff_id = 4;
  // target id
  int64 target_id = 5;
  // target type
  moego.models.message.v1.TargetType target_type = 6;
  // message type
  moego.models.message.v1.MessageType msg_type = 7;

  // ...
}
