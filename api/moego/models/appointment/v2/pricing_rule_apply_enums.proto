// @since 2025-03-05 10:27:11
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v2;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v2;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v2";

// pricing rule apply source type
enum PricingRuleApplySourceType {
  // unspecified value
  PRICING_RULE_APPLY_SOURCE_TYPE_UNSPECIFIED = 0;
  // appointment
  SOURCE_TYPE_APPOINTMENT = 1;
  // booking request
  SOURCE_TYPE_BOOKING_REQUEST = 2;
}
