syntax = "proto3";

package moego.models.appointment.v1;

import "google/type/date.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// pet playgroup model
message PetPlaygroupModel {
  // id
  int64 id = 1;
  // pet id
  int64 pet_id = 2;
  // playgroup id
  int64 playgroup_id = 3;
  // appointment id
  int64 appointment_id = 4;
  // date
  google.type.Date date = 5;
  // sort
  int32 sort = 6;
}
