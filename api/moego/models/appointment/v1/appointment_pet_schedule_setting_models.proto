syntax = "proto3";

package moego.models.appointment.v1;

import "moego/models/business_customer/v1/business_pet_schedule_setting_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// Appointment pet schedule setting for feeding and medication model
message AppointmentPetScheduleSettingModel {
  // id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // appointment id
  int64 appointment_id = 3;
  // schedule type, feeding or medication
  models.business_customer.v1.BusinessPetScheduleType schedule_type = 4;
  // schedule id, feeding id or medication id
  int64 schedule_id = 5;
  // schedule time, schedule time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
  int32 schedule_time = 6;
  // schedule extra json, such as schedule time label etc.
  map<string, string> schedule_extra_json = 7;
}
