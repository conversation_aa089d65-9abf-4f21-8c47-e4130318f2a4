syntax = "proto3";

package moego.models.appointment.v1;

import "moego/models/appointment/v1/appointment_pet_feeding_schedule_defs.proto";
import "moego/models/appointment/v1/appointment_pet_medication_schedule_defs.proto";
import "moego/models/appointment/v1/appointment_pet_schedule_setting_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// Pet's feeding and medication schedules
message PetScheduleDef {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Appointment feeding schedules
  repeated models.appointment.v1.AppointmentPetFeedingScheduleDef feedings = 2 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Appointment medication schedules
  repeated models.appointment.v1.AppointmentPetMedicationScheduleDef medications = 3 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// appointment pet schedule def
message AppointmentPetScheduleDef {
  // Pet's schedules
  repeated models.appointment.v1.PetScheduleDef schedules = 1;

  // Schedule settings
  repeated models.appointment.v1.AppointmentPetScheduleSettingModel schedule_settings = 2;
}
