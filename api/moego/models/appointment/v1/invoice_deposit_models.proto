// @since 2024-02-22 11:28:28
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/appointment/v1/invoice_deposit_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// invoice deposit model
message InvoiceDepositModel {
  // id
  int32 id = 1;
  // invoice id
  int64 invoice_id = 2;
  // business id
  int32 business_id = 3;
  // staff id
  int32 staff_id = 4;
  // amount
  double amount = 5;
  // create time
  google.protobuf.Timestamp create_time = 6;
  // update time
  google.protobuf.Timestamp update_time = 7;
  // status
  InvoiceDepositStatus status = 8;
  // guid
  string de_guid = 9;
  // company id
  int64 company_id = 10;
}
