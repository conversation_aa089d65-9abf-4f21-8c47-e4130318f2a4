syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// Appointment pet feeding model
message AppointmentPetFeedingModel {
  // id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // appointment id
  int64 appointment_id = 3;
  // pet detail id
  int64 pet_detail_id = 4;
  // pet id
  int64 pet_id = 5;
  // feeding amount, such as 1.2, 1/2, 1 etc.
  string feeding_amount = 6;
  // feeding unit, pet_metadata.metadata_value, metadata_name = 2
  string feeding_unit = 7;
  // feeding type, pet_metadata.metadata_value, metadata_name = 3
  string feeding_type = 8;
  // feeding source, pet_metadata.metadata_value, metadata_name = 4
  string feeding_source = 9;
  // feeding instruction, pet_metadata.metadata_value, metadata_name = 5
  string feeding_instruction = 10;
  // created at
  google.protobuf.Timestamp created_at = 11;
  // updated at
  google.protobuf.Timestamp updated_at = 12;
  // deleted at
  google.protobuf.Timestamp deleted_at = 13;
  // feeding note, user input
  string feeding_note = 14;
}
