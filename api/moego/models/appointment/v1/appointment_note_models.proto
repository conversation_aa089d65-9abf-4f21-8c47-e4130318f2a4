syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/appointment/v1/appointment_note_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// the appointment note model
message AppointmentNoteModel {
  // appointment note id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // customer id
  int64 customer_id = 3;
  // company id
  int64 company_id = 4;
  // grooming id
  int64 grooming_id = 5;
  // note
  string note = 6;
  // type
  AppointmentNoteType type = 7;
  // created at
  google.protobuf.Timestamp created_at = 8;
  // updated at
  google.protobuf.Timestamp updated_at = 9;
  // created by, staff id
  int64 created_by = 10;
  // updated by, staff id
  int64 updated_by = 11;
  // is deleted
  bool is_deleted = 12;
}

// the appointment note detail view
message AppointmentNoteDetailView {
  // appointment note id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // grooming id
  int64 grooming_id = 3;
  // note
  string note = 4;
  // type
  AppointmentNoteType type = 5;
  // updated at
  google.protobuf.Timestamp updated_at = 6;
  // updated by, staff id
  int64 updated_by = 7;
}
