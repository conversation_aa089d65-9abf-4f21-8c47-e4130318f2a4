syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// Appointment pet medication model
message AppointmentPetMedicationModel {
  // id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // appointment id
  int64 appointment_id = 3;
  // pet detail id
  int64 pet_detail_id = 4;
  // pet id
  int64 pet_id = 5;
  // medication amount, such as 1.2, 1/2, 1 etc.
  string medication_amount = 6;
  // medication unit, pet_metadata.metadata_value, metadata_name = 7
  string medication_unit = 7;
  // medication name, user input
  string medication_name = 8;
  // medication source, user input
  string medication_note = 9;
  // created at
  google.protobuf.Timestamp created_at = 10;
  // updated at
  google.protobuf.Timestamp updated_at = 11;
  // deleted at
  google.protobuf.Timestamp deleted_at = 12;
}
