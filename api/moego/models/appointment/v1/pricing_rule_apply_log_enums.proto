// @since 2024-12-09 11:49:21
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// pricing rule apply source type
enum PricingRuleApplySourceType {
  // unspecified value
  PRICING_RULE_APPLY_SOURCE_TYPE_UNSPECIFIED = 0;
  // appointment
  SOURCE_TYPE_APPOINTMENT = 1;
  // booking request
  SOURCE_TYPE_BOOKING_REQUEST = 2;
}
