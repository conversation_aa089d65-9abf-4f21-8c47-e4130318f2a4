// @since 2024-01-24 18:16:28
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// wait list status
enum WaitListStatus {
  // appointment only
  APPTONLY = 0;
  // wait list only
  WAITLISTONLY = 1;
  // both, appointment and wait list
  APPTANDWAITLIST = 2;
}
