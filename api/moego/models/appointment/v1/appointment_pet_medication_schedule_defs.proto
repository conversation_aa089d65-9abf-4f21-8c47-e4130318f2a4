syntax = "proto3";

package moego.models.appointment.v1;

import "moego/models/business_customer/v1/business_pet_feeding_medication_enum.proto";
import "moego/models/business_customer/v1/business_pet_schedule_setting_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// Appointment pet medication schedule definition
message AppointmentPetMedicationScheduleDef {
  // medication amount, such as 1.2, 1/2, 1 etc.
  string medication_amount = 1 [(validate.rules).string.max_len = 255];

  // medication unit, pet_metadata.metadata_value, metadata_name = 7
  string medication_unit = 2 [(validate.rules).string.max_len = 255];

  // medication name, user input
  string medication_name = 3 [(validate.rules).string.max_len = 255];

  // medication source, user input
  optional string medication_note = 4 [(validate.rules).string.max_len = 10240];

  // medication time, medication time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
  repeated models.business_customer.v1.BusinessPetScheduleTimeDef medication_times = 6 [(validate.rules).repeated = {
    min_items: 1
    max_items: 99
  }];

  // feeding medication schedule selected date
  optional SelectedDateDef selected_date = 7;

  // selected date
  message SelectedDateDef {
    // feeding medication schedule date type
    // default for PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY
    moego.models.business_customer.v1.FeedingMedicationScheduleDateType date_type = 1 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
    // specific date
    repeated string specific_dates = 2 [(validate.rules).repeated = {
      max_items: 100
      items: {
        string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
      }
    }];
  }
}
