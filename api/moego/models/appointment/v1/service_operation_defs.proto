syntax = "proto3";

package moego.models.appointment.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// the appointment service operation definition, for multi-staff work mode
message ServiceOperationDef {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64.gt = 0];

  // operation name
  string operation_name = 2 [(validate.rules).string = {max_len: 150}];

  // start time, in minutes
  int32 start_time = 3;

  // duration, in minutes
  int32 duration = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // price ratio
  double price_ratio = 5 [(validate.rules).double = {
    gte: 0
    lte: 1
  }];

  // exact price
  double price = 6 [(validate.rules).double = {gte: 0}];
}

// The appointment service operation definition, for multi-staff work mode
message ServiceOperationCalendarDef {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64.gt = 0];

  // duration, in minutes
  int32 duration = 2 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// The appointment service operation calendar schedule definition, for multi-staff work mode
message ServiceOperationCalendarScheduleDef {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64.gt = 0];

  // duration, in minutes
  int32 duration = 2 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // service start date, in yyyy-MM-dd format
  string start_date = 11 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // service end date, in yyyy-MM-dd format
  string end_date = 12 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // start time, in minutes
  int32 start_time = 13 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // end time, in minutes
  int32 end_time = 14 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}
