// @since 2024-06-06 00:35:06
// <AUTHOR> <z<PERSON><PERSON>@moego.pet>

syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "moego/models/appointment/v1/appointment_enums.proto";
import "moego/models/appointment/v1/appointment_models.proto";
import "moego/models/appointment/v1/pet_detail_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// The Event model
message EventModel {
  // appointment id
  int64 appointment_id = 1;
  // event detail
  oneof event_detail {
    // event for creating
    EventForCreating event_for_creating = 2;
    // event for updating
    EventForUpdating event_for_updating = 3;
  }
}

// create event
message EventForCreating {
  // event detail
  AppointmentModel appointment = 1;
}

// appointment update event, will be published when appointment is updated.
// Including three types of events: reschedule, update status, update pet details
// If an operation contains multiple types of events, will publish multiple events
message EventForUpdating {
  // event detail for reschedule
  message RescheduleDetail {
    // schedule setting
    message ScheduleSetting {
      // start date
      google.type.Date start_date = 1;
      // start time
      google.protobuf.Timestamp start_time = 2;
      // end date, optional, if not set, will use the start date
      google.type.Date end_date = 3;
      // end time, optional, if not set, will use the start time
      google.protobuf.Timestamp end_time = 4;
    }

    // service operation schedule setting
    message ServiceOperationScheduleSetting {
      // service operation id
      int64 service_operation_id = 1;
      // schedule setting before
      ScheduleSetting schedule_setting_before = 2;
      // schedule setting after
      ScheduleSetting schedule_setting_after = 3;
      // staff before
      int64 staff_id_before = 4;
      // staff after
      int64 staff_id_after = 5;
    }

    // pet detail schedule setting
    message PetDetailScheduleSetting {
      // pet detail id
      int64 pet_detail_id = 1;
      // pet id
      int64 pet_id = 2;
      // service id
      int64 service_id = 3;
      // schedule setting before
      ScheduleSetting schedule_setting_before = 4;
      // schedule setting after
      ScheduleSetting schedule_setting_after = 5;
      // staff before
      int64 staff_id_before = 6;
      // staff after
      int64 staff_id_after = 7;
      // service operation schedule settings
      repeated ServiceOperationScheduleSetting service_operation_schedule_settings = 8;
    }

    // appointment schedule setting before
    ScheduleSetting appointment_before = 1;
    // appointment schedule setting after
    ScheduleSetting appointment_after = 2;

    // pet detail schedule settings
    repeated PetDetailScheduleSetting pet_detail_schedule_settings = 3;
  }

  // event detail for update status
  message UpdateStatusDetail {
    // status before
    AppointmentStatus status_before = 1;
    // status after
    AppointmentStatus status_after = 2;
  }

  // event detail for update pet details
  message UpdatePetDetailsDetail {
    // pet detail list before
    repeated PetDetailModel pet_details_before = 1;
    // pet detail list after
    repeated PetDetailModel pet_details_after = 2;
  }

  // update detail
  oneof update_detail {
    // reschedule detail
    RescheduleDetail reschedule_detail = 1;
    // update status detail
    UpdateStatusDetail update_status_detail = 2;
    // update pet details detail
    UpdatePetDetailsDetail update_pet_details_detail = 3;
  }
}
