syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// CheckInOutAlertSettings
message CheckInOutAlertSettings {
  // company id
  int64 company_id = 1;
  // check in alert settings
  CheckInAlertSettings check_in_settings = 2;
  // check out alert settings
  CheckOutAlertSettings check_out_settings = 3;
  // the create time
  google.protobuf.Timestamp created_at = 7;
  // the update time
  google.protobuf.Timestamp updated_at = 8;
}

// CheckInAlertSettings
message CheckInAlertSettings {
  // enabled
  bool enabled = 1;
  // also trigger alert for daycare quick check in
  bool trigger_for_quick = 2;
  // alert settings
  optional AlertSettings settings = 3;
}

// CheckOutAlertSettings
message CheckOutAlertSettings {
  // enabled
  bool enabled = 1;
  // alert settings
  optional AlertSettings settings = 2;
}

// AlertSettings
message AlertSettings {
  // client alert settings
  optional ClientAlertSettings client_alert = 1;
  // pet alert settings
  optional PetAlertSettings pet_alert = 2;
}

// ClientAlertSettings
message ClientAlertSettings {
  // list of client icons
  repeated ClientAlertIcons client_icons = 1;
  // list of client tags
  repeated int64 client_tags = 2;
}

// PetAlertSettings
message PetAlertSettings {
  // list of pet icons
  repeated PetAlertIcons pet_icons = 1;
  // list of pet codes
  repeated int64 pet_codes = 2;
}

// client icons for alert
enum ClientAlertIcons {
  // unspecified value
  CLIENT_ALERT_ICONS_UNSPECIFIED = 0;
  // membership
  ICON_MEMBERSHIP = 1;
  // card on file
  ICON_CARD_ON_FILE = 2;
  // package
  ICON_PACKAGE = 3;
  // alert note
  ICON_ALERT_NOTE = 4;
  // unsigned agreement
  ICON_UNSIGNED_AGREEMENT = 5;
  // unpaid balance
  ICON_UNPAID_BALANCE = 6;
  // pickup person
  ICON_PICKUP_PERSON = 7;
}

// pet icons for alert
enum PetAlertIcons {
  // unspecified value
  PET_ALERT_ICONS_UNSPECIFIED = 0;
  // vaccine
  ICON_VACCINE = 1;
  // incident
  ICON_INCIDENT = 2;
}
