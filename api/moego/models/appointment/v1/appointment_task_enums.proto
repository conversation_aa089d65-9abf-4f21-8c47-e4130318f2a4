syntax = "proto3";

package moego.models.appointment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// The status of appointment task
enum AppointmentTaskStatus {
  // unspecified
  APPOINTMENT_TASK_STATUS_UNSPECIFIED = 0;
  // Incomplete
  INCOMPLETE = 1;
  // Completed
  COMPLETED = 2;
}

// The category of appointment task
enum AppointmentTaskCategory {
  // unspecified
  APPOINTMENT_TASK_CATEGORY_UNSPECIFIED = 0;
  // Feeding
  FEEDING = 1;
  // Medication
  MEDICATION = 2;
  // Add-ons
  ADD_ONS = 3;
}

// The schedule of appointment task
enum AppointmentTaskSchedule {
  // unspecified
  APPOINTMENT_TASK_SCHEDULE_UNSPECIFIED = 0;
  // AM
  AM = 1;
  // PM
  PM = 2;
  // Unassigned
  UNASSIGNED = 3;
}

// The note status of feeding task
enum FeedingTaskNoteStatus {
  // unspecified
  FEEDING_TASK_NOTE_STATUS_UNSPECIFIED = 0;
  // Had food
  HAD_FOOD = 1;
  // Missed food
  MISSED_FOOD = 2;
}

// The note status of medication task
enum MedicationTaskNoteStatus {
  // unspecified
  MEDICATION_TASK_NOTE_STATUS_UNSPECIFIED = 0;
  // Took medication
  TOOK_MEDICATION = 1;
  // Missed medication
  MISSED_MEDICATION = 2;
}
