syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/appointment/v1/appointment_enums.proto";
import "moego/models/appointment/v1/wait_list_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// the appointment model
message AppointmentModel {
  // appointment id
  int64 id = 1;
  // order id
  string order_id = 2;
  // business id
  int64 business_id = 3;
  // customer id
  int64 customer_id = 4;
  // appointment date, in yyyy-MM-dd format
  string appointment_date = 5;
  // appointment start time, the number of minutes of the day
  int32 appointment_start_time = 6;
  // appointment end time, the number of minutes of the day
  int32 appointment_end_time = 7;
  // is waiting list
  bool is_waiting_list = 8;
  // move waiting list staff id
  int64 move_waiting_by = 9;
  // confirmed time
  google.protobuf.Timestamp confirmed_time = 10;
  // check in time
  google.protobuf.Timestamp check_in_time = 11;
  // check out time
  google.protobuf.Timestamp check_out_time = 12;
  // canceled time
  google.protobuf.Timestamp canceled_time = 13;
  // appointment status
  AppointmentStatus status = 14;
  // is block
  bool is_block = 15;
  //  book online status
  int32 book_online_status = 16;
  // customer address id
  int64 customer_address_id = 17;
  // repeat id
  int64 repeat_id = 18;
  // is paid
  AppointmentPaymentStatus is_paid = 19;
  // color code
  string color_code = 20;
  // no show
  bool no_show = 21;
  // no show fee
  double no_show_fee = 22;
  // is push notification
  bool push_notification = 23;
  // cancel by type
  int32 cancel_by_type = 24;
  // cancel by
  int64 cancel_by = 25;
  // confirm by type
  int32 confirm_by_type = 26;
  // confirm by
  int64 confirm_by = 27;
  // create by id
  int64 created_by_id = 28;
  // out of area
  bool out_of_area = 29;
  // is deprecate
  bool is_deprecate = 30;
  // create time
  google.protobuf.Timestamp create_time = 31;
  // update time
  google.protobuf.Timestamp update_time = 32;
  // source
  AppointmentSource source = 33;
  // old appointment date
  string old_appointment_date = 34;
  // old appointment start time
  int32 old_appointment_start_time = 35;
  // old appointment end time
  int32 old_appointment_end_time = 36;
  // old appointment id
  int64 old_appt_id = 37;
  // schedule type
  int32 schedule_type = 38;
  // source platform
  string source_platform = 39;
  // ready time, deprecated, use ready_timestamp instead
  int64 ready_time = 40 [deprecated = true];
  // pickup notification send status
  int32 pickup_notification_send_status = 41;
  // pickup notification failed reason
  string pickup_notification_failed_reason = 42;
  // status before checkin
  AppointmentStatus status_before_checkin = 43;
  // status before ready
  AppointmentStatus status_before_ready = 44;
  // status before finish
  AppointmentStatus status_before_finish = 45;
  // no start time
  bool no_start_time = 46;
  // updated by id
  int64 updated_by_id = 47;
  // company id
  int64 company_id = 48;
  // is auto accept
  bool is_auto_accept = 49;
  // wait list status
  WaitListStatus wait_list_status = 50;
  // end date
  string appointment_end_date = 51;
  //  service type include
  int32 service_type_include = 52;
  // ready timestamp
  google.protobuf.Timestamp ready_timestamp = 53;
  // Is new order
  bool is_new_order = 54;
}

// the appointment calendar view
message AppointmentCalendarView {
  // appointment id
  int64 id = 1;
  // business id
  int64 business_id = 3;
  // customer id
  int64 customer_id = 4;
  // appointment date, in yyyy-MM-dd format
  string appointment_date = 5;
  // appointment start time, the number of minutes of the day
  int32 appointment_start_time = 6;
  // appointment end time, the number of minutes of the day
  int32 appointment_end_time = 7;
  // is waiting list
  bool is_waiting_list = 8;
  // move waiting list staff id
  int64 move_waiting_by = 9;
  // confirmed time
  google.protobuf.Timestamp confirmed_time = 10;
  // check in time
  google.protobuf.Timestamp check_in_time = 11;
  // check out time
  google.protobuf.Timestamp check_out_time = 12;
  // canceled time
  google.protobuf.Timestamp canceled_time = 13;
  // appointment status
  AppointmentStatus status = 14;
  // is block
  bool is_block = 15;
  //  book online status
  int32 book_online_status = 16;
  // customer address id
  int64 customer_address_id = 17;
  // repeat id
  int64 repeat_id = 18;
  // is paid
  AppointmentPaymentStatus is_paid = 19;
  // color code
  string color_code = 20;
  // no show
  bool no_show = 21;
  // no show fee
  double no_show_fee = 22;
  // is push notification
  bool push_notification = 23;
  // cancel by type
  int32 cancel_by_type = 24;
  // cancel by
  int64 cancel_by = 25;
  // confirm by type
  int32 confirm_by_type = 26;
  // confirm by
  int64 confirm_by = 27;
  // create by id
  int64 created_by_id = 28;
  // out of area
  bool out_of_area = 29;
  // is deprecate
  bool is_deprecate = 30;
  // create time
  google.protobuf.Timestamp create_time = 31;
  // update time
  google.protobuf.Timestamp update_time = 32;
  // source
  AppointmentSource source = 33;
  // old appointment date
  string old_appointment_date = 34;
  // old appointment start time
  int32 old_appointment_start_time = 35;
  // old appointment end time
  int32 old_appointment_end_time = 36;
  // old appointment id
  int64 old_appt_id = 37;
  // schedule type
  int32 schedule_type = 38;
  // source platform
  string source_platform = 39;
  // ready time, deprecated, use ready_timestamp instead
  int64 ready_time = 40 [deprecated = true];
  // pickup notification send status
  int32 pickup_notification_send_status = 41;
  // pickup notification failed reason
  string pickup_notification_failed_reason = 42;
  // status before checkin
  AppointmentStatus status_before_checkin = 43;
  // status before ready
  AppointmentStatus status_before_ready = 44;
  // status before finish
  AppointmentStatus status_before_finish = 45;
  // no start time
  bool no_start_time = 46;
  // updated by id
  int64 updated_by_id = 47;
  // company id
  int64 company_id = 48;
  // is auto accept
  bool is_auto_accept = 49;
  // wait list status
  WaitListStatus wait_list_status = 50;
  // end date
  string appointment_end_date = 51;
  //  service type include
  int32 service_type_include = 52;
  // start at same time
  bool start_at_same_time = 53;
  // ready timestamp
  google.protobuf.Timestamp ready_timestamp = 54;
  // Is new order flow
  bool is_new_order = 60;
  // Invoice ID, Unique big invoice that identifies the appointment
  string invoice_id = 61;
}

// the appointment overview
message AppointmentOverview {
  // appointment id
  int64 id = 1;
  // customer id
  int64 customer_id = 4;
  // appointment date, in yyyy-MM-dd format
  string appointment_date = 5;
  // appointment start time, the number of minutes of the day
  int32 appointment_start_time = 6;
  // appointment end time, the number of minutes of the day
  int32 appointment_end_time = 7;
  // is waiting list
  bool is_waiting_list = 8;
  // check in time
  google.protobuf.Timestamp check_in_time = 11;
  // appointment status
  AppointmentStatus status = 14;
  // is paid
  AppointmentPaymentStatus is_paid = 19;
  // color code
  string color_code = 20;
  // no show
  bool no_show = 21;
  // source
  AppointmentSource source = 33;
  // wait list status
  WaitListStatus wait_list_status = 50;
  // end date
  string appointment_end_date = 51;
  //  service type include
  int32 service_type_include = 52;
}
