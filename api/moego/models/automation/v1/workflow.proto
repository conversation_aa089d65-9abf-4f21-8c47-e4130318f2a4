syntax = "proto3";

package moego.models.automation.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/automation/v1/common.proto";
import "moego/models/automation/v1/step.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/reporting/v2/filter_model.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/automation/v1;automationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.automation.v1";

// Workflow
message Workflow {
  // Status
  enum Status {
    // STATUS_UNSPECIFIED
    STATUS_UNSPECIFIED = 0;
    // DRAFT
    DRAFT = 1;
    // ACTIVE
    ACTIVE = 2;
    // INACTIVE
    INACTIVE = 3;
    // DELETED
    DELETED = 4;
  }
  // Type
  enum Type {
    // TYPE_UNSPECIFIED
    TYPE_UNSPECIFIED = 0;
    // WORKFLOW
    WORKFLOW = 1;
    // TEMPLATE
    TEMPLATE = 2;
  }
  // RecommendType
  enum RecommendType {
    // recommend type unspecified
    RECOMMEND_TYPE_UNSPECIFIED = 0;
    // HOME_PAGE
    HOME_PAGE = 1;
  }
  // PhotoGallery
  message PhotoGallery {
    // image
    string image = 1;
    // recommend image
    string recommend_image = 2;
  }
  // ConsumerData
  message ConsumerData {
    // effect_client_num
    int32 effect_client_num = 1;
    // cost_sms_token_num
    int32 cost_sms_token_num = 2;
  }

  // meta data
  // Workflow ID
  int64 id = 1;
  // Company ID
  int64 company_id = 2;
  // Workflow name
  string name = 3;
  // Workflow description
  string description = 4;
  // Workflow status
  Status status = 5;
  // Workflow type
  Type type = 6;
  // Created at timestamp
  google.protobuf.Timestamp created_at = 7;
  // Updated at timestamp
  google.protobuf.Timestamp updated_at = 8;
  // Created by user ID
  int64 created_by = 9;
  // Updated by user ID
  int64 updated_by = 10;
  // Workflow image URL
  PhotoGallery photo_gallery = 11;
  // Enterprise ID
  int64 enterprise_id = 12;
  // Tenants IDs
  repeated int64 tenants_ids = 13;
  // Version
  int64 version = 14;

  // content data
  // Steps
  repeated Step steps = 20;
  // Trigger step ID
  string trigger_step_id = 21;
  // Trigger type
  Trigger.Type trigger_type = 22;
  // Trigger schedule
  Scheduled trigger_schedule = 23;
  // Consumer data
  ConsumerData consumer_data = 24;

  // record
  // Last trigger time
  google.protobuf.Timestamp last_trigger_time = 30;

  // category
  // Categories
  repeated WorkflowCategory category = 40;
  // Recommend type
  RecommendType recommend_type = 41;

  // counter
  // Number of reaches
  int32 reach_num = 50;
  // Number of new bookings
  int32 new_book_num = 51;
  // Number of apply franchisees
  int32 apply_tenants_num = 52;

  // enterprise apply to
  WorkflowEnterpriseApply workflow_enterprise_apply = 60;
}

// WorkflowCategory
message WorkflowCategory {
  // Category ID
  int64 id = 1;
  // Category name
  string name = 2;
  // Category description
  string desc = 3;
  // Category icon URL
  string icon = 4;
}

// WorkflowSetting
message WorkflowSetting {
  // Type
  enum Type {
    // TYPE_UNSPECIFIED
    TYPE_UNSPECIFIED = 0;
    // NOT_LIMIT
    NOT_LIMIT = 1;
    // TIME_FREQUENCY
    TIME_FREQUENCY = 2;
  }
  // TimeFrequency
  message TimeFrequency {
    // Time duration
    TimeDuration time_duration = 2;
    // Frequency limit
    int64 limit = 3;
  }
  // Setting type
  Type type = 1;
  // Time frequency settings
  TimeFrequency time_frequency = 2;
  // Company ID
  int64 company_id = 3;
}

// WorkflowOverview
message WorkflowOverview {
  // Total outreach clients
  int32 total_outreach_clients = 1;
  // Response clients
  int32 response_clients = 2;
  // Booked clients
  int32 book_clients = 3;
  // Total sales grooming booked
  int32 total_sales_grooming_booked = 4;
}

// WorkflowRecord
message WorkflowRecord {
  // Status
  enum Status {
    // STATUS_UNSPECIFIED
    STATUS_UNSPECIFIED = 0;
    // READY
    READY = 1;
    // RUNNING
    RUNNING = 2;
    // PENDING
    PENDING = 3;
    // SUCCESS
    SUCCESS = 4;
    // FAIL
    FAIL = 5;
  }

  // Record ID
  int64 id = 1;
  // Workflow ID
  int64 workflow_id = 2;
  // Record status
  Status status = 3;
  // Customer ID
  int64 customer_id = 4;

  // pack data
  // Workflow data
  Workflow workflow = 10;
  // Step records
  repeated StepRecord step_records = 11;
  // Customer data
  moego.models.business_customer.v1.BusinessCustomerInfoModel customer = 12;
}

// StepRecord
message StepRecord {
  // Status
  enum Status {
    // STATUS_UNSPECIFIED
    STATUS_UNSPECIFIED = 0;
    // PENDING
    PENDING = 1;
    // SUCCESS
    SUCCESS = 2;
    // FAIL
    FAIL = 3;
  }

  // Step record ID
  int64 id = 1;
  // Step ID
  string step_id = 2;
  // Record status
  Status status = 3;
  // Workflow record ID
  int64 workflow_record_id = 4;

  // pack data
  // Step data
  Step step = 10;
}

// WorkflowConfig
message WorkflowConfig {
  // Trigger
  Trigger trigger = 1;
  // Placeholders
  repeated Field place_holders = 2;
  // Filter groups
  repeated models.reporting.v2.FilterGroup filter_groups = 3;
  // Event filter groups
  repeated models.reporting.v2.FilterGroup event_filter_groups = 4;
}

// WorkflowEnterpriseApply
message WorkflowEnterpriseApply {
  // Type
  enum Type {
    // TYPE_UNSPECIFIED
    TYPE_UNSPECIFIED = 0;
    // ALL_FRANCHISEES
    ALL_FRANCHISEES = 1;
    // FRANCHISEES_GROUPS, fill tenants_group_ids && tenants_ids
    FRANCHISEES_GROUPS = 2;
    // FRANCHISEES, fill tenants_ids
    FRANCHISEES = 3;
  }

  // Apply Type
  Type type = 1;
  // Tenants Group IDs
  repeated int64 tenants_group_ids = 2;
  // Tenants IDs
  repeated int64 tenants_ids = 3;
}

// Goal
message Goal {}
