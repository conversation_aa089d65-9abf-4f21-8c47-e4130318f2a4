syntax = "proto3";

package moego.models.automation.v1;

import "google/protobuf/duration.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/automation/v1;automationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.automation.v1";

// Field
message Field {
  // key
  string key = 1;
  // name
  string name = 2;
  // place_holder_name
  string place_holder_name = 3;
  // description
  string description = 4;
}

// TimeDuration x Hour/Day/Week/Month
message TimeDuration {
  // unit type for value
  enum Unit {
    // unspecified
    UNIT_UNSPECIFIED = 0;
    // day Unit
    DAY = 1;
    // week Unit
    WEEK = 2;
    // month Unit
    MONTH = 3;
    // hour Unit
    HOUR = 4;
  }
  // unit
  Unit unit = 1;
  // value
  int64 value = 2;
  // duration
  google.protobuf.Duration duration = 3;
}

// MessageStatus
enum MessageStatus {
  // unspecified
  MESSAGE_STATUS_UNSPECIFIED = 0;
  // send success
  SENDING = 1;
  // twilio call back success
  SUCCESS = 2;
  // twilio call back fail
  FAIL = 3;
}

// UserActionType
enum UserActionType {
  // unspecified
  USER_ACTION_TYPE_UNSPECIFIED = 0;
  // create appointment action
  CREATE_APPT = 10;
}
