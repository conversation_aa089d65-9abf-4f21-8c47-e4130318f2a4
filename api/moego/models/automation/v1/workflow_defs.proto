syntax = "proto3";

package moego.models.automation.v1;

import "moego/models/automation/v1/step.proto";
import "moego/models/automation/v1/workflow.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/automation/v1;automationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.automation.v1";

// CreateWorkflowDefs
message CreateWorkflowDef {
  // meta data
  // Workflow name
  string name = 1;
  // Workflow description
  string description = 2;
  // Workflow image
  string image = 3;
  // Workflow recommend_image
  string recommend_image = 4;
  // Workflow Source Template ID
  int64 workflow_template_id = 5;

  // content data
  // Steps definitions
  repeated CreateStepDef steps = 20;
  // Workflow Consumer Data
  models.automation.v1.Workflow.ConsumerData consumer_data = 21;

  // category
  // Workflow categories
  repeated WorkflowCategory category = 40;
  // Recommend type
  moego.models.automation.v1.Workflow.RecommendType recommend_type = 41;

  // enterprise apply to
  WorkflowEnterpriseApply workflow_enterprise_apply = 50;
}

// CreateStepDefs
message CreateStepDef {
  // meta data
  // Step ID
  string id = 1;
  // Step name
  string name = 2;
  // Step description
  string description = 3;

  // adjacency data
  // Parent step ID
  string parent_id = 5;

  // content data
  // Step type
  moego.models.automation.v1.Step.Type type = 8;
  // Step data
  moego.models.automation.v1.Step.Data data = 9;
  // Step data preview
  optional moego.models.automation.v1.Step.PreviewData preview_data = 10;
}
