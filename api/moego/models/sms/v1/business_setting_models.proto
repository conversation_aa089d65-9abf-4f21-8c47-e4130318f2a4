// @since 2023-06-20 17:13:10
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.sms.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/sms/v1;smspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.sms.v1";

// The company atp status model
message BusinessPhoneNumberConfigModel {
  // query business id
  int64 business_id = 1;
  // business phone number
  string phone_number = 2;
}

//business pn setting model
message BusinessSmsSettingModel {
  //company id
  int64 company_id = 1;
  //business id
  int64 business_id = 2;
  //twilio account sid
  string twilio_sid = 3;
  //twilio account token
  string twilio_token = 4;
  //twilio accuont messsging service sid
  string twilio_ms_sid = 5;
  //twilio phone number
  string twilio_number = 6;

  //business call handle type
  int32 call_handle_type = 7;
  //business call phone number
  string call_phone_number = 8;
  //business call reply type
  int32 call_reply_type = 9;
  //business reply message body
  string reply_message = 10;
}

//address param
message BusinessNumberAddressModel {
  // address 1
  string address1 = 1;
  // address 2
  string address2 = 2;
  // country
  string country = 3;
  // city
  string city = 4;
  // state
  string state = 5;
  // zipcode
  string zipcode = 6;
  // lat
  string lat = 7;
  // lng
  string lng = 8;
}
