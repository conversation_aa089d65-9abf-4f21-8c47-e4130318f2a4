// @since 2025-04-07 15:32:56
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.fulfillment.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1;fulfillmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.fulfillment.v1";

// The GroupClass create definition
message GroupClassCreateDef {
  // The group class detail
  GroupClassDetailCreateDef group_class_detail = 1;
}

// The GroupClassDetail create definition
message GroupClassDetailCreateDef {
  // The pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];

  // The group class instance id
  int64 group_class_instance_id = 2 [(validate.rules).int64.gt = 0];
}
