// @since 2025-03-31 15:46:22
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.fulfillment.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1;fulfillmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.fulfillment.v1";

// The Staff time slot model
message StaffTimeSlotModel {
  // The unique identifier of the staff time slot
  int64 id = 1;

  // The company id
  int64 company_id = 2;

  // The company business id
  int64 business_id = 3;

  // Fulfillment id
  int64 fulfillment_id = 4;

  // Care type
  moego.models.offering.v1.ServiceItemType care_type = 5;

  // The care type service detail ID
  int64 detail_id = 6;

  // The order line item ID
  int64 order_line_item_id = 7;

  // The staff ID
  int64 staff_id = 8;

  // The pet ID
  int64 pet_id = 9;

  // The customer ID
  int64 customer_id = 10;

  // The start datetime of the staff time slot
  google.protobuf.Timestamp start_datetime = 11;

  // The end datetime of the staff time slot
  int64 end_datetime = 12;

  // the create time
  google.protobuf.Timestamp created_at = 20;

  // the update time
  google.protobuf.Timestamp updated_at = 21;

  // the delete time, non-null means is deleted
  optional google.protobuf.Timestamp deleted_at = 22;
}
