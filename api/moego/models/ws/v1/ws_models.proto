// @since 2024-07-07 15:01:03
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.ws.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/ws/v1;wspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.ws.v1";

// the connection group type
enum GroupType {
  // unspecified
  GROUP_TYPE_UNSPECIFIED = 0;
  // to connection, only one connection for a connection id
  CONNECTION = 1;
  // to account
  ACCOUNT = 2;
  // to staff
  STAFF = 3;
  // to business
  BUSINESS = 4;
  // to company
  COMPANY = 5;
  // to enterprise
  ENTERPRISE = 6;
}

// the ws client model
message ConnectionModel {
  // the id
  int64 id = 1;
  // account id
  optional int64 account_id = 2;
  // staff id
  optional int64 staff_id = 3;
  // business id
  optional int64 business_id = 4;
  // company id
  optional int64 company_id = 5;
  // enterprise id
  optional int64 enterprise_id = 6;
  // user agent
  string user_agent = 7;
  // ip
  string ip = 8;
  // push count
  int64 push_count = 9;
  // create time
  google.protobuf.Timestamp created_at = 10;
  // update time
  google.protobuf.Timestamp updated_at = 11;
}
