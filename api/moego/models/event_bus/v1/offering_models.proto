syntax = "proto3";

package moego.models.event_bus.v1;

import "google/protobuf/duration.proto";
import "google/type/interval.proto";
import "moego/models/offering/v1/group_class_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.event_bus.v1";

// GroupClassSession
message GroupClassSessionEvent {
  // session
  models.offering.v1.GroupClassSession session = 1;
  // before interval
  google.type.Interval before_interval = 2;
  // before duration
  google.protobuf.Duration before_duration = 3;
}
