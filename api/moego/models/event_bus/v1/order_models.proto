syntax = "proto3";

package moego.models.event_bus.v1;

import "moego/models/order/v1/order_enums.proto";
import "moego/models/order/v1/order_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.event_bus.v1";

// OrderEvent
message OrderEvent {
  // order id
  int64 id = 1;
  // guid
  string guid = 2;
  // order type
  order.v1.OrderModel.OrderType order_type = 3;
  // order status
  order.v1.OrderStatus order_status = 4;
  // order source type
  order.v1.OrderSourceType source_type = 5;
  // payment status
  order.v1.OrderModel.PaymentStatus payment_status = 6;
  // fulfillment status
  order.v1.OrderModel.FulfillmentStatus fulfillment_status = 7;
  // complete time
  int64 complete_time = 8;
  // source id
  int64 source_id = 9;
  // company id
  int64 company_id = 10;
  // business id
  int64 business_id = 11;
  // customer id
  int64 customer_id = 12;
  // order version
  int32 order_version = 13;
}

// refund order event
message RefundOrderEvent {
  // refund order id
  int64 refund_order_id = 1;
  // order id
  int64 order_id = 2;
  // company id
  int64 company_id = 3;
  // business id
  int64 business_id = 4;
}
