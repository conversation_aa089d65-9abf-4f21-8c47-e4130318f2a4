syntax = "proto3";

package moego.models.event_bus.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/engagement/v1/calling_log_models.proto";
import "moego/models/engagement/v1/voice_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.event_bus.v1";

// model for call update status
message CallUpdatedStatusEvent {
  // call log id
  int64 call_log_id = 1;
  // customer id
  int64 customer_id = 2;
  // staff id
  int64 staff_id = 3;
  // twilio call sid
  string twilio_call_sid = 4;
  // twilio conference sid
  string twilio_conference_sid = 5;
  // phone number
  string phone_number = 6;

  // direction
  models.engagement.v1.CallingDirection direction = 50;
  // status
  models.engagement.v1.Status status = 51;
  // start time
  google.protobuf.Timestamp init_time = 52;
}
