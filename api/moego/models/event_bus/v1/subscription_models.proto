syntax = "proto3";
package moego.models.event_bus.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/subscription/v1/subscription_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.event_bus.v1";

// Subscription update
message SubscriptionUpdated {
  // subscription id
  int64 id = 1;
  // plan product id
  int64 plan_product_id = 2;
  // status
  models.subscription.v1.Subscription.Status subscription_status = 3;
  // buyer type
  optional models.subscription.v1.User.Type buyer_type = 4;
  // buyer id
  optional int64 buyer_id = 5;
  // validity start time
  optional google.protobuf.Timestamp validity_start_time = 6;
  // validity end time
  optional google.protobuf.Timestamp validity_end_time = 7;
}

// Subscription payment
message SubscriptionPaymentEvent {
  // subscription id
  int64 subscription_id = 1;
  // failure_message
  string failure_message = 2;
}
