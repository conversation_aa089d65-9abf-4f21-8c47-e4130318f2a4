syntax = "proto3";

package moego.models.event_bus.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/capital/v1/loan_enums.proto";
import "moego/models/capital/v1/loan_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.event_bus.v1";

// Event of a loan offer payout.
message LoanOfferPayoutEvent {
  // The offer that payouts.
  moego.models.capital.v1.LoanOfferModel offer = 1;
  // Time of this payout transaction happened.
  google.protobuf.Timestamp transaction_settled_time = 2;
}

// Event of a loan transaction update.
message LoanTransactionUpdatedEvent {
  // The transaction.
  moego.models.capital.v1.LoanOfferRepaymentTransactionModel transaction = 1;
  // The type of the loan offer of this transaction.
  moego.models.capital.v1.LoanOfferType offer_type = 2;
}
