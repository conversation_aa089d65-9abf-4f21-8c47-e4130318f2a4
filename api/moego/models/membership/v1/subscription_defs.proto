// @since 2024-06-12 14:10:17
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.membership.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/billing/v1/subscription_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1;membershippb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.membership.v1";

// The Subscription Full Definition
message SubscriptionCreateDef {
  // customer id
  int64 customer_id = 1 [(validate.rules).int64 = {gt: 0}];
  // membership id
  int64 membership_id = 2 [(validate.rules).int64 = {gt: 0}];
  // membership revision, if set, will check
  // the membership revision is it or not.
  optional int32 membership_revision = 3 [(validate.rules).int32 = {gt: 0}];
  // external cof id, should go with internal cof id
  // frontend should add card first and use the cof id then
  // FIXME(Ritchie): please save a cof in our system.
  string external_card_id = 4 [(validate.rules).string = {
    max_len: 100
    min_len: 10
  }];
  // 支持创建未来的预约
  google.protobuf.Timestamp start_at = 5;
  // payment behavior
  optional models.billing.v1.SubscriptionModel.PaymentBehavior payment_behavior = 6;
}

// The Subscription Partial Definition
message SubscriptionUpdateDef {
  // external card id
  optional string external_card_id = 1;
}
