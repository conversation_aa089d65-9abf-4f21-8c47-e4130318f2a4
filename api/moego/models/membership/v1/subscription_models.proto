// @since 2024-06-12 14:10:17
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.membership.v1;

import "google/protobuf/timestamp.proto";
import "google/type/dayofweek.proto";
import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/models/customer/v1/customer_models.proto";
import "moego/models/membership/v1/membership_defs.proto";
import "moego/models/membership/v1/membership_models.proto";
import "moego/models/organization/v1/staff_models.proto";
import "moego/models/subscription/v1/subscription_models.proto";
import "moego/utils/v1/time_period.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1;membershippb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.membership.v1";

// The Subscription model
message SubscriptionModel {
  // subscription status, the value should same to
  // ref: `moego.models.subscription.v1.Subscription.Status`
  enum Status {
    // status unspecified
    STATUS_UNSPECIFIED = 0;
    // pending: waiting for the first charge result
    PENDING = 2;
    // the subscription is charged and in validity period
    // cancel in validity period will not change the status,
    // just change the `cancel_at_period_end` value to `true`.
    ACTIVE = 3;
    // manually cancelled and out of validity period
    CANCELLED = 4;
    // paused
    PAUSED = 5;
  }
  // 自动恢复订阅设置，仅对暂停状态的订阅有效
  message AutoResumeSetting {
    // 配置
    oneof setting {
      // 暂停产品周期的整数倍
      int64 num_of_billing_cycle = 1;
      // 暂停到指定日期
      google.protobuf.Timestamp date = 2;
    }
  }
  // the unique id
  int64 id = 1;
  // internal subscription id
  int64 internal_subscription_id = 2;
  // company id
  int64 company_id = 3;
  // customer id
  int64 customer_id = 4;
  // membership id
  int64 membership_id = 5;
  // the price snapshot
  double price = 6;

  // info from internal subscription
  // billing cycle
  MembershipModel.BillingCycle billing_cycle = 10 [deprecated = true];
  // create business id
  int64 business_id = 11;
  // billing cycle
  moego.utils.v1.TimePeriod billing_cycle_period = 12;
  // the create time
  google.protobuf.Timestamp created_at = 13;
  // the update time
  google.protobuf.Timestamp updated_at = 14;
  // the delete time, non-null means is deleted
  optional google.protobuf.Timestamp deleted_at = 15;
  // 最新的 card on file ID，即当前订阅的扣款卡
  string latest_card_on_file_id = 16;
  // validity period
  google.type.Interval validity_period = 17;
  // next billing date
  google.protobuf.Timestamp next_billing_date = 18;
  // expire date
  google.protobuf.Timestamp expires_at = 19;
  // cancelled but in active status
  bool cancel_at_period_end = 20;
  // sell link id
  optional int64 sell_link_id = 21;
  // membership revision
  int32 membership_revision = 22;
  // status
  // use sub status instead
  Status status = 23 [deprecated = true];
  // latest order id
  int64 latest_order_id = 25;
  // 暂停时间
  google.protobuf.Timestamp paused_at = 26;
  // 自动恢复时间
  google.protobuf.Timestamp auto_resume_at = 27;
  // 冗余暂停时的配置
  AutoResumeSetting auto_resume_setting = 28;
  // subscription model 的status
  models.subscription.v1.Subscription.Status sub_status = 29;
  // 取消原因，只有处于未激活状态的订阅才有效
  string cancel_reason = 30;
}

// membership subscription composite
message MembershipSubscriptionModel {
  // membership (latest)
  MembershipModel membership = 1;
  // subscription (latest)
  SubscriptionModel subscription = 2;
  // membership discount benefit
  optional moego.models.membership.v1.MembershipDiscountBenefitsDef membership_discount_benefits = 3;
  // user perk detail
  repeated PerkDetail perk_detail = 4;
}

// the attachable model for expose customer membership icon
message MembershipSubscriptionListModel {
  // subscriptions with membership
  repeated MembershipSubscriptionModel membership_subscriptions = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// The Subscription model
message SubscriptionModelPublicView {
  // the unique id
  int64 id = 1;
  // company id
  int64 company_id = 3;
  // customer id
  int64 customer_id = 4;
  // membership id
  int64 membership_id = 5;
  // info from internal subscription
  // billing cycle
  MembershipModel.BillingCycle billing_cycle = 10 [deprecated = true];
  // create business id
  int64 business_id = 11;
  // billing cycle
  moego.utils.v1.TimePeriod billing_cycle_period = 12;
  // the create time
  google.protobuf.Timestamp created_at = 13;
  // the update time
  google.protobuf.Timestamp updated_at = 14;
  // the delete time, non-null means is deleted
  optional google.protobuf.Timestamp deleted_at = 15;
  // pricing
  double price = 16;
  // validity period
  google.type.Interval validity_period = 17;
  // next billing date
  google.protobuf.Timestamp next_billing_date = 18;
  // expire date
  google.protobuf.Timestamp expires_at = 19;
  // cancelled but in active status
  bool cancel_at_period_end = 20;
  // sell link id
  optional int64 sell_link_id = 21;
  // membership revision
  int32 membership_revision = 22;
  // status
  SubscriptionModel.Status status = 23;
  // paused at
  google.protobuf.Timestamp paused_at = 25;
  // auto resume at
  google.protobuf.Timestamp auto_resume_at = 26;
  // internal subscription id
  int64 internal_subscription_id = 27;
}

// Buyer status
enum SubscriptionBuyerStatus {
  // default
  STATUS_UNSPECIFIED = 0;
  // in subscription
  IN_SUBSCRIPTION = 1;
  // cancelled
  CANCELLED = 2;
  // expired
  EXPIRED = 3;
  // pending
  PENDING = 4;
  // paused
  PAUSED = 5;
}

// the membership buyer model
message SubscriptionBuyerView {
  // the subscription id
  int64 subscription_id = 1;
  // buyer (customer) info
  moego.models.customer.v1.CustomerModelNameView buyer = 2;
  // seller (staff) info
  optional models.organization.v1.StaffModel seller = 3;
  // start date
  google.protobuf.Timestamp start_date = 4;
  // end date (also Next billing date)
  google.protobuf.Timestamp end_date = 5;
  // last redeem date
  google.protobuf.Timestamp last_redeem_date = 6;
  // total sales
  google.type.Money total_sales = 7;
  // status
  SubscriptionBuyerStatus status = 8 [deprecated = true];
  // invoice id
  int64 invoice_id = 9;
  // paused at
  google.protobuf.Timestamp paused_at = 10;
  // auto_resume_at
  google.protobuf.Timestamp auto_resume_at = 11;
  // purchase history total price
  google.type.Money purchase_history_total_price = 12;
  // internal subscription id
  int64 internal_subscription_id = 13;
  // internal subscription status
  subscription.v1.Subscription.Status internal_subscription_status = 14;
}

// the subscription buyer list filter
message SubscriptionBuyerListFilter {
  // status
  optional SubscriptionBuyerStatus status = 1;
  // buyer name keyword
  optional string membership_name_keyword = 2;
  // status list
  repeated SubscriptionBuyerStatus statuses = 3;
}

// the payment history item filter
message PaymentHistoryItemFilter {
  // the subscription id, internal subscription id
  int64 subscription_id = 1;
  // Keys for sort
  enum Key {
    // default
    SORT_BY_UNSPECIFIED = 0;
    // payment time
    SORT_BY_PAYMENT_TIME = 1;
  }
  // sort by
  optional Key sort_by = 2;
  // sort order
  optional bool is_desc = 3;
  // company id 兼容 c app 接口无法通过gauth获取company id
  optional int64 company_id = 4;
  // business id
  optional int64 business_id = 5;
}

// payment history item
message PaymentHistoryItemView {
  // the invoice id
  int64 invoice_id = 1;
  // the payment time
  google.protobuf.Timestamp payment_time = 2;
  // the payment amount
  google.type.Money payment_amount = 3;
  // payment method
  string payment_method = 4;
  // status_enum
  enum Status {
    // default
    PAYMENT_STATUS_UNSPECIFIED = 0;
    // SUCCESS
    PAYMENT_SUCCESS = 1;
    // FAILED
    PAYMENT_FAILED = 2;
    // PENDING
    PAYMENT_PROCESSING = 3;
  }
  // status
  Status status = 5;
}

// ob request setting model
message OBRequestSettingModel {
  // subscription id
  int64 subscription_id = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
  // day of week
  repeated google.type.DayOfWeek days_of_week = 4;
  // pet id, 旧数据为0, 表示所有 pet
  int64 pet_id = 5;
}
