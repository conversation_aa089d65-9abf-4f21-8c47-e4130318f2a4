// @since 2024-06-12 11:04:31
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.membership.v1;

import "google/protobuf/timestamp.proto";
import "google/type/dayofweek.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/membership/v1/membership_models.proto";
import "moego/models/membership/v1/redeem_models.proto";
import "moego/models/offering/v1/service_models.proto";
import "moego/utils/v1/time_period.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1;membershippb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.membership.v1";

// The Membership Full Definition
message MembershipCreateDef {
  // name, will check unique ignore case
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // the description
  string description = 2 [(validate.rules).string = {max_len: 1500}];
  // the status
  MembershipModel.Status status = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // the price
  double price = 6 [(validate.rules).double = {
    gt: 0
    lt: 100000
  }];
  // the tax id
  int64 tax_id = 7 [(validate.rules).int64 = {gt: 0}];
  // billing cycle
  optional MembershipModel.BillingCycle billing_cycle = 8 [deprecated = true];
  // policy
  string policy = 9 [(validate.rules).string = {max_len: 10000}];
  // enable purchase for online booking
  bool enable_online_booking = 10;
  // billing cycle
  moego.utils.v1.TimePeriod billing_cycle_period = 11;
  // enable discount benefits
  bool enable_discount_benefits = 12;
  // enable quantity benefits
  bool enable_quantity_benefits = 13;
  // billing cycyle day of week
  optional google.type.DayOfWeek billing_cycle_day_of_week = 14;

  // breed filter
  bool breed_filter = 15;
  // customized breed
  repeated offering.v1.CustomizedBreed customized_breed = 16;
  // available for all pet size
  bool pet_size_filter = 17;
  // available pet size (only if is_available_for_all_pet_size is false)
  repeated int64 customized_pet_sizes = 18;
  // available for all pet coat type
  bool coat_filter = 19;
  // available pet coat type (only if is_available_for_all_pet_coat_type is false)
  repeated int64 customized_coat = 20;
}

// The Membership Partial Definition
message MembershipUpdateDef {
  // name, will check unique ignore case
  optional string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // the description
  optional string description = 4 [(validate.rules).string = {max_len: 1500}];
  // the status
  optional MembershipModel.Status status = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // the price
  optional double price = 6 [(validate.rules).double = {
    gt: 0
    lt: 100000
  }];
  // the tax id
  optional int64 tax_id = 7 [(validate.rules).int64 = {gt: 0}];
  // billing cycle
  optional MembershipModel.BillingCycle billing_cycle = 8 [deprecated = true];
  // policy
  optional string policy = 9 [(validate.rules).string = {max_len: 10000}];
  // enable purchase for online booking
  optional bool enable_online_booking = 10;
  // billing cycle
  moego.utils.v1.TimePeriod billing_cycle_period = 11;
  // enable discount benefits
  optional bool enable_discount_benefits = 12;
  // enable quantity benefits
  optional bool enable_quantity_benefits = 13;
  // billing cycyle day of week
  optional google.type.DayOfWeek billing_cycle_day_of_week = 14;
  // breed filter
  bool breed_filter = 15;
  // customized breed
  repeated offering.v1.CustomizedBreed customized_breed = 16;
  // available for all pet size
  bool pet_size_filter = 17;
  // available pet size (only if is_available_for_all_pet_size is false)
  repeated int64 customized_pet_sizes = 18;
  // available for all pet coat type
  bool coat_filter = 19;
  // available pet coat type (only if is_available_for_all_pet_coat_type is false)
  repeated int64 customized_coat = 20;
}

// message for membership benefits
message MembershipQuantityBenefitsDef {
  // membership id
  int64 membership_id = 1;
  //  quality, a typo, should be quantity
  repeated models.membership.v1.QualityBenefitModel quantity_defs = 2;
  // 周期类型
  // period type
  PeriodType period_type = 3;
  // specified period
  moego.utils.v1.TimePeriod specified_period = 4;
}

// membership discount benefit
message MembershipDiscountBenefitsDef {
  // membership id
  int64 membership_id = 1;
  // discounts
  repeated models.membership.v1.DiscountBenefitModel discounts = 2;
}

// membership discount benefit
message CreateMembershipDiscountBenefitsDef {
  // discounts
  repeated DiscountDef discounts = 1;
}

// CreateMembershipQuantityBenefitsDef
message CreateMembershipQuantityBenefitsDef {
  // quantity
  repeated QuantityDef quantities = 1;
  // period type
  PeriodType period_type = 2;
  // specified period
  moego.utils.v1.TimePeriod specified_period = 3;
}

// membership discount benefit
message UpdateMembershipDiscountBenefitsDef {
  // discounts
  repeated DiscountDef discounts = 1;
}

// UpdateMembershipQuantityBenefitsDef
message UpdateMembershipQuantityBenefitsDef {
  // quantity
  repeated QuantityDef quantities = 1;
  // period type
  PeriodType period_type = 2;
  // specified period
  moego.utils.v1.TimePeriod specified_period = 3;
}

// discount def
message DiscountDef {
  // is all
  bool is_all = 1;
  // ids
  repeated int64 target_ids = 2;
  // value
  double value = 3;
  // unit
  DiscountUnit unit = 4 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // target type
  TargetType target_type = 5 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
}

// quantity
message QuantityDef {
  // is limit
  bool is_limited = 1;
  // count
  int64 limited_value = 2;
  // id
  int64 target_id = 3;
  // target type
  TargetType target_type = 4 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // target type
  TargetSubType target_sub_type = 5 [(validate.rules).enum = {defined_only: true}];
}

// 周期类型
enum PeriodType {
  // 未指定
  PERIOD_UNSPECIFIED = 0;
  // 跟随周期
  FOLLOW_MEMBERSHIP = 1;
  // 指定周期范围
  SPECIFIED = 2;
}

// Redeem Source Type
enum SourceType {
  // unspecified
  SOURCE_TYPE_UNSPECIFIED = 0;
  // appointment
  APPOINTMENT = 1;
  // Booking Request
  BOOKING_REQUEST = 2;
}

//perk cycle item
message PerkCycleItemDef {
  // validity start time
  google.protobuf.Timestamp validity_start_time = 1;
  // total remain perk amount
  int64 total_remain_perk_amount = 2;
  // perks
  repeated IncludeBenefitView perks = 3;
}

// pet filter
message PetFilter {
  // pet type
  models.customer.v1.PetType pet_type = 1;
  // pet breed
  optional string pet_breed = 2;
  // pet size
  optional int64 pet_size_id = 3;
  //pet code
  optional int64 pet_coat_id = 4;
}

// filter
message Filter {
  // pet filter
  repeated PetFilter pet_filters = 1;
}

// perk detail
message PerkDetail {
  // target type
  TargetType target_type = 1;
  // target sub type
  TargetSubType target_sub_type = 2;
  // remain amount
  int64 remain_perk_amount = 3;
  // is limited
  bool is_limited = 4;
  // validity end time
  google.protobuf.Timestamp validity_end_time = 5;
}
