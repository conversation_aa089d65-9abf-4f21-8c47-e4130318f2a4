syntax = "proto3";

package moego.models.reporting.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v1;reportingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.reporting.v1";

// campaign report
message CampaignReportModel {
  // campaign id
  int64 campaign_id = 1;
  // starter
  string starter = 2;
  // started at
  google.protobuf.Timestamp started_at = 4;
  // notification id
  int64 notification_id = 5;
  // notification title
  string notification_title = 6;
  // notification body
  string notification_body = 7;
  // notification record id
  int64 notification_record_id = 8;
  // receiver account id
  int64 receiver_account_id = 9;
  // receiver account id
  int64 receiver_business_id = 10;
  // receiver account id
  int64 receiver_company_id = 11;
  // receiver account id
  int64 receiver_staff_id = 12;
  // receiver name
  string receiver_name = 13;
  // is notification read
  bool is_read = 14;
  // app clicked at
  google.protobuf.Timestamp read_at = 15;
  // is notification clicked on app
  bool is_app_clicked = 16;
  // notification clicked on app at
  google.protobuf.Timestamp app_clicked_at = 17;
}

// payment report
message PaymentReport {
  // buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
  // message topic
  enum Topic {
    // pulsar namespace/topic: reporting/PAYMENT
    PAYMENT = 0;
  }
  // payment id
  int64 payment_id = 1;
  // rick control rules
  map<string, string> rick_control_rules = 2;
  // order id
  int64 order_id = 3;
  // customer id
  int64 customer_id = 4;
  // business id
  int64 business_id = 5;
  // company id
  int64 company_id = 6;
  // appointment id
  int64 appointment_id = 7;
}

// appointment report
message AppointmentReport {
  // buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
  // message topic
  enum Topic {
    // pulsar namespace/topic: reporting/APPOINTMENT
    APPOINTMENT = 0;
  }
  // operation id
  int64 operation_id = 1;
  // service_id
  int64 service_id = 2;
  // appointment_id
  int64 appointment_id = 3;
  // customer id
  int64 customer_id = 4;
  // pet id
  int64 pet_id = 5;
  // staff id
  int64 staff_id = 6;
  // business id
  int64 business_id = 7;
  // company id
  int64 company_id = 8;
  // van id
  int64 van_id = 9;
}
