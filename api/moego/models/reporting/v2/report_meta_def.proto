syntax = "proto3";

package moego.models.reporting.v2;

import "google/protobuf/timestamp.proto";
import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/diagram_model.proto";
import "moego/models/reporting/v2/field_model.proto";
import "moego/models/reporting/v2/filter_model.proto";
import "moego/models/reporting/v2/report_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2;reportingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.reporting.v2";

// Query page meta request definition
message QueryPageMetaParams {
  // tabs to query
  repeated InsightsTab tabs = 1;
  // reporting scene
  optional moego.models.reporting.v2.ReportingScene scene = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Page meta definition
message PageMetaDef {
  // page title
  string title = 1;
  // tab
  InsightsTab tab = 2;
  // diagrams of the page
  repeated ReportDiagram diagrams = 3;
  // Current page's required permission code
  string permission_code = 4;
}

// Query page meta response definition
message QueryPageMetaResult {
  // pages
  repeated PageMetaDef pages = 1;
  // last synced time
  google.protobuf.Timestamp last_synced_time = 2;
}

// Date time field
message DateTimeField {
  // field key
  string field_key = 1;
  // label of time selector
  string label = 2;
  // description/tooltips of time selector
  optional string description = 3;
}

// Dimension field
message DimensionField {
  // field key
  string field_key = 1;
  // field type
  Field.Type field_type = 2;
  // display label
  string label = 3;
  // description or tooltips
  optional string description = 4;
  // default order by
  bool asc = 5;
  // sub dimensions
  repeated DimensionField sub_dimensions = 6;
}

// Time selector config
message TimeSelector {
  // time selector fields
  repeated DateTimeField fields = 1;
}

// Common diagram meta definitions
message DiagramMeta {
  // uniq diagram id
  string diagram_id = 1;
  // title
  string title = 2;
  // description
  string description = 3;
  // download enable
  bool download_enable = 4;
  // required permission code
  string permission_code = 5;
  // dimension fields
  repeated DimensionField dimensions = 6;
  // metric fields
  repeated Field metrics = 7;
  // filter groups
  repeated FilterGroup filter_groups = 8;
  // time selector
  optional TimeSelector time_selector = 9;
  // customized configs
  optional TableCustomizedConfig customized_config = 10;
  // dynamic column mode, use final dimension to generate columns
  bool dynamic_column_mode = 11;
  // Whether to display location/franchisee selector
  bool show_scope_selector = 12;
  // Whether to display compare period selector
  bool show_compare_period_selector = 13;
  // Max grouping by dimensions count
  int32 max_query_dimensions = 14;
}

// QueryMetasParams
message QueryMetasParams {
  // diagram_ids to query
  repeated string diagram_ids = 1 [(validate.rules).repeated.items.string = {
    min_len: 1
    max_len: 100
  }];
}

// QueryMetasResult
message QueryMetasResult {
  // meta result
  repeated moego.models.reporting.v2.DiagramMeta metas = 1;
}
