syntax = "proto3";

package moego.models.reporting.v2;

import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/field_model.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2;reportingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.reporting.v2";

// Attribute model
message Attribute {
  // Attribute type enum
  enum Type {
    // Unspecified attribute type
    TYPE_UNSPECIFIED = 0;
    // metric
    METRIC = 1;
    // dimension
    DIMENSION = 2;
  }
  // Attribute type
  Type type = 1;
  // label
  string label = 2;
  // description/tooltips
  optional string description = 3;
  // field key
  string field_key = 4;
  // field type
  Field.Type field_type = 5;
  // Whether the field is sortable
  bool sortable = 6;
  // Whether the field is removable
  bool removable = 7;
  // Whether the field can be grouped by
  bool group_by_enable = 8;
  // Whether the field is movable
  bool movable = 9;
  // Field trend type: BENEFIT, HARMFUL, NEUTRAL
  Trend trend = 10;
}
