syntax = "proto3";

package moego.models.reporting.v2;

import "moego/models/reporting/v2/attribute_model.proto";
import "moego/models/reporting/v2/report_meta_def.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2;reportingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.reporting.v2";

// Metrics category definition
message MetricsCategoryDef {
  // category name
  string name = 1;
  // description
  optional string description = 2;
  // metrics
  repeated Attribute metrics = 3;
}

// Get dimensions params
message GetDimensionsParams {
  // diagram id to get dimensions, if not present, return all dimensions
  optional string diagram_id = 1;
}

// Get dimensions result
message GetDimensionsResult {
  // dimensions
  repeated DimensionField dimensions = 1;
}

// Get metrics params
message GetMetricsCategoriesParams {
  // diagram id to get metrics, if not present, return all metrics
  optional string diagram_id = 1;
}

// Get metrics result
message GetMetricsCategoriesResult {
  // metrics categories
  repeated MetricsCategoryDef categories = 1;
}
