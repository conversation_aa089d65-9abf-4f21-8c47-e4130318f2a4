syntax = "proto3";

package moego.models.reporting.v2;

import "google/protobuf/timestamp.proto";
import "google/type/interval.proto";
import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/diagram_model.proto";
import "moego/models/reporting/v2/field_model.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2;reportingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.reporting.v2";

// Scope filter definition: business or tenant
message ScopeFilter {
  // Scope type enumeration
  enum ScopeType {
    // Unspecified scope type
    SCOPE_TYPE_UNSPECIFIED = 0;
    // Business scope
    BUSINESS = 1;
    // Tenant scope
    TENANT = 2;
  }
  // scope ids, could be business_ids or tenant_ids
  repeated uint64 scope_ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
  // if query all scopes
  bool all_scopes = 2;
  // scope type, will set in api layer, currently can ignore in front-end
  ScopeType scope_type = 3;
  // scope parent id: company_id or enterprise_id, will set in api layer, currently can ignore in front-end
  optional uint64 scope_parent_id = 4;
}

// Time filter definition
message TimeFilter {
  // field key
  string field_key = 1;
  // current period
  google.type.Interval current_period = 2;
  // previous period
  optional google.type.Interval previous_period = 3;
}

// Dimension request definition
message DimensionRequest {
  // field key
  string field_key = 1;
  // order by asc or desc
  optional bool asc = 2;
}

// Dimension filter definition
message DimensionFilter {
  // group by fields
  repeated DimensionRequest dimensions = 1;
  // expanded dimension config, from last api call
  optional moego.models.reporting.v2.DimensionConfig dimension_config = 2;
}

// Dimension config
message DimensionConfig {
  // fields of dimension
  repeated DimensionRequest dimensions = 1;
  // if can expand for deeper dimension
  bool is_expandable = 2;
  // filters for current dimension
  repeated FilterRequest filters = 3;
}

// Fetch data response definition
message FetchDataDef {
  // rows of fetch data result
  repeated RowDataDef rows = 1;
  // fields
  repeated Field fields = 2;
  // pagination info
  moego.utils.v2.PaginationResponse pagination = 3;
  // total info for current query
  optional RowDataDef total = 4;
}

// Row data of Fetch data response definition
message RowDataDef {
  // row data
  map<string, NumberData> data = 1;
  // sub dimension data
  optional FetchDataDef sub_data = 2;
  // dimension config of current row
  optional DimensionConfig dimension_config = 3;
  // row unique id
  string row_uuid = 4;
  // drill config of one row
  optional DrillConfig drill_config = 5;
}

// FetchDataParams
message FetchDataParams {
  // diagram id
  string diagram_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // query scope: business id or all businesses
  optional moego.models.reporting.v2.ScopeFilter scope = 2;
  // query time
  optional moego.models.reporting.v2.TimeFilter time_range = 3;
  // pagination params
  optional moego.utils.v2.PaginationRequest pagination = 4;
  // order by params
  repeated moego.utils.v2.OrderBy order_bys = 5;
  // filter params
  repeated moego.models.reporting.v2.FilterRequest filters = 6;
  // dimension filter
  optional moego.models.reporting.v2.DimensionFilter dimension = 7;
  // metrics field keys params
  repeated string metric_keys = 8;
  // dynamic column mode, use final dimension to generate columns
  bool dynamic_column_mode = 9;
}

// FetchDataResult
message FetchDataResult {
  // result
  moego.models.reporting.v2.FetchDataDef data = 1;
  // report data last synced time
  optional google.protobuf.Timestamp last_synced_time = 2;
}

// ExportDataParams
message ExportDataParams {
  // diagram id
  string diagram_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // query scope: business id or all businesses
  optional moego.models.reporting.v2.ScopeFilter scope = 2;
  // query time
  optional moego.models.reporting.v2.TimeFilter time_range = 3;
  // order by params
  repeated moego.utils.v2.OrderBy order_bys = 4;
  // filter params
  repeated moego.models.reporting.v2.FilterRequest filters = 5;
  // dimension filter
  optional moego.models.reporting.v2.DimensionFilter dimension = 6;
}

// ExportDataResult
message ExportDataResult {
  // file id
  int64 file_id = 1;
}
