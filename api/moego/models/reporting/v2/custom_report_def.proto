syntax = "proto3";

package moego.models.reporting.v2;

import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/report_meta_def.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2;reportingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.reporting.v2";

// SaveCustomReportParams
message SaveCustomReportParams {
  // diagram id, exists when update
  optional string diagram_id = 1;
  // name of custom report, should be trimmed
  optional string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
    pattern: "^[^\\s].*[^\\s]$"
  }];
  // description of custom report
  optional string description = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // metric field keys
  repeated string metric_keys = 4 [(validate.rules).repeated = {
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 255
      }
    }
  }];
  // dimension fields
  repeated moego.models.reporting.v2.DimensionField dimensions = 5;
  // saved filters
  repeated moego.models.reporting.v2.FilterRequest saved_filters = 6;
  // dynamic column mode, use final dimension to generate columns
  optional bool dynamic_column_mode = 7;
}

// SaveCustomReportResult
message SaveCustomReportResult {
  // return created or updated diagram_id
  string diagram_id = 1;
}

// ModifyCustomDiagramParams
message ModifyCustomDiagramParams {
  // diagram id, required
  string diagram_id = 1;
  // name of custom report, should be trimmed
  optional string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
    pattern: "^[^\\s].*[^\\s]$"
  }];
  // description of custom report
  optional string description = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // dynamic column mode, use final dimension to generate columns
  optional bool dynamic_column_mode = 4;
}

// ModifyCustomDiagramResult
message ModifyCustomDiagramResult {
  // modify success or not
  bool success = 1;
}

// DuplicateCustomReportParams
message DuplicateCustomReportParams {
  // diagram ids
  repeated string diagram_ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 255
      }
    }
  }];
}

// DuplicateCustomReportResult
message DuplicateCustomReportResult {
  // duplicate success or not
  bool success = 1;
}

// DeleteCustomReportParams
message DeleteCustomReportParams {
  // diagram ids
  repeated string diagram_ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 255
      }
    }
  }];
}

// DeleteCustomReportResult
message DeleteCustomReportResult {
  // delete success or not
  bool success = 1;
}
