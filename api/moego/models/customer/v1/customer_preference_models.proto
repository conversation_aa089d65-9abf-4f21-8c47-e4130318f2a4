syntax = "proto3";

package moego.models.customer.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/organization/v1/company_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.customer.v1";

// customer preference model
message CustomerPreferenceModel {
  // id
  int64 id = 1;
  // account id
  int64 account_id = 2;
  // country code, ISO 3166-1 alpha-2 country code.
  string country_code = 3;
  // currency code, ISO 4217 alphabetic currency code.
  string currency_code = 4;
  // currency symbol
  string currency_symbol = 5;
  // date format
  models.organization.v1.DateFormat date_format = 6;
  // time format
  models.organization.v1.TimeFormat time_format = 7;
  // unit of weight
  models.organization.v1.WeightUnit weight_unit = 8;
  // unit of distance
  models.organization.v1.DistanceUnit distance_unit = 9;
  // created at
  google.protobuf.Timestamp created_at = 20;
  // updated at
  google.protobuf.Timestamp updated_at = 21;
}

// customer preference view
message CustomerPreferenceView {
  // country code, ISO 3166-1 alpha-2 country code.
  string country_code = 3;
  // currency code, ISO 4217 alphabetic currency code.
  string currency_code = 4;
  // currency symbol
  string currency_symbol = 5;
  // date format
  models.organization.v1.DateFormat date_format = 6;
  // time format
  models.organization.v1.TimeFormat time_format = 7;
  // unit of weight
  models.organization.v1.WeightUnit weight_unit = 8;
  // unit of distance
  models.organization.v1.DistanceUnit distance_unit = 9;
}
