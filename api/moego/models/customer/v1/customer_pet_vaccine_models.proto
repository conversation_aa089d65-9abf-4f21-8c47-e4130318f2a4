syntax = "proto3";

package moego.models.customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.customer.v1";

// pet vaccine model
message PetVaccineModel {
  // id
  int64 id = 1;
  // pet id
  int64 pet_id = 2;
  // vaccine metadata id
  int32 vaccine_metadata_id = 3;
  // expiration date
  string expiration_date = 4;
  // document url list
  repeated string document_url_list = 5;
}

// pet vaccine simple view
message PetVaccineSimpleView {
  // vaccine metadata id
  int32 vaccine_metadata_id = 1;
  // expiration date
  string expiration_date = 2;
  // document url list
  repeated string document_url_list = 3;
}

// pet vaccine online booking view
message PetVaccineOnlineBookingView {
  // vaccine binding id
  int32 vaccine_binding_id = 1;
  // vaccine id
  int32 vaccine_id = 2;
  // expiration date
  string expiration_date = 3;
  // document url list
  repeated string document_urls = 4;
}
