syntax = "proto3";

package moego.models.customer.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.customer.v1";

// online booking pet vaccine definition
message VaccineDef {
  // vaccine binding id
  optional int32 vaccine_binding_id = 1 [(validate.rules).int32 = {gt: 0}];
  // vaccine id
  int32 vaccine_id = 2 [(validate.rules).int32 = {gt: 0}];
  // expiration date
  string expiration_date = 3 [(validate.rules).string = {
    max_len: 20
    ignore_empty: true
    pattern: "^(\\d{4}-\\d{2}-\\d{2})?$"
  }];
  // document url
  repeated string document_urls = 4;
}
