syntax = "proto3";

package moego.models.customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.customer.v1";

// customer address model
message CustomerAddressModel {
  // id
  int64 id = 1;
  // address 1
  string address1 = 2;
  // address 2
  string address2 = 3;
  // country
  string country = 4;
  // city
  string city = 5;
  // state
  string state = 6;
  // zipcode
  string zipcode = 7;
  // lat
  string lat = 8;
  // lng
  string lng = 9;
  // is primary
  bool is_primary = 10;
}
