syntax = "proto3";

package moego.models.customer.v1;

import "moego/models/customer/v1/customer_pet_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.customer.v1";

// customer pet model
message CustomerPetModel {
  // id
  int64 id = 1;
  // pet name
  string pet_name = 2;
  // pet type
  PetType pet_type = 3;
  // avatar path
  string avatar_path = 4;
  // breed id
  int32 breed_id = 5;
  // breed mix
  bool breed_mix = 6;
  // birthday
  string birthday = 7;
  // gender
  PetGender gender = 8;
  // hair length metadata id
  int32 hair_length_metadata_id = 9;
  // behavior metadata id
  int32 behavior_metadata_id = 10;
  // weight
  string weight = 11;
  // weight unit metadata id
  int32 weight_unit_metadata_id = 12;
  // fixed metadata id
  int32 fixed_metadata_id = 13;
}

// customer pet model online booking view
message CustomerPetOnlineBookingView {
  // pet id
  int32 pet_id = 1;
  // pet name
  string pet_name = 2;
  // pet type id
  int32 pet_type_id = 3;
  // avatar path
  string avatar_path = 4;
  // breed name, from business settings
  string breed = 5;
  // breed mix, 0: no, 1: yes
  int32 breed_mix = 6;
  // fixed name, from business settings
  string fixed = 7;
  // birthday
  string birthday = 8;
  // gender
  int32 gender = 9;
  // hair length, from business settings
  string hair_length = 10;
  // behavior, from business settings
  string behavior = 11;
  // weight, units from business settings
  string weight = 12;
  // vet address
  string vet_address = 13;
  // vet name
  string vet_name = 14;
  // vet phone number
  string vet_phone = 15;
  // emergency contact name
  string emergency_contact_name = 16;
  // emergency contact phone
  string emergency_contact_phone = 17;
  // health issues
  string health_issues = 18;
  // new pet：没有创建过 appointment 的 pet
  bool is_new_pet = 19;
}
