syntax = "proto3";

package moego.models.customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.customer.v1";

// gender
enum PetGender {
  // unspecified
  PET_GENDER_UNSPECIFIED = 0;
  // male
  PET_GENDER_MALE = 1;
  // female
  PET_GENDER_FEMALE = 2;
  // unknown
  PET_GENDER_UNKNOWN = 3;
}

// pet type
enum PetType {
  // unspecified
  PET_TYPE_UNSPECIFIED = 0;
  // dog
  PET_TYPE_DOG = 1;
  // cat
  PET_TYPE_CAT = 2;
  // bird
  PET_TYPE_BIRD = 3;
  // rabbit
  PET_TYPE_RABBIT = 4;
  // guinea pig
  PET_TYPE_GUINEA_PIG = 5;
  // horse
  PET_TYPE_HORSE = 6;
  // rat
  PET_TYPE_RAT = 7;
  // mouse
  PET_TYPE_MOUSE = 8;
  // hamster
  PET_TYPE_HAMSTER = 9;
  // chinchilla
  PET_TYPE_CHINCHILLA = 10;
  // other
  PET_TYPE_OTHER = 11;
}

// evaluation status
enum EvaluationStatus {
  // unspecified
  EVALUATION_STATUS_UNSPECIFIED = 0;
  // pass
  PASS = 1;
  // fail
  FAIL = 2;
}
