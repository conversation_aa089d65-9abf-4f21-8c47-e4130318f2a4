// @since 2022-07-02 10:22:04
// <AUTHOR> <<EMAIL>>

syntax = "proto3";
package moego.models.customer.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.customer.v1";

// the customer model
// TODO: this model is not filled, we need more detailed information in next steps
message CustomerModel {
  // customer id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // the first name
  string first_name = 3;
  // the last name
  string last_name = 4;
  // customer primary phone number
  string phone_number = 5;
  // customer email
  string email = 6;
  // avatar path
  string avatar_path = 7;
  // referral source id
  int32 referral_source_id = 8;
  // preferred groomer id
  int32 preferred_groomer_id = 9;
  // preferred frequency type
  int32 preferred_frequency_type = 10;
  // preferred frequency day
  int32 preferred_frequency_day = 11;
  // preferred day of week
  repeated int32 preferred_day = 12;
  // preferred time of day
  repeated int32 preferred_time = 13;
  // is block online booking, deprecated
  bool is_block_online_booking = 14 [deprecated = true];
  // is block message
  bool is_block_message = 15;
  // client color
  bool send_auto_email = 16;
  // client color
  bool send_auto_message = 17;
  // client color
  string client_color = 18;
}

// customer name view
// works for somewhere use name only
message CustomerModelNameView {
  // customer id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // the first name
  string first_name = 3;
  // the last name
  string last_name = 4;
}

// customer view for online booking
message CustomerModelOnlineBookingView {
  // customer id
  int32 id = 1;
  // business id
  int32 business_id = 2;
  // the first name
  string first_name = 3;
  // the last name
  string last_name = 4;
  // owner phone number
  string phone_number = 5;
  // email
  string email = 6;
  // avatar path
  string avatar_path = 7;
  // referral source id
  int32 referral_source_id = 8;
  // preferred groomer id
  int32 preferred_groomer_id = 9;
  // preferred frequency type
  int32 preferred_frequency_type = 10;
  // preferred frequency day
  int32 preferred_frequency_day = 11;
  // preferred day of week
  repeated int32 preferred_day = 12;
  // preferred time of day
  repeated int32 preferred_time = 13;
  // is block online booking, deprecated
  int32 is_block_online_booking = 14 [deprecated = true];
  // birthday
  google.protobuf.Timestamp birthday = 15;
  // customer type
  // this field is set in CreateCustomer in monorepo
  // define in moego.backend.app.proto.customer.v1.Customer.Type
  // can view in MoeGolibrary/moego
  string customer_type = 16;
  // customer emergency contact
  message EmergencyContact {
    // customer contact id
    int64 id = 4;
    // first name
    string first_name = 1;
    // last name
    string last_name = 2;
    // phone number
    string phone_number = 3;
  }
  // emergency contact
  EmergencyContact emergency_contact = 17;

  // pickup contact 复用结构，不改了，偷懒，嘻嘻
  EmergencyContact pickup_contact = 18;
}
