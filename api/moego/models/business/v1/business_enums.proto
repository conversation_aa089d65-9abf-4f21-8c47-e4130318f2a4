syntax = "proto3";

package moego.models.business.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business/v1;businesspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business.v1";

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// business app type
enum BusinessAppType {
  // mobile grooming
  BUSINESS_APP_TYPE_MOBILE = 0;
  // grooming salon
  BUSINESS_APP_TYPE_SALON = 1;
  // hybrid
  BUSINESS_APP_TYPE_HYBRID = 2;
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// business pay type
enum BusinessPayType {
  // none
  BUSINESS_PAY_TYPE_NONE = 0;
  // stripe
  BUSINESS_PAY_TYPE_STRIPE = 1;
  // square
  BUSINESS_PAY_TYPE_SQUARE = 2;
}

// BusinessSource
enum BusinessSource {
  // unspecified
  BUSINESS_SOURCE_UNSPECIFIED = 0;
  // app android
  APP_ANDROID = 1;
  // app ios
  APP_IOS = 2;
  // web android
  WEB_ANDROID = 3;
  // web ios
  WEB_IOS = 4;
  // web desktop
  WEB_DESKTOP = 5;
}

// BusinessMoveFrom
enum BusinessMoveFrom {
  // unspecified
  BUSINESS_MOVE_FROM_UNSPECIFIED = 0;
  // just start
  JUST_START = 1;
  // move from paper
  MOVE_FROM_PAPER = 2;
  // from other software
  FROM_OTHER_SOFTWARE = 3;
}
