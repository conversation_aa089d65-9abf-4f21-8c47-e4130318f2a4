syntax = "proto3";

package moego.models.business.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business/v1;businesspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business.v1";

// staff model
// move to organization
message StaffModel {
  option deprecated = true;
  // staff id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // account id
  int64 account_id = 3;
  // avatar path
  string avatar_path = 4;
  // is deleted
  bool is_deleted = 5;
  // is available for book online
  bool is_book_online_available = 6;
  // is show on calendar
  bool is_show_on_calendar = 7;
  reserved 8 to 20;
  // first name
  string first_name = 21;
  // last name
  string last_name = 22;
}

// staff model in c app appt list view
message StaffModelClientView {
  // staff id
  int64 id = 1;
  // avatar path
  string avatar_path = 4;
  // is deleted
  bool is_deleted = 5;
  // is available for book online
  bool is_book_online_available = 6;
  // is show on calendar
  bool is_show_on_calendar = 7;
  // first name
  string first_name = 21;
  // last name
  string last_name = 22;
}
