syntax = "proto3";

package moego.models.capital.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/capital/v1;capitalpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.capital.v1";

// Loan channel
enum LoanChannel {
  // Unspecified
  LOAN_CHANNEL_UNSPECIFIED = 0;
  // Stripe
  STRIPE = 1;
  // Kanmon
  KANMON = 2;
}

// The type of a loan offer
enum LoanOfferType {
  // Unspecified
  LOAN_OFFER_TYPE_UNSPECIFIED = 0;
  // MCA: Merchant Cash Advance
  MCA = 1;
  // Term Loan
  TERM_LOAN = 2;
}

// The product type of a loan
enum LoanProductType {
  // Unspecified
  LOAN_PRODUCT_TYPE_UNSPECIFIED = 0;
  // Standard loan
  STANDARD = 1;
  // Refill loan
  REFILL = 2;
}

// The status of a loan offer
enum LoanOfferStatus {
  // Unspecified
  LOAN_OFFER_STATUS_UNSPECIFIED = 0;
  // Created
  CREATED = 1;
  // Accepted
  ACCEPTED = 2;
  // Paid out
  PAID_OUT = 3;
  // Fully repaid
  FULLY_REPAID = 4;
  // Expired
  EXPIRED = 5;
  // Rejected
  REJECTED = 6;
  // Canceled
  CANCELED = 7;
  // Replaced (currently not enabled for Stripe)
  REPLACED = 8;
  // Prequalified
  PREQUALIFIED = 9;
}

// The campaign type of a offer term
enum LoanCampaignType {
  // Unspecified
  LOAN_CAMPAIGN_TYPE_UNSPECIFIED = 0;
  // To Stripe's "newly_eligible_user"
  NEWLY_ELIGIBLE_USER = 1;
  // To Stripe's "previously_eligible_user"
  PREVIOUSLY_ELIGIBLE_USER = 2;
  // To Stripe's "repeat_user"
  REPEAT_USER = 3;
}

// The type of a loan transaction
enum LoanTransactionType {
  // Unspecified
  LOAN_TRANSACTION_TYPE_UNSPECIFIED = 0;
  // Payout would be the first transaction of a loan
  PAYOUT = 1;
  // Payment would cause a repayment
  PAYMENT = 2;
  // Reversal is a refund of a payment
  REVERSAL = 3;
}

// The fee type of a loan transaction.
enum LoanTransactionFeeType {
  // Unspecified
  LOAN_TRANSACTION_FEE_TYPE_UNSPECIFIED = 0;
  // The original definitions for the fee_amount of a transaction, for a MCA loan.
  MCA_FEE = 1;
  // Kanmon: This happens when a borrower misses a scheduled payment due to late payments.
  LATE_PAYMENT = 2;
  // Kanmon: This happens when a borrower misses a scheduled payment due to No Sufficient Funds.
  INSUFFICIENT_FUNDS = 3;
  // NOTE: Used by MoeGo.
  // Kanmon: This only applies to invoice and purchase order financing products, where the financing fee is not from
  // interest but instead from fee-based pricing.
  TRANSACTION_FEE = 4;
  // NOTE: Used by MoeGo.
  // Kanmon: This only applies to lines of credit, where the line of credit incurs an administrative fee, like an
  // account fee, to keep the line of credit active.
  MAINTENANCE = 5;
}

// The reason of a loan transaction. For now, it basically maps from the Stripe's "reason" field.
enum LoanTransactionReason {
  // Unspecified
  LOAN_TRANSACTION_REASON_UNSPECIFIED = 0;
  // "automatic_withholding"
  AUTOMATIC_WITHHOLDING = 1;
  // "automatic_withholding_refund"
  AUTOMATIC_WITHHOLDING_REFUND = 2;
  // "collection"
  COLLECTION = 3;
  // "collection_failure"
  COLLECTION_FAILURE = 4;
  // "financing_cancellation"
  FINANCING_CANCELLATION = 5;
  // "refill"
  REASON_REFILL = 6;
  // "requested_by_user"
  REQUESTED_BY_USER = 7;
  // "user_initiated"
  USER_INITIATED = 8;
}

// The ineligible reason of applying a loan
enum LoanIneligibleReason {
  // Unspecified
  LOAN_INELIGIBLE_REASON_UNSPECIFIED = 0;
  // No any PSP (Stripe/Square) is settle up.
  NO_PSP = 1;
  // Square is settle up, but MoeGo Pay is not settle up (e.g. no Stripe account is settle up).
  NO_MOEGO_PAY = 2;
  // MoeGo Pay is settle up, but not eligible so no offer is available.
  STRIPE_INELIGIBLE = 3;
}

// The status of a loan onboarding
enum LoanOnboardingStatus {
  // Unspecified
  LOAN_ONBOARDING_STATUS_UNSPECIFIED = 0;
  // Onboarded
  ONBOARDED = 1;
  // Not onboarded
  NOT_ONBOARDED = 2;
  // Need to submit additional information
  NEED_ADDITIONAL_INFORMATION = 3;
}
