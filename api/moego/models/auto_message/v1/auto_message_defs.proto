syntax = "proto3";

package moego.models.auto_message.v1;

import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/auto_message/v1;automessagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.auto_message.v1";

// service type template def
message ServiceTypeTemplateDef {
  // available service types
  repeated models.offering.v1.ServiceItemType service_item_types = 1 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  //email subject
  string email_subject = 2 [(validate.rules).string = {max_len: 256}];
  // email body
  string email_body = 3 [(validate.rules).string = {max_len: 131072}];
  // sms body
  string sms_body = 4 [(validate.rules).string = {max_len: 65536}];
  // app body
  string app_body = 5 [(validate.rules).string = {max_len: 65536}];
}

// service type template def list
message ServiceTypeTemplateDefList {
  // The list of values
  repeated ServiceTypeTemplateDef values = 1 [(validate.rules).repeated = {min_items: 1}];
}

// appointment template for service type config
message ServiceTypeConfigDef {
  // available service types
  repeated models.offering.v1.ServiceItemType service_item_types = 1 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // sms template id
  int64 sms_template_id = 2 [(validate.rules).int64 = {gt: 0}];
  // email template id
  int64 email_template_id = 3 [(validate.rules).int64 = {gt: 0}];
  // app template id
  int64 app_template_id = 4 [(validate.rules).int64 = {gte: 0}];
}

// service type config def list
message ServiceTypeConfigDefList {
  // The list of values
  repeated ServiceTypeConfigDef values = 1 [(validate.rules).repeated = {min_items: 1}];
}
