syntax = "proto3";

package moego.models.auto_message.v1;

import "moego/models/message/v1/message_enums.proto";
import "moego/models/message/v1/message_template_enums.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/auto_message/v1;automessagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.auto_message.v1";

// ob auto message list view
message OBAutoMessageListView {
  // auto message config id
  int64 id = 1;
  // is auto message enabled
  bool is_enabled = 2;
  // auto message use case
  moego.models.message.v1.MessageTemplateUseCase use_case = 3;
  // client receive
  repeated models.message.v1.MessageType client_receive = 4;
  // available service types
  repeated models.offering.v1.ServiceItemType service_item_types = 5;
}

// appointment auto message list view
message AppointmentAutoMessageListView {
  // auto message config id
  int64 id = 1;
  // is auto message enabled
  bool is_enabled = 2;
  // auto message use case
  moego.models.message.v1.MessageTemplateUseCase use_case = 3;
  // client receive
  repeated models.message.v1.MessageType client_receive = 4;
  // available service types
  repeated models.offering.v1.ServiceItemType service_item_types = 5;
}

// appointment auto message detail view
message AppointmentAutoMessageDetailView {
  // auto message config id
  int64 id = 1;
  // is auto message enabled
  bool is_enabled = 2;
  // auto message use case
  moego.models.message.v1.MessageTemplateUseCase use_case = 3;
  // client receive
  repeated models.message.v1.MessageType client_receive = 4;
  // template for service type
  repeated ServiceTypeTemplateView template = 5;
}

// payment auto message list view
message PayAutoMessageListView {
  // auto message config id
  int64 id = 1;
  // is auto message enabled
  bool is_enabled = 2;
  // auto message use case
  moego.models.message.v1.MessageTemplateUseCase use_case = 3;
  // client receive
  repeated models.message.v1.MessageType client_receive = 4;
}

// payment auto message detail view
message PayAutoMessageDetailView {
  // auto message config id
  int64 id = 1;
  // is auto message enabled
  bool is_enabled = 2;
  // auto message use case
  moego.models.message.v1.MessageTemplateUseCase use_case = 3;
  // client receive
  repeated models.message.v1.MessageType client_receive = 4;
  //email subject
  string email_subject = 5;
  // email body
  string email_body = 6;
  // sms body
  string sms_body = 7;
  // app body
  string app_body = 8;
}

// appointment reminder list view
message AppointmentReminderListView {
  // reminder message config id
  int64 id = 1;
  // is auto message enabled
  bool is_enabled = 2;
  // reminder message use case
  moego.models.message.v1.MessageTemplateUseCase use_case = 3;
  // client receive
  repeated models.message.v1.MessageType client_receive = 4;
  // days before
  int32 days_before = 5;
  // minutes at
  int32 minutes_at = 6;
  // available service types
  repeated models.offering.v1.ServiceItemType service_item_types = 7;
}

// appointment reminder detail view
message AppointmentReminderDetailView {
  // template id
  int64 id = 1;
  // is auto message enabled
  bool is_enabled = 2;
  // reminder message use case
  moego.models.message.v1.MessageTemplateUseCase use_case = 3;
  // client receive
  repeated models.message.v1.MessageType client_receive = 4;
  // days before
  int32 days_before = 5;
  // minutes at
  int32 minutes_at = 6;
  // template for service type
  repeated ServiceTypeTemplateView template = 7;
}

// service type template view
message ServiceTypeTemplateView {
  // id
  int64 id = 1;
  // available service types
  repeated models.offering.v1.ServiceItemType service_item_types = 2;
  //email subject
  string email_subject = 3;
  // email body
  string email_body = 4;
  // sms body
  string sms_body = 5;
  // app body
  string app_body = 6;
}

// default reminder list view
message ReminderListView {
  // reminder message config id
  int64 id = 1;
  // is auto message enabled
  bool is_enabled = 2;
  // reminder message use case
  moego.models.message.v1.MessageTemplateUseCase use_case = 3;
  // client receive
  repeated models.message.v1.MessageType client_receive = 4;
  // days before
  int32 days_before = 5;
  // hours after
  int32 hours_after = 6;
  // minutes at
  int32 minutes_at = 7;
}

// default reminder detail view
message ReminderDetailView {
  // template id
  int64 id = 1;
  // is auto message enabled
  bool is_enabled = 2;
  // reminder message use case
  moego.models.message.v1.MessageTemplateUseCase use_case = 3;
  // client receive
  repeated models.message.v1.MessageType client_receive = 4;
  // days before
  int32 days_before = 5;
  // hours after
  int32 hours_after = 6;
  // minutes at
  int32 minutes_at = 7;
  //email subject
  string email_subject = 8;
  // email body
  string email_body = 9;
  // sms body
  string sms_body = 10;
  // app body
  string app_body = 11;
}

// auto message appointment config model
message AppointmentAutoMsgConfigModel {
  // auto message appointment config id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
  // auto message use case
  moego.models.message.v1.MessageTemplateUseCase use_case = 4;
  // is auto message enabled
  bool is_enabled = 5;
  // is sms enabled
  bool is_sms_enabled = 6;
  // is email enabled
  bool is_email_enabled = 7;
  // minutes offset
  int32 minutes_offset = 8;
  // minutes at
  int32 minutes_at = 9;
  // template for service type
  repeated ServiceTypeConfigModel templates = 10;
  // is app enabled
  bool is_app_enabled = 11;
}

// appointment template for service type view
message ServiceTypeConfigModel {
  // id
  int64 id = 1;
  // available service types
  repeated models.offering.v1.ServiceItemType service_item_types = 2;
  // sms template id
  int64 sms_template_id = 3;
  // email template id
  int64 email_template_id = 4;
  // app template id
  int64 app_template_id = 5;
}

// auto message config model
message AutoMessageConfigModel {
  // auto message config id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
  // auto message use case
  moego.models.message.v1.MessageTemplateUseCase use_case = 4;
  // is auto message enabled
  bool is_enabled = 5;
  // minutes offset
  int32 minutes_offset = 6;
  // minutes at
  int32 minutes_at = 7;
  // is sms enabled
  bool is_sms_enabled = 8;
  // is email enabled
  bool is_email_enabled = 9;
  // sms template id
  int64 sms_template_id = 10;
  // email template id
  int64 email_template_id = 11;
  // is app enabled
  bool is_app_enabled = 12;
  // app template id
  int64 app_template_id = 13;
}
