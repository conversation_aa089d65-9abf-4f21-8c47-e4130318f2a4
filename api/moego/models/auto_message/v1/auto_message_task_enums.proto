// @since 2024-06-05 11:24:51
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.auto_message.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/auto_message/v1;automessagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.auto_message.v1";

// message task object type
enum AutoMessageTaskObjectType {
  // unspecified value
  AUTO_MESSAGE_TASK_OBJECT_TYPE_UNSPECIFIED = 0;
  // message object type: appointment
  AUTO_MESSAGE_TASK_OBJECT_TYPE_APPOINTMENT = 1;
  // message object type: pet
  AUTO_MESSAGE_TASK_OBJECT_TYPE_PET = 2;
  // message object type: customer
  AUTO_MESSAGE_TASK_OBJECT_TYPE_CUSTOMER = 3;
}

// message task receiver type
enum AutoMessageTaskReceiverType {
  // unspecified value
  AUTO_MESSAGE_TASK_RECEIVER_TYPE_UNSPECIFIED = 0;
  // message task receiver type: customer
  AUTO_MESSAGE_TASK_RECEIVER_TYPE_CUSTOMER = 1;
}

// message task status
enum AutoMessageTaskStatus {
  // unspecified value
  AUTO_MESSAGE_TASK_STATUS_UNSPECIFIED = 0;
  // init
  AUTO_MESSAGE_TASK_STATUS_INIT = 1;
  // sending
  AUTO_MESSAGE_TASK_STATUS_SENDING = 2;
  // succeed
  AUTO_MESSAGE_TASK_STATUS_SUCCEED = 3;
  // failed
  AUTO_MESSAGE_TASK_STATUS_FAILED = 4;
  // cancelled by staff
  AUTO_MESSAGE_TASK_STATUS_CANCELLED_BY_STAFF = 5;
  // cancelled by system
  AUTO_MESSAGE_TASK_STATUS_CANCELLED_BY_SYSTEM = 6;
}
