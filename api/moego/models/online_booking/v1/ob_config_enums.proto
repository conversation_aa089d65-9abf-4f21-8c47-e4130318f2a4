syntax = "proto3";

package moego.models.online_booking.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// ob access client type
enum AcceptClientType {
  // unspecified
  ACCEPT_CLIENT_TYPE_UNSPECIFIED = 0;
  // only accept new client
  ACCEPT_CLIENT_TYPE_NEW = 1;
  // only accept existing client
  ACCEPT_CLIENT_TYPE_EXISTING = 2;
  // accept both new and existing client
  ACCEPT_CLIENT_TYPE_BOTH = 3;
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// ob availability type
enum AvailabilityType {
  // by working hours
  AVAILABILITY_TYPE_BY_WORKING_HOURS = 0;
  // by slots
  AVAILABILITY_TYPE_BY_SLOTS = 1;
  // disable select time
  AVAILABILITY_TYPE_DISABLE_SELECT_TIME = 2;
}

// time slot format
enum TimeSlotFormat {
  // unspecified
  TIME_SLOT_FORMAT_UNSPECIFIED = 0;
  // exact times
  TIME_SLOT_FORMAT_EXACT_TIMES = 1;
  // arrival windows
  TIME_SLOT_FORMAT_ARRIVAL_WINDOWS = 2;
  // date only
  TIME_SLOT_FORMAT_DATE_ONLY = 3;
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// ob prepay type
enum PrepayType {
  // full amount
  PREPAY_TYPE_FULL_AMOUNT = 0;
  // deposit
  PREPAY_TYPE_DEPOSIT = 1;
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// ob prepay deposit type
enum PrepayDepositType {
  // fixed amount
  PREPAY_DEPOSIT_TYPE_FIXED_AMOUNT = 0;
  // percentage
  PREPAY_DEPOSIT_TYPE_PERCENTAGE = 1;
}

// ob booking range end date type
enum BookingRangeEndType {
  // unspecified
  BOOKING_RANGE_END_TYPE_UNSPECIFIED = 0;
  // relative date
  BOOKING_RANGE_END_TYPE_RELATIVE = 1;
  // absolute date
  BOOKING_RANGE_END_TYPE_ABSOLUTE = 2;
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// ob payments type
enum PaymentType {
  // disable payments
  PAYMENT_TYPE_DISABLE = 0;
  // card of file
  PAYMENT_TYPE_CARD_ON_FILE = 1;
  // prepayment / deposit
  PAYMENT_TYPE_PREPAY = 2;
  // pre-auth
  PAYMENT_TYPE_PRE_AUTH = 3;
}
