syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// booking care type model
message BookingCareType {
  // the unique id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;

  // booking care type name
  optional string name = 4;
  // description
  optional string description = 5;
  // icon image url
  optional string icon = 6;
  // image url
  optional string image = 7;
  // sort
  optional int32 sort = 8;
  // service type
  models.offering.v1.ServiceType service_type = 9;
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 10;

  // is all service applicable
  bool is_all_service_applicable = 11;
  // selected service ids, only effective when all_service is false
  repeated int64 selected_services = 12;

  // updated staff id
  int64 updated_by = 13;
  // the create time
  google.protobuf.Timestamp created_at = 14;
  // the update time
  google.protobuf.Timestamp updated_at = 15;
  // the delete time, non-null means is deleted
  optional google.protobuf.Timestamp deleted_at = 16;
}
