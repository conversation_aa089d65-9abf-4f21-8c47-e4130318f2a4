syntax = "proto3";

package moego.models.online_booking.v1;

import "google/type/date.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// boarding waitlist
message BoardingWaitlist {
  // start date
  google.type.Date start_date = 1;
  // end date
  google.type.Date end_date = 2;
}
