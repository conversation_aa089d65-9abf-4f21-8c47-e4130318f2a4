syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// The grooming add-on detail
message GroomingAddOnDetailModel {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // The id of service detail
  int64 service_detail_id = 3;
  // The id of pet, associated with the current service
  int64 pet_id = 4;
  // The id of staff, associated with the current service
  int64 staff_id = 5;
  // The id of add-on service, aka. grooming service id
  int64 add_on_id = 6;
  // The time of current service, unit minute
  int32 service_time = 7;
  // The price of current service
  double service_price = 8;
  // The start date of the service, yyyy-MM-dd
  string start_date = 9;
  // The start time of the service, unit minute, 540 means 09:00
  int32 start_time = 10;
  // The end date of the service, yyyy-MM-dd
  string end_date = 11;
  // The end time of the service, unit minute, 540 means 09:00
  int32 end_time = 12;
  // createdAt
  google.protobuf.Timestamp created_at = 13;
  // updatedAt
  google.protobuf.Timestamp updated_at = 14;
}
