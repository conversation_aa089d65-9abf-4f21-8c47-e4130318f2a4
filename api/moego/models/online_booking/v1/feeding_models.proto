syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// Stores information about feedings.
message FeedingModel {
  // The primary key identifier for each feeding.
  int64 id = 1;
  // The booking request identifier.
  int64 booking_request_id = 2;
  // The service detail identifier.
  int64 service_detail_id = 3;
  // service detail type, 2: boarding, 3: daycare
  moego.models.offering.v1.ServiceItemType service_detail_type = 4;
  // Feeding time.
  repeated FeedingSchedule time = 5;
  // Feeding amount, must be greater than 0.
  double amount = 6;
  // Feeding unit.
  string unit = 7;
  // Food type.
  string food_type = 8;
  // Food source.
  string food_source = 9;
  // Feeding instructions.
  string instruction = 10;
  // createdAt
  google.protobuf.Timestamp created_at = 11;
  // updatedAt
  google.protobuf.Timestamp updated_at = 12;
  // Feeding note
  string note = 13;
  // Feeding amount, such as 1.2, 1/2, 1 etc.
  optional string amount_str = 14;

  // Feeding schedule.
  message FeedingSchedule {
    // Label for the schedule.
    string label = 1;
    // Time for the schedule, in minutes.
    int32 time = 2;
  }
}
