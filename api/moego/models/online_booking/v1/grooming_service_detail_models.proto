// @since 2024-03-21 15:56:21
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// The grooming service or add-on detail
message GroomingServiceDetailModel {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // The id of pet, associated with the current service
  int64 pet_id = 3;
  // The id of staff, associated with the current service
  int64 staff_id = 4;
  // The id of current service
  int64 service_id = 5;
  // The time of current service, unit minute
  int32 service_time = 6;
  // The price of current service
  double service_price = 7;
  // 1: this, 2: don't save, 3: this and following, 4: all upcoming
  // deprecated by <PERSON> since 2025/3/10, 数据库里都没有这个字段，不要使用。
  optional models.offering.v1.ServiceScopeType scope_type_price = 8 [deprecated = true];
  // 1: this, 2: don't save, 3: this and following, 4: all upcoming
  // deprecated by Freeman since 2025/3/10, 数据库里都没有这个字段，不要使用。
  optional models.offering.v1.ServiceScopeType scope_type_time = 9 [deprecated = true];
  // The start date of the service, yyyy-MM-dd
  optional string start_date = 10;
  // The start time of the service, unit minute, 540 means 09:00
  optional int32 start_time = 11;
  // The end date of the service, yyyy-MM-dd
  optional string end_date = 12;
  // The end time of the service, unit minute, 540 means 09:00
  optional int32 end_time = 13;
  // createdAt
  google.protobuf.Timestamp created_at = 14;
  // updatedAt
  google.protobuf.Timestamp updated_at = 15;
  // price override type
  moego.models.offering.v1.ServiceOverrideType price_override_type = 16;
  // duration override type
  moego.models.offering.v1.ServiceOverrideType duration_override_type = 17;
  // date type
  optional moego.models.appointment.v1.PetDetailDateType date_type = 18;
}
