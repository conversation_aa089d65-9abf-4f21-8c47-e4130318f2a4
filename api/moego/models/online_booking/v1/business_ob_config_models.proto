// @since 2022-06-30 19:14:21
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.online_booking.v1;

import "moego/models/online_booking/v1/ob_config_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// BusinessOBConfigModel
message BusinessOBConfigModel {
  // business id
  int64 business_id = 1;
  // enable ob
  bool is_enable = 2;
  // ob url name
  string book_online_name = 3;
  // ob version
  int32 use_version = 4;
  // create time
  int64 create_time = 5;
  // update time
  int64 update_time = 6;
  // available time type
  AvailabilityType available_time_type = 7;
  // by slots timeslot format, 1-exact times, 2-arrival window
  TimeSlotFormat by_slot_timeslot_format = 8;
  // by slots timeslot interval mins
  int32 by_slot_timeslot_mins = 9;
  // by working hours timeslot format type, 1-exact times, 2-arrival windows, 3-date only
  TimeSlotFormat timeslot_format = 10;
  // by working hours timeslot interval mins
  int32 timeslot_mins = 11;
  // arrival window before mins
  int32 arrival_window_before_min = 12;
  // arrival window after mins
  int32 arrival_window_after_min = 13;
  // soonest available
  int32 soonest_available = 14;
  // farthest available
  int32 farthest_available = 15;
  // by slot soonest available
  int32 by_slot_soonest_available = 16;
  // by slot farthest available
  int32 by_slot_farthest_available = 17;
  // is need address
  bool is_need_address = 18;
  // accept client type
  AcceptClientType accept_client_type = 19;
  // allowed simplify submit
  bool is_allowed_simplify_submit = 20;
  // payment type
  PaymentType payment_type = 21;
  // required cof policy
  string cof_policy = 22;
  // prepay type
  PrepayType prepay_type = 23;
  // prepay tip enable
  bool is_prepay_tip_enable = 24;
  // prepay deposit type
  PrepayDepositType prepay_deposit_type = 25;
  // deposit amount
  double deposit_amount = 26;
  // deposit percentage
  double deposit_percentage = 27;
  // prepay policy
  string prepay_policy = 28;
  // pre-auth policy
  string pre_auth_policy = 29;
  // pre-auth tip enable
  bool is_pre_auth_tip_enable = 30;
  // display staff selection page
  bool is_display_staff_selection_page = 40;
  // booking range start offset
  int32 booking_range_start_offset = 41;
  // booking range end type, 1-offset, 2-date
  BookingRangeEndType booking_range_end_type = 42;
  // booking range end offset
  int32 booking_range_end_offset = 43;
  // booking range end date
  string booking_range_end_date = 44;
  // is check existing client address
  bool is_check_existing_client = 45;
}

// BusinessOBConfigSimpleView
message BusinessOBConfigSimpleView {
  // business id
  int64 business_id = 1;
  // enable ob
  bool is_enable = 2;
  // ob url name
  string book_online_name = 3;
}

// BusinessOBConfigClientPortalView
message BusinessOBConfigClientPortalView {
  // business id
  int64 business_id = 1;
  // enable ob
  bool is_enable = 2;
  // ob url name
  string book_online_name = 3;
  // ob version
  int32 use_version = 4;
}

// business ob config in c app appt list view
message BusinessOBConfigModelClientListView {
  // business id
  int64 business_id = 1;
  // available time type
  AvailabilityType available_time_type = 2;
  // by working hours timeslot format type
  TimeSlotFormat timeslot_format = 3;
  // by slots timeslot format
  TimeSlotFormat by_slot_timeslot_format = 4;
  // arrival window before mins
  int32 arrival_window_before_min = 5;
  // arrival window after mins
  int32 arrival_window_after_min = 6;
}

// business ob config in c app appt detail view
message BusinessOBConfigModelClientView {
  // business id
  int64 business_id = 1;
  // available time type
  AvailabilityType available_time_type = 2;
  // by working hours timeslot format type
  TimeSlotFormat timeslot_format = 3;
  // by slots timeslot format
  TimeSlotFormat by_slot_timeslot_format = 4;
  // arrival window before mins
  int32 arrival_window_before_min = 5;
  // arrival window after mins
  int32 arrival_window_after_min = 6;
  // enable ob
  bool is_enable = 7;
  // ob url name
  string book_online_name = 8;
  // ob version
  int32 use_version = 9;
  // accept client type
  AcceptClientType accept_client_type = 10;
}

// business ob config in c app booking view
message BusinessOBConfigModelBookingView {
  // business id
  int64 business_id = 1;
  // enable ob
  bool is_enable = 2;
  // ob url name
  string book_online_name = 3;
  // ob version
  int32 use_version = 4;
  // available time type
  AvailabilityType available_time_type = 5;
  // by working hours timeslot format type
  TimeSlotFormat timeslot_format = 6;
  // by slots timeslot format
  TimeSlotFormat by_slot_timeslot_format = 7;
  // arrival window before mins
  int32 arrival_window_before_min = 8;
  // arrival window after mins
  int32 arrival_window_after_min = 9;
  // soonest available
  int32 soonest_available = 10;
  // farthest available
  int32 farthest_available = 11;
  // by slot soonest available
  int32 by_slot_soonest_available = 12;
  // by slot farthest available
  int32 by_slot_farthest_available = 13;
  // display staff selection page
  bool is_display_staff_selection_page = 14;
  // booking range start offset
  int32 booking_range_start_offset = 15;
  // booking range end type, 1-offset, 2-date
  BookingRangeEndType booking_range_end_type = 16;
  // booking range end offset
  int32 booking_range_end_offset = 17;
  // booking range end date
  string booking_range_end_date = 18;
  // payment type
  PaymentType payment_type = 19;
  // required cof policy
  string cof_policy = 20;
  // prepay type
  PrepayType prepay_type = 21;
  // prepay tip enable
  bool is_prepay_tip_enable = 22;
  // prepay deposit type
  PrepayDepositType prepay_deposit_type = 23;
  // deposit amount
  double deposit_amount = 24;
  // deposit percentage
  double deposit_percentage = 25;
  // prepay policy
  string prepay_policy = 26;
  // pre-auth policy
  string pre_auth_policy = 27;
  // pre-auth tip enable
  bool is_pre_auth_tip_enable = 28;
  // allowed simplify submit
  bool is_allowed_simplify_submit = 29;
  // is need address
  bool is_need_address = 30;
  // is check existing client address
  bool is_check_existing_client = 45;
}
