// @since 2024-04-15 18:10:49
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.models.online_booking.v1;

import "moego/models/grooming/v1/service_enums.proto";
import "moego/models/offering/v1/service_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// The OBOffering view
message OBOfferingCategoryView {
  // category id
  int64 category_id = 1;
  // category name
  string name = 2;
  // service list
  repeated OBOfferingServiceView services = 3;
}

// The OBOffering view
message OBOfferingServiceView {
  // service basic info
  models.offering.v1.CustomizedServiceView service_basic_info = 1;
  // show price
  models.grooming.v1.ShowBasePrice show_price = 2;
}
