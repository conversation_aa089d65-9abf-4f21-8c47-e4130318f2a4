syntax = "proto3";

package moego.models.online_booking.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// selected pet's service and add-on request
message SelectedPetServiceDef {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // selected service id list
  int64 service_id = 2 [(validate.rules).int64 = {gt: 0}];
  // selected add-on id list
  repeated int64 add_on_ids = 3 [(validate.rules).repeated = {
    min_items: 0
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}
