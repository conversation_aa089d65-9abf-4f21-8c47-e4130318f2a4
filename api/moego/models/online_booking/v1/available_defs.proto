syntax = "proto3";

package moego.models.online_booking.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// available pet metadata definition
message AvailablePetMetadataDef {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // allowed pet type
  bool is_allowed_pet_type = 2;
  // allowed pet weight
  bool is_allowed_weight = 3;
}

// available pet definition
message AvailablePetDef {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff's first available date
  bool is_available = 2;
}

// pet's available service definition
message AvailableServiceDef {
  // pet_id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // applicable service id list
  repeated int64 service_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // applicable add-on id list
  repeated int64 addon_ids = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// pet's available staff definition
message AvailableStaffDef {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64 = {gt: 0}];
  // first name
  string first_name = 2;
  // last name
  string last_name = 3;
  // avatar path
  string avatar_path = 4;
  // staff's available pet list
  repeated AvailablePetDef available_pets = 6 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
  }];
}

// staff first available date definition
message AvailableStaffDateDef {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64 = {gt: 0}];
  // first available date
  string first_available_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
}

// available date definition
message AvailableDateDef {
  // date
  string date = 1 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // is available in the morning
  bool is_am = 2;
  // is available in the afternoon
  bool is_pm = 3;
}

// available time slot definition
message AvailableTimeslotDef {
  // date
  string date = 1 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // is available in the morning
  bool is_am = 2;
  // is available in the afternoon
  bool is_pm = 3;
  // available staff list for the specific day
  repeated AvailableStaffSpecificDayDef staffs = 4 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
  }];
}

// pet's available staff definition
message AvailableStaffSpecificDayDef {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64 = {gt: 0}];
  // first name
  string first_name = 2;
  // last name
  string last_name = 3;
  // staff's AM available time slots
  repeated int32 am_timeslots = 4 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1440
    unique: true
    items: {
      int32: {
        gte: 0
        lte: 1440
      }
    }
  }];
  // staff's PM available time slots
  repeated int32 pm_timeslots = 5 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1440
    unique: true
    items: {
      int32: {
        gte: 0
        lte: 1440
      }
    }
  }];
}

// available working hour range definition
message AvailableWorkingHourRangeDef {
  // date
  string date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // start time
  int32 start_time = 3 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // end time
  int32 end_time = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// staff's available working hour range definition
message AvailableStaffWorkingHourRangeDef {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64 = {gt: 0}];
  // working hour range list
  repeated AvailableWorkingHourRangeDef working_hour_ranges = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
  }];
}
