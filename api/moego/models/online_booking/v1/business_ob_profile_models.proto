// @since 2022-06-30 19:14:21
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.online_booking.v1;

import "moego/models/online_booking/v1/week_time_range_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// BusinessOBProfileModel
message BusinessOBProfileModel {
  // book online profile id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // business name
  int64 business_name = 3;
  // phone number
  string phone_number = 4;
  // website
  string website = 5;
  // address
  string address = 6;
  // business email
  string business_email = 7;
  // avatar path
  string avatar_path = 8;
  // description
  string description = 9;
  // facebook
  string facebook = 10;
  // instagram
  string instagram = 11;
  // google
  string google = 12;
  // yelp
  string yelp = 13;
  // other
  string other = 14;
  // language
  string language = 15;
  // button_color
  string button_color = 16;
  // create time, milliseconds
  int64 create_time = 17;
  // update time, milliseconds
  int64 update_time = 18;
  // working hours, key: Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday
  map<string, moego.models.online_booking.v1.WeekTimeRangeModel> business_working_hours = 19;
}

// BusinessOBClientPortalView
message BusinessOBClientPortalView {
  // business id
  int64 business_id = 2;
  // business name
  string business_name = 3;
  // phone number
  string phone_number = 4;
  // address
  string address = 6;
  // avatar path
  string avatar_path = 8;
}
