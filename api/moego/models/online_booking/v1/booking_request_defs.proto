// @since 2024-03-21 14:16:42
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/struct.proto";
import "google/type/date.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/online_booking/v1/booking_request_enums.proto";
import "moego/models/online_booking/v1/feeding_models.proto";
import "moego/models/online_booking/v1/medication_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// The booking request definition
message BookingRequestDef {
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];

  // customer id
  int64 customer_id = 3 [(validate.rules).int64.gt = 0];

  // appointment id, generated after the booking request is scheduled
  optional int64 appointment_id = 4 [(validate.rules).int64.gt = 0];

  // start date, format: yyyy-mm-dd
  string start_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // start time, the minutes from 00:00
  int32 start_time = 6 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // is paid flag
  bool is_paid = 7;

  // additional note
  optional string additional_note = 8 [(validate.rules).string = {max_len: 2000}];

  // source platform
  BookingRequestSourcePlatform source_platform = 9 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
}

// Pet to lodging def
message PetToLodgingDef {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // lodging unit id
  int64 lodging_unit_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// Pet to staff def
message PetToStaffDef {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service id
  int64 service_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
  // start time
  int32 start_time = 4 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
}

// Pet to service def
message PetToServiceDef {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // old evaluation service id
  optional int64 from_evaluation_service_id = 2 [(validate.rules).int64 = {gt: 0}];
  // new evaluation service id
  optional int64 to_evaluation_service_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// OB request pet params
message Pet {
  // Id, New pet has virtual id, existing pet has id
  optional int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Name
  optional string pet_name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // Avatar path
  optional string avatar_path = 3 [(validate.rules).string = {max_len: 255}];
  // Breed
  optional string breed = 4 [(validate.rules).string = {max_len: 255}];
  // Breed mix
  optional int32 breed_mix = 5 [(validate.rules).int32 = {gte: 0}];
  // Pet type id
  optional int32 pet_type_id = 6 [(validate.rules).int32 = {gt: 0}];
  // Gender
  optional int32 gender = 7 [(validate.rules).int32 = {gte: 0}];
  // Birthday
  optional string birthday = 8 [(validate.rules).string = {
    ignore_empty: true
    pattern: "^\\d{4}-\\d{2}-\\d{2}$"
  }];
  // Weight
  optional string weight = 9 [(validate.rules).string = {max_len: 255}];
  // Fixed
  optional string fixed = 10 [(validate.rules).string = {max_len: 255}];
  // Behavior
  optional string behavior = 11 [(validate.rules).string = {max_len: 255}];
  // Hair length
  optional string hair_length = 12 [(validate.rules).string = {max_len: 255}];
  // Expiry notification
  optional int32 expiry_notification = 13 [(validate.rules).int32 = {gte: 0}];
  // Vet name
  optional string vet_name = 14 [(validate.rules).string = {max_len: 255}];
  // Vet phone
  optional string vet_phone = 15 [(validate.rules).string = {max_len: 255}];
  // Vet address
  optional string vet_address = 16;
  // Emergency contact name
  optional string emergency_contact_name = 17 [(validate.rules).string = {max_len: 255}];
  // Emergency contact phone
  optional string emergency_contact_phone = 18 [(validate.rules).string = {max_len: 255}];
  // Health issues
  optional string health_issues = 19 [(validate.rules).string = {max_len: 2048}];
  // Vaccines
  repeated Vaccine vaccine_list = 20;
  // Pet image
  optional string pet_image = 21 [(validate.rules).string = {max_len: 2048}];
  // Is selected
  //  bool is_selected = 22;
  // Pet question answers
  map<string, google.protobuf.Value> pet_question_answers = 25;

  // OB request vaccine params
  message Vaccine {
    // Vaccine binding id
    optional int64 vaccine_binding_id = 1 [(validate.rules).int64 = {gt: 0}];
    // Vaccine name
    optional int32 type = 2 [(validate.rules).int32 = {gte: 0}];
    // Vaccine id
    optional int32 vaccine_id = 3 [(validate.rules).int32 = {gte: 0}];
    // Vaccine name
    optional string expiration_date = 4 [(validate.rules).string = {
      ignore_empty: true
      pattern: "^\\d{4}-\\d{2}-\\d{2}$"
    }];
    // Vaccine document
    optional string vaccine_document = 5;
    // Document urls
    repeated string document_urls = 6;
  }
}

// Feeding
message Feeding {
  // Feeding time
  repeated moego.models.online_booking.v1.FeedingModel.FeedingSchedule time = 1;
  // Feeding unit
  optional string unit = 3 [(validate.rules).string = {max_len: 255}];
  // Food type
  optional string food_type = 4 [(validate.rules).string = {max_len: 255}];
  // Food source
  optional string food_source = 5 [(validate.rules).string = {max_len: 255}];
  // Feeding instructions
  optional string instruction = 6 [(validate.rules).string = {max_len: 2048}];
  // Feeding note
  optional string note = 7 [(validate.rules).string = {max_len: 255}];
  // Feeding amount, such as 1.2, 1/2, 1 etc.
  optional string amount_str = 8 [(validate.rules).string = {max_len: 255}];
}

// Medication
message Medication {
  // Medication time
  repeated moego.models.online_booking.v1.MedicationModel.MedicationSchedule time = 1;
  // Medication unit
  optional string unit = 3 [(validate.rules).string = {max_len: 255}];
  // Medication name
  optional string medication_name = 4 [(validate.rules).string = {max_len: 255}];
  // Medication notes
  optional string notes = 5 [(validate.rules).string = {max_len: 2048}];
  // Medication amount, such as 1.2, 1/2, 1 etc.
  optional string amount_str = 8 [(validate.rules).string = {max_len: 255}];
}

// Boarding addon
message BoardingAddon {
  // Boarding addon id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // Specific dates
  repeated string dates = 4 [(validate.rules).repeated = {
    items: {
      string: {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}
    }
  }];
  // Quantity per day
  optional int32 quantity_per_day = 8;
  // date type
  optional moego.models.appointment.v1.PetDetailDateType date_type = 9;
  // start date
  optional google.type.Date start_date = 10;
}

// Daycare addon
message DaycareAddon {
  // Daycare addon id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // Is every day
  optional bool is_every_day = 3;
  // Specific dates
  repeated string dates = 4 [(validate.rules).repeated = {
    items: {
      string: {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}
    }
  }];
  // Quantity per day
  optional int32 quantity_per_day = 8;
}

// Daycare addon
message GroomingAddon {
  // grooming addon id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// Grooming service
message GroomingServiceDetail {
  // service id
  int64 service_id = 1 [(validate.rules).int64 = {gt: 0}];
  // start date
  optional string start_date = 3 [(validate.rules).string = {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}];
  // addons
  repeated GroomingAddon addons = 4;
}

// Boarding service
message BoardingServiceDetail {
  // Boarding service id
  int64 service_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Arrival date
  // 当 boarding service detail 加入 waitlist 时，这个参数可以为空
  optional string start_date = 3 [(validate.rules).string = {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}];
  // Arrival time, in minutes since midnight
  int32 arrival_time = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // Pick up date
  // 当 boarding service detail 加入 waitlist 时，这个参数可以为空
  optional string end_date = 5 [(validate.rules).string = {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}];
  // Pick up time, in minutes since midnight
  int32 pickup_time = 6 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // Addons
  repeated BoardingAddon addons = 11;
  // Feedings
  repeated Feeding feedings = 12;
  // Medications
  repeated Medication medications = 13;
}

// Daycare service
message DaycareServiceDetail {
  // Daycare service id
  int64 service_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Dates of the daycare, in the format of "yyyy-MM-dd"
  // 当 daycare service detail 加入 waitlist 时，这个参数可以为空
  repeated string dates = 2 [(validate.rules).repeated = {
    items: {
      string: {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}
    }
  }];
  // Arrival time, in minutes since midnight
  int32 arrival_time = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // Pick up time, in minutes since midnight
  optional int32 pickup_time = 6 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // Addons
  repeated DaycareAddon addons = 12;
  // Feedings
  repeated Feeding feedings = 13;
  // Medications
  repeated Medication medications = 14;
}

// Evaluation service
message EvaluationServiceDetail {
  // Evaluation service id
  int64 service_id = 1 [(validate.rules).int64 = {gt: 0}];
  // The date of the evaluation, in the format of "yyyy-MM-dd"
  string date = 2 [(validate.rules).string = {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}];
  // Minutes since midnight
  int32 time = 3 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// Dog walking service
message DogWalkingServiceDetail {
  // Dog walking service id
  int64 service_id = 1 [(validate.rules).int64 = {gt: 0}];
  // The staff id
  int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
  // The date of the evaluation, in the format of "yyyy-MM-dd"
  string date = 3 [(validate.rules).string = {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}];
  // Minutes since midnight
  int32 time = 5 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// Group class service
message GroupClassServiceDetail {
  // Group class instance id
  int64 group_class_instance_id = 1 [(validate.rules).int64 = {gt: 0}];
  // The trainer id, same to the staff id
  int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
  // The dates of each group session
  repeated string dates = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      string: {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}
    }
  }];
  // The start time of per group class session
  int32 start_time = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // The end time of per group class session
  int32 end_time = 5 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// OB request service
message ServiceDetail {
  // Service type
  oneof service {
    option (validate.required) = true;

    // Grooming service
    GroomingServiceDetail grooming = 1;
    // Boarding service
    BoardingServiceDetail boarding = 2;
    // Daycare service
    DaycareServiceDetail daycare = 3;
    // Evaluation service
    EvaluationServiceDetail evaluation = 4;
    // Dog walking service
    DogWalkingServiceDetail dog_walking = 5;
    // Group class service
    GroupClassServiceDetail group_class = 6;
  }
}

// Pet service details
message PetServiceDetails {
  // Pet id
  Pet pet = 1 [(validate.rules).message = {required: true}];
  // Is new pet
  bool is_new_pet = 2;
  // Service id
  repeated ServiceDetail service_details = 3;
}
