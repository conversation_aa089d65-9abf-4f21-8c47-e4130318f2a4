syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// The evaluation test detail
message EvaluationTestDetailModel {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // The id of pet, associated with the current evaluation test
  int64 pet_id = 3;
  // The id of current evaluation test
  int64 evaluation_id = 4;
  // The price of current evaluation test
  double service_price = 5;
  // The duration of current evaluation test, unit minute
  int32 duration = 6;
  // The start date of the evaluation test, yyyy-MM-dd
  string start_date = 7;
  // The start time of the evaluation test, unit minute, 540 means 09:00
  int32 start_time = 8;
  // The end date of the evaluation test, yyyy-MM-dd
  string end_date = 9;
  // The end time of the evaluation test, unit minute, 540 means 09:00
  int32 end_time = 10;
  // createdAt
  google.protobuf.Timestamp created_at = 11;
  // updatedAt
  google.protobuf.Timestamp updated_at = 12;
  // evaluation 绑定的 service id
  // 0 表示没有绑定 service
  int64 service_id = 13;
}
