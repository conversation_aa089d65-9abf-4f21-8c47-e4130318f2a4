// @since 2024-04-08 15:28:27
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.models.online_booking.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// date type
enum DateLimitType {
  // UNSPECIFIED
  DATE_TYPE_UNSPECIFIED = 0;
  // offset
  DATE_TYPE_OFFSET = 1;
  // specific date
  DATE_TYPE_SPECIFIC = 2;
}

// schedule type
enum ScheduleType {
  // UNSPECIFIED
  SCHEDULE_TYPE_UNSPECIFIED = 0;
  // every week
  EVERY_WEEK = 1;
  // every two weeks
  EVERY_TWO_WEEKS = 2;
  // every three weeks
  EVERY_THREE_WEEKS = 3;
  // every four weeks
  EVERY_FOUR_WEEKS = 4;
}

// time range type
enum TimeRangeType {
  // UNSPECIFIED
  TIME_RANGE_TYPE_UNSPECIFIED = 0;
  // arrival time
  ARRIVAL_TIME = 1;
  // pick up time
  PICK_UP_TIME = 2;
}

// accept customer type
enum AcceptCustomerType {
  // UNSPECIFIED
  ACCEPT_CUSTOMER_TYPE_UNSPECIFIED = 0;
  // only new customer
  NEW_CUSTOMER = 1;
  // only existing customer
  EXISTING_CUSTOMER = 2;
  // both existing and new customer
  BOTH_EXISTING_AND_NEW_CUSTOMER = 3;
}

// accept pet entry type
enum AcceptPetEntryType {
  // UNSPECIFIED
  ACCEPT_PET_ENTRY_TYPE_UNSPECIFIED = 0;
  // only new pet
  NEW = 1;
  // only existing pet
  EXISTING = 2;
  // both existing and new pet
  NEW_AND_EXISTING = 3;
}

// capacity override unit type
enum CapacityOverrideUnitType {
  // unspecified
  CAPACITY_OVERRIDE_UNIT_TYPE_UNSPECIFIED = 0;
  // per pet
  PET = 1;
  // percent
  PERCENT = 2;
}

// availability type
enum TimeAvailabilityType {
  // UNSPECIFIED
  TIME_AVAILABILITY_TYPE_UNSPECIFIED = 0;
  // working hour
  WORKING_HOUR = 1;
  // time slot
  TIME_SLOT = 2;
  // disable option
  DISABLE_OPTION = 3;
}

// NewPetAccessMode
enum NewPetAccessMode {
  // UNSPECIFIED
  NEW_PET_ACCESS_MODE_UNSPECIFIED = 0;
  // enabled
  NEW_PET_ACCESS_MODE_ENABLED = 1;
  // disabled
  NEW_PET_ACCESS_MODE_DISABLED = 2;
}

// ExistingPetAccessMode
enum ExistingPetAccessMode {
  // UNSPECIFIED
  EXISTING_PET_ACCESS_MODE_UNSPECIFIED = 0;
  // view only
  EXISTING_PET_ACCESS_MODE_VIEW = 1;
  // view and edit
  EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT = 2;
  // disabled
  EXISTING_PET_ACCESS_MODE_DISABLED = 3;
}

// NewClientAccessMode
enum NewClientAccessMode {
  // UNSPECIFIED
  NEW_CLIENT_ACCESS_MODE_UNSPECIFIED = 0;
  // enabled
  NEW_CLIENT_ACCESS_MODE_ENABLED = 1;
  // disabled
  NEW_CLIENT_ACCESS_MODE_DISABLED = 2;
}

// ExistingClientAccessMode
enum ExistingClientAccessMode {
  // UNSPECIFIED
  EXISTING_CLIENT_ACCESS_MODE_UNSPECIFIED = 0;
  // view only
  EXISTING_CLIENT_ACCESS_MODE_VIEW = 1;
  // view and edit
  EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT = 2;
  // disabled
  EXISTING_CLIENT_ACCESS_MODE_DISABLED = 3;
}
