// @since 2022-06-30 19:14:21
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.online_booking.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// BusinessOBGalleryModel
message BusinessOBGalleryModel {
  // gallery id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // staff id
  int64 staff_id = 3;
  // image path
  string image_path = 4;
  // sort number
  int64 sort = 5;
  // false / true
  bool is_delete = 6;
  // false / true
  bool is_star = 7;
  // create time, milliseconds
  int64 create_time = 8;
  // update time, milliseconds
  int64 update_time = 9;
}

// BusinessOBGalleryClientPortalView
message BusinessOBGalleryClientPortalView {
  // gallery id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // image path
  string image_path = 4;
  // sort number
  int64 sort = 5;
}
