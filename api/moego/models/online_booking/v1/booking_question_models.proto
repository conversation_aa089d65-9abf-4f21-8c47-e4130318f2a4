syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/online_booking/v1/booking_question_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// model for booking question
message BookingQuestionModel {
  // id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // question name
  string question = 3;
  // placeholder
  string placeholder = 4;
  // show question flag
  bool is_show = 5;
  // required flag
  bool is_required = 6;
  // type
  moego.models.online_booking.v1.BookingQuestionApplyType type = 7;
  // allow delete flag
  bool is_allow_delete = 8;
  // allow change status flag
  bool is_allow_change = 9;
  // allow edit flag
  bool is_allow_edit = 10;
  // sort number
  int64 sort = 11;
  // is deleted flag
  bool is_deleted = 12;
  // question type
  moego.models.online_booking.v1.BookingQuestionType question_type = 13;
  // extra json, for multiple options
  string extra_json = 14;
  // company id
  int64 company_id = 15;
  // question unique key
  string key = 16;
  // createdAt
  google.protobuf.Timestamp created_at = 20;
  // updatedAt
  google.protobuf.Timestamp updated_at = 21;
}
