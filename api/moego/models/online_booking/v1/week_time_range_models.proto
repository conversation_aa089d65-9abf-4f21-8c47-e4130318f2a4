// @since 2022-06-30 19:14:21
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.online_booking.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// WeekTimeRangeModel
message WeekTimeRangeModel {
  // time range, start to end
  repeated TimeRangeModel time_range = 1;
  // false / true
  bool is_selected = 2;
}

// TimeRangeModel
message TimeRangeModel {
  // start time, minutes of the day
  int32 start_time = 1;
  // end time, minutes of the day
  int32 end_time = 2;
}
