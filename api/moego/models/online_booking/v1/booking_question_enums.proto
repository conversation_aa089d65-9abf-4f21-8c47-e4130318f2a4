syntax = "proto3";

package moego.models.online_booking.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// question type for booking question
enum BookingQuestionApplyType {
  // unspecified
  BOOKING_QUESTION_TYPE_UNSPECIFIED = 0;
  // for pets
  FOR_PET = 1;
  // for pet owners
  FOR_PET_OWNER = 2;
}

// question answer for booking question
enum BookingQuestionType {
  // unspecified
  BOOKING_QUESTION_ANSWER_TYPE_UNSPECIFIED = 0;
  // short text
  SHORT_TEXT = 1;
  // long text
  LONG_TEXT = 2;
  // dropdown
  DROPDOWN = 3;
  // radio button
  RADIO = 4;
  // checkbox
  CHECKBOX = 5;
}
