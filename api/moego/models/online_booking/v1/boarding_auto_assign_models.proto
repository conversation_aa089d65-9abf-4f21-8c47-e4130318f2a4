syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// boarding auto assign record
message BoardingAutoAssignModel {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // boardingServiceDetailId
  int64 boarding_service_detail_id = 3;
  // The id of lodging
  int64 lodging_id = 4;
  // createdAt
  google.protobuf.Timestamp created_at = 5;
  // updatedAt
  google.protobuf.Timestamp updated_at = 6;
}
