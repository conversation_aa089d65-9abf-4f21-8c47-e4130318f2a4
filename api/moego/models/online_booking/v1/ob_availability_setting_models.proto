// @since 2024-04-08 15:28:27
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.models.online_booking.v1;

import "google/type/date.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/online_booking/v1/ob_availability_setting_defs.proto";
import "moego/models/online_booking/v1/ob_availability_setting_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// grooming service availability for ob
message GroomingServiceAvailabilityModel {
  // accept customer type
  AcceptCustomerType accept_customer_type = 4;
  // availability type
  TimeAvailabilityType time_availability_type = 5;
  // enable shift sync
  bool enable_shift_sync = 6;
}

// boarding service availability for ob
message BoardingServiceAvailabilityModel {
  // accepted pet type
  repeated moego.models.customer.v1.PetType accepted_pet_types = 1;
  // booking date range
  moego.models.online_booking.v1.DateRangeDef booking_date_range = 2;
  // pick up time range
  ArrivalPickUpTimeDef arrival_pick_up_time_range = 3;
  // accept customer type
  AcceptCustomerType accept_customer_type = 4;
  // lodging availability
  LodgingAvailabilityDef lodging_availability = 5;
}

// daycare service availability for ob
message DaycareServiceAvailabilityModel {
  // accepted pet type
  repeated moego.models.customer.v1.PetType accepted_pet_types = 1;
  // booking date range
  moego.models.online_booking.v1.DateRangeDef booking_date_range = 2;
  // pick up time range
  ArrivalPickUpTimeDef arrival_pick_up_time_range = 3;
  // accept customer type
  AcceptCustomerType accept_customer_type = 4;
  // lodging availability
  LodgingAvailabilityDef lodging_availability = 5;
  // capacity overrides
  repeated CapacityOverrideModel capacity_overrides = 6;
}

// dog walking service availability for ob
message DogWalkingServiceAvailabilityModel {
  // only support dog pet type
  repeated moego.models.customer.v1.PetType accepted_pet_types = 1;
  // booking date range
  moego.models.online_booking.v1.DateRangeDef booking_date_range = 2;
  // accept customer type
  AcceptCustomerType accept_customer_type = 4;
}

// evaluation service availability for ob
message EvaluationServiceAvailabilityModel {
  // booking date range
  moego.models.online_booking.v1.DateRangeDef booking_date_range = 2;
  // pick up time range
  ArrivalPickUpTimeDef arrival_pick_up_time_range = 3;
}

// arrival/pick up time model
message ArrivalPickUpTimeOverrideModel {
  // id
  int64 id = 1;
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 2;
  // arrival time/pick up time
  TimeRangeType type = 3;
  // start date
  google.type.Date start_date = 4;
  // end date
  google.type.Date end_date = 5;
  // is available
  bool is_available = 6;
  // arrival time range
  repeated DayTimeRangeDef day_time_ranges = 7;
  // is_active
  bool is_active = 8;
}

// capacity override model
message CapacityOverrideModel {
  // id
  int64 id = 1;
  // date ranges
  repeated CapacityDateRange date_ranges = 2;
  // capacity
  int32 capacity = 3;
  // unit type
  CapacityOverrideUnitType unit_type = 4;
  // is_active
  bool is_active = 5;

  // capacity date range
  message CapacityDateRange {
    // start date
    google.type.Date start_date = 1;
    // end date
    google.type.Date end_date = 2;
  }
}
