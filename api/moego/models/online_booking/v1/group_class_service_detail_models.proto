syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// The group class service detail
message GroupClassServiceDetailModel {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // The id of pet, associated with the current service
  int64 pet_id = 3;
  // The id of group class instance,
  int64 class_instance_id = 4;
  // The id of staff, associated with the current service
  int64 staff_id = 5;
  // The id of current service
  int64 service_id = 6;
  // The service price
  double service_price = 7;
  // The time of current service, unit minute
  repeated string specific_dates = 8;
  // The start time of the service, unit minute, 540 means 09:00
  int32 start_time = 9;
  // The end time of the service, unit minute, 540 means 09:00
  int32 end_time = 10;
  // Duration of each session in minutes
  int32 duration_per_session = 11;
  // createdAt
  google.protobuf.Timestamp created_at = 20;
  // updatedAt
  google.protobuf.Timestamp updated_at = 21;
}
