syntax = "proto3";

package moego.models.notification.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/notification/v1;notificationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.notification.v1";

// notification action definition
message NotificationActionDef {
  // action information
  oneof action {
    // open appointment detail
    OpenAppointmentDetail open_appointment_detail = 1;
    // go to home page
    GoToHomePage go_to_home_page = 3;
  }
}

// open appointment detail
message OpenAppointmentDetail {
  // appointment id
  int64 appointment_id = 1;
}

// go to home page
message GoToHomePage {}
