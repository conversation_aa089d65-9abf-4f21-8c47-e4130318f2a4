syntax = "proto3";

package moego.models.notification.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/notification/v1/notification_enums.proto";
import "moego/models/notification/v1/notification_extra_defs.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/notification/v1;notificationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.notification.v1";

// the notification model
message NotificationModel {
  // record id
  int64 id = 1;
  // notification source
  NotificationSource source = 2;
  // sender id
  int64 sender_id = 3;
  // receiver id
  int64 receiver_id = 4;
  // title
  string title = 5;
  // content
  string content = 6;
  // notification method
  NotificationMethod method = 7;
  // notification type
  NotificationType type = 8;
  // sent at
  google.protobuf.Timestamp sent_at = 9;
  // read at
  google.protobuf.Timestamp read_at = 10;
  // deleted at
  google.protobuf.Timestamp deleted_at = 11;
  // extra info
  NotificationExtraDef extra = 12;
}

// the notification model client view
message NotificationModelClientView {
  // record id
  int64 id = 1;
  // title
  string title = 5;
  // content
  string content = 6;
  // notification method
  NotificationMethod method = 7;
  // notification type
  NotificationType type = 8;
  // sent at
  google.protobuf.Timestamp sent_at = 9;
  // read at
  google.protobuf.Timestamp read_at = 10;
  // extra info
  NotificationExtraDef extra = 12;
}
