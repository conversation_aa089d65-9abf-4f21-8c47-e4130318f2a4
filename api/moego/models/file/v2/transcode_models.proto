syntax = "proto3";

package moego.models.file.v2;

import "google/protobuf/duration.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/file/v2;filepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.file.v2";

// JobStatus
enum JobStatus {
  // unspecified
  JOB_STATUS_UNSPECIFIED = 0;
  // job submitted
  SUBMITTED = 1;
  // job is running
  RUNNING = 2;
  // job completed
  COMPLETED = 3;
  // job canceled
  CANCELED = 4;
  // job error
  ERROR = 5;
}

// JobModel
message JobModel {
  // job id
  int64 id = 1;
  // account id for the creator
  optional int64 creator_id = 2;
  // company_id
  optional int64 company_id = 3;
  // business_id
  optional int64 business_id = 4;
  // owner type,eg:staff,pet...
  optional string owner_type = 5;
  // owner id
  optional int64 owner_id = 6;
  // job input source
  InputSource input_source = 7;
  // job status
  moego.models.file.v2.JobStatus status = 8;
  // output manifest
  repeated moego.models.file.v2.OutputManifests manifests = 9;
  // job extra info
  optional google.protobuf.Struct extra = 10;
  // job start time
  optional google.protobuf.Timestamp start_time = 12;
  // job end time
  optional google.protobuf.Timestamp end_time = 13;
  // job spent time in milliseconds
  optional google.protobuf.Duration duration = 14;
  // job record create time
  google.protobuf.Timestamp created_at = 15;
  // job record update time
  google.protobuf.Timestamp updated_at = 16;
}

// Rectangle
message Rectangle {
  // The distance, in pixels, between the rectangle and the left edge of the video frame. Specify only even numbers.
  optional int32 x = 1;
  // The distance, in pixels, between the rectangle and the top edge of the video frame. Specify only even numbers.
  optional int32 y = 2;
  // Height of rectangle in pixels. Specify only even numbers.
  optional int32 width = 3;
  // Width of rectangle in pixels. Specify only even numbers.
  optional int32 height = 4;
}

// JobTemplate
message JobTemplate {
  // template name
  string name = 1;
  // template type
  string type = 2;
  // template category
  string category = 3;
  // template description
  optional string description = 4;

  // reserved ...

  // output group settings
  repeated OutputGroup output_groups = 14;
  // template create time
  google.protobuf.Timestamp created_at = 15;
  // template update time
  google.protobuf.Timestamp updated_at = 16;
}

// InputSource
message InputSource {
  // input source: MoeGo, AWS/S3, HTTP
  string input_source = 1;
  // for MoeGo: input path is file id in database
  // for AWS/S3: input path is S3 URI
  // for HTTP: input path is public URL
  string input_path = 2;
}

// InputSettings
message InputSettings {
  // Specify the strength of the input filter.
  // To apply an automatic amount of filtering based the compression artifacts measured in your input: We recommend that you leave Filter strength blank and set Filter enable to Auto.
  // To manually apply filtering: Enter a value from 1 to 5, where 1 is the least amount of filtering and 5 is the most. The value that you enter applies to the strength of the Deblock or Denoise filters, or to the strength of the Advanced input filter.
  optional int32 filter_strength = 1;
  // Set PSI control for transport stream inputs to specify which data the demux process to scans.
  // Ignore PSI - Scan all PIDs for audio and video.
  // Use PSI - Scan only PSI data.
  optional string psi_control = 2;
  // Specify whether to apply input filtering to improve the video quality of your input.
  // To apply filtering depending on your input type and quality: Choose Auto.
  // To apply no filtering: Choose Disable.
  // To apply filtering regardless of your input type and quality: Choose Force.
  // When you do, you must also specify a value for Filter strength.
  optional string filter_enable = 3;
  // Enable Denoise to filter noise from the input. Default is disabled.
  // Only applicable to MPEG2, H.264, H.265, and uncompressed video inputs.
  optional string denoise_filter = 4;
  // Enable Deblock to produce smoother motion in the output. Default is disabled.
  // Only manually controllable for MPEG2 and uncompressed video inputs.
  optional string deblock_filter = 5;
  // Use this Timecode source setting, located under the input settings, to specify how the service counts input video frames.
  optional string timecode_source = 6;
  // Specify the timecode that you want the service to use for this input's initial frame.
  // To use this setting, you must set the Timecode source setting, located under the input settings, to Specified start.
  optional string timecode_start = 7;
  // When you have a progressive segmented frame (PsF) input, use this setting to flag the input as PsF.
  // MediaConvert doesn't automatically detect PsF.
  // Therefore, flagging your input as PsF results in better preservation of video quality when you do deinterlacing and frame rate conversion.
  // If you don't specify, the default value is Auto.
  // Auto is the correct setting for all inputs that are not PsF.
  // Don't set this value to PsF when your input is interlaced.
  // Doing so creates horizontal interlacing artifacts.
  optional string input_scan_type = 8;
  // Use Cropping selection to specify the video area that the service will include in the output video frame.
  // If you specify a value here, it will override any value that you specify in the output setting Cropping selection.
  optional Rectangle crop = 9;
  // Use Selection placement to define the video area in your output frame.
  // The area outside of the rectangle that you specify here is black.
  // If you specify a value here, it will override any value that you specify in the output setting Selection placement.
  // If you specify a value here, this will override any AFD values in your input, even if you set Respond to AFD to Respond.
  // If you specify a value here, this will ignore anything that you specify for the setting Scaling Behavior.
  optional Rectangle position = 10;
  // Contains sets of start and end times that together specify a portion of the input to be used in the outputs.
  // If you provide only a start time, the clip will be the entire input from that point to the end.
  // If you provide only an end time, it will be the entire input up to that point.
  // When you specify more than one input clip, the transcoding service creates the job outputs by stringing the clips together in the order you specify them.
  repeated InputClipping input_clippings = 11;

  // reserved ...

  // Use Audio selectors to specify a track or set of tracks from the input that you will use in your outputs.
  // You can use multiple Audio selectors per input.
  optional AudioSelector audio_selector = 15;
  // Input video selectors contain the video settings for the input.
  // Each of your inputs can have up to one video selector.
  optional VideoSelector video_selector = 16;
}

// OutputPreset
message OutputPreset {
  // preset name
  string name = 1;
  // preset type
  string type = 2;
  // preset category
  string category = 3;
  // preset description
  optional string description = 4;

  // reserved ...

  // transcode settings
  TranscodeSettings transcode_settings = 14;
  // preset create time
  google.protobuf.Timestamp created_at = 15;
  // preset update time
  google.protobuf.Timestamp updated_at = 16;
}

// OutputManifests
message OutputManifests {
  // output group name
  string name = 1;
  // output group type: FILE, HLS, CMAF, DASH_ISO, MS_SMOOTH
  string type = 2;
  // playlist file paths
  repeated string playlist_file_paths = 3;
  // output details
  repeated OutputDetail output_details = 4;
}

// OutputDetail
message OutputDetail {
  // output file paths
  repeated string output_file_paths = 1;
  // media play time
  optional google.protobuf.Duration duration = 2;
  // the width of the video resolution
  optional int32 width = 3;
  // the height of the video resolution
  optional int32 height = 4;
  // video bitrate
  optional int32 bitrate = 5;
}

// OutputGroup
message OutputGroup {
  // output group name
  string name = 1;
  // group settings
  optional OutputGroupSettings group_settings = 2;
  // encoding settings
  optional AutomatedEncodingSettings encoding_settings = 3;
  // outputs
  repeated Output outputs = 4;
}

// OutputGroupSettings
message OutputGroupSettings {
  // output group type: FILE, HLS, CMAF, DASH_ISO, MS_SMOOTH
  string type = 1;
  // group settings
  oneof group_settings {
    // File group settings
    FileGroupSettings file_group_settings = 2;
    // HLS group settings
    HlsGroupSettings hls_group_settings = 3;
    // CMAF group settings
    CmafGroupSettings cmaf_group_settings = 4;
    // DASH ISO group settings
    DashIsoGroupSettings dash_iso_settings = 5;
    // Microsoft smooth group settings
    MsSmoothGroupSettings ms_smooth_group_settings = 6;
  }
}

// AutomatedEncodingSettings
message AutomatedEncodingSettings {
  // ABR settings
  optional AutomatedAbrSettings abr_settings = 1;
}

// Output
message Output {
  // output file extension
  optional string extension = 1;
  // output filename modifier
  optional string name_modifier = 2;
  // output preset
  optional string preset = 3;

  // reserved ...

  // output settings
  optional OutputSettings output_settings = 7;
  // transcode settings
  optional TranscodeSettings transcode_settings = 8;
}

// OutputSettings
message OutputSettings {
  // HLS settings
  optional HlsSettings hls_settings = 1;
}

// TranscodeSettings
message TranscodeSettings {
  // container settings
  optional ContainerSettings container_settings = 1;
  // video settings
  optional VideoDescription video_description = 2;
  // audio settings
  repeated AudioDescription audio_descriptions = 3;
  // caption settings
  repeated CaptionDescription caption_descriptions = 4;
}

// ContainerSettings
message ContainerSettings {
  // container type: MP4, MOV, MPD, M2_TS, M3U8, F4V, RAW
  string type = 1;

  // container settings
  oneof container_settings {
    // MP4 container settings
    MP4Settings mp4_settings = 2;
    // MOV container settings
    MovSettings mov_settings = 3;
    // MPD container settings
    MpdSettings mpd_settings = 4;
    // M2_TS container settings
    M2tsSettings m2ts_settings = 5;
    // M3U8 container settings
    M3u8Settings m3u8_settings = 6;
    // F4V container settings
    F4vSettings f4v_settings = 7;
  }
}

// CaptionDescription
message CaptionDescription {
  // language code
  optional string language_code = 1;
  // language description
  optional string language_description = 2;
  // custom language code
  optional string custom_language_code = 3;
  // caption destination settings
  optional CaptionDestinationSettings destination_settings = 4;
}

// AudioDescription
message AudioDescription {
  // audio source name
  optional string audio_source_name = 1;
  // audio type
  optional int32 audio_type = 2;
  // audio type control
  optional string audio_type_control = 3;
  // language code
  optional string language_code = 4;
  // language code control
  optional string language_code_control = 5;
  // Specify a label for this output audio stream.
  // For example, "English", "Director commentary", or "track_2".
  // For streaming outputs, MediaConvert passes this information into destination manifests for display on the end-viewer's player device.
  // For outputs in other output groups, the service ignores this setting.
  optional string stream_name = 6;
  // Specify the QuickTime audio channel layout tags for the audio channels in this audio track.
  // When you don't specify a value, MediaConvert labels your track as Center (C) by default.
  // To use Audio layout tagging, your output must be in a QuickTime (MOV) container and your audio codec must be AAC, WAV, or AIFF.
  AudioChannelTaggingSettings audio_channel_tagging_settings = 7;
  // Advanced audio normalization settings.
  // Ignore these settings unless you need to comply with a loudness standard.
  AudioNormalizationSettings audio_normalization_settings = 8;
  // remix settings
  optional RemixSettings remix_settings = 9;
  // audio codec settings
  optional AudioCodecSettings audio_settings = 10;
}

// VideoDescription
message VideoDescription {
  // Use Width to define the video resolution width, in pixels, for this output.
  // To use the same resolution as your input: Leave both Width and Height blank.
  // To evenly scale from your input resolution: Leave Width blank and enter a value for Height.
  // For example, if your input is 1920x1080 and you set Height to 720, your output will be 1280x720.
  optional int32 width = 1;
  // Use Height to define the video resolution height, in pixels, for this output.
  // To use the same resolution as your input: Leave both Width and Height blank.
  // To evenly scale from your input resolution: Leave Height blank and enter a value for Width.
  // For example, if your input is 1920x1080 and you set Width to 1280, your output will be 1280x720.
  optional int32 height = 2;
  // Use Sharpness setting to specify the strength of anti-aliasing.
  // This setting changes the width of the anti-alias filter kernel used for scaling.
  // Sharpness only applies if your output resolution is different from your input resolution.
  // 0 is the softest setting, 100 the sharpest, and 50 recommended for most content.
  optional int32 sharpness = 3;
  // Specify the video Scaling behavior when your output has a different resolution than your input.
  // For more information, see https://docs.aws.amazon.com/mediaconvert/latest/ug/video-scaling.html
  optional string scaling_behavior = 4;
  // Choose Insert for this setting to include color metadata in this output.
  // Choose Ignore to exclude color metadata from this output.
  // If you don't specify a value, the service sets this to Insert by default.
  optional string color_metadata = 5;
  // This setting only applies to H.264, H.265, and MPEG2 outputs.
  // Use Insert AFD signaling to specify whether the service includes AFD values in the output video data and what those values are.
  // Choose None to remove all AFD values from this output.
  // Choose Fixed to ignore input AFD values and instead encode the value specified in the job.
  // Choose Auto to calculate output AFD values based on the input AFD scaler data.
  optional string afd_signaling = 6;
  // The anti-alias filter is automatically applied to all outputs.
  // The service no longer accepts the value DISABLED for AntiAlias.
  // If you specify that in your job, the service will ignore the setting.
  optional string anti_alias = 7;
  // Applies only to 29.97 fps outputs.
  // When this feature is enabled, the service will use drop-frame timecode on outputs.
  // If it is not possible to use drop-frame timecode, the system will fall back to non-drop-frame.
  // This setting is enabled by default when Timecode insertion is enabled
  optional string drop_frame_timecode = 8;
  // Applies only if you set AFD Signaling to Fixed.
  // Use Fixed to specify a four-bit AFD value which the service will write on all frames of this video output.
  optional int32 fixed_afd = 9;
  // Use Respond to AFD to specify how the service changes the video itself in response to AFD values in the input.
  // Choose Respond to clip the input video frame according to the AFD value, input display aspect ratio, and output display aspect ratio.
  // Choose Passthrough to include the input AFD values.
  // Do not choose this when AfdSignaling is set to NONE.
  // A preferred implementation of this workflow is to set RespondToAfd to and set AfdSignaling to AUTO.
  // Choose None to remove all input AFD values from this output.
  optional string respond_to_afd = 10;
  // Applies only to H.264, H.265, MPEG2, and ProRes outputs.
  // Only enable Timecode insertion when the input frame rate is identical to the output frame rate.
  // To include timecodes in this output, set Timecode insertion to PIC_TIMING_SEI.
  // To leave them out, set it to DISABLED.
  // Default is DISABLED.
  optional string timecode_insertion = 11;
  // Use Cropping selection to specify the video area that the service will include in the output video frame.
  optional Rectangle crop = 12;
  // Use Selection placement to define the video area in your output frame.
  // The area outside of the rectangle that you specify here is black.
  optional Rectangle position = 13;
  // video codec settings
  optional VideoCodecSettings settings = 14;
}

// AudioCodecSettings
message AudioCodecSettings {
  // codec type: MP2, MP3, WAV, AAC, AIFF, FLAC, OPUS, VORBIS
  string codec = 1;
  // codec settings
  oneof codec_settings {
    // AAC settings
    AacSettings aac_settings = 2;
    // AIFF settings
    AiffSettings aiff_settings = 3;
    // MP2 settings
    MP2Settings mp2_settings = 4;
    // MP3 settings
    MP3Settings mp3_settings = 5;
    // WAV settings
    WavSettings wav_settings = 6;
    // FLAC settings
    FlacSettings flac_settings = 7;
    // OPUS settings
    OpusSettings opus_settings = 8;
    // vorbis settings
    VorbisSettings vorbis_settings = 9;
  }
}

// VideoCodecSettings
message VideoCodecSettings {
  // codec type: FRAME_CAPTURE, H_264, H_265, AV1, MPEG2, PRORES, UNCOMPRESSED, VC3, VP8, VP9
  string codec = 1;
  // codec settings
  oneof codec_settings {
    // frame capture settings
    FrameCaptureSettings frame_capture_settings = 2;
    // H264 settings
    H264Settings h264_settings = 3;
    // H265 settings
    H265Settings h265_settings = 4;
    // AV1 settings
    AV1Settings av1_settings = 5;
    // MPEG2 settings
    MPEG2Settings mpeg2_settings = 6;
    // ProRes settings
    ProResSettings prores_settings = 7;
    // uncompress settings
    UncompressedSettings uncompressed_settings = 8;
    // VC3 settings
    VC3Settings vc3_settings = 9;
    // VP8 settings
    VP8Settings vp8_settings = 10;
    // VP9 settings
    VP9Settings vp9_settings = 11;
  }
}

// AutomatedAbrSettings
message AutomatedAbrSettings {
  // min ABR bit rate
  optional int32 min_abr_bitrate = 1;
  // max ABR bit rate
  optional int32 max_abr_bitrate = 2;
  // max renditions
  optional int32 max_renditions = 3;
  // list of ABR rules
  repeated AutomatedAbrRule rules = 4;
}

// Input Settings
//------------------------------------------------------------------------------

// InputClipping
message InputClipping {
  // Set Start timecode to the beginning of the portion of the input you are clipping.
  // The frame corresponding to the Start timecode value is included in the clip.
  // Start timecode or End timecode may be left blank, but not both.
  // Use the format HH:MM:SS:FF or HH:MM:SS;FF, where HH is the hour, MM is the minute, SS is the second, and FF is the frame number.
  // When choosing this value, take into account your setting for Input timecode source.
  // For example, if you have embedded timecodes that start at 01:00:00:00 and you want your clip to begin five minutes into the video, use 01:05:00:00.
  optional string start_timecode = 1;
  // Set End timecode to the end of the portion of the input you are clipping.
  // The frame corresponding to the End timecode value is included in the clip.
  // Start timecode or End timecode may be left blank, but not both.
  // Use the format HH:MM:SS:FF or HH:MM:SS;FF, where HH is the hour, MM is the minute, SS is the second, and FF is the frame number.
  // When choosing this value, take into account your setting for timecode source under input settings.
  // For example, if you have embedded timecodes that start at 01:00:00:00 and you want your clip to end six minutes into the video, use 01:06:00:00.
  optional string end_timecode = 2;
}

// AudioSelector
message AudioSelector {
  // Apply audio timing corrections to help synchronize audio and video in your output.
  optional string audio_duration_correction = 1;
  // Enable this setting on one audio selector to set it as the default for the job.
  optional string default_selection = 2;
  // Specifies audio data from an external file source.
  optional string external_audio_file_input = 3;
  // Selects a specific language code from within an audio source, using the ISO 639-2 or ISO 639-3 three-letter language code.
  optional string language_code = 4;
  // Specifies a time delta in milliseconds to offset the audio from the input video.
  optional int32 offset = 5;
  // Use this setting for input streams that contain Dolby E, to have the service extract specific program data from the track.
  optional int32 program_selection = 6;
  // Specifies the type of the audio selector
  optional string selector_type = 7;
  // Selects a specific PID from within an audio source (e. g. 257 selects PID 0x101)
  repeated int32 pids = 8;
  // Identify a track from the input audio to include in this selector by entering the track index number.
  repeated int32 tracks = 9;
  // Settings specific to audio sources in an HLS alternate rendition group.
  optional HlsRenditionGroupSettings hls_rendition_group_settings = 10;
  // Use these settings to reorder the audio channels of one input to match those of another input.
  optional RemixSettings remix_settings = 11;
}

// VideoSelector
message VideoSelector {
  // Ignore this setting unless this input is a QuickTime animation with an alpha channel. Use this setting to create separate Key and Fill outputs.
  optional string alpha_behavior = 1;
  // If your input video has accurate color space metadata, or if you don't know about color space: Keep the default value, Follow. MediaConvert will automatically detect your input color space.
  optional string color_space = 2;
  // There are two sources for color metadata, the input file and the job input settings Color space and HDR master display information settings.
  optional string color_space_usage = 3;
  // Set Embedded timecode override to Use MDPM when your AVCHD input contains timecode tag data in the Modified Digital Video Pack Metadata.
  optional string embedded_timecode_override = 4;
  // Use this setting if your input has video and audio durations that don't align, and your output or player has strict alignment requirements.
  optional string pad_video = 5;
  // Use Rotate to specify how the service rotates your video.
  optional string rotate = 6;
  // If the sample range metadata in your input video is accurate, or if you don't know about sample range, keep the default value, Follow, for this setting.
  optional string sample_range = 7;
  // Specify the maximum mastering display luminance.
  // Enter an integer from 0 to 2147483647, in units of 0.0001 nits.
  // For example, enter 10000000 for 1000 nits.
  optional int32 max_luminance = 8;
  // Use PID to select specific video data from an input file.
  optional int32 pid = 9;
  // Selects a specific program from within a multi-program transport stream.
  // Note that Quad 4K is not currently supported
  optional int32 program_number = 10;
  // Use these settings to provide HDR 10 metadata that is missing or inaccurate in your input video.
  optional Hdr10Metadata hdr10_metadata = 11;
}

// Hdr10Metadata
message Hdr10Metadata {
  // HDR Master Display Information must be provided by a color grader, using color grading tools.
  // Range is 0 to 50,000, each increment represents 0.00002 in CIE1931 color coordinate.
  // Note that this setting is not for color correction.
  optional int32 blue_primary_x = 1;
  // HDR Master Display Information must be provided by a color grader, using color grading tools.
  // Range is 0 to 50,000, each increment represents 0.00002 in CIE1931 color coordinate.
  // Note that this setting is not for color correction
  optional int32 blue_primary_y = 2;
  // HDR Master Display Information must be provided by a color grader, using color grading tools.
  // Range is 0 to 50,000, each increment represents 0.00002 in CIE1931 color coordinate.
  // Note that this setting is not for color correction.
  optional int32 green_primary_x = 3;
  // HDR Master Display Information must be provided by a color grader, using color grading tools.
  // Range is 0 to 50,000, each increment represents 0.00002 in CIE1931 color coordinate.
  // Note that this setting is not for color correction.
  optional int32 green_primary_y = 4;
  // Maximum light level among all samples in the coded video sequence, in units of candelas per square meter.
  // This setting doesn't have a default value; you must specify a value that is suitable for the content.
  optional int32 max_content_light_level = 5;
  // Maximum average light level of any frame in the coded video sequence, in units of candelas per square meter.
  // This setting doesn't have a default value; you must specify a value that is suitable for the content.
  optional int32 max_frame_average_light_level = 6;
  // Nominal maximum mastering display luminance in units of of 0.0001 candelas per square meter.
  optional int32 max_luminance = 7;
  // Nominal minimum mastering display luminance in units of of 0.0001 candelas per square meter
  optional int32 min_luminance = 8;
  // HDR Master Display Information must be provided by a color grader, using color grading tools.
  // Range is 0 to 50,000, each increment represents 0.00002 in CIE1931 color coordinate.
  // Note that this setting is not for color correction.
  optional int32 red_primary_x = 9;
  // HDR Master Display Information must be provided by a color grader, using color grading tools.
  // Range is 0 to 50,000, each increment represents 0.00002 in CIE1931 color coordinate.
  // Note that this setting is not for color correction
  optional int32 red_primary_y = 10;
  // HDR Master Display Information must be provided by a color grader, using color grading tools.
  // Range is 0 to 50,000, each increment represents 0.00002 in CIE1931 color coordinate.
  // Note that this setting is not for color correction.
  optional int32 white_point_x = 11;
  // HDR Master Display Information must be provided by a color grader, using color grading tools.
  // Range is 0 to 50,000, each increment represents 0.00002 in CIE1931 color coordinate.
  // Note that this setting is not for color correction.
  optional int32 white_point_y = 12;
}

// HlsRenditionGroupSettings
message HlsRenditionGroupSettings {
  // Specify alternative group ID
  optional string rendition_group_id = 1;
  // Specify ISO 639-2 or ISO 639-3 code in the language property
  optional string rendition_language_code = 2;
  // Specify media name
  optional string rendition_name = 3;
}

// RemixSettings
message RemixSettings {
  // Optionally specify the channel in your input that contains your audio description audio signal.
  optional int32 audio_description_audio_channel = 1;
  // Optionally specify the channel in your input that contains your audio description data stream.
  optional int32 audio_description_data_channel = 2;
  // Specify the number of audio channels from your input that you want to use in your output.
  optional int32 channels_in = 3;
  // Specify the number of channels in this output after remixing. Valid values: 1, 2, 4, 6, 8... 64.
  optional int32 channels_out = 4;
  // Channel mapping contains the group of fields that hold the remixing value for each channel, in dB.
  optional ChannelMapping channel_mapping = 5;
}

// ChannelMapping
message ChannelMapping {
  // output channel mappings
  repeated OutputChannelMapping output_channels = 1;
}

// OutputChannelMapping
message OutputChannelMapping {
  // Use this setting to specify your remix values when they are integers, such as -10, 0, or 4.
  repeated int32 input_channels = 1;
  // Use this setting to specify your remix values when they have a decimal component, such as -10.312, 0.08, or 4.9.
  repeated double input_channels_fine_tune = 2;
}

// Output Group Settings
//------------------------------------------------------------------------------

// FileGroupSettings
message FileGroupSettings {
  // S3 output location, must be s3://xxx
  optional string destination = 1;
}

// HlsGroupSettings
message HlsGroupSettings {
  // S3 output location, must be s3://xxx
  optional string destination = 1;
  // audio only header
  optional string audio_only_header = 2;
  // caption segment length control
  optional string caption_segment_length_control = 3;
  // caption language setting
  optional string caption_language_setting = 4;
  // client cache
  optional string client_cache = 5;
  // codec specification
  optional string codec_specification = 6;
  // directory structure
  optional string directory_structure = 7;
  // image based trick play
  optional string image_based_trick_play = 8;
  // manifest compression
  optional string manifest_compression = 9;
  // manifest duration format
  optional string manifest_duration_format = 10;
  // min final segment length
  optional double min_final_segment_length = 11;
  // min segment length
  optional int32 min_segment_length = 12;
  // output selection
  optional string output_selection = 13;
  // program date time
  optional string program_date_time = 14;
  // program date time period
  optional int32 program_date_time_period = 15;
  // progressive write HLS manifest
  optional string progressive_write_hls_manifest = 16;
  // segment control
  optional string segment_control = 17;
  // segment length
  optional int32 segment_length = 18;
  // segment length control
  optional string segment_length_control = 19;
  // segment INF resolution
  optional string stream_inf_resolution = 20;
  // target duration compatibility mode
  optional string target_duration_compatibility_mode = 21;
  // timed metadata ID3 frame
  optional string timed_metadata_id3_frame = 22;
  // timed metadata ID3 period
  optional int32 timed_metadata_id3_period = 23;
  // timestamp delta milliseconds
  optional int32 timestamp_delta_milliseconds = 24;
}

// CmafGroupSettings
message CmafGroupSettings {
  // S3 output location, must be s3://xxx
  optional string destination = 1;
  // client cache
  optional string client_cache = 2;
  // codec specification
  optional string codec_specification = 3;
  // dash I-Frame trick play name modifier
  optional string dash_i_frame_trick_play_name_modifier = 4;
  // dash manifest style
  optional string dash_manifest_style = 5;
  // fragment length
  optional int32 fragment_length = 6;
  // image based trick play
  optional string image_based_trick_play = 7;
  // manifest compression
  optional string manifest_compression = 8;
  // manifest duration format
  optional string manifest_duration_format = 9;
  // min buffer time
  optional int32 min_buffer_time = 10;
  // min final segment length
  optional double min_final_segment_length = 11;
  // min segment length
  optional int32 mpd_manifest_bandwidth_type = 12;
  // mpd profile
  optional int32 mpd_profile = 13;
  // pts offset handling for B-Frames
  optional string pts_offset_handling_for_b_frames = 14;
  // segment control
  optional string segment_control = 15;
  // segment length
  optional int32 segment_length = 16;
  // segment length control
  optional string segment_length_control = 17;
  // segment INF resolution
  optional string stream_inf_resolution = 18;
  // target duration compatibility mode
  optional string target_duration_compatibility_mode = 19;
  // video composition offsets
  optional string video_composition_offsets = 20;
  // write dash manifest
  optional string write_dash_manifest = 21;
  // write hls manifest
  optional string write_hls_manifest = 22;
  // write segment timeline in representation
  optional string write_segment_timeline_in_representation = 23;
}

// DashIsoGroupSettings
message DashIsoGroupSettings {
  // S3 output location, must be s3://xxx
  optional string destination = 1;
  // dash I-Frame trick play name modifier
  optional string dash_i_frame_trick_play_name_modifier = 2;
  // dash manifest style
  optional string dash_manifest_style = 3;
  // fragment length
  optional int32 fragment_length = 4;
  // HbbTV compliance
  optional string hbbtv_compliance = 5;
  // image based trick play
  optional string image_based_trick_play = 6;
  // min buffer time
  optional int32 min_buffer_time = 7;
  // min final segment length
  optional double min_final_segment_length = 8;
  // min segment length
  optional int32 mpd_manifest_bandwidth_type = 9;
  // mpd profile
  optional int32 mpd_profile = 10;
  // pts offset handling for B-Frames
  optional string pts_offset_handling_for_b_frames = 11;
  // segment control
  optional string segment_control = 12;
  // segment length
  optional int32 segment_length = 13;
  // segment length control
  optional string segment_length_control = 14;
  // video composition offsets
  optional string video_composition_offsets = 15;
  // write segment timeline in representation
  optional string write_segment_timeline_in_representation = 16;
}

// MsSmoothGroupSettings
message MsSmoothGroupSettings {
  // S3 output location, must be s3://xxx
  optional string destination = 1;
  // audio deduplication
  optional string audio_deduplication = 2;
  // fragment length
  optional int32 fragment_length = 3;
  // fragment length control
  optional string fragment_length_control = 4;
  // manifest encoding
  optional string manifest_encoding = 5;
}

// HlsSettings
message HlsSettings {
  // audio group id
  optional string audio_group_id = 1;
  // audio only container
  optional string audio_only_container = 2;
  // audio rendition sets
  optional string audio_rendition_sets = 3;
  // audio tack type
  optional string audio_track_type = 4;
  // descriptive video service flag
  optional string descriptive_video_service_flag = 5;
  // I-Frame only manifest
  optional string i_frame_only_manifest = 6;
  // segment modifier
  optional string segment_modifier = 7;
}

// Container Settings
//------------------------------------------------------------------------------

// MP4Settings
message MP4Settings {
  // audio duration
  optional string audio_duration = 1;
  // cslg atom
  optional string cslg_atom = 2;
  // ctts version
  optional int32 ctts_version = 3;
  // free space box
  optional string free_space_box = 4;
  // moov placement
  optional string moov_placement = 5;
  // mp4 major brand
  optional string mp4_major_brand = 6;
}

// MovSettings
message MovSettings {
  // clap atom
  optional string clap_atom = 1;
  // cslg atom
  optional string cslg_atom = 2;
  // mpeg2 fourcc control
  optional string mpeg2_fourcc_control = 3;
  // padding control
  optional string padding_control = 4;
  // reference
  optional string reference = 5;
}

// MpdSettings
message MpdSettings {
  // audio duration
  optional string audio_duration = 1;
  // accessibility caption hints
  optional string accessibility_caption_hints = 2;
  // caption container type
  optional string caption_container_type = 3;
  // key-length-value metadata
  optional string klv_metadata = 4;
  // manifest metadata signaling
  optional string manifest_metadata_signaling = 5;
  // SCTE-35 ESAM
  optional string scte35_esam = 6;
  // SCTE-35 source
  optional string scte35_source = 7;
  // timed metadata
  optional string timed_metadata = 8;
  // timed metadata box version
  optional string timed_metadata_box_version = 9;
  // timed metadata scheme id uri
  optional string timed_metadata_scheme_id_uri = 10;
  // timed metadata value
  optional string timed_metadata_value = 11;
}

// M2tsSettings
message M2tsSettings {
  // audio buffer model
  optional string audio_buffer_model = 1;
  // audio duration
  optional string audio_duration = 2;
  // audio frames per pes
  optional int32 audio_frames_per_pes = 3;
  // list of audio pid
  repeated int32 audio_pids = 4;
  //bitrate
  optional int32 bitrate = 5;
  // buffer model
  optional string buffer_model = 6;
  // data PTS control
  optional string data_pts_control = 7;
  // list of DVB sub pid
  repeated int32 dvb_sub_pids = 8;
  // DVB teletext pid
  optional int32 dvb_teletext_pid = 9;
  // EBP audio interval
  optional string ebp_audio_interval = 10;
  // EBP placement
  optional string ebp_placement = 11;
  // controls whether to include the ES Rate field in the PES header.
  optional string es_rate_in_pes = 12;
  // Keep the default value unless you know that your audio EBP markers are incorrectly appearing before your video EBP markers.
  // To correct this problem, set this value to Force.
  optional string force_ts_video_ebp_order = 13;
  // fragment time
  optional double fragment_time = 14;
  // key-length-value metadata
  optional string klv_metadata = 15;
  // max pcr interval
  optional int32 max_pcr_interval = 16;
  // min EBP interval
  optional int32 min_ebp_interval = 17;
  // nielsen ID3
  optional string nielsen_id3 = 18;
  // null packet bitrate
  optional double null_packet_bitrate = 19;
  // pat interval
  optional int32 pat_interval = 20;
  // pcr control
  optional string pcr_control = 21;
  // pcr pid
  optional int32 pcr_pid = 22;
  // pmt interval
  optional int32 pmt_interval = 23;
  // pmt pid
  optional int32 pmt_pid = 24;
  // prevent buffer underflow
  optional string prevent_buffer_underflow = 25;
  // private metadata pid
  optional int32 private_metadata_pid = 26;
  // program number
  optional int32 program_number = 27;
  // pts offset
  optional int32 pts_offset = 28;
  // pts offset mode
  optional string pts_offset_mode = 29;
  // rate mode
  optional string rate_mode = 30;
  // SCTE-35 pid
  optional int32 scte35_pid = 31;
  // SCTE-35 source
  optional string scte35_source = 32;
  // segmentation markers
  optional string segmentation_markers = 33;
  // segmentation style
  optional string segmentation_style = 34;
  // segmentation time
  optional double segmentation_time = 35;
  // timed metadata pid
  optional int32 timed_metadata_pid = 36;
  // transport stream id
  optional int32 transport_stream_id = 37;
  // video pid
  optional int32 video_pid = 38;
}

// M3u8Settings
message M3u8Settings {
  // audio duration
  optional string audio_duration = 1;
  // audio frames per pes
  optional int32 audio_frames_per_pes = 2;
  // list of audio pid
  repeated int32 audio_pids = 3;
  // data PTS control
  optional string data_pts_control = 4;
  // max pcr interval
  optional int32 max_pcr_interval = 5;
  // nielsen ID3
  optional string nielsen_id3 = 6;
  // pat interval
  optional int32 pat_interval = 7;
  // pcr control
  optional string pcr_control = 8;
  // pcr pid
  optional int32 pcr_pid = 9;
  // pmt interval
  optional int32 pmt_interval = 10;
  // pmt pid
  optional int32 pmt_pid = 11;
  // private metadata pid
  optional int32 private_metadata_pid = 12;
  // program number
  optional int32 program_number = 13;
  // pts offset
  optional int32 pts_offset = 14;
  // pts offset mode
  optional string pts_offset_mode = 15;
  // SCTE-35 pid
  optional int32 scte35_pid = 16;
  // SCTE-35 source
  optional string scte35_source = 17;
  // timed metadata
  optional string timed_metadata = 18;
  // timed metadata pid
  optional int32 timed_metadata_pid = 19;
  // transport stream id
  optional int32 transport_stream_id = 20;
  // video pid
  optional int32 video_pid = 21;
}

// F4vSettings
message F4vSettings {
  // moov placement
  optional string moov_placement = 1;
}

// Video Codec Settings
//------------------------------------------------------------------------------

// FrameCaptureSettings
message FrameCaptureSettings {
  // Frame capture will encode the first frame of the output stream, then one frame every framerateDenominator/ framerateNumerator seconds.
  // For example, settings of framerateNumerator = 1 and framerateDenominator = 3 (a rate of 1/ 3 frame per second) will capture the first frame, then 1 frame every 3s.
  // Files will be named as filename.n.jpg where n is the 0-based sequence number of each Capture.
  optional int32 framerate_denominator = 1;
  // Frame capture will encode the first frame of the output stream, then one frame every framerateDenominator/ framerateNumerator seconds.
  // For example, settings of framerateNumerator = 1 and framerateDenominator = 3 (a rate of 1/ 3 frame per second) will capture the first frame, then 1 frame every 3s.
  // Files will be named as filename.NNNNNNN.jpg where N is the 0-based frame sequence number zero padded to 7 decimal places.
  optional int32 framerate_numerator = 2;
  // Maximum number of captures (encoded jpg output files).
  optional int32 max_captures = 3;
  // JPEG Quality - a higher value equals higher quality.
  optional int32 quality = 4;
}

// H264Settings
message H264Settings {
  // codec level
  optional string codec_level = 1;
  // codec profile
  optional string codec_profile = 2;
  // adaptive quantization
  optional string adaptive_quantization = 3;
  // bitrate
  optional int32 bitrate = 4;
  // max bitrate
  optional int32 max_bitrate = 5;
  // hrd buffer size
  optional int32 hrd_buffer_size = 6;
  // hrd buffer initial fill percentage
  optional int32 hrd_buffer_initial_fill_percentage = 7;
  // hrd buffer final fill percentage
  optional int32 hrd_buffer_final_fill_percentage = 8;
  // Optionally include or suppress markers at the end of your output that signal the end of the video stream.
  // To include end of stream markers: Leave blank or keep the default value, Include.
  // To not include end of stream markers: Choose Suppress.
  // This is useful when your output will be inserted into another stream.
  optional string end_of_stream_markers = 9;
  // dynamic sub gop
  optional string dynamic_sub_gop = 10;
  // entropy encoding
  optional string entropy_encoding = 11;
  // field encoding
  optional string field_encoding = 12;
  // interlace mode
  optional string interlace_mode = 13;
  // framerate control
  optional string framerate_control = 14;
  // framerate conversion algorithm
  optional string framerate_conversion_algorithm = 15;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateDenominator to specify the denominator of this fraction.
  // In this example, use 1001 for the value of FramerateDenominator.
  optional int32 framerate_denominator = 16;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateNumerator to specify the numerator of this fraction.
  // In this example, use 24000 for the value of FramerateNumerator.
  optional int32 framerate_numerator = 17;
  // Specify whether to allow B-frames to be referenced by other frame types.
  // To use reference B-frames when your GOP structure has 1 or more B-frames: Leave blank or keep the default value Enabled.
  // We recommend that you choose Enabled to help improve the video quality of your output relative to its bitrate.
  // To not use reference B-frames: Choose Disabled.
  optional string gop_b_reference = 18;
  // gop closed cadence
  optional int32 gop_closed_cadence = 19;
  // gop size
  optional double gop_size = 20;
  // gop size units
  optional string gop_size_units = 21;
  // quality tuning level
  optional string quality_tuning_level = 22;
  // par control
  optional string par_control = 23;
  // Required when you set Pixel aspect ratio to SPECIFIED.
  // On the console, this corresponds to any value other than Follow source.
  // When you specify an output pixel aspect ratio (PAR) that is different from your input video PAR, provide your output PAR as a ratio.
  // For example, for D1/DV NTSC widescreen, you would specify the ratio 40:33.
  // In this example, the value for parDenominator is 33
  optional int32 par_denominator = 24;
  // Required when you set Pixel aspect ratio to SPECIFIED.
  // On the console, this corresponds to any value other than Follow source.
  // When you specify an output pixel aspect ratio (PAR) that is different from your input video PAR, provide your output PAR as a ratio.
  // For example, for D1/DV NTSC widescreen, you would specify the ratio 40:33.
  // In this example, the value for parNumerator is 40.
  optional int32 par_numerator = 25;
  // number B-Frames between reference frames
  optional int32 number_b_frames_between_reference_frames = 26;
  // number reference frames
  optional int32 number_reference_frames = 27;
  // Specify the number of slices per picture. This value must be 1, 2, 4, 8, 16, or 32.
  // For progressive pictures, this value must be less than or equal to the number of macroblock rows.
  // For interlaced pictures, this value must be less than or equal to half the number of macroblock rows.
  optional int32 slices = 28;
  // softness
  optional int32 softness = 29;
  // min I-Frames interval
  optional int32 min_i_interval = 30;
  // Places a PPS header on each encoded picture, even if repeated.
  optional string repeat_pps = 31;
  // Enable this setting to insert I-frames at scene changes that the service automatically detects.
  // This improves video quality and is enabled by default.
  // If this output uses QVBR, choose Transition detection for further video quality improvement.
  // For more information about QVBR, see https://docs.aws.amazon.com/console/mediaconvert/cbr-vbr-qvbr.
  optional string scene_change_detect = 32;
  // Use this setting for interlaced outputs, when your output frame rate is half of your input frame rate.
  // In this situation, choose Optimized interlacing to create a better quality interlaced output.
  // In this case, each progressive frame from the input corresponds to an interlaced field in the output.
  // Keep the default value, Basic interlacing, for all other output frame rates. With basic interlacing, MediaConvert performs any frame rate conversion first and then interlaces the frames.
  // When you choose Optimized interlacing and you set your output frame rate to a value that isn't suitable for optimized interlacing, MediaConvert automatically falls back to basic interlacing.
  // Required settings: To use optimized interlacing, you must set Telecine to None or Soft.
  // You can't use optimized interlacing for hard telecine outputs.
  // You must also set Interlace mode to a value other than Progressive.
  optional string scan_type_conversion_mode = 33;
  // When you do frame rate conversion from 23.976 frames per second (fps) to 29.97 fps, and your output scan type is interlaced, you can optionally enable hard or soft telecine to create a smoother picture.
  // Hard telecine produces a 29.97i output.
  // Soft telecine produces an output with a 23.976 output that signals to the video player device to do the conversion during play back.
  // When you keep the default value, None, MediaConvert does a standard frame rate conversion to 29.97 without doing anything with the field polarity to create a smoother picture.
  optional string telecine = 34;
  // Only use this setting when you change the default value, AUTO, for the setting `adaptive_quantization`.
  // When you keep all defaults, excluding `adaptive_quantization` and all other adaptive quantization from your JSON job specification.
  // MediaConvert automatically applies the best types of quantization for your video content.
  // When you set `adaptive_quantization` to a value other than AUTO, the default value for flicker_adaptive_quantization is Disabled. Change this value to Enabled to reduce I-frame pop.
  // I-frame pop appears as a visual flicker that can arise when the encoder saves bits by copying some macroblocks many times from frame to frame, and then refreshes them at the I-frame.
  // When you enable this setting, the encoder updates these macroblocks slightly more often to smooth out the flicker.
  // To manually enable or disable flicker_adaptive_quantization, you must set Adaptive quantization to a value other than AUTO.
  optional string flicker_adaptive_quantization = 35;
  // Keep the default value, Enabled, to adjust quantization within each frame based on spatial variation of content complexity.
  // When you enable this feature, the encoder uses fewer bits on areas that can sustain more distortion with no noticeable visual degradation and uses more bits on areas where any small distortion will be noticeable.
  // For example, complex textured blocks are encoded with fewer bits and smooth textured blocks are encoded with more bits.
  // Enabling this feature will almost always improve your video quality.
  // Note, though, that this feature doesn't take into account where the viewer's attention is likely to be.
  // If viewers are likely to be focusing their attention on a part of the screen with a lot of complex texture, you might choose to disable this feature.
  // Related setting: When you enable spatial adaptive quantization, set the value for Adaptive quantization depending on your content.
  // For homogeneous content, such as cartoons and video games, set it to Low.
  // For content with a wider variety of textures, set it to High or Higher.
  optional string spatial_adaptive_quantization = 36;
  // Only use this setting when you change the default value, AUTO, for the setting `adaptive_quantization`.
  // When you keep all defaults, excluding `adaptive_quantization` and all other adaptive quantization from your JSON job specification.
  // MediaConvert automatically applies the best types of quantization for your video content.
  optional string temporal_adaptive_quantization = 37;
  // Inserts timecode for each frame as 4 bytes of an unregistered SEI message.
  optional string unregistered_sei_timecode = 38;
  // Ignore this setting unless your input frame rate is 23.976 or 24 frames per second (fps). Enable slow PAL to create a 25 fps output.
  // When you enable slow PAL, MediaConvert relabels the video frames to 25 fps and resamples your audio to keep it synchronized with the video.
  // Note that enabling this setting will slightly reduce the duration of your video.
  // Required settings: You must also set Framerate to 25.
  optional string slow_pal = 39;
  // Produces a bitstream compliant with SMPTE RP-2027.
  optional string syntax = 40;
  // rate control mode
  optional string rate_control_mode = 41;
  // qvbr settings
  optional QvbrSettings qvbr_settings = 42;
}

// H265Settings
message H265Settings {
  // codec level
  optional string codec_level = 1;
  // codec profile
  optional string codec_profile = 2;
  // adaptive quantization
  optional string adaptive_quantization = 3;
  // bitrate
  optional int32 bitrate = 4;
  // max bitrate
  optional int32 max_bitrate = 5;
  // hrd buffer size
  optional int32 hrd_buffer_size = 6;
  // hrd buffer initial fill percentage
  optional int32 hrd_buffer_initial_fill_percentage = 7;
  // hrd buffer final fill percentage
  optional int32 hrd_buffer_final_fill_percentage = 8;
  // Optionally include or suppress markers at the end of your output that signal the end of the video stream.
  // To include end of stream markers: Leave blank or keep the default value, Include.
  // To not include end of stream markers: Choose Suppress.
  // This is useful when your output will be inserted into another stream.
  optional string end_of_stream_markers = 9;
  // dynamic sub gop
  optional string dynamic_sub_gop = 10;
  // interlace mode
  optional string interlace_mode = 11;
  // framerate control
  optional string framerate_control = 12;
  // framerate conversion algorithm
  optional string framerate_conversion_algorithm = 13;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateDenominator to specify the denominator of this fraction.
  // In this example, use 1001 for the value of FramerateDenominator.
  optional int32 framerate_denominator = 14;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateNumerator to specify the numerator of this fraction.
  // In this example, use 24000 for the value of FramerateNumerator.
  optional int32 framerate_numerator = 15;
  // Specify whether to allow B-frames to be referenced by other frame types.
  // To use reference B-frames when your GOP structure has 1 or more B-frames: Leave blank or keep the default value Enabled.
  // We recommend that you choose Enabled to help improve the video quality of your output relative to its bitrate.
  // To not use reference B-frames: Choose Disabled.
  optional string gop_b_reference = 16;
  // gop closed cadence
  optional int32 gop_closed_cadence = 17;
  // gop size
  optional double gop_size = 18;
  // gop size units
  optional string gop_size_units = 19;
  // quality tuning level
  optional string quality_tuning_level = 20;
  // par control
  optional string par_control = 21;
  // Required when you set Pixel aspect ratio to SPECIFIED.
  // On the console, this corresponds to any value other than Follow source.
  // When you specify an output pixel aspect ratio (PAR) that is different from your input video PAR, provide your output PAR as a ratio.
  // For example, for D1/DV NTSC widescreen, you would specify the ratio 40:33.
  // In this example, the value for parDenominator is 33
  optional int32 par_denominator = 22;
  // Required when you set Pixel aspect ratio to SPECIFIED.
  // On the console, this corresponds to any value other than Follow source.
  // When you specify an output pixel aspect ratio (PAR) that is different from your input video PAR, provide your output PAR as a ratio.
  // For example, for D1/DV NTSC widescreen, you would specify the ratio 40:33.
  // In this example, the value for parNumerator is 40.
  optional int32 par_numerator = 23;
  // number B-Frames between reference frames
  optional int32 number_b_frames_between_reference_frames = 24;
  // number reference frames
  optional int32 number_reference_frames = 25;
  // Specify the number of slices per picture. This value must be 1, 2, 4, 8, 16, or 32.
  // For progressive pictures, this value must be less than or equal to the number of macroblock rows.
  // For interlaced pictures, this value must be less than or equal to half the number of macroblock rows.
  optional int32 slices = 26;
  // min I-Frames interval
  optional int32 min_i_interval = 27;
  // Places a PPS header on each encoded picture, even if repeated.
  optional string sample_adaptive_offset_filter_mode = 28;
  // Enable this setting to insert I-frames at scene changes that the service automatically detects.
  // This improves video quality and is enabled by default.
  // If this output uses QVBR, choose Transition detection for further video quality improvement.
  // For more information about QVBR, see https://docs.aws.amazon.com/console/mediaconvert/cbr-vbr-qvbr.
  optional string scene_change_detect = 29;
  // Use this setting for interlaced outputs, when your output frame rate is half of your input frame rate.
  // In this situation, choose Optimized interlacing to create a better quality interlaced output.
  // In this case, each progressive frame from the input corresponds to an interlaced field in the output.
  // Keep the default value, Basic interlacing, for all other output frame rates. With basic interlacing, MediaConvert performs any frame rate conversion first and then interlaces the frames.
  // When you choose Optimized interlacing and you set your output frame rate to a value that isn't suitable for optimized interlacing, MediaConvert automatically falls back to basic interlacing.
  // Required settings: To use optimized interlacing, you must set Telecine to None or Soft.
  // You can't use optimized interlacing for hard telecine outputs.
  // You must also set Interlace mode to a value other than Progressive.
  optional string scan_type_conversion_mode = 30;
  // Ignore this setting unless your input frame rate is 23.976 or 24 frames per second (fps). Enable slow PAL to create a 25 fps output.
  // When you enable slow PAL, MediaConvert relabels the video frames to 25 fps and resamples your audio to keep it synchronized with the video.
  // Note that enabling this setting will slightly reduce the duration of your video.
  // Required settings: You must also set Framerate to 25.
  optional string slow_pal = 31;
  // Only use this setting when you change the default value, AUTO, for the setting `adaptive_quantization`.
  // When you keep all defaults, excluding `adaptive_quantization` and all other adaptive quantization from your JSON job specification.
  // MediaConvert automatically applies the best types of quantization for your video content.
  // When you set `adaptive_quantization` to a value other than AUTO, the default value for flicker_adaptive_quantization is Disabled. Change this value to Enabled to reduce I-frame pop.
  // I-frame pop appears as a visual flicker that can arise when the encoder saves bits by copying some macroblocks many times from frame to frame, and then refreshes them at the I-frame.
  // When you enable this setting, the encoder updates these macroblocks slightly more often to smooth out the flicker.
  // To manually enable or disable flicker_adaptive_quantization, you must set Adaptive quantization to a value other than AUTO.
  optional string flicker_adaptive_quantization = 32;
  // Keep the default value, Enabled, to adjust quantization within each frame based on spatial variation of content complexity.
  // When you enable this feature, the encoder uses fewer bits on areas that can sustain more distortion with no noticeable visual degradation and uses more bits on areas where any small distortion will be noticeable.
  // For example, complex textured blocks are encoded with fewer bits and smooth textured blocks are encoded with more bits.
  // Enabling this feature will almost always improve your video quality.
  // Note, though, that this feature doesn't take into account where the viewer's attention is likely to be.
  // If viewers are likely to be focusing their attention on a part of the screen with a lot of complex texture, you might choose to disable this feature.
  // Related setting: When you enable spatial adaptive quantization, set the value for Adaptive quantization depending on your content.
  // For homogeneous content, such as cartoons and video games, set it to Low.
  // For content with a wider variety of textures, set it to High or Higher.
  optional string spatial_adaptive_quantization = 33;
  // Only use this setting when you change the default value, AUTO, for the setting `adaptive_quantization`.
  // When you keep all defaults, excluding `adaptive_quantization` and all other adaptive quantization from your JSON job specification.
  // MediaConvert automatically applies the best types of quantization for your video content.
  optional string temporal_adaptive_quantization = 34;
  // Inserts timecode for each frame as 4 bytes of an unregistered SEI message.
  optional string unregistered_sei_timecode = 35;
  // Enables temporal layer identifiers in the encoded bitstream.
  // Up to 3 layers are supported depending on GOP structure: I- and P-frames form one layer, reference B-frames can form a second layer and non-reference b-frames can form a third layer.
  // Decoders can optionally decode only the lower temporal layers to generate a lower frame rate output.
  // For example, given a bitstream with temporal IDs and with b-frames = 1 (i. e. IbPbPb display order), a decoder could decode all the frames for full frame rate output or only the I and P frames (lowest temporal layer) for a half frame rate output.
  optional string temporal_ids = 36;
  // When you do frame rate conversion from 23.976 frames per second (fps) to 29.97 fps, and your output scan type is interlaced, you can optionally enable hard or soft telecine to create a smoother picture.
  // Hard telecine produces a 29.97i output.
  // Soft telecine produces an output with a 23.976 output that signals to the video player device to do the conversion during play back.
  // When you keep the default value, None, MediaConvert does a standard frame rate conversion to 29.97 without doing anything with the field polarity to create a smoother picture.
  optional string telecine = 37;
  // tiles
  optional string tiles = 38;
  // write mp4 packaging type
  optional string write_mp4_packaging_type = 39;
  // rate control mode
  optional string rate_control_mode = 40;
  // qvbr settings
  optional QvbrSettings qvbr_settings = 41;
}

// AV1Settings
message AV1Settings {
  // adaptive quantization
  optional string adaptive_quantization = 1;
  // Specify the Bit depth. You can choose 8-bit or 10-bit
  optional string bit_depth = 2;
  // Film grain synthesis replaces film grain present in your content with similar quality synthesized AV1 film grain.
  // We recommend that you choose Enabled to reduce the bandwidth of your QVBR quality level 5, 6, 7, or 8 outputs.
  // For QVBR quality level 9 or 10 outputs we recommend that you keep the default value, Disabled.
  // When you include Film grain synthesis, you cannot include the Noise reducer preprocessor.
  optional string film_grain_synthesis = 3;
  // framerate control
  optional string framerate_control = 4;
  // framerate conversion algorithm
  optional string framerate_conversion_algorithm = 5;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateDenominator to specify the denominator of this fraction.
  // In this example, use 1001 for the value of FramerateDenominator.
  optional int32 framerate_denominator = 6;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateNumerator to specify the numerator of this fraction.
  // In this example, use 24000 for the value of FramerateNumerator.
  optional int32 framerate_numerator = 7;
  // gop size
  optional double gop_size = 8;
  // max bitrate
  optional int32 max_bitrate = 9;
  // number B-Frames between reference frames
  optional int32 number_b_frames_between_reference_frames = 10;
  // Specify the number of slices per picture. This value must be 1, 2, 4, 8, 16, or 32.
  // For progressive pictures, this value must be less than or equal to the number of macroblock rows.
  // For interlaced pictures, this value must be less than or equal to half the number of macroblock rows.
  optional int32 slices = 11;
  // Keep the default value, Enabled, to adjust quantization within each frame based on spatial variation of content complexity.
  // When you enable this feature, the encoder uses fewer bits on areas that can sustain more distortion with no noticeable visual degradation and uses more bits on areas where any small distortion will be noticeable.
  // For example, complex textured blocks are encoded with fewer bits and smooth textured blocks are encoded with more bits.
  // Enabling this feature will almost always improve your video quality.
  // Note, though, that this feature doesn't take into account where the viewer's attention is likely to be.
  // If viewers are likely to be focusing their attention on a part of the screen with a lot of complex texture, you might choose to disable this feature.
  // Related setting: When you enable spatial adaptive quantization, set the value for Adaptive quantization depending on your content.
  // For homogeneous content, such as cartoons and video games, set it to Low.
  // For content with a wider variety of textures, set it to High or Higher.
  optional string spatial_adaptive_quantization = 12;
  // rate control mode
  optional string rate_control_mode = 13;
  // qvbr settings
  optional QvbrSettings qvbr_settings = 14;
}

// MPEG2Settings
message MPEG2Settings {
  // codec level
  optional string codec_level = 1;
  // codec profile
  optional string codec_profile = 2;
  // adaptive quantization
  optional string adaptive_quantization = 3;
  // bitrate
  optional int32 bitrate = 4;
  // max bitrate
  optional int32 max_bitrate = 5;
  // hrd buffer size
  optional int32 hrd_buffer_size = 6;
  // hrd buffer initial fill percentage
  optional int32 hrd_buffer_initial_fill_percentage = 7;
  // hrd buffer final fill percentage
  optional int32 hrd_buffer_final_fill_percentage = 8;
  // dynamic sub gop
  optional string dynamic_sub_gop = 9;
  // framerate control
  optional string framerate_control = 10;
  // framerate conversion algorithm
  optional string framerate_conversion_algorithm = 11;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateDenominator to specify the denominator of this fraction.
  // In this example, use 1001 for the value of FramerateDenominator.
  optional int32 framerate_denominator = 12;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateNumerator to specify the numerator of this fraction.
  // In this example, use 24000 for the value of FramerateNumerator.
  optional int32 framerate_numerator = 13;
  // gop closed cadence
  optional int32 gop_closed_cadence = 14;
  // gop size
  optional double gop_size = 15;
  // gop size units
  optional string gop_size_units = 16;
  // interlace mode
  optional string interlace_mode = 17;
  // Use Intra DC precision to set quantization precision for intra-block DC coefficients.
  // If you choose the value auto, the service will automatically select the precision based on the per-frame compression ratio.
  optional string intra_dc_precision = 18;
  // Enable this setting to insert I-frames at scene changes that the service automatically detects.
  // This improves video quality and is enabled by default.
  // If this output uses QVBR, choose Transition detection for further video quality improvement.
  // For more information about QVBR, see https://docs.aws.amazon.com/console/mediaconvert/cbr-vbr-qvbr.
  optional string scene_change_detect = 19;
  // Use this setting for interlaced outputs, when your output frame rate is half of your input frame rate.
  // In this situation, choose Optimized interlacing to create a better quality interlaced output.
  // In this case, each progressive frame from the input corresponds to an interlaced field in the output.
  // Keep the default value, Basic interlacing, for all other output frame rates. With basic interlacing, MediaConvert performs any frame rate conversion first and then interlaces the frames.
  // When you choose Optimized interlacing and you set your output frame rate to a value that isn't suitable for optimized interlacing, MediaConvert automatically falls back to basic interlacing.
  // Required settings: To use optimized interlacing, you must set Telecine to None or Soft.
  // You can't use optimized interlacing for hard telecine outputs.
  // You must also set Interlace mode to a value other than Progressive.
  optional string scan_type_conversion_mode = 20;
  // quality tuning level
  optional string quality_tuning_level = 21;
  // number B-Frames between reference frames
  optional int32 number_b_frames_between_reference_frames = 22;
  // par control
  optional string par_control = 23;
  // Required when you set Pixel aspect ratio to SPECIFIED.
  // On the console, this corresponds to any value other than Follow source.
  // When you specify an output pixel aspect ratio (PAR) that is different from your input video PAR, provide your output PAR as a ratio.
  // For example, for D1/DV NTSC widescreen, you would specify the ratio 40:33.
  // In this example, the value for parDenominator is 33
  optional int32 par_denominator = 24;
  // Required when you set Pixel aspect ratio to SPECIFIED.
  // On the console, this corresponds to any value other than Follow source.
  // When you specify an output pixel aspect ratio (PAR) that is different from your input video PAR, provide your output PAR as a ratio.
  // For example, for D1/DV NTSC widescreen, you would specify the ratio 40:33.
  // In this example, the value for parNumerator is 40.
  optional int32 par_numerator = 25;
  // min I-Frames interval
  optional int32 min_i_interval = 26;
  // softness
  optional int32 softness = 27;
  // Ignore this setting unless your input frame rate is 23.976 or 24 frames per second (fps). Enable slow PAL to create a 25 fps output.
  // When you enable slow PAL, MediaConvert relabels the video frames to 25 fps and resamples your audio to keep it synchronized with the video.
  // Note that enabling this setting will slightly reduce the duration of your video.
  // Required settings: You must also set Framerate to 25.
  optional string slow_pal = 28;
  // Produces a bitstream compliant with SMPTE RP-2027.
  optional string syntax = 29;
  // When you do frame rate conversion from 23.976 frames per second (fps) to 29.97 fps, and your output scan type is interlaced, you can optionally enable hard or soft telecine to create a smoother picture.
  // Hard telecine produces a 29.97i output.
  // Soft telecine produces an output with a 23.976 output that signals to the video player device to do the conversion during play back.
  // When you keep the default value, None, MediaConvert does a standard frame rate conversion to 29.97 without doing anything with the field polarity to create a smoother picture.
  optional string telecine = 30;
  // rate control mode
  optional string rate_control_mode = 31;
  // Keep the default value, Enabled, to adjust quantization within each frame based on spatial variation of content complexity.
  // When you enable this feature, the encoder uses fewer bits on areas that can sustain more distortion with no noticeable visual degradation and uses more bits on areas where any small distortion will be noticeable.
  // For example, complex textured blocks are encoded with fewer bits and smooth textured blocks are encoded with more bits.
  // Enabling this feature will almost always improve your video quality.
  // Note, though, that this feature doesn't take into account where the viewer's attention is likely to be.
  // If viewers are likely to be focusing their attention on a part of the screen with a lot of complex texture, you might choose to disable this feature.
  // Related setting: When you enable spatial adaptive quantization, set the value for Adaptive quantization depending on your content.
  // For homogeneous content, such as cartoons and video games, set it to Low.
  // For content with a wider variety of textures, set it to High or Higher.
  optional string spatial_adaptive_quantization = 32;
  // Only use this setting when you change the default value, AUTO, for the setting `adaptive_quantization`.
  // When you keep all defaults, excluding `adaptive_quantization` and all other adaptive quantization from your JSON job specification.
  // MediaConvert automatically applies the best types of quantization for your video content.
  optional string temporal_adaptive_quantization = 33;
}

// ProResSettings
message ProResSettings {
  // codec profile
  optional string codec_profile = 1;
  // framerate control
  optional string framerate_control = 2;
  // framerate conversion algorithm
  optional string framerate_conversion_algorithm = 3;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateDenominator to specify the denominator of this fraction.
  // In this example, use 1001 for the value of FramerateDenominator.
  optional int32 framerate_denominator = 4;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateNumerator to specify the numerator of this fraction.
  // In this example, use 24000 for the value of FramerateNumerator.
  optional int32 framerate_numerator = 5;
  // par control
  optional string par_control = 6;
  // Required when you set Pixel aspect ratio to SPECIFIED.
  // On the console, this corresponds to any value other than Follow source.
  // When you specify an output pixel aspect ratio (PAR) that is different from your input video PAR, provide your output PAR as a ratio.
  // For example, for D1/DV NTSC widescreen, you would specify the ratio 40:33.
  // In this example, the value for parDenominator is 33
  optional int32 par_denominator = 7;
  // Required when you set Pixel aspect ratio to SPECIFIED.
  // On the console, this corresponds to any value other than Follow source.
  // When you specify an output pixel aspect ratio (PAR) that is different from your input video PAR, provide your output PAR as a ratio.
  // For example, for D1/DV NTSC widescreen, you would specify the ratio 40:33.
  // In this example, the value for parNumerator is 40.
  optional int32 par_numerator = 8;
  // interlace mode
  optional string interlace_mode = 9;
}

// UncompressedSettings
message UncompressedSettings {
  // The four character code for the uncompressed video.
  optional string fourcc = 1;
  // framerate control
  optional string framerate_control = 2;
  // framerate conversion algorithm
  optional string framerate_conversion_algorithm = 3;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateDenominator to specify the denominator of this fraction.
  // In this example, use 1001 for the value of FramerateDenominator.
  optional int32 framerate_denominator = 4;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateNumerator to specify the numerator of this fraction.
  // In this example, use 24000 for the value of FramerateNumerator.
  optional int32 framerate_numerator = 5;
  // interlace mode
  optional string interlace_mode = 6;
  // Use this setting for interlaced outputs, when your output frame rate is half of your input frame rate.
  // In this situation, choose Optimized interlacing to create a better quality interlaced output.
  // In this case, each progressive frame from the input corresponds to an interlaced field in the output.
  // Keep the default value, Basic interlacing, for all other output frame rates. With basic interlacing, MediaConvert performs any frame rate conversion first and then interlaces the frames.
  // When you choose Optimized interlacing and you set your output frame rate to a value that isn't suitable for optimized interlacing, MediaConvert automatically falls back to basic interlacing.
  // Required settings: To use optimized interlacing, you must set Telecine to None or Soft.
  // You can't use optimized interlacing for hard telecine outputs.
  // You must also set Interlace mode to a value other than Progressive.
  optional string scan_type_conversion_mode = 7;
  // Ignore this setting unless your input frame rate is 23.976 or 24 frames per second (fps). Enable slow PAL to create a 25 fps output.
  // When you enable slow PAL, MediaConvert relabels the video frames to 25 fps and resamples your audio to keep it synchronized with the video.
  // Note that enabling this setting will slightly reduce the duration of your video.
  // Required settings: You must also set Framerate to 25.
  optional string slow_pal = 8;
  // When you do frame rate conversion from 23.976 frames per second (fps) to 29.97 fps, and your output scan type is interlaced, you can optionally enable hard or soft telecine to create a smoother picture.
  // Hard telecine produces a 29.97i output.
  // Soft telecine produces an output with a 23.976 output that signals to the video player device to do the conversion during play back.
  // When you keep the default value, None, MediaConvert does a standard frame rate conversion to 29.97 without doing anything with the field polarity to create a smoother picture.
  optional string telecine = 9;
}

// VC3Settings
message VC3Settings {
  // framerate control
  optional string framerate_control = 1;
  // framerate conversion algorithm
  optional string framerate_conversion_algorithm = 2;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateDenominator to specify the denominator of this fraction.
  // In this example, use 1001 for the value of FramerateDenominator.
  optional int32 framerate_denominator = 3;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateNumerator to specify the numerator of this fraction.
  // In this example, use 24000 for the value of FramerateNumerator.
  optional int32 framerate_numerator = 4;
  // interlace mode
  optional string interlace_mode = 5;
  // Use this setting for interlaced outputs, when your output frame rate is half of your input frame rate.
  // In this situation, choose Optimized interlacing to create a better quality interlaced output.
  // In this case, each progressive frame from the input corresponds to an interlaced field in the output.
  // Keep the default value, Basic interlacing, for all other output frame rates. With basic interlacing, MediaConvert performs any frame rate conversion first and then interlaces the frames.
  // When you choose Optimized interlacing and you set your output frame rate to a value that isn't suitable for optimized interlacing, MediaConvert automatically falls back to basic interlacing.
  // Required settings: To use optimized interlacing, you must set Telecine to None or Soft.
  // You can't use optimized interlacing for hard telecine outputs.
  // You must also set Interlace mode to a value other than Progressive.
  optional string scan_type_conversion_mode = 6;
  // Ignore this setting unless your input frame rate is 23.976 or 24 frames per second (fps). Enable slow PAL to create a 25 fps output.
  // When you enable slow PAL, MediaConvert relabels the video frames to 25 fps and resamples your audio to keep it synchronized with the video.
  // Note that enabling this setting will slightly reduce the duration of your video.
  // Required settings: You must also set Framerate to 25.
  optional string slow_pal = 7;
  // When you do frame rate conversion from 23.976 frames per second (fps) to 29.97 fps, and your output scan type is interlaced, you can optionally enable hard or soft telecine to create a smoother picture.
  // Hard telecine produces a 29.97i output.
  // Soft telecine produces an output with a 23.976 output that signals to the video player device to do the conversion during play back.
  // When you keep the default value, None, MediaConvert does a standard frame rate conversion to 29.97 without doing anything with the field polarity to create a smoother picture.
  optional string telecine = 8;
  // Specify the VC3 class to choose the quality characteristics for this output.
  // VC3 class, together with the settings Framerate (framerateNumerator and framerateDenominator) and Resolution (height and width), determine your output bitrate.
  // For example, say that your video resolution is 1920x1080 and your framerate is 29.97.
  // Then Class 145 gives you an output with a bitrate of approximately 145 Mbps and Class 220 gives you and output with a bitrate of approximately 220 Mbps.
  // VC3 class also specifies the color bit depth of your output.
  optional string vc3_class = 9;
}

// VP8Settings
message VP8Settings {
  // bitrate
  optional int32 bitrate = 1;
  // framerate control
  optional string framerate_control = 2;
  // framerate conversion algorithm
  optional string framerate_conversion_algorithm = 3;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateDenominator to specify the denominator of this fraction.
  // In this example, use 1001 for the value of FramerateDenominator.
  optional int32 framerate_denominator = 4;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateNumerator to specify the numerator of this fraction.
  // In this example, use 24000 for the value of FramerateNumerator.
  optional int32 framerate_numerator = 5;
  // gop size
  optional double gop_size = 6;
  // hrd buffer size
  optional int32 hrd_buffer_size = 7;
  // max bitrate
  optional int32 max_bitrate = 8;
  // par control
  optional string par_control = 9;
  // Required when you set Pixel aspect ratio to SPECIFIED.
  // On the console, this corresponds to any value other than Follow source.
  // When you specify an output pixel aspect ratio (PAR) that is different from your input video PAR, provide your output PAR as a ratio.
  // For example, for D1/DV NTSC widescreen, you would specify the ratio 40:33.
  // In this example, the value for parDenominator is 33
  optional int32 par_denominator = 10;
  // Required when you set Pixel aspect ratio to SPECIFIED.
  // On the console, this corresponds to any value other than Follow source.
  // When you specify an output pixel aspect ratio (PAR) that is different from your input video PAR, provide your output PAR as a ratio.
  // For example, for D1/DV NTSC widescreen, you would specify the ratio 40:33.
  // In this example, the value for parNumerator is 40.
  optional int32 par_numerator = 11;
  // quality tuning level
  optional string quality_tuning_level = 12;
  // rate control mode
  optional string rate_control_mode = 13;
}

// VP9Settings
message VP9Settings {
  // bitrate
  optional int32 bitrate = 1;
  // framerate control
  optional string framerate_control = 2;
  // framerate conversion algorithm
  optional string framerate_conversion_algorithm = 3;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateDenominator to specify the denominator of this fraction.
  // In this example, use 1001 for the value of FramerateDenominator.
  optional int32 framerate_denominator = 4;
  // When you use the API for transcode jobs that use frame rate conversion, specify the frame rate as a fraction.
  // For example, 24000 / 1001 = 23.976 fps.
  // Use FramerateNumerator to specify the numerator of this fraction.
  // In this example, use 24000 for the value of FramerateNumerator.
  optional int32 framerate_numerator = 5;
  // gop size
  optional double gop_size = 6;
  // hrd buffer size
  optional int32 hrd_buffer_size = 7;
  // max bitrate
  optional int32 max_bitrate = 8;
  // par control
  optional string par_control = 9;
  // Required when you set Pixel aspect ratio to SPECIFIED.
  // On the console, this corresponds to any value other than Follow source.
  // When you specify an output pixel aspect ratio (PAR) that is different from your input video PAR, provide your output PAR as a ratio.
  // For example, for D1/DV NTSC widescreen, you would specify the ratio 40:33.
  // In this example, the value for parDenominator is 33
  optional int32 par_denominator = 10;
  // Required when you set Pixel aspect ratio to SPECIFIED.
  // On the console, this corresponds to any value other than Follow source.
  // When you specify an output pixel aspect ratio (PAR) that is different from your input video PAR, provide your output PAR as a ratio.
  // For example, for D1/DV NTSC widescreen, you would specify the ratio 40:33.
  // In this example, the value for parNumerator is 40.
  optional int32 par_numerator = 11;
  // quality tuning level
  optional string quality_tuning_level = 12;
  // rate control mode
  optional string rate_control_mode = 13;
}

// QvbrSettings
message QvbrSettings {
  // max average_bitrate
  optional int32 max_average_bitrate = 1;
  // qvbr quality level
  optional int32 qvbr_quality_level = 2;
  // qvbr quality level fine tune
  optional double qvbr_quality_level_fine_tune = 3;
}

// Audio Codec Settings
//------------------------------------------------------------------------------

// AudioChannelTaggingSettings
message AudioChannelTaggingSettings {
  // Specify the QuickTime audio channel layout tags for the audio channels in this audio track.
  // Enter channel layout tags in the same order as your output's audio channel order.
  // For example, if your output audio track has a left and a right channel, enter Left (L) for the first channel and Right (R) for the second.
  // If your output has multiple single-channel audio tracks, enter a single channel layout tag for each track.
  optional string channel_tag = 1;
  // Specify the QuickTime audio channel layout tags for the audio channels in this audio track.
  // Enter channel layout tags in the same order as your output's audio channel order.
  // For example, if your output audio track has a left and a right channel, enter Left (L) for the first channel and Right (R) for the second.
  // If your output has multiple single-channel audio tracks, enter a single channel layout tag for each track.
  repeated string channel_tags = 2;
}

// AudioNormalizationSettings
message AudioNormalizationSettings {
  // Choose one of the following audio normalization algorithms: ITU-R BS.1770-1: Ungated loudness.
  // A measurement of ungated average loudness for an entire piece of content, suitable for measurement of short-form content under ATSC recommendation A/ 85.
  // Supports up to 5.1 audio channels. ITU-R BS.1770-2: Gated loudness.
  // A measurement of gated average loudness compliant with the requirements of EBU-R128. Supports up to 5.1 audio channels.
  // ITU-R BS.1770-3: Modified peak.
  // The same loudness measurement algorithm as 1770-2, with an updated true peak measurement.
  // ITU-R BS.1770-4: Higher channel count.
  // Allows for more audio channels than the other algorithms, including configurations such as 7.1.
  optional string algorithm = 1;
  // When enabled the output audio is corrected using the chosen algorithm.
  // If disabled, the audio will be measured but not adjusted.
  optional string algorithm_control = 2;
  // Content measuring above this level will be corrected to the target level.
  // Content measuring below this level will not be corrected.
  optional int32 correction_gate_level = 3;
  // If set to LOG, log each output's audio track loudness to a CSV file.
  optional string loudness_logging = 4;
  // If set to TRUE_PEAK, calculate and log the TruePeak for each output's audio track loudness.
  optional string peak_calculation = 5;
  // When you use Audio normalization, optionally use this setting to specify a target loudness.
  // If you don't specify a value here, the encoder chooses a value for you, based on the algorithm that you choose for Algorithm.
  // If you choose algorithm 1770-1, the encoder will choose -24 LKFS; otherwise, the encoder will choose -23 LKFS
  optional double target_lkfs = 6;
  // Specify the True-peak limiter threshold in decibels relative to full scale (dBFS).
  // The peak inter-audio sample loudness in your output will be limited to the value that you specify, without affecting the overall target LKFS.
  // Enter a value from 0 to -8. Leave blank to use the default value 0.
  optional double true_peak_limiter_threshold = 7;
}

// MP2Settings
message MP2Settings {
  // bitrate
  optional int32 bitrate = 1;
  // channels
  optional int32 channels = 2;
  // sample rate
  optional int32 sample_rate = 3;
}

// MP3Settings
message MP3Settings {
  // bitrate
  optional int32 bitrate = 1;
  // channels
  optional int32 channels = 2;
  // sample rate
  optional int32 sample_rate = 3;
  // VBR quality
  optional int32 vbr_quality = 4;
  // rate control mode
  optional string rate_control_mode = 5;
}

// AacSettings
message AacSettings {
  // bitrate
  optional int32 bitrate = 1;
  // codec profile
  optional string codec_profile = 2;
  //coding mode
  optional string coding_mode = 3;
  // raw format
  optional string raw_format = 4;
  // sample rate
  optional int32 sample_rate = 5;
  // specification
  optional string specification = 6;
  // VBR quality
  optional int32 vbr_quality = 7;
  // choose BROADCASTER_MIXED_AD when the input contains pre-mixed main audio + audio description (AD) as a stereo pair.
  optional string audio_description_broadcaster_mix = 8;
  // rate control mode
  optional string rate_control_mode = 9;
}

// AacSettings
message AiffSettings {
  // bit depth
  optional int32 bit_depth = 1;
  // channels
  optional int32 channels = 2;
  // sample rate
  optional int32 sample_rate = 3;
}

// WavSettings
message WavSettings {
  // bit depth
  optional int32 bit_depth = 1;
  // channels
  optional int32 channels = 2;
  // sample rate
  optional int32 sample_rate = 3;
  // format
  optional int32 format = 4;
}

// FlacSettings
message FlacSettings {
  // bit depth
  optional int32 bit_depth = 1;
  // channels
  optional int32 channels = 2;
  // sample rate
  optional int32 sample_rate = 3;
}

// OpusSettings
message OpusSettings {
  // bitrate
  optional int32 bitrate = 1;
  // channels
  optional int32 channels = 2;
  // sample rate
  optional int32 sample_rate = 3;
}

// VorbisSettings
message VorbisSettings {
  // channels
  optional int32 channels = 1;
  // sample rate
  optional int32 sample_rate = 2;
  // Specify the variable audio quality of this Vorbis output from -1 (lowest quality, ~45 kbit/ s) to 10 (highest quality, ~500 kbit/ s).
  // The default value is 4 (~128 kbit/ s). Values 5 and 6 are approximately 160 and 192 kbit/ s, respectively.
  optional int32 vbr_quality = 3;
}

// Automated ABR Settings
//------------------------------------------------------------------------------

// AutomatedAbrRule
message AutomatedAbrRule {
  // ABR rule type: MIN_TOP_RENDITION_SIZE, MIN_BOTTOM_RENDITION_SIZE, FORCE_INCLUDE_RENDITIONS, ALLOWED_RENDITIONS
  string type = 1;

  // The following fields can only specify one of them depending on the type.

  // min top rendition
  optional MinTopRenditionSize min_top_rendition_size = 2;
  // min bottom rendition
  optional MinBottomRenditionSize min_bottom_rendition_size = 3;
  // allowed renditions
  repeated AllowedRenditionSize allowed_renditions = 4;
  // force include renditions
  repeated ForceIncludeRenditionSize force_include_renditions = 5;
}

// MinTopRenditionSize
message MinTopRenditionSize {
  // width
  optional int32 width = 1;
  // height
  optional int32 height = 2;
}

// MinBottomRenditionSize
message MinBottomRenditionSize {
  // width
  optional int32 width = 1;
  // height
  optional int32 height = 2;
}

// ForceIncludeRenditionSize
message ForceIncludeRenditionSize {
  // width
  optional int32 width = 1;
  // height
  optional int32 height = 2;
}

// AllowedRenditionSize
message AllowedRenditionSize {
  // width
  optional int32 width = 1;
  // height
  optional int32 height = 2;
  // required: ENABLED, DISABLED
  optional string required = 3;
}

// Caption Settings
//------------------------------------------------------------------------------

// CaptionDestinationSettings
message CaptionDestinationSettings {
  // caption destination type
  string type = 1;
  // destination settings
  oneof destination_settings {
    // BURN_IN destination settings
    BurninDestinationSettings burnin_destination_settings = 2;
    // DVB_SUB destination settings
    DvbSubDestinationSettings dvb_sub_destination_settings = 3;
    // EMBEDDED destination settings
    EmbeddedDestinationSettings embedded_destination_settings = 4;
    // IMSC destination settings
    ImscDestinationSettings imsc_destination_settings = 5;
    // SCC destination settings
    SccDestinationSettings scc_destination_settings = 6;
    // SRT destination settings
    SrtDestinationSettings srt_destination_settings = 7;
    // TELETEXT destination settings
    TeletextDestinationSettings teletext_destination_settings = 8;
    // TTML destination settings
    TtmlDestinationSettings ttml_destination_settings = 9;
    // WEBVTT destination settings
    WebvttDestinationSettings webvtt_destination_settings = 10;
  }
}

// BurninDestinationSettings
message BurninDestinationSettings {
  // alignment
  optional string alignment = 1;
  // apply font color
  optional string apply_font_color = 2;
  // background color
  optional string background_color = 3;
  // background opacity
  optional int32 background_opacity = 4;
  // fallback font
  optional string fallback_font = 5;
  // font color
  optional string font_color = 6;
  // font file bold
  optional string font_file_bold = 7;
  // font file bold italic
  optional string font_file_bold_italic = 8;
  // font file italic
  optional string font_file_italic = 9;
  // font file regular
  optional string font_file_regular = 10;
  // font opacity
  optional int32 font_opacity = 11;
  // font resolution
  optional int32 font_resolution = 12;
  // font script
  optional string font_script = 13;
  // font size
  optional int32 font_size = 14;
  // hex font color
  optional string hex_font_color = 15;
  // outline color
  optional string outline_color = 16;
  // outline size
  optional int32 outline_size = 17;
  // shadow color
  optional string shadow_color = 18;
  // shadow opacity
  optional int32 shadow_opacity = 19;
  // shadow x offset
  optional int32 shadow_x_offset = 20;
  // shadow y offset
  optional int32 shadow_y_offset = 21;
  // style passthrough
  optional string style_passthrough = 22;
  // teletext spacing
  optional string teletext_spacing = 23;
  // x position
  optional int32 x_position = 24;
  // y position
  optional int32 y_position = 25;
}

// DvbSubDestinationSettings
message DvbSubDestinationSettings {
  // alignment
  optional string alignment = 1;
  // apply font color
  optional string apply_font_color = 2;
  // background color
  optional string background_color = 3;
  // background opacity
  optional int32 background_opacity = 4;
  // DDS handling
  optional string dds_handling = 5;
  // subtitling type
  optional string subtitling_type = 6;
  // DDS x coordinate
  optional int32 dds_x_coordinate = 7;
  // DDS y coordinate
  optional int32 dds_y_coordinate = 8;
  // width
  optional int32 width = 9;
  // height
  optional int32 height = 10;
  // fallback font
  optional string fallback_font = 11;
  // font color
  optional string font_color = 12;
  // font file bold
  optional string font_file_bold = 13;
  // font file bold italic
  optional string font_file_bold_italic = 14;
  // font file italic
  optional string font_file_italic = 15;
  // font file regular
  optional string font_file_regular = 16;
  // font opacity
  optional int32 font_opacity = 17;
  // font resolution
  optional int32 font_resolution = 18;
  // font script
  optional string font_script = 19;
  // font size
  optional int32 font_size = 20;
  // hex font color
  optional string hex_font_color = 21;
  // outline color
  optional string outline_color = 22;
  // outline size
  optional int32 outline_size = 23;
  // shadow color
  optional string shadow_color = 24;
  // shadow opacity
  optional int32 shadow_opacity = 25;
  // shadow x offset
  optional int32 shadow_x_offset = 26;
  // shadow y offset
  optional int32 shadow_y_offset = 27;
  // style passthrough
  optional string style_passthrough = 28;
  // teletext spacing
  optional string teletext_spacing = 29;
  // x position
  optional int32 x_position = 30;
  // y position
  optional int32 y_position = 31;
}

// EmbeddedDestinationSettings
message EmbeddedDestinationSettings {
  // Ignore this setting unless your input captions are SCC format and your output captions are embedded in the video stream.
  // Specify a CC number for each captions channel in this output.
  // If you have two channels, choose CC numbers that aren't in the same field.
  // For example, choose 1 and 3.
  // For more information, see https://docs.aws.amazon.com/console/mediaconvert/dual-scc-to-embedded.
  optional int32 destination_608_channel_number = 1;
  // Ignore this setting unless your input captions are SCC format and you want both 608 and 708 captions embedded in your output stream.
  // Optionally, specify the 708 service number for each output captions channel.
  // Choose a different number for each channel.
  // To use this setting, also set Force 608 to 708 upconvert to Upconvert in your input captions selector settings.
  // If you choose to upconvert but don't specify a 708 service number, MediaConvert uses the number that you specify for CC channel number for the 708 service number.
  // For more information, see https://docs.aws.amazon.com/console/mediaconvert/dual-scc-to-embedded.
  optional int32 destination_708_service_number = 2;
}

// ImscDestinationSettings
message ImscDestinationSettings {
  // accessibility
  optional string accessibility = 1;
  // style passthrough
  optional string style_passthrough = 2;
}

// SccDestinationSettings
message SccDestinationSettings {
  // Set Framerate to make sure that the captions and the video are synchronized in the output.
  // Specify a frame rate that matches the frame rate of the associated video.
  // If the video frame rate is 29.97, choose 29.97 dropframe only if the video has video_insertion=true and drop_frame_timecode=true; otherwise, choose 29.97 non-dropframe.
  optional string framerate = 1;
}

// SrtDestinationSettings
message SrtDestinationSettings {
  // style passthrough
  optional string style_passthrough = 1;
}

// TeletextDestinationSettings
message TeletextDestinationSettings {
  // page number
  optional string page_number = 1;
  // page types
  repeated string page_types = 2;
}

// TtmlDestinationSettings
message TtmlDestinationSettings {
  // style passthrough
  optional string style_passthrough = 1;
}

// WebvttDestinationSettings
message WebvttDestinationSettings {
  // accessibility
  optional string accessibility = 1;
  // style passthrough
  optional string style_passthrough = 2;
}
