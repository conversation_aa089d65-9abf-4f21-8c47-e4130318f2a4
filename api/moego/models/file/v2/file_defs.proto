syntax = "proto3";
package moego.models.file.v2;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/file/v2;filepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.file.v2";

// PlatformSource
message PlatformSourceDef {}

// TenantSource
message TenantSourceDef {
  // company_id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business_id
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}
