syntax = "proto3";

package moego.models.file.v2;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/file/v2;filepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.file.v2";

// file status
enum FileStatus {
  // UNSPECIFIED
  FILE_STATUS_UNSPECIFIED = 0;
  // UPLOADING
  FILE_STATUS_UPLOADING = 1;
  // UPLOADED
  FILE_STATUS_UPLOADED = 2;
  // ERROR
  FILE_STATUS_ERROR = 3;
  // DELETE
  FILE_STATUS_DELETE = 4;
}

// file usage
enum FileUsage {
  // UNSPECIFIED
  FILE_USAGE_UNSPECIFIED = 0;
  // used for testing
  USED_FOR_TESTING = 1;
  // used for avatar
  USED_FOR_AVATAR = 2;
  // used for photo
  USED_FOR_PHOTO = 3;
  // used for email template image
  USED_FOR_EMAIL_TEMPLATE_IMAGE = 4;
  // used for import/export template
  USED_FOR_IMPORT_EXPORT_TEMPLATE = 5;
  // used for export
  USED_FOR_EXPORT = 6;
  // used for message report
  USED_FOR_MESSAGE_REPORT = 7;
  // used for video transcode
  USED_FOR_VIDEO_TRANSCODE = 8;
  // used for video
  USED_FOR_VIDEO = 9;
  // used for idogcam camera viewer
  USED_FOR_IDOGCAM_CAMERA_VIEWER = 10;
}

// file model
message FileModel {
  // id
  int64 id = 1;
  // account id for the creator
  int64 creator_id = 2;
  // company_id
  int64 company_id = 3;
  // business_id
  int64 business_id = 4;
  // usage for the file, must be one of FileUsage
  string usage = 5;
  // source file name(with extension)
  string file_name = 6;
  // s3 file key
  string file_key = 7;
  // file size byte
  int64 file_size_byte = 8;
  // owner type,eg:staff,pet...
  string owner_type = 9;
  // owner id
  int64 owner_id = 10;
  // file status
  FileStatus status = 11;
  // access url for download
  string access_url = 12;
  // bucket
  string bucket = 13;
  // create time
  google.protobuf.Timestamp created_at = 14;
  // update time
  optional google.protobuf.Timestamp updated_at = 15;
  // delete time
  optional google.protobuf.Timestamp deleted_at = 16;
  // file extension
  string file_ext = 17;
  // file metadata
  map<string, string> metadata = 18;
}
