syntax = "proto3";

package moego.models.payment.v2;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v2";

// EntityType
enum EntityType {
  // Unspecified
  ENTITY_TYPE_UNSPECIFIED = 0;
  // Company
  COMPANY = 1;
  // Business
  BUSINESS = 2;
  // Customer
  CUSTOMER = 3;
  // MoeGo
  MOEGO = 4;
}

// ChannelType
enum ChannelType {
  // Unspecified
  CHANNEL_TYPE_UNSPECIFIED = 0;
  // Adyen
  ADYEN = 1;
  // Stripe
  STRIPE = 2;
}
