syntax = "proto3";

package moego.models.payment.v2;

import "moego/models/payment/v2/common_enums.proto";
import "moego/models/payment/v2/onboard_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v2";

// Onboard 的一个步骤
message OnboardStep {
  // 步骤标识，前端用来区分步骤、展示对应的文案
  string key = 1;
  // 待验证的信息
  repeated OnboardVerification verifications = 2;
}

// 某个具体的验证记录
message OnboardVerification {
  // 状态
  OnboardVerificationStatus status = 1;
  // 依赖该验证的 capability，不同 channel 可能取值有所不同。
  string capability = 2;
  // 信息，status 不为 Valid 的时候有效。
  string error_message = 3;
}

// 一个渠道账号
message ChannelAccount {
  // ID
  int64 id = 1;
  // Entity type
  EntityType entity_type = 2;
  // Entity ID.
  int64 entity_id = 3;
  // channel type
  ChannelType channel_type = 4;
  // Channel account ID
  string channel_account_id = 5;
  // Account (verification) status
  ChannelAccountStatus status = 6;
  // Charges enabled
  bool charges_enabled = 7;
  // Payouts enabled
  bool payouts_enabled = 8;
}

// The extra for an channel account.
message ChannelAccountExtra {
  // The extra for an Adyen company-level account.
  message AdyenCompanyExtra {
    // ID for Adyen business line
    string business_line_id = 1;
    // ID for Adyen account holder
    string account_holder_id = 2;
  }

  // The extra for an Adyen business-level account.
  message AdyenBusinessExtra {
    // ID for Adyen store
    string store_id = 1;
    // The reference of the Adyen store
    string store_reference = 2;
  }

  // The extra
  oneof extra {
    // Adyen Company account extra
    AdyenCompanyExtra adyen_company_extra = 1;
    // Adyen Business account extra
    AdyenBusinessExtra adyen_business_extra = 2;
  }
}

// The bank account
message BankAccount {
  // 渠道
  ChannelType channel_type = 1;
  // 渠道侧 ID，这边先不存 Bank Account，所以没有 ID，只有渠道 ID
  string channel_id = 2;
  // 卡号信息
  oneof account {
    // US
    // (-- api-linter: core::0142::time-field-type=disabled
    //     aip.dev/not-precedent: 误报。 --)
    USAccount us = 3;
    // UK
    UKAccount uk = 4;
    // AU
    AUAccount au = 5;
    // CA
    CAAccount ca = 6;
  }
}

// US account number
message USAccount {
  // Routing number
  string routing_number = 1;
  // Account number
  string account_number = 2;
}

// UK account number
message UKAccount {
  // Sort code
  string sort_code = 1;
  // Account number
  string account_number = 2;
}

// Australia account number
message AUAccount {
  // BSB code
  string bsb_code = 1;
  // Account number
  string account_number = 2;
}

// Canada account number
message CAAccount {
  // Transit number
  string transit_number = 1;
  // Institution number
  string institution_number = 2;
  // Account number
  string account_number = 3;
}
