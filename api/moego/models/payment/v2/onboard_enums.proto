syntax = "proto3";

package moego.models.payment.v2;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v2";

// OnboardStatus
enum OnboardStatus {
  // Unspecified
  ONBOARD_STATUS_UNSPECIFIED = 0;
  // Initial
  INITIAL = 1;
  // Configured (ready for onboarding, may already started onboarding)
  CONFIGURED = 2;
  // Onboarded, but the user has not be acknowledged
  ONBOARDED = 3;
  // Finished (the user confirm explicitly)
  FINISHED = 4;
  // Not applicable
  NOT_APPLICABLE = 5;
}

// OnboardVerificationStatus
enum OnboardVerificationStatus {
  // Unspecified
  ONBOARD_VERIFICATION_STATUS_UNSPECIFIED = 0;
  // Pending
  PENDING = 1;
  // Valid
  VALID = 2;
  // Invalid
  INVALID = 3;
  // Rejected (invalid and cannot re-submit)
  REJECTED = 4;
}

// ChannelAccountStatus
enum ChannelAccountStatus {
  // Unspecified
  CHANNEL_ACCOUNT_STATUS_UNSPECIFIED = 0;
  // Restricted，payout 和 charge 至少其中一个不可用
  CHANNEL_ACCOUNT_STATUS_RESTRICTED = 1;
  // Restricted soon，需要在指定时间内 update 信息
  CHANNEL_ACCOUNT_STATUS_RESTRICTED_SOON = 2;
  // Pending，渠道正在审核账号信息，稍后会自动流转
  CHANNEL_ACCOUNT_STATUS_PENDING = 3;
  // Enabled，但有额外的资料建议提供
  CHANNEL_ACCOUNT_STATUS_ENABLED = 4;
  // Completed，账号正常
  CHANNEL_ACCOUNT_STATUS_COMPLETED = 5;
  // Rejected，一般是账号被 ban
  CHANNEL_ACCOUNT_STATUS_REJECTED = 6;
}
