syntax = "proto3";

package moego.models.payment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// PaymentStatus is the status of a payment
enum PaymentStatus {
  // created
  CREATED = 0;
  // processing
  PROCESSING = 1;
  // paid
  PAID = 2;
  // completed
  COMPLETED = 3;
  // failed
  FAILED = -1;
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// Refund payment status.
enum RefundPaymentStatus {
  // created
  REFUND_PAYMENT_STATUS_CREATED = 0;
  // processing
  REFUND_PAYMENT_STATUS_PROCESSING = 1;
  // paid
  REFUND_PAYMENT_STATUS_PAID = 2;
  // completed
  REFUND_PAYMENT_STATUS_COMPLETED = 3;
  // failed
  REFUND_PAYMENT_STATUS_FAILED = -1;
  // init
  REFUND_PAYMENT_STATUS_INIT = -2;
}

// PaymentModule is the module of a payment
enum PaymentModule {
  // unspecified
  PAYMENT_MODULE_UNSPECIFIED = 0;
  // grooming
  GROOMING = 1;
  // retail
  RETAIL = 2;
  // membership
  MEMBERSHIP = 3;
}
