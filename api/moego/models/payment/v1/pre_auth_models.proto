// @since 2024-01-24 15:56:45
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.payment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// The PreAuth model for appointment
message PreAuthCalendarView {
  // pre auth id
  int64 pre_auth_id = 1;
  // payment id
  int64 payment_id = 2;
  // ticket id
  int64 ticket_id = 3;
  // customer id
  int64 customer_id = 4;
  // pre auth amount
  double pre_auth_amount = 5;
  // pre auth time
  int64 pre_auth_time = 6;
  // pre auth status
  int32 pre_auth_status = 7;
  // pre auth payment method
  string pre_auth_payment_method = 8;
  // pre auth failed message
  string pre_auth_failed_message = 9;
  // pre auth card number
  string pre_auth_card_number = 10;
  // in bspd
  bool in_bspd = 11;
  // pre auth finish time
  int64 pre_auth_finish_time = 12;
  // is capture
  bool is_capture = 13;
  // convenience fee
  double convenience_fee = 14;
  // tips amount
  double tips_amount = 15;
  // booking fee
  double booking_fee = 16;
  // is open
  bool is_open = 17;
}
