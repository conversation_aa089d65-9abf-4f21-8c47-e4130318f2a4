syntax = "proto3";

package moego.models.payment.v1;

import "moego/models/payment/v1/payer_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// payer
message PayerDef {
  // payer_type
  PayerType payer_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // payer_id
  int64 payer_id = 2;
}

// bill payer
message BillPayerSettingDef {
  // bill type
  BillType bill_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // payer
  PayerDef payer = 2;
}
