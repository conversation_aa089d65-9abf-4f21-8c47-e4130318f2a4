// @since 2024-01-24 17:19:54
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.payment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// The Pre Pay for Appointment
message PrePayCalendarView {
  // ticket id
  int64 ticket_id = 1;
  // pre pay amount
  double pre_pay_amount = 2;
  // pre pay status
  int32 pre_pay_status = 3;
  // pre pay rate
  double pre_pay_rate = 4;
}
