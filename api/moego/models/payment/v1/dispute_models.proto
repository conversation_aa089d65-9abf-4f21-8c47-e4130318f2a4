syntax = "proto3";

package moego.models.payment.v1;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "moego/models/payment/v2/common_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// dispute model
message DisputeFundingOperateModel {
  // dispute status
  enum Status {
    // unspecified
    STATUS_UNSPECIFIED = 0;
    // init
    INIT = 1;
    // succeed
    SUCCEED = 2;
  }
  // fund type
  enum FundType {
    // unspecified
    FUND_TYPE_UNSPECIFIED = 0;
    // 从商家那里扣钱
    WITHDRAWN = 1;
    // 把钱还给商家
    REINSTATED = 2;
  }
  // dispute fund operate id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // business_id
  int64 business_id = 3;
  // dispute id
  int64 dispute_id = 4;
  // channel dispute id
  string channel_dispute_id = 5;
  // channel type
  v2.ChannelType channel_type = 6;
  // payment id
  int64 payment_id = 7;
  // dispute amount
  google.type.Money dispute_amount = 8;
  // dispute fee
  google.type.Money dispute_fee = 9;
  // total amount = dispute amount + dispute fee
  google.type.Money total_amount = 10;
  // status
  Status status = 11;
  // fund type
  FundType fund_type = 12;
  // operate time
  google.protobuf.Timestamp operated_time = 13;
}

// dispute event log model
message DisputeEventLogModel {
  // dispute log status
  enum Status {
    // unspecified
    STATUS_UNSPECIFIED = 0;
    // init
    INIT = 1;
    // succeed
    SUCCEED = 2;
  }
}

// dispute model
message DisputeModel {
  // dispute status
  enum FundWithdrawStatus {
    // unspecified
    FUND_WITHDRAW_STATUS_UNSPECIFIED = 0;
    // init
    IS_WITHDRAWN = 1;
    // succeed
    NOT_WITHDRAWN = 2;
  }
}
