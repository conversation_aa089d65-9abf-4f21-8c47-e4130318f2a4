syntax = "proto3";

package moego.models.payment.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// payment method model, reference: https://stripe.com/docs/api/payment_methods/object
message PaymentMethodModel {
  // payment method id
  string id = 1;
  // payment method type, card, paypal etc
  string type = 2;
  // type
  oneof payment_type {
    option (validate.required) = true;
    // credit card
    Card card = 11;
  }
}

// 支付方式的扩展字段，不同的支付方式扩展的字段需求不同.
// 暂时抠出来不放在 PaymentMethodModel 里面，后面重构可以考虑嵌入.
message PaymentMethodExtra {
  // By payment method 定义的结构，不共用，不混用.
  oneof extra {
    // 重构前所有的支付方式的额外字段.
    Legacy legacy = 1;
  }

  // 重构前所有的支付方式的字段都混合放在 Legacy 中，便于与重构前的 Payment 结构对齐.
  message Legacy {
    // Card type, 实际是 card.brand.
    string card_type = 1;
    // Expiration month.
    int32 exp_month = 2;
    // Expiration year.
    int32 exp_year = 3;
    // Card number, 实际是 card.last4.
    string card_number = 4;
    // Signature.
    string signature = 5;
    // Stripe payment method.
    int32 stripe_payment_method = 6;
    // Card funding.
    string card_funding = 7;
    // Stripe client secret.
    string stripe_client_secret = 8;
    // Stripe charge ID.
    string stripe_charge_id = 9;
    // Stripe intent ID.
    string stripe_intent_id = 10;
    // Merchant.
    string merchant = 11;
    // Check number.
    string check_number = 12;
    // Device ID.
    string device_id = 13;
    // Square payment method.
    int32 square_payment_method = 14;
    // Square checkout ID.
    string square_checkout_id = 15;
    // Square customer ID.
    string square_customer_id = 16;
  }
}

// 退款方式的扩展字段，不同的退款方式的字段需求不同.
// 暂时抠出来不放在 PaymentMethodModel 里面，后面重构可以考虑嵌入.
message RefundPaymentMethodExtra {
  // By refund payment method 定义的结构，不共用，不混用.
  oneof extra {
    // 重构前所有的支付方式的额外字段.
    Legacy legacy = 1;
  }

  // 重构前所有的支付方式的字段都混合放在 Legacy 中，便于与重构前的 RefundPayment 结构对齐.
  message Legacy {
    // Stripe refund ID.
    string stripe_refund_id = 1;
    // Source payment id.
    // 目前是 Payment 中 stripe_intent_id.
    string source_payment_id = 2;
  }
}

// card model
message Card {
  // brand of the credit card, visa
  string brand = 1;
  // expiration month of the credit card
  int32 exp_month = 2;
  // expiration year of the credit card
  int32 exp_year = 3;
  // last 4 digits of the credit card
  string last4 = 4;
}

// payment method model, reference: https://stripe.com/docs/api/payment_methods/object
message PaymentMethodModelPublicView {
  // payment method type, card, paypal etc
  string type = 2;
  // type
  oneof payment_type {
    option (validate.required) = true;
    // credit card
    Card card = 11;
  }
}
