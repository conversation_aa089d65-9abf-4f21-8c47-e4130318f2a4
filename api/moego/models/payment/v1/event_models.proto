syntax = "proto3";

package moego.models.payment.v1;

import "moego/models/payment/v1/event_enums.proto";
import "moego/models/payment/v1/payout_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// payout event model
message PayoutEventModel {
  // 事件类型
  PayoutEventType event_type = 1;
  // payout 模型详情
  PayoutModel payout_model = 2;
}
