syntax = "proto3";

package moego.models.payment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// payout event type
enum PayoutEventType {
  // unspecified
  EVENT_TYPE_UNSPECIFIED = 0;
  // payout created
  PAYOUT_CREATED = 1;
  // payout paid
  PAYOUT_PAID = 2;
}
