// @since 2023-06-16 16:48:23
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.price_checker.v1;

import "moego/models/price_checker/v1/price_checker_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/price_checker/v1;pricecheckerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.price_checker.v1";

// price distribution
message PriceDistribution {
  // price distribution detail list
  repeated PriceDistributionDetail detail_list = 1;
  // average price
  int32 average_price = 2;
  // affordable price
  int32 affordable_price = 3;
  // upscale price
  int32 upscale_price = 4;
}

// price distribution detail
message PriceDistributionDetail {
  // price
  int32 price = 1;
  // amount
  int32 amount = 2;
}

// breed price distribution
message BreedPriceDistribution {
  // breed name
  string breed_name = 1;
  // price
  int32 price = 2;
}

// pet size info
message PetSizeInfo {
  // pet size
  PetSize pet_size = 1;
  // min size
  int32 min_size = 2;
  // max size
  int32 max_size = 3;
  // size description
  string size_description = 4;
}

// pet price distribution
message PetPriceDistribution {
  // service mode
  ServiceMode service_mode = 1;
  // pet size info
  PetSizeInfo pet_size_info = 2;
  // coat type
  CoatType coat_type = 3;
  // price distribution
  PriceDistribution price_distribution = 4;
}

// pet price distribution submit
message PetPriceDistributionSubmit {
  // service mode
  ServiceMode service_mode = 1 [(validate.rules).enum = {defined_only: true}];
  // pet size
  PetSize pet_size = 2 [(validate.rules).enum = {defined_only: true}];
  // coat type
  CoatType coat_type = 3 [(validate.rules).enum = {defined_only: true}];
  // price
  int32 price = 4 [(validate.rules).int32 = {gt: 0}];
}
