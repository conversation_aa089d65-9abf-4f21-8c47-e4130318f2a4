// @since 2023-05-27 21:28:41
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.admin_permission.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/admin_permission/v1;adminpermissionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.admin_permission.v1";

// The Role model
message RoleModel {
  // the unique id
  int64 id = 1;
  // the role name
  string name = 2;
  // the description
  string description = 3;
  // the owner id
  string owner_id = 4;

  // the operator id
  string operator_id = 12;
  // the create time
  google.protobuf.Timestamp created_at = 13;
  // the update time
  optional google.protobuf.Timestamp updated_at = 14;
  // the delete time
  optional google.protobuf.Timestamp deleted_at = 15;
}
