// @since 2023-05-27 21:29:19
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.admin_permission.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/admin_permission/v1;adminpermissionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.admin_permission.v1";

// role bindings
message RoleBindingsModel {
  // account id
  string account_id = 1;
  // role ids
  repeated int64 role_ids = 2;
}
