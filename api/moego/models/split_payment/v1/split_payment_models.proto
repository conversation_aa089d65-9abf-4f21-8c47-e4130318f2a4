syntax = "proto3";

package moego.models.split_payment.v1;

import "google/type/money.proto";
import "moego/models/split_payment/v1/split_payment_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/split_payment/v1;splitpaymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.split_payment.v1";

// split detail model
message SplitDetailModel {
  // channel account id
  optional string channel_account_id = 1;
  // moego account id
  optional string moego_account_id = 2;
  // account type
  moego.models.split_payment.v1.AccountType account_type = 3;
  // amount
  google.type.Money amount = 4;
  // split type (processing fee, tips ...)
  moego.models.split_payment.v1.SplitType split_type = 5;
  // reference, for Adyen
  optional string reference = 6;
  // description
  optional string description = 7;
  // 渠道分账ID
  optional string channel_split_id = 8;
  // 参与方tx ID
  optional string external_transaction_id = 9;
  // 参与方类型
  optional moego.models.split_payment.v1.ExternalType external_type = 10;
  // status
  string status = 11;
}

// split entity
message SplitEntity {
  // entity id
  int64 entity_id = 1 [(validate.rules).int64 = {gt: 0}];
  // entity type
  moego.models.split_payment.v1.SplitEntityType entity_type = 2;
}

// split detail event
message SplitDetailEventModel {
  // event Type
  enum EventType {
    // Unspecified
    EVENT_TYPE_UNSPECIFIED = 0;
    // 正向分账子单明细处理成功
    EVENT_TYPE_SPLIT_DETAIL_SUCCESS = 1;
    // 逆向分账子单明细处理成功
    EVENT_TYPE_SPLIT_REVERSE_DETAIL_SUCCESS = 2;
  }

  // 事件类型
  EventType event_type = 1;
  // 事件详情, 分账明细
  SplitDetailModel split_detail_model = 2;
}
