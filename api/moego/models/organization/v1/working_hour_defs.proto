syntax = "proto3";

package moego.models.organization.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// working hours
message WorkingHoursDef {
  // working hours for monday
  repeated TimeRangeDef monday = 1;
  // working hours for tuesday
  repeated TimeRangeDef tuesday = 2;
  // working hours for wednesday
  repeated TimeRangeDef wednesday = 3;
  // working hours for thursday
  repeated TimeRangeDef thursday = 4;
  // working hours for friday
  repeated TimeRangeDef friday = 5;
  // working hours for saturday
  repeated TimeRangeDef saturday = 6;
  // working hours for sunday
  repeated TimeRangeDef sunday = 7;
}

// time range of working hours
message TimeRangeDef {
  // start minute of working hours
  int32 start_time = 1 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // end minute of working hours
  int32 end_time = 2 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
}
