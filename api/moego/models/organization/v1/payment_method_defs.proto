syntax = "proto3";

package moego.models.organization.v1;

import "moego/models/organization/v1/payment_method_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// Definition of Payment method config
message PaymentMethodDef {
  // id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // payment method name
  string name = 3 [
    (validate.rules).string.min_len = 1,
    (validate.rules).string.max_len = 255
  ];
  // payment method sort value
  int32 sort = 4;
  // payment method type: 1-Default, 2-Customized
  PaymentMethodType type = 5 [(validate.rules).enum.defined_only = true];
  // payment method inactive status: 0-Active, 1-Inactive
  PaymentMethodInactive inactive = 6 [(validate.rules).enum.defined_only = true];
}

// Definition of Payment method list
message PaymentMethodListDef {
  // payment method list
  repeated PaymentMethodDef list = 1;
}
