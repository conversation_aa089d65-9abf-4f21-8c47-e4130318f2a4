syntax = "proto3";

package moego.models.organization.v1;

import "moego/models/organization/v1/company_enums.proto";
import "moego/models/organization/v1/time_zone.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// company preference setting definition
message UpdateCompanyPreferenceSettingDef {
  // currency code
  string currency_code = 1 [(validate.rules).string.len = 3];
  // currency symbol
  string currency_symbol = 2 [(validate.rules).string.min_len = 1];
  // date format
  DateFormat date_format_type = 3;
  // time format
  TimeFormat time_format_type = 4;
  // unit of weight
  WeightUnit unit_of_weight_type = 5;
  // unit of distance
  DistanceUnit unit_of_distance_type = 6;
  // whether the notification sound is on
  bool notification_sound_enable = 7;
  // timezone
  TimeZone time_zone = 8;
}

// company extra info
message CompanyExtraInfoDef {
  // unread notification count
  int32 unread_notification_count = 1;
}
