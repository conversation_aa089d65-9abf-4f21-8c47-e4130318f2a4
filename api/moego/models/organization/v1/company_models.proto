syntax = "proto3";

package moego.models.organization.v1;

import "moego/models/organization/v1/company_enums.proto";
import "moego/models/organization/v1/country_defs.proto";
import "moego/models/organization/v1/time_zone.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// company
message CompanyModel {
  // id
  int64 id = 1;
  // name
  string name = 2;
  // owner account id
  int64 account_id = 3;
  // country
  string country = 4;
  // create time
  int64 create_time = 5;
  // update time
  int64 update_time = 6;
  // location num
  int32 location_num = 7;
  // staff num
  int32 staff_num = 8;
  // vans num
  int32 vans_num = 9;
  // level
  int32 level = 10;
  // is new pricing
  int32 is_new_pricing = 11;
  // enable square
  int32 enable_square = 12;
  // enable stripe reader
  int32 enable_stripe_reader = 13;
  // enterprise id
  int64 enterprise_id = 14;
  // buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
  // company type
  enum CompanyType {
    // Mobile Grooming
    MOBILE = 0;
    // Salon
    SALON = 1;
    // Mobile & Salon
    HYBRID = 2;
  }
  // company type
  CompanyType company_type = 15;
}

// company model brief view
message CompanyBriefView {
  // id
  int64 id = 1;
  // name
  string name = 2;
  // owner account id
  int64 account_id = 3;
  // enterprise_id
  int64 enterprise_id = 4;
}

// company preference setting model
message CompanyPreferenceSettingModel {
  // currency code
  string currency_code = 1;
  // currency symbol
  string currency_symbol = 2;
  // date format
  DateFormat date_format_type = 3;
  // time format
  TimeFormat time_format_type = 4;
  // unit of weight
  WeightUnit unit_of_weight_type = 5;
  // unit of distance
  DistanceUnit unit_of_distance_type = 6;
  // whether the notification sound is on
  bool notification_sound_enable = 7;
  // country
  models.organization.v1.CountryDef country = 8;
  // timezone
  models.organization.v1.TimeZone time_zone = 9;
  // logo path
  string logo_path = 10;
  // theme color
  string theme_color = 11;
}
