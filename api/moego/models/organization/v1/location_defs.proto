syntax = "proto3";

package moego.models.organization.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/organization/v1/address_defs.proto";
import "moego/models/organization/v1/location_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// create location definition
message CreateLocationDef {
  // name of the location
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // business type
  BusinessType business_type = 2 [(validate.rules).enum = {defined_only: true}];
  // contact email
  string contact_email = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // address
  models.organization.v1.AddressDef address = 4 [(validate.rules).message = {required: true}];
  // website
  optional string website = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // source from
  BusinessSourceFromType source_from = 6 [(validate.rules).enum = {defined_only: true}];
}

// update location definition
message UpdateLocationDef {
  // id of the location
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // name of the location
  optional string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // business type
  optional BusinessType business_type = 3 [(validate.rules).enum = {defined_only: true}];
  // contact email
  optional string contact_email = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // phone number
  optional string contact_phone_number = 5 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
  // address
  optional models.organization.v1.AddressDef address = 6;
  // avatar path
  optional string avatar_path = 7 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
}

// update online preference definition
message UpdateOnlinePreferenceDef {
  // id of the location
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // website
  optional string website = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // facebook link
  optional string facebook_link = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 1000
  }];
  // instagram link
  optional string instagram_link = 4 [(validate.rules).string = {
    min_len: 0
    max_len: 1000
  }];
  // google link
  optional string google_link = 5 [(validate.rules).string = {
    min_len: 0
    max_len: 1000
  }];
  // yelp link
  optional string yelp_link = 6 [(validate.rules).string = {
    min_len: 0
    max_len: 1000
  }];
  // tiktok link
  optional string tiktok_link = 7 [(validate.rules).string = {
    min_len: 0
    max_len: 1000
  }];
}

// location date time def
message LocationDateTimeDef {
  // company id
  int64 company_id = 1;
  // business id
  int64 business_id = 2;
  // local date time
  google.protobuf.Timestamp local_date_time = 3;
  // current date
  string current_date = 4;
  // current minutes
  int32 current_minutes = 5;
  // timezone name
  string timezone_name = 6;
}
