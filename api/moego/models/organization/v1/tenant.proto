// @since 2022-05-30 17:46:23
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.organization.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// tenant
message Tenant {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // business id, optional
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}
