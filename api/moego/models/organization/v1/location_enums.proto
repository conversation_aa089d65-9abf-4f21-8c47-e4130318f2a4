syntax = "proto3";

package moego.models.organization.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// Business type
enum BusinessType {
  // Mobile Grooming
  MOBILE = 0;
  // Salon
  SALON = 1;
  // Mobile & Salon
  HYBRID = 2;
}

// Business source from type
enum BusinessSourceFromType {
  // UNSPECIFIED
  BUSINESS_SOURCE_FROM_UNSPECIFIED = 0;
  // APP_ANDROID
  APP_ANDROID = 1;
  // APP_IOS
  APP_IOS = 2;
  // WEB_ANDROID
  WEB_ANDROID = 3;
  // WEB_IOS
  WEB_IOS = 4;
  // WEB_DESKTOP
  WEB_DESKTOP = 5;
  // enterprise_tenant
  ENTERPRISE_TENANT = 6;
}

// Source Type (How to know us)
enum SourceType {
  // UNSPECIFIED
  SOURCE_TYPE_UNSPECIFIED = 0;
  // Other
  OTHER = 1;
  // FACEBOOK
  FACEBOOK = 2;
  // INTERNET_SEARCH
  INTERNET_SEARCH = 3;
  // CAPTERRA
  CAPTERRA = 4;
  // SQUARE
  SQUARE = 5;
  // Expo
  EXPO = 6;
}
