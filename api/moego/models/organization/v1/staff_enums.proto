syntax = "proto3";

package moego.models.organization.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// staff employee category enum
enum StaffEmployeeCategory {
  // company staff
  COMPANY_STAFF = 0;
  // company owner
  COMPANY_OWNER = 1;
  // enterprise owner
  ENTERPRISE_OWNER = 2;
  // enterprise staff
  ENTERPRISE_STAFF = 3;
}

// staff invite link send method type enum
enum StaffInviteLinkSendMethodType {
  // send invite link unspecified
  STAFF_INVITE_LINK_SEND_METHOD_TYPE_UNSPECIFIED = 0;
  // send invite link via sms
  SMS = 1;
  // send invite link via email
  EMAIL = 2;
}

// staff link status enum
enum StaffLinkStatus {
  // staff link status unspecified
  STAFF_LINK_STATUS_UNSPECIFIED = 0;
  // staff link status unlinked
  UNLINKED = 1;
  // staff link status pending
  PENDING = 2;
  // staff link status linked
  LINKED = 3;
}

// staff type
enum StaffSource {
  // staff source unspecified, default value
  STAFF_SOURCE_UNSPECIFIED = 0;
  // created by system
  STAFF_SOURCE_SYSTEM = 1;
}

// staff login limit type
enum StaffLoginLimitType {
  // unspecified
  LOGIN_LIMIT_TYPE_UNSPECIFIED = 0;
  // no limit
  NO_LIMIT = 1;
  // time range in a day
  TIME_RANGE_IN_ONE_DAY = 2;
}
