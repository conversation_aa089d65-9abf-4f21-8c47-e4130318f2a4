syntax = "proto3";

package moego.models.organization.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// PaymentMethod status
enum PaymentMethodStatus {
  // Payment method status unspecified
  PAYMENT_METHOD_STATUS_UNSPECIFIED = 0;
  // Payment method status normal
  PAYMENT_METHOD_STATUS_NORMAL = 1;
  // Payment method status deleted
  PAYMENT_METHOD_STATUS_DELETED = 2;
}

// PaymentMethod type
enum PaymentMethodType {
  // Unspecified payment method type
  PAYMENT_METHOD_TYPE_UNSPECIFIED = 0;
  // Default payment method, which cannot be deleted
  DEFAULT = 1;
  // Customized payment method, created by user, can be deleted
  CUSTOMIZED = 2;
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// PaymentMethod inactive status
enum PaymentMethodInactive {
  // Payment method is active
  PAYMENT_METHOD_ACTIVE = 0;
  // Payment method is inactive
  PAYMENT_METHOD_INACTIVE = 1;
}
