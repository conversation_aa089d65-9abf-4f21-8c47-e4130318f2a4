syntax = "proto3";

package moego.models.organization.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// EnterpriseModel
message EnterpriseModel {
  option deprecated = true;
  // buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
  // source
  enum Source {
    // normally add
    NORMALLY_ADD = 0;
    // manually add
    MANUALLY_ADD = 1;
    // split company
    SPLIT_COMPANY = 2;
  }
  // id
  int64 id = 1;
  // name
  string name = 2;
  // account_id
  int64 account_id = 3;
  // email
  string email = 4;
  // source
  Source source = 5;
  // created at
  google.protobuf.Timestamp created_at = 10;
  // updated at
  optional google.protobuf.Timestamp updated_at = 11;
}
