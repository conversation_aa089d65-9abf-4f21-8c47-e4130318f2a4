syntax = "proto3";

package moego.models.organization.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// company tier class
enum TierEnums {
  // UNSPECIFIED
  TIER_UNSPECIFIED = 0;
  // tier 1
  TIER_1 = 1;
  // tier 2
  TIER_2 = 2;
  // tier 3
  TIER_3 = 3;
  // tier 4
  TIER_4 = 4;
}
