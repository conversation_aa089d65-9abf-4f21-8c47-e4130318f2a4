syntax = "proto3";

package moego.models.organization.v1;

import "google/protobuf/timestamp.proto";
import "google/type/latlng.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// staff tracking model
message StaffTrackingModel {
  // record id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // staff id
  int64 staff_id = 3;
  // staff id
  string device_id = 4;
  // lat lng
  google.type.LatLng coordinate = 5;
  //create time
  google.protobuf.Timestamp created_at = 6;
}
