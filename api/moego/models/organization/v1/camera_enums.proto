syntax = "proto3";

package moego.models.organization.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// Camera Type
enum CameraConfigType {
  // Unspecified
  CAMERA_TYPE_UNSPECIFIED = 0;
  // idogcam
  IDOGCAM = 1;
  // abckam
  ABCKAM = 2;
}

// Third-party data origin status
enum OriginStatus {
  // Unspecified
  ORIGIN_STATUS_UNSPECIFIED = 0;
  // valid
  VALID = 1;
  // Not Found
  NOT_FOUND = 2;
}

//  Visibility Type
enum VisibilityType {
  // Unspecified
  VISIBILITY_TYPE_UNSPECIFIED = 0;
  // Public
  PUBLIC = 1;
  // Private
  PRIVATE = 2;
}
