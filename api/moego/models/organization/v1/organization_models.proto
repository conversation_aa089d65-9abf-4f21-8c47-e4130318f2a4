syntax = "proto3";

package moego.models.organization.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// Type
enum OrganizationType {
  // unspecified
  TYPE_UNSPECIFIED = 0;
  // business
  BUSINESS = 1;
  // company
  COMPANY = 2;
  // enterprise
  ENTERPRISE = 3;
}
