syntax = "proto3";

package moego.models.organization.v1;

import "google/type/dayofweek.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// AvailabilityType staff availability 配置
enum AvailabilityType {
  // availability type
  AVAILABILITY_TYPE_UNSPECIFIED = 0;
  // availability type
  BY_TIME = 1;
  // availability type
  BY_SLOT = 2;
}

// ScheduleType
enum ScheduleType {
  // schedule type unspecified
  SCHEDULE_TYPE_UNSPECIFIED = 0;
  // Every one week
  ONE_WEEK = 1;
  // Every two weeks
  TWO_WEEK = 2;
  // Every three weeks
  THREE_WEEK = 3;
  // Every four weeks
  FOUR_WEEK = 4;
}

// limit type
enum LimitType {
  // limit type unspecified
  LIMIT_TYPE_UNSPECIFIED = 0;
  // limit by service
  SERVICE_LIMIT = 1;
  // limit by size
  PET_SIZE_LIMIT = 2;
  // limit by breed
  PET_BREED_LIMIT = 3;
}

// staff availability
message StaffAvailability {
  // company id/business id不需要
  // staff id
  int64 staff_id = 1;
  // is available
  bool is_available = 2;
  // schedule type
  ScheduleType schedule_type = 3;
  // slot daily setting
  repeated SlotAvailabilityDay slot_availability_day_list = 4;
  // time daily setting
  repeated TimeAvailabilityDay time_availability_day_list = 5;
  // slot start sunday
  string slot_start_sunday = 6;
  // time start sunday
  string time_start_sunday = 7;
  // time schedule type
  ScheduleType time_schedule_type = 8;
}

// slot availability day
message SlotAvailabilityDay {
  // day of week
  google.type.DayOfWeek day_of_week = 1;
  // is available
  bool is_available = 2;
  // schedule type
  ScheduleType schedule_type = 3;
  // slot daily setting
  SlotDailySetting slot_daily_setting = 4;
  // staff available hour
  repeated SlotHourSetting slot_hour_setting_list = 5;
  // override date
  string override_date = 6;
  // staff id
  int64 staff_id = 7;
}

// time availability day
message TimeAvailabilityDay {
  // day of week
  google.type.DayOfWeek day_of_week = 1;
  // is available
  bool is_available = 2;
  // schedule type
  ScheduleType schedule_type = 3;
  // time daily setting
  TimeDailySetting time_daily_setting = 5;
  // time available hour
  repeated TimeHourSetting time_hour_setting_list = 10;
  // override date
  string override_date = 6;
  // staff id
  int64 staff_id = 7;
}

// by slot daily setting def
message SlotDailySetting {
  // slot start time
  int32 start_time = 1;
  // slot end time
  int32 end_time = 2;
  // slot capacity
  int32 capacity = 3;
  // booking limit
  BookingLimitation limit = 4;
  // booking limit group
  repeated LimitationGroup limitation_groups = 5;
}

// by time daily setting def
message TimeDailySetting {
  // booking limit
  BookingLimitation limit = 1;
  // booking limit group
  repeated LimitationGroup limitation_groups = 5;
}

// by slot hour setting def
message SlotHourSetting {
  // slot start time
  int32 start_time = 1;
  // slot capacity
  int32 capacity = 2;
  // booking limit
  BookingLimitation limit = 3;
  // end time
  int32 end_time = 4;
  // booking limit group
  repeated LimitationGroup limitation_groups = 5;
}

// by time hour setting def
message TimeHourSetting {
  // start time
  int32 start_time = 1;
  // end time
  int32 end_time = 2;
}

// slot/time limit setting
message BookingLimitation {
  // service limits
  repeated ServiceLimitation service_limits = 1;
  // pet size limits
  repeated PetSizeLimitation pet_size_limits = 2;
  // pet breed limits
  repeated PetBreedLimitation pet_breed_limits = 3;

  // service limitation
  message ServiceLimitation {
    // service_id
    repeated int64 service_ids = 1;
    // is_all_service
    bool is_all_service = 2;
    // capacity
    int32 capacity = 3;
  }

  // pet size limitation
  message PetSizeLimitation {
    // pet_size_id
    repeated int64 pet_size_ids = 1;
    // is_all_size
    bool is_all_size = 2;
    // capacity
    int32 capacity = 3;
  }

  // pet breed limitation
  message PetBreedLimitation {
    // pet_type_id
    int64 pet_type_id = 1;
    // is_all_breed
    bool is_all_breed = 2;
    // breed_ids
    repeated int64 breed_ids = 3;
    // capacity
    int32 capacity = 4;
  }
}

// limitation group
message LimitationGroup {
  // service limits
  repeated ServiceLimitation service_limits = 1;
  // pet size limits
  repeated PetSizeLimitation pet_size_limits = 2;
  // pet breed limits
  repeated PetBreedLimitation pet_breed_limits = 3;
  // only accept selected
  bool only_accept_selected = 4;
}

// service limitation
message ServiceLimitation {
  // service_id
  repeated int64 service_ids = 1;
  // is_all_service
  bool is_all_service = 2;
  // capacity
  int32 capacity = 3;
}

// pet size limitation
message PetSizeLimitation {
  // pet_size_id
  repeated int64 pet_size_ids = 1;
  // is_all_size
  bool is_all_size = 2;
  // capacity
  int32 capacity = 3;
}

// pet breed limitation
message PetBreedLimitation {
  // pet_type_id
  int64 pet_type_id = 1;
  // is_all_breed
  bool is_all_breed = 2;
  // breed_ids
  repeated int64 breed_ids = 3;
  // capacity
  int32 capacity = 4;
}
