syntax = "proto3";

package moego.models.grooming.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/grooming/v1/review_booster_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1;groomingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.grooming.v1";

// the review booster record model
message ReviewBoosterRecordModel {
  // review booster record id
  int64 id = 1;
  // review booster id
  int64 review_booster_id = 2;
  // business id
  int64 business_id = 3;
  // customer id
  int64 customer_id = 4;
  // positive score
  int32 positive_score = 5;
  // appointment id
  int64 appointment_id = 6;
  // appointment date
  string appointment_date = 7;
  // review content
  string review_content = 8;
  // review time
  google.protobuf.Timestamp review_time = 9;
  // source
  ReviewBoosterRecordSource source = 10;
  // staff ids
  repeated int64 staff_ids = 11;
  // pet ids
  repeated int64 pet_ids = 12;
  // create time
  google.protobuf.Timestamp create_time = 13;
  // update time
  google.protobuf.Timestamp update_time = 14;
}

// review booster record view for client app view
message ReviewBoosterRecordModelClientView {
  // review booster record id
  int64 id = 1;
  // positive score
  int32 positive_score = 2;
  // review time
  google.protobuf.Timestamp review_time = 3;
  // source
  ReviewBoosterRecordSource source = 4;
  // appointment id
  string appointment_id = 5;
}
