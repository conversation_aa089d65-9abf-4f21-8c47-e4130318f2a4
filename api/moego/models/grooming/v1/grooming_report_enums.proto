syntax = "proto3";

package moego.models.grooming.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1;groomingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.grooming.v1";

// grooming report status
enum GroomingReportStatus {
  // unspecified
  GROOMING_REPORT_STATUS_UNSPECIFIED = 0;
  // created
  GROOMING_REPORT_STATUS_CREATED = 1;
  // draft
  GROOMING_REPORT_STATUS_DRAFT = 2;
  // ready
  GROOMING_REPORT_STATUS_READY = 3;
  // sent
  GROOMING_REPORT_STATUS_SENT = 4;
}

// grooming report send method
enum GroomingReportSendMethod {
  // unspecified
  GROOMING_REPORT_SEND_METHOD_UNSPECIFIED = 0;
  // send by sms
  GROOMING_REPORT_SEND_BY_SMS = 1;
  // send by email
  GROOMING_REPORT_SEND_BY_EMAIL = 2;
}
