syntax = "proto3";

package moego.models.grooming.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1;groomingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.grooming.v1";

// service type
enum ServiceType {
  // unspecified
  SERVICE_TYPE_UNSPECIFIED = 0;
  // main service
  SERVICE_TYPE_SERVICE = 1;
  // add ons
  SERVICE_TYPE_ADD_ONS = 2;
}

// service scope type
enum ServiceScopeType {
  // unspecified
  SERVICE_SCOPE_TYPE_UNSPECIFIED = 0;
  // only this appt
  SERVICE_SCOPE_TYPE_THIS = 1;
  // this appt and future appts
  SERVICE_SCOPE_TYPE_THIS_AND_FUTURE = 2;
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// service in ob show base price type
enum ShowBasePrice {
  // do not show base price
  SHOW_BASE_PRICE_NO = 0;
  // show fixed price
  SHOW_BASE_PRICE_FIXED = 1;
  // show price with "Starting at"
  SHOW_BASE_PRICE_STARTING_AT = 2;
  // show price as varies
  SHOW_BASE_PRICE_VARIES = 3;
}

// service custom type
enum ServiceCustomType {
  // unspecified
  SERVICE_CUSTOM_TYPE_UNSPECIFIED = 0;
  // custom service price
  SERVICE_CUSTOM_TYPE_PRICE = 1;
  // custom service duration
  SERVICE_CUSTOM_TYPE_DURATION = 2;
}
