syntax = "proto3";

package moego.models.grooming.v1;

import "moego/models/grooming/v1/appointment_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1;groomingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.grooming.v1";

// appointment list sort definition
message AppointmentSortDef {
  // sort field
  AppointmentSortField field = 1;
  // sort asc or desc
  bool asc = 2;
}
