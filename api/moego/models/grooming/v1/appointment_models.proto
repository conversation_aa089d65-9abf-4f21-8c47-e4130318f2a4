syntax = "proto3";

package moego.models.grooming.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/grooming/v1/appointment_enums.proto";
import "moego/models/grooming/v1/pet_detail_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1;groomingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.grooming.v1";

// the appointment model
message AppointmentModel {
  // appointment id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // customer id
  int64 customer_id = 3;
  // appointment date, in yyyy-MM-dd format
  string appointment_date = 4;
  // appointment start time, in minutes
  int32 appointment_start_time = 5;
  // appointment end time, in minutes
  int32 appointment_end_time = 6;
  // appointment source
  AppointmentSource source = 7;
  // appointment status
  AppointmentStatus status = 8;
  // payment status
  AppointmentPaymentStatus is_paid = 9;
  // create time
  google.protobuf.Timestamp create_time = 10;
  // update time
  google.protobuf.Timestamp update_time = 11;
  // no start time
  bool no_start_time = 12;
  // is waiting list
  bool is_waiting_list = 13;
  // move waiting list staff id
  int64 move_waiting_by = 14;
  // confirmed time
  google.protobuf.Timestamp confirmed_time = 15;
  // check in time
  google.protobuf.Timestamp check_in_time = 16;
  // check out time
  google.protobuf.Timestamp check_out_time = 17;
  // canceled time
  google.protobuf.Timestamp canceled_time = 18;
  // is blocked time
  bool is_block = 19;
  // booking request status, true is booking request, false is booking
  bool book_online_status = 20;
  // customer address id
  int64 customer_address_id = 21;
  // repeat appointment id
  int64 repeat_id = 22;
  // color code
  string color_code = 23;

  // appointment pet details
  repeated PetDetailModel pet_details = 40;
}

// appointment view for c app list
message AppointmentModelClientListView {
  // appointment id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // customer id
  int64 customer_id = 3;
  // appointment date, in yyyy-MM-dd format
  string appointment_date = 4;
  // appointment start time, in minutes
  int32 appointment_start_time = 5;
  // appointment end time, in minutes
  int32 appointment_end_time = 6;
  // no start time
  bool no_start_time = 7;
}

// appointment detail view for client app detail view
message AppointmentModelClientView {
  // appointment id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // customer id
  int64 customer_id = 3;
  // appointment date, in yyyy-MM-dd format
  string appointment_date = 4;
  // appointment start time, in minutes
  int32 appointment_start_time = 5;
  // appointment end time, in minutes
  int32 appointment_end_time = 6;
  // appointment status
  AppointmentStatus status = 7;
  // payment status
  AppointmentPaymentStatus payment_status = 8;
  // no start time
  bool no_start_time = 9;
  // booking request status, true is booking request, false is booking
  bool book_online_status = 10;
}
