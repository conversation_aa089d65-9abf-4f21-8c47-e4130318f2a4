// @since 2023-06-30 19:39:51
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.llm.v1;

import "moego/models/llm/v1/conversation_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/llm/v1;llmpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.llm.v1";

// the conversation message def
message ConversationMessageDef {
  // the role
  ConversationRole role = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // the content
  string content = 2 [(validate.rules).string = {max_len: 65535}];
}
