// @since 2023-07-02 15:02:33
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.llm.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/llm/v1;llmpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.llm.v1";

// usage def
message UsageDef {
  // input tokens
  int32 input_tokens = 1 [(validate.rules).int32 = {gte: 0}];
  // output tokens
  int32 output_tokens = 2 [(validate.rules).int32 = {gte: 0}];
  // total tokens
  int32 total_tokens = 3 [(validate.rules).int32 = {gte: 0}];
  // input unit price
  double input_unit_price = 4;
  // output unit price
  double output_unit_price = 5;
  // input amount
  double input_amount = 6;
  // output amount
  double output_amount = 7;
  // total amount
  double total_amount = 8;
}
