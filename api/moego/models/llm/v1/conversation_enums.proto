// @since 2023-06-30 19:38:00
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.llm.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/llm/v1;llmpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.llm.v1";

// the conversation role
enum ConversationRole {
  // the conversation role is unknown
  CONVERSATION_ROLE_UNSPECIFIED = 0;
  // system
  CONVERSATION_ROLE_SYSTEM = 1;
  // user
  CONVERSATION_ROLE_USER = 2;
  // ai
  CONVERSATION_ROLE_AI = 3;
}
