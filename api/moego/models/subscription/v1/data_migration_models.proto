syntax = "proto3";

package moego.models.subscription.v1;

import "google/protobuf/timestamp.proto";
import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/models/subscription/v1/subscription_models.proto";
import "moego/utils/v1/time_period.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/subscription/v1;subscriptionmodpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.subscription.v1";

// product data
message ProductData {
  // id 导入时不需要填写仅作为返回值，更新时需要填写，删除时填负数
  int64 id = 1;
  // mid 虚拟 ID，用于做关联关系绑定
  string mid = 2;
  // name
  string name = 3;
  // description
  string description = 4;
  // type
  Product.Type type = 5;
  // seller
  models.subscription.v1.User seller = 6;
  // purchase limit
  PurchaseLimit purchase_limit = 7;
  // business type
  Product.BusinessType business_type = 8;
}

// price data
message PriceData {
  // id 导入时不需要填写仅作为返回值，更新时需要填写，删除时填负数
  int64 id = 1;
  // mid 虚拟 ID，用于做关联关系绑定
  string mid = 2;
  // product id
  int64 product_id = 3;
  // name
  string name = 4;
  // type
  Price.Type type = 5;
  // unit_amount
  google.type.Money unit_amount = 6;
  // billing cycle
  moego.utils.v1.TimePeriod billing_cycle = 7;
}

// feature data
message FeatureData {
  // id 导入时不需要填写仅作为返回值，更新时需要填写，删除时填负数
  int64 id = 1;
  // mid 虚拟 ID，用于做关联关系绑定
  string mid = 2;
  // product id
  int64 product_id = 3;
  // name
  string name = 4;
  // description
  string description = 5;
  // 特性 key
  models.subscription.v1.Feature.Key key = 6;
  // 特性设置
  models.subscription.v1.Feature.Setting setting = 7;
}

// subscription data
message SubscriptionData {
  // id 导入时不需要填写仅作为返回值，更新时需要填写，删除时填负数
  int64 id = 1;
  // mid 虚拟 ID，用于做关联关系绑定
  string mid = 2;
  // buyer
  models.subscription.v1.User buyer = 3;
  // seller
  models.subscription.v1.User seller = 4;
  // price id
  int64 price_id = 5;
  // tax id
  int64 tax_id = 6;
  // validity period
  google.type.Interval validity_period = 7;
  // auto resume at
  google.protobuf.Timestamp auto_resume_at = 8;
}

// entitlement data
message EntitlementData {
  // id 导入时不需要填写仅作为返回值，更新时需要填写，删除时填负数
  int64 id = 1;
  // mid 虚拟 ID，用于做关联关系绑定
  string mid = 2;
  // subscription id
  int64 subscription_id = 3;
  // feature id
  int64 feature_id = 4;
  // setting
  models.subscription.v1.Feature.Setting setting = 5;
}
