syntax = "proto3";

package moego.models.business_customer.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/dayofweek.proto";
import "moego/utils/v1/time_of_day_interval.proto";
import "moego/utils/v1/time_period.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// the customer model
// 这个模型弃用, 里面的字段太乱了, 没有设计好. 请使用 BusinessCustomerInfoModel
message BusinessCustomerModel {
  option deprecated = true;

  // customer id
  int64 id = 1;
  // company id
  int64 company_id = 6;
  // preferred business id
  int64 preferred_business_id = 5;
  // business id, please use preferred_business_id instead
  int64 business_id = 2 [deprecated = true];
  // customer code
  string customer_code = 7;
  // account id, 0 means no binding account
  int64 account_id = 8;
  // external id
  string external_id = 9;

  // first name
  string first_name = 3;
  // last name
  string last_name = 4;
  // avatar path
  string avatar_path = 10;
  // email
  string email = 11;
  // phone number
  // this is the identifier for the customer,
  // but it may not be the primary phone number
  string phone_number = 12;
  // client color
  string client_color = 13;
  // referral source id
  int64 referral_source_id = 21;
  // source
  string source = 22;

  // inactive
  bool inactive = 23;

  // if the customer is deleted
  bool deleted = 24;

  // created at
  google.protobuf.Timestamp created_at = 25;
  // updated at
  google.protobuf.Timestamp updated_at = 26;

  // created by (staff id)
  int64 created_by = 27;
  // updated by (staff id)
  int64 updated_by = 28;

  /**
   * settings of switch
   */

  // is block online booking
  bool is_block_online_booking = 14;
  // is block message
  bool is_block_message = 15;
  // send auto email
  bool send_auto_email = 16;
  // send auto message
  bool send_auto_message = 17;
  // unconfirmed reminder by (this field need to be redesigned)
  int32 unconfirmed_reminder_by = 18;
  // is unsubscribed
  bool is_unsubscribed = 19;
  // is recurring
  bool is_recurring = 20;

  /**
   * settings of preference
   */
  // preferred groomer id
  int64 preferred_groomer_id = 30;
  // preferred grooming frequency
  moego.utils.v1.TimePeriod preferred_grooming_frequency = 31;
  // preferred day of week
  repeated google.type.DayOfWeek preferred_day_of_week = 32;
  // preferred time of day
  moego.utils.v1.TimeOfDayInterval preferred_time_of_day = 33;

  /**
   * settings of sharing appointment, need to be redesigned
   */
  // share appt status
  int32 share_appt_status = 40;
  // share range type
  int32 share_range_type = 41;
  // share range value
  int32 share_range_value = 42;
  // share appt ids
  repeated int64 share_appt_ids = 43;

  // last service time, not precise and may not exist, may be removed in the future
  optional google.type.Date last_service_time = 44;
  // send app auto message
  bool send_app_auto_message = 45;
  // birthday
  google.protobuf.Timestamp birthday = 46;
  // referral source desc
  string referral_source_desc = 47;

  // CRM leads 字段
  string customer_type = 48;
}

// info model for business customer
// 注意, 如果是业务相关的 preference 字段, 请优先考虑加到对应的 preference model 里,
// 主要是考虑到 preference model 可能在以后会收拢到对应的业务域中.
message BusinessCustomerInfoModel {
  // customer id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // preferred business id
  int64 preferred_business_id = 3;
  // source
  Source source = 4;

  // avatar path
  string avatar_path = 5;
  // first name
  string first_name = 6;
  // last name
  string last_name = 7;
  // email
  string email = 8;
  // phone number
  // this is the identifier for the customer,
  // but it may not be the primary phone number
  string phone_number = 9;
  // client color
  string client_color = 10;

  // customer code
  string customer_code = 11;
  // account id, 0 means no binding account
  int64 account_id = 12;

  // referral source id
  int64 referral_source_id = 13;

  // customer tag ids
  repeated int64 tag_ids = 14;

  // inactive
  bool inactive = 15;
  // if the customer is deleted
  bool deleted = 16;

  // source of business customer
  enum Source {
    // unspecified
    SOURCE_UNSPECIFIED = 0;

    // 存储值为 "unknown", 未知来源
    UNKNOWN = 1;

    // 存储值为 "0", 历史数据, 含义不明
    SOURCE_ZERO = 2 [deprecated = true];
    // 存储值为 "1", 历史数据, 含义不明
    SOURCE_ONE = 3 [deprecated = true];
    // 存储值为 "2", 历史数据, 含义不明
    SOURCE_TWO = 4 [deprecated = true];
    // 存储值为 "3", 历史数据, 含义不明
    SOURCE_THREE = 5 [deprecated = true];
    // 存储值为 "4", 历史数据, 含义不明
    SOURCE_FOUR = 6 [deprecated = true];
    // 存储值为 "5", 历史数据, 含义不明
    SOURCE_FIVE = 7 [deprecated = true];

    // 存储值为 "manual", 表示 B 端手动创建
    MANUAL_CREATE = 8;
    // 存储值为 "contacts", 表示 B 端通过 contacts app 导入
    SELF_IMPORT = 9;
    // 存储值为 "dm", 表示 customer support 通过 data import 脚本导入
    DATA_IMPORT = 10;
    // 存储值为 "ob", 表示通过 online booking 创建
    ONLINE_BOOKING = 11;
    // 存储值为 "if", 表示通过 intake form 创建
    INTAKE_FORM = 12;
    // 存储值为 "call", 表示通过电话联系创建
    CALL_IN = 13;
    // 存储值为 "text", 表示通过短信联系创建
    TEXT_IN = 14;
    // 存储值为 "branded", 表示通过 branded app 创建
    BRANDED_APP = 15;
  }
}

// event bus model for business customer
message BusinessCustomerEventModel {
  // operation
  Operation operation = 1;

  // customer id
  int64 id = 2;

  // TODO: add more fields if needed

  // operation
  enum Operation {
    // unspecified
    OPERATION_UNSPECIFIED = 0;
    // create
    CREATE = 1;
    // TODO: update and delete
  }
}

// the customer model
message BusinessCustomerModelPublicView {
  // customer id
  int64 id = 1;
  // company id
  int64 company_id = 6;

  // first name
  string first_name = 3;
  // last name
  string last_name = 4;
  // avatar path
  string avatar_path = 10;
  // email
  string email = 11;
  // phone number
  // this is the identifier for the customer,
  // but it may not be the primary phone number
  string phone_number = 12;
}

// business customer view for c app client view
message BusinessCustomerModelClientView {
  // is block online booking
  bool is_block_online_booking = 1;
  // is block message
  bool is_block_message = 2;
  // the first name
  string first_name = 3;
  // the last name
  string last_name = 4;
}

// business customer view for c app link view
message BusinessCustomerModelLinkView {
  // the first name
  string first_name = 3;
  // the last name
  string last_name = 4;
}

// business customer view for name status
message BusinessCustomerNameStatusView {
  // customer id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // first name
  string first_name = 3;
  // last name
  string last_name = 4;
  // if the customer is deleted
  bool deleted = 5;
}

// business customer name view
message BusinessCustomerModelNameView {
  // customer id
  int64 id = 1;
  // the first name
  string first_name = 3;
  // the last name
  string last_name = 4;
}

// business customer id view
message BusinessCustomerModelIdView {
  // customer id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // preferred business id
  int64 preferred_business_id = 3;
}

// business customer view for appointment view
message BusinessCustomerCalendarView {
  // customer id
  int64 id = 1;
  // first name
  string first_name = 2;
  // last name
  string last_name = 3;
  // avatar path
  string avatar_path = 4;
  // email
  string email = 11;
  // phone number
  // this is the identifier for the customer,
  // but it may not be the primary phone number
  string phone_number = 12;
  // client color
  string client_color = 13;
  // referral source id
  int64 referral_source_id = 21;
  // source
  string source = 22;

  // inactive
  bool inactive = 23;

  // if the customer is deleted
  bool deleted = 24;

  // is block online booking
  bool is_block_online_booking = 14;
  // is block message
  bool is_block_message = 15;
  // send auto email
  bool send_auto_email = 16;
  // send auto message
  bool send_auto_message = 17;
  // unconfirmed reminder by (this field need to be redesigned)
  int32 unconfirmed_reminder_by = 18;
  // is unsubscribed
  bool is_unsubscribed = 19;
  // is recurring
  bool is_recurring = 20;

  /**
   * settings of preference
   */
  // preferred groomer id
  int64 preferred_groomer_id = 30;
  // preferred grooming frequency
  moego.utils.v1.TimePeriod preferred_grooming_frequency = 31;
  // preferred day of week
  repeated google.type.DayOfWeek preferred_day_of_week = 32;
  // preferred time of day
  moego.utils.v1.TimeOfDayInterval preferred_time_of_day = 33;
  // send app auto message
  bool send_app_auto_message = 34;
}

// business customer view for branded app account view
message BusinessCustomerBrandedAppView {
  // customer id
  int64 id = 1;
  // the first name
  string first_name = 2;
  // the last name
  string last_name = 3;
  // avatar path
  string avatar_path = 4;
  // email
  string email = 5;
  // phone number
  string phone_number = 6;
}
