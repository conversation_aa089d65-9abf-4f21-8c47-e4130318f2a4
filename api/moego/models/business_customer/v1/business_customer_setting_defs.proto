// @since 2024-08-05 15:34:20
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// update def for business customer
message BusinessCustomerCreationSettingUpdateDef {
  // enable customer creation from sms
  optional bool enable_creation_from_sms = 1;

  // enable create customer from call
  optional bool enable_creation_from_call = 2;
}
