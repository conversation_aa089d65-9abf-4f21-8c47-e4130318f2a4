syntax = "proto3";

package moego.models.business_customer.v1;

import "google/type/latlng.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// create def for business customer address
message BusinessCustomerAddressCreateDef {
  // customer id
  int64 customer_id = 1 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];

  // address 1
  string address1 = 4 [(validate.rules).string = {max_len: 255}];

  // address 2
  string address2 = 5 [(validate.rules).string = {max_len: 255}];

  // city
  string city = 6 [(validate.rules).string = {max_len: 255}];

  // state
  string state = 7 [(validate.rules).string = {max_len: 255}];

  // country
  string country = 8 [(validate.rules).string = {max_len: 255}];

  // zipcode
  string zipcode = 9 [(validate.rules).string = {max_len: 10}];

  // coordinate, optional
  optional google.type.LatLng coordinate = 10;

  // if this address is primary, default is false
  bool is_primary = 11;
}

// update def for business customer address
message BusinessCustomerAddressUpdateDef {
  // address 1, optional
  optional string address1 = 4 [(validate.rules).string = {max_len: 255}];

  // address 2, optional
  optional string address2 = 5 [(validate.rules).string = {max_len: 255}];

  // city, optional
  optional string city = 6 [(validate.rules).string = {max_len: 255}];

  // state, optional
  optional string state = 7 [(validate.rules).string = {max_len: 255}];

  // country, optional
  optional string country = 8 [(validate.rules).string = {max_len: 255}];

  // zipcode, optional
  optional string zipcode = 9 [(validate.rules).string = {max_len: 10}];

  // coordinate, optional
  optional google.type.LatLng coordinate = 10;

  // if this address is primary, optional
  optional bool is_primary = 11;
}
