syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// customer referral source model
message BusinessCustomerReferralSourceModel {
  // referral source id
  int64 id = 1;

  // referral source name
  string name = 2;

  // referral source sort. The larger the sort number, the higher the priority.
  int32 sort = 3;

  // if the referral source is deleted
  bool deleted = 4;
}

// customer referral source name view
message BusinessCustomerReferralSourceNameView {
  // referral source name
  string name = 2;
}
