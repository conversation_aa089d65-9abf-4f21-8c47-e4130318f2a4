syntax = "proto3";

package moego.models.business_customer.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// create def for customer tag
message BusinessCustomerTagCreateDef {
  // tag name
  string name = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 50
    // not blank
    pattern: ".*\\S+.*"
  }];
}

// update def for customer tag
message BusinessCustomerTagUpdateDef {
  // tag name
  optional string name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 50
    // not blank
    pattern: ".*\\S+.*"
  }];
}
