syntax = "proto3";

package moego.models.business_customer.v1;

import "google/type/date.proto";
import "moego/models/business_customer/v1/business_pet_vaccine_record_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// create def for pet vaccine record
message BusinessPetVaccineRecordCreateDef {
  // vaccine id, required
  int64 vaccine_id = 1 [(validate.rules).int64.gt = 0];

  // expiration date, optional
  optional google.type.Date expiration_date = 2;

  // vaccine document urls
  repeated string document_urls = 3 [(validate.rules).repeated = {
    unique: true
    max_items: 20
    items: {
      string: {uri: true}
    }
  }];
  // source, default is SOURCE_UNSPECIFIED
  Source source = 4 [(validate.rules).enum = {defined_only: true}];
  // verify status, default is VERIFY_STATUS_UNSPECIFIED
  optional VerifyStatus verify_status = 5 [(validate.rules).enum = {defined_only: true}];
}

// update def for pet vaccine record
message BusinessPetVaccineRecordUpdateDef {
  // record id, required
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // vaccine id, optional
  optional int64 vaccine_id = 2 [(validate.rules).int64 = {
    ignore_empty: true
    gt: 0
  }];

  // expiration date, optional
  optional google.type.Date expiration_date = 3;

  // vaccine document urls
  repeated string document_urls = 4 [(validate.rules).repeated = {
    unique: true
    max_items: 20
    items: {
      string: {uri: true}
    }
  }];
  // source
  optional Source source = 5 [(validate.rules).enum = {defined_only: true}];
  // verify status
  optional VerifyStatus verify_status = 6 [(validate.rules).enum = {defined_only: true}];
}
