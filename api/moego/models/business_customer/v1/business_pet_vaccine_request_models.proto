syntax = "proto3";

package moego.models.business_customer.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// BusinessPetVaccineRequestModel
message BusinessPetVaccineRequestModel {
  // vaccine request id
  int64 id = 1;

  // vaccine record id
  int64 vaccine_record_id = 2;

  // pet id
  int64 pet_id = 3;

  // vaccine id
  int64 vaccine_id = 4;

  // expiration date, may not exist
  optional google.type.Date expiration_date = 5;

  // vaccine document urls
  repeated string document_urls = 6;

  // status
  Status status = 7;

  // create time
  google.protobuf.Timestamp create_time = 8;

  // status
  enum Status {
    // unspecified
    STATUS_UNSPECIFIED = 0;
    // PENDING
    PENDING = 1;
    // APPROVED
    APPROVED = 2;
    // DECLINED
    DECLINED = 3;
  }
}

// BusinessPetVaccineRequestBindingModel
message BusinessPetVaccineRequestBindingModel {
  // vaccine record id
  // 没有 vaccine_record_id 表示这组 requests 是要新增 vaccine record
  // 有 vaccine_record_id 表示这组 requests 是要更新这个 vaccine record
  optional int64 vaccine_record_id = 1;
  // requests
  repeated BusinessPetVaccineRequestModel requests = 2;
}
