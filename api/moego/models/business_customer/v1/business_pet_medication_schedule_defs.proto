syntax = "proto3";

package moego.models.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_schedule_setting_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Business pet medication schedule model
message BusinessPetMedicationScheduleDef {
  // pet id
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];
  // medication amount, such as 1.2, 1/2, 1 etc.
  string medication_amount = 3 [(validate.rules).string.max_len = 255];
  // medication unit, pet_metadata.metadata_value, metadata_name = 7
  string medication_unit = 4 [(validate.rules).string.max_len = 255];
  // medication name, user input
  string medication_name = 5 [(validate.rules).string.max_len = 255];
  // medication source, user input
  string medication_note = 6 [(validate.rules).string.max_len = 10240];
  // medication time, medication time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
  repeated BusinessPetScheduleTimeDef medication_times = 7 [(validate.rules).repeated = {
    min_items: 1
    max_items: 99
  }];
}

// Business pet medication schedule view
message BusinessPetMedicationScheduleView {
  // medication id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];
  // medication amount, such as 1.2, 1/2, 1 etc.
  string medication_amount = 3 [(validate.rules).string.max_len = 255];
  // medication unit, pet_metadata.metadata_value, metadata_name = 7
  string medication_unit = 4 [(validate.rules).string.max_len = 255];
  // medication name, user input
  string medication_name = 5 [(validate.rules).string.max_len = 255];
  // medication source, user input
  string medication_note = 6 [(validate.rules).string.max_len = 10240];
  // medication time, medication time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
  repeated BusinessPetScheduleTimeDef medication_times = 7 [(validate.rules).repeated = {
    min_items: 1
    max_items: 99
  }];
}
