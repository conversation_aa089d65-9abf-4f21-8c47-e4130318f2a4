syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// customer tag model
message BusinessCustomerTagModel {
  // tag id
  int64 id = 1;

  // tag name
  string name = 2;

  // tag sort. The larger the sort number, the higher the priority.
  int32 sort = 3;

  // if the tag is deleted
  bool deleted = 4;
}

// customer tag name view
message BusinessCustomerTagNameView {
  // tag name
  string name = 2;
}

// customer tag binding model
message BusinessCustomerTagBindingModel {
  // customer id
  int64 customer_id = 1;
  // customer tag id list
  repeated int64 tag_ids = 2;
}
