syntax = "proto3";
package moego.models.business_customer.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// pet incident report model
message BusinessPetIncidentReportModel {
  // incident report id
  int64 id = 1;
  // pet id list
  repeated int64 pet_ids = 2;
  // incident timestamp
  google.protobuf.Timestamp incident_time = 3;
  // incident type id
  int64 incident_type_id = 4;
  // incident description
  string description = 5;
  // attachment url list
  repeated PetIncidentAttachment attachment_files = 6;
  // business id
  int64 business_id = 7;
  // is staff injured
  bool is_staff_injured = 8;
  // is pet injured
  bool is_pet_injured = 9;
  // is vet visit
  bool is_vet_visited = 10;
}

// pet incident attachment
message PetIncidentAttachment {
  // attachment url
  string url = 1;
  // attachment name
  string name = 2;
}
