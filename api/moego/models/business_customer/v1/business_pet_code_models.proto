syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// pet code model
message BusinessPetCodeModel {
  // pet code id
  int64 id = 1;

  // pet code abbreviation
  string abbreviation = 2;

  // same as 'abbreviation', just for compatibility, will be removed in the future
  string code_number = 3 [deprecated = true];

  // pet code description
  string description = 4;

  // pet code color
  string color = 5;

  // pet code sort. The larger the sort number, the higher the priority.
  int32 sort = 6;

  // if the pet code is deleted
  bool deleted = 7;
}

// pet code binding model
message BusinessPetCodeBindingModel {
  // pet id
  int64 pet_id = 1;
  // pet code id list
  repeated int64 pet_code_ids = 2;
}

// pet code view
message BusinessPetCodeView {
  // pet code id
  int64 id = 1;

  // pet code abbreviation
  string abbreviation = 2;

  // pet code description
  string description = 4;

  // pet code color
  string color = 5;

  // pet code sort. The larger the sort number, the higher the priority.
  int32 sort = 6;
}
