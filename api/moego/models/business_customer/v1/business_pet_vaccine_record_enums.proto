syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// source
enum Source {
  // default
  SOURCE_UNSPECIFIED = 0;
  // third party VetVerifi
  SOURCE_VET_VERIFI = 1;
}

// verify status
enum VerifyStatus {
  // unspecified
  VERIFY_STATUS_UNSPECIFIED = 0;
  // clear
  VERIFY_STATUS_CLEAR = 1;
  // not clear
  VERIFY_STATUS_NOT_CLEAR = 2;
}
