syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Business pet metadata names.
enum BusinessPetMetadataName {
  // unspecified
  PET_METADATA_NAME_UNSPECIFIED = 0;
  // feeding schedule
  FEEDING_SCHEDULE = 1;
  // feeding unit
  FEEDING_UNIT = 2;
  // feeding type
  FEEDING_TYPE = 3;
  // feeding source
  FEEDING_SOURCE = 4;
  // feeding instruction
  FEEDING_INSTRUCTION = 5;
  // medication schedule
  MEDICATION_SCHEDULE = 6;
  // medication unit
  MEDICATION_UNIT = 7;
}
