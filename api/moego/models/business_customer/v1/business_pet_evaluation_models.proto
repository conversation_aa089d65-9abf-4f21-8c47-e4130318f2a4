syntax = "proto3";

package moego.models.business_customer.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// pet evaluation model
message PetEvaluationModel {
  // id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // pet id
  int64 pet_id = 3;
  // evaluation id
  int64 evaluation_id = 4;
  // evaluation status
  models.customer.v1.EvaluationStatus evaluation_status = 5;
  // created at
  google.protobuf.Timestamp created_at = 6;
  // updated at
  google.protobuf.Timestamp updated_at = 7;
}

// pet evaluation history model
message PetEvaluationHistoryModel {
  // id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // pet id
  int64 pet_id = 3;
  // action type
  ActionType action_type = 4;
  // reset interval days
  int32 reset_interval_days = 5;
  // original value
  models.customer.v1.EvaluationStatus original_status = 6;
  // new value
  models.customer.v1.EvaluationStatus new_status = 7;
  // staff id
  int64 operator_staff_id = 8;
  // created at
  google.protobuf.Timestamp created_at = 9;
  // evaluation
  int64 evaluation_id = 10;

  //action type
  enum ActionType {
    // unspecified
    EVALUATION_ACTION_TYPE_UNSPECIFIED = 0;
    //update by appt
    UPDATE_BY_EVALUATION_APPOINTMENT = 1;
    //update by staff
    UPDATE_BY_STAFF = 2;
    //update by system
    UPDATE_BY_SYSTEM = 3;
  }
}
