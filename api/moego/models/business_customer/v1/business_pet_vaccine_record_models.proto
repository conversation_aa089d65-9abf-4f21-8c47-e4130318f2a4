syntax = "proto3";

package moego.models.business_customer.v1;

import "google/type/date.proto";
import "moego/models/business_customer/v1/business_pet_vaccine_record_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// pet vaccine record model
message BusinessPetVaccineRecordModel {
  // vaccine record id
  int64 id = 1;

  // vaccine id
  int64 vaccine_id = 3;

  // expiration date, may not exist
  optional google.type.Date expiration_date = 4;

  // vaccine document urls
  repeated string document_urls = 5;

  // if the record has been deleted
  bool deleted = 6;
  // source
  Source source = 7;
  // verify status
  VerifyStatus verify_status = 8;
}

// pet vaccine record binding model
message BusinessPetVaccineRecordBindingModel {
  // pet id
  int64 pet_id = 1;

  // vaccine record list
  repeated BusinessPetVaccineRecordModel records = 2;
}
