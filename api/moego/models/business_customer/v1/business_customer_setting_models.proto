// @since 2024-08-05 15:34:20
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// customer setting model for company
message BusinessCustomerSettingModel {
  // customer creation setting
  BusinessCustomerCreationSettingModel creation_setting = 1;
}

// customer creation setting for company
message BusinessCustomerCreationSettingModel {
  // enable customer creation from sms
  bool enable_creation_from_sms = 1;

  // enable create customer from call
  bool enable_creation_from_call = 2;
}
