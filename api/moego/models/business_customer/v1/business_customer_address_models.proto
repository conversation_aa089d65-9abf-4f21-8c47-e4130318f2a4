syntax = "proto3";

package moego.models.business_customer.v1;

import "google/type/latlng.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Address model for business customer.
// A business customer can have multiple addresses, but only one primary address.
message BusinessCustomerAddressModel {
  // address id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // business id, please do not use this field
  int64 business_id = 3 [deprecated = true];
  // address 1
  string address1 = 4;
  // address 2
  string address2 = 5;
  // country
  string country = 6;
  // city
  string city = 7;
  // state
  string state = 8;
  // zipcode
  string zipcode = 9;
  // coordinate, include latitude and longitude
  // Some address may not save coordinate,
  // please check `hasCoordinate()` before using it.
  google.type.LatLng coordinate = 10;

  // if this address is primary
  bool is_primary = 11;
  // if this address is deleted
  bool deleted = 12;
}

// business customer address model in c app appt detail view
message BusinessCustomerAddressModelClientView {
  // address id
  int64 id = 1;
  // address 1
  string address1 = 4;
  // address 2
  string address2 = 5;
  // country
  string country = 6;
  // city
  string city = 7;
  // state
  string state = 8;
  // zipcode
  string zipcode = 9;
  // coordinate, include latitude and longitude
  google.type.LatLng coordinate = 10;
}

// business customer address model for appointment detail view
message BusinessCustomerAddressCalendarView {
  // address id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // address 1
  string address1 = 4;
  // address 2
  string address2 = 5;
  // country
  string country = 6;
  // city
  string city = 7;
  // state
  string state = 8;
  // zipcode
  string zipcode = 9;
}

// business customer address view
message BusinessCustomerAddressView {
  // address id
  int64 id = 1;
  // address 1
  string address1 = 2;
  // address 2
  string address2 = 3;
  // country
  string country = 4;
  // city
  string city = 5;
  // state
  string state = 6;
  // zipcode
  string zipcode = 7;
  // coordinate, include latitude and longitude
  google.type.LatLng coordinate = 8;
  // if this address is primary
  bool is_primary = 9;
}
