syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Update def for business customer pet preference. All fields are optional. Only fields that are set will be updated.
message BusinessCustomerPetPreferenceUpdateDef {
  // enable vaccine expiry notification
  optional bool enable_vaccine_expiry_notification = 1;

  // todo: preferred grooming frequency
}
