syntax = "proto3";

package moego.models.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_vaccine_defs.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// pet vaccine model
message BusinessPetVaccineModel {
  // vaccine id
  int64 id = 1;

  // vaccine name
  string name = 2;

  // vaccine sort. The larger the sort number, the higher the priority.
  int32 sort = 3;

  // if the pet vaccine is deleted
  bool deleted = 4;

  // availability of pet types
  BusinessPetVaccineAvailabilityDef availability = 5;
}

// pet vaccine name view
message BusinessPetVaccineNameView {
  // vaccine name
  string name = 2;
}
