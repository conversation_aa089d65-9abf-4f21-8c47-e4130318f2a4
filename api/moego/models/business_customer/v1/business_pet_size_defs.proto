syntax = "proto3";

package moego.models.business_customer.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// upsert def for pet size
message BusinessPetSizeUpsertDef {
  // pet size name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
    // not blank
    pattern: ".*\\S+.*"
  }];

  // pet size lower weight limit, >= 0
  int32 weight_low = 2 [(validate.rules).int32 = {gte: 0}];

  // pet size upper weight limit, > 0
  int32 weight_high = 3 [(validate.rules).int32 = {gt: 0}];
}
