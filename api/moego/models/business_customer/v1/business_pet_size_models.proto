syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// pet size model
message BusinessPetSizeModel {
  // pet size id
  int64 id = 1;

  // pet size name
  string name = 2;

  // pet size lower weight limit
  int32 weight_low = 3;

  // pet size upper weight limit
  int32 weight_high = 4;
}
