syntax = "proto3";

package moego.models.business_customer.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// update def for pet medical info
message BusinessPetMedicalInfoUpdateDef {
  // vet name
  optional string vet_name = 1 [(validate.rules).string = {max_len: 100}];
  // vet phone number
  optional string vet_phone_number = 2 [(validate.rules).string = {max_len: 30}];
  // vet address
  optional string vet_address = 3 [(validate.rules).string = {max_len: 100}];
  // emergency contact name
  optional string emergency_contact_name = 4 [(validate.rules).string = {max_len: 100}];
  // emergency contact phone number
  optional string emergency_contact_phone_number = 5 [(validate.rules).string = {max_len: 30}];
  // health issues
  optional string health_issues = 6 [(validate.rules).string = {max_len: 3000}];
}
