syntax = "proto3";

package moego.models.business_customer.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// pet note model
message BusinessPetNoteModel {
  // pet note id
  int64 id = 1;

  // pet id
  int64 pet_id = 2;

  // note
  string note = 3;

  // created by (staff id)
  int64 created_by = 4;

  // updated by (staff id)
  int64 updated_by = 5;

  // created at
  google.protobuf.Timestamp created_at = 6;

  // updated at
  google.protobuf.Timestamp updated_at = 7;

  // is pinned
  bool is_pinned = 8;

  // pinned at
  google.protobuf.Timestamp pinned_at = 9;
}
