syntax = "proto3";

package moego.models.business_customer.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Business pet feeding model
message BusinessPetFeedingModel {
  // id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // pet id
  int64 pet_id = 3;
  // feeding amount, such as 1.2, 1/2, 1 etc.
  string feeding_amount = 4;
  // feeding unit, pet_metadata.metadata_value, metadata_name = 2
  string feeding_unit = 5;
  // feeding type, pet_metadata.metadata_value, metadata_name = 3
  string feeding_type = 6;
  // feeding source, pet_metadata.metadata_value, metadata_name = 4
  string feeding_source = 7;
  // feeding instruction, pet_metadata.metadata_value, metadata_name = 5
  string feeding_instruction = 8;
  // created at
  google.protobuf.Timestamp created_at = 9;
  // updated at
  google.protobuf.Timestamp updated_at = 10;
  // deleted at
  google.protobuf.Timestamp deleted_at = 11;
  // feeding note
  string feeding_note = 12;
}
