syntax = "proto3";

package moego.models.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_schedule_setting_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Business pet feeding schedule model
message BusinessPetFeedingScheduleDef {
  // pet id
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];
  // feeding amount, such as 1.2, 1/2, 1 etc.
  string feeding_amount = 3 [(validate.rules).string.max_len = 255];
  // feeding unit, pet_metadata.metadata_value, metadata_name = 2
  string feeding_unit = 4 [(validate.rules).string.max_len = 255];
  // feeding type, pet_metadata.metadata_value, metadata_name = 3
  string feeding_type = 5 [(validate.rules).string.max_len = 255];
  // feeding source, pet_metadata.metadata_value, metadata_name = 4
  string feeding_source = 6 [(validate.rules).string.max_len = 255];
  // feeding instruction, pet_metadata.metadata_value, metadata_name = 5
  optional string feeding_instruction = 7 [(validate.rules).string.max_len = 255];
  // feeding time, feeding time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
  repeated BusinessPetScheduleTimeDef feeding_times = 8 [(validate.rules).repeated = {
    min_items: 1
    max_items: 99
  }];
  // feeding note
  optional string feeding_note = 9 [(validate.rules).string.max_len = 10240];
}

// Business pet feeding schedule view
message BusinessPetFeedingScheduleView {
  // feeding id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];
  // feeding amount, such as 1.2, 1/2, 1 etc.
  string feeding_amount = 3 [(validate.rules).string.max_len = 255];
  // feeding unit, pet_metadata.metadata_value, metadata_name = 2
  string feeding_unit = 4 [(validate.rules).string.max_len = 255];
  // feeding type, pet_metadata.metadata_value, metadata_name = 3
  string feeding_type = 5 [(validate.rules).string.max_len = 255];
  // feeding source, pet_metadata.metadata_value, metadata_name = 4
  string feeding_source = 6 [(validate.rules).string.max_len = 255];
  // feeding instruction, pet_metadata.metadata_value, metadata_name = 5
  optional string feeding_instruction = 7 [(validate.rules).string.max_len = 255];
  // feeding time, feeding time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
  repeated BusinessPetScheduleTimeDef feeding_times = 8 [(validate.rules).repeated = {
    min_items: 1
    max_items: 99
  }];
  // feeding note
  optional string feeding_note = 9 [(validate.rules).string.max_len = 10240];
}
