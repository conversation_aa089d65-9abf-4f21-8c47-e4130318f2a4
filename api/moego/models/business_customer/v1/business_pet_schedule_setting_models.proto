syntax = "proto3";

package moego.models.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_schedule_setting_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Business pet schedule setting for feeding and medication model
message BusinessPetScheduleSettingModel {
  // id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // schedule type, feeding or medication
  models.business_customer.v1.BusinessPetScheduleType schedule_type = 3;
  // schedule id, feeding id or medication id
  int64 schedule_id = 4;
  // schedule time, schedule time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
  int32 schedule_time = 5;
  // schedule extra json, such as schedule time label etc.
  map<string, string> schedule_extra_json = 6;
}
