syntax = "proto3";

package moego.models.accounting.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/accounting/v1/accounting_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/accounting/v1;accountingmodpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.accounting.v1";

// Setting
message Setting {
  // company id
  int64 company_id = 1;
  // business id
  int64 business_id = 2;
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 3;
  // start sync time
  google.protobuf.Timestamp start_sync_time = 4;
}
