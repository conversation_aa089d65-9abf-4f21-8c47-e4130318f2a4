// @since 2022/5/31 2:15 PM
// <AUTHOR>

syntax = "proto3";

package moego.models.grey_gateway.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grey_gateway/v1;greygatewaypb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.grey_gateway.v1";

// grey item
message GreyItemModel {
  // id
  int64 id = 1;
  // item version, the format is "MMddxxx" (e.g. 0501001)
  string name = 2;
  // kubernetes namespace
  string namespace = 3;
  // description
  string description = 5;
  // jira tickets
  repeated string jira_tickets = 10;
  // service branch map
  map<string, string> svc_branch_map = 15;
  // create time
  google.protobuf.Timestamp created_at = 20;
  // update time
  google.protobuf.Timestamp updated_at = 21;
  // delete time
  google.protobuf.Timestamp deleted_at = 22;
}
