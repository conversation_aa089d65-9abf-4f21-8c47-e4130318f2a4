// @since 2023-04-07 09:48:36
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.metadata.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/metadata/v1/metadata_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/metadata/v1;metadatapb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.metadata.v1";

// the key model
message KeyModel {
  // the id
  int64 id = 1;
  // the name
  // should be unique in system
  // should not be updated
  string name = 2;
  // the description of a key
  string description = 10;
  // the owner type
  OwnerType owner_type = 3;
  // the default value
  string default_value = 4;
  // the start time, null means works any time
  optional google.protobuf.Timestamp start_at = 5;
  // the end time, null means works forever
  optional google.protobuf.Timestamp end_at = 6;
  // the operator id
  string internal_operator_id = 7;
  // the permission level
  PermissionLevel permission_level = 8;
  // the key group
  string group = 9;
  // the create time
  google.protobuf.Timestamp created_at = 13;
  // the update time
  optional google.protobuf.Timestamp updated_at = 14;
  // the delete time
  optional google.protobuf.Timestamp deleted_at = 15;
}

// The value model
message ValueModel {
  // the unique id
  int64 id = 1;
  // the key id
  int64 key_id = 2;
  // the owner id
  int64 owner_id = 3;
  // the value, not null
  string value = 4;

  // the operator id
  int64 operator_id = 5;
  // the internal operator id
  string internal_operator_id = 6;
  // the create time
  google.protobuf.Timestamp created_at = 13;
  // the update time
  optional google.protobuf.Timestamp updated_at = 14;
  // the delete time
  optional google.protobuf.Timestamp deleted_at = 15;
}

// The owner model
message OwnerModel {
  // type
  OwnerType type = 1;
  // id
  int64 id = 2;
}
