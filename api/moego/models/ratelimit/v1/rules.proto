syntax = "proto3";

package moego.models.ratelimit.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/ratelimit/v1;ratelimitpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.ratelimit.v1";

// 限流规则
message Rule {
  // 限流规则类型，默认为常规类型
  enum Type {
    // 默认值
    TYPE_UNSPECIFIED = 0;
    // 常规限流
    NORMAL = 1;
    // 黑名单
    BLACKLIST = 2;
    // 白名单
    WHITELIST = 3;
  }
  // 规则名称
  string name = 1;
  // 维度
  repeated Dimension dimensions = 2;
  // 时间片
  TimeSlot time_slot = 3;
  // 阈值
  int64 threshold = 4;
  // 规则类型
  Type type = 5;
}

// 限流维度，包含标签、具体值和匹配方式
message Dimension {
  // 标签，必填
  string label = 1;
  // 具体值，根据类型填写
  oneof matcher {
    // 是否存在
    bool exist = 2;
    // 精确匹配
    string exact = 3;
    // 包含
    string in = 4;
    // 正则匹配
    string regex = 5;
    // 范围匹配
    Range range = 6;
    // 前缀匹配
    string prefix = 7;
    // 后缀匹配
    string suffix = 8;
  }
}

// 左闭右开区间
message Range {
  // 最小值
  int64 min = 1;
  // 最大值
  int64 max = 2;
}

// 限流时间片，大小为 size * unit
message TimeSlot {
  // 时间单位
  enum Unit {
    // 默认值
    UNIT_UNSPECIFIED = 0;
    // 秒
    SECOND = 1;
    // 分钟
    MINUTE = 2;
    // 小时
    HOUR = 3;
    // 天
    DAY = 4;
  }
  // 时间片大小
  int64 size = 1;
  // 时间单位
  Unit unit = 2;
}
