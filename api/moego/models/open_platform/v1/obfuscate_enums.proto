syntax = "proto3";

package moego.models.open_platform.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/open_platform/v1;openplatformpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.open_platform.v1";

// Obfuscate case
enum ObfuscateCase {
  // unspecified
  OBFUSCATE_CASE_UNSPECIFIED = 0;
  // business
  BUSINESS = 1;
  // customer
  CUSTOMER = 2;
  // customer_tag
  CUSTOMER_TAG = 3;
  // customer_note
  CUSTOMER_NOTE = 4;
  // pet
  PET = 5;
  // staff
  STAFF = 6;
  // block
  BLOCK = 7;
  // appointment
  APPOINTMENT = 8;
  // service
  SERVICE = 9;
  // role
  ROLE = 10;
  // company
  COMPANY = 11;
  // van
  VAN = 12;
  // address
  ADDRESS = 13;
  // order
  ORDER = 14;
  // note
  NOTE = 15;
  // pet_code
  PET_CODE = 16;
  // agreement
  AGREEMENT = 17;
  // agreement_record
  AGREEMENT_RECORD = 18;
  // target
  TARGET = 19;
  // order_line_item
  ORDER_LINE_ITEM = 20;
  // payment
  PAYMENT = 21;
  // refund
  REFUND = 22;
  // review
  REVIEW = 23;
  // product
  PRODUCT = 24;
  // package
  PACKAGE = 25;
  // service_charge
  SERVICE_CHARGE = 26;
  // evaluation_service
  EVALUATION_SERVICE = 27;
  // abandoned_booking
  ABANDONED_BOOKING = 28;
  // online_booking
  ONLINE_BOOKING = 29;
  // grooming_report
  GROOMING_REPORT = 30;
}
