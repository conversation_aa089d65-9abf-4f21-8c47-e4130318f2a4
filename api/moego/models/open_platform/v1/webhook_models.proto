syntax = "proto3";

package moego.models.open_platform.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/event_bus/v1/event_defs.proto";
import "moego/models/open_platform/v1/oauth_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/open_platform/v1;openplatformpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.open_platform.v1";

// Webhook represents a webhook configuration for event notifications.
message Webhook {
  // ContentType defines the format of the payload.
  enum ContentType {
    // Default value.
    CONTENT_TYPE_UNSPECIFIED = 0;
    // application/json
    CONTENT_TYPE_JSON = 1;
    // application/x-www-form-urlencoded
    CONTENT_TYPE_FORM_URLENCODED = 2;
    // application/grpc, application/grpc+proto
    CONTENT_TYPE_GRPC = 3;
  }

  // Unique identifier of the webhook.
  int64 id = 1;

  // The enterprise ID associated with this webhook.
  int64 enterprise_id = 2;

  // The client ID (UUID) associated with this webhook.
  string client_id = 3;

  // List of organizations the webhook is subscribed to.
  // If empty, the webhook is subscribed to all organizations.
  repeated Organization organizations = 4;

  // List of event types the webhook is subscribed to.
  // If it is empty, no events will be subscribed to
  repeated models.event_bus.v1.EventType event_types = 5;

  // URL to receive webhook payloads.
  string endpoint_url = 6;

  // Optional secret token used to sign payloads (e.g., HMAC).
  string secret_token = 7;

  // Content-Type header used when delivering payloads.
  // Default: CONTENT_TYPE_JSON
  ContentType content_type = 8;

  // Whether the webhook is currently active.
  bool is_active = 9;

  // Whether to verify SSL certificates when delivering payloads.
  // Default: true (recommended for security)
  bool verify_ssl = 10;

  // A list of values for a header key.
  message HeaderValues {
    // The values
    repeated string values = 1;
  }

  // Custom HTTP headers to include when delivering payloads.
  map<string, HeaderValues> headers = 11;

  // Timestamp when the webhook was created.
  google.protobuf.Timestamp created_time = 12;

  // Timestamp when the webhook was last updated.
  google.protobuf.Timestamp updated_time = 13;
}

// WebhookQuotaConfig represents the quota configuration for a specific client.
message WebhookQuotaConfig {
  // Unique ID for this quota configuration.
  int64 id = 1;
  // The client ID (UUID) associated with this quota configuration.
  string client_id = 2;
  // Maximum number of webhooks allowed for this client.
  int32 max_webhooks = 3;
  // Maximum number of days to retain delivery logs for this client.
  int32 max_delivery_retention_days = 4;
  // Maximum number of push events allowed per minute for this client.
  int32 max_push_per_minute = 5;
  // Maximum number of push events allowed per day for this client.
  int32 max_push_per_day = 6;
  // Maximum number of push events allowed per month for this client.
  int32 max_push_per_month = 7;
  // Timestamp when the quota configuration was created.
  google.protobuf.Timestamp created_time = 8;
  // Timestamp when the quota configuration was last updated.
  google.protobuf.Timestamp updated_time = 9;
}

// WebhookDelivery represents a log entry for a webhook event delivery attempt.
message WebhookDelivery {
  // Unique ID for this delivery attempt.
  int64 id = 1;

  // Reference to the webhook that triggered this delivery.
  int64 webhook_id = 2;

  // Type of the event being delivered.
  models.event_bus.v1.EventType event_type = 3;

  // Unique message/event ID from the message queue or event source.
  string event_id = 4;

  // URL where the event was delivered.
  string request_url = 5;

  // Actual destination the request was delivered to (e.g., IP, domain, or URL).
  string delivered_to = 6;

  // HTTP headers sent with the delivery request.
  map<string, Webhook.HeaderValues> request_headers = 7;

  // Payload of the event delivery request.
  bytes request_body = 8;

  // HTTP status code returned by the endpoint.
  int32 response_status = 9;

  // HTTP headers received in the response.
  map<string, Webhook.HeaderValues> response_headers = 10;

  // Body of the response, may be truncated.
  bytes response_body = 11;

  // Timestamp when the event was delivered.
  google.protobuf.Timestamp delivered_at = 12;

  // Time taken to complete the delivery in milliseconds.
  int64 duration_ms = 13;

  // Whether the delivery was successful (HTTP 2xx).
  bool success = 14;

  // Error message in case of failure.
  string error = 15;

  // Number of retries before final delivery outcome.
  int32 retry_count = 16;

  // Format of the request body
  Webhook.ContentType request_format = 17;

  // Format of the response body
  Webhook.ContentType response_format = 18;
}
