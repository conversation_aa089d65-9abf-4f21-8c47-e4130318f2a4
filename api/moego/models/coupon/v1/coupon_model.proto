// @since 2-23-12-6
// <AUTHOR> <<EMAIL>>
syntax = "proto3";

package moego.models.coupon.v1;

import "moego/models/coupon/v1/coupon_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/coupon/v1;couponpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.coupon.v1";

// CouponDetail is the detail of a coupon
message CouponModel {
  // unique identifier for the coupon
  int32 id = 1;

  // code of the coupon
  string code = 2;

  // stripe coupon id
  string stripe_coupon_id = 3;

  // percentage off for the coupon
  double percent_off = 4;

  // amount off for the coupon
  double amount_off = 5;

  // maximum number of redemptions for the coupon
  int32 max_redemptions = 6;

  // number of times the coupon has been redeemed
  int32 times_redeemed = 7;

  // timestamp when the coupon expires
  string redeem_by = 8;

  // indicates if the coupon is valid
  int32 valid = 9;

  // status of the coupon
  int32 status = 10;

  // business category of the coupon
  CouponBusinessCategory business_category = 11;

  // validity period in months
  int32 valid_month = 12;

  // created time
  int64 created_time = 13;

  // update time
  int64 updated_time = 14;
}

// coupon simple view
message CouponSimpleView {
  // unique identifier for the coupon
  int32 id = 1;

  // code of the coupon
  string code = 2;

  // stripe coupon id
  string stripe_coupon_id = 3;

  // percentage off for the coupon
  double percent_off = 4;

  // indicates if the coupon is valid
  int32 valid = 5;

  // status of the coupon
  int32 status = 6;

  // business category of the coupon
  CouponBusinessCategory business_category = 7;

  // validity period in months
  int32 valid_month = 8;

  // created time
  int64 created_time = 9;

  // maximum number of redemptions for the coupon
  int32 max_redemptions = 10;

  // number of times the coupon has been redeemed
  int32 times_redeemed = 11;

  //the coupon  redeem by time ,the timestamp format("yy-mm-dd HH:MM:SS ZONE")
  string redeem_by = 12;

  //amount off for the coupon
  double amount_off = 13;
}
