// @since 2024-02-21 15:43:52
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.models.permission.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/permission/v1;permissionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.permission.v1";

// Permission scope available rule enum
enum PermissionScopeAvailableRule {
  // unspecified
  PERMISSION_SCOPE_AVAILABLE_RULE_UNSPECIFIED = 0;
  // only for multi-location user
  ONLY_MULTI_LOCATION = 1;
  // only for single-location user
  ONLY_SINGLE_LOCATION = 2;
}

// Permission category type enum
enum PermissionCategoryType {
  // unspecified
  PERMISSION_CATEGORY_TYPE_UNSPECIFIED = 0;
  // enterprise hub
  ENTERPRISE_HUB = 1;
  // moego platform
  MOEGO_PLATFORM = 2;
}

// Role type
enum RoleType {
  // unspecified
  ROLE_TYPE_UNSPECIFIED = 0;
  // company staff role
  ROLE_TYPE_COMPANY_STAFF = 1;
  // company owner role
  ROLE_TYPE_COMPANY_OWNER = 2;
  // enterprise staff role
  ROLE_TYPE_ENTERPRISE_STAFF = 3;
  // enterprise owner role
  ROLE_TYPE_ENTERPRISE_OWNER = 4;
  // tenant owner role, a special role in Enterprise Hub.
  // 有 enterprise id 无 company id
  // 在 enterprise 修改该 role 的权限时，会同时修改 MoeGo Platform 下 tenant 对应 company 的 owner 的权限
  ROLE_TYPE_TENANT_OWNER = 5;
}
