syntax = "proto3";

package moego.models.permission.v1;

import "moego/models/permission/v1/permission_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/permission/v1;permissionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.permission.v1";

// model for role
message RoleModel {
  // role id
  int64 id = 1;
  // role name
  string name = 2;
  // role description
  string description = 3;
  // company id
  int64 company_id = 4;
  // role permissions
  repeated PermissionCategoryModel permission_category_list = 5;
  // enterprise id
  int64 enterprise_id = 6;
  // role type
  RoleType type = 7;
}

// brief view of role
message RoleBriefView {
  // role id
  int64 id = 1;
  // role name
  string name = 2;
  // role description
  string description = 3;
  // enterprise id
  int64 enterprise_id = 4;
  // role type
  RoleType type = 5;
}

// model for permission
message PermissionModel {
  // permission id
  int64 id = 1;
  // permission name in backend
  string name = 2;
  // permission name to display
  string display_name = 3;
  // permission description
  string description = 4;
  // sub permissions
  repeated PermissionModel sub_permission_list = 5;
  // permission scopes
  repeated PermissionScopeModel scope_list = 6;
  // selected permission scope id
  int64 selected_scope_index = 7;
  // is permission selected
  bool is_selected = 8;
  // selected sub scope id
  int64 selected_sub_scope_index = 9;
}

// model for permission scope
message PermissionScopeModel {
  // extra params.
  // you can insert or reuse the params inside.
  message ExtraParam {
    // before days.
    // current used to control staff message permissions.
    optional uint32 before_day = 1 [(validate.rules).uint32 = {
      gte: 0
      lte: 30
      ignore_empty: true
    }];
    // after days.
    // current used to control staff message permissions.
    optional uint32 after_day = 2 [(validate.rules).uint32 = {
      gte: 0
      lte: 30
      ignore_empty: true
    }];
  }

  // permission scope id
  int64 index = 1;
  // permission id
  int64 permission_id = 2;
  // permission scope name in backend
  string name = 3;
  // permission scope name to display
  string display_name = 4;
  // permission scope available rule
  PermissionScopeAvailableRule available_rule = 5;
  // optional extra params.
  optional ExtraParam extra_param = 6;
  // sub permission scopes.
  // the sub permission scope should not have sub permission scope.
  repeated PermissionScopeModel sub_permission_scope_list = 7;
}

// model for permission category
message PermissionCategoryModel {
  // permission category id
  int64 id = 1;
  // permission category name in backend
  string name = 2;
  // permission category name to display
  string display_name = 3;
  // permission category description
  string description = 4;
  // permissions
  repeated PermissionModel permission_list = 5;
  // category type
  PermissionCategoryType category_type = 6;
}
