// @since 2023-07-03 12:43:02
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.ai_assistant.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/ai_assistant/v1;aiassistantpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.ai_assistant.v1";

// The BusinessConversation status
enum BusinessConversationStatus {
  // unspecified
  BUSINESS_CONVERSATION_STATUS_UNSPECIFIED = 0;
  // communicating
  BUSINESS_CONVERSATION_STATUS_COMMUNICATING = 1;
  // closed
  BUSINESS_CONVERSATION_STATUS_CLOSED = 2;
}

// The BusinessConversationQuestion status
enum BusinessConversationQuestionStatus {
  // unspecified
  BUSINESS_CONVERSATION_QUESTION_STATUS_UNSPECIFIED = 0;
  // created
  BUSINESS_CONVERSATION_QUESTION_STATUS_CREATED = 1;
  // failed
  BUSINESS_CONVERSATION_QUESTION_STATUS_FAILED = 2;
  // replied
  BUSINESS_CONVERSATION_QUESTION_STATUS_REPLIED = 3;
  // abandoned
  BUSINESS_CONVERSATION_QUESTION_STATUS_ABANDONED = 4;
}
