// @since 2-23-12-14
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.pay_ops.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/pay_ops/v1;payopspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.pay_ops.v1";

// stripe dispute model,
// corresponding to the `moe_payment`.`mm_stripe_dispute` table in MySQL.
message StripeDisputeModel {
  // Unique identifier
  int32 id = 1;

  // Business identifier
  int32 business_id = 2;

  // Dispute identifier
  string dispute_id = 3;

  // Payment id
  int32 payment_id = 4;

  // Payment intent id
  string payment_intent_id = 5;

  // Payment method (com.stripe.model.Charge#paymentMethod)
  string payment_method = 6;

  // Amount (com.stripe.model.Dispute#amount)
  int64 amount = 7;

  // Currency (com.stripe.model.Dispute#currency)
  string currency = 8;

  // Status (com.stripe.model.Dispute#status)
  string status = 9;

  // Reason for the dispute (com.stripe.model.Dispute#reason)
  string reason = 10;

  // Customer (com.stripe.model.Charge#customer)
  string customer = 11;

  // Charge creation time (com.stripe.model.Charge#created)
  int64 charged_on = 12;

  // Dispute creation time (com.stripe.model.Dispute#created)
  int64 disputed_on = 13;

  // Dispute response due time (com.stripe.model.Dispute.EvidenceDetails#dueBy)
  int64 responded_on = 14;

  // Record creation timestamp
  int64 created_at = 15;

  // Record update timestamp
  int64 updated_at = 16;

  // Record deletion timestamp (nullable)
  int64 deleted_at = 17;

  // business email
  string business_email = 18;
}

// stripe dispute info
message StripeDisputeInfoModel {
  // stripe dispute id
  string id = 1;
  // dispute amount
  int64 amount = 2;
  // associated charge id
  string charge_id = 3;
  // currency code
  string currency = 4;
  // additional metadata
  string metadata = 5;
  // dispute reason
  string reason = 6;
  // current status of the dispute
  string status = 7;
  // creation timestamp
  int64 created = 8;
  // evidence details
  EvidenceModel evidence = 9;
  // detailed evidence information
  EvidenceDetailsModel evidence_details = 10;
}

// evidence details model
message EvidenceDetailsModel {
  // deadline for submitting evidence
  int64 due_by = 1;
  // whether evidence has been submitted
  bool has_evidence = 2;
  // whether the submission was past the due date
  bool past_due = 3;
  // number of times evidence has been submitted
  int64 submission_count = 4;
}

// evidence model
message EvidenceModel {
  // server or activity logs showing customer access
  string access_activity_log = 1;
  // customer's billing address
  string billing_address = 2;
  // subscription cancellation policy document id
  string cancellation_policy = 3;
  // explanation of how refund policy was shown
  string cancellation_policy_disclosure = 4;
  // justification for not canceling subscription
  string cancellation_rebuttal = 5;
  // communication with the customer
  string customer_communication = 6;
  // customer's email address
  string customer_email_address = 7;
  // customer's name
  string customer_name = 8;
  // ip address used for purchase
  string customer_purchase_ip = 9;
  // customer's signature document id
  string customer_signature = 10;
  // documentation for duplicate charge
  string duplicate_charge_documentation = 11;
  // explanation of duplicate charge
  string duplicate_charge_explanation = 12;
  // id of the duplicate charge
  string duplicate_charge_id = 13;
  // description of the sold product or service
  string product_description = 14;
  // receipt or notification sent to customer
  string receipt = 15;
  // refund policy document id
  string refund_policy = 16;
  // documentation of refund policy disclosure
  string refund_policy_disclosure = 17;
  // justification for refusing a refund
  string refund_refusal_explanation = 18;
  // date when service was received
  string service_date = 19;
  // proof of service provided
  string service_documentation = 20;
  // address where product was shipped
  string shipping_address = 21;
  // carrier used for shipping
  string shipping_carrier = 22;
  // date when product was shipped
  string shipping_date = 23;
  // shipping documentation
  string shipping_documentation = 24;
  // tracking number for the shipment
  string shipping_tracking_number = 25;
  // additional evidence file id
  string uncategorized_file = 26;
  // additional text evidence
  string uncategorized_text = 27;
}
