// @since 2-24-01-02
// <AUTHOR> <<EMAIL>>
syntax = "proto3";

package moego.models.pay_ops.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/pay_ops/v1;payopspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.pay_ops.v1";

// status of transaction history
enum TransactionHistoryStatus {
  // unknown status
  TRANSACTION_HISTORY_UNSPECIFIED = 0;
  // create
  CREATED = 1;
  // processing
  PROCESSING = 2;
  // paid
  PAID = 3;
  // completed
  COMPLETED = 4;
  // failed
  FAILED = 5;
}
