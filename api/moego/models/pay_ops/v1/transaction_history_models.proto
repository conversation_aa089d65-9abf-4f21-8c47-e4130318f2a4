// @since 2-24-01-02
// <AUTHOR> <<EMAIL>>
syntax = "proto3";

package moego.models.pay_ops.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/pay_ops/v1;payopspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.pay_ops.v1";

// transaction history model for table payment
message TransactionHistoryModel {
  // unique identifier for the payment
  uint32 id = 1;
  // module name
  string module = 2;
  // module invoice id
  int32 invoice_id = 3;
  // customer id
  int32 customer_id = 4;
  // staff id
  int32 staff_id = 5;
  // payment method
  string method = 6;
  // payment amount
  double amount = 7;
  // status
  string status = 8;
  // create time
  int64 create_time = 9;
  // update time
  int64 update_time = 10;
  // check number
  string check_number = 11;
  // card type
  string card_type = 12;
  // card number
  string card_number = 13;
  // expiry month
  string exp_month = 14;
  // expiry year
  string exp_year = 15;
  // signature
  string signature = 16;
  // paid by
  string paid_by = 17;
  // description
  string description = 18;
  // method id
  int32 method_id = 19;
  // stripe payment intent id
  string stripe_intent_id = 20;
  // stripe client secret
  string stripe_client_secret = 21;
  // business id
  int32 business_id = 22;
  // stripe charge id
  string stripe_charge_id = 23;
  // is online
  bool is_online = 24;
  // currency, e.g. usd refer to https://en.wikipedia.org/wiki/ISO_4217
  string currency = 25;
  // grooming id
  uint32 grooming_id = 26;
  // square customer id
  string square_customer_id = 27;
  // square location id, not null if it is square payment
  string location_id = 28;
  // square payment method: 1 - card, 2 - card on file, 3- terminal, 4 - reader sdk
  string square_payment_method = 29;
  // square device id if payed by terminal
  string device_id = 30;
  // square terminal payment checkout id
  string square_checkout_id = 31;
  // credit card vendor
  string vendor = 32;
  // merchant id when taking payment
  string merchant = 33;
  // square cancel reason
  string cancel_reason = 34;
  // tips for square
  double tips = 35;
  // processing fee from stripe or square, sync from webhook
  double processing_fee = 36;
  // 1:yes 0:no
  int32 is_deposit = 37;
  // stripe payment method: 1 - card, 2 - card on file, 3- bluetooth, 4 - smart reader
  string stripe_payment_method = 38;
  // synced from stripe: credit/debit/prepay/unknown
  string card_funding = 39;
  // company id
  int64 company_id = 40;

  /**
   * Additional fields
   */
  // customer name
  string customer_name = 41;
  // currency symbol
  string currency_symbol = 42;
  // stripe refund status
  string stripe_refund_status = 43;
  // booking fee
  string booking_fee = 44;
}

// charge model for stripe
message StripeChargeModel {
  // Timestamp of the charge
  int64 created = 1;

  // Amount charged
  string amount = 2;

  // Description of the charge
  string description = 3;

  // URL of the receipt
  string receipt_url = 4;

  // Currency unit of the charge
  string currency = 5;

  // ID of the charge
  string charge_id = 6;

  // Payment method used for the charge
  string payment_method = 7;

  // Status of the charge
  string status = 8;

  // stripe refund status
  string stripe_refund_status = 9;
}
