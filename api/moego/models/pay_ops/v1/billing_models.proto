// @since 2-23-11-21
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.pay_ops.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/pay_ops/v1;payopspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.pay_ops.v1";

// corresponding to the stripe_company_customer table in the mysql
message StripeCompanyCustomerModel {
  // id
  int32 id = 1;
  // company id
  int32 company_id = 2;
  // customer id registered on stripe platform
  string stripe_customer_id = 3;
  // is the stripe platform parameter
  // an account or a customer
  string object = 4;
  // create time
  int64 create_time = 5;
  // update time
  int64 update_time = 6;
  // currency type
  string currency = 7;
  // email
  string email = 8;
  // indicates whether the object exists in real-time mode or test mode
  // true or false.
  string live_mode = 9;
  // json transparent transmission parameters (array)
  string metadata = 10;
  // Status 1 - Normal 0 - Deleted
  int32 status = 11;
}

// stripe company customer simple view
// used to display list data
message StripeCompanyCustomerSimpleView {
  // id
  int32 id = 1;
  // company id
  int32 company_id = 2;
  // customer id registered on stripe platform
  string stripe_customer_id = 3;
  // is the stripe platform parameter
  // an account or a customer
  string object = 4;
  // create time
  int64 create_time = 5;
  // currency type
  string currency = 7;
  // email
  string email = 8;
}

// stripe customer info
// The purpose of this model is to receive the information returned
// when calling the query balance interface provided by Stripe using the stripe_customer_id
message StripeCustomerInfoModel {
  // customer id, registered on stripe platform
  string customer_id = 1;
  // email, customer email
  string email = 2;
  // name
  string name = 3;
  // invoice balance
  int64 invoice_balance = 4;
  // default source
  string default_source = 5;
  // created, create time
  int64 created = 6;
  // description
  string description = 7;
  // meta
  string meta = 8;
  // balance list
  repeated moego.models.pay_ops.v1.StripeBalanceTransactionModel balance_transactions = 9;
}

// stripe balance transaction
message StripeBalanceTransactionModel {
  // id
  string id = 1;
  // amount
  int64 amount = 2;
  // currency
  string currency = 3;
  // created, create time
  int64 created = 4;
  // description
  string description = 5;
  // invoice
  string invoice = 6;
  // type
  string type = 7;
  // ending balance
  int64 ending_balance = 8;
}

// company permission state model
// corresponding to the stripe_company_permission_state table in the mysql
message CompanyPermissionStateModel {
  // id
  int32 id = 1;
  // company id
  int32 company_id = 2;
  // stripe subscriptions id
  string stripe_subscriptions_id = 3;
  // level
  int32 level = 4;
  // begin date
  int64 begin_date = 5;
  // expire date
  int64 expire_date = 6;
  // current plan id
  int32 current_plan_id = 7;
  // auto renew
  int32 auto_renew = 8;
  // next plan id
  int32 next_plan_id = 9;
  // charge status
  int32 charge_status = 10;
  // charge message
  string charge_msg = 11;
  // package message quantity
  int32 package_msg_num = 12;
  // package message used quantity
  // 0 represents that the number of text messages has not increased by 50%,
  // 1: it has increased
  int32 package_msg_used_num = 13;
  // buy message remaining quantity
  int32 buy_msg_remain_num = 14;
  // business number
  int32 business_num = 15;
  // update time
  int64 update_time = 16;
  // create time
  int64 create_time = 17;
  // charge failed time
  int64 charge_failed_time = 18;
  // additional staff number
  int32 additional_staff_num = 19;
}

// subscription info by stripe
message StripeSubscriptionInfoModel {
  // Unique identifier for the subscription
  string id = 1;
  // Timestamp for the end of the current period
  int64 current_period_end = 2;
  // Timestamp for the start of the current period
  int64 current_period_start = 3;
  // Indicates if the subscription will be cancelled at the end of the billing period
  bool cancel_at_period_end = 4;
  // Description of the subscription
  string description = 5;
  // Timestamp when the subscription was created
  int64 created = 6;
  // Current status of the subscription
  string status = 7;
  // Timestamp when the subscription started
  int64 start_date = 8;
  // Metadata associated with the subscription
  string metadata = 9;
  // Information about the applied discount, if any
  StripeCouponInfoModel stripe_coupon_info_model = 10;
  // latest invoice, if any
  StripeInvoiceInfoModel latest_invoice = 11;
  // upcoming invoice, if any
  StripeInvoiceInfoModel upcoming_invoice = 12;
}

// stripe coupon info
message StripeCouponInfoModel {
  // Unique identifier for the coupon
  string id = 1;
  // Name of the coupon
  string name = 2;
  // Timestamp when the coupon was created
  int64 created = 3;
  // Duration type for which the coupon is valid (e.g., 'forever', 'once', 'repeating')
  string duration = 4;
  // Duration in months for which the coupon is valid, if applicable
  int64 duration_in_months = 5;
  // Indicates if the coupon is in live mode
  bool livemode = 6;
  // Indicates if the coupon has been deleted
  bool deleted = 7;
  // Maximum number of redemptions allowed for the coupon
  int64 max_redemptions = 8;
  // Metadata associated with the coupon
  string metadata = 9;
  // Percentage discount offered by the coupon
  double percent_off = 10;
  // Timestamp when the coupon expires
  int64 redeem_by = 11;
  // Number of times the coupon has been redeemed
  int64 times_redeemed = 12;
  // Indicates if the coupon is still valid
  bool valid = 13;
  // Timestamp marking the start of coupon validity
  int64 start = 14;
  // Timestamp marking the end of coupon validity
  int64 end = 15;
}

// Information about the invoice
message StripeInvoiceInfoModel {
  // total amount due on the invoice
  int64 amount_due = 1;

  // creation timestamp of the invoice
  int64 created = 2;

  // timestamp of the start of the billing period
  int64 period_start = 3;

  // timestamp of the end of the billing period
  int64 period_end = 4;

  // country associated with the account
  string account_country = 5;

  // billing reason description
  // see more at: https://stripe.com/docs/api/invoices/object#invoice_object-amount_due
  string billing_reason = 6;

  // total amount after discounts and taxes
  int64 total = 7;

  // description of the invoice
  string description = 8;

  // hosted invoice URL
  string hosted_invoice_url = 9;

  // payment intent associated with the invoice
  string payment_intent = 10;
}

//stripe card list
message StripeCardListInfoModel {
  // card brand
  string brand = 1;

  // card id
  string card_id = 2;

  // card country
  string country = 3;

  // card cvc
  string cvc_check = 4;

  // card expire Month
  int64 exp_month = 5;

  // card expire year
  int64 exp_year = 6;

  // card defauld ?
  bool is_default = 7;

  // card is valid
  bool is_valid = 8;

  // card last 4
  string last4 = 9;

  // card customer name
  string name = 10;
}
