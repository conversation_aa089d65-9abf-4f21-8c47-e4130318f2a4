// @since 2-23-12-14
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.pay_ops.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/pay_ops/v1;payopspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.pay_ops.v1";

// dispute upload documents type
enum DisputeUploadDocumentType {
  // unspecified
  DISPUTE_UPLOAD_DOCUMENT_TYPE_UNSPECIFIED = 0;
  // choose category
  DISPUTE_UPLOAD_DOCUMENT_TYPE_CHOOSE_CATEGORY = 1;
  // customer communication
  DISPUTE_UPLOAD_DOCUMENT_TYPE_CUSTOMER_COMMUNICATION = 2;
  // customer signature
  DISPUTE_UPLOAD_DOCUMENT_TYPE_CUSTOMER_SIGNATURE = 3;
  // duplicate charge documentation
  DISPUTE_UPLOAD_DOCUMENT_TYPE_DUPLICATE_CHARGE_DOCUMENTATION = 4;
  // receipt
  DISPUTE_UPLOAD_DOCUMENT_TYPE_RECEIPT = 5;
  // refund and cancellation policy
  DISPUTE_UPLOAD_DOCUMENT_TYPE_REFUND_AND_CANCELLATION_POLICY = 6;
  // service documentation
  DISPUTE_UPLOAD_DOCUMENT_TYPE_SERVICE_DOCUMENTATION = 7;
  // shipping documentation
  DISPUTE_UPLOAD_DOCUMENT_TYPE_SHIPPING_DOCUMENTATION = 8;
  // other
  DISPUTE_UPLOAD_DOCUMENT_TYPE_OTHER = 9;
  // credit voucher
  DISPUTE_UPLOAD_DOCUMENT_TYPE_CREDIT_VOUCHER = 10;
  // goverment order
  DISPUTE_UPLOAD_DOCUMENT_TYPE_GOVERMENT_ORDER = 11;
  //Terms disclosure
  DISPUTE_UPLOAD_DOCUMENT_TYPE_TERMS_DISCLOSURE = 12;
}

// dispute reason type
enum DisputeReasonType {
  // unspecified
  DISPUTE_REASON_TYPE_UNSPECIFIED = 0;
  // bank cannot process
  DISPUTE_REASON_TYPE_BANK_CANNOT_PROCESS = 1;
  // check returned
  DISPUTE_REASON_TYPE_CHECK_RETURNED = 2;
  // credit not processed
  DISPUTE_REASON_TYPE_CREDIT_NOT_PROCESSED = 3;
  // customer initiated
  DISPUTE_REASON_TYPE_CUSTOMER_INITIATED = 4;
  // debit not authorized
  DISPUTE_REASON_TYPE_DEBIT_NOT_AUTHORIZED = 5;
  // duplicate
  DISPUTE_REASON_TYPE_DUPLICATE = 6;
  // fraudulent
  DISPUTE_REASON_TYPE_FRAUDULENT = 7;
  // general
  DISPUTE_REASON_TYPE_GENERAL = 8;
  // incorrect account details
  DISPUTE_REASON_TYPE_INCORRECT_ACCOUNT_DETAILS = 9;
  // insufficient funds
  DISPUTE_REASON_TYPE_INSUFFICIENT_FUNDS = 10;
  // product not received
  DISPUTE_REASON_TYPE_PRODUCT_NOT_RECEIVED = 11;
  // product unacceptable
  DISPUTE_REASON_TYPE_PRODUCT_UNACCEPTABLE = 12;
  // subscription canceled
  DISPUTE_REASON_TYPE_SUBSCRIPTION_CANCELED = 13;
  // unrecognized
  DISPUTE_REASON_TYPE_UNRECOGNIZED = 14;
}

// dispute status type
enum DisputeStatusType {
  // unspecified
  DISPUTE_STATUS_TYPE_UNSPECIFIED = 0;
  // warning needs response
  DISPUTE_STATUS_TYPE_WARNING_NEEDS_RESPONSE = 1;
  // warning under review
  DISPUTE_STATUS_TYPE_WARNING_UNDER_REVIEW = 2;
  // warning closed
  DISPUTE_STATUS_TYPE_WARNING_CLOSED = 3;
  // needs response
  DISPUTE_STATUS_TYPE_NEEDS_RESPONSE = 4;
  // under review
  DISPUTE_STATUS_TYPE_UNDER_REVIEW = 5;
  // won
  DISPUTE_STATUS_TYPE_WON = 6;
  // lost
  DISPUTE_STATUS_TYPE_LOST = 7;
}
