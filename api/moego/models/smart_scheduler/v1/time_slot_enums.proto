// @since 2025-06-06 11:03:21
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.smart_scheduler.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/smart_scheduler/v1;smartschedulerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.smart_scheduler.v1";

// time slot type
enum TimeSlotType {
  // unspecified value
  TIME_SLOT_TYPE_UNSPECIFIED = 0;

  // slot is completely empty
  EMPTY = 1;

  // slot has partial bookings but is still available
  PARTIALLY_BOOKED = 2;

  // slot is fully booked
  FULLY_BOOKED = 3;

  // slot is blocked and cannot be booked
  IN_BLOCK = 4;

  // limitations not met
  LIMITATION_NOT_MET = 5;

  // service limitation not met
  SERVICE_LIMITATION_NOT_MET = 6;

  // pet size limitation not met
  PET_SIZE_LIMITATION_NOT_MET = 7;

  // pet breed limitation not met
  PET_BREED_LIMITATION_NOT_MET = 8;
}
