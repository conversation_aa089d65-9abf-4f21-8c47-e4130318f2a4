// @since 2025-06-06 11:14:03
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.smart_scheduler.v1;

import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/smart_scheduler/v1/time_slot_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/smart_scheduler/v1;smartschedulerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.smart_scheduler.v1";

// The TimeSlot
message TimeSlot {
  // date
  string date = 1 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // start time
  int32 start_time = 2 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // end time
  optional int32 end_time = 3 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // staff id
  int64 staff_id = 4 [(validate.rules).int64.gt = 0];

  // time slot type
  TimeSlotType time_slot_type = 5;

  // booked pet count
  int32 booked_pet_count = 6;

  // pet capacity
  int32 pet_capacity = 7;

  // booked pet info
  repeated BookedInfo booked_infos = 8;

  // preview booked pet info
  repeated BookedInfo preview_booked_infos = 9;
}

// booked info
message BookedInfo {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];
  // pet name
  string pet_name = 2 [(validate.rules).string = {min_len: 1}];
  // customer id
  int64 customer_id = 3 [(validate.rules).int64.gt = 0];
  // customer last name
  string customer_last_name = 4 [(validate.rules).string = {min_len: 1}];
}

// pet param
message PetParam {
  // pet index
  int64 pet_index = 1 [(validate.rules).int64.gt = 0];
  // pet type
  moego.models.customer.v1.PetType pet_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // pet breed
  string breed = 3 [(validate.rules).string = {min_len: 1}];
  // pet weight
  optional string weight = 4;
  // pet coat
  optional string coat = 5;
  // service ids
  repeated int64 service_ids = 6 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // staff id
  optional int64 staff_id = 7 [(validate.rules).int64.gt = 0];
}
