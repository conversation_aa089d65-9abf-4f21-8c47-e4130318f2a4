syntax = "proto3";

package moego.models.enterprise.v1;

import "moego/models/organization/v1/organization_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// TemplateType
enum TemplateType {
  // Unspecified template type
  TEMPLATE_TYPE_UNSPECIFIED = 0;
  // service
  SERVICE = 1;
  // workflow
  WORKFLOW = 2;
}

// TemplatePushMapping
message TemplatePushMapping {
  // id
  int64 id = 1;
  // template type
  TemplateType template_type = 2;
  // template id
  int64 template_id = 3;
  // target organization type
  moego.models.organization.v1.OrganizationType target_organization_type = 4;
  // target organization id
  int64 target_organization_id = 5;
  // target id
  int64 target_id = 6;
}

// order by
message TemplatePushChangeHistoryOrderBy {
  // field
  enum Field {
    // 未指定
    FIELD_UNSPECIFIED = 0;
    // 创建时间
    UPDATED_AT = 1;
  }
  // field
  Field field = 1;
  // asc
  bool asc = 2;
}
