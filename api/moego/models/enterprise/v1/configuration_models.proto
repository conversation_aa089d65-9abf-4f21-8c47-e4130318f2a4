syntax = "proto3";

package moego.models.enterprise.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// configuration template
message ConfigurationTemplate {
  // id
  int64 id = 1;
  // type
  ConfigurationTemplateType type = 2;
  // enterprise_id
  int64 enterprise_id = 3;
  // name
  string name = 4;
  // last_published_at
  google.protobuf.Timestamp last_published_at = 5;
  // impacted franchisees
  int64 impacted_franchisees = 6;
  // publish status
  PublishStatus publish_status = 7;

  // configuration template type
  enum ConfigurationTemplateType {
    // buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
    // Unspecified configuration template type
    TYPE_UNSPECIFIED = 0;
    // agreement
    AGREEMENT = 1;
    // intake form
    INTAKE_FORM = 2;
    // client & pet
    // client tag
    CLIENT_AND_PET_CLIENT_TAG = 3;
    // referral source
    CLIENT_AND_PET_CLIENT_REFERRAL_SOURCE = 4;
    // grooming frequency
    CLIENT_AND_PET_CLIENT_GROOMING_FREQUENCY = 5;
    // pet code
    CLIENT_AND_PET_PET_CODE = 6;
    // pet type & breed
    CLIENT_AND_PET_PET_TYPE_AND_BREED = 7;
    // pet size
    CLIENT_AND_PET_PET_SIZE = 8;
    // pet vaccine
    CLIENT_AND_PET_PET_VACCINE = 9;
    // pet coat type
    CLIENT_AND_PET_PET_COAT_TYPE = 10;
    // pet fixed
    CLIENT_AND_PET_PET_FIXED = 11;
    // pet behavior
    CLIENT_AND_PET_PET_BEHAVIOR = 12;
  }
  // publish status
  enum PublishStatus {
    // buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
    // Unspecified publish status
    STATUS_UNSPECIFIED = 0;
    // unpublished, never published
    UNPUBLISHED = 1;
    // up to date
    UP_TO_DATE = 2;
    // outdated
    OUTDATED = 3;
  }
}

// configuration template publish record
message ConfigurationTemplatePublishRecord {
  // publish result
  enum PublishResult {
    // buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
    // Unspecified
    UNSPECIFIED = 0;
    // success
    SUCCESS = 1;
    // failed
    FAILED = 2;
  }
  // id
  int64 id = 1;
  // template_id
  int64 template_id = 2;
  // enterprise_id
  int64 enterprise_id = 3;
  // published_at
  google.protobuf.Timestamp published_at = 4;
  // impacted franchisees
  int64 impacted_franchisees_num = 5;
  // publish result
  PublishResult publish_result = 6;
}
