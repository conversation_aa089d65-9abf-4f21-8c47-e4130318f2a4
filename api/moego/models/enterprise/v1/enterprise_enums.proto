syntax = "proto3";

package moego.models.enterprise.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// unit of weight
enum WeightUnit {
  // UNSPECIFIED
  WEIGHT_UNIT_UNSPECIFIED = 0;
  // pound
  POUND = 1;
  // kilogram
  KILOGRAM = 2;
}

// unit of distance
enum DistanceUnit {
  // unspecified
  DISTANCE_UNIT_UNSPECIFIED = 0;
  // mile
  MILE = 1;
  // kilometer
  KILOMETER = 2;
}

// date format
enum DateFormat {
  // unspecified
  DATE_FORMAT_UNSPECIFIED = 0;
  // MM/DD/YYYY (e.g. 01/31/2024)
  MM_DD_YYYY_LINE = 1;
  // DD/MM/YYYY (e.g. 31/01/2024)
  DD_MM_YYYY_LINE = 2;
  // DD.MM.YYYY (e.g. 31.01.2024)
  DD_MM_YYYY_DOT = 3;
  // YYYY.MM.DD (e.g. 2024.01.31)
  YYYY_MM_DD_DOT = 4;
  // YYYY/MM/DD (e.g. 2024/01/31)
  YYYY_MM_DD_LINE = 5;
  // MMM DD/YYYY (e.g. Jan 31/2024)
  MMM_DD_YYYY_LINE = 6;
}

// time format
enum TimeFormat {
  // unspecified
  TIME_FORMAT_UNSPECIFIED = 0;
  // 24 hour
  HOUR_24 = 1;
  // 12 hour
  HOUR_12 = 2;
}

// TenantTextType
enum TenantTextType {
  // unspecified
  TENANT_TEXT_TYPE_UNSPECIFIED = 0;
  // tenant name lowercase
  TENANT_LOWERCASE = 1;
  // tenant name lowercase plural
  TENANT_LOWERCASE_PLURAL = 2;
  // tenant name title case
  TENANT_TITLE = 3;
  // tenant name title case plural
  TENANT_TITLE_PLURAL = 4;
}
