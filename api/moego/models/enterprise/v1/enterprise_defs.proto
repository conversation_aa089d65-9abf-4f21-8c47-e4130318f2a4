syntax = "proto3";

package moego.models.enterprise.v1;

import "moego/models/enterprise/v1/enterprise_enums.proto";
import "moego/models/enterprise/v1/enterprise_models.proto";
import "moego/models/enterprise/v1/time_zone.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// create enterprise def
message CreateEnterpriseDef {
  // name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // address
  AddressDef address = 2;
  // email
  string email = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 255
    email: true
  }];
  // staff avatar path
  string logo_path = 4;
  // theme color
  string theme_color = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 20
  }];
  // currency code
  string currency_code = 6 [(validate.rules).string.len = 3];
  // currency symbol
  string currency_symbol = 7 [(validate.rules).string.min_len = 1];
  // whether the notification sound is on
  bool notification_sound_enable = 8;
  // date format
  DateFormat date_format_type = 9 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // time format
  TimeFormat time_format_type = 10 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // unit of weight
  WeightUnit unit_of_weight_type = 11 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // unit of distance
  DistanceUnit unit_of_distance_type = 12 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // timezone
  models.enterprise.v1.TimeZone time_zone = 13;

  // source
  models.enterprise.v1.EnterpriseModel.Source source = 14;
}

// UpdateEnterpriseDef
message UpdateEnterpriseDef {
  // name
  optional string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // address
  optional AddressDef address = 2;
  // email
  optional string email = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 255
    email: true
  }];
  // staff avatar path
  optional string logo_path = 4;
  // theme color
  optional string theme_color = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 20
  }];
  // currency code
  optional string currency_code = 6 [(validate.rules).string.len = 3];
  // currency symbol
  optional string currency_symbol = 7 [(validate.rules).string.min_len = 1];
  // whether the notification sound is on
  optional bool notification_sound_enable = 8;
  // date format
  optional DateFormat date_format_type = 9 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // time format
  optional TimeFormat time_format_type = 10 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // unit of weight
  optional WeightUnit unit_of_weight_type = 11 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // unit of distance
  optional DistanceUnit unit_of_distance_type = 12 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // timezone
  optional models.enterprise.v1.TimeZone time_zone = 13;
}
