syntax = "proto3";

package moego.models.enterprise.v1;

import "moego/models/organization/v1/company_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// tenant model
message TenantTemplateModel {
  // enum for tenant template status
  enum Status {
    // UNSPECIFIED is the default value
    TENANT_TEMPLATE_STATUS_UNSPECIFIED = 0;
    // normal
    NORMAL = 1;
    // delete
    DELETE = 2;
  }

  // enum for tenant template type
  enum Type {
    // UNSPECIFIED is the default value
    TENANT_TEMPLATE_TYPE_UNSPECIFIED = 0;
    // demo company type
    COMPANY = 1;
    // config
    CONFIG = 2;
  }
  // tenant template id
  int64 id = 1;
  // enterprise id
  int64 enterprise_id = 2;
  // tenant template name
  string name = 3;
  // tenant template status
  Status status = 4;
  // tenant template type
  Type type = 5;
  // tenant template config
  optional string config = 6;
  // min van num
  int64 min_van_num = 7;
  // min location num
  int64 min_location_num = 8;
  // type for company struct
  message CompanyTypeInfoModel {
    // template company id
    int64 id = 1;
    // company type
    moego.models.organization.v1.CompanyModel.CompanyType type = 2;
  }
  // company type info
  optional CompanyTypeInfoModel company_type_info = 9;
}
