syntax = "proto3";

package moego.models.enterprise.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// tenant group model
message TenantGroupModel {
  // tenant group id
  int64 id = 1;
  // tenant name
  string name = 2;
  // tenant status
  // enum for tenant status
  enum Status {
    // UNSPECIFIED is the default value
    TENANT_GROUP_STATUS_UNSPECIFIED = 0;
    // normal
    NORMAL = 1;
    // delete
    DELETE = 2;
  }
  // status
  Status status = 3;
}
