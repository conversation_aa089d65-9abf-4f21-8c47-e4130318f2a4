syntax = "proto3";

package moego.models.enterprise.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// label type def
message LabelTypeDef {
  // type
  int32 type = 1;
  // label
  string label = 2;
}

// label type def list
message CurrencyDef {
  // currency
  string currency_code = 1;
  // symbol
  string currency_symbol = 2;
}
