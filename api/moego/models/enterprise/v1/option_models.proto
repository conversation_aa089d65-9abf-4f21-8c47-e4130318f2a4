syntax = "proto3";

package moego.models.enterprise.v1;

import "moego/models/enterprise/v1/option_defs.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// OptionModel is a model for options
message OptionModel {
  // List of calendar formats
  repeated models.enterprise.v1.LabelTypeDef calendar_formats = 1;

  // List of currencies
  repeated models.enterprise.v1.CurrencyDef currencies = 2;

  // Array of country names
  repeated string countries = 3;

  // List of date formats
  repeated models.enterprise.v1.LabelTypeDef date_formats = 4;

  // List of number formats
  repeated models.enterprise.v1.LabelTypeDef number_formats = 5;

  // List of time formats
  repeated models.enterprise.v1.LabelTypeDef time_formats = 6;

  // List of units of weight
  repeated models.enterprise.v1.LabelTypeDef units_of_weight = 7;

  // List of units of distance
  repeated models.enterprise.v1.LabelTypeDef units_of_distance = 8;
}
