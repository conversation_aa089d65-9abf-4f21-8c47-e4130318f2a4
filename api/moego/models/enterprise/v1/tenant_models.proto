syntax = "proto3";

package moego.models.enterprise.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// tenant view
message TenantView {
  // id
  int64 id = 1;
  // name
  string name = 2;
}

// tenant
message TenantModel {
  // enum for tenant status
  enum Status {
    // UNSPECIFIED is the default value
    TENANT_STATUS_UNSPECIFIED = 0;
    // 实例化
    INSTANTIATED = 1;
    // 激活
    LIVE = 2;
    // 删除
    DELETED = 3;
  }

  // enum for own type
  enum OwnType {
    // UNSPECIFIED is the default value
    OWN_TYPE_UNSPECIFIED = 0;
    // 加盟商
    CUSTOMIZATION = 1;
    // enterprise 企业
    ENTERPRISE = 2;
  }
  // id
  int64 id = 1;
  // enterprise id
  int64 enterprise_id = 2;
  // company id
  int64 related_company_id = 3;
  // van num
  int32 van_num = 4;
  // location num
  int32 location_num = 5;
  // territory id
  int64 territory_id = 6;
  // template id
  int64 template_id = 7;
  //  name
  string name = 8;
  // color code
  string color_code = 9;
  // status
  Status status = 10;
  // own type
  OwnType own_type = 11;
  // joined time
  google.protobuf.Timestamp joined_time = 12;
  // state
  string state = 13;
  // city
  string city = 14;
  // type
  enum Type {
    // UNSPECIFIED is the default value
    TYPE_UNSPECIFIED = 0;
    // normal tenant
    NORMAL = 1;
    // training tenant
    TRAINING = 2;
    // template tenant
    TEMPLATE = 3;
  }
  // type
  Type type = 15;
}

// TenantObject
// 作为 Enterprise Hub 中对 Tenant 和 Tenant Group 的统一抽象
message TenantObject {
  // type
  enum Type {
    // unspecified
    TYPE_UNSPECIFIED = 0;
    // all
    ALL = 1;
    // tenant
    TENANT = 2;
    // tenant group
    TENANT_GROUP = 3;
  }
  // id
  int64 id = 1;
  // type
  Type type = 2;
  // name
  string name = 3;
}
