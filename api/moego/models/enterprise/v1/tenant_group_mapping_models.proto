syntax = "proto3";

package moego.models.enterprise.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// tenant group mapping model
message TenantGroupMappingModel {
  // id
  int64 id = 1;
  //  tenant id
  int64 tenant_id = 2;
  //  group id
  int64 group_id = 3;
  // tenant status
  // enum for tenant status
  enum Status {
    // UNSPECIFIED is the default value
    TENANT_GROUP_MAPPING_STATUS_UNSPECIFIED = 0;
    // normal
    NORMAL = 1;
    // delete
    DELETE = 2;
  }
  // status
  Status status = 4;
}
