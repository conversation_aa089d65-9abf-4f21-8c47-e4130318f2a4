syntax = "proto3";

package moego.models.enterprise.v1;

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/enterprise/v1/enterprise_enums.proto";
import "moego/models/enterprise/v1/tenant_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/field_model.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// price book
message PriceBook {
  // id
  int64 id = 1;
  // name
  string name = 2;
}

// category
message ServiceCategory {
  // id
  int64 id = 1;
  // enterprise id
  int64 enterprise_id = 2;
  // price book
  PriceBook price_book = 3;
  // name
  string name = 4;
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 5;
  // sort
  int64 sort = 6;
  // service type
  moego.models.offering.v1.ServiceType service_type = 7 [(validate.rules).enum = {not_in: 0}];
}

// service model
// 原有 service 模型是企业级设计，这里先简化
message Service {
  // id
  int64 id = 1;
  // enterprise id
  int64 enterprise_id = 2;
  // price book
  PriceBook price_book = 3;
  // name
  string name = 4;
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 5;
  // category
  ServiceCategory category = 6;
  // description
  string description = 7;
  // inactive
  bool inactive = 8;
  // color
  string color = 9;
  // sort
  int64 sort = 10;
  // price
  google.type.Money price = 11;
  // service price unit
  moego.models.offering.v1.ServicePriceUnit service_price_unit = 12;
  // 万分位税率
  int64 tax_rate = 13;
  // duration
  google.protobuf.Duration duration = 14;
  // max duration
  google.protobuf.Duration max_duration = 15;
  // limitation
  Limitation limitation = 16;
  // service type
  moego.models.offering.v1.ServiceType service_type = 17;
  // images
  repeated string images = 18;

  // limitation
  message Limitation {
    // pet type breeds
    message PetTypeBreeds {
      // all breeds
      bool all_breeds = 1;
      // pet type id
      moego.models.customer.v1.PetType pet_type_id = 2;
      // pet type name
      string pet_type_name = 3;
      // required breeds
      repeated PetBreed pet_breeds = 4;
    }
    // all pet types and breeds
    bool all_pet_types_and_breeds = 1;
    // required pet types
    repeated PetTypeBreeds pet_type_breeds = 2;
    // all pet sizes
    bool all_pet_sizes = 3;
    // pet weight range
    WeightRange pet_weight_range = 4;
    // all coat types
    bool all_coat_types = 5;
    // required coat types
    repeated string required_coat_types = 6;
  }
}

// pet breed
message PetBreed {
  // pet type id
  moego.models.customer.v1.PetType pet_type_id = 1;
  // pet type name
  string pet_type_name = 2;
  // pet breed name
  string name = 3;
}

// pet type model
// Pet type can not be deleted, only can be set to unavailable.
message PetType {
  // id, primary key of pet type record in database
  int64 id = 1;

  // pet type id
  moego.models.customer.v1.PetType pet_type_id = 2;

  // pet type name, e.g. Dog, Cat, etc.
  string pet_type_name = 3;

  // if the pet type is available
  bool is_available = 4;

  // pet type sort. The larger the sort number, the higher the priority.
  int32 sort = 5;
}

// weight range
message WeightRange {
  // min weight
  Weight min = 1;
  // max weight
  Weight max = 2;
}

// weight
message Weight {
  // weight
  int64 weight = 1;
  // unit
  WeightUnit unit = 2;
}

// service change history
message ServiceChangeHistory {
  // id
  int64 id = 1;
  // enterprise id
  int64 enterprise_id = 2;
  // service id
  int64 service_id = 3;
  // impacted tenants
  int64 impacted_tenants = 4;
  // roll out at
  google.protobuf.Timestamp roll_out_at = 5;
  // updated at
  google.protobuf.Timestamp updated_at = 6;
  // impacted tenant ids
  repeated int64 impacted_tenant_ids = 7;
}

// service change
message ServiceChange {
  // id
  int64 id = 1;
  // enterprise id
  int64 enterprise_id = 2;
  // history id
  int64 history_id = 3;
  // target
  TenantObject target = 4;
  // service id
  int64 service_id = 5;
  // service name
  string service_name = 6;
  // details
  repeated DetailCategory detail_categories = 7;
  // detail
  message Detail {
    // field name
    string field_name = 1;
    // field type
    reporting.v2.Field.Type type = 2;
    // old value
    reporting.v2.Value old = 3;
    // new value
    reporting.v2.Value new = 4;
    // is changed
    bool is_changed = 5;
  }
  // detail category
  message DetailCategory {
    //name
    string name = 1;
    // details
    repeated Detail details = 2;
  }
}
