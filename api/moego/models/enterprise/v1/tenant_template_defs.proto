syntax = "proto3";

package moego.models.enterprise.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// update tenant template def
message UpdateTenantTemplateDef {
  // tenant template id
  int64 id = 1;
  // tenant template name
  optional string name = 2;
}

// create tenant template def
message CreateTenantTemplateDef {
  // tenant template name
  string name = 1;
  // tenant template, see models.enterprise.v1.TenantTemplateModel.Type
  oneof template {
    // type company
    int64 company_id = 3;
  }
}
