syntax = "proto3";

package moego.models.enterprise.v1;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "moego/models/enterprise/v1/tenant_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// Campaign
message Campaign {
  // Campaign type
  enum Type {
    // Unspecified campaign type
    TYPE_UNSPECIFIED = 0;
    // sms
    SMS = 1;
    // email
    EMAIL = 2;
  }
  // Campaign status
  enum Status {
    // Unspecified campaign status
    STATUS_UNSPECIFIED = 0;
    // draft
    DRAFT = 1;
    // scheduled
    SCHEDULED = 2;
    // sent
    SENT = 3;
    // cancelled
    CANCELLED = 4;
  }
  // stat
  message Stat {
    // franchisee
    int64 franchisee = 1;
    // sent
    int64 sent = 2;
    // open rate %
    int64 open_rate = 3;
    // click rate %
    int64 click_rate = 4;
    // bookings
    int64 bookings = 5;
    // revenue
    google.type.Money revenue = 6;
  }
  // id
  int64 id = 1;
  // enterprise_id
  int64 enterprise_id = 2;
  // staff_id
  int64 staff_id = 3;
  // name
  string name = 4;
  // type
  Type type = 5;
  // status
  Status status = 6;
  // scheduled_at
  google.protobuf.Timestamp scheduled_at = 7;
  // stat
  Stat stat = 8;
}

// Campaign template
message CampaignTemplate {
  // Template status
  enum Status {
    // Unspecified campaign status
    STATUS_UNSPECIFIED = 0;
    // draft
    DRAFT = 1;
    // published
    PUBLISHED = 2;
  }
  // id
  int64 id = 1;
  // enterprise_id
  int64 enterprise_id = 2;
  // staff_id
  int64 staff_id = 3;
  // name
  string name = 4;
  // type
  Campaign.Type type = 5;
  // status
  Status status = 6;
  // internal template id
  int64 internal_template_id = 7;
  // stat
  Campaign.Stat stat = 8;
  // 下面这几个都是外部模型的字段， enterprise 这边不存
  // description
  string description = 9;
  // cover
  string cover = 10;
  // subject
  string subject = 11;
  // content
  string content = 12;
}

// CampaignTemplatePushRecord
message CampaignTemplatePushRecord {
  // id
  int64 id = 1;
  // enterprise id
  int64 enterprise_id = 2;
  // TODO(ark: 改造成通用的 enterprise hub 分发数据方案)
  // template id
  int64 template_id = 3;
  // type
  Campaign.Type type = 4;
  // internal template id
  int64 internal_template_id = 5;
  // target
  TenantObject target = 6;
  // staff id
  int64 staff_id = 7;
}
