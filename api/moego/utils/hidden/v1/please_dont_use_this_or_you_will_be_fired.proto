// @since 2022-06-24 16:14:34
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.utils.hidden.v1;

import "google/protobuf/struct.proto";
import "google/rpc/error_details.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/hidden/v1;hiddenV1";
option java_multiple_files = true;
option java_package = "com.moego.idl.utils.hidden.v1";

// this file is used for some implementations use proto messages
// which are not imported directly.
message PleaseDontUseThisOrYouWillBeFired {
  // bad request, used by pgv-java
  google.rpc.BadRequest bad_request = 1;
  // value, used by CommonError.data
  google.protobuf.Value value = 2;
}

// use for debug system
message DebugVersion {
  // debug 1
  int32 debug_1 = 1;
}
