// @since 2023-04-09 19:29:53
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.utils.v2;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2;utilsV2";
option java_multiple_files = true;
option java_package = "com.moego.idl.utils.v2";

// offset style pagination request
message PaginationRequest {
  // page size, allow 0
  optional int32 page_size = 1 [(validate.rules).int32 = {
    lte: 1000
    gte: 0
  }];
  // page number, start from 1
  optional int32 page_num = 2 [(validate.rules).int32 = {gte: 1}];
}

// pagination response
message PaginationResponse {
  // the total count
  int32 total = 1;
  // the page size from request
  int32 page_size = 2;
  // the page num from request
  int32 page_num = 3;
}
