// @since 2022-05-30 17:23:56
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.utils.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1;utilsV1";
option java_multiple_files = true;
option java_package = "com.moego.idl.utils.v1";

// the condition for int64
// Deprecated use `moego.utils.v2.Predicate` instead.
message Int64Condition {
  option deprecated = true;
  // eq
  optional int64 eq = 1;
  // in
  repeated int64 in = 2;
  // not in
  repeated int64 not_in = 3;
  // lte
  optional int64 lte = 4;
  // gte
  optional int64 gte = 5;
  // lt
  optional int64 lt = 6;
  // gt
  optional int64 gt = 7;
}

// the condition for string
// Deprecated use `moego.utils.v2.Predicate` instead.
message StringCondition {
  option deprecated = true;
  // eq
  optional string eq = 1;
  // in
  repeated string in = 2;
  // not in
  repeated string not_in = 3;
  // lte
  optional string lte = 4;
  // gte
  optional string gte = 5;
  // lt
  optional string lt = 6;
  // gt
  optional string gt = 7;
  // like
  optional string like = 8;
  // prefix like
  optional string prefix_like = 9;
  // suffix like
  optional string suffix_like = 10;
  // not like
  optional string not_like = 11;
  // prefix not like
  optional string prefix_not_like = 12;
  // suffix not like
  optional string suffix_not_like = 13;
}
