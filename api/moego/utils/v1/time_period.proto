syntax = "proto3";

package moego.utils.v1;

import "google/type/calendar_period.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1;utilsV1";
option java_multiple_files = true;
option java_package = "com.moego.idl.utils.v1";

// TimePeriod
message TimePeriod {
  // period unit, e.g: DAY, WEEK, MONTH
  google.type.CalendarPeriod period = 1;
  // period value
  // If unit is `DAY`, amount means every `value` days. e.g: value = 3, means every 3 days
  // If unit is `WEEK`, amount means every `value` weeks. e.g: value = 2, means every 2 weeks (= 14 days)
  // If unit is `MONTH`, amount means every `value` months. e.g: value = 1, means every 1 month (= 30 days)
  int32 value = 2;
}
