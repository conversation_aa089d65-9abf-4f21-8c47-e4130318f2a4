// @since 2022-06-30 19:52:34
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.utils.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1;utilsV1";
option java_multiple_files = true;
option java_package = "com.moego.idl.utils.v1";

// offset style pagination request
// Deprecated: should be optional, use `moego.utils.v2.PaginationRequest` instead.
message PaginationRequest {
  option deprecated = true;
  // page size, allow 0
  int64 page_size = 1 [(validate.rules).int64 = {
    lte: 200
    gte: 0
  }];
  // page number, start from 0
  int64 page_no = 2 [(validate.rules).int64 = {
    lte: 1000
    gte: 0
  }];
}

// pagination response
// Deprecated: temp as int32, use `moego.utils.v2.PaginationResponse` instead.
message PaginationResponse {
  option deprecated = true;
  // the total count
  int64 total = 1;
  // the page size from request
  int64 page_size = 2;
  // the page num from request
  int64 page_no = 3;
}
