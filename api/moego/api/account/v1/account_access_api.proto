syntax = "proto3";

package moego.api.account.v1;

import "google/protobuf/empty.proto";
import "moego/models/account/v1/account_defs.proto";
import "moego/models/risk_control/v1/verification_code_defs.proto";
import "moego/models/risk_control/v1/verification_code_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/account/v1;accountapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.account.v1";

// check identifier available request
message CheckIdentifierAvailableRequest {
  // account identifier
  oneof identifier {
    option (validate.required) = true;

    // email
    string email = 1 [(validate.rules).string = {
      email: true
      max_len: 100
    }];

    // phone number
    string phone_number = 2 [(validate.rules).string = {pattern: "^\\+[1-9]\\d{1,18}$"}];
  }
}

// check identifier available response
message CheckIdentifierAvailableResponse {
  // if identifier is used
  bool used = 1;
}

// account register request
message AccountRegisterRequest {
  // email
  string email = 1 [(validate.rules).string = {
    max_len: 100
    email: true
  }];

  // phone number
  optional string phone_number = 2 [(validate.rules).string = {
    ignore_empty: true
    pattern: "^\\+[1-9]\\d{1,18}$"
  }];

  // password
  string password = 3 [(validate.rules).string = {
    min_len: 6
    max_len: 100
  }];

  // first name
  string first_name = 4 [(validate.rules).string = {max_len: 50}];

  // last name
  string last_name = 5 [(validate.rules).string = {max_len: 50}];

  // verification
  optional moego.models.risk_control.v1.VerificationCodeDef verification = 6;
}

// account register response
message AccountRegisterResponse {}

// login by token def
message TokenDef {
  // token
  string token = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
}

// login by email and password def
message EmailPasswordDef {
  // email
  string email = 1 [(validate.rules).string = {
    min_len: 3
    max_len: 100
  }];

  // password
  string password = 2 [(validate.rules).string = {max_len: 100}];
}

// login by phone number and verification code def
message PhoneNumberVerificationCodeDef {
  // phone number
  string phone_number = 1 [(validate.rules).string = {pattern: "^\\+[1-9]\\d{1,18}$"}];

  // scenario
  moego.models.risk_control.v1.VerificationCodeScenario scenario = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // verification token
  string token = 3;
  // verification code
  string code = 4;
}

// account login request
message AccountLoginRequest {
  // login method
  oneof login_method {
    option (validate.required) = true;

    // login by token
    TokenDef by_token = 1;

    // login by email and password
    EmailPasswordDef by_email_password = 2;
  }
}

// account login response
message AccountLoginResponse {}

// register or login request
message RegisterOrLoginRequest {
  // access method
  oneof access_method {
    option (validate.required) = true;

    // login by phone and verify code
    PhoneNumberVerificationCodeDef by_phone_verify_code = 1;
  }

  // namespace, default is MOEGO(id=0)
  optional moego.models.account.v1.NamespaceDef namespace = 2;
}

// register or login response
message RegisterOrLoginResponse {
  // return true if register as a new account, false if login as an existing account
  bool registered = 1;
}

// fork session request
message ForkSessionRequest {}

// fork session response
message ForkSessionResponse {
  // new session token
  string token = 1;
}

// account access API
service AccountAccessService {
  // Check if the account identifier has been used.
  // The account identifier can be email or phone number.
  // An identifier is usable (`used` = false) if it is not associated with any accounts.
  // An identifier is occupied (`used` = true) if it is associated with an account which has not been deleted.
  // If an account is deleted, its identifier will be usable again (`used` = false).
  rpc CheckIdentifierAvailable(CheckIdentifierAvailableRequest) returns (CheckIdentifierAvailableResponse);

  // Register an account.
  //
  // Error codes:
  // - CODE_EMAIL_CONFLICT: the email has been used by an existing account.
  // - CODE_PHONE_NUMBER_CONFLICT: the phone number has been used by an existing account.
  // - CODE_VERIFY_CODE_NOT_MATCH: the verification code has been used or not match for the given token.
  rpc Register(AccountRegisterRequest) returns (AccountRegisterResponse);

  // Login an account.
  //
  // Error codes:
  // - CODE_ACCOUNT_OR_PASSWORD_ERROR: account or password error.
  rpc Login(AccountLoginRequest) returns (AccountLoginResponse);

  // Register or login account.
  //
  // Error codes:
  // - CODE_VERIFICATION_CODE_NOT_MATCH: the verification code has been used or not match for the given token.
  // - CODE_VERIFICATION_CODE_EXPIRED: the verification code has been expired.
  rpc RegisterOrLogin(RegisterOrLoginRequest) returns (RegisterOrLoginResponse);

  // Logout the current account.
  //
  // Error codes:
  // - CODE_SESSION_NOT_EXIST: the session cannot be found or has been deleted.
  rpc Logout(google.protobuf.Empty) returns (google.protobuf.Empty);

  // Fork current session to a new session. The session context will be copied to the new session.
  rpc ForkSession(ForkSessionRequest) returns (ForkSessionResponse);
}
