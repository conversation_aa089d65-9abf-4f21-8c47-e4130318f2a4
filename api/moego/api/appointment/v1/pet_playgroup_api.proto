syntax = "proto3";

package moego.api.appointment.v1;

import "google/type/date.proto";
import "moego/api/appointment/v1/overview_api.proto";
import "moego/models/appointment/v1/appointment_enums.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/offering/v1/playgroup_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// list playgroup calendar view params
message ListPlaygroupCalendarViewParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
  // start date
  google.type.Date start_date = 2 [(validate.rules).message.required = true];
  // end date
  google.type.Date end_date = 3 [(validate.rules).message.required = true];
  // playgroup model pagination
  moego.utils.v2.PaginationRequest playgroup_pagination = 4;
}

// list playgroup calendar view result
message ListPlaygroupCalendarViewResult {
  // playgroup model list
  repeated moego.models.offering.v1.PlaygroupModel playgroups = 1;
  // pet playgroup daily view list
  repeated PlaygroupDailyView playgroup_dailies = 2;
  // pet view list
  repeated PetView pets = 3;
  // appointment view list
  repeated AppointmentView appointments = 4;
  // playgroup model pagination
  moego.utils.v2.PaginationResponse playgroup_pagination = 5;

  // playgroup daily view
  message PlaygroupDailyView {
    // date
    google.type.Date date = 1;
    // playgroup view
    repeated PlaygroupView playgroups = 2;
  }

  // playgroup view
  message PlaygroupView {
    // playgroup id
    int64 playgroup_id = 1;
    // pet number
    int32 pet_number = 2;
    // pet id list
    repeated PetPlaygroupView pet_playgroups = 3;
  }

  // pet group view
  message PetPlaygroupView {
    // pet playgroup id
    int64 pet_playgroup_id = 1;
    // pet id
    int64 pet_id = 2;
    // appointment id
    int64 appointment_id = 3;
    // pet playgroup list sort. start with 1 and put the smallest first
    int32 sort = 4;
  }

  // pet view
  message PetView {
    // pet id
    int64 pet_id = 1;
    // pet name
    string pet_name = 2;
    // pet image
    string pet_image = 3;
    // pet type
    moego.models.customer.v1.PetType pet_type = 4;
    // pet playgroup color
    string pet_playgroup_color = 5;
    // vaccine binding list
    repeated VaccineComposite vaccines = 6;
    // enable vaccine expiry notification, ture is enable, false is disable
    bool enable_vaccine_expiry_notification = 7;
    // customer
    CustomerView customer = 8;
  }

  // appointment view
  message AppointmentView {
    // appointment id
    int64 appointment_id = 1;
    // appointment status
    moego.models.appointment.v1.AppointmentStatus appointment_status = 2;
    // service item types
    repeated moego.models.offering.v1.ServiceItemType service_item_types = 3;
    // appointment pet ids
    repeated int64 pet_ids = 4;
    // appointment start date
    google.type.Date appointment_start_date = 5;
  }

  // customer view
  message CustomerView {
    // customer id
    int64 customer_id = 1;
    // first name
    string first_name = 2;
    // last name
    string last_name = 3;
  }
}

// reschedule pet playgroup
message ReschedulePetPlaygroupParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
  // pet playgroup id
  int64 pet_playgroup_id = 3 [(validate.rules).int64.gt = 0];
  // target playgroup id
  int64 playgroup_id = 2 [(validate.rules).int64.gt = 0];
  // target date
  google.type.Date date = 5 [(validate.rules).message.required = true];
  // target index
  int32 index = 6 [(validate.rules).int32.gt = 0];
}

// reschedule pet playgroup result
message ReschedulePetPlaygroupResult {}

// pet playgroup service
service PetPlaygroupService {
  // list playgroup calendar view
  rpc ListPlaygroupCalendarView(ListPlaygroupCalendarViewParams) returns (ListPlaygroupCalendarViewResult) {}
  // reschedule pet playgroup or date
  rpc ReschedulePetPlaygroup(ReschedulePetPlaygroupParams) returns (ReschedulePetPlaygroupResult) {}
}
