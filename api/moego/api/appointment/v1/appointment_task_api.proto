syntax = "proto3";

package moego.api.appointment.v1;

import "google/type/date.proto";
import "moego/models/appointment/v1/appointment_task_enums.proto";
import "moego/models/appointment/v1/appointment_task_models.proto";
import "moego/models/appointment/v1/boarding_split_lodging_defs.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/business_customer/v1/business_customer_pet_models.proto";
import "moego/models/business_customer/v1/business_pet_code_models.proto";
import "moego/models/offering/v1/lodging_unit_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/organization/v1/staff_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// The params of get appointment task filters
message ListAppointmentTaskFiltersParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];

  // date
  google.type.Date date = 2 [(validate.rules).message = {required: true}];
}

// The result of get appointment task filters
message ListAppointmentTaskFiltersResult {
  // The appointment task filters
  repeated Filter filters = 1;

  // Filter
  message Filter {
    // The filter key
    string key = 1;

    // The filter label
    string label = 2;

    // The filter options
    repeated Option options = 3;
  }

  // Option
  message Option {
    // The option key
    string key = 1;

    // The option label
    string label = 2;

    // The option count
    int32 count = 3;

    // The option filter
    oneof option {
      // The category
      models.offering.v1.ServiceItemType care_type = 4;

      // The status
      models.appointment.v1.AppointmentTaskStatus status = 5;
    }
  }
}

// The params of list appointment tasks
message ListAppointmentTasksParams {
  // The business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];

  // date
  google.type.Date date = 2 [(validate.rules).message = {required: true}];

  // Group by
  GroupBy group_by = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // Pagination
  moego.utils.v2.PaginationRequest pagination = 4;

  // filter
  Filter filter = 5;

  // Order by
  repeated moego.utils.v2.OrderBy order_bys = 6;

  // Appointment task list filter
  message Filter {
    // Task statuses
    repeated models.appointment.v1.AppointmentTaskStatus statuses = 1 [(validate.rules).repeated = {
      min_items: 0
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // Care types
    repeated models.offering.v1.ServiceItemType care_types = 2 [(validate.rules).repeated = {
      min_items: 0
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // Staff ids
    repeated int64 staff_ids = 3 [(validate.rules).repeated = {
      min_items: 0
      items: {
        int64: {gt: 0}
      }
    }];

    // The pet id
    repeated int64 pet_ids = 4 [(validate.rules).repeated = {
      min_items: 0
      items: {
        int64: {gt: 0}
      }
    }];

    // Selected tab filter
    oneof selected_tab {
      // The category
      models.appointment.v1.AppointmentTaskSchedule schedule_tab = 5 [(validate.rules).enum = {
        defined_only: true
        not_in: [0]
      }];

      // The schedule
      models.appointment.v1.AppointmentTaskCategory category_tab = 6 [(validate.rules).enum = {
        defined_only: true
        not_in: [0]
      }];
    }

    // Selected group filter
    oneof selected_group {
      // Schedule filter
      ScheduleFilter schedule_group = 7;

      // Category filter
      models.appointment.v1.AppointmentTaskCategory category_group = 8;

      // Add-on filter
      int64 add_on_id = 9;
    }
  }

  // Group by
  enum GroupBy {
    // Unspecified
    GROUP_BY_UNSPECIFIED = 0;
    // Category
    CATEGORY = 1;
    // Schedule
    SCHEDULE = 2;
  }
}

// The result of list appointment tasks
message ListAppointmentTasksMultiGroupResult {
  // The task groups
  repeated TaskGroup task_groups = 1;

  // The Pagination response
  moego.utils.v2.PaginationResponse pagination = 2;

  // The tab count
  repeated TabCount tab_counts = 3;
}

// The result of appointment task tab count
message TabCount {
  // The tab
  oneof tab {
    // The category
    models.appointment.v1.AppointmentTaskSchedule schedule = 1;

    // The schedule
    models.appointment.v1.AppointmentTaskCategory category = 2;
  }

  // The count
  int32 count = 3;
}

// The schedule filter
message ScheduleFilter {
  // The schedule name, feeding/medication label
  string schedule_name = 1;

  // The schedule time, minutes of the day, 480
  int32 schedule_time = 2;
}

// The task group
message TaskGroup {
  // The group
  oneof group {
    // Category group
    CategoryGroup category_group = 1;

    // Schedule group
    ScheduleGroup schedule_group = 2;
  }
}

// Category group
message CategoryGroup {
  // The category
  models.appointment.v1.AppointmentTaskCategory category = 1;

  // The schedule groups
  repeated ScheduleTaskGroup groups = 2;
}

// Schedule task group
message ScheduleTaskGroup {
  // Schedule
  ScheduleFilter schedule = 1;

  // The task items
  repeated AppointmentTaskItem task_items = 2;

  // The count
  int32 count = 3;
}

// Schedule group
message ScheduleGroup {
  // The schedule
  models.appointment.v1.AppointmentTaskSchedule schedule = 1;

  // The category groups
  repeated CategoryTaskGroup groups = 2;
}

// Category task group
message CategoryTaskGroup {
  // The category item
  CategoryGroupItem category_item = 1;

  // The task items
  repeated AppointmentTaskItem task_items = 2;

  // The count
  int32 count = 3;
}

// Category group item
message CategoryGroupItem {
  // The category
  models.appointment.v1.AppointmentTaskCategory category = 1;

  // The category name
  string category_name = 2;

  // The add-on id
  optional int64 add_on_id = 3;
}

// Appointment task item
message AppointmentTaskItem {
  // The appointment task
  models.appointment.v1.AppointmentTaskView task = 1;

  // The pet info
  models.business_customer.v1.BusinessCustomerPetCalendarView pet = 2;

  // The lodging info
  models.offering.v1.LodgingUnitView lodging = 3;

  // The staff info
  models.organization.v1.StaffBasicView staff = 4;

  // The pet codes
  // Please use pet_code_views instead
  repeated string pet_codes = 5 [deprecated = true];

  // The pet code views
  repeated models.business_customer.v1.BusinessPetCodeView pet_code_views = 6;

  // boarding split lodging
  repeated models.appointment.v1.BoardingSplitLodgingDetailDef split_lodgings = 7;
}

// The params of list appointment task count by category
message ListAppointmentTaskCountByCategoryParams {
  // The business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];

  // date
  google.type.Date date = 2 [(validate.rules).message = {required: true}];

  // filter
  Filter filter = 3;

  // Appointment task list filter
  message Filter {
    // Care types
    repeated models.offering.v1.ServiceItemType care_types = 1 [(validate.rules).repeated = {
      min_items: 0
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // Task statuses
    repeated models.appointment.v1.AppointmentTaskStatus statuses = 2 [(validate.rules).repeated = {
      min_items: 0
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // Staff ids
    repeated int64 staff_ids = 3 [(validate.rules).repeated = {
      min_items: 0
      items: {
        int64: {gt: 0}
      }
    }];
  }
}

// The result of list appointment task count by category
message ListAppointmentTaskCountByCategoryResult {
  // The all task count
  int32 count = 1;

  // The category items
  repeated CategoryGroupCountItem category_items = 3;

  // The category group count item
  message CategoryGroupCountItem {
    // The category
    models.appointment.v1.AppointmentTaskCategory category = 1;

    // The category name
    string category_name = 2;

    // The add-on id
    optional int64 add_on_id = 3;

    // The count
    int32 count = 4;

    // The completed count
    int32 completed_count = 5;
  }
}

// The params of list appointment tasks by category
message ListAppointmentTasksByCategoryParams {
  // The business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];

  // date
  google.type.Date date = 2 [(validate.rules).message = {required: true}];

  // Pagination
  moego.utils.v2.PaginationRequest pagination = 4;

  // filter
  Filter filter = 5;

  // Appointment task list filter
  message Filter {
    // Care types
    repeated models.offering.v1.ServiceItemType care_types = 1 [(validate.rules).repeated = {
      min_items: 0
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // Task statuses
    repeated models.appointment.v1.AppointmentTaskStatus statuses = 2 [(validate.rules).repeated = {
      min_items: 0
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // The category filter
    optional models.appointment.v1.AppointmentTaskCategory category = 3 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];

    // The add-on id
    optional int64 add_on_id = 4 [(validate.rules).int64 = {gt: 0}];
  }
}

// The result of list appointment tasks by category
message ListAppointmentTasksByCategoryResult {
  // The task items
  repeated AppointmentTaskItem task_items = 1;

  // The Pagination response
  moego.utils.v2.PaginationResponse pagination = 2;

  // The category item
  CategoryGroupItem category = 3;
}

// The params of update appointment task
message UpdateAppointmentTaskParams {
  // The task id
  int64 task_id = 1 [(validate.rules).int64 = {gt: 0}];

  // The staff id
  optional int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];

  // The status
  optional models.appointment.v1.AppointmentTaskStatus status = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // The note status
  oneof note_status {
    // The note status of feeding task
    models.appointment.v1.FeedingTaskNoteStatus feeding_note_status = 10;

    // The note status of medication task
    models.appointment.v1.MedicationTaskNoteStatus medication_note_status = 11;
  }

  // The note content
  optional string note_content = 5 [(validate.rules).string = {max_len: 500}];
}

// The result of update appointment task
message UpdateAppointmentTaskResult {}

// The params of batch update appointment task
message BatchUpdateAppointmentTaskParams {
  // The selected task id
  repeated int64 task_ids = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];

  // The staff id
  optional int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];

  // The status
  optional models.appointment.v1.AppointmentTaskStatus status = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // Filter
  Filter filter = 4;

  // Group filter list
  // The logical relationship between each group filter is OR
  repeated GroupFilter group_filters = 5;

  // date
  google.type.Date date = 6 [(validate.rules).message = {required: true}];

  // Appointment task list filter
  message Filter {
    // Task statuses
    repeated models.appointment.v1.AppointmentTaskStatus statuses = 1 [(validate.rules).repeated = {
      min_items: 0
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // Care types
    repeated models.offering.v1.ServiceItemType care_types = 2 [(validate.rules).repeated = {
      min_items: 0
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // Staff ids
    repeated int64 staff_ids = 3 [(validate.rules).repeated = {
      min_items: 0
      items: {
        int64: {gt: 0}
      }
    }];

    // The pet id
    repeated int64 pet_ids = 4 [(validate.rules).repeated = {
      min_items: 0
      items: {
        int64: {gt: 0}
      }
    }];
  }

  // The tab group filter
  message GroupFilter {
    // Selected group filter
    oneof selected_group {
      // Schedule filter
      ScheduleFilter schedule_group = 1;

      // Category filter
      models.appointment.v1.AppointmentTaskCategory category_group = 2;

      // Add-on filter
      int64 add_on_id = 3;
    }
  }
}

// The result of batch update appointment task
message BatchUpdateAppointmentTaskResult {
  // Affected row
  int32 affected_row = 1;
}

// The params of remove task staff
message RemoveTaskStaffParams {
  // The task id
  int64 task_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result of remove task staff
message RemoveTaskStaffResult {}

// The params of remove task note
message RemoveTaskNoteParams {
  // The task id
  int64 task_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result of remove task note
message RemoveTaskNoteResult {}

// The params of search pet with task date
message SearchPetWithTaskDateParams {
  // The business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];

  // date
  google.type.Date date = 2 [(validate.rules).message = {required: true}];

  // The pet keyword
  string keyword = 3 [(validate.rules).string = {max_len: 50}];

  // pagination
  moego.utils.v2.PaginationRequest pagination = 4 [(validate.rules).message.required = true];
}

// The result of search pet with task date
message SearchPetWithTaskDateResult {
  // The pet and client view list
  repeated PetAndClientView pets = 1;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;

  // pet and client view
  message PetAndClientView {
    // pet
    models.business_customer.v1.BusinessCustomerPetCalendarView pet = 1;

    // client
    models.business_customer.v1.BusinessCustomerModelNameView client = 2;
  }
}

// The appointment task service
service AppointmentTaskService {
  // List appointment task filters
  rpc ListAppointmentTaskFilters(ListAppointmentTaskFiltersParams) returns (ListAppointmentTaskFiltersResult);

  // List appointment tasks by multiple group
  rpc ListAppointmentTasksMultiGroup(ListAppointmentTasksParams) returns (ListAppointmentTasksMultiGroupResult);

  // List appointment task count by category
  rpc ListAppointmentTaskCountByCategory(ListAppointmentTaskCountByCategoryParams) returns (ListAppointmentTaskCountByCategoryResult);

  // List appointment tasks by category
  rpc ListAppointmentTasksByCategory(ListAppointmentTasksByCategoryParams) returns (ListAppointmentTasksByCategoryResult);

  // List owner appointment tasks
  rpc ListOwnerAppointmentTasks(ListAppointmentTasksByCategoryParams) returns (ListAppointmentTasksByCategoryResult);

  // Update appointment task, e.g. assign staff, change status
  rpc UpdateAppointmentTask(UpdateAppointmentTaskParams) returns (UpdateAppointmentTaskResult);

  // Batch update appointment task, e.g. assign staff, change status
  rpc BatchUpdateAppointmentTask(BatchUpdateAppointmentTaskParams) returns (BatchUpdateAppointmentTaskResult);

  // Remove task staff
  rpc RemoveTaskStaff(RemoveTaskStaffParams) returns (RemoveTaskStaffResult);

  // Remove task note
  rpc RemoveTaskNote(RemoveTaskNoteParams) returns (RemoveTaskNoteResult);

  // Search for a pet with a task for the day
  rpc SearchPetWithTaskDate(SearchPetWithTaskDateParams) returns (SearchPetWithTaskDateResult);
}
