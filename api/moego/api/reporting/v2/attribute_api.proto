syntax = "proto3";

package moego.api.reporting.v2;

import "moego/models/reporting/v2/attribute_def.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/reporting/v2;reportingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.reporting.v2";

// Attribute API for company
service AttributeService {
  // Get dimensions
  rpc GetDimensions(moego.models.reporting.v2.GetDimensionsParams) returns (moego.models.reporting.v2.GetDimensionsResult);
  // Get metrics categories
  rpc GetMetricsCategories(moego.models.reporting.v2.GetMetricsCategoriesParams) returns (moego.models.reporting.v2.GetMetricsCategoriesResult);
}
