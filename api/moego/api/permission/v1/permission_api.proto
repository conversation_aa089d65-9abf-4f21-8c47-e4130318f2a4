syntax = "proto3";

package moego.api.permission.v1;

import "moego/models/permission/v1/permission_defs.proto";
import "moego/models/permission/v1/permission_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/permission/v1;permissionapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.permission.v1";

// get role list request
message GetRoleListParams {}

// get role list response
message GetRoleListResult {
  // role list
  repeated moego.models.permission.v1.RoleBriefView role_list = 1;
}

// get role detail request
message GetRoleDetailParams {
  // role id, 0 for owner
  int64 role_id = 1 [(validate.rules).int64.gte = 0];
}

// get role detail response
message GetRoleDetailResult {
  // role detail
  moego.models.permission.v1.RoleModel role_detail = 1;
}

// create role request
message CreateRoleParams {
  // role name
  string role_name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
}

// create role response
message CreateRoleResult {
  // role list after created
  repeated moego.models.permission.v1.RoleBriefView role_list = 1;
  // role id
  int64 role_id = 2;
}

// update role request
message UpdateRoleParams {
  // role id
  int64 role_id = 1 [(validate.rules).int64.gt = 0];
  // role name
  string role_name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
}

// update role response
message UpdateRoleResult {
  // role list after updated
  repeated moego.models.permission.v1.RoleBriefView role_list = 1;
}

// delete role request
message DeleteRoleParams {
  // role id
  int64 role_id = 1 [(validate.rules).int64.gt = 0];
}

// delete role response
message DeleteRoleResult {
  // role list after deleted
  repeated moego.models.permission.v1.RoleBriefView role_list = 1;
}

// duplicate role request
message DuplicateRoleParams {
  // role id
  int64 role_id = 1 [(validate.rules).int64.gt = 0];
  // role name
  string role_name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 20
  }];
}

// duplicate role response
message DuplicateRoleResult {
  // role list after duplicated
  repeated moego.models.permission.v1.RoleBriefView role_list = 1;
}

// edit permissions request
message EditPermissionsParams {
  // role id
  int64 role_id = 1 [(validate.rules).int64.gt = 0];
  // permission list in a category
  repeated moego.models.permission.v1.EditCategoryPermissionDef permission_category_list = 2;
}

// edit permissions response
message EditPermissionsResult {
  // permission list after edited
  repeated moego.models.permission.v1.PermissionCategoryModel permission_category_list = 1;
}

// permission service
service PermissionService {
  // get role list
  rpc GetRoleList(GetRoleListParams) returns (GetRoleListResult) {}
  // get role detail
  rpc GetRoleDetail(GetRoleDetailParams) returns (GetRoleDetailResult) {}
  // create role
  rpc CreateRole(CreateRoleParams) returns (CreateRoleResult) {}
  // update role
  rpc UpdateRole(UpdateRoleParams) returns (UpdateRoleResult) {}
  // delete role
  rpc DeleteRole(DeleteRoleParams) returns (DeleteRoleResult) {}
  // duplicate role
  rpc DuplicateRole(DuplicateRoleParams) returns (DuplicateRoleResult) {}
  // edit permissions
  rpc EditPermissions(EditPermissionsParams) returns (EditPermissionsResult) {}
}
