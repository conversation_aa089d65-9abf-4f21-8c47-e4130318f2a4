// @since 2024-04-08 15:28:27
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.api.online_booking.v1;

import "google/type/date.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/online_booking/v1/ob_availability_setting_defs.proto";
import "moego/models/online_booking/v1/ob_availability_setting_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.online_booking.v1";

// get boarding service availability params
message GetBoardingServiceAvailabilityParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get boarding service availability result
message GetBoardingServiceAvailabilityResult {
  // boarding service availability
  moego.models.online_booking.v1.BoardingServiceAvailabilityModel boarding_service_availability = 1;
}

// update boarding service availability params
message UpdateBoardingServiceAvailabilityParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // boarding service availability
  moego.models.online_booking.v1.BoardingServiceAvailabilityUpdateDef boarding_service_availability = 2;
}

// update boarding service availability result
message UpdateBoardingServiceAvailabilityResult {
  // boarding service availability
  moego.models.online_booking.v1.BoardingServiceAvailabilityModel boarding_service_availability = 1;
}

// get daycare service availability params
message GetDaycareServiceAvailabilityParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get daycare service availability result
message GetDaycareServiceAvailabilityResult {
  // daycare service availability
  moego.models.online_booking.v1.DaycareServiceAvailabilityModel daycare_service_availability = 1;
}

// update daycare service availability params
message UpdateDaycareServiceAvailabilityParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // daycare service availability
  moego.models.online_booking.v1.DaycareServiceAvailabilityUpdateDef daycare_service_availability = 2;
}

// update daycare service availability result
message UpdateDaycareServiceAvailabilityResult {
  // daycare service availability
  moego.models.online_booking.v1.DaycareServiceAvailabilityModel daycare_service_availability = 1;
}

// get evaluation service availability params
message GetEvaluationServiceAvailabilityParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get evaluation service availability result
message GetEvaluationServiceAvailabilityResult {
  // evaluation service availability
  moego.models.online_booking.v1.EvaluationServiceAvailabilityModel availability = 1;
}

// update evaluation service availability params
message UpdateEvaluationServiceAvailabilityParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // evaluation service availability
  moego.models.online_booking.v1.EvaluationServiceAvailabilityUpdateDef availability = 2;
}

// update evaluation service availability result
message UpdateEvaluationServiceAvailabilityResult {
  // evaluation service availability
  moego.models.online_booking.v1.EvaluationServiceAvailabilityModel availability = 1;
}

// get grooming service availability params
message GetGroomingServiceAvailabilityParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get grooming service availability result
message GetGroomingServiceAvailabilityResult {
  // grooming service availability
  moego.models.online_booking.v1.GroomingServiceAvailabilityModel availability = 1;
}

// update grooming service availability params
message UpdateGroomingServiceAvailabilityParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // grooming service availability
  moego.models.online_booking.v1.GroomingServiceAvailabilityUpdateDef availability = 2;
}

// update grooming service availability result
message UpdateGroomingServiceAvailabilityResult {
  // grooming service availability
  moego.models.online_booking.v1.GroomingServiceAvailabilityModel availability = 1;
}

// arrival/pick up time model
message ArrivalPickUpTimeOverrideView {
  // id
  int64 id = 1;
  // start date
  google.type.Date start_date = 2;
  // end date
  google.type.Date end_date = 3;
  // is available
  bool is_available = 4;
  // arrival time range
  repeated moego.models.online_booking.v1.DayTimeRangeDef day_time_ranges = 5;
  // is_active
  bool is_active = 6;
}

// List arrival pick up time overrides params
message ListArrivalPickUpTimeOverridesParams {
  // business id
  optional int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service item types, optional
  repeated models.offering.v1.ServiceItemType service_item_types = 2 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// List arrival pick up time overrides  result
message ListArrivalPickUpTimeOverridesResult {
  // arrival time override
  repeated ArrivalPickUpTimeOverrideView arrival_time_overrides = 1;
  // pick up time override
  repeated ArrivalPickUpTimeOverrideView pick_up_time_overrides = 2;
}

// batch create arrival pick up time override params
message BatchCreateArrivalPickUpTimeOverrideParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // arrival time overrides
  repeated CreateDef arrival_time_overrides = 3;
  // pick up time overrides
  repeated CreateDef pick_up_time_overrides = 4;

  // arrival/pick up time override crate params
  message CreateDef {
    // start date
    google.type.Date start_date = 1;
    // end date
    google.type.Date end_date = 2;
    // is available
    bool is_available = 3;
    // arrival time range
    repeated moego.models.online_booking.v1.DayTimeRangeDef day_time_ranges = 4;
  }
}

// batch create arrival pick up time override result
message BatchCreateArrivalPickUpTimeOverrideResult {
  // arrival time overrides
  repeated ArrivalPickUpTimeOverrideView arrival_time_overrides = 1;
  // pick up time overrides
  repeated ArrivalPickUpTimeOverrideView pick_up_time_overrides = 2;
}

// batch delete arrival pick up time override params
message BatchDeleteArrivalPickUpTimeOverrideParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // arrival pick up time override ids
  repeated int64 ids = 2 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch delete arrival pick up time override result
message BatchDeleteArrivalPickUpTimeOverrideResult {}

// batch update arrival pick up time override params
message BatchUpdateArrivalPickUpTimeOverrideParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // arrival time overrides
  repeated UpdateDef arrival_time_overrides = 2;
  // pick up time overrides
  repeated UpdateDef pick_up_time_overrides = 3;
  // arrival/pick up time override update params
  message UpdateDef {
    // id
    int64 id = 1 [(validate.rules).int64 = {gt: 0}];
    // start date
    optional google.type.Date start_date = 2;
    // end date
    optional google.type.Date end_date = 3;
    // is available
    optional bool is_available = 4;
    // day time range list
    optional moego.models.online_booking.v1.DayTimeRangeDefList day_time_ranges = 5;
  }
}

// batch update arrival pick up time override result
message BatchUpdateArrivalPickUpTimeOverrideResult {
  // arrival time overrides
  repeated ArrivalPickUpTimeOverrideView arrival_time_overrides = 1;
  // pick up time overrides
  repeated ArrivalPickUpTimeOverrideView pick_up_time_overrides = 2;
}

// create capacity override params
message CreateCapacityOverrideParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // capacity override
  moego.models.online_booking.v1.CapacityOverrideDef capacity_override = 3 [(validate.rules).message.required = true];
}

// create capacity override result
message CreateCapacityOverrideResult {
  // capacity overrides
  repeated moego.models.online_booking.v1.CapacityOverrideModel capacity_overrides = 1;
}

// delete capacity override params
message DeleteCapacityOverrideParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // capacity override id
  int64 id = 2 [(validate.rules).int64 = {gt: 0}];
}

// delete capacity override result
message DeleteCapacityOverrideResult {
  // capacity overrides
  repeated moego.models.online_booking.v1.CapacityOverrideModel capacity_overrides = 1;
}

// update capacity override params
message UpdateCapacityOverrideParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // capacity override
  moego.models.online_booking.v1.CapacityOverrideDef capacity_override = 2 [(validate.rules).message.required = true];
}

// update capacity override result
message UpdateCapacityOverrideResult {
  // capacity overrides
  repeated moego.models.online_booking.v1.CapacityOverrideModel capacity_overrides = 1;
}

// the ob_availability_setting service
service OBAvailabilitySettingService {
  // get boarding service availability
  rpc GetBoardingServiceAvailability(GetBoardingServiceAvailabilityParams) returns (GetBoardingServiceAvailabilityResult) {}
  // update boarding service availability
  rpc UpdateBoardingServiceAvailability(UpdateBoardingServiceAvailabilityParams) returns (UpdateBoardingServiceAvailabilityResult) {}
  // get daycare service availability
  rpc GetDaycareServiceAvailability(GetDaycareServiceAvailabilityParams) returns (GetDaycareServiceAvailabilityResult) {}
  // update daycare service availability
  rpc UpdateDaycareServiceAvailability(UpdateDaycareServiceAvailabilityParams) returns (UpdateDaycareServiceAvailabilityResult) {}

  // get evaluation service availability
  rpc GetEvaluationServiceAvailability(GetEvaluationServiceAvailabilityParams) returns (GetEvaluationServiceAvailabilityResult) {}
  // update evaluation service availability
  rpc UpdateEvaluationServiceAvailability(UpdateEvaluationServiceAvailabilityParams) returns (UpdateEvaluationServiceAvailabilityResult) {}

  // get grooming service availability
  rpc GetGroomingServiceAvailability(GetGroomingServiceAvailabilityParams) returns (GetGroomingServiceAvailabilityResult) {}
  // update grooming service availability
  rpc UpdateGroomingServiceAvailability(UpdateGroomingServiceAvailabilityParams) returns (UpdateGroomingServiceAvailabilityResult) {}

  // list arrival pick up time overrides
  rpc ListArrivalPickUpTimeOverrides(ListArrivalPickUpTimeOverridesParams) returns (ListArrivalPickUpTimeOverridesResult) {}
  // batch create arrival pick up time override
  rpc BatchCreateArrivalPickUpTimeOverride(BatchCreateArrivalPickUpTimeOverrideParams) returns (BatchCreateArrivalPickUpTimeOverrideResult) {}
  // batch delete arrival pick up time override
  rpc BatchDeleteArrivalPickUpTimeOverride(BatchDeleteArrivalPickUpTimeOverrideParams) returns (BatchDeleteArrivalPickUpTimeOverrideResult) {}
  // batch update arrival pick up time override
  rpc BatchUpdateArrivalPickUpTimeOverride(BatchUpdateArrivalPickUpTimeOverrideParams) returns (BatchUpdateArrivalPickUpTimeOverrideResult) {}

  // create capacity override
  rpc CreateCapacityOverride(CreateCapacityOverrideParams) returns (CreateCapacityOverrideResult) {}
  // delete capacity override
  rpc DeleteCapacityOverride(DeleteCapacityOverrideParams) returns (DeleteCapacityOverrideResult) {}
  // update capacity override
  rpc UpdateCapacityOverride(UpdateCapacityOverrideParams) returns (UpdateCapacityOverrideResult) {}
}
