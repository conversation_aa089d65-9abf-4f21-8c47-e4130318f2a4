// @since 2024-10-11 12:09:44
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.online_booking.v1;

import "moego/models/offering/v1/service_enum.proto";
import "moego/models/online_booking/v1/automation_defs.proto";
import "moego/models/online_booking/v1/automation_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.online_booking.v1";

// get automation setting params
message GetAutomationSettingParams {
  // the business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service item types
  moego.models.offering.v1.ServiceItemType service_item_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// get automation setting result
message GetAutomationSettingResult {
  // the automation
  moego.models.online_booking.v1.AutomationSettingModel automation_setting = 1;
}

// create automation setting params
message UpdateAutomationSettingParams {
  // the business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service item types
  moego.models.offering.v1.ServiceItemType service_item_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // enable auto accept
  bool enable_auto_accept = 3;
  // auto accept condition
  moego.models.online_booking.v1.AutomationConditionDef auto_accept_condition = 4;
}

// create automation setting result
message UpdateAutomationSettingResult {
  // the updated automation
  moego.models.online_booking.v1.AutomationSettingModel automation_setting = 1;
}

// the automation service
service AutomationService {
  // get automation setting
  rpc GetAutomationSetting(GetAutomationSettingParams) returns (GetAutomationSettingResult);
  // update automation setting
  rpc UpdateAutomationSetting(UpdateAutomationSettingParams) returns (UpdateAutomationSettingResult);
}
