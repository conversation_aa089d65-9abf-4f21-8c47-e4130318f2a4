// @since 2024-08-29 10:29:03
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.online_booking.v1;

import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.online_booking.v1";

// set customer block status params
message SetCustomerBlockStatusParams {
  // service item types
  repeated moego.models.offering.v1.ServiceItemType service_item_types = 1 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    max_items: 10
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // customer ids
  repeated int64 customer_ids = 2 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
  // need to block, true: block, false: unblock
  bool need_block = 3;
}

// set customer block status result
message SetCustomerBlockStatusResult {}

// the customer availability service
service CustomerAvailabilityService {
  // set customer block status
  rpc SetCustomerBlockStatus(SetCustomerBlockStatusParams) returns (SetCustomerBlockStatusResult);
}
