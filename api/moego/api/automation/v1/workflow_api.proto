syntax = "proto3";

package moego.api.automation.v1;

import "moego/models/automation/v1/filter.proto";
import "moego/models/automation/v1/workflow.proto";
import "moego/models/automation/v1/workflow_defs.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/filter_model.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/automation/v1;automationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.automation.v1";

// Workflow Service
service WorkflowService {
  // GetWorkflowConfig
  rpc GetWorkflowConfig(GetWorkflowConfigParams) returns (GetWorkflowConfigResult);

  // CreateWorkflow
  rpc CreateWorkflow(CreateWorkflowParams) returns (CreateWorkflowResult);

  // UpdateWorkflowContent
  rpc UpdateWorkflowContent(UpdateWorkflowContentParams) returns (UpdateWorkflowContentResult);
  // UpdateWorkflowInfo
  rpc UpdateWorkflowInfo(UpdateWorkflowInfoParams) returns (UpdateWorkflowInfoResult);

  // ListWorkflowCategories
  rpc ListWorkflowCategories(ListWorkflowCategoriesParams) returns (ListWorkflowCategoriesResult);
  // ListWorkflows
  rpc ListWorkflows(ListWorkflowsParams) returns (ListWorkflowsResult);
  // ListEnterpriseWorkflows
  rpc ListEnterpriseWorkflows(ListEnterpriseWorkflowsParams) returns (ListEnterpriseWorkflowsResult);
  // ListWorkflowRecords
  rpc ListWorkflowRecords(ListWorkflowRecordsParams) returns (ListWorkflowRecordsResult);
  // ListWorkflowTemplates
  rpc ListWorkflowTemplates(ListWorkflowTemplatesParams) returns (ListWorkflowTemplatesResult);
  // GetWorkflowInfo
  rpc GetWorkflowInfo(GetWorkflowInfoParams) returns (GetWorkflowInfoResult);
  // GetWorkflowTemplateInfo
  rpc GetWorkflowTemplateInfo(GetWorkflowTemplateInfoParams) returns (GetWorkflowTemplateInfoResult);

  // UpdateWorkflowSetting
  rpc UpdateWorkflowSetting(UpdateWorkflowSettingParams) returns (UpdateWorkflowSettingResult);

  // GetWorkflowSetting
  rpc GetWorkflowSetting(GetWorkflowSettingParams) returns (GetWorkflowSettingResult);

  // opt:CreateWorkflowTemplate
  rpc CreateWorkflowTemplate(CreateWorkflowTemplateParams) returns (CreateWorkflowTemplateResult);
  // opt:FilterCustomer
  rpc FilterCustomer(FilterCustomerParams) returns (FilterCustomerResult);
}

// GetWorkflowConfigParams
message GetWorkflowConfigParams {}

// GetWorkflowConfigResult
message GetWorkflowConfigResult {
  // WorkflowConfig List
  repeated models.automation.v1.WorkflowConfig workflow_configs = 1;
  // Common Filters
  repeated models.reporting.v2.FilterGroup filter_groups = 2;
  // Event Filters
  repeated models.automation.v1.EventFilterGroups event_filter_groups = 3;
}

// CreateWorkflowParams
message CreateWorkflowParams {
  // Workflow to be created
  models.automation.v1.CreateWorkflowDef workflow = 1;
}

// CreateWorkflowResult
message CreateWorkflowResult {
  // Workflow
  models.automation.v1.Workflow workflow = 1;
}

// UpdateWorkflowContentParams
message UpdateWorkflowContentParams {
  // Workflow ID
  int64 workflow_id = 1;
  // Steps to update
  repeated models.automation.v1.CreateStepDef steps = 2;
  // Workflow Consumer Data
  models.automation.v1.Workflow.ConsumerData consumer_data = 3;
}

// UpdateWorkflowContentResult
message UpdateWorkflowContentResult {
  // Workflow
  models.automation.v1.Workflow workflow = 1;
}

// UpdateWorkflowInfoParams
message UpdateWorkflowInfoParams {
  // Workflow ID
  int64 workflow_id = 1;
  // Workflow name
  optional string name = 2;
  // Workflow description
  optional string desc = 3;
  // Workflow status
  optional models.automation.v1.Workflow.Status status = 4;
  // Workflow setting
  optional models.automation.v1.WorkflowSetting setting = 5;
  // shut down steps option for set workflow to INACTIVE
  optional bool shut_down_pending_steps = 6;
}

// UpdateWorkflowInfoResult
message UpdateWorkflowInfoResult {
  // Workflow
  models.automation.v1.Workflow workflow = 1;
}

// ListWorkflowCategoriesParams
message ListWorkflowCategoriesParams {}

// ListWorkflowCategoriesResult
message ListWorkflowCategoriesResult {
  // Categories
  repeated models.automation.v1.WorkflowCategory categories = 1;
}

// ListWorkflowsParams
message ListWorkflowsParams {
  // Filter
  message Filter {
    // Workflow name filter
    optional string name = 1;
    // Workflow status filter
    repeated models.automation.v1.Workflow.Status status = 2;
    // Category ID filter
    optional int64 category_id = 3;
  }
  // Pagination
  optional moego.utils.v2.PaginationRequest pagination = 1;
  // Filter
  optional Filter filter = 2;
}

// ListWorkflowsResult
message ListWorkflowsResult {
  // Workflows
  repeated models.automation.v1.Workflow workflows = 1;
  // Pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// ListEnterpriseWorkflowsParams
message ListEnterpriseWorkflowsParams {
  // Filter
  message Filter {
    // Workflow name filter
    optional string name = 1;
    // Workflow status filter
    repeated models.automation.v1.Workflow.Status status = 2;
  }
  // Pagination
  optional moego.utils.v2.PaginationRequest pagination = 1;
  // Filter
  optional Filter filter = 2;
}

// ListEnterpriseWorkflowsResult
message ListEnterpriseWorkflowsResult {
  // Workflows
  repeated models.automation.v1.Workflow workflows = 1;
  // Pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// ListWorkflowRecordsParams
message ListWorkflowRecordsParams {
  // Filter
  message Filter {
    // Customer name filter
    optional string customer_name = 1;
  }
  // Workflow ID
  int64 workflow_id = 1;
  // Pagination
  optional moego.utils.v2.PaginationRequest pagination = 2;
  // Filter
  optional Filter filter = 3;
}

// ListWorkflowRecordsResult
message ListWorkflowRecordsResult {
  // Workflow records
  repeated models.automation.v1.WorkflowRecord workflow_records = 1;
  // Pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// ListWorkflowTemplatesParams
message ListWorkflowTemplatesParams {
  // Filter
  message Filter {
    // Template name filter
    optional string name = 1;
    // Category ID filter
    optional int64 category_id = 2;
    // Recommendation type filter
    optional moego.models.automation.v1.Workflow.RecommendType recommend_type = 3;
  }
  // Pagination
  optional moego.utils.v2.PaginationRequest pagination = 1;
  // Filter
  optional Filter filter = 2;
}

// ListWorkflowTemplatesResult
message ListWorkflowTemplatesResult {
  // Workflows
  repeated models.automation.v1.Workflow workflows = 1;
  // Pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// GetWorkflowInfoParams
message GetWorkflowInfoParams {
  // Workflow ID
  int64 workflow_id = 1;
}

// GetWorkflowInfoResult
message GetWorkflowInfoResult {
  // Workflow
  models.automation.v1.Workflow workflow = 1;
}

// GetWorkflowTemplateInfoParams
message GetWorkflowTemplateInfoParams {
  // Workflow template ID
  int64 workflow_id = 1;
}

// GetWorkflowTemplateInfoResult
message GetWorkflowTemplateInfoResult {
  // Workflow
  models.automation.v1.Workflow workflow = 1;
}

// GetWorkflowSettingParams
message GetWorkflowSettingParams {}

// GetWorkflowSettingResult
message GetWorkflowSettingResult {
  // Workflow setting
  models.automation.v1.WorkflowSetting setting = 2;
}

// UpdateWorkflowSettingParams
message UpdateWorkflowSettingParams {
  // Workflow setting
  models.automation.v1.WorkflowSetting setting = 2;
}

// UpdateWorkflowSettingResult
message UpdateWorkflowSettingResult {
  // Workflow setting
  models.automation.v1.WorkflowSetting setting = 1;
}

// CreateWorkflowTemplateParams
message CreateWorkflowTemplateParams {
  // Workflow to be created as template
  models.automation.v1.CreateWorkflowDef workflow = 1;
}

// CreateWorkflowTemplateResult
message CreateWorkflowTemplateResult {
  // Workflow
  moego.models.automation.v1.Workflow workflow = 1;
}

// FilterCustomerParams
message FilterCustomerParams {
  // Filter requests
  repeated models.reporting.v2.FilterRequest filters = 1;
  // Pagination
  optional moego.utils.v2.PaginationRequest pagination = 2;
  // Company ID
  int64 company_id = 3;

  // Customer ID filter
  optional int64 customer_id = 10;
}

// FilterCustomerResult
message FilterCustomerResult {
  // Customers
  repeated moego.models.business_customer.v1.BusinessCustomerInfoModel customer = 1;
  // Pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;
}
