syntax = "proto3";

package moego.api.promotion.v1;

import "moego/models/package/v1/package_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/promotion/v1;promotionapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.promotion.v1";

// PackageService
service PackageService {
  // ListPackages
  rpc ListPackages(ListPackagesRequest) returns (ListPackagesResponse);
}

// ListPackagesRequest
message ListPackagesRequest {
  // business IDs, empty = all
  repeated int64 business_ids = 1;
}

// ListPackagesResponse
message ListPackagesResponse {
  // packages
  repeated moego.models.package.v1.PackageModel packages = 1;
}
