syntax = "proto3";

package moego.api.order.v1;

import "moego/models/order/v1/service_charge_model.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/order/v1;orderapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.order.v1";

// get service charge request params
message GetServiceChargeListRequest {
  // is active
  optional bool is_active = 1;
  // appointment id
  optional int64 appointment_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// add service charge request params
message AddServiceChargeRequest {
  // name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 150
  }];
  // split method
  optional string description = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 1000
  }];
  // price, must be positive
  double price = 3 [(validate.rules).double = {gt: 0}];
  // tax id, 0 or null means no tax
  optional int32 tax_id = 4 [(validate.rules).int32 = {gte: 0}];
  // is mandatory
  // deprecated by <PERSON> since 2024/9/25, use auto_apply_status instead
  optional bool is_mandatory = 5 [deprecated = true];
  // is active
  optional bool is_active = 6;
  // apply to upcoming
  optional bool apply_upcoming_appt = 7;
  // auto apply status
  optional moego.models.order.v1.ServiceCharge.AutoApplyStatus auto_apply_status = 8 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply condition
  optional moego.models.order.v1.ServiceCharge.AutoApplyCondition auto_apply_condition = 9 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply time
  optional int32 auto_apply_time = 10 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// update service charge request params
message UpdateServiceChargeRequest {
  // id, exist for update
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // name
  optional string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 150
  }];
  // split method
  optional string description = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 1000
  }];
  // price, must be positive
  optional double price = 4 [(validate.rules).double = {gt: 0}];
  // tax id, 0 or null means no tax
  optional int32 tax_id = 5 [(validate.rules).int32 = {gte: 0}];
  // is mandatory, preserved 8-19 for future use
  // deprecated by Freeman since 2024/9/25, use auto_apply_status instead
  optional bool is_mandatory = 6 [deprecated = true];
  // is active
  optional bool is_active = 7;
  // apply to upcoming
  optional bool apply_upcoming_appt = 8;
  // auto apply status
  optional moego.models.order.v1.ServiceCharge.AutoApplyStatus auto_apply_status = 9 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply condition
  optional moego.models.order.v1.ServiceCharge.AutoApplyCondition auto_apply_condition = 10 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply time
  optional int32 auto_apply_time = 11 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// sort service charge request params
message SortServiceChargeRequest {
  // sorted id list from front-end
  repeated int64 sorted_id = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
}

// delete service charge request params
message DeleteServiceChargeRequest {
  // to be deleted id
  int64 id = 1;
  // apply to upcoming, if ture, will delete service charge from upcoming unconfirmed and no-fully-paid appointments
  optional bool apply_upcoming_appt = 2;
}

// get service charge list response
message GetServiceChargeListResponse {
  // service charge list
  repeated moego.models.order.v1.ServiceCharge service_charge = 1;
}

// operate service charge response
message OperateServiceChargeResponse {
  // result
  bool result = 1;
}

// service charge api
service ServiceChargeService {
  // get service charge list
  rpc GetServiceChargeList(GetServiceChargeListRequest) returns (GetServiceChargeListResponse);
  // add service charge
  rpc AddServiceCharge(AddServiceChargeRequest) returns (moego.models.order.v1.ServiceCharge);
  // update service charge
  rpc UpdateServiceCharge(UpdateServiceChargeRequest) returns (moego.models.order.v1.ServiceCharge);
  // sort service charge
  rpc SortServiceCharge(SortServiceChargeRequest) returns (OperateServiceChargeResponse);
  // delete service charge
  rpc DeleteServiceCharge(DeleteServiceChargeRequest) returns (OperateServiceChargeResponse);
}
