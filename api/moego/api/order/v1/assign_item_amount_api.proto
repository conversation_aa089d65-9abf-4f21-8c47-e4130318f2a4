syntax = "proto3";

package moego.api.order.v1;

import "moego/models/order/v1/item_paid_amount_assignment_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/order/v1;orderapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.order.v1";

// Assign item paid amount request
message AssignItemPaidAmountParams {
  // Order ID
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // List of items to assign paid amount
  repeated moego.models.order.v1.ItemPaidAmountAssignment items = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 20
  }];
  // business_id of the order
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// Assign item paid amount response
message AssignItemPaidAmountResult {
  // Whether the operation was successful
  bool success = 1;
}

// Get assigned item paid amount request
message GetAssignedItemPaidAmountParams {
  // Order ID
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business_id of the order
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// Get assigned item paid amount response
message GetAssignedItemPaidAmountResult {
  // Order ID
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // List of item payment assignments
  repeated moego.models.order.v1.ItemPaidAmountAssignment items = 2;
}

// Assign item amount service API
service AssignItemAmountApiService {
  // Assign paid amount to specific items
  rpc AssignItemPaidAmount(AssignItemPaidAmountParams) returns (AssignItemPaidAmountResult);
  // Get assigned paid amount for order items
  rpc GetAssignedItemPaidAmount(GetAssignedItemPaidAmountParams) returns (GetAssignedItemPaidAmountResult);
}
