syntax = "proto3";

package moego.api.file.v2;

import "moego/models/file/v2/file_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/file/v2;fileapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.file.v2";

// QueryFileRequest
message QueryFileRequest {
  // file id
  int64 file_id = 1;
}

// QueryFileResponse
message QueryFileResponse {
  // file info
  moego.models.file.v2.FileModel file = 1;
}

// GetUploadPresignedUrlRequest
message GetUploadPresignedUrlRequest {
  // owner type,eg:staff,pet...
  string owner_type = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // owner id
  int64 owner_id = 2 [(validate.rules).int64 = {gt: 0}];
  // usage for the file, must be one of FileUsage
  string usage = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // file md5 after base64
  string md5 = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // file name(with extension)
  string file_name = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 200
  }];
  // file size byte
  int64 file_size_byte = 6 [(validate.rules).int64 = {
    gte: 0
    lte: 104857600
  }];
  // file metadata
  map<string, string> metadata = 7;
}

// GetPresignedUrlResponse
message GetUploadPresignedUrlResponse {
  // file id
  int64 file_id = 1;
  // presigned url for upload
  string presigned_url = 2;

  // the headers used for later uploads, must contain Content-Type and Content-MD5.
  map<string, string> metadata = 8;
}

// FlushFileRequest
message FlushFileRequest {
  // file id
  int64 file_id = 1;
}

// FlushFileResponse
message FlushFileResponse {
  // file info
  moego.models.file.v2.FileModel file = 1;
}

// FileService
service FileService {
  // GetUploadPresignedUrl
  rpc GetUploadPresignedUrl(GetUploadPresignedUrlRequest) returns (GetUploadPresignedUrlResponse);

  // QueryFile
  rpc QueryFile(QueryFileRequest) returns (QueryFileResponse);

  // flush file status
  rpc FlushFile(FlushFileRequest) returns (FlushFileResponse);
}
