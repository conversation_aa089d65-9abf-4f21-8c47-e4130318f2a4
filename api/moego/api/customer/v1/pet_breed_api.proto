syntax = "proto3";

package moego.api.customer.v1;

import "google/protobuf/empty.proto";
import "moego/models/customer/v1/customer_pet_breed_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/customer/v1;customerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.customer.v1";

//  pet breed list response
message PetBreedListResponse {
  // pet breed list
  repeated moego.models.customer.v1.PetBreedModel pet_breed_list = 1;
}

// pet breed service
service PetBreedService {
  // get pet breed list
  rpc GetPetBreedList(google.protobuf.Empty) returns (PetBreedListResponse);
}
