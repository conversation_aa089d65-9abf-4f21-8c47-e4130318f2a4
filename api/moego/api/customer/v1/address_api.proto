syntax = "proto3";

package moego.api.customer.v1;

import "google/protobuf/empty.proto";
import "moego/models/customer/v1/customer_address_models.proto";
import "moego/utils/v1/id_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/customer/v1;customerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.customer.v1";

// address request
message AddressRequest {
  // address 1
  string address1 = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // address 2
  string address2 = 2 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 255
  }];
  // country
  string country = 3 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 255
  }];
  // city
  string city = 4 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 255
  }];
  // state
  string state = 5 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 255
  }];
  // zipcode
  string zipcode = 6 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 10
  }];
  // lat
  string lat = 7 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 50
  }];
  // lng
  string lng = 8 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 50
  }];
  // is primary
  bool is_primary = 9;
}

// address list
message AddressListResponse {
  // address list
  repeated moego.models.customer.v1.CustomerAddressModel address_list = 1;
}

// add address request
message AddAddressRequest {
  // address request
  AddressRequest address = 1 [(validate.rules).message = {required: true}];
}

// update address request
message UpdateAddressRequest {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // address request
  AddressRequest address = 2 [(validate.rules).message = {required: true}];
}

// address service
service AddressService {
  // get address list
  rpc GetAddressList(google.protobuf.Empty) returns (AddressListResponse);
  // add address
  rpc AddAddress(AddAddressRequest) returns (moego.utils.v1.Id);
  // update address
  rpc UpdateAddress(UpdateAddressRequest) returns (google.protobuf.Empty);
  // delete address
  rpc DeleteAddress(moego.utils.v1.Id) returns (google.protobuf.Empty);
}
