syntax = "proto3";

package moego.api.message.v1;

import "moego/models/message/v1/auto_message_template_defs.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/message/v1;messageapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.message.v1";

// Get preview template message content params
message GetPreviewTemplateParams {
  // auto message for appointment
  models.message.v1.AutoMessageAppointmentDef for_appointment = 1;
}

// Get preview template message content result
message GetPreviewTemplateResult {
  // template message content, contains template variables
  string template_content = 1;
  // filled message content, template variables replaced with values
  string preview_content = 2;
}

// Auto message template service
service AutoMessageTemplateService {
  // Get preview message content
  rpc GetPreviewTemplate(GetPreviewTemplateParams) returns (GetPreviewTemplateResult);
}
