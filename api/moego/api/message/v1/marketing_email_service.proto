syntax = "proto3";

package moego.api.message.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/message/v1/marketing_email_defs.proto";
import "moego/models/message/v1/marketing_email_enums.proto";
import "moego/models/message/v1/marketing_email_models.proto";
import "moego/utils/v1/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/message/v1;messageapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.message.v1";

// MassEmailSendRequest
message MassEmailSendRequest {
  // email to send
  models.message.v1.EmailDef email = 1 [(validate.rules).message.required = true];
  // draft id, optional
  optional int64 draft_id = 2;
  // recipient filter
  optional models.message.v1.RecipientFilterDef recipient_filter = 3;
}

// MassEmailSendResponse
message MassEmailSendResponse {
  // email id
  int64 id = 1;
}

// GetEmailListRequest
message GetEmailListRequest {
  // pagination request
  moego.utils.v1.PaginationRequest pagination = 1 [(validate.rules).message.required = true];
  // email status filter
  optional models.message.v1.MarketingEmailStatus status = 2;
  // email subject filter
  optional string subject = 3 [(validate.rules).string.max_len = 200];
}

// GetEmailListResponse
message GetEmailListResponse {
  // pagination response
  moego.utils.v1.PaginationResponse pagination = 1;
  // email list
  repeated models.message.v1.MarketingEmailModelBriefView list = 2;
}

// GetEmailDetailRequest
message GetEmailDetailRequest {
  // email id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// for schedule email, send right now
message SendNowRequest {
  // email id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// save email draft request
message SaveEmailDraftRequest {
  // email content
  models.message.v1.EmailDef email = 1 [(validate.rules).message.required = true];
  // recipient filter
  optional models.message.v1.RecipientFilterDef recipient_filter = 2;
  // draft id, optional
  optional int64 draft_id = 3;
}

// save email draft response
message SaveEmailDraftResponse {
  // email id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// send test email for preview
message SendTestEmailRequest {
  // email subject
  string subject = 1 [(validate.rules).string.max_len = 200];
  // email content
  string content = 2 [(validate.rules).string.max_len = 100000];
  // attachments
  repeated models.message.v1.AttachmentDef attachments = 3;
  // recipient email
  string recipient_email = 4 [(validate.rules).string.email = true];
}

// get count of available emails response
message GetAvailableEmailsCountResponse {
  // available emails
  int64 available_emails = 1;
  // used emails
  int64 used_emails = 2;
}

// cancel schedule email
message CancelScheduleEmailRequest {
  // email id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// reschedule email
message RescheduleEmailRequest {
  // email id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // new send time
  google.protobuf.Timestamp send_time = 2 [(validate.rules).timestamp.required = true];
}

// get recipient list request
message GetRecipientListRequest {
  // email id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // pagination request
  moego.utils.v1.PaginationRequest pagination = 2 [(validate.rules).message.required = true];
  // recipient status filter
  optional models.message.v1.MarketingEmailRecipientStatus status = 3;
}

// get recipient list response
message GetRecipientListResponse {
  // pagination response
  moego.utils.v1.PaginationResponse pagination = 1;
  // recipient list
  repeated models.message.v1.MarketingEmailRecipientModel recipients = 2;
}

// get marketing email template list
message GetMarketingEmailTemplateListRequest {
  // pagination request
  moego.utils.v1.PaginationRequest pagination = 1 [(validate.rules).message.required = true];
}

// get marketing email template list response
message GetMarketingEmailTemplateListResponse {
  // pagination response
  moego.utils.v1.PaginationResponse pagination = 1;
  // template list
  repeated models.message.v1.MarketingEmailTemplateModelBriefView templates = 2;
}

// get marketing email template detail request
message GetMarketingEmailTemplateDetailRequest {
  // template id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// view email reply request
message ViewEmailReplyRequest {
  // recipient id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// view email reply response
message ViewEmailReplyResponse {
  // email reply
  string content = 1;
}

// delete email
message DeleteEmailRequest {
  // email id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// calculateCreditCostRequest
message CalculateCreditCostRequest {
  // number of recipients
  int64 recipient_count = 1 [(validate.rules).int64.gt = 0];
}

// calculateCreditCostResponse
message CalculateCreditCostResponse {
  // credit cost
  int64 credit_cost = 1;
}

// get appointments after an email request
message GetAppointmentsAfterEmailRequest {
  // email id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // pagination request
  moego.utils.v1.PaginationRequest pagination = 2 [(validate.rules).message.required = true];
}

// get appointments after an email response
message GetAppointmentsAfterEmailResponse {
  // pagination response
  moego.utils.v1.PaginationResponse pagination = 1;
  // appointment list
  repeated models.message.v1.MarketingEmailApptBriefView appointments = 2;
}

// Marketing Email Service
service MarketingEmailService {
  // mass send email
  rpc MassEmailSend(MassEmailSendRequest) returns (MassEmailSendResponse);

  // get email list
  rpc GetEmailList(GetEmailListRequest) returns (GetEmailListResponse);

  // get email detail
  rpc GetEmailDetail(GetEmailDetailRequest) returns (models.message.v1.MarketingEmailModel);

  // send now for schedule email
  rpc SendNow(SendNowRequest) returns (google.protobuf.Empty);

  // send test email for preview
  rpc SendTestEmail(SendTestEmailRequest) returns (google.protobuf.Empty);

  // get available emails count
  rpc GetAvailableEmailsCount(google.protobuf.Empty) returns (GetAvailableEmailsCountResponse);

  // cancel schedule email
  rpc CancelScheduleEmail(CancelScheduleEmailRequest) returns (google.protobuf.Empty);

  // save email draft
  rpc SaveEmailDraft(SaveEmailDraftRequest) returns (SaveEmailDraftResponse);

  // reschedule email
  rpc RescheduleEmail(RescheduleEmailRequest) returns (google.protobuf.Empty);

  // get recipient list
  rpc GetRecipientList(GetRecipientListRequest) returns (GetRecipientListResponse);

  // get marketing email template list
  rpc GetMarketingEmailTemplateList(GetMarketingEmailTemplateListRequest) returns (GetMarketingEmailTemplateListResponse);

  // get marketing email template detail
  rpc GetMarketingEmailTemplateDetail(GetMarketingEmailTemplateDetailRequest) returns (models.message.v1.MarketingEmailTemplateModel);

  // view email reply
  rpc ViewEmailReply(ViewEmailReplyRequest) returns (ViewEmailReplyResponse);

  // delete email
  rpc DeleteEmail(DeleteEmailRequest) returns (google.protobuf.Empty);

  // calculate credit cost
  rpc CalculateCreditCost(CalculateCreditCostRequest) returns (CalculateCreditCostResponse);

  // get appointments after an email
  rpc GetAppointmentsAfterEmail(GetAppointmentsAfterEmailRequest) returns (GetAppointmentsAfterEmailResponse);
}
