syntax = "proto3";

package moego.api.grooming.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/grooming/v1/grooming_report_enums.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/grooming/v1;groomingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.grooming.v1";

// list grooming report card params
message ListGroomingReportCardParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];

  // start date
  optional google.type.Date start_date = 3;

  // end date
  optional google.type.Date end_date = 4;

  // report status
  optional models.grooming.v1.GroomingReportStatus status = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: 0
  }];

  // pet id
  optional int64 pet_id = 6 [(validate.rules).int64 = {gt: 0}];

  // pagination
  moego.utils.v2.PaginationRequest pagination = 7 [(validate.rules).message = {required: true}];
}

// list grooming report card result
message ListGroomingReportCardResult {
  // grooming report card list
  repeated GroomingReportCardDef grooming_report_cards = 1;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// grooming report card def
message GroomingReportCardDef {
  // report card id
  int64 report_card_id = 1;

  // pet overview
  ReportCardPetOverview pet_overview = 2;

  // last update time
  google.protobuf.Timestamp update_time = 3;

  // send time
  google.protobuf.Timestamp send_time = 4;

  // appointment id
  int64 appointment_id = 5;

  // send method
  models.grooming.v1.GroomingReportSendMethod send_method = 6;

  // media(image/video) count
  int32 media_count = 7;

  // service date, date of report
  google.type.Date service_date = 8;

  // uuid
  string uuid = 9;
}

// report card pet overview
message ReportCardPetOverview {
  // pet id
  int64 pet_id = 1;

  // pet name
  string pet_name = 2;

  // pet type
  models.customer.v1.PetType pet_type = 3;

  // pet avatar
  string avatar_path = 4;
}

// batch delete grooming report params
message BatchDeleteGroomingReportCardParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];

  // report card ids
  repeated int64 report_card_ids = 3;
}

// batch delete grooming report result
message BatchDeleteGroomingReportCardResult {}

// batch send grooming report params
message BatchSendGroomingReportCardParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];

  // report card ids
  repeated int64 report_card_ids = 3;

  // send method
  models.grooming.v1.GroomingReportSendMethod send_method = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: 0
  }];

  // staff id
  int64 staff_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// batch send grooming report card result
message BatchSendGroomingReportCardResult {}

// service
service GroomingReportService {
  // list grooming report card
  rpc ListGroomingReportCard(ListGroomingReportCardParams) returns (ListGroomingReportCardResult);
  // batch delete grooming report
  rpc BatchDeleteGroomingReportCard(BatchDeleteGroomingReportCardParams) returns (BatchDeleteGroomingReportCardResult);
  // batch send grooming report
  rpc BatchSendGroomingReportCard(BatchSendGroomingReportCardParams) returns (BatchSendGroomingReportCardResult);
}
