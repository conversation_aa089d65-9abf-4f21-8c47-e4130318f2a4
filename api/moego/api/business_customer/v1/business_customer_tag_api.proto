syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_customer_tag_defs.proto";
import "moego/models/business_customer/v1/business_customer_tag_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// list customer tag params
message ListCustomerTagParams {}

// list customer tag result
message ListCustomerTagResult {
  // tag list
  repeated moego.models.business_customer.v1.BusinessCustomerTagModel tags = 1;
}

// list customer tag template params
message ListCustomerTagTemplateParams {}

// list customer tag template result
message ListCustomerTagTemplateResult {
  // tag list
  repeated moego.models.business_customer.v1.BusinessCustomerTagNameView tags = 1;
}

// create customer tag params
message CreateCustomerTagParams {
  // customer tag
  moego.models.business_customer.v1.BusinessCustomerTagCreateDef tag = 1 [(validate.rules).message.required = true];
}

// create customer tag result
message CreateCustomerTagResult {
  // customer tag
  moego.models.business_customer.v1.BusinessCustomerTagModel tag = 1;
}

// update customer tag params
message UpdateCustomerTagParams {
  // customer tag id
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // customer tag
  moego.models.business_customer.v1.BusinessCustomerTagUpdateDef tag = 2 [(validate.rules).message.required = true];
}

// update customer tag result
message UpdateCustomerTagResult {}

// sort customer tag params
message SortCustomerTagParams {
  // customer tag id list, should contain all customer tag ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// sort customer tag result
message SortCustomerTagResult {}

// delete customer tag params
message DeleteCustomerTagParams {
  // customer tag id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// delete customer tag result
message DeleteCustomerTagResult {}

// API for customer tag settings
service BusinessCustomerTagService {
  // List customer tags of current company
  rpc ListCustomerTag(ListCustomerTagParams) returns (ListCustomerTagResult);

  // List customer tag template
  rpc ListCustomerTagTemplate(ListCustomerTagTemplateParams) returns (ListCustomerTagTemplateResult);

  // Create a customer tag
  rpc CreateCustomerTag(CreateCustomerTagParams) returns (CreateCustomerTagResult);

  // Update a customer tag
  rpc UpdateCustomerTag(UpdateCustomerTagParams) returns (UpdateCustomerTagResult);

  // Sort customer tags
  rpc SortCustomerTag(SortCustomerTagParams) returns (SortCustomerTagResult);

  // Delete a customer tag
  rpc DeleteCustomerTag(DeleteCustomerTagParams) returns (DeleteCustomerTagResult);
}
