syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_breed_defs.proto";
import "moego/models/business_customer/v1/business_pet_breed_models.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// list pet breed params
message ListPetBreedParams {
  // pet type id
  moego.models.customer.v1.PetType pet_type_id = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// list pet breed result
message ListPetBreedResult {
  // pet breed list
  repeated moego.models.business_customer.v1.BusinessPetBreedModel breeds = 1;
}

// batch upsert pet breed params
message BatchUpsertPetBreedParams {
  // pet type id
  moego.models.customer.v1.PetType pet_type_id = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // pet breeds to create, can be empty
  repeated moego.models.business_customer.v1.BusinessPetBreedUpsertDef breeds_to_create = 5;

  // pet breeds to update, can be empty
  map<int64, moego.models.business_customer.v1.BusinessPetBreedUpsertDef> breeds_to_update = 6;
}

// batch upsert pet breed result
message BatchUpsertPetBreedResult {}

// create pet breed params
message CreatePetBreedParams {
  // pet type id
  moego.models.customer.v1.PetType pet_type_id = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // pet breed to create
  moego.models.business_customer.v1.BusinessPetBreedCreateDef breed = 2 [(validate.rules).message.required = true];
}

// create pet breed result
message CreatePetBreedResult {
  // pet breed
  moego.models.business_customer.v1.BusinessPetBreedModel breed = 1;
}

// update pet breed params
message UpdatePetBreedParams {
  // pet breed id
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // pet breed to update
  moego.models.business_customer.v1.BusinessPetBreedUpdateDef breed = 2 [(validate.rules).message.required = true];
}

// update pet breed result
message UpdatePetBreedResult {}

// delete pet breed params
message DeletePetBreedParams {
  // pet breed id
  int64 id = 2 [(validate.rules).int64.gt = 0];
}

// delete pet breed result
message DeletePetBreedResult {}

// API for pet breed settings
service BusinessPetBreedService {
  // List pet breeds of current company and given pet type
  rpc ListPetBreed(ListPetBreedParams) returns (ListPetBreedResult);

  // Batch upsert pet breeds
  rpc BatchUpsertPetBreed(BatchUpsertPetBreedParams) returns (BatchUpsertPetBreedResult);

  // Add a pet breed
  rpc CreatePetBreed(CreatePetBreedParams) returns (CreatePetBreedResult);

  // Update a pet breed
  rpc UpdatePetBreed(UpdatePetBreedParams) returns (UpdatePetBreedResult);

  // Delete a pet breed
  rpc DeletePetBreed(DeletePetBreedParams) returns (DeletePetBreedResult);
}
