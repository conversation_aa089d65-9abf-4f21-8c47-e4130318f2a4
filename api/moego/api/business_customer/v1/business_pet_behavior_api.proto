syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_behavior_defs.proto";
import "moego/models/business_customer/v1/business_pet_behavior_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// list pet behavior params
message ListPetBehaviorParams {}

// list pet behavior result
message ListPetBehaviorResult {
  // pet behavior list
  repeated moego.models.business_customer.v1.BusinessPetBehaviorModel behaviors = 1;
}

// create pet behavior params
message CreatePetBehaviorParams {
  // pet behavior
  moego.models.business_customer.v1.BusinessPetBehaviorCreateDef behavior = 1 [(validate.rules).message.required = true];
}

// create pet behavior result
message CreatePetBehaviorResult {
  // pet behavior
  moego.models.business_customer.v1.BusinessPetBehaviorModel behavior = 1;
}

// update pet behavior params
message UpdatePetBehaviorParams {
  // pet behavior id
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // pet behavior
  moego.models.business_customer.v1.BusinessPetBehaviorUpdateDef behavior = 2 [(validate.rules).message.required = true];
}

// update pet behavior result
message UpdatePetBehaviorResult {}

// sort pet behavior params
message SortPetBehaviorParams {
  // pet behavior id list, should contain all pet behavior ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// sort pet behavior result
message SortPetBehaviorResult {}

// delete pet behavior params
message DeletePetBehaviorParams {
  // pet behavior id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// delete pet behavior result
message DeletePetBehaviorResult {}

// API for pet behavior settings
service BusinessPetBehaviorService {
  // List pet behaviors of current company
  rpc ListPetBehavior(ListPetBehaviorParams) returns (ListPetBehaviorResult);

  // Create a pet behavior
  rpc CreatePetBehavior(CreatePetBehaviorParams) returns (CreatePetBehaviorResult);

  // Update a pet behavior
  rpc UpdatePetBehavior(UpdatePetBehaviorParams) returns (UpdatePetBehaviorResult);

  // Sort pet behaviors
  rpc SortPetBehavior(SortPetBehaviorParams) returns (SortPetBehaviorResult);

  // Delete a pet behavior
  rpc DeletePetBehavior(DeletePetBehaviorParams) returns (DeletePetBehaviorResult);
}
