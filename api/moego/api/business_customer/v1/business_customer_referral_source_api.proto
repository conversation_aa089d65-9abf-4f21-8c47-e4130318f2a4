syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_customer_referral_source_defs.proto";
import "moego/models/business_customer/v1/business_customer_referral_source_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// list customer referral source template params
message ListCustomerReferralSourceTemplateParams {}

// list customer referral source template result
message ListCustomerReferralSourceTemplateResult {
  // referral source template list
  repeated moego.models.business_customer.v1.BusinessCustomerReferralSourceNameView referral_sources = 1;
}

// list customer referral source params
message ListCustomerReferralSourceParams {}

// list customer referral source result
message ListCustomerReferralSourceResult {
  // referral source list
  repeated moego.models.business_customer.v1.BusinessCustomerReferralSourceModel referral_sources = 1;
}

// create customer referral source params
message CreateCustomerReferralSourceParams {
  // referral source
  moego.models.business_customer.v1.BusinessCustomerReferralSourceCreateDef referral_source = 1 [(validate.rules).message.required = true];
}

// create customer referral source result
message CreateCustomerReferralSourceResult {
  // referral source
  moego.models.business_customer.v1.BusinessCustomerReferralSourceModel referral_source = 1;
}

// update customer referral source params
message UpdateCustomerReferralSourceParams {
  // referral source id
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // customer referral source
  moego.models.business_customer.v1.BusinessCustomerReferralSourceUpdateDef referral_source = 2 [(validate.rules).message.required = true];
}

// update customer referral source result
message UpdateCustomerReferralSourceResult {}

// sort customer referral source params
message SortCustomerReferralSourceParams {
  // customer referral source id list, should contain all customer referral source ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// sort customer referral source result
message SortCustomerReferralSourceResult {}

// delete customer referral source params
message DeleteCustomerReferralSourceParams {
  //  referral source id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// delete customer referral source result
message DeleteCustomerReferralSourceResult {}

// API for customer referral source settings
service BusinessCustomerReferralSourceService {
  // List customer referral source template
  rpc ListCustomerReferralSourceTemplate(ListCustomerReferralSourceTemplateParams) returns (ListCustomerReferralSourceTemplateResult);

  // List customer referral sources of current company
  rpc ListCustomerReferralSource(ListCustomerReferralSourceParams) returns (ListCustomerReferralSourceResult);

  // Create a customer referral source
  rpc CreateCustomerReferralSource(CreateCustomerReferralSourceParams) returns (CreateCustomerReferralSourceResult);

  // Update a customer referral source
  rpc UpdateCustomerReferralSource(UpdateCustomerReferralSourceParams) returns (UpdateCustomerReferralSourceResult);

  // Sort customer referral sources
  rpc SortCustomerReferralSource(SortCustomerReferralSourceParams) returns (SortCustomerReferralSourceResult);

  // Delete a customer referral source
  rpc DeleteCustomerReferralSource(DeleteCustomerReferralSourceParams) returns (DeleteCustomerReferralSourceResult);
}
