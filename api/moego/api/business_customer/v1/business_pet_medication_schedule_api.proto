syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_medication_schedule_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// Create medication schedule params
message CreateMedicationScheduleParams {
  // pet medication schedule
  models.business_customer.v1.BusinessPetMedicationScheduleDef medication_schedule = 1 [(validate.rules).message = {required: true}];
}

// Create medication schedule result
message CreateMedicationScheduleResult {
  // medication id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// Update medication schedule params
message UpdateMedicationScheduleParams {
  // medication id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // pet medication schedule
  models.business_customer.v1.BusinessPetMedicationScheduleDef medication_schedule = 2 [(validate.rules).message = {required: true}];
}

// Update medication schedule result
message UpdateMedicationScheduleResult {}

// Delete medication schedule params
message DeleteMedicationScheduleParams {
  // medication id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// Delete medication schedule result
message DeleteMedicationScheduleResult {}

// List pet's medication schedule params
message ListPetMedicationScheduleParams {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];
}

// List pet's medication schedule result
message ListPetMedicationScheduleResult {
  // pet's medication schedules
  repeated models.business_customer.v1.BusinessPetMedicationScheduleView medication_schedules = 1;
}

// Business pet medication schedule service
service BusinessPetMedicationScheduleService {
  // Create medication schedule
  // Medication display rules: {Medication schedule} {Amount} {Medication unit} {Medication name} {Medication notes}
  rpc CreateMedicationSchedule(CreateMedicationScheduleParams) returns (CreateMedicationScheduleResult);

  // Update medication schedule
  rpc UpdateMedicationSchedule(UpdateMedicationScheduleParams) returns (UpdateMedicationScheduleResult);

  // Delete medication schedule
  rpc DeleteMedicationSchedule(DeleteMedicationScheduleParams) returns (DeleteMedicationScheduleResult);

  // List pet's medication schedule
  rpc ListPetMedicationSchedule(ListPetMedicationScheduleParams) returns (ListPetMedicationScheduleResult);
}
