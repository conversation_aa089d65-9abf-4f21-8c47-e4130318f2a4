syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_code_defs.proto";
import "moego/models/business_customer/v1/business_pet_code_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// list pet code params
message ListPetCodeParams {}

// list pet code result
message ListPetCodeResult {
  // pet code list
  repeated moego.models.business_customer.v1.BusinessPetCodeModel pet_codes = 1;
}

// create pet code params
message CreatePetCodeParams {
  // pet code
  moego.models.business_customer.v1.BusinessPetCodeCreateDef pet_code = 1 [(validate.rules).message.required = true];
}

// create pet code result
message CreatePetCodeResult {
  // pet code
  moego.models.business_customer.v1.BusinessPetCodeModel pet_code = 1;
}

// update pet code params
message UpdatePetCodeParams {
  // pet code id
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // pet code
  moego.models.business_customer.v1.BusinessPetCodeUpdateDef pet_code = 2 [(validate.rules).message.required = true];
}

// sort pet code params
message SortPetCodeParams {
  // pet code id list, should contain all pet code ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// sort pet code result
message SortPetCodeResult {}

// delete pet code params
message DeletePetCodeParams {
  // pet code id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// delete pet code result
message DeletePetCodeResult {}

// update pet code result
message UpdatePetCodeResult {}

// create pet code and binding params
message CreatePetCodeAndBindingParams {
  // pet code
  moego.models.business_customer.v1.BusinessPetCodeCreateDef pet_code = 1 [(validate.rules).message.required = true];

  // pet id
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];
  // comment
  string comment = 3 [(validate.rules).string = {max_len: 512}];
}

// create pet code and binding result
message CreatePetCodeAndBindingResult {
  // pet code
  moego.models.business_customer.v1.BusinessPetCodeModel pet_code = 1;
}

// update  binding params
message UpdateBindingParams {
  // pet  id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];

  // pet code id
  int64 pet_code_id = 2 [(validate.rules).int64.gt = 0];

  // comment
  string comment = 3 [(validate.rules).string = {max_len: 512}];

  // status
  enum Status {
    // unspecified
    STATUS_UNSPECIFIED = 0;
    // binding
    BINDING = 1;
    // unbinding
    UNBINDING = 2;
  }
  // status
  Status status = 4;
}

// update binding result
message UpdateBindingResult {}

// API for pet code settings
service BusinessPetCodeService {
  // List pet codes of current company
  rpc ListPetCode(ListPetCodeParams) returns (ListPetCodeResult);

  // Create a pet code
  rpc CreatePetCode(CreatePetCodeParams) returns (CreatePetCodeResult);

  // Update a pet code
  rpc UpdatePetCode(UpdatePetCodeParams) returns (UpdatePetCodeResult);

  // Sort pet codes
  rpc SortPetCode(SortPetCodeParams) returns (SortPetCodeResult);

  // Delete a pet code
  rpc DeletePetCode(DeletePetCodeParams) returns (DeletePetCodeResult);

  // create pet code and binding
  rpc CreatePetCodeAndBinding(CreatePetCodeAndBindingParams) returns (CreatePetCodeAndBindingResult);

  // update
  rpc UpdateBinding(UpdateBindingParams) returns (UpdateBindingResult);
}
