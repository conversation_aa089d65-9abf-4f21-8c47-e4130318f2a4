syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_customer_merge_enums.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/membership/v1/subscription_models.proto";
import "moego/models/payment/v1/credit_card_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// update customer preferred business params
message UpdateCustomerPreferredBusinessParams {
  // customer id
  int64 customer_id = 1 [(validate.rules).int64 = {gt: 0}];
  // preferred business id
  int64 preferred_business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// update customer preferred business result
message UpdateCustomerPreferredBusinessResult {}

// list duplicate customer groups params
message ListDuplicateCustomerGroupsParams {
  // 最多返回多少组
  int32 max_group_count = 1 [(validate.rules).int32 = {gt: 0}];
}

// list duplicate customer groups result
message ListDuplicateCustomerGroupsResult {
  // groups, no more than max_group_count
  repeated Group groups = 1;

  // group, each group should have at least 2 customers
  message Group {
    // customers
    repeated CustomerView customers = 1;
  }

  // customer info view
  message CustomerView {
    // id
    int64 id = 1;
    // first name
    string first_name = 2;
    // last name
    string last_name = 3;
    // phone number
    string phone_number = 4;
    // email
    string email = 5;
    // pets
    // passed away pets will not be included
    repeated PetView pets = 6;

    // total appointment count
    int32 total_appointment_count = 7;
  }

  // pet info view
  message PetView {
    // id
    int64 id = 1;
    // pet name
    string pet_name = 2;
    // pet type
    models.customer.v1.PetType pet_type = 3;
    // breed
    string breed = 4;
  }
}

// preview customer merge params
message PreviewCustomerMergeParams {
  // all customer ids in a duplicate group
  repeated int64 customer_ids = 1 [(validate.rules).repeated = {
    unique: true
    min_items: 2
    max_items: 10
    items: {
      int64: {gt: 0}
    }
  }];

  // primary customer id, selected by user (optional).
  // if not set, a recommended primary customer will be selected automatically (return in PreviewCustomerMergeResult).
  optional int64 primary_customer_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// preview customer merge result
message PreviewCustomerMergeResult {
  // primary customer id
  int64 primary_customer_id = 1;

  // customers
  repeated CustomerView customers = 2;

  // pets
  // passed away pets will not be included
  repeated PetView pets = 3;

  // contacts
  repeated ContactView contacts = 4;

  // credit cards
  repeated models.payment.v1.CreditCardModelPublicView credit_cards = 5;

  // membership subscriptions
  repeated models.membership.v1.MembershipSubscriptionModel membership_subscriptions = 6;

  // signed agreements
  repeated AgreementView signed_agreements = 7;

  // total appointment count
  int32 total_appointment_count = 8;

  // upcoming appointment count
  int32 upcoming_appointment_count = 9;

  // customer view
  message CustomerView {
    // customer id
    int64 id = 1;
    // avatar path
    string avatar_path = 2;
    // first name
    string first_name = 3;
    // last name
    string last_name = 4;
    // email
    string email = 5;
    // phone number
    // this is the identifier for the customer,
    // but it may not be the primary phone number
    string phone_number = 6;
    // client color
    string client_color = 7;
    // has membership
    bool has_membership = 8;
  }

  // pet view
  message PetView {
    // id
    int64 id = 1;
    // customer id
    int64 customer_id = 2;
    // pet name
    string pet_name = 3;
    // pet type
    models.customer.v1.PetType pet_type = 4;
    // breed
    string breed = 5;
    // avatar path
    string avatar_path = 6;

    // if the pet is duplicate
    bool duplicate = 7;
  }

  // contact view
  message ContactView {
    // id
    int64 id = 1;
    // customer id
    int64 customer_id = 2;
    // first name
    string first_name = 3;
    // last name
    string last_name = 4;
    // phone number
    string phone_number = 5;
    // email
    string email = 6;
  }

  // agreement view
  message AgreementView {
    // id
    int64 id = 1;
    // agreement title
    string title = 2;
  }
}

// merge customers params
message MergeCustomersParams {
  // all customer ids in a duplicate group
  repeated int64 customer_ids = 1 [(validate.rules).repeated = {
    unique: true
    min_items: 2
    max_items: 10
    items: {
      int64: {gt: 0}
    }
  }];

  // primary customer id, selected by user.
  // this customer will be kept after merge
  int64 primary_customer_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// merge customers result
message MergeCustomersResult {}

// check customer merge status params
message CheckCustomerMergeStatusParams {
  // customer id
  int64 customer_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// check customer merge status result
message CheckCustomerMergeStatusResult {
  // status
  models.business_customer.v1.CustomerMergeStatus status = 1;
}

// API for business customer
service BusinessCustomerService {
  // Update customer's preferred business
  rpc UpdateCustomerPreferredBusiness(UpdateCustomerPreferredBusinessParams) returns (UpdateCustomerPreferredBusinessResult);

  // List duplicate customer groups
  rpc ListDuplicateCustomerGroups(ListDuplicateCustomerGroupsParams) returns (ListDuplicateCustomerGroupsResult);

  // preview customer merge
  rpc PreviewCustomerMerge(PreviewCustomerMergeParams) returns (PreviewCustomerMergeResult);

  // Merge customers
  rpc MergeCustomers(MergeCustomersParams) returns (MergeCustomersResult);

  // check customer merge status
  rpc CheckCustomerMergeStatus(CheckCustomerMergeStatusParams) returns (CheckCustomerMergeStatusResult);
}
