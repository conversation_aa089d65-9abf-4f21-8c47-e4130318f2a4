syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_incident_type_defs.proto";
import "moego/models/business_customer/v1/business_pet_incident_type_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// list pet incident type params
message ListPetIncidentTypeParams {
  // include deleted pet incident types
  optional bool is_include_deleted = 1;
}

// list pet incident type result
message ListPetIncidentTypeResult {
  // pet incident type list
  repeated moego.models.business_customer.v1.BusinessPetIncidentTypeModel incident_types = 1;
}

// create pet incident type params
message CreatePetIncidentTypeParams {
  // pet incident type
  moego.models.business_customer.v1.BusinessPetIncidentTypeCreateDef incident_type = 1 [(validate.rules).message.required = true];
}

// create pet incident type result
message CreatePetIncidentTypeResult {
  // pet incident type
  moego.models.business_customer.v1.BusinessPetIncidentTypeModel incident_type = 1;
}

// update pet incident type params
message UpdatePetIncidentTypeParams {
  // pet incident type id
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // pet incident type
  moego.models.business_customer.v1.BusinessPetIncidentTypeUpdateDef incident_type = 2 [(validate.rules).message.required = true];
}

// update pet incident type result
message UpdatePetIncidentTypeResult {}

// sort pet incident type params
message SortPetIncidentTypeParams {
  // pet incident type id list, should contain all pet incident type ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// sort pet incident type result
message SortPetIncidentTypeResult {}

// delete pet incident type params
message DeletePetIncidentTypeParams {
  // pet incident type id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// delete pet incident type result
message DeletePetIncidentTypeResult {}

// API for pet incident type settings
service BusinessPetIncidentTypeService {
  // List pet incident types of current company
  rpc ListPetIncidentType(ListPetIncidentTypeParams) returns (ListPetIncidentTypeResult);

  // Create a pet incident type
  rpc CreatePetIncidentType(CreatePetIncidentTypeParams) returns (CreatePetIncidentTypeResult);

  // Update a pet incident type
  rpc UpdatePetIncidentType(UpdatePetIncidentTypeParams) returns (UpdatePetIncidentTypeResult);

  // Sort pet incident types
  rpc SortPetIncidentType(SortPetIncidentTypeParams) returns (SortPetIncidentTypeResult);

  // Delete a pet incident type
  rpc DeletePetIncidentType(DeletePetIncidentTypeParams) returns (DeletePetIncidentTypeResult);
}
