syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/utils/v1/time_period.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// get customer grooming frequency params
message GetCustomerGroomingFrequencyParams {}

// get customer grooming frequency result
message GetCustomerGroomingFrequencyResult {
  // grooming frequency
  moego.utils.v1.TimePeriod grooming_frequency = 1;
}

// upsert customer grooming frequency params
message UpsertCustomerGroomingFrequencyParams {
  // grooming frequency to upsert, currently only support unit of DAY, WEEK, MONTH
  moego.utils.v1.TimePeriod grooming_frequency = 1 [(validate.rules).message.required = true];

  // apply to all customers, default is false
  bool apply_to_all_customers = 2;
}

// upsert customer grooming frequency result
message UpsertCustomerGroomingFrequencyResult {}

// API for customer preferred frequency settings
service BusinessCustomerPreferredFrequencyService {
  // Get customer grooming frequency
  rpc GetCustomerGroomingFrequency(GetCustomerGroomingFrequencyParams) returns (GetCustomerGroomingFrequencyResult);

  // Upsert customer grooming frequency
  rpc UpsertCustomerGroomingFrequency(UpsertCustomerGroomingFrequencyParams) returns (UpsertCustomerGroomingFrequencyResult);

  // Maybe there is boarding/daycare frequency in the future?
}
