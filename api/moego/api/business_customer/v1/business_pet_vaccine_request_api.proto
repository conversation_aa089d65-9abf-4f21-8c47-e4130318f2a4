syntax = "proto3";

package moego.api.business_customer.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// ReviewPetVaccineRequestParams
message ReviewPetVaccineRequestParams {
  // pet vaccine request id
  int64 id = 1;
}

// ReviewPetVaccineRequestResult
message ReviewPetVaccineRequestResult {
  // When expired = true, it means client / pet / vaccine has been deleted.
  // In this case, client, pet, pet_vaccine_after, pet_vaccine_before will not be set in the result.
  bool expired = 1;
  // pet vaccine (after)
  Vaccine pet_vaccine_after = 2;
  // pet vaccine (before)
  Vaccine pet_vaccine_before = 3;
  // client
  Client client = 4;
  // pet
  Pet pet = 5;
  // create time of vaccine request
  google.protobuf.Timestamp create_time = 6;

  // vaccine
  message Vaccine {
    // vaccine binding id (pet_vaccine_before) or vaccine request id (pet_vaccine_after)
    int64 record_id = 1;
    // vaccine id
    int64 vaccine_id = 2;
    // vaccine name
    string vaccine_name = 3;
    // expiration date, may not exist
    optional google.type.Date expiration_date = 4;
    // vaccine document urls
    repeated string document_urls = 5;
  }

  // client
  message Client {
    // first name
    string first_name = 1;
    // last name
    string last_name = 2;
    // phone number
    string phone_number = 3;
  }

  // pet
  message Pet {
    // pet name
    string pet_name = 1;
    // avatar path
    string avatar_path = 2;
    // pet type id
    moego.models.customer.v1.PetType pet_type = 3;
    // breed
    string breed = 4;
  }
}

// ApprovePetVaccineRequestParams
message ApprovePetVaccineRequestParams {
  // pet vaccine request id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // notification id
  int64 notification_id = 2 [(validate.rules).int64.gt = 0];

  // expiration date, optional
  // if set, will override expiration date in pet vaccine request
  google.type.Date expiration_date = 3;
}

// ApprovePetVaccineRequestResult
message ApprovePetVaccineRequestResult {}

// DeclinePetVaccineRequestParams
message DeclinePetVaccineRequestParams {
  // pet vaccine request id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // notification id
  int64 notification_id = 2 [(validate.rules).int64.gt = 0];
  // force to decline.
  // use case: pet vaccine request 可能已经被处理，或者 client / pet / vaccine 被删除，导致 pet vaccine request 无法被处理,
  // 但是 pending review notification 没清除, 此时需要传 force = true, 把通知清除, 相当于 dismiss 掉这个无效的 pet vaccine request.
  // 如果 pet vaccine request 已经被 approve, force = true 不会改变 approve 的结果, 也不会抛异常.
  // 如果 force = false, 已经 approve 的 pet vaccine request 不可被 decline, 会抛一个异常
  bool force = 3;
}

// DeclinePetVaccineRequestResult
message DeclinePetVaccineRequestResult {}

// API for pet vaccine Request
service BusinessPetVaccineRequestService {
  // Review a pet vaccine request
  rpc ReviewPetVaccineRequest(ReviewPetVaccineRequestParams) returns (ReviewPetVaccineRequestResult);
  // Approve a pet vaccine request
  rpc ApprovePetVaccineRequest(ApprovePetVaccineRequestParams) returns (ApprovePetVaccineRequestResult);
  // Decline a pet vaccine request
  rpc DeclinePetVaccineRequest(DeclinePetVaccineRequestParams) returns (DeclinePetVaccineRequestResult);
}
