syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_feeding_schedule_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// Create feeding schedule params
message CreateFeedingScheduleParams {
  // pet feeding schedule
  models.business_customer.v1.BusinessPetFeedingScheduleDef feeding_schedule = 1 [(validate.rules).message = {required: true}];
}

// Create feeding schedule result
message CreateFeedingScheduleResult {
  // feeding id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// Update feeding schedule params
message UpdateFeedingScheduleParams {
  // feeding id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // pet feeding schedule
  models.business_customer.v1.BusinessPetFeedingScheduleDef feeding_schedule = 2 [(validate.rules).message = {required: true}];
}

// Update feeding schedule result
message UpdateFeedingScheduleResult {}

// Delete feeding schedule params
message DeleteFeedingScheduleParams {
  // feeding id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// Delete feeding schedule result
message DeleteFeedingScheduleResult {}

// List pet's feeding schedule params
message ListPetFeedingScheduleParams {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];
}

// List pet's feeding schedule result
message ListPetFeedingScheduleResult {
  // pet's feeding schedules
  repeated models.business_customer.v1.BusinessPetFeedingScheduleView feeding_schedules = 1;
}

// Business pet feeding schedule service
service BusinessPetFeedingScheduleService {
  // Create feeding schedule
  // Feeding display rules: {Feeding schedule} {Amount} {Feeding unit} {Feeding type} {Feeding source} {Feeding instruction}
  rpc CreateFeedingSchedule(CreateFeedingScheduleParams) returns (CreateFeedingScheduleResult);

  // Update feeding schedule
  rpc UpdateFeedingSchedule(UpdateFeedingScheduleParams) returns (UpdateFeedingScheduleResult);

  // Delete feeding schedule
  rpc DeleteFeedingSchedule(DeleteFeedingScheduleParams) returns (DeleteFeedingScheduleResult);

  // List pet's feeding schedule
  rpc ListPetFeedingSchedule(ListPetFeedingScheduleParams) returns (ListPetFeedingScheduleResult);
}
