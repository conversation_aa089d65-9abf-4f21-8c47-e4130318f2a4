// @since 2024-08-05 15:34:20
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_customer_setting_defs.proto";
import "moego/models/business_customer/v1/business_customer_setting_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// get customer setting params
message GetCustomerSettingParams {}

// get customer setting result
message GetCustomerSettingResult {
  // customer setting
  models.business_customer.v1.BusinessCustomerSettingModel setting = 1;
}

// update customer creation setting params
message UpdateCustomerCreationSettingParams {
  // customer creation setting
  models.business_customer.v1.BusinessCustomerCreationSettingUpdateDef creation_setting = 1 [(validate.rules).message.required = true];
}

// update customer creation setting result
message UpdateCustomerCreationSettingResult {}

// the business customer setting service
service BusinessCustomerSettingService {
  // get all customer settings
  rpc GetCustomerSetting(GetCustomerSettingParams) returns (GetCustomerSettingResult);

  // update customer creation setting
  rpc UpdateCustomerCreationSetting(UpdateCustomerCreationSettingParams) returns (UpdateCustomerCreationSettingResult);

  // update customer xxx setting
}
