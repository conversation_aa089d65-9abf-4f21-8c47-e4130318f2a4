syntax = "proto3";

package moego.api.offering.v1;

import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1;offeringapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.offering.v1";

// ListServiceStaffOverrideRule params
message ListServiceStaffOverrideRuleParams {
  // business id，staff 和 location 有关联，需要传 business_id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
  // service id list
  repeated int64 service_id_list = 2 [(validate.rules).repeated = {max_items: 1000}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3 [(validate.rules).message.required = true];
}

// ListServiceStaffOverrideRule result
message ListServiceStaffOverrideRuleResult {
  // service staff override rule list
  repeated ServiceStaffOverrideRule service_staff_override_rule_list = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;

  // service staff override rule
  message ServiceStaffOverrideRule {
    // service id
    int64 service_id = 1;
    // staff id
    int64 staff_id = 2;
    // price
    optional double price = 3;
    // duration
    optional int32 duration = 4;
  }
}

// ServiceStaffOverrideRule service
service ServiceStaffOverrideRuleService {
  // List service staff override rule
  rpc ListServiceStaffOverrideRule(ListServiceStaffOverrideRuleParams) returns (ListServiceStaffOverrideRuleResult);
}
