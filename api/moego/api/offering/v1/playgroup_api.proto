syntax = "proto3";

package moego.api.offering.v1;

import "moego/models/offering/v1/playgroup_defs.proto";
import "moego/models/offering/v1/playgroup_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1;offeringapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.offering.v1";

// list playgroup params
message ListPlaygroupParams {
  // pagination
  moego.utils.v2.PaginationRequest pagination = 1;
}

// list playgroup result
message ListPlaygroupResult {
  // playgroups
  repeated moego.models.offering.v1.PlaygroupModel playgroups = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// create playgroup params
message CreatePlaygroupParams {
  // playgroup def
  moego.models.offering.v1.CreatePlaygroupDef playgroup = 1;
}

// create playgroup result
message CreatePlaygroupResult {
  // playgroup
  moego.models.offering.v1.PlaygroupModel playgroup = 1;
}

// update playgroup params
message UpdatePlaygroupParams {
  // playgroup def
  moego.models.offering.v1.UpdatePlaygroupDef playgroup = 1;
}

// update playgroup result
message UpdatePlaygroupResult {
  // playgroup
  moego.models.offering.v1.PlaygroupModel playgroup = 1;
}

// delete playgroup params
message DeletePlaygroupParams {
  // playgroup id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// delete playgroup result
message DeletePlaygroupResult {}

// sort playgroup params
message SortPlaygroupParams {
  // playgroup id list, should contain all playgroup ids
  repeated int64 ids = 1 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// sort playgroup result
message SortPlaygroupResult {}

// playgroup service
service PlaygroupService {
  // list playgroup
  rpc ListPlaygroup(ListPlaygroupParams) returns (ListPlaygroupResult);
  // create playgroup
  rpc CreatePlaygroup(CreatePlaygroupParams) returns (CreatePlaygroupResult);
  // update playgroup
  rpc UpdatePlaygroup(UpdatePlaygroupParams) returns (UpdatePlaygroupResult);
  // delete playgroup
  rpc DeletePlaygroup(DeletePlaygroupParams) returns (DeletePlaygroupResult);
  // sort playgroup
  rpc SortPlaygroup(SortPlaygroupParams) returns (SortPlaygroupResult);
}
