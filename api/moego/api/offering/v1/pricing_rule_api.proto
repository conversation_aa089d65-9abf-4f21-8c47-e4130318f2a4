// @since 2024-07-30 11:14:10
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.offering.v1;

import "moego/models/offering/v1/pricing_rule_defs.proto";
import "moego/models/offering/v1/pricing_rule_enums.proto";
import "moego/models/offering/v1/pricing_rule_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1;offeringapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.offering.v1";

// upsert pricing rule params
message UpsertPricingRuleParams {
  // pricing_rule def
  moego.models.offering.v1.PricingRuleUpsertDef pricing_rule_upsert_def = 1 [(validate.rules).message = {required: true}];
  // apply to upcoming appointments
  bool apply_to_upcoming_appointments = 2;
}

// upsert pricing rule result
message UpsertPricingRuleResult {
  // the created pricing_rule
  moego.models.offering.v1.PricingRuleModel pricing_rule = 1;
}

// get pricing rule params
message GetPricingRuleParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get pricing rule result
message GetPricingRuleResult {
  // the pricing rule
  moego.models.offering.v1.PricingRuleModel pricing_rule = 1;
}

// list pricing rule params
message ListPricingRulesParams {
  // filter
  optional moego.models.offering.v1.ListPricingRuleFilter filter = 3;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 9;
}

// list pricing rule result
message ListPricingRulesResult {
  // the pricing rule list
  repeated moego.models.offering.v1.PricingRuleModel pricing_rules = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// calculate pricing rule params
message CalculatePricingRuleParams {
  // pet detail list
  repeated moego.models.offering.v1.PetDetailCalculateDef pet_details = 1 [(validate.rules).repeated = {min_items: 1}];
  // pricing rule def, if empty, will use the best pricing rule
  optional moego.models.offering.v1.PricingRuleUpsertDef pricing_rule_upsert_def = 2;
  // calculate for preview
  bool is_preview = 3;
}

// calculate pricing rule result
message CalculatePricingRuleResult {
  // pet detail list
  repeated moego.models.offering.v1.PetDetailCalculateResultDef pet_details = 1;
  // formula for preview calculation
  optional string formula = 2;
  // used pricing rule list
  repeated moego.models.offering.v1.PricingRuleModel pricing_rules = 3;
}

// delete pricing rule params
message DeletePricingRuleParams {
  // the unique id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // apply to upcoming appointments
  bool apply_to_upcoming_appointments = 2;
}

// delete pricing rule result
message DeletePricingRuleResult {}

// get associated services params
message ListAssociatedServicesParams {
  // service item: grooming/boarding/daycare
  moego.models.offering.v1.ServiceItemType service_item_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service type: service/addon
  moego.models.offering.v1.ServiceType service_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // filter
  optional Filter filter = 3;
  // filter
  message Filter {
    // exclude pricing rule id
    optional int64 exclude_pricing_rule_id = 1 [(validate.rules).int64 = {gt: 0}];
    // rule group type
    optional moego.models.offering.v1.RuleGroupType rule_group_type = 2 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
  }
}

// get associated services result
message ListAssociatedServicesResult {
  // associated service ids
  repeated int64 associated_service_ids = 1;
  // all service associated
  bool all_service_associated = 2;
}

// check rule name is exist params
message CheckRuleNameParams {
  // rule name
  string rule_name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 150
  }];
  // exclude pricing rule id
  optional int64 exclude_pricing_rule_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// check rule name is exist result
message CheckRuleNameResult {
  // rule name is exist
  bool is_exist = 1;
}

// check configuration params
message CheckConfigurationParams {
  // pricing_rule def
  moego.models.offering.v1.PricingRuleUpsertDef pricing_rule_upsert_def = 1 [(validate.rules).message = {required: true}];
}

// check configuration result
message CheckConfigurationResult {
  // is valid
  bool is_valid = 1;
  // error message
  optional string error_message = 2;
}

// pricing rule service
service PricingRuleService {
  option deprecated = true;
  // create pricing rule, deprecated, use v2 instead
  rpc UpsertPricingRule(UpsertPricingRuleParams) returns (UpsertPricingRuleResult) {
    option deprecated = true;
  }
  // get pricing rule, deprecated, use v2 instead
  rpc GetPricingRule(GetPricingRuleParams) returns (GetPricingRuleResult) {
    option deprecated = true;
  }
  // list pricing rule, deprecated, use v2 instead
  rpc ListPricingRules(ListPricingRulesParams) returns (ListPricingRulesResult) {
    option deprecated = true;
  }
  // calculate pricing rule, deprecated, use v2 instead
  rpc CalculatePricingRule(CalculatePricingRuleParams) returns (CalculatePricingRuleResult) {
    option deprecated = true;
  }
  // delete pricing rule, deprecated, use v2 instead
  rpc DeletePricingRule(DeletePricingRuleParams) returns (DeletePricingRuleResult) {
    option deprecated = true;
  }
  // list associated services, used for pricing rule selecting services, deprecated, use v2 instead
  rpc ListAssociatedServices(ListAssociatedServicesParams) returns (ListAssociatedServicesResult) {
    option deprecated = true;
  }
  // check rule name is exist, deprecated, use v2 instead
  rpc CheckRuleName(CheckRuleNameParams) returns (CheckRuleNameResult) {
    option deprecated = true;
  }
  // check configuration, deprecated, use v2 instead
  rpc CheckConfiguration(CheckConfigurationParams) returns (CheckConfigurationResult) {
    option deprecated = true;
  }
}
