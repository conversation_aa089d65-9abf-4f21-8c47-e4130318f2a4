syntax = "proto3";

package moego.api.risk_control.v1;

import "moego/models/risk_control/v1/verification_code_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/risk_control/v1;riskcontrolapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.risk_control.v1";

// send verification code request
message SendVerificationCodeRequest {
  // verification identifier, contains account information and applicable scenarios
  models.risk_control.v1.VerificationIdentifierDef identifier = 1 [(validate.rules).message = {required: true}];
}

// send verification code response
message SendVerificationCodeResponse {
  // verification token, used for verification together with the verification code
  string token = 1;
}

// verification code API
service VerificationCodeService {
  // Send verification code to phone number or email verify identity.
  // anonymous session: for register, login
  // login session: change phone number, change email, add phone number, etc.
  //
  // Error codes:
  // - CODE_VERIFICATION_CODE_SENT_COUNT_LIMITED: the number of verification codes sent to the same phone number or email address has reached the limit.
  // - CODE_VERIFICATION_CODE_SEND_INTERVAL_NOT_REACHED: the interval between sending verification codes has not reached the limit.
  rpc SendVerificationCode(SendVerificationCodeRequest) returns (SendVerificationCodeResponse);
}
