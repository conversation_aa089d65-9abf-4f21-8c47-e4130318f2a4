syntax = "proto3";
package moego.api.engagement.v1;

import "moego/models/engagement/v1/setting.proto";
import "moego/models/engagement/v1/setting_defs.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/engagement/v1;engagementapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.engagement.v1";

//setting service是对外提供的方法
service SettingService {
  // 获取Setting值的方法
  rpc GetSetting(GetSettingParams) returns (GetSettingResult);
  // 修改Setting值的方法
  rpc UpdateSetting(UpdateSettingParams) returns (UpdateSettingResult);
  // 获取tmp calling seats
  rpc GetTmpCallingSeats(TmpCallingSeatsParams) returns (TmpCallingSeatsResult);
}

// GetSettingParams
message GetSettingParams {}

//GetSettingResult
message GetSettingResult {
  //Setting
  moego.models.engagement.v1.Setting setting = 1;
  // seats setting
  moego.models.engagement.v1.SeatsSetting seats_setting = 2;
}

//保存Setting值的请求对象
message UpdateSettingParams {
  //修改Setting的请求参数
  moego.models.engagement.v1.UpdateSettingDef update_setting = 1;
  // update seat settings
  optional moego.models.engagement.v1.UpdateSeatsSettingDef update_seats_setting = 2;
}

//保存Setting值的响应对象
message UpdateSettingResult {
  //修改Setting的响应参数
  moego.models.engagement.v1.Setting setting = 1;
  // seats setting
  models.engagement.v1.SeatsSetting seats_setting = 2;
}

//TmpCallingSeatsParams
message TmpCallingSeatsParams {}

//TmpCallingSeatsResult
message TmpCallingSeatsResult {
  //tmp calling seats
  repeated moego.models.engagement.v1.TmpSeat tmp_calling_seats = 1;
  // seats limit
  uint32 seats_limit = 2;
  // is seats limit reached
  bool is_seats_limit_reached = 3;
}
