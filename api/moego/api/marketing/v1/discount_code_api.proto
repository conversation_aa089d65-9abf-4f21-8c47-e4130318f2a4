// @since 2023-09-11 14:34:19
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.marketing.v1;

import "google/protobuf/empty.proto";
import "moego/models/marketing/v1/discount_code_defs.proto";
import "moego/models/marketing/v1/discount_code_enums.proto";
import "moego/models/marketing/v1/discount_code_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/marketing/v1;marketingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.marketing.v1";

// generate discount code response
message GenerateDiscountCodeResponse {
  // discount code
  string discount_code = 1;
}

// check discount code request
message CheckDiscountCodeRequest {
  // code
  string discount_code = 1 [(validate.rules).string = {
    min_len: 4
    max_len: 20
  }];
}

// check discount code response
message CheckDiscountCodeResponse {
  // is duplicate
  bool is_duplicate = 1;
}

// create discount_code request
message CreateDiscountCodeRequest {
  // discount code
  string discount_code = 1 [(validate.rules).string = {
    min_len: 4
    max_len: 100
  }];
  // description
  optional string description = 2 [(validate.rules).string = {max_len: 200}];
  // discount amount
  double amount = 3 [(validate.rules).double = {
    gt: 0
    lt: 999999999
  }];
  // discount type
  moego.models.marketing.v1.DiscountCodeType type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // start date
  string start_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end date
  optional string end_date = 6 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // allowed all thing
  bool allowed_all_thing = 7;
  // allowed all services
  bool allowed_all_services = 8;
  // allowed services
  repeated int64 service_ids = 9;
  // allowed add ons
  repeated int64 add_on_ids = 10;
  // allowed all products
  bool allowed_all_products = 11;
  // allowed products
  repeated int64 product_ids = 12;

  // allowed all clients
  bool allowed_all_clients = 13;
  // allowed new clients
  bool allowed_new_clients = 14;
  // clients group
  string clients_group = 15 [(validate.rules).string = {
    min_len: 0
    max_len: 10000
  }];
  // allowed clients
  repeated int64 client_ids = 16;

  // limit usage
  int32 limit_usage = 17 [(validate.rules).int32 = {
    gte: 0
    lt: 1000000
  }];
  // limit number per client
  int32 limit_number_per_client = 18 [(validate.rules).int32 = {
    gte: 0
    lt: 1000000
  }];
  // limit budget
  int32 limit_budget = 19 [(validate.rules).int32 = {
    gte: 0
    lt: 1000000
  }];

  // auto apply association
  bool auto_apply_association = 20;
  // enable online booking
  bool enable_online_booking = 21;

  // allowed locations
  repeated int64 location_ids = 22;

  // expiry_def
  moego.models.marketing.v1.ExpiryDef expiry_def = 23;
}

// create discount code response
message CreateDiscountCodeResponse {
  // id
  int64 id = 1;
}

// edit discount_code request
message EditDiscountCodeRequest {
  // discount code
  string discount_code = 1 [(validate.rules).string = {
    min_len: 4
    max_len: 20
  }];
  // description
  string description = 2 [(validate.rules).string = {max_len: 200}];
  // discount amount
  double amount = 3 [(validate.rules).double = {
    gt: 0
    lt: 999999999
  }];
  // discount type
  moego.models.marketing.v1.DiscountCodeType type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // start date
  string start_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end date (blank string for no end date)
  string end_date = 6;

  // allowed all thing
  bool allowed_all_thing = 7;
  // allowed all services
  bool allowed_all_services = 8;
  // allowed services
  repeated int64 service_ids = 9;
  // allowed add ons
  repeated int64 add_on_ids = 10;
  // allowed all products
  bool allowed_all_products = 11;
  // allowed products
  repeated int64 product_ids = 12;

  // allowed all clients
  bool allowed_all_clients = 13;
  // allowed new clients
  bool allowed_new_clients = 14;
  // clients group
  string clients_group = 15 [(validate.rules).string = {
    min_len: 0
    max_len: 10000
  }];
  // allowed clients
  repeated int64 client_ids = 16;

  // limit usage
  int32 limit_usage = 17 [(validate.rules).int32 = {
    gte: 0
    lt: 1000000
  }];
  // limit number per client
  int32 limit_number_per_client = 18 [(validate.rules).int32 = {
    gte: 0
    lt: 1000000
  }];
  // limit budget
  int32 limit_budget = 19 [(validate.rules).int32 = {
    gte: 0
    lt: 1000000
  }];

  // auto apply association
  bool auto_apply_association = 20;
  // enable online booking
  bool enable_online_booking = 21;

  // unique id
  int64 id = 22;

  // allowed locations
  repeated int64 location_ids = 23;

  // expiry def
  optional moego.models.marketing.v1.ExpiryDef expiry_def = 24;
}

// edit discount code response
message EditDiscountCodeResponse {
  // id
  int64 id = 1;
}

// get discount_code request
message GetDiscountCodeRequest {
  // code id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get discount_code response
message GetDiscountCodeResponse {
  // discount code model
  moego.models.marketing.v1.DiscountCodeModel discount_code_model = 1;
  // discount code summary
  moego.models.marketing.v1.DiscountCodeSummaryDef discount_code_summary_def = 2;
}

// get discount code request
message GetDiscountCodeListRequest {
  // pagination request
  moego.utils.v2.PaginationRequest pagination = 1 [(validate.rules).message.required = true];
  // status
  repeated moego.models.marketing.v1.DiscountCodeStatus status = 2 [(validate.rules).repeated = {max_items: 5}];
  // code
  optional string discount_code = 3 [(validate.rules).string = {max_len: 20}];
}

// get discount code response
message GetDiscountCodeListResponse {
  // pagination response
  moego.utils.v2.PaginationResponse pagination = 1;
  // discount code list
  repeated moego.models.marketing.v1.DiscountCodeCompositeView discount_code_composite_views = 2;
}

// get discount code log request
message GetDiscountCodeLogListRequest {
  // pagination request
  moego.utils.v2.PaginationRequest pagination = 1 [(validate.rules).message.required = true];
  // code id
  int64 id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get discount code log response
message GetDiscountCodeLogListResponse {
  // pagination response
  moego.utils.v2.PaginationResponse pagination = 1;
  // discount code log list
  repeated moego.models.marketing.v1.DiscountCodeLogCompositeView discount_code_log_composite_views = 2;
}

// get discount code log request
message GetDiscountCodeLogOverviewRequest {
  // code id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get discount code log response
message GetDiscountCodeLogOverviewResponse {
  // total usage
  int32 total_usage = 1;
  // total client
  int32 total_client = 2;
  // discount sales
  double discount_sales = 3;
  // invoice sales
  double invoice_sales = 4;
}

// archive discount code request
message ChangeStatusRequest {
  // code id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // status
  moego.models.marketing.v1.DiscountCodeStatus status = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // expiry def,if change status to active, it may need to reset expiry time
  optional moego.models.marketing.v1.ExpiryDef expiry_def = 3;
}

// archive discount code request
message ChangeStatusResponse {
  // success
  bool success = 1;
}

// check discount code valid for customer request
message CheckDiscountCodeValidForCustomerRequest {
  // code name
  string code_name = 1 [(validate.rules).string = {max_len: 20}];
  // service ids
  repeated int64 service_ids = 2;
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 3;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 4;
  }
  // appointment date
  optional string appointment_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
}

// check discount code valid for customer response
message CheckDiscountCodeValidForCustomerResponse {
  // discount code model online booking view
  moego.models.marketing.v1.DiscountCodeModelOnlineBookingView discount_code_model_online_booking_view = 1;
}

// get business discount code config request
message GetBusinessDiscountCodeConfigRequest {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1 [(validate.rules).string = {max_len: 255}];
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2 [(validate.rules).string = {max_len: 255}];
  }
}

// get business discount code config response
message GetBusinessDiscountCodeConfigResponse {
  // business has valid discount code
  bool has_valid_discount_code = 1;
}

// the discount_code service
service DiscountCodeService {
  // generate discount code
  rpc GenerateDiscountCode(google.protobuf.Empty) returns (GenerateDiscountCodeResponse);
  // check discount code
  rpc CheckDiscountCode(CheckDiscountCodeRequest) returns (CheckDiscountCodeResponse);
  // create discount code
  rpc CreateDiscountCode(CreateDiscountCodeRequest) returns (CreateDiscountCodeResponse);
  // edit discount code
  rpc EditDiscountCode(EditDiscountCodeRequest) returns (EditDiscountCodeResponse);
  // get discount code
  rpc GetDiscountCode(GetDiscountCodeRequest) returns (GetDiscountCodeResponse);
  // get discount code list
  rpc GetDiscountCodeList(GetDiscountCodeListRequest) returns (GetDiscountCodeListResponse);
  // get discount code log overview
  rpc GetDiscountCodeLogOverview(GetDiscountCodeLogOverviewRequest) returns (GetDiscountCodeLogOverviewResponse);
  // get discount code log list
  rpc GetDiscountCodeLogList(GetDiscountCodeLogListRequest) returns (GetDiscountCodeLogListResponse);
  // change status
  rpc ChangeStatus(ChangeStatusRequest) returns (ChangeStatusResponse);
  // check discount code valid for customer
  rpc CheckDiscountCodeValidForCustomer(CheckDiscountCodeValidForCustomerRequest) returns (CheckDiscountCodeValidForCustomerResponse);
  // get business discount code config
  rpc GetBusinessDiscountCodeConfig(GetBusinessDiscountCodeConfigRequest) returns (GetBusinessDiscountCodeConfigResponse);
}
