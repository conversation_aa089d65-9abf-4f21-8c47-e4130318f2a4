syntax = "proto3";

package moego.api.zendesk.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/zendesk/v1;zendeskapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.zendesk.v1";

// get zendesk jwt request
message GetZendeskJwtRequest {}

// get zendesk jwt response
message GetZendeskJwtResponse {
  // jwt string
  string jwt = 1;
}

// zendesk API
service ZendeskService {
  // Get zendesk JWT string.
  rpc GetZendeskJwt(GetZendeskJwtRequest) returns (GetZendeskJwtResponse);
}
