syntax = "proto3";

package moego.api.organization.v1;

import "google/protobuf/timestamp.proto";
import "google/type/latlng.proto";
import "moego/models/organization/v1/staff_tracking.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/organization/v1;organizationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.organization.v1";

// staff tracking service
service StaffTrackingService {
  // upload staff location
  rpc CreateStaffTracking(CreateStaffTrackingParams) returns (CreateStaffTrackingResult);
  // staff location list
  rpc ListStaffTracking(ListStaffTrackingParams) returns (ListStaffTrackingResult);
  // batch create staff tracking
  rpc BatchCreateStaffTracking(BatchCreateStaffTrackingParams) returns (BatchCreateStaffTrackingResult);
}

// upload staff location params
message CreateStaffTrackingParams {
  // coordinate, include latitude and longitude
  google.type.LatLng coordinate = 1 [(validate.rules).message.required = true];
}

// upload staff location result
message CreateStaffTrackingResult {
  //result
  bool result = 1;
}

// list staff location params
message ListStaffTrackingParams {
  // staff id list
  repeated int64 staff_ids = 1 [(validate.rules).repeated = {min_items: 1}];
}

// list staff location result
message ListStaffTrackingResult {
  //staff_trackings
  repeated moego.models.organization.v1.StaffTrackingModel staff_trackings = 1;
}

// batch create staff tracking params
message BatchCreateStaffTrackingParams {
  // param
  message Param {
    // time
    google.protobuf.Timestamp timestamp = 1 [(validate.rules).timestamp.required = true];
    // coordinate, include latitude and longitude
    google.type.LatLng coordinate = 2 [(validate.rules).message.required = true];
  }
  // staff tracking list
  repeated Param params = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1440
  }];
}

// batch create staff tracking result
message BatchCreateStaffTrackingResult {}
