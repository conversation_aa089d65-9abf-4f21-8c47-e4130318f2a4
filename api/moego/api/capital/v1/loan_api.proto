syntax = "proto3";

package moego.api.capital.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/capital/v1/loan_enums.proto";
import "moego/models/capital/v1/loan_models.proto";
import "moego/service/capital/v1/loan_service.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/capital/v1;capitalapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.capital.v1";

// Response for GetLoanEligibility
// Currently only an is_eligible field is available. Could add more fields (e.g. ineligible_reason) in the future.
message GetLoanEligibilityResponse {
  // Whether the specified loan user is eligible for MoeGo capital.
  bool is_eligible = 1;
  // Ineligible reason.
  moego.models.capital.v1.LoanIneligibleReason ineligible_reason = 2;
}

// Request for GetLoanEligibilityFlags
message GetLoanEligibilityFlagsRequest {}

// Response for GetLoanEligibilityFlags
message GetLoanEligibilityFlagsResponse {
  // Whether the user has setup MGP
  bool is_mgp_setup = 1;
  // Whether the user has MGP primary type
  bool is_mgp_primary = 2;
  // Whether the user has MGP primary type long enough
  bool is_mgp_primary_long_enough = 3;
}

// Request for GetOfferDetail
message GetOfferDetailRequest {
  // The ID of the offer
  string offer_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }];
}

// Response for GetOfferDetail
message GetOfferDetailResponse {
  // The offer detail
  moego.models.capital.v1.LoanOfferModel offer = 1 [(validate.rules).message.required = true];
  // Next unfinished interval. If null, there may be no scheduled intervals yet, or all intervals are finished. For now,
  // this is only applicable for Term Loan.
  optional moego.models.capital.v1.LoanOfferIntervalModel next_unfinished_interval = 2;
  // Terms count of all. This is only applicable for Term Loan.
  optional int32 total_terms = 3 [(validate.rules).int32.gte = 0];
  // Terms count which are unfinished. This is only applicable for Term Loan.
  optional int32 unfinished_terms = 4 [(validate.rules).int32.gte = 0];
}

// Response for ListOffers
message ListOffersResponse {
  // The offers list
  repeated moego.models.capital.v1.LoanOfferModel offers = 1;
}

// Request for ListOffersV2
message ListOffersV2Request {}

// Response for ListOffersV2
message ListOffersV2Response {
  // Offer and its details
  message OfferAndDetails {
    // The offer
    moego.models.capital.v1.LoanOfferModel offer = 1 [(validate.rules).message.required = true];
    // Next unfinished interval. If null, there may be no scheduled intervals yet, or all intervals are finished. For
    // now, this is only applicable for Term Loan.
    optional moego.models.capital.v1.LoanOfferIntervalModel next_unfinished_interval = 2;
    // Terms count of all. This is only applicable for Term Loan.
    optional int32 total_terms = 3 [(validate.rules).int32.gte = 0];
    // Terms count which are unfinished. This is only applicable for Term Loan.
    optional int32 unfinished_terms = 4 [(validate.rules).int32.gte = 0];
  }

  // The offers list
  repeated OfferAndDetails offers = 1;
}

// Response for ListAllBusinessesOffers
message ListAllBusinessesOffersResponse {
  // The offers list
  repeated moego.models.capital.v1.LoanOfferModel offers = 1;
}

// Request for ListAllBusinessesOffersV2
message ListAllBusinessesOffersV2Request {
  // Only returns offers from specified channels. If empty, offers from all channels are returned.
  repeated moego.models.capital.v1.LoanChannel channels = 1;
}

// Response for ListAllBusinessesOffersV2
message ListAllBusinessesOffersV2Response {
  // The offers list
  repeated moego.models.capital.v1.LoanOfferModel offers = 1;
}

// Request for GetRepaymentIntervals
message GetRepaymentIntervalsRequest {
  // The ID of the offer
  string offer_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }];
}

// Response for GetRepaymentIntervals
message GetRepaymentIntervalsResponse {
  // The current interval of the loan offer
  repeated moego.models.capital.v1.LoanOfferIntervalModel intervals = 1;
}

// Request for CreateLink
message CreateLinkRequest {
  // link type
  moego.service.capital.v1.CreateLinkRequest.AccountLinkType type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // The ID of the loan offer. Required if `type` is `APPLY_OFFER`, `ELIGIBILITY_REVIEW` or `MANUALLY_REPAY`.
  optional string offer_id = 3 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 64
  }];
  // The URL to redirect to after the user finishes operations at the apply link site. Require to be validated for
  // APPLY_OFFER, ELIGIBILITY_REVIEW and MANUALLY_REPAY.
  string return_url = 4 [(validate.rules).string = {
    uri: true
    ignore_empty: true
  }];
  // The URL to redirect to if the old link expires. Require to be validated for APPLY_OFFER, ELIGIBILITY_REVIEW and
  // MANUALLY_REPAY.
  string refresh_url = 5 [(validate.rules).string = {
    uri: true
    ignore_empty: true
  }];
}

// Response for CreateLink
message CreateLinkResponse {
  // The URL of the apply link
  string url = 1 [(validate.rules).string = {
    uri: true
    ignore_empty: true
  }];
  // Time when the URL or token will expire, in second timestamp. 0 if it never expires.
  google.protobuf.Timestamp expire_at = 2 [(validate.rules).timestamp.gte.seconds = 0];
  // The token to connect to the link.
  optional string connect_token = 3 [(validate.rules).string = {min_len: 1}];
}

// Request for GetRepayments
message GetRepaymentsRequest {
  // The ID of the loan offer
  string offer_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }];
  // Pagination query
  moego.utils.v2.PaginationRequest pagination = 2;
}

// Response for GetRepayments
message GetRepaymentsResponse {
  // The list of repayment records.
  repeated moego.models.capital.v1.LoanOfferRepaymentTransactionModel repayments = 1;
  // Pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// Response for GetNotableUpdates
message GetNotableUpdatesResponse {
  // Whether the user has updates. This will only be true when the user just received the paid out funds from a loan.
  bool have_updates = 1;
}

// Request for GetOfferNotableUpdates
message GetOfferNotableUpdatesRequest {}

// Response for GetOfferNotableUpdates
message GetOfferNotableUpdatesResponse {
  // Notable updates for offers, e.g. an offer is rejected.
  repeated moego.models.capital.v1.NotableOfferUpdate notable_offer_updates = 1;
}

// Request for DismissOfferNotableUpdate
message DismissOfferNotableUpdateRequest {
  // The ID of the loan offer, to dismiss the corresponding offer update
  string offer_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }];
}

// Response for DismissOfferNotableUpdate
message DismissOfferNotableUpdateResponse {}

// Request for GetOnboardingStatus
message GetOnboardingStatusRequest {
  // The channel.
  moego.models.capital.v1.LoanChannel channel_name = 1;
}

// Response for GetOnboardingStatus
message GetOnboardingStatusResponse {
  // The onboarding status of the business
  moego.models.capital.v1.LoanOnboardingStatus onboarding_status = 1;
  // The offer type of the business has chosen
  moego.models.capital.v1.LoanOfferType offer_type = 2;
}

// Request for AdminSyncOffer
message AdminSyncOfferRequest {
  // Sync mode
  enum SyncMode {
    // Unspecified
    SYNC_MODE_UNSPECIFIED = 0;
    // Sync an offer by ID. The offer must be already existing in our DB.
    SYNC_SINGLE_BY_ID = 1;
    // Sync all offers from all the channel.
    SYNC_ALL = 2;
  }

  // Offer ID
  // Required for SYNC_SINGLE_BY_ID. For backward compatibility, pass empty string for other mode.
  string offer_id = 1 [(validate.rules).string = {max_len: 64}];
  // Sync mode. For backward compatibility, will behave as SYNC_SINGLE_BY_ID if this field is not given.
  optional SyncMode sync_mode = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// The service for loan domain.
service LoanService {
  // Get the eligibility information for specific loan user.
  // Frontend should not call this rpc later. Use GetLoanEligibilityFlags instead.
  rpc GetLoanEligibility(google.protobuf.Empty) returns (GetLoanEligibilityResponse) {
    option deprecated = true;
  }
  // Get the eligibility information for specific loan user.
  rpc GetLoanEligibilityFlags(GetLoanEligibilityFlagsRequest) returns (GetLoanEligibilityFlagsResponse);
  // Get the detail of a specific Loan Offer.
  rpc GetOfferDetail(GetOfferDetailRequest) returns (GetOfferDetailResponse);
  // List all Loan Offers for current business in all status.
  // Frontend should not call this rpc later. Use ListOffersV2 instead. This method only returns Stripe offers.
  rpc ListOffers(google.protobuf.Empty) returns (ListOffersResponse) {
    option deprecated = true;
  }
  // List all Loan Offers for current business in all status, with additional details.
  rpc ListOffersV2(ListOffersV2Request) returns (ListOffersV2Response);
  // List all Loan Offers of all businesses for current company, in all status.
  // Frontend should not call this rpc later. Use ListAllBusinessesOffersV2 instead. This method only returns Stripe
  // offers.
  rpc ListAllBusinessesOffers(google.protobuf.Empty) returns (ListAllBusinessesOffersResponse) {
    option deprecated = true;
  }
  // List all Loan Offers of all businesses for current company, in all status.
  rpc ListAllBusinessesOffersV2(ListAllBusinessesOffersV2Request) returns (ListAllBusinessesOffersV2Response);
  // Get all intervals for a specified offer.
  rpc GetRepaymentIntervals(GetRepaymentIntervalsRequest) returns (GetRepaymentIntervalsResponse);
  // Create a link for a Loan Offer.
  rpc CreateLink(CreateLinkRequest) returns (CreateLinkResponse);
  // Get the repayments for specific Loan Offer.
  rpc GetRepayments(GetRepaymentsRequest) returns (GetRepaymentsResponse);
  // Get updates worth noting, for current business.
  rpc GetNotableUpdates(google.protobuf.Empty) returns (GetNotableUpdatesResponse);
  // Dismiss notable updates, if any.
  rpc DismissNotableUpdates(google.protobuf.Empty) returns (google.protobuf.Empty);
  // Get updates worth noting, for offers.
  rpc GetOfferNotableUpdates(GetOfferNotableUpdatesRequest) returns (GetOfferNotableUpdatesResponse);
  // Dismiss an offer's notable update, if any.
  rpc DismissOfferNotableUpdate(DismissOfferNotableUpdateRequest) returns (DismissOfferNotableUpdateResponse);
  // Get the onboarding status for current business/company/etc.
  rpc GetOnboardingStatus(GetOnboardingStatusRequest) returns (GetOnboardingStatusResponse);
  // Admin-only: Sync offer and its transactions
  rpc AdminSyncOffer(AdminSyncOfferRequest) returns (google.protobuf.Empty);
}
