// 这个 service 应该归属业务域，不应该在 order 域，只是现在是 order 服务实现的，先放这里。

syntax = "proto3";

package moego.service.order.v2;

import "google/type/money.proto";
import "moego/models/offering/v1/service_models.proto";
import "moego/models/order/v1/deposit_rule_defs.proto";
import "moego/models/order/v1/deposit_rule_models.proto";
import "moego/models/order/v1/order_detail_models.proto";
import "moego/models/order/v1/order_line_item_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2;ordersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.order.v2";

// Deposit rule service
service DepositRuleService {
  // Create a deposit rule
  rpc CreateDepositRule(CreateDepositRuleRequest) returns (CreateDepositRuleResponse);
  // Update a deposit rule
  rpc UpdateDepositRule(UpdateDepositRuleRequest) returns (UpdateDepositRuleResponse);
  // Delete a deposit rule
  rpc DeleteDepositRule(DeleteDepositRuleRequest) returns (DeleteDepositRuleResponse);
  // List deposit rules. Rules are sorted by: No expiration (created time desc) > Date range desc.
  // Pagination is not supported yet.
  rpc ListDepositRules(ListDepositRulesRequest) returns (ListDepositRulesResponse);

  // PreviewDepositOrder previews a deposit order for given service items, etc.
  rpc PreviewDepositOrder(PreviewDepositOrderRequest) returns (PreviewDepositOrderResponse);

  // Migrate OB payment settings to deposit rules.
  rpc MigrateToDepositRules(MigrateToDepositRulesRequest) returns (MigrateToDepositRulesResponse);
}

// Request for CreateDepositRule
message CreateDepositRuleRequest {
  // Company ID
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Business ID
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // Def
  moego.models.order.v1.CreateDepositRuleDef rule = 3;
}

// Response for CreateDepositRule
message CreateDepositRuleResponse {
  // Created rule
  moego.models.order.v1.DepositRule rule = 1;
}

// Request for UpdateDepositRule
message UpdateDepositRuleRequest {
  // Rule ID
  int64 rule_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Def
  moego.models.order.v1.UpdateDepositRuleDef rule = 2;
}

// Response for UpdateDepositRule
message UpdateDepositRuleResponse {
  // Updated rule
  moego.models.order.v1.DepositRule rule = 1;
}

// Request for DeleteDepositRule
message DeleteDepositRuleRequest {
  // Rule ID
  int64 rule_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// Response for DeleteDepositRule
message DeleteDepositRuleResponse {}

// Request for ListDepositRules
message ListDepositRulesRequest {
  // Business ID
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// Response for ListDepositRules
message ListDepositRulesResponse {
  // Rules
  repeated moego.models.order.v1.DepositRule rules = 1;
}

// Request for PreviewDepositOrder
message PreviewDepositOrderRequest {
  // Company ID
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Business ID
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // Customer ID，**对于还没创建的新 customer （包括 Lead）则不传**。
  optional int64 customer_id = 3;
  // start date, format: yyyy-mm-dd
  // appointment/booking request 不方便构造，所以简单定义个 start date 先用着了……
  string appointment_start_date = 4;
  // Service pricing of the appointment, after applying pricing rules. Note that the discounts, membership discounts and
  // taxes are not applied yet.
  repeated ServicePricingDetail service_pricing_details = 5;
  // Include convenience fee
  bool include_convenience_fee = 6;

  // Appointment 域现在是用 PetDetail 来承载服务记录信息，太重了，暂时没找到其他合适的 pb，所以这里另行定义。字段基本参照
  // CalculateServiceAmount。
  message ServicePricingDetail {
    // The pet id receiving this service.
    int64 pet_id = 1;
    // Service detail. Note that the price of it is the original price.
    moego.models.offering.v1.CustomizedServiceView service = 2;
    // The unit price after applying pricing rules.
    google.type.Money unit_price = 3;
    // The quantity.
    int64 quantity = 4;
    // The total price after applying pricing rules.
    google.type.Money total_price = 5;
    // Associated service ID, if this service is add-on. 0 if not associated.
    int64 associated_service_id = 6;
    // External UUID of the line for this pricing detail, will be returned in the preview result.
    string external_uuid = 7;
  }
}

// Response for PreviewDepositOrder
message PreviewDepositOrderResponse {
  // Preview of the deposit order. If not set, it means no deposit rule is matched.
  optional models.order.v1.OrderDetailModelV1 deposit_order_detail_preview = 1;
  // Deposit items to be created.
  repeated PriceItemByRule deposit_order_price_items = 2;

  // Price item calculated from deposit rules
  message PriceItemByRule {
    // The deposit price item.
    moego.models.order.v1.PriceDetailModel.PriceItem price_item = 1;
    // Matched rule that is used to calculate this item.
    int64 rule_id = 2;
    // External UUID of the line for this price item.
    string external_uuid = 3;
  }
}

// Request for MigrateToDepositRules
message MigrateToDepositRulesRequest {
  // Business IDs to migrate.
  repeated int64 business_ids = 1;
}

// Response for MigrateToDepositRules
message MigrateToDepositRulesResponse {
  // Migration errors, if any.
  repeated Error errors = 1;

  // Migration error.
  message Error {
    // Business ID
    int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
    // Error message
    string message = 2;
  }
}
