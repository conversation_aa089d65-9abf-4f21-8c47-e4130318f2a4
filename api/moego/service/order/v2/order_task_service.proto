syntax = "proto3";

package moego.service.order.v2;

import "google/protobuf/empty.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2;ordersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.order.v2";

// order task service
service OrderTaskService {
  // retry redeem promotion
  rpc RetryRedeemPromotion(google.protobuf.Empty) returns (google.protobuf.Empty);
}
