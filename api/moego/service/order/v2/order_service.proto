syntax = "proto3";

package moego.service.order.v2;

import "google/type/decimal.proto";
import "google/type/money.proto";
import "moego/models/order/v1/order_detail_models.proto";
import "moego/models/order/v1/order_enums.proto";
import "moego/models/order/v1/order_line_item_models.proto";
import "moego/models/order/v1/order_models.proto";
import "moego/models/order/v1/order_promotion_models.proto";
import "moego/models/promotion/v1/promotion.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2;ordersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.order.v2";

// order service
service OrderService {
  // 创建order payment, 并调用payment
  rpc PayOrder(PayOrderRequest) returns (PayOrderResponse);

  // 创建订单
  rpc CreateOrder(CreateOrderRequest) returns (CreateOrderResponse);
  // 预览创建订单, response with mock order data
  rpc PreviewCreateOrder(PreviewCreateOrderRequest) returns (PreviewCreateOrderResponse);
}

// pay order request
message PayOrderRequest {
  // Order ID.
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Company ID.
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // Business ID.
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // Staff ID. 0 for pay online
  int64 staff_id = 4 [(validate.rules).int64 = {gte: 0}];
  // total amount：  total_amount = amount + payment_tips
  // 支付金额 = 填写的金额
  google.type.Money amount = 5 [(validate.rules).message.required = true];
  // payment tips before create = 支付前选择的tips
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: 想不出更好的命名。 --)
  optional google.type.Money payment_tips_before_create = 6;
}

// pay order response
message PayOrderResponse {
  // Order payment.
  models.order.v1.OrderPaymentModel order_payment = 1;
}

// create order request, like create order v1, but with less useless fields.
message CreateOrderRequest {
  // Pre-defined source type.
  models.order.v1.OrderSourceType source_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Source ID. 结合 source type 表示不同的 ID.
  int64 source_id = 2 [(validate.rules).int64 = {gt: 0}];
  // Company ID.
  int64 company_id = 3 [(validate.rules).int64 = {gt: 0}];
  // Business ID.
  int64 business_id = 4 [(validate.rules).int64 = {gt: 0}];
  // Staff ID. 0 for pay online
  int64 staff_id = 5 [(validate.rules).int64 = {gte: 0}];
  // Customer ID.
  int64 customer_id = 6 [(validate.rules).int64 = {gt: 0}];
  // order title
  string title = 7;
  // description
  string description = 8;

  // Order items.
  repeated PreviewCreateOrderRequest.CartItem items = 11 [(validate.rules).repeated = {min_items: 1}];
  // tips amount.
  google.type.Money tips_amount = 12;
  // applied promotions.
  PreviewCreateOrderRequest.AppliedPromotions applied_promotions = 13;
}

// CreateOrderResponse.
message CreateOrderResponse {
  // Order.
  models.order.v1.OrderDetailModelV1 order = 1;
}

// Preview creates order request.
message PreviewCreateOrderRequest {
  // Pre-defined source type.
  models.order.v1.OrderSourceType source_type = 1 [(validate.rules).enum = {defined_only: true}];
  // Source ID. 结合 source type 表示不同的 ID.
  int64 source_id = 2 [(validate.rules).int64 = {gt: 0}];
  // Company ID.
  int64 company_id = 3 [(validate.rules).int64 = {gt: 0}];
  // Business ID.
  int64 business_id = 4 [(validate.rules).int64 = {gt: 0}];
  // Customer ID.
  int64 customer_id = 5 [(validate.rules).int64 = {gt: 0}];

  // Order items.
  repeated CartItem items = 11 [(validate.rules).repeated = {min_items: 1}];
  // tips amount.
  google.type.Money tips_amount = 12;
  // Promotions.
  oneof promotions {
    // 自动 apply 可用的优惠. 不含 store credit.
    bool auto_apply_promotions = 13;
    // 手动指定的优惠.
    AppliedPromotions applied_promotions = 14;
  }

  // 手动指定的优惠.
  message AppliedPromotions {
    // 指定的优惠列表.
    repeated Promotion promotions = 1;
  }

  // 购物车中的商品。
  // 包含的信息应当是 OrderItemModel 的 真子集.
  message CartItem {
    // 商品类型，即老订单模型中的 ObjectType.
    models.order.v1.ItemType item_type = 2;
    // 商品 ID. 与 item type 一起表示具体的商品的 ID.
    int64 item_id = 3;
    // 订单领域之外的唯一 KEY，用于唯一确定一个待结算的实体。
    // 可以是 petDetail ID, fulfillment id, etc.
    string external_uuid = 4;
    // Description, 简单的文字描述.
    string description = 5;
    // Name
    string name = 6;
    // Staff ID.
    int64 staff_id = 7 [(validate.rules).int64 = {gte: 0}];
    // Pet ID.
    int64 pet_id = 8 [(validate.rules).int64 = {gte: 0}];

    // 单价，存在详情时，会保证 UnitPrice 是由 Detail 计算得出.
    google.type.Money unit_price = 11;
    // Subtotal 详情.
    // 传入了 subtotal detail 时，会按照 subtotal / quantity 计算得到 unit price。
    models.order.v1.PriceDetailModel subtotal_detail = 12;
    // 数量.
    int32 quantity = 13;
    // 税.
    Tax tax = 14;
  }

  // 使用的优惠.
  message Promotion {
    // 作用目标. 通过 CartItemModel 的 external_uuid 进行匹配和定位.
    repeated string cart_item_external_uuids = 1;

    // 使用的优惠.
    oneof promotion {
      // pkg / membership / 非一次性的 discount.
      models.promotion.v1.Source coupon_source = 11;
      // 一次性的 discount.
      models.order.v1.OneTimeDiscountCode one_time_discount = 12;
      // Store credit.
      google.type.Money store_credit = 13;
    }
  }

  // 税.
  message Tax {
    // Tax ID.
    int64 id = 1 [(validate.rules).int64 = {gte: 0}];
    // Tax name.
    string name = 2 [(validate.rules).string = {min_len: 0}];
    // Tax rate, 百分比，保留 2 位小数。
    // 12.34 表示 12.34%。 计算时会转换成 0.1234。
    google.type.Decimal rate = 3;
  }
}

// Preview creates order response.
message PreviewCreateOrderResponse {
  // Order.
  models.order.v1.OrderDetailModelV1 order = 1;
  // Applied promotions.
  PreviewCreateOrderRequest.AppliedPromotions applied_promotions = 2;
}
