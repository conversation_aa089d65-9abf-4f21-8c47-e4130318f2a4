syntax = "proto3";

package moego.service.order.v1;

import "moego/models/business_customer/v1/business_customer_merge_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1;ordersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.order.v1";

// order merge client request
message MergeCustomerOrderDataRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // merge relation
  models.business_customer.v1.BusinessCustomerMergeRelationDef merge_relation = 2;
}

// order merge client response
message MergeCustomerOrderDataResponse {
  // success
  bool success = 1;
}

//order merge service
service OrderMergeService {
  //Order merge customer data include : order
  rpc MergeCustomerOrderData(MergeCustomerOrderDataRequest) returns (MergeCustomerOrderDataResponse);
}
