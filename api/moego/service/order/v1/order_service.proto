syntax = "proto3";

package moego.service.order.v1;

import "google/protobuf/empty.proto";
import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/models/order/v1/order_detail_models.proto";
import "moego/models/order/v1/order_enums.proto";
import "moego/models/order/v1/order_line_discount_models.proto";
import "moego/models/order/v1/order_line_extra_fee_models.proto";
import "moego/models/order/v1/order_line_item_models.proto";
import "moego/models/order/v1/order_line_tax_models.proto";
import "moego/models/order/v1/order_models.proto";
import "moego/models/order/v1/refund_order_enums.proto";
import "moego/models/order/v1/refund_order_models.proto";
import "moego/models/payment/v1/payment_method_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1;ordersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.order.v1";

/**
 * Request body for GetOrder service
 */
message CreateOrderRequest {
  // order
  moego.models.order.v1.OrderModel order = 1;
  // line items
  repeated moego.models.order.v1.OrderLineItemModel line_items = 2;
  // if not calculate again
  optional bool not_calculate = 3;
  // auto apply discount code
  optional bool auto_apply_discount = 4;
}

/**
 * Request body for UpdateOrder service
 */
message UpdateOrderRequest {
  // order
  moego.models.order.v1.OrderModel order = 1;
  // line items
  repeated moego.models.order.v1.OrderLineItemModel line_items = 2;
  // line taxes
  repeated moego.models.order.v1.OrderLineTaxModel line_taxes = 3;
  // line discounts
  repeated moego.models.order.v1.OrderLineDiscountModel line_discounts = 4;
  // line extra fees
  repeated moego.models.order.v1.OrderLineExtraFeeModel line_extra_fees = 5;
  // last update version for verification
  optional int32 latest_version = 6;
}

/**
 * Response body for create order
 */
message CreateOrderResponse {
  // order id
  int64 id = 1;
}

/**
 * Response body for update order
 */
message UpdateOrderResponse {
  // update record count
  int32 record = 1;
}

/**
 * Request body for GetOrder service
 */
message GetOrderRequest {
  // business id
  optional int64 business_id = 1;
  // order id
  optional int64 id = 2;
  // source id
  optional int64 source_id = 3;
  // source type
  optional string source_type = 4;
  //guid
  optional string guid = 5;
  // status
  optional sint32 status = 6;
  // get latest order(与 source_id and source_type 一起使用)
  optional bool latest = 7;
}

/**
 * get order list request params
 */
message GetOrderListRequest {
  // business id
  optional int64 business_id = 1;
  // order ids
  repeated int64 order_ids = 2;
  // order source type
  optional string source_type = 3;
  // source ids
  repeated int64 source_ids = 4;
  // if query detail: if true, return order with items, tax, discount, extra fee else only order
  optional bool query_detail = 5;
  // status
  repeated int32 status = 6;
  // include all orders (extra, deposit, etc.), if false, only return origin order
  optional bool include_extra_order = 7;
}

/**
 * get order list response
 */
message GetOrderListResponse {
  // order list
  repeated moego.models.order.v1.OrderDetailModel order_list = 1;
}

/**
 * incremental updating order
 */
message UpdateOrderIncrRequest {
  // order id
  int64 order_id = 1;
  // order for update
  optional moego.models.order.v1.OrderModel order = 2;
  // line items
  repeated moego.models.order.v1.OrderLineItemModel line_items = 3;
  // line taxes
  repeated moego.models.order.v1.OrderLineTaxModel line_taxes = 4;
  // line discounts
  repeated moego.models.order.v1.OrderLineDiscountModel line_discounts = 5;
  // line extra fees
  repeated moego.models.order.v1.OrderLineExtraFeeModel line_extra_fees = 6;
  // last update version for verification
  optional int32 latest_version = 7;
  // true will check amount and return refund channel wont save change
  optional bool check_refund = 8;
}

/**
 * update response
 */
message UpdateOrderIncrResponse {
  // update records count
  int32 record = 1;
  // trigger refund
  moego.models.order.v1.RefundChannelResponse refund_channel = 2;
}

/**
   retail invoice query request params
*/
message GetRetailInvoicesRequest {
  // business id
  int64 business_id = 1;
  // customer id
  optional int64 customer_id = 2;
  // order type
  optional string type = 3;
  // query detail
  optional bool query_detail = 4;

  // keyword for title and description
  optional string keyword = 5;
  // page size
  optional int32 page_size = 6;
  // page num
  optional int32 page_num = 7;
  // sort by
  optional string sort_by = 8;
  // order by
  optional string order_by = 9;

  // query by create time start
  optional int64 start_time = 10;
  // query by create time end
  optional int64 end_time = 11;
}

/**
   retail invoice query response
*/
message GetRetailInvoicesResponse {
  // query total count
  int64 count = 1;
  // query result list, paging
  repeated moego.models.order.v1.OrderDetailModel orders = 2;
}

/**
 * query order item detail request params
 */
message GetOrderItemDetailRequest {
  // business id, query by primary key, not necessary
  optional int64 business_id = 1;
  // order id, query by primary key, not necessary
  repeated int64 order_ids = 2;
  // item id list for batch query
  repeated int64 line_item_ids = 3;
  // item type
  optional string item_type = 4;
}

/**
 * query order item detail response
 */
message GetOrderItemDetailResponse {
  // item id list for batch query
  repeated moego.models.order.v1.OrderLineItemModel line_items = 1;
  // item tax list
  repeated moego.models.order.v1.OrderLineTaxModel line_taxes = 2;
}

/**
 * query tips order list request params
 */
message GetTipsOrderListRequest {
  // business id
  int64 business_id = 1;
  // source id list, tips only for grooming id
  repeated int64 source_ids = 2;
  // source type
  optional string source_type = 3;
  // paging num
  int32 page_num = 4;
  // paging size
  int32 page_size = 5;
  // business id list
  repeated int64 business_ids = 6;
}

/**
 * query tips order list request params
 */
message GetTipsOrderListResponse {
  // total count
  int32 count = 1;
  // order list
  repeated moego.models.order.v1.OrderModel orders = 2;
}

// modify item tax input
message ModifyItemTaxInput {
  // business id
  int64 business_id = 1 [(validate.rules) = {
    int64: {gt: 0}
  }];
  // order id
  int32 order_id = 2 [(validate.rules) = {
    int32: {gt: 0}
  }];
  // service/product/service charge id
  int32 object_id = 3 [(validate.rules) = {
    int32: {gt: 0}
  }];
  // item type
  string item_type = 4;
  // tax id
  int32 tax_id = 5 [(validate.rules) = {
    int32: {gt: 0}
  }];
  // operator id
  int64 operator_id = 6 [(validate.rules) = {
    int64: {gt: 0}
  }];
  //trigger refund
  optional bool check_refund = 7;
  // company id
  int64 company_id = 8 [(validate.rules) = {
    int64: {gt: 0}
  }];
}

// modify item tax response
message ModifyItemTaxOutput {
  // result
  bool result = 1;
  //refund info
  optional moego.models.order.v1.RefundChannelResponse refund_channel = 2;
}

// add/remove service charge input
message OperateOrderServiceChargeInput {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // order id
  int64 order_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service charge id list
  repeated int64 service_charge_id = 3 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
  // operator id
  int64 operator_id = 4;
  // true will check amount and return refund channel wont save change
  optional bool check_refund = 5;
  // company id
  int64 company_id = 6;
}

// add service charge to order output
message OperateOrderServiceChargeOutput {
  // result
  bool result = 1;
  //trigger refund
  optional moego.models.order.v1.RefundChannelResponse refund_channel = 2;
}

//set tip request
message SetTipsRequest {
  // invoice id
  int64 invoice_id = 1;
  // business id
  int64 business_id = 2;
  // staff id
  int64 staff_id = 3;
  // value type
  string value_type = 4;
  // value
  double value = 5;
  // omit result
  bool omit_result = 6;
  // last modified time
  int64 last_modified_time = 7;
  // true will check amount and return refund channel wont save change
  optional bool check_refund = 8;
}

//set tip response
message SetTipsResult {
  // result
  bool result = 1;
  //trigger refund
  optional moego.models.order.v1.RefundChannelResponse refund_channel = 2;
}

// ListOrdersRequest
message ListOrdersRequest {
  // pagination
  moego.utils.v2.PaginationRequest pagination = 1;
  // business id
  repeated int64 business_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 500
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // filter
  message Filter {
    // order ids
    repeated int64 ids = 1 [(validate.rules).repeated = {
      min_items: 1
      max_items: 500
      ignore_empty: true
      items: {
        int64: {gt: 0}
      }
    }];
    // status
    repeated int32 statuses = 2 [(validate.rules).repeated = {
      min_items: 1
      max_items: 20
      ignore_empty: true
    }];
    // last updated time range
    google.type.Interval last_updated_time_range = 3;
  }
  // filter
  optional Filter filter = 3;
}

// ListOrdersResponse
message ListOrdersResponse {
  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 1;
  // appointment detail
  repeated moego.models.order.v1.OrderModel orders = 2;
}

// get related order request
message GetOrderHistoryRequest {
  // origin order id
  int64 origin_order_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get related order response
message GetOrderHistoryResponse {
  // order history view
  repeated moego.models.order.v1.OrderModel order_models = 1;
}

// update extra order request
message UpdateExtraOrderRequest {
  // extra order id
  int64 extra_order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // extra charge model
  moego.models.order.v1.OrderModel order = 2;
  // line items
  repeated moego.models.order.v1.OrderLineItemModel line_items = 3;
  // line taxes
  repeated moego.models.order.v1.OrderLineTaxModel line_taxes = 4;
  // line discounts
  repeated moego.models.order.v1.OrderLineDiscountModel line_discounts = 5;
  // line extra fees
  repeated moego.models.order.v1.OrderLineExtraFeeModel line_extra_fees = 6;
  // added pet detail ids
  repeated int64 added_pet_detail_ids = 7 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // deleted pet detail ids
  repeated int64 deleted_pet_detail_ids = 8 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// update extra order response
message UpdateExtraOrderResponse {
  // update record count
  int32 record = 1;
}

// get grooming detail relation request
message GetGroomingDetailRelationRequest {
  // order id
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // is origin order
  bool is_origin_order = 2;
}

// get grooming detail relation response
message GetGroomingDetailRelationResponse {
  // grooming detail relation models
  repeated models.order.v1.GroomingDetailRelationModel grooming_detail_relation_models = 1;
}

// edit staff params
message EditStaffCommissionParams {
  // order id
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet detail list
  repeated moego.models.order.v1.EditStaffCommissionItem edit_staff_commission_items = 2;
}

// edit staff result
message EditStaffCommissionResult {}

// upgrade invoice reinvent request
message UpgradeInvoiceReinventRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// upgrade invoice reinvent response
message UpgradeInvoiceReinventResponse {}

// check invoice reinvent request
message CheckInvoiceReinventRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// check invoice reinvent response
message CheckInvoiceReinventResponse {
  // is in invoice reinvent whitelist
  bool is_in_invoice_reinvent_whitelist = 1;
}

// create order payment request
message PayOrderRequest {
  // Order ID.
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Company ID.
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // Business ID.
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // Customer ID.
  int64 customer_id = 4 [(validate.rules).int64 = {gt: 0}];
  // Staff ID. 0 for pay online
  int64 staff_id = 5 [(validate.rules).int64 = {gte: 0}];
  // Payment method id 对应 payment的method_id
  int64 payment_method_id = 6 [(validate.rules).int64 = {gt: 0}];
  // Payment method  对应 payment的method
  string payment_method = 7 [(validate.rules).string = {max_len: 255}];
  // Payment method extra
  moego.models.payment.v1.PaymentMethodExtra payment_method_extra = 8 [(validate.rules).message.required = true];
  // Payment method vendor 对应 payment的vendor
  string payment_method_vendor = 9 [(validate.rules).string = {max_len: 255}];
  // is online
  bool is_online = 10;
  // is deposit
  bool is_deposit = 11;
  // paid by
  string paid_by = 12;
  // payment status
  models.order.v1.OrderPaymentStatus payment_status = 13 [(validate.rules).enum = {defined_only: true}];

  // total amount：  total_amount = amount + payment_tips + convenience_fee.
  google.type.Money total_amount = 14 [(validate.rules).message.required = true];
  // Payment amount.
  google.type.Money amount = 15 [(validate.rules).message.required = true];

  // payment tips before create
  optional google.type.Money payment_tips_before_create = 16;
  // convenience fee
  optional google.type.Money convenience_fee = 17;
  // payment id
  optional int64 payment_id = 18;
}

// create order payment response
message PayOrderResponse {
  // Order payment.
  models.order.v1.OrderPaymentModel order_payment = 1 [(validate.rules).message.required = true];
}

// update order payment request
message UpdateOrderPaymentRequest {
  // Order payment ID.
  int64 order_payment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // payment status
  optional models.order.v1.OrderPaymentStatus payment_status = 2 [(validate.rules).enum = {defined_only: true}];
  // Payment method extra
  optional moego.models.payment.v1.PaymentMethodExtra payment_method_extra = 3 [(validate.rules).message.required = true];
  // Payment method vendor
  optional string payment_method_vendor = 4 [(validate.rules).string = {max_len: 255}];
  // total amount：  total_amount = amount + payment_tips.
  // 更新场景： 1. terminal 添加 tips 2. debit card 取消convenience fee
  optional google.type.Money total_amount = 5 [(validate.rules).message.required = true];
  // payment tips after create
  optional google.type.Money payment_tips_after_create = 6;
  // processing fee
  optional google.type.Money processing_fee = 7;
  // payment status reason
  // 搭配 Payment status 展示，进入该状态的原因:
  //     Failed -> 失败的原因
  //     Canceled -> 取消的原因
  optional string payment_status_reason = 8 [(validate.rules).string = {max_len: 1023}];

  // order id
  optional int64 order_id = 9;
  // customer id
  optional int64 customer_id = 10;
  // convenience fee
  optional google.type.Money convenience_fee = 11;
  // payment id
  optional int64 payment_id = 12;
  // payment tips before create
  optional google.type.Money payment_tips_before_create = 13;
}

// update order payment response
message UpdateOrderPaymentResponse {
  // Order payment.
  models.order.v1.OrderPaymentModel order_payment = 1;
}

// sync order payment and order request
message SyncOrderPaymentAndOrderResponse {
  // check count
  int32 check_count = 1;
  // sync count
  int32 sync_count = 2;
}

// Preview refund order request.
message PreviewRefundOrderRequest {
  // Order ID.
  int64 order_id = 1;
  // Refund mode.
  models.order.v1.RefundMode refund_mode = 2 [(validate.rules).enum = {defined_only: true}];
  // Business ID.
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // 从哪 一/几 笔 Order Payment 中退款.
  repeated service.order.v1.RefundOrderRequest.OrderPayment source_order_payments = 4;

  // Refund by.
  oneof refund_by {
    // For REFUND_BY_ITEM.
    RefundOrderRequest.RefundByItem refund_by_item = 11;

    // For REFUND_BY_PAYMENT.
    RefundOrderRequest.RefundByPayment refund_by_payment = 12;
  }
}

// Preview refund order response.
message PreviewRefundOrderResponse {
  // Order.
  models.order.v1.OrderDetailModelV1 order = 1;
  // Previewed refund order.
  models.order.v1.RefundOrderDetailModel preview_refund_order = 2;
  // Previewed related refund orders. On refunding deducted deposit, multiple refund orders may be created. The related
  // refund orders will be set to this field.
  repeated RelatedRefundOrder related_refund_orders = 8;
  // 与 Order item 一一对应，表示每一个 item 可以退的数量/金额.
  // 特别的，如果退款模式是 By Payment，这里不会有内容.
  repeated RefundableItem refundable_items = 3;
  // 可以退的 Tips.
  google.type.Money refundable_tips = 4;
  // Refund flags.
  RefundFlags refund_flags = 5;
  // 可以退的 Order Payments.
  repeated RefundableOrderPayment refundable_order_payments = 6;
  // 可以退的 ConvenienceFee.
  google.type.Money refundable_convenience_fee = 7;

  // Related refund order detail.
  message RelatedRefundOrder {
    // Order.
    models.order.v1.OrderDetailModelV1 order = 1;
    // Previewed refund order detail.
    models.order.v1.RefundOrderDetailModel preview_refund_order = 2;
  }

  // Refund flags.
  message RefundFlags {
    // 退款的 Order Payment 是否支持组合退款.
    // TODO(yunxiang): 这个逻辑有点子怪，考虑列出所有能退的。在提交的时候判断一下选中的能否覆盖需要退的总额。
    bool can_combine_order_payments = 1;
    // Convenience Fee 是否可选.
    // 当设置为 False 时，Convenience Fee 必须选中.
    bool is_refund_convenience_fee_optional = 2;
  }

  // Refundable item.
  message RefundableItem {
    // Order item ID.
    int64 order_item_id = 1;
    // Refund item mode.
    models.order.v1.RefundItemMode refund_item_mode = 2;
    // 是否可以退款.
    bool is_refundable = 3;

    // 依照 RefundItemMode 的取值有不同的内容.
    oneof refund_by {
      option (validate.required) = true;

      // 按数量退的时候，可以退的数量.
      int32 refundable_quantity = 11;
      // 按金额退的时候，可以退的金额.
      google.type.Money refundable_amount = 12;
    }
  }

  // 可以退的 Order Payment.
  message RefundableOrderPayment {
    // Order payment ID.
    int64 order_payment_id = 1;
    // 该渠道还能退的金额.
    google.type.Money refundable_amount = 2;
  }
}

// Preview refund order payments request.
// 纯计算接口.
message PreviewRefundOrderPaymentsRequest {
  // 需要退款的金额.
  google.type.Money refund_amount = 1;
  // 需要退款的金额的标志.
  RefundOrderRequest.RefundByPayment.RefundAmountFlags refund_amount_flag = 2;
  // 需要分摊退款金额的 Order Payments.
  // 这里不需要真实的 Order Payments, 只是复用结构.
  // 只需要金额相关的字段齐全即可.
  repeated models.order.v1.OrderPaymentModel source_order_payments = 3;
}

// Preview refund order payments response.
message PreviewRefundOrderPaymentsResponse {
  // 总的退款金额.
  google.type.Money refund_total_amount = 1;
  // 总的退款金额中 **包含** 的 Convenience Fee.
  google.type.Money refund_convenience_fee = 2;
  // 各 Order Payment 的退款明细.
  repeated models.order.v1.RefundOrderPaymentModel refund_order_payments = 3;
}

// Refund order request
message RefundOrderRequest {
  // Order ID.
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Business ID.
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // Staff ID.
  int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
  // 退款原因.
  string refund_reason = 4 [(validate.rules).string = {max_len: 1023}];
  // Refund mode.
  models.order.v1.RefundMode refund_mode = 5 [(validate.rules).enum = {defined_only: true}];
  // 从哪 一/几 笔 Order Payment 中退款.
  // 移除了 min_items 校验：在支持 deposit 抵扣后，一笔订单可能会被订金全额抵扣，因此可能不需要传 source_order_payments.
  repeated OrderPayment source_order_payments = 6;

  // Refund by.
  oneof refund_by {
    // For REFUND_BY_ITEM.
    RefundByItem refund_by_item = 11;

    // For REFUND_BY_PAYMENT.
    RefundByPayment refund_by_payment = 12;
  }

  // 待退款的 OrderPayment.
  message OrderPayment {
    // Order Payment ID.
    int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  }

  // Refund by item.
  message RefundByItem {
    // Items.
    repeated RefundItem refund_items = 1;
    // Tips.
    google.type.Money refund_tips = 2;

    // Refund item.
    message RefundItem {
      // Order item ID.
      int64 order_item_id = 1 [(validate.rules).int64 = {gt: 0}];
      // Refund item mode.
      models.order.v1.RefundItemMode refund_item_mode = 2 [(validate.rules).enum = {
        defined_only: true
        /* ignore UNSPECIFIED */
        not_in: 0
      }];
      // Refund param by mode.
      oneof refund_item_mode_param {
        option (validate.required) = true;

        // Refund quantity. For REFUND_BY_QUANTITY.
        int64 refund_quantity = 11 [(validate.rules).int64 = {
          gt: 0
          ignore_empty: true
        }];
        // Refund amount. For REFUND_BY_AMOUNT.
        google.type.Money refund_amount = 12;
      }
    }
  }

  // Refund by payment.
  message RefundByPayment {
    // 退款的金额.
    google.type.Money refund_amount = 1;
    // 退款的金额的标记.
    RefundAmountFlags refund_amount_flags = 2;

    // 指定的要退款的 Order Payment 的 ID 列表.
    repeated int64 order_payment_ids = 3 [(validate.rules).repeated = {
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];

    // 退款金额的标志.
    message RefundAmountFlags {
      // 需要退款的金额中是否包含了 Convenience Fee.
      bool is_convenience_fee_included = 1;
      // 内部使用，前端在接口中传这个值会被忽略
      // 退的是否是 deposit 金额，如果是，则不需要传 payment，金额全部退到 refund_deposit_amount
      optional bool is_deposit_amount = 2;
    }
  }
}

// refund order response
message RefundOrderResponse {
  // Refund order.
  models.order.v1.RefundOrderModel refund_order = 1;
  // Refund order items. Empty if REFUND_BY_AMOUNT.
  repeated models.order.v1.RefundOrderItemModel refund_order_items = 2;
  // Refund order payment.
  repeated models.order.v1.RefundOrderPaymentModel refund_order_payment = 3;

  // On refunding deducted deposit, multiple refund orders may be created. The related refund orders will be set to this
  // field.
  repeated RelatedRefundOrder related_refund_orders = 4;

  // Refund order fields for a related refund order.
  message RelatedRefundOrder {
    // Refund order.
    models.order.v1.RefundOrderModel refund_order = 1;
    // Refund order items. Empty if REFUND_BY_AMOUNT.
    repeated models.order.v1.RefundOrderItemModel refund_order_items = 2;
    // Refund order payment.
    repeated models.order.v1.RefundOrderPaymentModel refund_order_payment = 3;
  }
}

// SyncRefundOrderPaymentRequest.
// 用于定时任务触发异常中断的 RefundOrderPayment 流程继续走下去.
message SyncRefundOrderPaymentRequest {
  // 需要触发的 RefundOrderPayment 的 ID.
  int64 refund_order_payment_id = 1;
}

// SyncRefundOrderPaymentResponse.
message SyncRefundOrderPaymentResponse {
  // 总共发现的需要处理的 RefundOrderPayment 数量.
  int64 total = 1;
  // 同步成功的数量.
  int64 synced = 2;
}

// List orders request.
// 依据源单 ID 查询关联的订单、退款单详情.
message ListOrdersV1Request {
  // Business ID.
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // 源单 ID.
  int64 origin_order_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// List orders response.
message ListOrdersV1Response {
  // Orders.
  repeated models.order.v1.OrderModelV1 orders = 1;
  // Refund orders.
  repeated models.order.v1.RefundOrderModel refund_orders = 2;
}

// List order detail request.
// 依据源单 ID 查询关联的订单、退款单详情.
message ListOrderDetailRequest {
  // Business ID.
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // 源单 ID
  int64 origin_order_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// List order detail response.
message ListOrderDetailResponse {
  // Orders.
  repeated models.order.v1.OrderDetailModelV1 orders = 1;
  // Refund orders.
  repeated models.order.v1.RefundOrderDetailModel refund_orders = 2;
}

// Query order detail request.
// 查询 Company 内的订单. 支持多条件筛选.
message QueryOrderDetailRequest {
  // Company ID.
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Business ID，为 0 时表示不限制.
  int64 business_id = 2 [(validate.rules).int64 = {gte: 0}];
  // SourceType.
  // 传 0 表示不限制.
  models.order.v1.OrderSourceType source_type = 3 [(validate.rules).enum = {defined_only: true}];
  // Source ID.
  int64 source_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// Query order detail response.
message QueryOrderDetailResponse {
  // Orders.
  repeated models.order.v1.OrderDetailModelV1 orders = 1;
  // Refund orders.
  repeated models.order.v1.RefundOrderDetailModel refund_orders = 2;
}

// Get order detail request.
message GetOrderDetailRequest {
  // Business ID.
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Order ID.
  int64 order_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// Get order detail response.
message GetOrderDetailResponse {
  // Order detail.
  models.order.v1.OrderDetailModelV1 order = 1;
}

// Get refund order detail request.
message GetRefundOrderDetailRequest {
  // Business ID.
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Refund order ID.
  int64 refund_order_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// Get refund order detail response.
message GetRefundOrderDetailResponse {
  // Refund order detail.
  models.order.v1.RefundOrderDetailModel refund_order = 1;
}

// Create noshow order request.
message CreateNoShowOrderRequest {
  // Company ID.
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Business ID.
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // Staff ID.
  int64 staff_id = 3 [(validate.rules).int64 = {gte: 0}];
  // Customer ID.
  int64 customer_id = 4 [(validate.rules).int64 = {gt: 0}];

  // Source type.
  models.order.v1.OrderSourceType source_type = 11 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Source ID.
  int64 source_id = 12 [(validate.rules).int64 = {gt: 0}];
  // Description.
  string description = 13;
  // NoShow fee. This amount should greater than ZERO.
  google.type.Money no_show_fee_amount = 14;
}

// Create noshow order response.
message CreateNoShowOrderResponse {
  // created no-show order.
  models.order.v1.OrderDetailModelV1 order = 1;
}

// Create extra tip order request.
message CreateTipOrderRequest {
  // Company ID.
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Business ID.
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // Staff ID.
  int64 staff_id = 3 [(validate.rules).int64 = {gte: 0}];
  // Customer ID.
  int64 customer_id = 4 [(validate.rules).int64 = {gt: 0}];

  // Source type.
  models.order.v1.OrderSourceType source_type = 11 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Source ID.
  int64 source_id = 12 [(validate.rules).int64 = {gt: 0}];
  // Description.
  string description = 13;
  // Tip amount. This amount should be greater than ZERO.
  google.type.Money tip_amount = 14;
  // tips based amount
  google.type.Money tip_based_amount = 15;
}

// Create extra tip order response.
message CreateTipOrderResponse {
  // created extra tip order.
  models.order.v1.OrderDetailModelV1 order = 1;
}

// Create deposit order.
message CreateDepositOrderRequest {
  // Company ID.
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Business ID.
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // Staff ID.
  int64 staff_id = 3 [(validate.rules).int64 = {gte: 0}];
  // Customer ID.
  int64 customer_id = 4 [(validate.rules).int64 = {gt: 0}];

  // Source type.
  models.order.v1.OrderSourceType source_type = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Source ID.
  int64 source_id = 6 [(validate.rules).int64 = {gt: 0}];
  // Deposit amount.
  google.type.Money deposit_amount = 7;
  // Description (like "By percentage, $250.00*30%").
  string deposit_description = 8;
  // Deposit price details to be created.
  moego.models.order.v1.PriceDetailModel deposit_price_detail = 9;
}

// CreateDepositOrderResponse
message CreateDepositOrderResponse {
  // Order.
  models.order.v1.OrderDetailModelV1 order = 1;
}

// Request for GetDepositDetail.
message GetDepositDetailRequest {
  // Deposit order ID.
  int64 deposit_order_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// Response for GetDepositDetail.
message GetDepositDetailResponse {
  // Collected deposit (including refunded amount).
  google.type.Money collected_amount = 1;
  // Refunded overpaid amount.
  google.type.Money reversed_amount = 2;
  // Deducted amount.
  google.type.Money deducted_amount = 3;
  // The deposit balance (collected_amount - reversed_amount - deducted_amount).
  google.type.Money balance = 4;
}

// Request for UpdateDepositOrderSource
message UpdateDepositOrderSourceRequest {
  // Old source type.
  models.order.v1.OrderSourceType old_source_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Old source ID.
  int64 old_source_id = 2 [(validate.rules).int64 = {gt: 0}];
  // New source type.
  models.order.v1.OrderSourceType new_source_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // New source ID.
  int64 new_source_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// Response for UpdateDepositOrderSource
message UpdateDepositOrderSourceResponse {}

// Cancel order request.
message CancelOrderRequest {
  // Business ID.
  int64 business_id = 1;
  // Staff ID.
  int64 staff_id = 2;
  // Order ID.
  int64 order_id = 3;
}

// Cancel order response.
message CancelOrderResponse {}

// 临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
// 用途是在 check in 的时候，获取一个稳定的 invoice id.
// 同一组 SourceType + SourceID 会固定返回同样的 Invoice ID。
// 其余参数是为了创建空订单而设置的。
message CreateInvoiceIDRequest {
  // Source type.
  models.order.v1.OrderSourceType source_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Source ID.
  int64 source_id = 2 [(validate.rules).int64 = {gt: 0}];
  // Company ID.
  int64 company_id = 3 [(validate.rules).int64 = {gt: 0}];
  // Business ID.
  int64 business_id = 4 [(validate.rules).int64 = {gt: 0}];
  // Staff ID. 0 for pay online
  int64 staff_id = 5 [(validate.rules).int64 = {gte: 0}];
  // Customer ID.
  int64 customer_id = 6 [(validate.rules).int64 = {gt: 0}];
}

// 临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
message CreateInvoiceIDResponse {
  // Invoice ID.
  int64 invoice_id = 1;
}

// order service
service OrderService {
  // create order
  rpc CreateOrder(CreateOrderRequest) returns (CreateOrderResponse);
  // update order
  rpc UpdateOrder(UpdateOrderRequest) returns (UpdateOrderResponse);
  // update order incremental
  rpc UpdateOrderIncremental(UpdateOrderIncrRequest) returns (UpdateOrderIncrResponse);
  // get order
  rpc GetOrder(GetOrderRequest) returns (moego.models.order.v1.OrderModel);
  // get order with items, tax, discount, extra fee
  rpc GetOrderDetail(GetOrderRequest) returns (moego.models.order.v1.OrderDetailModel);
  // get order list
  rpc GetOrderList(GetOrderListRequest) returns (GetOrderListResponse);
  // get retail order list
  rpc GetRetailOrderList(GetRetailInvoicesRequest) returns (GetRetailInvoicesResponse);
  // get order item detail
  rpc GetOrderItemDetail(GetOrderItemDetailRequest) returns (GetOrderItemDetailResponse);
  // report get tips order list by page
  rpc GetTipsOrderList(GetTipsOrderListRequest) returns (GetTipsOrderListResponse);
  // modify order item tax
  rpc ModifyItemTax(ModifyItemTaxInput) returns (ModifyItemTaxOutput);
  // add service charge to order
  rpc AddServiceChargeToOrder(OperateOrderServiceChargeInput) returns (OperateOrderServiceChargeOutput);
  // remove service charge to order
  rpc RemoveServiceChargeFromOrder(OperateOrderServiceChargeInput) returns (OperateOrderServiceChargeOutput);
  //set tips
  rpc SetTips(SetTipsRequest) returns (SetTipsResult);
  // ListOrders support pagination
  // Deprecated: please use ListOrdersV1.
  rpc ListOrders(ListOrdersRequest) returns (ListOrdersResponse);
  // get order list by origin order id
  rpc GetOrderHistory(GetOrderHistoryRequest) returns (GetOrderHistoryResponse);
  // update extra order
  rpc UpdateExtraOrder(UpdateExtraOrderRequest) returns (UpdateExtraOrderResponse);
  // edit staff commission
  rpc EditStaffCommission(EditStaffCommissionParams) returns (EditStaffCommissionResult);
  // get order and grooming detail relation
  rpc GetGroomingDetailRelation(GetGroomingDetailRelationRequest) returns (GetGroomingDetailRelationResponse);
  // upgrade invoice reinvent
  rpc UpgradeInvoiceReinvent(UpgradeInvoiceReinventRequest) returns (UpgradeInvoiceReinventResponse);
  // check invoice reinvent response
  rpc CheckInvoiceReinvent(CheckInvoiceReinventRequest) returns (CheckInvoiceReinventResponse);
  // create order payment
  rpc PayOrder(PayOrderRequest) returns (PayOrderResponse);
  // update order payment
  rpc UpdateOrderPayment(UpdateOrderPaymentRequest) returns (UpdateOrderPaymentResponse);
  // task for sync order payment and order
  rpc SyncOrderPaymentAndOrder(google.protobuf.Empty) returns (SyncOrderPaymentAndOrderResponse);
  // preview refund order.
  rpc PreviewRefundOrder(PreviewRefundOrderRequest) returns (PreviewRefundOrderResponse);
  // preview refund order payments.
  // 纯计算接口.
  rpc PreviewRefundOrderPayments(PreviewRefundOrderPaymentsRequest) returns (PreviewRefundOrderPaymentsResponse);
  // refund order
  rpc RefundOrder(RefundOrderRequest) returns (RefundOrderResponse);
  // sync refund order payment.
  // 同步 refund order payment 与 refund payment.
  // 用于定时任务触发异常中断的 RefundOrderPayment 流程继续走下去.
  rpc SyncRefundOrderPayment(SyncRefundOrderPaymentRequest) returns (SyncRefundOrderPaymentResponse);
  // list orders.
  // 通过 Origin Order ID 查询整个订单及关联的 Extra Order 和 Refund Order。
  // 与 ListOrderDetail 的区别在于，这个接口只包含 Order & RefundOrder 本身，不包含 Order Payment， Item 等。
  rpc ListOrdersV1(ListOrdersV1Request) returns (ListOrdersV1Response);
  // list full order detail.
  // 通过 Origin Order ID 查询整个订单以及关联的 Extra Order 和 Refund Order 的详情。
  rpc ListOrderDetail(ListOrderDetailRequest) returns (ListOrderDetailResponse);
  // Query order detail.
  rpc QueryOrderDetail(QueryOrderDetailRequest) returns (QueryOrderDetailResponse);
  // get order detail v1.
  // 查订单详情.
  rpc GetOrderDetailV1(GetOrderDetailRequest) returns (GetOrderDetailResponse);
  // get refund order detail.
  rpc GetRefundOrderDetail(GetRefundOrderDetailRequest) returns (GetRefundOrderDetailResponse);

  // CreateNoShowOrder creates noshow-type order.
  rpc CreateNoShowOrder(CreateNoShowOrderRequest) returns (CreateNoShowOrderResponse);
  // CreateTipOrder creates extra tip order.
  rpc CreateTipOrder(CreateTipOrderRequest) returns (CreateTipOrderResponse);
  // CreateDepositOrder creates a deposit order.
  rpc CreateDepositOrder(CreateDepositOrderRequest) returns (CreateDepositOrderResponse);
  // GetDepositDetail gets deposit detail.
  rpc GetDepositDetail(GetDepositDetailRequest) returns (GetDepositDetailResponse);
  // UpdateDepositOrderSource updates deposit order source type and id from booking request to appointment.
  rpc UpdateDepositOrderSource(UpdateDepositOrderSourceRequest) returns (UpdateDepositOrderSourceResponse);

  // CancelOrder cancels un-finished order.
  rpc CancelOrder(CancelOrderRequest) returns (CancelOrderResponse);

  // CreateInvoiceID, 临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
  rpc CreateInvoiceID(CreateInvoiceIDRequest) returns (CreateInvoiceIDResponse);
}
