syntax = "proto3";

package moego.service.order.v1;

import "moego/models/order/v1/order_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1;ordersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.order.v1";

/**
 * Get customer recent items request params
 */
message GetCustomerRecentItemsRequest {
  // customer id
  int64 customer_id = 1;
  // business id
  optional int64 business_id = 2;
  // type list
  repeated string types = 3;
  // object id, for only these object
  repeated int64 object_ids = 4;
  // query from time
  optional int64 since_time = 5;
  // limit count
  optional int32 count = 6;
}

/**
 * Get customer recent orders request params
 */
message GetCustomerRecentOrdersRequest {
  // customer id
  int64 customer_id = 1;
  // business id
  optional int64 business_id = 2;
  // type list
  repeated string types = 3;
  // object id, for only these object
  repeated int64 object_ids = 4;
  // status
  repeated int32 status = 5;
  // query from time
  optional int64 since_time = 6;
  // limit count
  optional int32 count = 7;
}

/**
 * Get customer payment summary request params
 */
message GetCustomerPaymentSummaryRequest {
  // business id
  optional int64 business_id = 1;
  // query customer ids
  repeated int64 customer_id = 2;
  // order source types
  repeated string source_types = 3;
  // order status
  optional int32 status = 4;
  // order source id for query remain amount
  repeated int64 source_id = 5;
}

/**
 * Customer order item
 */
message CustomerOrderItem {
  // object id
  int64 object_id = 1;
  // item type
  string type = 2;
  // item name
  string name = 3;
  // item unit price
  double unit_price = 4;
  // total quantity
  int32 quantity = 5;
  // staff id
  optional int64 staff_id = 6;
  // order id
  optional int64 order_id = 7;
  // order item id
  optional int64 order_item_id = 8;
  // quantity of using package
  optional int32 purchased_quantity = 9;
  // purchased time
  optional int64 purchased_time = 10;
  // item description
  optional string description = 11;
}

/**
 * Get customer recent items response: list
 */
message GetCustomerRecentItemsResponse {
  // item list
  repeated CustomerOrderItem items = 1;
}

/**
 * Get customer recent orders response: list
 */
message GetCustomerRecentOrdersResponse {
  // order list
  repeated moego.models.order.v1.OrderModel orders = 1;
}

/**
 * Customer payment summary item
 */
message CustomerPaymentSummaryModel {
  // customer id
  int64 customer_id = 1;
  // total payment amount
  double total_payment_amount = 2;
  // total paid amount
  double total_paid_amount = 3;
  // total remain amount
  double total_remain_amount = 4;
  // grooming id list for query remain amount (special query condition)
  repeated int64 source_id = 5;
}

/**
 * Get customer payment summary response: list
 */
message GetCustomerPaymentSummaryResponse {
  // summary list
  repeated CustomerPaymentSummaryModel payment_summaries = 1;
}

// customer's order service
service CustomerOrderService {
  // get customer recent orders
  rpc GetCustomerRecentOrders(GetCustomerRecentOrdersRequest) returns (GetCustomerRecentOrdersResponse);
  // get customer recent items
  rpc GetCustomerRecentItems(GetCustomerRecentItemsRequest) returns (GetCustomerRecentItemsResponse);
  // get customer payment summary
  rpc GetCustomerPaymentSummary(GetCustomerPaymentSummaryRequest) returns (GetCustomerPaymentSummaryResponse);
}
