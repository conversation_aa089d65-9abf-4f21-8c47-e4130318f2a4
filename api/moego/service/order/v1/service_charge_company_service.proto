syntax = "proto3";

package moego.service.order.v1;

import "moego/models/business_customer/v1/business_pet_schedule_setting_enums.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/order/v1/service_charge_def.proto";
import "moego/models/order/v1/service_charge_enums.proto";
import "moego/models/order/v1/service_charge_model.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1;ordersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.order.v1";

// get service charge request params
message GetCompanyServiceChargeListRequest {
  // business id
  int64 company_id = 1;
  // business id
  repeated int64 business_ids = 2;
  // is active
  optional bool is_active = 3;
  // is mandatory
  optional bool is_mandatory = 4;
  // include deleted, default false
  optional bool included_deleted = 5;
  // include tax ids. ignored if tax_ids is empty
  repeated int32 tax_ids = 6;
  // surcharge type
  optional moego.models.order.v1.SurchargeType surcharge_type = 7 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
}

// add service charge request params
message AddCompanyServiceChargeRequest {
  // company id
  int64 company_id = 1;
  // name
  string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 150
  }];
  // split method
  optional string description = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 1000
  }];
  // price, must be positive
  double price = 4 [(validate.rules).double = {gte: 0}];
  // tax id, 0 or null means no tax
  int32 tax_id = 5 [(validate.rules).int32 = {gte: 0}];
  // is mandatory, preserved 8-19 for future use
  optional bool is_mandatory = 6;
  // is active
  optional bool is_active = 7;
  // is all location
  optional bool is_all_location = 8;
  // apply to upcoming
  optional bool apply_upcoming_appt = 9;
  // operator id
  int64 operator_id = 10;
  //service charge location override data
  repeated moego.models.order.v1.ServiceChargeLocationOverride location_override_list = 11;
  // auto apply status
  optional moego.models.order.v1.ServiceCharge.AutoApplyStatus auto_apply_status = 12 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply condition
  optional moego.models.order.v1.ServiceCharge.AutoApplyCondition auto_apply_condition = 13 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply time, unit: minute
  // Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
  optional int32 auto_apply_time = 14 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // auto apply time type, default is AUTO_APPLY_TIME_TYPE_CERTAIN_TIME
  // Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
  optional moego.models.order.v1.ServiceCharge.AutoApplyTimeType auto_apply_time_type = 15 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto support service
  // Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
  repeated models.offering.v1.ServiceItemType service_item_types = 16 [(validate.rules).repeated = {
    items: {
      enum: {
        not_in: [0]
        defined_only: true
      }
    }
  }];
  // apply type
  optional moego.models.order.v1.ServiceCharge.ApplyType apply_type = 17 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // surcharge type
  optional moego.models.order.v1.SurchargeType surcharge_type = 18 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // charge method
  optional moego.models.order.v1.ChargeMethod charge_method = 19 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // food source
  optional moego.models.order.v1.FoodSourceDef food_source = 20;

  // 24-hours period rule
  // charge type
  optional moego.models.order.v1.ServiceCharge.TimeBasedPricingType time_based_pricing_type = 21 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // multiple pets charge type
  optional moego.models.order.v1.ServiceCharge.MultiplePetsChargeType multiple_pets_charge_type = 22 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // 24-hours period rule
  repeated moego.models.order.v1.ServiceChargeExceedHourRule hourly_exceed_rules = 23 [(validate.rules).repeated = {
    min_items: 0
    max_items: 30
    items: {
      message: {required: true}
    }
  }];

  // whether the service charge is available for all services
  optional bool enable_service_filter = 24;

  // service filters
  repeated moego.models.order.v1.ServiceFilter service_filter_rules = 25;
}

// update service charge request params
message UpdateCompanyServiceChargeRequest {
  // company id
  int64 company_id = 1;
  // id, exist for update
  int64 id = 2 [(validate.rules).int64 = {gt: 0}];
  // name
  optional string name = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 150
  }];
  // split method
  optional string description = 4 [(validate.rules).string = {
    min_len: 0
    max_len: 1000
  }];
  // price, must be positive
  double price = 5 [(validate.rules).double = {gte: 0}];
  // tax id, 0 or null means no tax
  int32 tax_id = 6 [(validate.rules).int32 = {gte: 0}];
  // is mandatory
  optional bool is_mandatory = 7;
  // is active
  optional bool is_active = 8;
  // is all location
  optional bool is_all_location = 9;
  // apply to upcoming
  optional bool apply_upcoming_appt = 10;
  //single location id
  optional int64 single_location_id = 11;
  // operator id
  int64 operator_id = 12;
  //service charge location override data
  repeated moego.models.order.v1.ServiceChargeLocationOverride location_override_list = 13;
  // auto apply status
  optional moego.models.order.v1.ServiceCharge.AutoApplyStatus auto_apply_status = 14 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply condition
  optional moego.models.order.v1.ServiceCharge.AutoApplyCondition auto_apply_condition = 15 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply time, unit: minute
  // Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
  optional int32 auto_apply_time = 16 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // auto apply time type, default is AUTO_APPLY_TIME_TYPE_CERTAIN_TIME
  // Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
  optional moego.models.order.v1.ServiceCharge.AutoApplyTimeType auto_apply_time_type = 17 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto support service
  // Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
  repeated models.offering.v1.ServiceItemType service_item_types = 18 [(validate.rules).repeated = {
    items: {
      enum: {
        not_in: [0]
        defined_only: true
      }
    }
  }];
  // apply type
  optional moego.models.order.v1.ServiceCharge.ApplyType apply_type = 19 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // charge method
  optional moego.models.order.v1.ChargeMethod charge_method = 20 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // food source ids
  optional moego.models.order.v1.FoodSourceDef food_source = 21;

  // 24-hours period rule
  // charge type
  optional moego.models.order.v1.ServiceCharge.TimeBasedPricingType time_based_pricing_type = 22 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // multiple pets charge type
  optional moego.models.order.v1.ServiceCharge.MultiplePetsChargeType multiple_pets_charge_type = 23 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // 24-hours period rule
  repeated moego.models.order.v1.ServiceChargeExceedHourRule hourly_exceed_rules = 24 [(validate.rules).repeated = {
    min_items: 0
    max_items: 30
    items: {
      message: {required: true}
    }
  }];

  // whether the service charge is available for all services
  optional bool enable_service_filter = 25;

  // service filters
  repeated moego.models.order.v1.ServiceFilter service_filter_rules = 26;
}

// get Company service charge list response
message GetCompanyServiceChargeListResponse {
  // service charge list
  repeated moego.models.order.v1.ServiceCharge service_charge = 1;
}

// order migrate service response
message OrderMigrateServiceRequest {
  // company id
  int64 company_id = 1;
}

// order migrate service response
message OrderMigrateServiceResponse {}

// list associated food source request
message ListSurchargeAssociatedFoodSourceRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // exist service charge id
  optional int64 exist_service_charge_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// list associated food source response
message ListSurchargeAssociatedFoodSourceResponse {
  // food source ids
  repeated int64 food_source_ids = 1;
}

// Get feeding and medication charge request
message GetFeedingMedicationChargeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // schedule type
  moego.models.business_customer.v1.BusinessPetScheduleType schedule_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // food source ids
  repeated int64 food_source_ids = 4 [(validate.rules).repeated = {
    min_items: 0
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Get feeding and medication charge response
message GetFeedingMedicationChargeResponse {
  // service charge list
  repeated moego.models.order.v1.FeedingMedicationChargeView service_charges = 1;
}

// service charge company api
service ServiceChargeCompanyService {
  // get service charge list
  rpc GetCompanyServiceChargeList(GetCompanyServiceChargeListRequest) returns (GetCompanyServiceChargeListResponse);
  // add service charge
  rpc AddCompanyServiceCharge(AddCompanyServiceChargeRequest) returns (moego.models.order.v1.ServiceCharge);
  // update service charge
  rpc UpdateCompanyServiceCharge(UpdateCompanyServiceChargeRequest) returns (moego.models.order.v1.ServiceCharge);
  //order as migrate service
  rpc OrderMigrateService(OrderMigrateServiceRequest) returns (OrderMigrateServiceResponse);
  // list associated food source
  rpc ListSurchargeAssociatedFoodSource(ListSurchargeAssociatedFoodSourceRequest) returns (ListSurchargeAssociatedFoodSourceResponse);
  // Get feeding and medication charge
  rpc GetFeedingMedicationCharge(GetFeedingMedicationChargeRequest) returns (GetFeedingMedicationChargeResponse);
}
