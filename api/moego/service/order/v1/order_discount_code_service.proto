// @since 2023-10-20 17:20:50
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.order.v1;

import "moego/models/marketing/v1/discount_code_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1;ordersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.order.v1";

// get available discount code list input
message GetAvailableDiscountListInput {
  // pagination request
  moego.utils.v2.PaginationRequest pagination = 1 [(validate.rules).message.required = true];
  // invoice id
  int64 invoice_id = 2 [(validate.rules).int64 = {gt: 0}];
  // code name
  optional string code_name = 3 [(validate.rules).string = {max_len: 20}];
  // business id
  int64 business_id = 4 [(validate.rules).int64 = {gt: 0}];
  // company id
  int64 company_id = 5;
}

// get available discount code list output
message GetAvailableDiscountListOutput {
  // pagination response
  moego.utils.v2.PaginationResponse pagination = 1;
  // discount code list
  repeated moego.models.marketing.v1.DiscountCodeCompositeView discount_code_composite_views = 2;
}

// migrate discount code id input
message MigrateDiscountCodeInput {
  // discount code id map
  map<int64, int64> discount_code_id_map = 1;
}

// migrate discount code id output
message MigrateDiscountCodeInputOutput {}

// the discount_code service
service OrderDiscountCodeService {
  // get available discount code list
  rpc GetAvailableDiscountList(GetAvailableDiscountListInput) returns (GetAvailableDiscountListOutput);
  // migrate discount code id
  rpc MigrateDiscountCodeId(MigrateDiscountCodeInput) returns (MigrateDiscountCodeInputOutput);
}
