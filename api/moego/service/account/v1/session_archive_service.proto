syntax = "proto3";

package moego.service.account.v1;

import "google/type/date.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1;accountsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.account.v1";

// session archive service request
message CreateSessionArchiveTaskRequest {
  // start id
  int64 start_id = 1 [(validate.rules).int64.gte = 0];
  // end id
  int64 end_id = 2 [(validate.rules).int64.gte = 0];
  // step
  int32 step = 3 [(validate.rules).int32.gt = 0];
  // max date
  google.type.Date max_date = 4 [(validate.rules).message.required = true];
}

// session archive service response
message CreateSessionArchiveTaskResponse {
  // task id
  int64 id = 1;
}

// update session archive task status request
message UpdateSessionArchiveTaskStatusRequest {
  // task id
  int64 id = 1;
  // status
  string status = 2;
}

// update session archive task status response
message UpdateSessionArchiveTaskStatusResponse {}

// session archive service
service SessionArchiveService {
  // create session archive task
  rpc CreateSessionArchiveTask(CreateSessionArchiveTaskRequest) returns (CreateSessionArchiveTaskResponse);

  // update session archive task status
  rpc UpdateSessionArchiveTaskStatus(UpdateSessionArchiveTaskStatusRequest) returns (UpdateSessionArchiveTaskStatusResponse);
}
