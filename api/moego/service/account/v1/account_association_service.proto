syntax = "proto3";

package moego.service.account.v1;

import "moego/models/account/v1/account_association_defs.proto";
import "moego/models/account/v1/account_association_enums.proto";
import "moego/models/account/v1/account_association_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1;accountsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.account.v1";

// get account association request
message GetAccountAssociationRequest {
  // identifier
  oneof identifier {
    option (validate.required) = true;

    // by id
    // int64 id = 1 [(validate.rules).int64 = {gt: 0}];

    // by account and platform
    models.account.v1.AccountAndPlatformQueryDef account_and_platform = 2;

    // by platform account
    models.account.v1.PlatformAccountQueryDef platform_account = 3;
  }
}

// get account association response
message GetAccountAssociationResponse {
  // an account association, may not exist
  optional models.account.v1.AccountAssociationModel association = 1;
}

// list account association request
message ListAccountAssociationRequest {
  // account id
  int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];

  // platform, optional
  optional models.account.v1.AccountAssociationPlatform platform = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// list account association response
message ListAccountAssociationResponse {
  // account associations
  repeated models.account.v1.AccountAssociationModel associations = 1;
}

// add account association request
message AddAccountAssociationRequest {
  // association
  models.account.v1.AccountAssociationCreateDef association = 1 [(validate.rules).message = {required: true}];
}

// add account association response
message AddAccountAssociationResponse {}

// reset account association request
message ResetAccountAssociationRequest {
  // association
  models.account.v1.AccountAssociationCreateDef association = 1 [(validate.rules).message = {required: true}];
}

// reset account association response
message ResetAccountAssociationResponse {}

// account association service
service AccountAssociationService {
  // Get an account association by one of the following conditions:
  // - Getting by `account_id` and `platform`.
  //   A MoeGo account can associate with more than one platform account of the same 3rd party platform. This method will
  //   only return the newest association of given platform. If all associations of this platform are needed, please use
  //   `ListAccountAssociation` with `platform` specified.
  // - Getting by `platform` and `platform_account_id`.
  //   A 3rd party platform account can only associate with one MoeGo account. So at most only one association will be
  //   returned.
  //
  // If the association does not exist, the response will be empty. Therefore, please check `response.hasAssociation()`
  // before using it.
  rpc GetAccountAssociation(GetAccountAssociationRequest) returns (GetAccountAssociationResponse);

  // List account associations of an account.
  // If the `platform` is not specified, all associations of the account will be returned.
  // If the `platform` is specified, associations of the specified platform (maybe more than one) will be returned.
  // If the association does not exist, the response will be empty.
  rpc ListAccountAssociation(ListAccountAssociationRequest) returns (ListAccountAssociationResponse);

  // Add a 3rd party platform account to a MoeGo account. This association will be appended if the MoeGo account already
  // has associations with the same 3rd party platform, i.e., an account can associate with multiple platform accounts
  // of the same 3rd party platform. However, a 3rd party platform account can only associate with one MoeGo account.
  // If this 3rd party platform account already be associated with this MoeGo account, the association will be updated
  // with given `platform_data` and `visible`. If it is associated with other MoeGo account, an error will be thrown.
  //
  // Error codes:
  // - CODE_PLATFORM_ACCOUNT_ALREADY_ASSOCIATED: This platform account has already been associated with another MoeGo account.
  rpc AddAccountAssociation(AddAccountAssociationRequest) returns (AddAccountAssociationResponse);

  // Clear all associations of a 3rd party platform and add a new association of this 3rd party platform for a MoeGo account.
  // A MoeGo account can associate with multiple platform accounts of the same 3rd party platform. This method will
  // clear all associations of the given platform, replacing them with a new one.
  // Error codes:
  // - CODE_PLATFORM_ACCOUNT_ALREADY_ASSOCIATED: This platform account has already been associated with another MoeGo account.
  rpc ResetAccountAssociation(ResetAccountAssociationRequest) returns (ResetAccountAssociationResponse);
}
