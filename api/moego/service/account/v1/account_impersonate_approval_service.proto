syntax = "proto3";

package moego.service.account.v1;

import "moego/models/account/v1/account_impersonate_approval_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1;accountsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.account.v1";

// account impersonate approval service
service AccountImpersonateApprovalService {
  // save approval instance. If approval exists, update it.
  rpc SaveApprovalInstance(SaveApprovalInstanceRequest) returns (SaveApprovalInstanceResponse);

  // get latest approval instance, status = PENDING or APPROVED
  rpc GetLatestApprovalInstance(GetLatestApprovalInstanceRequest) returns (GetLatestApprovalInstanceResponse);
}

// save approval instance request
message SaveApprovalInstanceRequest {
  // approval instance
  models.account.v1.AccountImpersonateApprovalInstanceModel instance = 1 [(validate.rules).message.required = true];
}

// save approval instance response
message SaveApprovalInstanceResponse {}

// get latest approval instance request
message GetLatestApprovalInstanceRequest {
  // impersonator
  string impersonator = 1 [(validate.rules).string = {max_len: 255}];
  // source
  string source = 2 [(validate.rules).string = {max_len: 50}];

  // target account
  oneof target_account {
    option (validate.required) = true;
    // target account id
    int64 target_account_id = 3 [(validate.rules).int64 = {gt: 0}];
    // target account email
    string target_account_email = 4 [(validate.rules).string = {max_len: 255}];
  }

  // status, if set, use as a filter to get latest approval instance
  optional string status = 5;
}

// get latest approval instance response
message GetLatestApprovalInstanceResponse {
  // approval instance, may not exist
  optional models.account.v1.AccountImpersonateApprovalInstanceModel instance = 1;
}
