syntax = "proto3";

package moego.service.temp_order.v1;

import "moego/models/order/v1/item_paid_amount_assignment_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/temp_order/v1;ordersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.temp_order.v1";

// Request for assigning paid amount to specific items
message AssignItemPaidAmountRequest {
  // Order ID
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Business ID
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // List of items to assign paid amount
  repeated moego.models.order.v1.ItemPaidAmountAssignment items = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 20
  }];
}

// Response for assigning paid amount to specific items
message AssignItemPaidAmountResponse {
  // Whether the operation was successful
  bool success = 1;
}

// Request for getting assigned paid amounts for order items
message GetAssignedItemPaidAmountRequest {
  // Order ID
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Business ID
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// Response for getting assigned paid amounts for order items
message GetAssignedItemPaidAmountResponse {
  // Order ID
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // List of item payment assignments
  repeated moego.models.order.v1.ItemPaidAmountAssignment items = 2;
}

// Service for managing item amount assignments
service AssignItemAmountService {
  // 分配已支付金额到指定项目 assigned item paid amount for order for whitelist(centralbark only)
  rpc AssignItemPaidAmount(AssignItemPaidAmountRequest) returns (AssignItemPaidAmountResponse);
  // get assigned item paid amount for order for whitelist(centralbark only)
  rpc GetAssignedItemPaidAmount(GetAssignedItemPaidAmountRequest) returns (GetAssignedItemPaidAmountResponse);
}
