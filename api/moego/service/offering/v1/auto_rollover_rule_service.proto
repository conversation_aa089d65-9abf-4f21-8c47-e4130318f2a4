// @since 2024-06-03 22:06:04
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.service.offering.v1;

import "moego/models/offering/v1/auto_rollover_rule_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.offering.v1";

// batch get auto rollover rule request
message BatchGetAutoRolloverRuleRequest {
  // service id list
  repeated int64 service_ids = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
    max_items: 100
  }];
}

// batch get auto rollover rule response
message BatchGetAutoRolloverRuleResponse {
  // auto rollover rule list
  map<int64, models.offering.v1.AutoRolloverRuleModel> service_id_to_auto_rollover_rule = 1;
}

// the auto_rollover_rule service
service AutoRolloverRuleService {
  // batch get auto rollover rule
  rpc BatchGetAutoRolloverRule(BatchGetAutoRolloverRuleRequest) returns (BatchGetAutoRolloverRuleResponse) {}
}
