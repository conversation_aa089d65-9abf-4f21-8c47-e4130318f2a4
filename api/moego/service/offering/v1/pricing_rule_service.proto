// @since 2024-07-30 11:14:10
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.offering.v1;

import "moego/models/offering/v1/pricing_rule_defs.proto";
import "moego/models/offering/v1/pricing_rule_enums.proto";
import "moego/models/offering/v1/pricing_rule_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.offering.v1";

// upsert pricing rule request
message UpsertPricingRuleRequest {
  // the pricing_rule def
  moego.models.offering.v1.PricingRuleUpsertDef pricing_rule_upsert_def = 1 [(validate.rules).message = {required: true}];
  // company id
  int64 company_id = 6 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 7 [(validate.rules).int64 = {gt: 0}];
}

// upsert pricing rule response
message UpsertPricingRuleResponse {
  // the created pricing_rule
  moego.models.offering.v1.PricingRuleModel pricing_rule = 1;
}

// get pricing rule request
message GetPricingRuleRequest {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id
  int64 company_id = 6 [(validate.rules).int64 = {gt: 0}];
}

// get pricing rule response
message GetPricingRuleResponse {
  // the pricing rule
  moego.models.offering.v1.PricingRuleModel pricing_rule = 1;
}

// list pricing rule request
message ListPricingRulesRequest {
  // filter
  optional moego.models.offering.v1.ListPricingRuleFilter filter = 3;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 4;
  // pricing rule ids
  repeated int64 ids = 5 [(validate.rules).repeated = {
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // company id
  int64 company_id = 10 [(validate.rules).int64 = {gt: 0}];
}

// list pricing rule response
message ListPricingRulesResponse {
  // the pricing_rule
  repeated moego.models.offering.v1.PricingRuleModel pricing_rules = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// calculate pricing rule request
message CalculatePricingRuleRequest {
  // pet detail list
  repeated moego.models.offering.v1.PetDetailCalculateDef pet_details = 1 [(validate.rules).repeated = {min_items: 1}];
  // pricing_rule def
  optional moego.models.offering.v1.PricingRuleUpsertDef pricing_rule_upsert_def = 2;
  // calculate for preview
  bool is_preview = 3;
  // company id
  int64 company_id = 9 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 10 [(validate.rules).int64 = {gt: 0}];
}

// calculate pricing rule response
message CalculatePricingRuleResponse {
  // pet detail list
  repeated moego.models.offering.v1.PetDetailCalculateResultDef pet_details = 1;
  // formula for preview calculation
  optional string formula = 2;
}

// delete pricing rule request
message DeletePricingRuleRequest {
  // the unique id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// delete pricing rule response
message DeletePricingRuleResponse {}

// get associated services request
message ListAssociatedServicesRequest {
  // service item: grooming/boarding/daycare
  moego.models.offering.v1.ServiceItemType service_item_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service type: service/addon
  moego.models.offering.v1.ServiceType service_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // filter
  optional Filter filter = 3;
  // filter
  message Filter {
    // exclude pricing rule id
    optional int64 exclude_pricing_rule_id = 1 [(validate.rules).int64 = {gt: 0}];
    // rule group type
    optional moego.models.offering.v1.RuleGroupType rule_group_type = 2 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
  }

  // company id
  int64 company_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// get associated services response
message ListAssociatedServicesResponse {
  // associated service ids
  repeated int64 associated_service_ids = 1;
  // all service associated
  bool all_service_associated = 2;
}

// check rule name is exist request
message CheckRuleNameRequest {
  // rule name
  string rule_name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 150
  }];
  // exclude pricing rule id
  optional int64 exclude_pricing_rule_id = 2 [(validate.rules).int64 = {gt: 0}];
  // company id
  int64 company_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// check rule name is exist response
message CheckRuleNameResponse {
  // rule name is exist
  bool is_exist = 1;
}

// pricing rule service
service PricingRuleService {
  // create pricing_rule, deprecated, use v2 instead
  rpc UpsertPricingRule(UpsertPricingRuleRequest) returns (UpsertPricingRuleResponse) {
    option deprecated = true;
  }
  // get pricing_rule, deprecated, use v2 instead
  rpc GetPricingRule(GetPricingRuleRequest) returns (GetPricingRuleResponse) {
    option deprecated = true;
  }
  // list pricing_rule, deprecated, use v2 instead
  rpc ListPricingRules(ListPricingRulesRequest) returns (ListPricingRulesResponse) {
    option deprecated = true;
  }
  // calculate pricing rule, deprecated, use v2 instead
  rpc CalculatePricingRule(CalculatePricingRuleRequest) returns (CalculatePricingRuleResponse) {
    option deprecated = true;
  }
  // delete pricing rule, deprecated, use v2 instead
  rpc DeletePricingRule(DeletePricingRuleRequest) returns (DeletePricingRuleResponse) {
    option deprecated = true;
  }
  // list associated services, used for pricing rule selecting services, deprecated, use v2 instead
  rpc ListAssociatedServices(ListAssociatedServicesRequest) returns (ListAssociatedServicesResponse) {
    option deprecated = true;
  }
  // check rule name is exist, deprecated, use v2 instead
  rpc CheckRuleName(CheckRuleNameRequest) returns (CheckRuleNameResponse) {
    option deprecated = true;
  }
}
