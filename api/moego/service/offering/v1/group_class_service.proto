syntax = "proto3";

package moego.service.offering.v1;

import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/type/datetime.proto";
import "moego/models/offering/v1/group_class_models.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.offering.v1";

// The Group Class Service
service GroupClassService {
  // create sessions
  rpc CreateInstanceAndSessions(CreateInstanceAndSessionsRequest) returns (CreateInstanceAndSessionsResponse);
  // count group class status
  rpc CountInstancesGroupByStatus(CountInstancesGroupByStatusRequest) returns (CountInstancesGroupByStatusResponse);
  // count group class statuses by group class id
  rpc CountInstancesGroupByClass(CountInstancesGroupByClassRequest) returns (CountInstancesGroupByClassResponse);
  // GetTrainingClassBatch
  rpc GetInstance(GetInstanceRequest) returns (GetInstanceResponse);
  // ListTrainingClassBatch
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse);
  // UpdateGroupClassInstance
  rpc UpdateInstanceAndSessions(UpdateInstanceAndSessionsRequest) returns (UpdateInstanceAndSessionsResponse);
  // DeleteInstanceAndSessions
  rpc DeleteInstanceAndSessions(DeleteInstanceAndSessionsRequest) returns (DeleteInstanceAndSessionsResponse);

  // edit session
  rpc UpdateSession(UpdateSessionRequest) returns (UpdateSessionResponse);
  // list sessions
  rpc ListSessions(ListSessionsRequest) returns (ListSessionsResponse);

  // refresh status
  rpc TaskRefreshInstanceStatus(google.protobuf.Empty) returns (google.protobuf.Empty);
}

// CreateTrainingClassBatch request
message CreateInstanceAndSessionsRequest {
  // company id
  int64 company_id = 1;
  // business id
  int64 business_id = 2;
  // group class id
  int64 group_class_id = 3;
  // trainer
  int64 staff_id = 4;
  // start time
  google.type.DateTime start_time = 5;
  // occurrence
  models.offering.v1.GroupClassInstance.Occurrence occurrence = 6;
}

// CreateTrainingClassBatch response
message CreateInstanceAndSessionsResponse {
  // created instance
  models.offering.v1.GroupClassInstance instance = 1;
  // created sessions
  repeated models.offering.v1.GroupClassSession sessions = 2;
}

// GetTrainingClassBatch request
message GetInstanceRequest {
  // the id
  int64 id = 1;
}

// GetTrainingClassBatch response
message GetInstanceResponse {
  // the training class batch
  models.offering.v1.GroupClassInstance group_class_instance = 1;
}

// CountGroupClassInstanceStatus request
message CountInstancesGroupByStatusRequest {
  // company
  int64 company_id = 1;
  // business id
  int64 business_id = 2;
  // trainer
  repeated int64 staff_ids = 3;
}

// CountGroupClassInstanceStatus response {
message CountInstancesGroupByStatusResponse {
  // the count
  message Count {
    // status
    models.offering.v1.GroupClassInstance.Status status = 1;
    // count
    int64 count = 2;
  }
  // the counts
  repeated Count counts = 1;
}

// CountGroupClassInstancesByStatus request
message CountInstancesGroupByClassRequest {
  // company
  int64 company_id = 1;
  // business id
  int64 business_id = 2;
  // trainer
  repeated int64 staff_ids = 3;
  // status
  models.offering.v1.GroupClassInstance.Status status = 4;
}

// CountGroupClassInstancesByStatus response
message CountInstancesGroupByClassResponse {
  // count
  message Count {
    // group class id
    int64 group_class_id = 1;
    // count
    int64 count = 2;
  }

  // the counts
  repeated Count counts = 1;
}

// ListTrainingClassBatch request
message ListInstancesRequest {
  // pagination
  utils.v2.PaginationRequest pagination = 1;
  // company
  int64 company_id = 2;
  // ids
  repeated int64 ids = 3;
  // the business id
  repeated int64 business_ids = 4;
  // the training group class id
  repeated int64 group_class_ids = 5;
  // trainer
  repeated int64 staff_ids = 6;
  // status
  models.offering.v1.GroupClassInstance.Status status = 7;
}

// ListTrainingClassBatch response
message ListInstancesResponse {
  // pagination
  utils.v2.PaginationResponse pagination = 1;
  // the training class batches
  repeated models.offering.v1.GroupClassInstance group_class_instances = 2;
}

// UpdateInstanceAndSessions request
message UpdateInstanceAndSessionsRequest {
  // id
  int64 id = 1;
  // trainer
  int64 staff_id = 2;
  // start time
  google.type.DateTime start_time = 3;
  // occurrence
  models.offering.v1.GroupClassInstance.Occurrence occurrence = 4;
}

// UpdateInstanceAndSessions response
message UpdateInstanceAndSessionsResponse {
  // the updated instance
  models.offering.v1.GroupClassInstance instance = 1;
  // the updated sessions
  repeated models.offering.v1.GroupClassSession sessions = 2;
}

// DeleteInstanceAndSessions request
message DeleteInstanceAndSessionsRequest {
  // id
  int64 id = 1;
}

// DeleteInstanceAndSessions response
message DeleteInstanceAndSessionsResponse {}

// UpdateSession request
message UpdateSessionRequest {
  // the id
  int64 id = 1;
  // start time
  optional google.protobuf.Timestamp start_time = 2;
  // duration
  optional google.protobuf.Duration duration = 3;
}

// UpdateSession response
message UpdateSessionResponse {
  // the training session
  models.offering.v1.GroupClassSession session = 1;
}

// ListSessions request
message ListSessionsRequest {
  // The company id
  int64 company_id = 1;
  // batch id
  repeated int64 group_class_instance_id = 2;
  // start time min
  optional google.protobuf.Timestamp start_time_min = 3;
  // start time max
  optional google.protobuf.Timestamp start_time_max = 4;
  // session id
  repeated int64 session_ids = 5;
}

// ListSessions response
message ListSessionsResponse {
  // the sessions
  repeated models.offering.v1.GroupClassSession sessions = 1;
}
