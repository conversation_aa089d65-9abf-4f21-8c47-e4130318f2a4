// @since 2024-03-12 11:33:59
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.service.offering.v1;

import "moego/models/offering/v1/evaluation_defs.proto";
import "moego/models/offering/v1/evaluation_models.proto";
import "moego/models/offering/v1/service_defs.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/organization/v1/tenant.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.offering.v1";

// create evaluation request
message CreateEvaluationRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the evaluation def
  moego.models.offering.v1.EvaluationDef evaluation_def = 2 [(validate.rules).message = {required: true}];
}

// create evaluation response
message CreateEvaluationResponse {
  // the evaluation model
  moego.models.offering.v1.EvaluationModel evaluation_model = 1 [(validate.rules).message = {required: true}];
}

// update evaluation request
message UpdateEvaluationRequest {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // the evaluation def
  moego.models.offering.v1.EvaluationDef evaluation_def = 2 [(validate.rules).message = {required: true}];
  // tenant 直接设置 required true 是个 break change，这次先加上，下次再迭代这里的时候，再把 required set true
  optional moego.models.organization.v1.Tenant tenant = 3 [(validate.rules).message.required = false];
}

// update evaluation response
message UpdateEvaluationResponse {
  // the evaluation model
  moego.models.offering.v1.EvaluationModel evaluation_model = 1 [(validate.rules).message = {required: true}];
}

// get evaluation list request
message GetEvaluationListRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get evaluation list response
message GetEvaluationListResponse {
  // the evaluation model list
  repeated moego.models.offering.v1.EvaluationModel evaluations = 1;
}

// get evaluation request
message GetEvaluationRequest {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // the business id, if set, will return business override evaluation price and duration
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get evaluation response
message GetEvaluationResponse {
  // the evaluation model
  moego.models.offering.v1.EvaluationModel evaluation_model = 1 [(validate.rules).message = {required: true}];
}

// get applicable evaluation list request
message GetApplicableEvaluationListRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service type
  optional models.offering.v1.ServiceItemType service_item_type = 3;
  // filter by pet
  optional models.offering.v1.ServiceFilterByPet filter_by_pet = 4;
  // include inactive
  bool include_inactive = 5;
}

// get applicable evaluation list response
message GetApplicableEvaluationListResponse {
  // the evaluation model list
  repeated moego.models.offering.v1.EvaluationBriefView evaluations = 1;
}

// get business list with applicable evaluation request
message GetBusinessListWithApplicableEvaluationRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get business list with applicable evaluation response
message GetBusinessListWithApplicableEvaluationResponse {
  // bool is all location
  bool is_all_location = 1;
  // the business id list, only return when is_all_location is false
  repeated int64 business_ids = 2;
}

// get evaluation list with evaluation ids request
message GetEvaluationListWithEvaluationIdsRequest {
  // the evaluation ids
  repeated int64 evaluation_ids = 1;
  // the business id, if set, will return business override evaluation price and duration
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get evaluation list with evaluation ids response
message GetEvaluationListWithEvaluationIdsResponse {
  // the evaluation model list
  repeated moego.models.offering.v1.EvaluationBriefView evaluations = 1;
}

// list evaluation request
message ListEvaluationRequest {
  // filter
  Filter filter = 1;
  // filter
  message Filter {
    // filter by the is resettable
    optional bool is_resettable = 1;
    //company ids recommended fields for indexing
    repeated int64 company_ids = 2 [(validate.rules).repeated = {
      min_items: 0
      items: {
        int64: {gt: 0}
      }
      unique: true
    }];
    //evaluation ids
    repeated int64 ids = 3 [(validate.rules).repeated = {
      min_items: 0
      items: {
        int64: {gt: 0}
      }
      unique: true
    }];
    // business id, if set, will return business override evaluation price and duration
    optional int64 business_id = 4 [(validate.rules).int64 = {gt: 0}];
  }
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3;
}

// list evaluation response
message ListEvaluationResponse {
  // the evaluation model filter list
  repeated moego.models.offering.v1.EvaluationModel evaluations = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// delete evaluation request
message DeleteEvaluationRequest {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // the company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// delete evaluation response
message DeleteEvaluationResponse {}

// the evaluation service
service EvaluationService {
  // create evaluation
  rpc CreateEvaluation(CreateEvaluationRequest) returns (CreateEvaluationResponse);
  // update evaluation
  rpc UpdateEvaluation(UpdateEvaluationRequest) returns (UpdateEvaluationResponse);
  // delete evaluation
  rpc DeleteEvaluation(DeleteEvaluationRequest) returns (DeleteEvaluationResponse);
  // get evaluation
  rpc GetEvaluation(GetEvaluationRequest) returns (GetEvaluationResponse);
  // get evaluation list
  rpc GetEvaluationList(GetEvaluationListRequest) returns (GetEvaluationListResponse);
  // get applicable evaluation list
  rpc GetApplicableEvaluationList(GetApplicableEvaluationListRequest) returns (GetApplicableEvaluationListResponse);
  // get business list with applicable evaluation
  rpc GetBusinessListWithApplicableEvaluation(GetBusinessListWithApplicableEvaluationRequest) returns (GetBusinessListWithApplicableEvaluationResponse);
  // get evaluation list with evaluation ids
  rpc GetEvaluationListWithEvaluationIds(GetEvaluationListWithEvaluationIdsRequest) returns (GetEvaluationListWithEvaluationIdsResponse);
  // list evaluation
  rpc ListEvaluation(ListEvaluationRequest) returns (ListEvaluationResponse);
}
