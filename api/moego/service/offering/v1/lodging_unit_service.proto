syntax = "proto3";

package moego.service.offering.v1;

import "moego/models/offering/v1/lodging_type_models.proto";
import "moego/models/offering/v1/lodging_unit_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.offering.v1";

/**
 * Request body for batch create LodgingUnit service
 */
message BatchCreateLodgingUnitRequest {
  // name of the lodging unit
  repeated CreateLodgingUnitRequest lodging_unit_params_list = 1 [(validate.rules).repeated = {min_items: 1}];
}

/**
 * Response body for create LodgingUnit
 */
message BatchCreateLodgingUnitResponse {
  // lodging unit list
  repeated models.offering.v1.LodgingUnitModel lodging_unit_list = 1;
}

/**
 * Request body for create LodgingUnit service
 */
message CreateLodgingUnitRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // token staff id
  int64 token_staff_id = 3 [(validate.rules).int64.gt = 0];
  // name of the lodging unit
  string name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // lodging type of this lodging unit
  int64 lodging_type_id = 5 [(validate.rules).int64.gt = 0];
  // camera id
  optional int64 camera_id = 6 [(validate.rules).int64 = {gte: 0}];
}

/**
 * Request body for update LodgingUnit
 */
message UpdateLodgingUnitRequest {
  // id of the lodging unit
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // token staff id
  int64 token_staff_id = 2 [(validate.rules).int64.gt = 0];
  // company id for authorization
  optional int64 company_id = 3 [(validate.rules).int64.gt = 0];
  // name of the lodging unit
  optional string name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // camera id
  optional int64 camera_id = 5 [(validate.rules).int64 = {gte: 0}];
}

/**
 * Response body for update LodgingUnit
 */
message UpdateLodgingUnitResponse {
  // lodging unit
  models.offering.v1.LodgingUnitModel lodging_unit = 1;
}

/**
 * Request body for delete LodgingUnit
 */
message DeleteLodgingUnitRequest {
  // id of the lodging unit
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // token staff id
  int64 token_staff_id = 2 [(validate.rules).int64.gt = 0];
  // company id for authorization
  optional int64 company_id = 3 [(validate.rules).int64.gt = 0];
}

/**
 * Response body for delete LodgingUnit
 */
message DeleteLodgingUnitResponse {}

/**
 * Request body for batch delete LodgingUnit
 */
message BatchDeleteLodgingUnitRequest {
  // id list of the lodging unit
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    max_items: 100
    items: {
      int64: {gt: 0}
    }
  }];
  // token staff id
  optional int64 token_staff_id = 2 [(validate.rules).int64.gt = 0];
  // company id for authorization
  optional int64 company_id = 3 [(validate.rules).int64.gt = 0];
}

/**
 * Response body for batch delete LodgingUnit
 */
message BatchDeleteLodgingUnitResponse {}

/**
 * Request body for get LodgingUnit list
 */
message GetLodgingUnitListRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // lodging type id list
  optional LodgingTypeIdList type_id_list = 3;
  // service id
  optional int64 service_id = 4 [(validate.rules).int64 = {gt: 0}];
  // evaluation service id
  optional int64 evaluation_service_id = 5 [(validate.rules).int64 = {gt: 0}];
  // lodging unit ids
  repeated int64 unit_ids = 6 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // camera ids
  repeated int64 camera_ids = 7 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

/**
 * Request body for lodging type id list
 */
message LodgingTypeIdList {
  // lodging type id list
  repeated int64 id_list = 1 [(validate.rules).repeated = {min_items: 0}];
}

/**
 * get LodgingUnit list response
 */
message GetLodgingUnitListResponse {
  // lodging unit list
  repeated models.offering.v1.LodgingUnitModel lodging_unit_list = 1;
}

/**
 * Request body for get LodgingUnit list
 */
message MGetLodgingUnitRequest {
  // lodging unit id list
  repeated int64 id_list = 1 [(validate.rules).repeated = {min_items: 0}];
}

/**
 * get LodgingUnit list response
 */
message MGetLodgingUnitResponse {
  // lodging unit list
  repeated models.offering.v1.LodgingUnitModel lodging_unit_list = 1;
  // relate lodging type list
  repeated models.offering.v1.LodgingTypeModel lodging_type_list = 2;
}

// The request for sort lodging unit by ids
message SortLodgingUnitByIdsRequest {
  // ids of lodging unit to sort
  repeated int64 ids = 1 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // the company id
  optional int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // the login staff id
  optional int64 staff_id = 3 [(validate.rules).int64.gt = 0];
}

// The response for sort lodging unit by ids
message SortLodgingUnitByIdsResponse {}

// lodging Unit service
service LodgingUnitService {
  // batch create lodging Unit
  rpc BatchCreateLodgingUnit(BatchCreateLodgingUnitRequest) returns (BatchCreateLodgingUnitResponse);
  // update lodging Unit
  rpc UpdateLodgingUnit(UpdateLodgingUnitRequest) returns (UpdateLodgingUnitResponse);
  // delete lodging Unit
  rpc DeleteLodgingUnit(DeleteLodgingUnitRequest) returns (DeleteLodgingUnitResponse);
  // batch delete lodging Unit
  rpc BatchDeleteLodgingUnit(BatchDeleteLodgingUnitRequest) returns (BatchDeleteLodgingUnitResponse);
  // get lodging Unit list
  rpc GetLodgingUnitList(GetLodgingUnitListRequest) returns (GetLodgingUnitListResponse);
  // get lodging Unit by id list
  rpc MGetLodgingUnit(MGetLodgingUnitRequest) returns (MGetLodgingUnitResponse);
  // sort lodging Unit list
  rpc SortLodgingUnitByIds(SortLodgingUnitByIdsRequest) returns (SortLodgingUnitByIdsResponse);
}
