syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_vaccine_models.proto";
import "moego/models/business_customer/v1/business_pet_vaccine_record_defs.proto";
import "moego/models/business_customer/v1/business_pet_vaccine_record_models.proto";
import "moego/models/organization/v1/tenant.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get pet vaccine record request
message GetPetVaccineRecordRequest {
  // pet vaccine record id
  int64 id = 1;
}

// get pet vaccine record response
message GetPetVaccineRecordResponse {
  // vaccine record
  moego.models.business_customer.v1.BusinessPetVaccineRecordModel vaccine_record = 1;
}

// list pet vaccine record request
message ListPetVaccineRecordRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];

  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];

  // pet id
  int64 pet_id = 3 [(validate.rules).int64.gt = 0];

  // include vaccine, default is false
  bool include_vaccine = 4;
}

// list pet vaccine response
message ListPetVaccineRecordResponse {
  // vaccine records
  repeated moego.models.business_customer.v1.BusinessPetVaccineRecordModel vaccine_records = 1;

  // vaccines, if include_vaccine is false, this field will be empty
  repeated moego.models.business_customer.v1.BusinessPetVaccineModel vaccines = 2;
}

// batch list vaccine record request
message BatchListVaccineRecordRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];

  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];

  // pet ids
  repeated int64 pet_ids = 3 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    items: {
      int64: {gt: 0}
    }
  }];

  // include vaccine, default is false
  bool include_vaccine = 4;
}

// batch list vaccine record response
message BatchListVaccineRecordResponse {
  // vaccine record bindings
  repeated moego.models.business_customer.v1.BusinessPetVaccineRecordBindingModel bindings = 1;

  // vaccines, if include_vaccine is false, this field will be empty
  repeated moego.models.business_customer.v1.BusinessPetVaccineModel vaccines = 2;
}

// batch create pet vaccine record request
message BatchCreatePetVaccineRecordRequest {
  // tenant, optional
  optional models.organization.v1.Tenant tenant = 1;

  // pet id
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];

  // vaccine records
  repeated moego.models.business_customer.v1.BusinessPetVaccineRecordCreateDef vaccine_records = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
  }];

  // 是否移除重复的疫苗记录
  // 若为 true, 如果新增的疫苗记录与已有的疫苗记录重复(vaccine id, expiration date, source, document urls 完全一致), 则不会被添加.
  // 若为 false, 新增的疫苗记录即使跟已有的疫苗记录重复了也不会移除, 会被添加.
  bool remove_duplication = 4;
}

// batch create pet vaccine record response
message BatchCreatePetVaccineRecordResponse {}

// update pet vaccine records request
message UpdatePetVaccineRecordsRequest {
  // tenant, optional
  optional models.organization.v1.Tenant tenant = 1;

  // pet id
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];

  // vaccine records
  repeated moego.models.business_customer.v1.BusinessPetVaccineRecordUpdateDef vaccine_records = 4 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
  }];
}

// update pet vaccine records response
message UpdatePetVaccineRecordsResponse {}

// Service for pet vaccine record
service BusinessPetVaccineRecordService {
  // Get a vaccine record
  rpc GetPetVaccineRecord(GetPetVaccineRecordRequest) returns (GetPetVaccineRecordResponse);

  // List vaccine record of a pet
  rpc ListPetVaccineRecord(ListPetVaccineRecordRequest) returns (ListPetVaccineRecordResponse);

  // Batch list vaccine record of several pets
  rpc BatchListVaccineRecord(BatchListVaccineRecordRequest) returns (BatchListVaccineRecordResponse);

  // Batch create vaccine records for a pet
  rpc BatchCreatePetVaccineRecord(BatchCreatePetVaccineRecordRequest) returns (BatchCreatePetVaccineRecordResponse);

  // Update pet vaccine records
  rpc UpdatePetVaccineRecords(UpdatePetVaccineRecordsRequest) returns (UpdatePetVaccineRecordsResponse);
}
