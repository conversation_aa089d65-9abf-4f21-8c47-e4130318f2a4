syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_evaluation_models.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/organization/v1/tenant.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

//create pet evaluation history request
message CreatePetEvaluationHistoryRequest {
  // tenant
  moego.models.organization.v1.Tenant tenant = 1 [(validate.rules).message = {required: true}];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];
  // evaluation status
  optional models.customer.v1.EvaluationStatus evaluation_status = 3;
  // evaluation action type
  moego.models.business_customer.v1.PetEvaluationHistoryModel.ActionType action_type = 4;
  // operator staff id
  optional int64 operator_staff_id = 5;
  //evaluation
  optional int64 evaluation_id = 6;
}

// create pet evaluation history response
message CreatePetEvaluationHistoryResponse {}

// list evaluation history request
message ListPetEvaluationHistoryRequest {
  // tenant
  moego.models.organization.v1.Tenant tenant = 1 [(validate.rules).message = {required: true}];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];
}

// list evaluation history response
message ListPetEvaluationHistoryResponse {
  // evaluation history models
  repeated models.business_customer.v1.PetEvaluationHistoryModel evaluation_histories = 1;
}

// reset pet evaluation request task
message ResetPetEvaluationTaskRequest {}

// reset pet evaluation response task
message ResetPetEvaluationTaskResponse {}

// UpdatePetEvaluationParams
message UpdatePetEvaluationRequest {
  // tenant
  moego.models.organization.v1.Tenant tenant = 1 [(validate.rules).message = {required: true}];
  // pet ids
  repeated int64 pet_ids = 3;
  // evaluation id
  int64 evaluation_id = 4 [(validate.rules).int64.gt = 0];
  // evaluation status
  models.customer.v1.EvaluationStatus evaluation_status = 5;
}

// update pet evaluation result
message UpdatePetEvaluationResponse {}

// list pet evaluation request
message ListPetEvaluationRequest {
  // tenant
  moego.models.organization.v1.Tenant tenant = 1 [(validate.rules).message = {required: true}];
  // pet id
  repeated int64 pet_ids = 2;
}

// list pet evaluation response
message ListPetEvaluationResponse {
  // pet evaluations
  repeated moego.models.business_customer.v1.PetEvaluationModel pet_evaluations = 1;
}

// service for pet evaluation
service BusinessPetEvaluationService {
  // create pet evaluation history
  rpc CreatePetEvaluationHistory(CreatePetEvaluationHistoryRequest) returns (CreatePetEvaluationHistoryResponse);
  // list pet evaluation history
  rpc ListPetEvaluationHistory(ListPetEvaluationHistoryRequest) returns (ListPetEvaluationHistoryResponse);
  // pet reset evaluation
  rpc ResetPetEvaluationTask(ResetPetEvaluationTaskRequest) returns (ResetPetEvaluationTaskResponse);
  // update pet evaluation
  rpc UpdatePetEvaluation(UpdatePetEvaluationRequest) returns (UpdatePetEvaluationResponse);
  // List pet evaluation
  rpc ListPetEvaluation(ListPetEvaluationRequest) returns (ListPetEvaluationResponse);
}
