syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_size_defs.proto";
import "moego/models/business_customer/v1/business_pet_size_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// list pet size request
message ListPetSizeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
}

// list pet size response
message ListPetSizeResponse {
  // pet size list
  repeated moego.models.business_customer.v1.BusinessPetSizeModel sizes = 1;
}

// batch upsert pet size request
message BatchUpsertPetSizeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];

  // pet sizes to create, no pet sizes will be created if it is empty
  repeated moego.models.business_customer.v1.BusinessPetSizeUpsertDef sizes_to_create = 3;

  // pet sizes to update, no pet sizes will be updated if it is empty, and the pet sizes in database will be deleted
  map<int64, moego.models.business_customer.v1.BusinessPetSizeUpsertDef> sizes_to_update = 4;
}

// batch upsert pet size response
message BatchUpsertPetSizeResponse {
  // pet sizes
  repeated moego.models.business_customer.v1.BusinessPetSizeModel sizes = 1;
  // deleted pet size ids
  repeated int64 deleted_size_ids = 2;
}

// Service for pet size settings
service BusinessPetSizeService {
  // List pet sizes.
  // If the company does not exists, or does not define any pet sizes, an empty list will be returned rather than an error.
  rpc ListPetSize(ListPetSizeRequest) returns (ListPetSizeResponse);

  // Batch upsert pet sizes.
  // Each pet size name must be unique among all pet sizes.
  //
  // Error sizes:
  // - CODE_PARAMS_ERROR: The name is already used by another pet size, or the pet size does not exist, or has been
  //                      deleted, or does not belong to the company or business.
  rpc BatchUpsertPetSize(BatchUpsertPetSizeRequest) returns (BatchUpsertPetSizeResponse);
}
