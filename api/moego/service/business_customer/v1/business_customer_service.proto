syntax = "proto3";

package moego.service.business_customer.v1;

import "google/type/interval.proto";
import "moego/models/business_customer/v1/business_customer_defs.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/business_customer/v1/business_customer_pet_defs.proto";
import "moego/models/organization/v1/tenant.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get customer request
message GetCustomerRequest {
  option deprecated = true;

  // company id, deprecated, use tenant instead
  int64 company_id = 1 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];
  // tenant, optional for id and customer code, required for phone number and email
  optional moego.models.organization.v1.Tenant tenant = 6;

  // customer's identifier
  oneof identifier {
    option (validate.required) = true;
    // customer id
    int64 id = 2 [(validate.rules).int64 = {gt: 0}];
    // customer code
    string customer_code = 3 [(validate.rules).string = {
      min_len: 1
      max_len: 10
    }];
    // customer's phone number (main contact)
    string phone_number = 4 [(validate.rules).string = {
      min_len: 1
      max_len: 20
    }];
    // customer's email (main contact)
    string email = 5 [(validate.rules).string = {
      min_len: 1
      max_len: 50
    }];
  }
}

// get customer response
message GetCustomerResponse {
  option deprecated = true;
  // customer
  moego.models.business_customer.v1.BusinessCustomerModel customer = 1;
}

// get customer info request
message GetCustomerInfoRequest {
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 1;
  // customer id
  int64 id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get customer info response
message GetCustomerInfoResponse {
  // customer
  moego.models.business_customer.v1.BusinessCustomerInfoModel customer = 1;
}

// batch get customer request
message BatchGetCustomerRequest {
  option deprecated = true;
  // company id, deprecated, use tenant instead
  int64 company_id = 1 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];
  // customer ids
  repeated int64 ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 3;
}

// batch get customer response
message BatchGetCustomerResponse {
  option deprecated = true;
  // customer list
  repeated moego.models.business_customer.v1.BusinessCustomerModel customers = 1;
}

// batch get customer info request
message BatchGetCustomerInfoRequest {
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 1;
  // customer ids
  repeated int64 ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch get customer info response
message BatchGetCustomerInfoResponse {
  // customer list
  repeated moego.models.business_customer.v1.BusinessCustomerInfoModel customers = 1;
}

// update customer preferred business request
message UpdateCustomerPreferredBusinessRequest {
  // company id, deprecated, use tenant instead
  int64 company_id = 1 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 5;

  // customer id
  int64 customer_id = 2 [(validate.rules).int64 = {gt: 0}];
  // preferred business id
  int64 preferred_business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // updated by, staff id
  int64 updated_by = 4 [(validate.rules).int64 = {gt: 0}];
}

// update customer preferred business response
message UpdateCustomerPreferredBusinessResponse {}

// list customers request
message ListCustomersRequest {
  // filter, each field will be used as AND , under the same field will be used as OR
  message Filter {
    // update time
    google.type.Interval update_time = 1;
  }
  // pagination
  moego.utils.v2.PaginationRequest pagination = 1 [(validate.rules).message = {required: true}];
  // tenant
  moego.models.organization.v1.Tenant tenant = 2 [(validate.rules).message = {required: true}];
  // filter
  Filter filter = 3;
}

// list customers response
message ListCustomersResponse {
  // pagination
  moego.utils.v2.PaginationResponse pagination = 1;
  // customer list
  repeated moego.models.business_customer.v1.BusinessCustomerModel customers = 2;
}

// create customer with additional info request
message CreateCustomerWithAdditionalInfoRequest {
  // tenant, required
  // Tenant 里的 business id 只是用来兼容 AS 之前的逻辑, 用于约束 email 和 phone number 的唯一范围.
  // - 如果 Tenant 设置了 business id, 则在 business 范围内检查 email 和 phone number 的唯一性.
  // - 如果 Tenant 只设置了 company id, 则在 company 范围内检查 email 和 phone number 的唯一性.
  // 注意, 如果需要设置 preferred business, 请填 BusinessCustomerCreateDef 里的 preferred_business id.
  moego.models.organization.v1.Tenant tenant = 1 [(validate.rules).message = {required: true}];

  // created by (staff id), optional
  optional int64 created_by = 2 [(validate.rules).int64 = {gt: 0}];

  // customer with additional info
  moego.models.business_customer.v1.BusinessCustomerWithAdditionalInfoCreateDef customer_with_additional_info = 3 [(validate.rules).message = {required: true}];

  // pets with additional info
  repeated moego.models.business_customer.v1.BusinessCustomerPetWithAdditionalInfoCreateDef pets_with_additional_info = 4 [(validate.rules).repeated = {
    max_items: 20
    items: {
      message: {required: true}
    }
  }];
}

// create customer with additional info response
message CreateCustomerWithAdditionalInfoResponse {
  // customer id
  int64 customer_id = 1;
}

// update customer request
message UpdateCustomerRequest {
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 1;

  // updated by (staff id), optional
  optional int64 updated_by = 2 [(validate.rules).int64 = {gt: 0}];

  // customer id
  int64 id = 3 [(validate.rules).int64.gt = 0];

  // customer update def
  models.business_customer.v1.BusinessCustomerUpdateDef customer = 4 [(validate.rules).message = {required: true}];
}

// update customer response
message UpdateCustomerResponse {}

// check identifier request
message CheckIdentifierRequest {
  // tenant, required
  moego.models.organization.v1.Tenant tenant = 1 [(validate.rules).message = {required: true}];

  // identifier
  oneof identifier {
    option (validate.required) = true;
    // phone number
    string phone_number = 2 [(validate.rules).string = {
      min_len: 1
      max_len: 20
    }];

    // email
    string email = 3 [(validate.rules).string = {
      min_len: 1
      max_len: 50
    }];
  }
}

// check identifier response
message CheckIdentifierResponse {
  // exist
  bool exist = 1;

  // 是否有多个 customer 使用了该 identifier
  // 当 exist 为 false 时, multiple 恒为 false
  // 当 exist 为 true 时:
  // - 如果只有一个 customer 使用了该 identifier, multiple 为 false
  // - 如果有多个 customer 使用了该 identifier, multiple 为 true
  bool multiple = 2;

  // 当 exist 为 true 时, 返回使用了该 identifier 的 customer id
  // 如果有多个 customer 使用了该 identifier, 返回其中最大的 customer id
  int64 customer_id = 3;
}

// Service for business customer
service BusinessCustomerService {
  // get business customer, deprecated
  // use `GetCustomerInfo` instead
  rpc GetCustomer(GetCustomerRequest) returns (GetCustomerResponse) {
    option deprecated = true;
  }

  // get customer info
  rpc GetCustomerInfo(GetCustomerInfoRequest) returns (GetCustomerInfoResponse);

  // batch get business customer, deprecated
  // use `BatchGetCustomerInfo` instead
  rpc BatchGetCustomer(BatchGetCustomerRequest) returns (BatchGetCustomerResponse) {
    option deprecated = true;
  }

  // batch get business customer info
  rpc BatchGetCustomerInfo(BatchGetCustomerInfoRequest) returns (BatchGetCustomerInfoResponse);

  // Update customer's preferred business.
  // 1. Update business id of customer (should not be deleted)
  // 2. Update business id of customer's address (exclude deleted)
  // 3. Update business id of customer's contact (exclude deleted)
  // 4. Update business id of customer's preferred tip config
  // 5. Update business id of customer's pets (include passed away, exclude deleted)
  //
  // Notice that business id of customer's notes and pets' notes doesn't need to be changed.
  rpc UpdateCustomerPreferredBusiness(UpdateCustomerPreferredBusinessRequest) returns (UpdateCustomerPreferredBusinessResponse);
  // list customers
  rpc ListCustomers(ListCustomersRequest) returns (ListCustomersResponse);

  // create a customer with additional info
  // additional info includes:
  // - communication preference
  // - appointment preference
  // - payment preference
  // - additional contacts
  // - addresses
  // - pets with additional info
  rpc CreateCustomerWithAdditionalInfo(CreateCustomerWithAdditionalInfoRequest) returns (CreateCustomerWithAdditionalInfoResponse);

  // update customer
  rpc UpdateCustomer(UpdateCustomerRequest) returns (UpdateCustomerResponse);

  // check if the identifier (phone number) is used in the tenant
  rpc CheckIdentifier(CheckIdentifierRequest) returns (CheckIdentifierResponse);
}
