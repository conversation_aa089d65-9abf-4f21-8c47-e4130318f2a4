syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_customer_referral_source_defs.proto";
import "moego/models/business_customer/v1/business_customer_referral_source_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get customer referral source request
message GetCustomerReferralSourceRequest {
  // referral source id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// get customer referral source response
message GetCustomerReferralSourceResponse {
  // referral source
  moego.models.business_customer.v1.BusinessCustomerReferralSourceModel referral_source = 1;
}

// list customer referral source request
message ListCustomerReferralSourceRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
}

// list customer referral source response
message ListCustomerReferralSourceResponse {
  // customer referral source list
  repeated moego.models.business_customer.v1.BusinessCustomerReferralSourceModel referral_sources = 1;
}

// list customer referral source template request
message ListCustomerReferralSourceTemplateRequest {}

// list customer referral source template response
message ListCustomerReferralSourceTemplateResponse {
  // customer referral source template
  repeated moego.models.business_customer.v1.BusinessCustomerReferralSourceModel referral_sources = 1;
}

// create customer referral source request
message CreateCustomerReferralSourceRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];

  // referral source
  moego.models.business_customer.v1.BusinessCustomerReferralSourceCreateDef referral_source = 3 [(validate.rules).message.required = true];
}

// create customer referral source response
message CreateCustomerReferralSourceResponse {
  // tag
  moego.models.business_customer.v1.BusinessCustomerReferralSourceModel referral_source = 1;
}

// update customer referral source request
message UpdateCustomerReferralSourceRequest {
  // referral source id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];

  // tag
  moego.models.business_customer.v1.BusinessCustomerReferralSourceUpdateDef referral_source = 4 [(validate.rules).message.required = true];
}

// update customer referral source response
message UpdateCustomerReferralSourceResponse {}

// sort customer referral source request
message SortCustomerReferralSourceRequest {
  // referral source id list, should contain all referral source ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];

  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// sort customer referral source response
message SortCustomerReferralSourceResponse {}

// delete customer referral source request
message DeleteCustomerReferralSourceRequest {
  // referral source id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// delete customer referral source response
message DeleteCustomerReferralSourceResponse {}

// Service for customer referral source settings
service BusinessCustomerReferralSourceService {
  // Get a customer referral source.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The referral source does not exist, or does not belong to the given company or business.
  rpc GetCustomerReferralSource(GetCustomerReferralSourceRequest) returns (GetCustomerReferralSourceResponse);

  // List customer referral sources.
  // If the company does not exists, or does not define any referral sources, an empty list will be returned rather than an error.
  rpc ListCustomerReferralSource(ListCustomerReferralSourceRequest) returns (ListCustomerReferralSourceResponse);

  // List customer referral source template.
  rpc ListCustomerReferralSourceTemplate(ListCustomerReferralSourceTemplateRequest) returns (ListCustomerReferralSourceTemplateResponse);

  // Create a customer referral source.
  // The name of the new referral source must be unique among all referral sources of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The name is already used by another referral source of the company or business.
  rpc CreateCustomerReferralSource(CreateCustomerReferralSourceRequest) returns (CreateCustomerReferralSourceResponse);

  // Update a customer referral source.
  // If the name of the referral source is changed, it must be unique among all referral source of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The name is already used by another referral source of the company or business, or the
  //                      referral source does not exist, or has been deleted, or does not belong to the company or business.
  rpc UpdateCustomerReferralSource(UpdateCustomerReferralSourceRequest) returns (UpdateCustomerReferralSourceResponse);

  // Sort customer referral sources of the company or business.
  // Referral sources will be sorted according to the order of `ids`. If there are referral sources of the company or business
  // whose ids are not included in `ids`, they will be sorted to the end. If an id in `ids` does not exist or does not
  // belong to the company or business, it will be ignored.
  rpc SortCustomerReferralSource(SortCustomerReferralSourceRequest) returns (SortCustomerReferralSourceResponse);

  // Delete a customer referral source.
  // If the referral source is already deleted, it will return success without throwing any error.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The referral source does not exist, or does not belong to the given company.
  rpc DeleteCustomerReferralSource(DeleteCustomerReferralSourceRequest) returns (DeleteCustomerReferralSourceResponse);
}
