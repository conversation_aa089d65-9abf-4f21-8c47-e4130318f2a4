syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_medical_info_models.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// Business pet medical info service
service BusinessPetMedicalInfoService {
  // list pet medical info
  rpc ListPetMedicalInfo(ListPetMedicalInfoRequest) returns (ListPetMedicalInfoResponse) {}
}

// list pet medical info request
message ListPetMedicalInfoRequest {
  // filter
  message Filter {
    // pet ids
    repeated int64 pet_ids = 1;
  }
  // filter
  Filter filter = 1;
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 2;
}

// list pet medical info response
message ListPetMedicalInfoResponse {
  // pet medical info
  repeated moego.models.business_customer.v1.BusinessPetMedicalInfoModel pet_medical_info = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}
