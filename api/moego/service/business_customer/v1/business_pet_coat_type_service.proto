syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_coat_type_defs.proto";
import "moego/models/business_customer/v1/business_pet_coat_type_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get pet coat type request
message GetPetCoatTypeRequest {
  // pet coat type id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// get pet coat type response
message GetPetCoatTypeResponse {
  // pet coat type
  moego.models.business_customer.v1.BusinessPetCoatTypeModel coat_type = 1;
}

// list pet coat type request
message ListPetCoatTypeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
}

// list pet coat type response
message ListPetCoatTypeResponse {
  // pet coat type list
  repeated moego.models.business_customer.v1.BusinessPetCoatTypeModel coat_types = 1;
}

// list pet coat type template request
message ListPetCoatTypeTemplateRequest {}

// list pet coat type template response
message ListPetCoatTypeTemplateResponse {
  // pet coat type template
  repeated moego.models.business_customer.v1.BusinessPetCoatTypeModel coat_types = 1;
}

// create pet coat type request
message CreatePetCoatTypeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];

  // pet coat type
  moego.models.business_customer.v1.BusinessPetCoatTypeCreateDef coat_type = 3 [(validate.rules).message.required = true];
}

// create pet coat type response
message CreatePetCoatTypeResponse {
  // pet coat type
  moego.models.business_customer.v1.BusinessPetCoatTypeModel coat_type = 1;
}

// update pet coat type request
message UpdatePetCoatTypeRequest {
  // pet coatType id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];

  // pet coat type
  moego.models.business_customer.v1.BusinessPetCoatTypeUpdateDef coat_type = 4 [(validate.rules).message.required = true];
}

// update pet coat type response
message UpdatePetCoatTypeResponse {}

// sort pet coat type request
message SortPetCoatTypeRequest {
  // pet coat type id list, should contain all pet coatType ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];

  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// sort pet coat type response
message SortPetCoatTypeResponse {}

// delete pet coat type request
message DeletePetCoatTypeRequest {
  // pet coat type id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// delete pet coat type response
message DeletePetCoatTypeResponse {}

// Service for pet coat type settings
service BusinessPetCoatTypeService {
  // Get a pet coat type.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The pet coat type does not exist, or does not belong to the given company or business.
  rpc GetPetCoatType(GetPetCoatTypeRequest) returns (GetPetCoatTypeResponse);

  // List pet coat types.
  // If the company does not exists, or does not define any pet coat types, an empty list will be returned rather than an error.
  rpc ListPetCoatType(ListPetCoatTypeRequest) returns (ListPetCoatTypeResponse);

  // List pet coat type template.
  rpc ListPetCoatTypeTemplate(ListPetCoatTypeTemplateRequest) returns (ListPetCoatTypeTemplateResponse);

  // Create a pet coat type.
  // The name of the new pet coat type must be unique among all pet coat types of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The name is already used by another pet coat type of the company or business.
  rpc CreatePetCoatType(CreatePetCoatTypeRequest) returns (CreatePetCoatTypeResponse);

  // Update a pet coat type.
  // If the name of the pet coat type is changed, it must be unique among all pet coat types of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The name is already used by another pet coat type of the company or business, or the pet coat
  //                      type does not exist, or has been deleted, or does not belong to the company or business.
  rpc UpdatePetCoatType(UpdatePetCoatTypeRequest) returns (UpdatePetCoatTypeResponse);

  // Sort pet coat type of the company or business.
  // Pet coat types will be sorted according to the order of `ids`. If there are coat types of the company or business
  // whose ids are not included in `ids`, they will be sorted to the end. If an id in `ids` does not exist or does not
  // belong to the company or business, it will be ignored.
  rpc SortPetCoatType(SortPetCoatTypeRequest) returns (SortPetCoatTypeResponse);

  // Delete a pet coat type.
  // If the pet coat type is already deleted, will return success without throwing any error.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The pet coat type does not exist, or does not belong to the given company.
  rpc DeletePetCoatType(DeletePetCoatTypeRequest) returns (DeletePetCoatTypeResponse);
}
