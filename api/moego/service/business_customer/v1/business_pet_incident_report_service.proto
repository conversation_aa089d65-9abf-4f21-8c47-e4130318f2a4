syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_incident_report_defs.proto";
import "moego/models/business_customer/v1/business_pet_incident_report_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get pet incident report request
message GetPetIncidentReportRequest {
  // pet incident report id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// get pet incident report response
message GetPetIncidentReportResponse {
  // pet incident report
  moego.models.business_customer.v1.BusinessPetIncidentReportModel incident_report = 1;
}

// list pet incident report request
message ListPetIncidentReportRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // pet id
  int64 pet_id = 3 [(validate.rules).int64.gt = 0];
}

// list pet incident report response
message ListPetIncidentReportResponse {
  // pet incident report list
  repeated moego.models.business_customer.v1.BusinessPetIncidentReportModel incident_reports = 1;
}

// create pet incident report request
message CreatePetIncidentReportRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // pet incident report
  moego.models.business_customer.v1.BusinessPetIncidentReportCreateDef incident_report = 2 [(validate.rules).message.required = true];
  // operate business id
  int64 business_id = 3;
  // operate staff id
  int64 staff_id = 4;
}

// create pet incident report response
message CreatePetIncidentReportResponse {
  // pet incident report
  moego.models.business_customer.v1.BusinessPetIncidentReportModel incident_report = 1;
}

// update pet incident report request
message UpdatePetIncidentReportRequest {
  // pet incident report id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // pet incident report
  moego.models.business_customer.v1.BusinessPetIncidentReportUpdateDef incident_report = 3 [(validate.rules).message.required = true];
  // operate business id
  int64 business_id = 4;
  // operate staff id
  int64 staff_id = 5;
}

// update pet incident report response
message UpdatePetIncidentReportResponse {}

// delete pet incident report request
message DeletePetIncidentReportRequest {
  // pet incident report id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // operate business id
  int64 business_id = 3;
  // operate staff id
  int64 staff_id = 4;
}

// delete pet incident report response
message DeletePetIncidentReportResponse {}

// batch get pet incident report request
message BatchGetPetIncidentReportRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id ( if set, will return incident reports of the pet in the business )
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // pet ids
  repeated int64 pet_ids = 3 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch get pet incident report response
message BatchGetPetIncidentReportResponse {
  // pet incident report map, key is pet id, value is pet incident reports
  map<int64, IncidentReportsList> incident_reports_map = 1;

  // pet incident report list
  message IncidentReportsList {
    // pet incident report list
    repeated moego.models.business_customer.v1.BusinessPetIncidentReportModel incident_reports = 1;
  }
}

// Service for pet incident report settings
service BusinessPetIncidentReportService {
  // Get a pet incident report.
  rpc GetPetIncidentReport(GetPetIncidentReportRequest) returns (GetPetIncidentReportResponse);

  // List pet incident reports.
  rpc ListPetIncidentReport(ListPetIncidentReportRequest) returns (ListPetIncidentReportResponse);

  // Create a pet incident report.
  rpc CreatePetIncidentReport(CreatePetIncidentReportRequest) returns (CreatePetIncidentReportResponse);

  // Update a pet incident report.
  rpc UpdatePetIncidentReport(UpdatePetIncidentReportRequest) returns (UpdatePetIncidentReportResponse);

  // Delete a pet incident report.
  rpc DeletePetIncidentReport(DeletePetIncidentReportRequest) returns (DeletePetIncidentReportResponse);

  // Batch get pet incident reports.
  rpc BatchGetPetIncidentReport(BatchGetPetIncidentReportRequest) returns (BatchGetPetIncidentReportResponse);
}
