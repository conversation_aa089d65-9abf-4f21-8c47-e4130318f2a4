syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_customer_contact_models.proto";
import "moego/models/organization/v1/tenant.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// Service for business customer's contacts
service BusinessCustomerContactService {
  // Get the primary contact for a customer
  rpc GetCustomerPrimaryContact(GetCustomerPrimaryContactRequest) returns (GetCustomerPrimaryContactResponse);

  // List contacts by given conditions
  rpc ListCustomerContact(ListCustomerContactRequest) returns (ListCustomerContactResponse);

  // List contacts by phone number
  rpc ListCustomerContactByPhoneNumber(ListCustomerContactByPhoneNumberRequest) returns (ListCustomerContactByPhoneNumberResponse);

  // fill up E164 phone number
  rpc FillE164PhoneNumber(FillE164PhoneNumberRequest) returns (FillE164PhoneNumberResponse);
}

// get customer primary contact request
message GetCustomerPrimaryContactRequest {
  // company id, deprecated, use tenant instead
  int64 company_id = 1 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 3;

  // customer id
  int64 customer_id = 2 [(validate.rules).int64.gt = 0];
}

// get customer primary contact response
message GetCustomerPrimaryContactResponse {
  // Customer's primary contact.
  // It should exist in most cases. In extreme cases (e.g.: imported by DM), it may not exist.
  optional moego.models.business_customer.v1.BusinessCustomerContactModel contact = 1;
}

// list customer contact request
message ListCustomerContactRequest {
  // company id, deprecated, use tenant instead
  int64 company_id = 1 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];

  // tenant, optional if customer_id is set, otherwise required
  optional moego.models.organization.v1.Tenant tenant = 3;

  // customer id, optional
  int64 customer_id = 2 [(validate.rules).int64.gte = 0];

  // phone number, optional
  // if not empty, list customer with the phone number (main contact)
  optional string phone_number = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 20
  }];

  // contact type, optional
  // if not set, both main and additional contacts will be included
  optional ContactType contact_type = 5;

  // pagination, required
  moego.utils.v2.PaginationRequest pagination = 10;

  // contact type
  message ContactType {
    // include main contact, default is true
    optional bool main = 1;
    // include additional contact, default is true
    optional bool additional = 2;
  }
}

// list customer contact response
message ListCustomerContactResponse {
  // contacts
  repeated moego.models.business_customer.v1.BusinessCustomerContactModel contacts = 1;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// list customer contact by phone number request
message ListCustomerContactByPhoneNumberRequest {
  // phone number, required
  string phone_number = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 20
  }];
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 2;
}

// list customer contact by phone number response
message ListCustomerContactByPhoneNumberResponse {
  // contacts
  repeated moego.models.business_customer.v1.BusinessCustomerContactModel contacts = 1;
}

// fill up E164 phone number request
message FillE164PhoneNumberRequest {
  // e164 phone number def
  message E164Def {
    // customer contact id, required
    int64 customer_contact_id = 1 [(validate.rules).int64.gt = 0];
    // phone number, required
    string phone_number = 2 [(validate.rules).string = {
      min_len: 1
      max_len: 20
    }];
  }
  // e164 phone number def
  repeated E164Def e164_defs = 1;
}

// fill up E164 phone number response
message FillE164PhoneNumberResponse {
  // customer contacts
  repeated moego.models.business_customer.v1.BusinessCustomerContactModel contacts = 1;
}
