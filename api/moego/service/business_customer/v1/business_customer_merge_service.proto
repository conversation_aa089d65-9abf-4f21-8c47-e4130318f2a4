syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_customer_merge_defs.proto";
import "moego/models/business_customer/v1/business_customer_merge_enums.proto";
import "moego/models/business_customer/v1/business_customer_merge_models.proto";
import "moego/models/organization/v1/tenant.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// Merge operation for business customer
service BusinessCustomerMergeService {
  // list duplicate customer groups
  rpc ListDuplicateCustomerGroups(ListDuplicateCustomerGroupsRequest) returns (ListDuplicateCustomerGroupsResponse);

  // check if the given customers are duplicate
  rpc CheckCustomerDuplication(CheckCustomerDuplicationRequest) returns (CheckCustomerDuplicationResponse);

  // preview pet merge relation
  rpc PreviewPetMergeRelation(PreviewPetMergeRelationRequest) returns (PreviewPetMergeRelationResponse);

  // Merge customers
  rpc MergeCustomers(MergeCustomersRequest) returns (MergeCustomersResponse);

  // batch check customer merge status
  rpc BatchCheckCustomerMergeStatus(BatchCheckCustomerMergeStatusRequest) returns (BatchCheckCustomerMergeStatusResponse);
}

// list duplicate customer groups request
message ListDuplicateCustomerGroupsRequest {
  // tenant
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message = {required: true}];

  // 最多返回多少组
  int32 max_group_count = 2 [(validate.rules).int32 = {gt: 0}];

  // 在给定的 preferred_business_ids 中的查询 duplicate customers
  // 如果为空，则查询 tenant 下所有的 duplicate customers
  repeated int64 preferred_business_ids = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// list duplicate customer groups response
message ListDuplicateCustomerGroupsResponse {
  // customer groups, no more than max_group_count
  repeated models.business_customer.v1.DuplicateCustomerGroup groups = 1;
}

// check customer duplication request
message CheckCustomerDuplicationRequest {
  // tenant
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message = {required: true}];

  // customer ids
  repeated int64 customer_ids = 2 [(validate.rules).repeated = {
    unique: true
    max_items: 10
    items: {
      int64: {gt: 0}
    }
  }];
}

// check customer duplication response
message CheckCustomerDuplicationResponse {
  // duplicate
  bool duplicate = 1;
}

// preview pet merge relation request
message PreviewPetMergeRelationRequest {
  // tenant
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message = {required: true}];

  // customer merge relation
  models.business_customer.v1.MergeRelationDef customer_merge_relation = 2 [(validate.rules).message = {required: true}];
}

// preview pet merge relation response
message PreviewPetMergeRelationResponse {
  // pet merge relations
  repeated models.business_customer.v1.MergeRelationDef pet_merge_relations = 1;
}

// merge customers request
message MergeCustomersRequest {
  // tenant
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message = {required: true}];

  // customer merge relation
  models.business_customer.v1.MergeRelationDef customer_merge_relation = 2 [(validate.rules).message = {required: true}];
}

// merge customers response
message MergeCustomersResponse {}

// batch check customer merge status request
message BatchCheckCustomerMergeStatusRequest {
  // customer ids
  repeated int64 customer_ids = 1 [(validate.rules).repeated = {
    unique: true
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch check customer merge status response
message BatchCheckCustomerMergeStatusResponse {
  // merge status for each customer
  map<int64, models.business_customer.v1.CustomerMergeStatus> status = 1;
}
