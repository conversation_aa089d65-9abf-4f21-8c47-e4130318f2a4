syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_note_defs.proto";
import "moego/models/business_customer/v1/business_pet_note_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// list pet note request
message ListPetNoteRequest {
  // company id, optional
  int64 company_id = 1 [(validate.rules).int64.gte = 0];

  // pet id
  int64 pet_id = 3 [(validate.rules).int64.gt = 0];

  // pagination, required
  utils.v2.PaginationRequest pagination = 2;
}

// list pet note response
message ListPetNoteResponse {
  // pet note list
  repeated moego.models.business_customer.v1.BusinessPetNoteModel notes = 1;

  // pagination
  utils.v2.PaginationResponse pagination = 2;
}

// batch list pet note request
message BatchListPetNoteRequest {
  // pet ids
  repeated int64 pet_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 500
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch list pet notes response
message BatchListPetNoteResponse {
  // notes
  message PetNotes {
    // notes
    repeated moego.models.business_customer.v1.BusinessPetNoteModel notes = 1;
  }
  // pet id to notes
  map<int64, PetNotes> pet_notes_map = 1;
}

// create pet note request
message CreatePetNoteRequest {
  // created by staff id
  optional int64 created_by = 1 [(validate.rules).int64.gt = 0];

  // pet id
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];

  // pet note
  moego.models.business_customer.v1.BusinessPetNoteCreateDef note = 3 [(validate.rules).message.required = true];
}

// create pet note response
message CreatePetNoteResponse {
  // pet note
  moego.models.business_customer.v1.BusinessPetNoteModel note = 1;
}

// update pet note request
message UpdatePetNoteRequest {
  // updated by staff id
  optional int64 updated_by = 1 [(validate.rules).int64.gt = 0];

  // pet note id
  int64 id = 2 [(validate.rules).int64.gt = 0];

  // pet note
  moego.models.business_customer.v1.BusinessPetNoteUpdateDef note = 3 [(validate.rules).message.required = true];
}

// update pet note response
message UpdatePetNoteResponse {
  // pet note
  moego.models.business_customer.v1.BusinessPetNoteModel note = 1;
}

// delete pet note request
message DeletePetNoteRequest {
  // deleted by staff id
  optional int64 deleted_by = 1 [(validate.rules).int64.gt = 0];

  // pet note id
  int64 id = 2 [(validate.rules).int64.gt = 0];
}

// delete pet note response
message DeletePetNoteResponse {}

// get pet note request
message GetPetNoteRequest {
  // pet note id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// get pet note response
message GetPetNoteResponse {
  // pet note
  moego.models.business_customer.v1.BusinessPetNoteModel note = 1;
}

// get pet latest note request
message GetPetLatestNoteRequest {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];
}

// get pet latest note response
message GetPetLatestNoteResponse {
  // pet note, may not exist
  optional moego.models.business_customer.v1.BusinessPetNoteModel note = 1;
}

// pin pet note request
message PinPetNoteRequest {
  // updated by staff id
  optional int64 updated_by = 1 [(validate.rules).int64.gt = 0];

  // pet note id
  int64 id = 2 [(validate.rules).int64.gt = 0];

  // is pinned
  bool is_pinned = 3;

  // company id
  int64 company_id = 4 [(validate.rules).int64.gt = 0];

  // business id
  optional int64 business_id = 5 [(validate.rules).int64.gt = 0];
}

// pin pet note response
message PinPetNoteResponse {
  // pet note
  moego.models.business_customer.v1.BusinessPetNoteModel note = 1;
}

// Service for pet note
service BusinessPetNoteService {
  // Create pet note.
  // A pet can at most have 200 notes.
  rpc CreatePetNote(CreatePetNoteRequest) returns (CreatePetNoteResponse);

  // Update pet note
  rpc UpdatePetNote(UpdatePetNoteRequest) returns (UpdatePetNoteResponse);

  // Delete pet note
  rpc DeletePetNote(DeletePetNoteRequest) returns (DeletePetNoteResponse);

  // List pet notes of a pet
  rpc ListPetNote(ListPetNoteRequest) returns (ListPetNoteResponse);

  // Get pet note by id
  rpc GetPetNote(GetPetNoteRequest) returns (GetPetNoteResponse);

  // Get the latest note of a pet.
  // If the pet has no note, the response will be empty. Therefore, please check by `response.hasNote()` before using it.
  rpc GetPetLatestNote(GetPetLatestNoteRequest) returns (GetPetLatestNoteResponse);

  // Batch list pet note of multiple pets
  rpc BatchListPetNote(BatchListPetNoteRequest) returns (BatchListPetNoteResponse);

  // Pin or unpin a pet note
  rpc PinPetNote(PinPetNoteRequest) returns (PinPetNoteResponse);
}
