syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_fixed_defs.proto";
import "moego/models/business_customer/v1/business_pet_fixed_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get pet fixed request
message GetPetFixedRequest {
  // pet fixed id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// get pet fixed response
message GetPetFixedResponse {
  // pet fixed
  moego.models.business_customer.v1.BusinessPetFixedModel fixed = 1;
}

// list pet fixed request
message ListPetFixedRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
}

// list pet fixed response
message ListPetFixedResponse {
  // pet fixed list
  repeated moego.models.business_customer.v1.BusinessPetFixedModel fixeds = 1;
}

// create pet fixed request
message CreatePetFixedRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];

  // pet fixed
  moego.models.business_customer.v1.BusinessPetFixedCreateDef fixed = 3 [(validate.rules).message.required = true];
}

// create pet fixed response
message CreatePetFixedResponse {
  // pet fixed
  moego.models.business_customer.v1.BusinessPetFixedModel fixed = 1;
}

// update pet fixed request
message UpdatePetFixedRequest {
  // pet fixed id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];

  // pet fixed
  moego.models.business_customer.v1.BusinessPetFixedUpdateDef fixed = 4 [(validate.rules).message.required = true];
}

// update pet fixed response
message UpdatePetFixedResponse {}

// sort pet fixed request
message SortPetFixedRequest {
  // pet fixed id list, should contain all pet fixed ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];

  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// sort pet fixed response
message SortPetFixedResponse {}

// delete pet fixed request
message DeletePetFixedRequest {
  // pet fixed id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// delete pet fixed response
message DeletePetFixedResponse {}

// Service for pet fixed settings
service BusinessPetFixedService {
  // Get a pet fixed.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The pet fixed does not exist, or does not belong to the given company or business.
  rpc GetPetFixed(GetPetFixedRequest) returns (GetPetFixedResponse);

  // List pet fixed.
  // If the company does not exists, or does not define any pet fixed, an empty list will be returned rather than an error.
  rpc ListPetFixed(ListPetFixedRequest) returns (ListPetFixedResponse);

  // Create a pet fixed.
  // The name of the new pet fixed must be unique among all pet fixed of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The name is already used by another pet fixed of the company or business.
  rpc CreatePetFixed(CreatePetFixedRequest) returns (CreatePetFixedResponse);

  // Update a pet fixed.
  // If the name of the pet fixed is changed, it must be unique among all pet fixed of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The name is already used by another pet fixed of the company or business, or the pet fixed
  //                      does not exist, or has been deleted, or does not belong to the company or business.
  rpc UpdatePetFixed(UpdatePetFixedRequest) returns (UpdatePetFixedResponse);

  // Sort pet fixed of the company or business.
  // Pet fixed will be sorted according to the order of `ids`. If there are fixed of the company or business whose ids
  // are not included in `ids`, they will be sorted to the end. If an id in `ids` does not exist or does not belong to
  // the company or business, it will be ignored.
  rpc SortPetFixed(SortPetFixedRequest) returns (SortPetFixedResponse);

  // Delete a pet fixed.
  // If the pet fixed is already deleted, it will return success without throwing any error.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The pet fixed does not exist, or does not belong to the given company.
  rpc DeletePetFixed(DeletePetFixedRequest) returns (DeletePetFixedResponse);
}
