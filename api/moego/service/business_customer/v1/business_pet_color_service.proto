syntax = "proto3";

package moego.service.business_customer.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// business pet color service
service BusinessPetColorService {
  // list color
  rpc ListColor(ListColorRequest) returns (ListColorResponse);
  // update color
  // if not exist, will create a new color
  rpc UpdateColor(UpdateColorRequest) returns (UpdateColorResponse);

  // binding pet and color
  rpc BindingColor(BindingColorRequest) returns (BindingColorResponse);

  // list binding
  rpc ListColorBinding(ListColorBindingRequest) returns (ListColorBindingResponse);
}

// list color request
message ListColorRequest {
  // color id list
  repeated int64 color_ids = 1;
  // color name fuzzy search
  optional string color_name = 2;
  // company id
  int64 company_id = 3 [(validate.rules).int64.gt = 0];
}

// list color response
message ListColorResponse {
  // color message
  message Color {
    // id
    int64 id = 1;
    // name
    string name = 2;
    // company id
    int64 company_id = 3;
    // status
    int32 status = 4;
    // create time
    google.protobuf.Timestamp create_time = 5;
    // update time
    google.protobuf.Timestamp update_time = 6;
  }
  // color list
  repeated Color colors = 1;
}

// update color request
message UpdateColorRequest {
  // status
  enum Status {
    // unspecified
    STATUS_UNSPECIFIED = 0;
    // ..
    NORMAL = 1;
    // ..
    DELETED = 2;
  }
  // company id
  int64 company_id = 1;
  // color id, if is null, will create a new color
  optional int64 color_id = 2;
  // color name
  optional string color_name = 3;
  // status
  optional Status status = 4;
}

// update color response
message UpdateColorResponse {
  // id
  int64 id = 1;
}

// binding color
message BindingColorRequest {
  // status
  enum Status {
    // unspecified
    STATUS_UNSPECIFIED = 0;
    // ..
    NORMAL = 1;
    // ..
    DELETED = 2;
  }
  // color id
  int64 color_id = 1;
  // pet id
  int64 pet_id = 2;
  // status
  Status status = 3;
}

// response
message BindingColorResponse {
  // result
  bool result = 1;
}

// list binding request
message ListColorBindingRequest {
  // pet id
  optional int64 pet_id = 1;
  // color id
  optional int64 color_id = 2;
}

// list binding response
message ListColorBindingResponse {
  // binding message
  message ColorBinding {
    // id
    int64 id = 1;
    // pet id
    int64 pet_id = 2;
    // color id
    int64 color_id = 3;
    // status 1=normal 2=deleted
    int32 status = 4;
  }
  // binding list
  repeated ColorBinding bindings = 1;
}
