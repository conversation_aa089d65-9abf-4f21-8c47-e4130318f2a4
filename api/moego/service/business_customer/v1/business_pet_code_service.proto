syntax = "proto3";

package moego.service.business_customer.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/business_customer/v1/business_pet_code_defs.proto";
import "moego/models/business_customer/v1/business_pet_code_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get pet code request
message GetPetCodeRequest {
  // pet code id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// get pet code response
message GetPetCodeResponse {
  // pet code
  moego.models.business_customer.v1.BusinessPetCodeModel pet_code = 1;
}

// list pet code request
message ListPetCodeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
}

// list pet code response
message ListPetCodeResponse {
  // pet code list
  repeated moego.models.business_customer.v1.BusinessPetCodeModel pet_codes = 1;
}

// create pet code request
message CreatePetCodeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];

  // pet code
  moego.models.business_customer.v1.BusinessPetCodeCreateDef pet_code = 3 [(validate.rules).message.required = true];
}

// create pet code response
message CreatePetCodeResponse {
  // pet code
  moego.models.business_customer.v1.BusinessPetCodeModel pet_code = 1;
}

// update pet code request
message UpdatePetCodeRequest {
  // pet code id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];

  // pet code
  moego.models.business_customer.v1.BusinessPetCodeUpdateDef pet_code = 4 [(validate.rules).message.required = true];
}

// update pet code response
message UpdatePetCodeResponse {}

// sort pet code request
message SortPetCodeRequest {
  // pet code id list, should contain all pet code ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];

  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// sort pet code response
message SortPetCodeResponse {}

// delete pet code request
message DeletePetCodeRequest {
  // pet code id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// delete pet code response
message DeletePetCodeResponse {}

// list binding pet code request
message ListBindingPetCodeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // pet id
  int64 pet_id = 3 [(validate.rules).int64.gt = 0];
}

// list binding pet code response
message ListBindingPetCodeResponse {
  // pet code binding model
  message Binding {
    // pet id
    int64 pet_id = 1;
    // code id
    int64 code_id = 2;
    // comment
    string comment = 3;
    // binding time
    google.protobuf.Timestamp binding_time = 4;
  }
  // pet code list
  repeated moego.models.business_customer.v1.BusinessPetCodeModel pet_codes = 1;

  // pet code binding list
  repeated Binding bindings = 2;
}

// batch list binding pet code request
message BatchListBindingPetCodeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // pet id list
  repeated int64 pet_ids = 3 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch list binding pet code response
message BatchListBindingPetCodeResponse {
  // pet code bindings
  // deprecated
  // reason: pet code binding 已经不是简单的id 实体了, 多了其他字段
  // 为了避免breaking change, 这里用新的实体返回bindings, see line 167
  repeated moego.models.business_customer.v1.BusinessPetCodeBindingModel bindings = 1;

  // pet codes
  repeated moego.models.business_customer.v1.BusinessPetCodeModel pet_codes = 2;

  // pet code binding model
  message Binding {
    // pet id
    int64 pet_id = 1;
    // code id
    int64 code_id = 2;
    // comment
    string comment = 3;
    // binding time
    google.protobuf.Timestamp binding_time = 4;
  }
  // pet code binding list
  repeated Binding pet_code_bindings = 3;
}

// binding pet code request
message BindingPetCodeRequest {
  // status
  enum Status {
    // unspecified
    STATUS_UNSPECIFIED = 0;
    // binding
    BINDING = 1;
    // unbinding
    UNBINDING = 2;
  }
  // pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];
  // pet code id
  int64 pet_code_id = 2 [(validate.rules).int64.gt = 0];
  // comment
  string comment = 3;
  // status
  Status status = 4;
}

// binding result
message BindingPetCodeResponse {}

// Service for pet code settings
service BusinessPetCodeService {
  // Get a pet code.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The pet code does not exist, or does not belong to the given company or business.
  rpc GetPetCode(GetPetCodeRequest) returns (GetPetCodeResponse);

  // List pet codes.
  // If `pet_id` is set, only pet codes associated with the pet will be returned.
  // If the company does not exists, or does not define any pet codes, an empty list will be returned rather than an error.
  rpc ListPetCode(ListPetCodeRequest) returns (ListPetCodeResponse);

  // Create a pet code.
  // The abbreviation of the new pet code must be unique among all pet codes of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The abbreviation is already used by another pet code of the company or business.
  rpc CreatePetCode(CreatePetCodeRequest) returns (CreatePetCodeResponse);

  // Update a pet code.
  // If the abbreviation of the pet code is changed, it must be unique among all pet codes of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The abbreviation is already used by another pet code of the company or business, or the pet
  //                      code does not exist, or has been deleted, or does not belong to the company or business.
  rpc UpdatePetCode(UpdatePetCodeRequest) returns (UpdatePetCodeResponse);

  // Sort pet codes of the company or business.
  // Pet codes will be sorted according to the order of `ids`. If there are codes of the company or business whose ids
  // are not included in `ids`, they will be sorted to the end. If an id in `ids` does not exist or does not belong to
  // the company or business, it will be ignored.
  rpc SortPetCode(SortPetCodeRequest) returns (SortPetCodeResponse);

  // Delete a pet code.
  // If the pet code is already deleted, it will return success without throwing any error.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The pet code does not exist, or does not belong to the given company.
  rpc DeletePetCode(DeletePetCodeRequest) returns (DeletePetCodeResponse);

  // List pet codes binding to a pet.
  rpc ListBindingPetCode(ListBindingPetCodeRequest) returns (ListBindingPetCodeResponse);

  // List pet codes binding to several pets.
  rpc BatchListBindingPetCode(BatchListBindingPetCodeRequest) returns (BatchListBindingPetCodeResponse);

  // binding or unbinding pet code to pet
  // usage status control
  // if binding is repeated, it will be updated
  rpc BindingPetCode(BindingPetCodeRequest) returns (BindingPetCodeResponse);
}
