syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_type_defs.proto";
import "moego/models/business_customer/v1/business_pet_type_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get pet type request
message GetPetTypeRequest {
  // id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// get pet type response
message GetPetTypeResponse {
  // pet type
  moego.models.business_customer.v1.BusinessPetTypeModel type = 1;
}

// list pet type template request
message ListPetTypeTemplateRequest {}

// list pet type template response
message ListPetTypeTemplateResponse {
  // pet type template list
  repeated moego.models.business_customer.v1.BusinessPetTypeModel types = 1;
}

// list pet type request
message ListPetTypeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // include unavailable pet types, default is false (only include available pet types)
  bool include_unavailable = 3;
}

// list pet type response
message ListPetTypeResponse {
  // pet type list
  repeated moego.models.business_customer.v1.BusinessPetTypeModel types = 1;
}

// update pet type request
message UpdatePetTypeRequest {
  // id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];

  // pet type
  moego.models.business_customer.v1.BusinessPetTypeUpdateDef type = 4 [(validate.rules).message.required = true];
}

// update pet type response
message UpdatePetTypeResponse {}

// sort pet type request
message SortPetTypeRequest {
  // pet type id list, should contain all pet type ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];

  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// sort pet type response
message SortPetTypeResponse {}

// Service for pet type settings. Pet types are initialized when a company or business is created, and can never be
// deleted. Therefore, `CreatePetType` and `DeletePetType` are not provided in this
// service. But pet types can be marked as unavailable by `UpdatePetType` if the user does not want
// them to be shown in the pet type list.
service BusinessPetTypeService {
  // Get a pet type by id.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The pet type does not exist, or does not belong to the given company or business.
  rpc GetPetType(GetPetTypeRequest) returns (GetPetTypeResponse);

  // List pet type template
  rpc ListPetTypeTemplate(ListPetTypeTemplateRequest) returns (ListPetTypeTemplateResponse);

  // List pet types.
  // By default, only available pet types will be returned. If `include_unavailable` is set to true, unavailable pet
  // types will also be returned.
  // If the company does not exists, or does not define any pet types, an empty list will be returned rather than an error.
  rpc ListPetType(ListPetTypeRequest) returns (ListPetTypeResponse);

  // Update a pet type.
  // The name of the pet type is not allowed to be changed because it is defined by the platform.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The pet type does not exist, or does not belong to the company or business.
  rpc UpdatePetType(UpdatePetTypeRequest) returns (UpdatePetTypeResponse);

  // Sort pet types of the company or business.
  // Pet types will be sorted according to the order of `ids`. If there are pet types of the company or business whose
  // ids are not included in `ids`, they will be sorted to the end. If an id in `ids` does not exist or does not belong
  // to the company or business, it will be ignored.
  rpc SortPetType(SortPetTypeRequest) returns (SortPetTypeResponse);
}
