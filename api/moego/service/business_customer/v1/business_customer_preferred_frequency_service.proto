syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/organization/v1/tenant.proto";
import "moego/utils/v1/time_period.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get customer grooming frequency request
message GetCustomerGroomingFrequencyRequest {
  // company id, deprecated, use tenant instead
  int64 company_id = 1 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];

  // business id, deprecated, use tenant instead
  optional int64 business_id = 2 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];

  // tenant, required
  models.organization.v1.Tenant tenant = 3;
}

// get customer grooming frequency response
message GetCustomerGroomingFrequencyResponse {
  // grooming frequency
  moego.utils.v1.TimePeriod grooming_frequency = 1;
}

// upsert customer grooming frequency request
message UpsertCustomerGroomingFrequencyRequest {
  // company id, deprecated, use tenant instead
  int64 company_id = 1 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];

  // business id, deprecated, use tenant instead
  optional int64 business_id = 2 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];

  // tenant, required
  models.organization.v1.Tenant tenant = 5;

  // grooming frequency to upsert, currently only support unit of DAY, WEEK, MONTH
  moego.utils.v1.TimePeriod grooming_frequency = 3 [(validate.rules).message.required = true];

  // apply to all customers, default is false
  bool apply_to_all_customers = 4;
}

// upsert customer grooming frequency response
message UpsertCustomerGroomingFrequencyResponse {}

// Service for customer preferred frequency settings.
// Preferred frequency is currently applied to customers.
// In the future, it may be applied to pets, and the service will be moved to BusinessPetPreferredFrequencyService
service BusinessCustomerPreferredFrequencyService {
  // Get customer grooming frequency
  rpc GetCustomerGroomingFrequency(GetCustomerGroomingFrequencyRequest) returns (GetCustomerGroomingFrequencyResponse);

  // Upsert customer grooming frequency
  rpc UpsertCustomerGroomingFrequency(UpsertCustomerGroomingFrequencyRequest) returns (UpsertCustomerGroomingFrequencyResponse);

  // Maybe there is boarding/daycare frequency in the future?
}
