syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_breed_defs.proto";
import "moego/models/business_customer/v1/business_pet_breed_models.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get pet breed request
message GetPetBreedRequest {
  // pet breed id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// get pet breed response
message GetPetBreedResponse {
  // pet breed
  moego.models.business_customer.v1.BusinessPetBreedModel breed = 1;
}

// list pet breed request
message ListPetBreedRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // pet type id
  optional moego.models.customer.v1.PetType pet_type_id = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// list pet breed response
message ListPetBreedResponse {
  // pet breed list
  repeated moego.models.business_customer.v1.BusinessPetBreedModel breeds = 1;
}

// batch upsert pet breed request
message BatchUpsertPetBreedRequest {
  // pet type id
  moego.models.customer.v1.PetType pet_type_id = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];

  // pet breeds to create
  repeated moego.models.business_customer.v1.BusinessPetBreedUpsertDef breeds_to_create = 5;

  // pet breeds to update
  map<int64, moego.models.business_customer.v1.BusinessPetBreedUpsertDef> breeds_to_update = 6;
}

// batch upsert pet breed response
message BatchUpsertPetBreedResponse {
  // deleted breed names, 这个字段是为了兼容以前的逻辑，感觉不太合理，考虑重新设计一下
  repeated string deleted_breed_names = 1;
}

// create pet breed request
message CreatePetBreedRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];

  // pet type id
  moego.models.customer.v1.PetType pet_type_id = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // pet breed
  moego.models.business_customer.v1.BusinessPetBreedCreateDef breed = 4 [(validate.rules).message.required = true];
}

// create pet breed response
message CreatePetBreedResponse {
  // pet breed
  moego.models.business_customer.v1.BusinessPetBreedModel breed = 1;
}

// update pet breed request
message UpdatePetBreedRequest {
  // pet breed id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];

  // pet breed
  moego.models.business_customer.v1.BusinessPetBreedUpdateDef breed = 4;
}

// update pet breed response
message UpdatePetBreedResponse {}

// delete pet breed request
message DeletePetBreedRequest {
  // pet breed id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// delete pet breed response
message DeletePetBreedResponse {}

// Service for pet breed settings
service BusinessPetBreedService {
  // Get a pet breed.
  //
  // Error codes:
  // - CODE_NOT_FOUND: The pet breed does not exist, or does not belong to the company or business.
  rpc GetPetBreed(GetPetBreedRequest) returns (GetPetBreedResponse);

  // List pet breeds.
  // Pet breeds of the given pet type defined by the company will be returned, or all pet breeds defined by the company
  // will be returned if `type` is not set.
  // If the company does not exists, or does not define any pet breeds, an empty list will be returned rather than an error.
  rpc ListPetBreed(ListPetBreedRequest) returns (ListPetBreedResponse);

  // Batch upsert pet breeds of a pet type and set the pet type to available.
  // If a pet breed of the pet type is not in the request (create or update) but exists in the database, it will be deleted.
  // Each pet breed name in the request should be unique among the pet type.
  // Please notice that this method upsert pet breeds of the company and don't care about the business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The breed name is duplicated, or the pet type does not belong to the company.
  rpc BatchUpsertPetBreed(BatchUpsertPetBreedRequest) returns (BatchUpsertPetBreedResponse);

  // Create a pet breed.
  // The name of the new pet breed must be unique among all pet breeds of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The name is already used by another pet breed of the company or business.
  rpc CreatePetBreed(CreatePetBreedRequest) returns (CreatePetBreedResponse);

  // Update a pet breed.
  // If the name of the pet breed is changed, it must be unique among all pet breed of the company or business.
  //
  // Error codes:
  // - CODE_NOT_FOUND: The name is already used by another pet breed of the company or business, or the pet breed does
  //                   not exist, or has been deleted, or does not belong to the company or business.
  rpc UpdatePetBreed(UpdatePetBreedRequest) returns (UpdatePetBreedResponse);

  // Delete a pet breed.
  // If the pet breed is already deleted, it will return success without throwing any error.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The pet breed does not exist, or does not belong to the given company.
  rpc DeletePetBreed(DeletePetBreedRequest) returns (DeletePetBreedResponse);
}
