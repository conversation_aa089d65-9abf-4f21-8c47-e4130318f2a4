syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_feeding_models.proto";
import "moego/models/business_customer/v1/business_pet_feeding_schedule_defs.proto";
import "moego/models/business_customer/v1/business_pet_schedule_setting_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// Create feeding schedule request
message CreateFeedingScheduleRequest {
  // pet feeding schedule
  models.business_customer.v1.BusinessPetFeedingScheduleDef feeding_schedule = 1 [(validate.rules).message = {required: true}];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
}

// Create feeding schedule response
message CreateFeedingScheduleResponse {
  // feeding id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// batch crate feeding schedule request
message BatchCreateFeedingScheduleRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // medication schedule
  repeated models.business_customer.v1.BusinessPetFeedingScheduleDef feeding_schedules = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// batch create feeding schedule response
message BatchCreateFeedingScheduleResponse {}

// Update feeding schedule request
message UpdateFeedingScheduleRequest {
  // feeding id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // pet feeding schedule
  models.business_customer.v1.BusinessPetFeedingScheduleDef feeding_schedule = 2 [(validate.rules).message = {required: true}];
  // company id
  int64 company_id = 3 [(validate.rules).int64.gt = 0];
}

// Update feeding schedule response
message UpdateFeedingScheduleResponse {}

// Delete feeding schedule request
message DeleteFeedingScheduleRequest {
  // feeding id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
}

// Delete feeding schedule response
message DeleteFeedingScheduleResponse {}

// List pet's feeding schedule request
message ListPetFeedingScheduleRequest {
  // pet id. Will be added to pet_ids if larger than zero, otherwise, it will be ignored
  int64 pet_id = 1 [(validate.rules).int64.gte = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // pet id list
  repeated int64 pet_ids = 3 [(validate.rules).repeated = {
    max_items: 100
    items: {
      int64: {gt: 0}
    }
  }];
}

// List pet's feeding schedule response
message ListPetFeedingScheduleResponse {
  // pet's feeding contents
  repeated models.business_customer.v1.BusinessPetFeedingModel feedings = 1;
  // pet's feeding schedules
  repeated models.business_customer.v1.BusinessPetScheduleSettingModel schedules = 2;
}

// Business pet feeding schedule service
service BusinessPetFeedingScheduleService {
  // Create feeding schedule
  // Feeding display rules: {Feeding schedule} {Amount} {Feeding unit} {Feeding type} {Feeding source} {Feeding instruction}
  rpc CreateFeedingSchedule(CreateFeedingScheduleRequest) returns (CreateFeedingScheduleResponse);

  // Batch create feeding schedule
  rpc BatchCreateFeedingSchedule(BatchCreateFeedingScheduleRequest) returns (BatchCreateFeedingScheduleResponse);

  // Update feeding schedule
  rpc UpdateFeedingSchedule(UpdateFeedingScheduleRequest) returns (UpdateFeedingScheduleResponse);

  // Delete feeding schedule
  rpc DeleteFeedingSchedule(DeleteFeedingScheduleRequest) returns (DeleteFeedingScheduleResponse);

  // List pet's feeding schedule
  rpc ListPetFeedingSchedule(ListPetFeedingScheduleRequest) returns (ListPetFeedingScheduleResponse);
}
