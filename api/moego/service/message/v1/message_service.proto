syntax = "proto3";

package moego.service.message.v1;

import "moego/models/message/v1/message_enums.proto";
import "moego/models/message/v1/message_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/message/v1;messagesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.message.v1";

// send sms msg request
message SendSmsRequest {
  // business id
  int64 business_id = 1;
  // customer id
  int64 customer_id = 2;
  // staff id
  int64 staff_id = 3;
  // target id
  int64 target_id = 4;
  // target type
  moego.models.message.v1.TargetType target_type = 5;
  // sender type
  moego.models.message.v1.SenderType sender_type = 6;
  // sms sender
  moego.models.message.v1.PhoneUser sender = 7;
  // sms receiver
  moego.models.message.v1.PhoneUser receiver = 8;
  // sms msg text
  optional string msg_text = 9;
  // medias
  repeated string msg_medias = 10;
  // sms callback url
  optional string callback = 11;
}

// send email msg request
message SendEmailRequest {
  // business id
  int64 business_id = 1;
  // customer id
  int64 customer_id = 2;
  // staff id
  int64 staff_id = 3;
  // target id
  int64 target_id = 4;
  // target type
  moego.models.message.v1.TargetType target_type = 5;
  // sender type
  moego.models.message.v1.SenderType sender_type = 6;
  // sender
  moego.models.message.v1.EmailUser sender = 7;
  // receiver
  repeated moego.models.message.v1.EmailUser receiver = 8;
  // email title
  string email_title = 9;
  // email content
  string email_content = 10;
}

// message service
service MessageService {
  // send a sms message
  rpc SendSms(SendSmsRequest) returns (moego.models.message.v1.MessageModel);

  // send a email message
  rpc SendEmail(SendEmailRequest) returns (moego.models.message.v1.MessageModel);
}
