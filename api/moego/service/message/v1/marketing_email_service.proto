syntax = "proto3";

package moego.service.message.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/message/v1/marketing_email_defs.proto";
import "moego/models/message/v1/marketing_email_enums.proto";
import "moego/models/message/v1/marketing_email_models.proto";
import "moego/utils/v1/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/message/v1;messagesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.message.v1";

// mass email send request
message MassEmailSendRequest {
  // business id
  int64 business_id = 1 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // email detail
  models.message.v1.EmailDef email = 2 [(validate.rules).message.required = true];
  // draft id, optional
  optional int64 draft_id = 3;
  // recipient filter
  optional models.message.v1.RecipientFilterDef recipient_filter = 4;
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 5 [(validate.rules).int64.gt = 0];
  // staff id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 staff_id = 6 [(validate.rules).int64.gt = 0];
  // template id for tracking
  optional int64 template_id = 7;
}

// mass email send response
message MassEmailSendResponse {
  // message id
  int64 id = 1;
}

// get email list request
message GetEmailListRequest {
  // pagination request
  moego.utils.v1.PaginationRequest pagination = 1;
  // business id
  int64 business_id = 2 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // email status
  optional moego.models.message.v1.MarketingEmailStatus status = 3;
  // email subject
  optional string subject = 4 [(validate.rules).string.max_len = 200];
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 5 [(validate.rules).int64.gt = 0];
}

// GetEmailListResponse
message GetEmailListResponse {
  // pagination response
  moego.utils.v1.PaginationResponse pagination = 1;
  // email list
  repeated models.message.v1.MarketingEmailModelBriefView list = 2;
}

// GetEmailDetailRequest
message GetEmailDetailRequest {
  // email id
  int64 email_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 3 [(validate.rules).int64.gt = 0];
}

// for schedule email, send right now
message SendNowRequest {
  // email id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 3 [(validate.rules).int64.gt = 0];
  // staff id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 staff_id = 4 [(validate.rules).int64.gt = 0];
}

// save email draft request
message SaveEmailDraftRequest {
  // email content
  models.message.v1.EmailDef email = 1 [(validate.rules).message.required = true];
  // business id
  int64 business_id = 2 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // recipient filter
  optional models.message.v1.RecipientFilterDef recipient_filter = 3;
  // draft id, optional
  optional int64 draft_id = 4;
  // company id
  int64 company_id = 5 [(validate.rules).int64.gte = 0];
  // staff id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 staff_id = 6 [(validate.rules).int64.gt = 0];
}

// save email draft response
message SaveEmailDraftResponse {
  // email id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// send test email for preview
message SendTestEmailRequest {
  // email subject
  string subject = 1 [(validate.rules).string.max_len = 200];
  // email content
  string content = 2 [(validate.rules).string.max_len = 100000];
  // attachments
  repeated models.message.v1.AttachmentDef attachments = 3;
  // business id
  int64 business_id = 4 [(validate.rules).int64.gt = 0];
  // recipient email
  string recipient_email = 5 [(validate.rules).string.email = true];
  // staff id
  int64 staff_id = 6 [(validate.rules).int64.gt = 0];
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 7 [(validate.rules).int64.gt = 0];
}

// get count of available emails response
message GetAvailableEmailsCountRequest {
  // business id
  int64 business_id = 1 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 2 [(validate.rules).int64.gt = 0];
}

// get count of available emails response
message GetAvailableEmailsCountResponse {
  // available emails
  int64 available_emails = 1;
  // used emails
  int64 used_emails = 2;
}

// cancel schedule email
message CancelScheduleEmailRequest {
  // email id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 3 [(validate.rules).int64.gt = 0];
  // staff id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 staff_id = 4 [(validate.rules).int64.gt = 0];
}

// delete email
message DeleteEmailRequest {
  // email id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 3 [(validate.rules).int64.gt = 0];
  // staff id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 staff_id = 4 [(validate.rules).int64.gt = 0];
}

// reschedule email
message RescheduleEmailRequest {
  // email id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // new send time
  google.protobuf.Timestamp send_time = 2 [(validate.rules).timestamp.required = true];
  // business id
  int64 business_id = 3 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 4 [(validate.rules).int64.gt = 0];
  // staff id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 staff_id = 5 [(validate.rules).int64.gt = 0];
}

// get recipient list request
message GetRecipientListRequest {
  // email id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // pagination request
  moego.utils.v1.PaginationRequest pagination = 2 [(validate.rules).message.required = true];
  // recipient status filter
  optional models.message.v1.MarketingEmailRecipientStatus status = 3;
  // business id
  int64 business_id = 4 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 5 [(validate.rules).int64.gt = 0];
}

// get recipient list response
message GetRecipientListResponse {
  // pagination response
  moego.utils.v1.PaginationResponse pagination = 1;
  // recipient list
  repeated models.message.v1.MarketingEmailRecipientModel recipients = 2;
}

// create marketing email template request
message CreateMarketingEmailTemplateRequest {
  // template name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 128
  }];
  // template description
  string description = 2 [(validate.rules).string = {max_len: 200}];
  // template subject
  string subject = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 200
  }];
  // template content
  string content = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 100000
  }];
  // template cover
  string cover = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // default client filter of this template, JSON string
  string client_filter = 6;
  // template type
  models.message.v1.MarketingEmailTemplateModel.MarketingEmailTemplateType type = 7;
  // enterprise id
  optional int64 enterprise_id = 8 [(validate.rules).int64.gt = 0];
}

// create marketing email template response
message CreateMarketingEmailTemplateResponse {
  // created template
  models.message.v1.MarketingEmailTemplateModel template = 1;
}

// update marketing email template request
message UpdateMarketingEmailTemplateRequest {
  // template id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // template name
  optional string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 128
  }];
  // template description
  optional string description = 3 [(validate.rules).string = {max_len: 200}];
  // template subject
  optional string subject = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 200
  }];
  // template content
  optional string content = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 100000
  }];
  // template cover
  optional string cover = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // default client filter of this template, JSON string
  optional string client_filter = 7;
}

// update marketing email template response
message UpdateMarketingEmailTemplateResponse {
  // updated template
  models.message.v1.MarketingEmailTemplateModel template = 1;
}

// get marketing email template list
message GetMarketingEmailTemplateListRequest {
  // pagination request
  moego.utils.v1.PaginationRequest pagination = 1 [(validate.rules).message.required = true];
  // business id
  int64 business_id = 2 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 3 [(validate.rules).int64.gt = 0];
}

// get marketing email template list response
message GetMarketingEmailTemplateListResponse {
  // pagination response
  moego.utils.v1.PaginationResponse pagination = 1;
  // template list
  repeated models.message.v1.MarketingEmailTemplateModelBriefView templates = 2;
}

// get marketing email template detail request
message GetMarketingEmailTemplateDetailRequest {
  // template id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 3 [(validate.rules).int64.gt = 0];
}

// view email reply request
message ViewEmailReplyRequest {
  // recipient id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 3 [(validate.rules).int64.gt = 0];
}

// view email reply response
message ViewEmailReplyResponse {
  // email reply
  string content = 1;
}

// handle email event
message HandleEmailEventRequest {
  // mandrill email id
  string mandrill_id = 1 [(validate.rules).string = {max_len: 100}];
  // event type
  models.message.v1.EmailEventType event_type = 2 [(validate.rules).enum.defined_only = true];
  // reply content, only for reply event
  optional string reply_content = 3 [(validate.rules).string = {max_len: 1000000}];
}

// calculate credit cost request
message CalculateCreditCostRequest {
  // number of recipients
  int64 recipient_count = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 3 [(validate.rules).int64.gt = 0];
}

// calculate credit cost response
message CalculateCreditCostResponse {
  // credit cost
  int64 credit_cost = 1;
}

// get appointments after an email request
message GetAppointmentsAfterEmailRequest {
  // email id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // pagination request
  moego.utils.v1.PaginationRequest pagination = 2 [(validate.rules).message.required = true];
  // business id
  int64 business_id = 3 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 4 [(validate.rules).int64.gt = 0];
}

// get appointments after an email response
message GetAppointmentsAfterEmailResponse {
  // pagination response
  moego.utils.v1.PaginationResponse pagination = 1;
  // appointment list
  repeated models.message.v1.MarketingEmailApptBriefView appointments = 2;
}

// remove email from rejected list request
message RemoveEmailFromRejectedListRequest {
  // customer id
  int64 customer_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 3 [(validate.rules).int64.gt = 0];
}

// get marketing campaigns summary request
message MarketingCampaignsSummaryRequest {
  // business id
  int64 business_id = 1 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // time sent at from
  int64 sent_at_from = 2;
  // time sent at to
  int64 sent_at_to = 3;
  // company id， 必填，但是为了兼容老版本，声明为 optional
  optional int64 company_id = 4 [(validate.rules).int64.gt = 0];
}

// get marketing campaigns summary response
message MarketingCampaignsSummaryResponse {
  // email sent cnt
  int32 email_sent_cnt = 1;
  // email opened cnt
  int32 email_opened_cnt = 2;
  // contribute booking cnt
  int32 contribute_booking_cnt = 3;
}

// marketing email service
service MarketingEmailService {
  // mass email send
  rpc MassEmailSend(MassEmailSendRequest) returns (MassEmailSendResponse);

  // get email list
  rpc GetEmailList(GetEmailListRequest) returns (GetEmailListResponse);

  // get email detail
  rpc GetEmailDetail(GetEmailDetailRequest) returns (models.message.v1.MarketingEmailModel);

  // send now for schedule email
  rpc SendNow(SendNowRequest) returns (google.protobuf.Empty);

  // send test email for preview
  rpc SendTestEmail(SendTestEmailRequest) returns (google.protobuf.Empty);

  // get available emails count
  rpc GetAvailableEmailsCount(GetAvailableEmailsCountRequest) returns (GetAvailableEmailsCountResponse);

  // cancel schedule email
  rpc CancelScheduleEmail(CancelScheduleEmailRequest) returns (google.protobuf.Empty);

  // save email draft
  rpc SaveEmailDraft(SaveEmailDraftRequest) returns (SaveEmailDraftResponse);

  // reschedule email
  rpc RescheduleEmail(RescheduleEmailRequest) returns (google.protobuf.Empty);

  // get recipient list
  rpc GetRecipientList(GetRecipientListRequest) returns (GetRecipientListResponse);

  // create marketing email template
  rpc CreateMarketingEmailTemplate(CreateMarketingEmailTemplateRequest) returns (CreateMarketingEmailTemplateResponse);

  // update marketing email template
  rpc UpdateMarketingEmailTemplate(UpdateMarketingEmailTemplateRequest) returns (UpdateMarketingEmailTemplateResponse);

  // get marketing email template list
  rpc GetMarketingEmailTemplateList(GetMarketingEmailTemplateListRequest) returns (GetMarketingEmailTemplateListResponse);

  // get marketing email template detail
  rpc GetMarketingEmailTemplateDetail(GetMarketingEmailTemplateDetailRequest) returns (models.message.v1.MarketingEmailTemplateModel);

  // view email reply
  rpc ViewEmailReply(ViewEmailReplyRequest) returns (ViewEmailReplyResponse);

  // delete email
  rpc DeleteEmail(DeleteEmailRequest) returns (google.protobuf.Empty);

  // trigger schedule email
  rpc TriggerScheduleEmail(google.protobuf.Empty) returns (google.protobuf.Empty);

  // handle email event
  rpc HandleEmailEvent(HandleEmailEventRequest) returns (google.protobuf.Empty);

  // calculate credit cost
  rpc CalculateCreditCost(CalculateCreditCostRequest) returns (CalculateCreditCostResponse);

  // get appointments after an email
  rpc GetAppointmentsAfterEmail(GetAppointmentsAfterEmailRequest) returns (GetAppointmentsAfterEmailResponse);

  // trigger email resend for pending emails
  rpc TriggerEmailResend(google.protobuf.Empty) returns (google.protobuf.Empty);

  // remove email from rejected list
  rpc RemoveEmailFromRejectedList(RemoveEmailFromRejectedListRequest) returns (google.protobuf.Empty);

  // get marketing campaigns email annual summary
  rpc MarketingCampaignsSummary(MarketingCampaignsSummaryRequest) returns (MarketingCampaignsSummaryResponse);
}
