syntax = "proto3";

package moego.service.message.v2;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/message/v2;messagesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.message.v2";

// AddHocService 数据管理服务
service AddHocService {
  // 添加短信转发规则
  rpc AddMessageForwardRecord(AddMessageForwardRecordRequest) returns (AddMessageForwardRecordResponse);
}

// AddMessageForwardRecordRequest 请求
message AddMessageForwardRecordRequest {
  // company id
  int64 company_id = 1;
  // business id
  int64 business_id = 2;
  // E.164 style phone number
  string e164_phone_number = 3;
}

// AddMessageForwardRecordResponse 响应
message AddMessageForwardRecordResponse {}
