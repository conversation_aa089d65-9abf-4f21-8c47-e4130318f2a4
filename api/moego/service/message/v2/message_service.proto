syntax = "proto3";

package moego.service.message.v2;

import "moego/models/business_customer/v1/business_customer_merge_defs.proto";
import "moego/models/message/v2/message_enums.proto";
import "moego/models/message/v2/message_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/message/v2;messagesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.message.v2";

// 消息服务，同时支撑 Business，Customer 和 Platform 的消息服务
service MessageService {
  // 通过 ID 获取对话
  rpc GetChat(GetChatRequest) returns (GetChatResponse);
  // 获取对话列表
  rpc ListChat(ListChatRequest) returns (ListChatResponse);
  // 创建对话
  rpc CreateChat(CreateChatRequest) returns (CreateChatResponse);

  // 获取对话的消息
  rpc ListChatMessage(ListChatMessageRequest) returns (ListChatMessageResponse);
  // 发送消息
  rpc SendMessage(SendMessageRequest) returns (SendMessageResponse);
  // 获取全部的未读消息数量
  rpc GetAllChatUnreadMessageCount(GetAllChatUnreadMessageCountRequest) returns (GetAllChatUnreadMessageCountResponse);
  // 获取指定 company 和 business 内的未读消息数量
  rpc GetCustomerUnreadMessageCount(GetCustomerUnreadMessageCountRequest) returns (GetCustomerUnreadMessageCountResponse);
  // 批量获取消息
  rpc BatchGetMessage(BatchGetMessageRequest) returns (BatchGetMessageResponse);
  // 合并消息记录
  rpc MergeMessages(MergeMessagesRequest) returns (MergeMessagesResponse);
}

// 获取 Chat 的请求
message GetChatRequest {
  // Chat ID
  uint64 chat_id = 1 [(validate.rules).uint64 = {gt: 0}];
}

// 获取对话的响应
message GetChatResponse {
  // 对话
  models.message.v2.ChatModel chat = 1;
}

// 获取 Chat 列表的请求
// CompanyID, BusinessID, CustomerID 三者不能同时不传或为 0
message ListChatRequest {
  // Company ID
  optional uint64 company_id = 1;
  // Business ID
  optional uint64 business_id = 2;
  // Customer ID
  optional uint64 customer_id = 3;
  // 筛选条件
  optional Filter filter = 4;
  // 分页查询参数
  utils.v2.PaginationRequest pagination = 5;

  // Filter
  message Filter {
    // 是否星标
    optional bool is_starred = 1;
    // 按对话的状态过滤
    repeated models.message.v2.ChatStatus chat_statuses = 2 [(validate.rules).repeated = {
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [400] /* 不允许查询已删除的对话 CHAT_DELETED: 400 */
        }
      }
    }];
  }
}

// 获取 Chat 列表的响应
message ListChatResponse {
  // Chat 列表
  repeated models.message.v2.ChatModel chats = 1;
  // 分页查询响应
  utils.v2.PaginationResponse pagination = 2;
}

// 创建对话的请求
message CreateChatRequest {
  // Company ID
  uint64 company_id = 1 [(validate.rules).uint64 = {gt: 0}];
  // Business ID
  uint64 business_id = 2 [(validate.rules).uint64 = {gt: 0}];
  // Customer ID
  uint64 customer_id = 3 [(validate.rules).uint64 = {gt: 0}];

  // 会话已存在时如何处理
  ActionOnDuplicated action_on_duplicated = 4;

  // 会话已存在时的处理方式
  enum ActionOnDuplicated {
    // 未指定行为，效果等同于 RETURN_ERROR
    ACTION_UNSPECIFIED = 0;
    // 返回报错
    ACTION_RETURN_ERROR = 1;
    // 返回已存在的 Chat
    ACTION_RETURN_EXIST_CHAT = 2;
  }
}

// 创建会话的响应
message CreateChatResponse {
  // 会话
  models.message.v2.ChatModel chat = 1;
  // 是否为新创建的 chat
  // 当会话已存在，且 ActionOnDuplicated 设置为 ACTION_RETURN_EXIST_CHAT 时该字段为 true
  bool is_new_chat = 2;
}

// 获取对话的消息的请求
message ListChatMessageRequest {
  // 会话 ID
  uint64 chat_id = 1;
  // 查询消息列表的锚点，当 next = 0 时会无视 mode，固定获取最新的 page_size 条消息
  uint64 next = 2;
  // 本次获取消息的最大数量
  uint32 page_size = 3 [(validate.rules).uint32 = {
    gt: 0
    lte: 1000
  }];
  // 查询模式
  // 仅当 next > 0 时有效
  Mode mode = 4 [(validate.rules).enum = {defined_only: true}];

  // 查询模式
  enum Mode {
    // 未指定模式，等效于 MODE_NEW_MESSAGE
    MODE_UNSPECIFIED = 0;
    // 获取新消息
    MODE_NEW_MESSAGE = 1;
    // 获取历史消息
    MODE_HISTORICAL_MESSAGE = 2;
  }
}

// 获取对话消息的响应
message ListChatMessageResponse {
  // 获取到的消息列表
  repeated models.message.v2.MessageModel messages = 1;
}

// 发送消息的请求
message SendMessageRequest {
  // UUID v7
  // 用于处理幂等逻辑，不设置时会自动生成
  optional string uuid = 1 [(validate.rules).string = {
    len: 36
    ignore_empty: true
  }];
  // 目标对话的 ID
  uint64 chat_id = 2 [(validate.rules).uint64 = {gt: 0}];

  // 消息发送方的角色
  models.message.v2.Role sender_role = 11 [(validate.rules).enum = {defined_only: true}];
  // 消息发送方的 ID，如果是角色是平台，该字段 **必须** 为 0
  uint64 sender_id = 12 [(validate.rules).uint64 = {gte: 0}];
  // 消息接收方的角色，不能与发送方角色相同
  models.message.v2.Role receiver_role = 13 [(validate.rules).enum = {
    defined_only: true
    in: [
      1, // Business
      2 // Customer
    ]
  }];
  // 消息接收方的 ID
  uint64 receiver_id = 14 [(validate.rules).uint64 = {gt: 0}];

  // 消息投送的渠道
  models.message.v2.Channel channel = 21 [(validate.rules).enum = {defined_only: true}];
  // 消息内容，为了兼容图片等会把链接放在这里
  string content = 22 [(validate.rules).string = {max_len: 1024}];

  // 消息内容的版本号
  uint32 version = 31 [(validate.rules).uint32 = {gte: 0}];
  // 消息内容的类型
  models.message.v2.ContentType content_type = 32 [(validate.rules).enum = {defined_only: true}];
  // 元信息
  models.message.v2.MessageModel.Metadata metadata = 42;
}

// 发送消息的响应
message SendMessageResponse {
  // 发送的消息
  models.message.v2.MessageModel message = 1;
}

// 获取全部的未读消息数量的请求
message GetAllChatUnreadMessageCountRequest {
  // 查询的角色
  models.message.v2.Role receiver_role = 1 [(validate.rules).enum = {
    in: [2] /* 目前只允许 Customer 使用 */
  }];
  // 查询的角色 ID
  uint64 receiver_id = 2 [(validate.rules).uint64 = {gt: 0}];
}

// 获取全部的未读消息数量的响应
message GetAllChatUnreadMessageCountResponse {
  // 未读消息数
  uint64 unread_message_count = 1;
}

// 获取 customer 在指定 company 和 business 内的未读消息数量的请求
message GetCustomerUnreadMessageCountRequest {
  // Company ID
  uint64 company_id = 1 [(validate.rules).uint64 = {gt: 0}];
  // Business IDs, empty for all business
  repeated uint64 business_ids = 2 [(validate.rules).repeated = {
    items: {
      uint64: {gt: 0}
    }
  }];
  // Customer ID
  uint64 customer_id = 3 [(validate.rules).uint64 = {gt: 0}];
}

//  获取customer 在指定 company 和 business 内的未读消息数量的响应
message GetCustomerUnreadMessageCountResponse {
  // 未读消息数
  uint64 unread_message_count = 1;
}

// 批量获取消息的请求
message BatchGetMessageRequest {
  // Customer ID, 与 Business ID 至少填 1 个
  optional uint64 customer_id = 1 [(validate.rules).uint64 = {gte: 0}];
  // Business ID，与 Customer ID 至少填 1 个
  optional uint64 business_id = 2 [(validate.rules).uint64 = {gte: 0}];

  // 消息 ID 列表
  repeated uint64 message_ids = 11 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
}

// 批量获取消息的响应
message BatchGetMessageResponse {
  // 消息列表
  repeated models.message.v2.MessageModel messages = 1;
}

// 合并消息记录的请求
message MergeMessagesRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // merge relation
  models.business_customer.v1.BusinessCustomerMergeRelationDef merge_relation = 2;
}

// 合并消息记录的响应
message MergeMessagesResponse {
  // success
  bool success = 1;
}
