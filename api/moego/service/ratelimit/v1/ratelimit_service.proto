syntax = "proto3";

package moego.service.ratelimit.v1;

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/ratelimit/v1/rules.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/ratelimit/v1;ratelimitsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.ratelimit.v1";

// 限流服务
service RateLimitService {
  // 校验请求是否允许通过
  rpc Allow(AllowRequest) returns (AllowResponse);
  // 给指定 Domain 注册限流规则
  rpc RegisterRules(RegisterRulesRequest) returns (RegisterRulesResponse);
}

// 限流请求
message AllowRequest {
  // 限流规则空间的唯一标识
  string domain = 1;
  // 限流资源，用于匹配限流规则，形式为 <Label, Value>，Label 是限流维度标签，Value 是具体值
  map<string, string> resources = 2;
  // 请求发生时的时间戳（毫秒级）
  google.protobuf.Timestamp timestamp = 3;
  // 一条限流请求可以代表多个相同的资源请求来命中限流规则
  int64 hits = 4 [(validate.rules).int64 = {gte: 1}];
}

// 限流响应
message AllowResponse {
  // 是否允许通过
  bool allow = 1;
  // 触发限流后的重试时间
  google.protobuf.Duration retry_after = 2;
}

// 注册限流规则请求
message RegisterRulesRequest {
  // 限流规则空间的唯一标识
  string domain = 1;
  // 每个 Domain 里都可以有多条限流规则
  repeated moego.models.ratelimit.v1.Rule rules = 2;
}

// 注册限流规则响应
message RegisterRulesResponse {
  // 限流规则注册成功确认
  bool succeed = 1;
}
