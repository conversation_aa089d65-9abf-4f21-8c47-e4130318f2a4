syntax = "proto3";

package moego.service.reporting.v2;

import "google/type/money.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/reporting/v2;reportingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.reporting.v2";

// External API, 专门写不是reporting主业的服务
service ExternalService {
  // list customer unpaid amount
  rpc ListCustomerUnpaidAmount(ListCustomerUnpaidAmountRequest) returns (ListCustomerUnpaidAmountResponse);
}

// list customer unpaid amount request
message ListCustomerUnpaidAmountRequest {
  // company id
  int64 company_id = 1;
  // customer id list
  repeated int64 customer_ids = 2;
}

// list customer unpaid amount response
message ListCustomerUnpaidAmountResponse {
  // customer unpaid amount
  message CustomerUnpaidAmount {
    // customer id
    int64 customer_id = 1;
    // unpaid amount
    google.type.Money amount = 2;
  }
  // customer unpaid amount list
  repeated CustomerUnpaidAmount customer_unpaid_amounts = 1;
}
