syntax = "proto3";

package moego.service.reporting.v2;

import "moego/models/reporting/v2/attribute_def.proto";
import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/report_meta_def.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/reporting/v2;reportingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.reporting.v2";

// Attribute Internal API
service AttributeService {
  // Get dimensions
  rpc GetDimensions(GetDimensionsRequest) returns (GetDimensionsResponse);
  // Get metrics categories
  rpc GetMetricsCategories(GetMetricsCategoriesRequest) returns (GetMetricsCategoriesResponse);
}

// Get dimensions request
message GetDimensionsRequest {
  // diagram id to get dimensions, if not present, return all dimensions
  optional string diagram_id = 1;
  // reporting scene
  models.reporting.v2.ReportingScene scene = 2;
}

// Get dimensions response
message GetDimensionsResponse {
  // dimensions
  repeated moego.models.reporting.v2.DimensionField dimensions = 1;
}

// Get metrics categories request
message GetMetricsCategoriesRequest {
  // diagram id to get metrics, if not present, return all metrics
  optional string diagram_id = 1;
  // reporting scene
  models.reporting.v2.ReportingScene scene = 2;
}

// Get metrics categories response
message GetMetricsCategoriesResponse {
  // metrics categories
  repeated moego.models.reporting.v2.MetricsCategoryDef categories = 1;
}
