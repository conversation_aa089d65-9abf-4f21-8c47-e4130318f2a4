syntax = "proto3";

package moego.service.reporting.v1;

import "moego/models/reporting/v1/metrics_models.proto";
import "moego/models/reporting/v1/reporting_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/reporting/v1;reportingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.reporting.v1";

// the metrics service
service MetricsService {
  // search business metrics
  rpc SearchBusinessMetrics(SearchBusinessMetricsRequest) returns (SearchBusinessMetricsResponse);
  // search campaign metrics
  rpc SearchCampaignMetrics(SearchCampaignMetricsRequest) returns (SearchCampaignMetricsResponse);
  // search campaign reports
  rpc SearchCampaignReports(SearchCampaignReportsRequest) returns (SearchCampaignReportsResponse);
}

// search business request
message SearchBusinessMetricsRequest {
  // predicate
  moego.utils.v2.Predicate predicate = 1;
  // order by (support multi fields), optional
  repeated moego.utils.v2.OrderBy order_bys = 2;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3 [(validate.rules).message.required = true];
}

// search business response
message SearchBusinessMetricsResponse {
  // business metrics
  repeated models.reporting.v1.BusinessMetricsModel metrics = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// search campaign request
message SearchCampaignMetricsRequest {
  // group by
  GroupBy group_by = 1;
  // predicate
  moego.utils.v2.Predicate predicate = 2;
  // order by (support multi fields), optional
  repeated moego.utils.v2.OrderBy order_bys = 3;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 4 [(validate.rules).message.required = true];
}

// Group By
enum GroupBy {
  // Unspecified
  GROUP_BY_UNSPECIFIED = 0;
  // Campaign
  CAMPAIGN = 1;
  // Staff
  STAFF = 2;
}

// search campaign response
message SearchCampaignMetricsResponse {
  // metrics
  repeated CampaignMetrics metrics = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// CampaignMetrics
message CampaignMetrics {
  // metrics
  oneof metrics {
    // campaign metrics group by campaign id
    moego.models.reporting.v1.CampaignMetricsModel campaign_metrics = 1;
    // campaign metrics group by staff id
    moego.models.reporting.v1.StaffCampaignMetricsModel staff_campaign_metrics = 2;
  }
}

// SearchCampaignReportsRequest
message SearchCampaignReportsRequest {
  // predicate
  moego.utils.v2.Predicate predicate = 1;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2 [(validate.rules).message.required = true];
}

// SearchCampaignReportsResponse
message SearchCampaignReportsResponse {
  // reports
  repeated models.reporting.v1.CampaignReportModel reports = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}
