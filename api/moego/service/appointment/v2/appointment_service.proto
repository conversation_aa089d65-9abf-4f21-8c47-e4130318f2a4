// @since 2025/6/13
// <AUTHOR>

syntax = "proto3";

package moego.service.appointment.v2;

import "moego/service/appointment/v1/pet_detail_service.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v2;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v2";

// Appointment service
service AppointmentService {
  // Update appointment
  rpc UpdateAppointment(UpdateAppointmentRequest) returns (UpdateAppointmentResponse);
}

// UpdateAppointment request
message UpdateAppointmentRequest {
  // company id, optional
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id, optional
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // Appointment id
  int64 appointment_id = 3 [(validate.rules).int64 = {gt: 0}];

  // appointment to update
  optional Appointment appointment = 4;

  // pet details to update
  repeated PetDetail pet_details = 5;

  // Appointment
  message Appointment {
    //
  }

  // Pet detail
  message PetDetail {
    // action
    oneof action {
      // add pet detail
      Add add = 1;
      // update pet detail
      Update update = 2;
      // delete pet detail
      Delete delete = 3;
    }

    // Add pet detail
    message Add {
      // pet detail
      moego.service.appointment.v1.CreatePetDetailRequest pet_detail = 1 [(validate.rules).message.required = true];
    }

    // Update pet detail
    message Update {
      // pet detail
      moego.service.appointment.v1.UpdatePetDetailRequest pet_detail = 1 [(validate.rules).message.required = true];
    }

    // Delete pet detail
    message Delete {
      // pet detail id
      int64 pet_detail_id = 1 [(validate.rules).int64.gt = 0];
    }
  }
}

// UpdateAppointment response
message UpdateAppointmentResponse {}
