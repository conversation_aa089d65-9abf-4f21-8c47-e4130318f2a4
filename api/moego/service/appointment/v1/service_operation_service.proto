// @since 2024-02-13 11:14:12
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.appointment.v1;

import "moego/models/appointment/v1/service_operation_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// create service_operation request
message GetServiceOperationListRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the appointment id
  repeated int64 appointment_ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
}

// get service_operation request
message GetServiceOperationListResponse {
  // operation
  repeated models.appointment.v1.ServiceOperationModel service_operations = 1;
}

// the service_operation service
service ServiceOperationService {
  // get service_operation
  rpc GetServiceOperationList(GetServiceOperationListRequest) returns (GetServiceOperationListResponse);
}
