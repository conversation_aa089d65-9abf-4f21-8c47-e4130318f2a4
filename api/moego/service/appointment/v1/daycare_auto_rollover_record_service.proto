syntax = "proto3";

package moego.service.appointment.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/appointment/v1/daycare_auto_rollover_record_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// CreateDaycareAutoRolloverRecord request
message CreateDaycareAutoRolloverRecordRequest {
  // 对应 moe_grooming.moe_grooming_pet_detail.id
  int64 daycare_service_detail_id = 1 [(validate.rules).int64 = {gt: 0}];
  // daycare service id
  // 如果没有指定，根据 daycare_service_detail_id 查询
  optional int64 service_id = 2 [(validate.rules).int64 = {gt: 0}];
  // 1: pending, 2: processing, 3: done
  // 如果没有指定，使用 pending
  optional moego.models.appointment.v1.DaycareAutoRolloverRecordModel.Status status = 7 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // rollover 触发时间
  // 如果没有指定，实时计算出 rollover_time
  optional google.protobuf.Timestamp rollover_time = 8;
}

// CreateDaycareAutoRolloverRecord response
message CreateDaycareAutoRolloverRecordResponse {
  // Inserted record
  moego.models.appointment.v1.DaycareAutoRolloverRecordModel record = 1;
}

// GetDaycareAutoRolloverRecord request
message GetDaycareAutoRolloverRecordRequest {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// Get DaycareAutoRolloverRecord response
message GetDaycareAutoRolloverRecordResponse {
  // Existing record
  moego.models.appointment.v1.DaycareAutoRolloverRecordModel record = 1;
}

// RolloverDaycareAutoRolloverRecord request
message RolloverDaycareAutoRolloverRecordRequest {
  // start time, inclusive
  google.protobuf.Timestamp start = 1 [(validate.rules).timestamp = {required: true}];
  // end time, exclusive
  google.protobuf.Timestamp end = 2 [(validate.rules).timestamp = {required: true}];
}

// RolloverDaycareAutoRolloverRecord response
message RolloverDaycareAutoRolloverRecordResponse {}

// BatchCreateDaycareAutoRolloverRecordByServiceId request
message BatchCreateDaycareAutoRolloverRecordByServiceIdRequest {
  // service id
  int64 service_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// BatchCreateDaycareAutoRolloverRecordByServiceId response
message BatchCreateDaycareAutoRolloverRecordByServiceIdResponse {
  // Inserted records count
  int32 affected_rows = 1;
}

// BatchDeleteDaycareAutoRolloverRecordByServiceId request
message BatchDeleteDaycareAutoRolloverRecordByServiceIdRequest {
  // service id
  int64 service_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// BatchDeleteDaycareAutoRolloverRecordByServiceId response
message BatchDeleteDaycareAutoRolloverRecordByServiceIdResponse {
  // Deleted records count
  int32 affected_rows = 1;
}

// RefreshDaycareAutoRolloverRecordByServiceId request
message RefreshDaycareAutoRolloverRecordByServiceIdRequest {
  // service id
  int64 service_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// RefreshDaycareAutoRolloverRecordByServiceId response
message RefreshDaycareAutoRolloverRecordByServiceIdResponse {}

// DaycareAutoRolloverRecord service
service DaycareAutoRolloverRecordService {
  // Create a record, return inserted record.
  rpc CreateDaycareAutoRolloverRecord(CreateDaycareAutoRolloverRecordRequest) returns (CreateDaycareAutoRolloverRecordResponse) {}

  // 给指定 service id 批量创建 rollover 记录
  rpc BatchCreateDaycareAutoRolloverRecordByServiceId(BatchCreateDaycareAutoRolloverRecordByServiceIdRequest) returns (BatchCreateDaycareAutoRolloverRecordByServiceIdResponse) {}

  // 删除指定 service id 的所有 rollover 记录
  rpc BatchDeleteDaycareAutoRolloverRecordByServiceId(BatchDeleteDaycareAutoRolloverRecordByServiceIdRequest) returns (BatchDeleteDaycareAutoRolloverRecordByServiceIdResponse) {}

  // 刷新指定 service id 的所有 rollover 记录的 rollover_time
  rpc RefreshDaycareAutoRolloverRecordByServiceId(RefreshDaycareAutoRolloverRecordByServiceIdRequest) returns (RefreshDaycareAutoRolloverRecordByServiceIdResponse) {}

  // Rollover DaycareAutoRolloverRecord.
  // Rollover 指定时间范围内的记录
  rpc RolloverDaycareAutoRolloverRecord(RolloverDaycareAutoRolloverRecordRequest) returns (RolloverDaycareAutoRolloverRecordResponse) {}
}
