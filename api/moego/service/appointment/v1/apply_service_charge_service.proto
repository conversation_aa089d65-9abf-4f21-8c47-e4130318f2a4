// @since 2025-05-21 18:13:31
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.appointment.v1;

import "moego/models/appointment/v1/apply_service_charge_model.proto";
import "moego/models/order/v1/service_charge_model.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// get auto apply service charge request
message GetAutoApplyServiceChargeRequest {
  // appointment id, at least one of appointment_id, online_booking_id, or pet_detail must be provided
  optional int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // online booking id, at least one of appointment_id, online_booking_id, or pet_detail must be provided
  optional int64 online_booking_id = 2 [(validate.rules).int64 = {gt: 0}];
  // calculate service charge param, at least one of appointment_id, online_booking_id, or pet_detail must be provided
  optional moego.models.appointment.v1.CalculateServiceChargeParam calculate_service_charge_param = 3;
  // company id
  int64 company_id = 4 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 5 [(validate.rules).int64 = {gt: 0}];
  // service charge filter
  optional ServiceChargeFilter service_charge_filter = 6;

  // filter
  message ServiceChargeFilter {
    // service charge ids
    repeated int64 service_charge_ids = 1 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];
    // auto apply status list
    repeated moego.models.order.v1.ServiceCharge.AutoApplyStatus auto_apply_status = 2 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];
  }
}

// get auto apply service charge response
message GetAutoApplyServiceChargeResponse {
  // auto apply service charge
  repeated moego.models.appointment.v1.ApplyServiceChargeModel apply_service_charge = 1;
}

// apply service charge service
service ApplyServiceChargeService {
  // get auto apply service charge
  rpc GetAutoApplyServiceCharge(GetAutoApplyServiceChargeRequest) returns (GetAutoApplyServiceChargeResponse);
}
