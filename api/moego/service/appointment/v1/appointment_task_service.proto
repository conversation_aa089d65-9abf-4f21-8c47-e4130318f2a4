syntax = "proto3";

package moego.service.appointment.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/interval.proto";
import "moego/models/appointment/v1/appointment_task_enums.proto";
import "moego/models/appointment/v1/appointment_task_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/organization/v1/tenant.proto";
import "moego/utils/v1/time_of_day_interval.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// The request message of ListAppointmentTasks
message ListAppointmentTasksRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // business id
  repeated int64 business_ids = 2 [(validate.rules).repeated = {
    min_items: 0
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // filter
  Filter filter = 3;

  // sort
  // Please use order_bys instead of order_by
  optional moego.utils.v2.OrderBy order_by = 4 [deprecated = true];

  // pagination
  moego.utils.v2.PaginationRequest pagination = 5;

  // order bys
  repeated moego.utils.v2.OrderBy order_bys = 6;

  // Appointment task list filter
  message Filter {
    // The filter of start_date field
    google.type.Interval start_date_interval = 1;

    // The filter of start_time field
    moego.utils.v1.TimeOfDayInterval start_time_interval = 2;

    // The flag of unassigned start_time
    optional bool unassigned_start_time = 3;

    // The time label of start_time
    optional string time_label = 4;

    // The filter of category field
    repeated models.appointment.v1.AppointmentTaskCategory categories = 5 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // The filter of care_type field
    repeated models.offering.v1.ServiceItemType care_types = 6 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // The filter of status field
    repeated models.appointment.v1.AppointmentTaskStatus statuses = 7 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // The filter of staff_id field
    repeated int64 staff_ids = 8 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];

    // The filter of service_id field
    // The service_id is the main service id
    repeated int64 service_ids = 9 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];

    // The filter of pet_id field
    repeated int64 pet_ids = 10 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];

    // The filter of appointment_id field
    repeated int64 appointment_ids = 11 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];

    // The filter of add_on_id field
    repeated int64 add_on_ids = 12 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];
  }
}

// The response message of ListAppointmentTasks
message ListAppointmentTasksResponse {
  // appointment tasks
  repeated models.appointment.v1.AppointmentTaskModel tasks = 1;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// The request message of CountAppointmentTasks
message CountAppointmentTasksRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];

  // filter
  Filter filter = 3;

  // Group by category. Empty means not group
  repeated models.appointment.v1.AppointmentTaskCategory group_categories = 4 [(validate.rules).repeated = {
    min_items: 0
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];

  // Group by schedule. Empty means not group
  repeated models.appointment.v1.AppointmentTaskSchedule group_schedules = 5 [(validate.rules).repeated = {
    min_items: 0
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];

  // Group by care type. Empty means not group
  repeated models.offering.v1.ServiceItemType group_care_types = 6 [(validate.rules).repeated = {
    min_items: 0
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];

  // Group by task status. Empty means not group
  repeated models.appointment.v1.AppointmentTaskStatus group_status = 7 [(validate.rules).repeated = {
    min_items: 0
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];

  // The message of filter
  message Filter {
    // The filter of start_date field
    google.type.Interval start_date_interval = 1;

    // The filter of care_type field
    repeated models.offering.v1.ServiceItemType care_types = 6 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // The filter of status field
    repeated models.appointment.v1.AppointmentTaskStatus statuses = 7 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // The filter of staff_id field
    repeated int64 staff_ids = 8 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];

    // The filter of pet_id field
    repeated int64 pet_ids = 10 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];
  }
}

// The response message of CountAppointmentTasks
message CountAppointmentTasksResponse {
  // Total count without grouping
  int32 total_count = 1;

  // TaskCategory-wise counts
  repeated CategoryCount category_counts = 2;

  // StartTime(Schedule)-wise counts
  repeated ScheduleCount schedule_counts = 3;

  // CareType-wise counts
  repeated CareTypeCount care_type_counts = 4;

  // Status-wise counts if by was true
  repeated StatusCount status_counts = 5;

  // The message of category count
  message CategoryCount {
    // category
    models.appointment.v1.AppointmentTaskCategory category = 1;
    // count
    int32 count = 2;
  }

  // The message of schedule count
  message ScheduleCount {
    // schedule
    models.appointment.v1.AppointmentTaskSchedule schedule = 1;
    // count
    int32 count = 2;
  }

  // The message of care type count
  message CareTypeCount {
    // care type
    models.offering.v1.ServiceItemType care_type = 1;
    // count
    int32 count = 2;
  }

  // The message of status count
  message StatusCount {
    // task status
    models.appointment.v1.AppointmentTaskStatus status = 1;
    // count
    int32 count = 2;
  }
}

// The request message of update appointment task
message PatchAppointmentTaskRequest {
  // The task id
  int64 task_id = 1 [(validate.rules).int64 = {gt: 0}];

  // The staff id
  optional int64 staff_id = 2 [(validate.rules).int64 = {gte: 0}];

  // The status
  optional models.appointment.v1.AppointmentTaskStatus status = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // The note status
  oneof note_status {
    // The note status of feeding task
    models.appointment.v1.FeedingTaskNoteStatus feeding_note_status = 10;

    // The note status of medication task
    models.appointment.v1.MedicationTaskNoteStatus medication_note_status = 11;

    // Customized note status
    string customized_note_status = 12;
  }

  // The note content
  optional string note_content = 5 [(validate.rules).string = {max_len: 500}];

  // The token staff id
  optional int64 token_staff_id = 6 [(validate.rules).int64 = {gt: 0}];
}

// The response message of update appointment task
message PatchAppointmentTaskResponse {
  // Updated result
  bool updated = 1;
}

// The request message of batch update appointment task
message BatchPatchAppointmentTaskRequest {
  // The task id
  repeated int64 task_ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // The staff id
  optional int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];

  // The status
  optional models.appointment.v1.AppointmentTaskStatus status = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // Tenant
  models.organization.v1.Tenant tenant = 4;

  // Filter
  Filter filter = 5;

  // Group filter list
  // The logical relationship between each group filter is OR
  repeated GroupFilter group_filters = 6;

  // Appointment task list filter
  message Filter {
    // The filter of start_date field
    google.type.Interval start_date_interval = 1;

    // Task statuses
    repeated models.appointment.v1.AppointmentTaskStatus statuses = 2 [(validate.rules).repeated = {
      min_items: 0
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // Care types
    repeated models.offering.v1.ServiceItemType care_types = 3 [(validate.rules).repeated = {
      min_items: 0
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // Staff ids
    repeated int64 staff_ids = 4 [(validate.rules).repeated = {
      min_items: 0
      items: {
        int64: {gt: 0}
      }
    }];

    // The pet id
    repeated int64 pet_ids = 5 [(validate.rules).repeated = {
      min_items: 0
      items: {
        int64: {gt: 0}
      }
    }];
  }

  // The tab group filter
  message GroupFilter {
    // The filter of start_time field
    optional int32 start_time = 1;

    // The flag of unassigned start_time
    optional bool unassigned_start_time = 2;

    // The time label of start_time
    optional string time_label = 3;

    // The filter of category field
    optional models.appointment.v1.AppointmentTaskCategory category = 4;

    // The filter of add_on_id field
    optional int64 add_on_id = 5;
  }
}

// The response message of batch update appointment task
message BatchPatchAppointmentTaskResponse {
  // Updated count
  int32 affected_row = 1;
}

// The request message of update appointment task
message UpdateAppointmentTaskRequest {
  // id
  int64 id = 1;
  // The category of task
  models.appointment.v1.AppointmentTaskCategory task_category = 2;
  // The instruction
  string instruction = 3;
  // The appointment id
  int64 appointment_id = 4;
  // The service id
  int64 service_id = 5;
  // The pet id
  int64 pet_id = 6;
  // The lodging id
  int64 lodging_id = 7;
  // The staff id
  int64 staff_id = 8;
  // The start date
  google.type.Date start_date = 9;
  // The start time, the minutes of the day
  optional int32 start_time = 10;
  // The start time label
  optional string time_label = 11;
  // The duration
  optional int32 duration = 12;
  // The status of task
  models.appointment.v1.AppointmentTaskStatus status = 13;
  // The note status
  optional string note_status = 14;
  // The note content
  optional string note_content = 15;
  // The created at
  google.protobuf.Timestamp created_at = 16;
  // The updated at
  google.protobuf.Timestamp updated_at = 17;
  // The deleted at
  optional google.protobuf.Timestamp deleted_at = 18;
  // The company id, don't update this field
  //  int64 company_id = 19;
  // The business id, don't update this field
  //  int64 business_id = 20;
  // The main service care type
  models.offering.v1.ServiceItemType care_type = 21;
}

// The response message of update appointment task
message UpdateAppointmentTaskResponse {
  // Updated result
  bool updated = 1;
}

// The request message of get appointment task
message GetAppointmentTaskRequest {
  // The task id
  int64 task_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The response message of get appointment task
message GetAppointmentTaskResponse {
  // The task
  models.appointment.v1.AppointmentTaskModel task = 1;
}

// The request message of list appointment task groups
message ListAppointmentTaskGroupsRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // The business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];

  // filter
  Filter filter = 3;

  // The message of filter
  message Filter {
    // The filter of start_date field
    google.type.Interval start_date_interval = 1;

    // The filter of care_type field
    repeated models.offering.v1.ServiceItemType care_types = 6 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // The filter of status field
    repeated models.appointment.v1.AppointmentTaskStatus statuses = 7 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // The filter of staff_id field
    repeated int64 staff_ids = 8 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];

    // The filter of pet_id field
    repeated int64 pet_ids = 10 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];
  }

  // The tab type
  oneof tab {
    // Category tab (Feeding/Medication/Add-on)
    models.appointment.v1.AppointmentTaskCategory category = 4;

    // Schedule tab (AM/PM/Unassigned)
    models.appointment.v1.AppointmentTaskSchedule schedule = 5;
  }
}

// The response message of list appointment task groups
message ListAppointmentTaskGroupsResponse {
  // The groups
  repeated Group groups = 1;

  // Group information
  message Group {
    // The group value
    oneof value {
      // For feeding and medication tabs: time slot info
      TimeGroup time = 1;

      // For Add-on and unassigned tab: service info
      AddOnGroup add_on = 2;

      // For AM and PM tabs: category info
      CategoryGroup category = 3;
    }
  }

  // Time group (for Feeding and Medication tabs)
  message TimeGroup {
    // The start time, the minutes of the day
    int32 start_time = 1;

    // The start time label
    string time_label = 2;

    // Count of tasks
    int32 count = 3;
  }

  // AddOn group (for Add-on tab and Unassigned tab)
  message AddOnGroup {
    // The add on id
    int64 add_on_id = 1;

    // Count of tasks
    int32 count = 3;
  }

  // Category group (for AM and PM tabs)
  message CategoryGroup {
    // The category type
    models.appointment.v1.AppointmentTaskCategory category = 1;

    // Count of tasks
    int32 count = 2;
  }
}

// The request message of list appointment task pets
message ListAppointmentTaskPetsRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];

  // filter
  Filter filter = 3;

  // The message of filter
  message Filter {
    // The filter of start_date field
    google.type.Interval start_date_interval = 1;
  }
}

// The response message of list appointment task pets
message ListAppointmentTaskPetsResponse {
  // The pet id
  repeated int64 pet_ids = 1;
}

// The request message of delete appointment task
message DeleteAppointmentTaskRequest {
  // The filter
  Filter filter = 1;

  // The filter
  message Filter {
    // The appointment ids
    repeated int64 appointment_ids = 1 [(validate.rules).repeated = {
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];
  }
}

// The response message of delete appointment task
message DeleteAppointmentTaskResponse {
  // Affected row
  int32 affected_row = 1;
}

// The request message of update task by appointment
message PatchTasksByAppointmentRequest {
  // The appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // The staff id
  optional int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];

  // The status
  optional models.appointment.v1.AppointmentTaskStatus status = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// The response message of update task by appointment
message PatchTasksByAppointmentResponse {
  // Updated count
  int32 affected_row = 1;
}

// The request message of sync appointment task
message SyncAppointmentTaskRequest {
  // The start appointment id
  int32 start_appointment_id = 2 [(validate.rules).int32 = {gt: 0}];
  // The end appointment id
  int32 end_appointment_id = 3 [(validate.rules).int32 = {gt: 0}];
  // Thread count
  optional int32 thread_count = 4 [(validate.rules).int32 = {gt: 0}];
  // sync start date
  // Only synchronize task data after that date
  optional string sync_start_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
}

// The response message of sync appointment task
message SyncAppointmentTaskResponse {}

// The appointment task service
service AppointmentTaskService {
  // List appointment tasks by filter
  rpc ListAppointmentTasks(ListAppointmentTasksRequest) returns (ListAppointmentTasksResponse);

  // Count appointment tasks by filter
  rpc CountAppointmentTasks(CountAppointmentTasksRequest) returns (CountAppointmentTasksResponse);

  // Update appointment task, optional field update
  // e.g. assign staff, change status, update note
  rpc PatchAppointmentTask(PatchAppointmentTaskRequest) returns (PatchAppointmentTaskResponse);

  // Batch update appointment task, optional field update
  // e.g. assign staff, change status
  rpc BatchPatchAppointmentTask(BatchPatchAppointmentTaskRequest) returns (BatchPatchAppointmentTaskResponse);

  // Update appointment task, full field update
  rpc UpdateAppointmentTask(UpdateAppointmentTaskRequest) returns (UpdateAppointmentTaskResponse);

  // Get appointment task
  rpc GetAppointmentTask(GetAppointmentTaskRequest) returns (GetAppointmentTaskResponse);

  // List appointment task groups
  rpc ListAppointmentTaskGroups(ListAppointmentTaskGroupsRequest) returns (ListAppointmentTaskGroupsResponse);

  // List appointment task pets by filter
  rpc ListAppointmentTaskPets(ListAppointmentTaskPetsRequest) returns (ListAppointmentTaskPetsResponse);

  // Delete appointment task
  rpc DeleteAppointmentTask(DeleteAppointmentTaskRequest) returns (DeleteAppointmentTaskResponse);

  // Update appointment task, optional field update
  // e.g. assign staff, change status
  rpc PatchTasksByAppointment(PatchTasksByAppointmentRequest) returns (PatchTasksByAppointmentResponse);

  // Sync appointment task
  rpc SyncAppointmentTask(SyncAppointmentTaskRequest) returns (SyncAppointmentTaskResponse);
}
