// @since 2025-05-01 15:22:53
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.appointment.v1;

import "moego/models/appointment/v1/service_charge_detail_models.proto";
import "moego/models/order/v1/service_charge_model.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// create service_charge_detail request
message AddServiceChargeDetailRequest {
  // Appointment ID
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Service charge ID, foreign key to service_charge table
  repeated int64 service_charge_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // The Login company ID
  int64 company_id = 3 [(validate.rules).int64 = {gt: 0}];

  // The login staff ID
  optional int64 staff_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// create service_charge_detail response
message AddServiceChargeDetailResponse {}

// delete service_charge_detail request
message DeleteServiceChargeDetailRequest {
  // the service_charge_detail id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];

  // The Login company ID
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];

  // The login staff ID
  optional int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// delete service_charge_detail response
message DeleteServiceChargeDetailResponse {}

// update upcoming appointment service charge details request
message UpdateUpcomingServiceChargeDetailsRequest {
  // Action type
  ActionType action_type = 1;

  // The service charge id, required for every action
  int64 service_charge_id = 2;

  // The old service charge, required for update action
  optional moego.models.order.v1.ServiceCharge old_service_charge = 3;

  // The login company ID
  int64 company_id = 4 [(validate.rules).int64 = {gt: 0}];

  // The login staff ID
  optional int64 updated_by = 5 [(validate.rules).int64 = {gt: 0}];

  // Action type
  enum ActionType {
    // Unspecified
    ACTION_TYPE_UNSPECIFIED = 0;
    // Add a new service charge
    ADD = 1;
    // Update an existing service charge
    UPDATE = 2;
    // Delete an existing service charge
    DELETE = 3;
  }
}

// update upcoming appointment service charge details response
message UpdateUpcomingServiceChargeDetailsResponse {}

// list service charge details request
message ListServiceChargeDetailsRequest {
  // Appointment ID
  repeated int64 appointment_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // The login company ID
  optional int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// list service charge details response
message ListServiceChargeDetailsResponse {
  // Service charge details
  repeated moego.models.appointment.v1.ServiceChargeDetailModel service_charge_details = 1;
}

// list hit late pick-up rules request
message ListHitLatePickUpRulesRequest {
  // The login company ID
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Appointment ID
  int64 appointment_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// list hit late pick-up rules response
message ListHitLatePickUpRulesResponse {
  // service charge
  repeated moego.models.order.v1.ServiceCharge service_charges = 1;
}

// add late pick-up fee request
message AddLatePickUpServiceChargeDetailRequest {
  // The login company ID
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Appointment ID
  int64 appointment_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// add late pick-up fee response
message AddLatePickUpServiceChargeDetailResponse {}

// the service_charge_detail service
service ServiceChargeDetailService {
  // add service_charge_detail
  rpc AddServiceChargeDetail(AddServiceChargeDetailRequest) returns (AddServiceChargeDetailResponse);

  // delete service_charge_detail
  rpc DeleteServiceChargeDetail(DeleteServiceChargeDetailRequest) returns (DeleteServiceChargeDetailResponse);

  // Update upcoming appointment service charge details
  rpc UpdateUpcomingServiceChargeDetails(UpdateUpcomingServiceChargeDetailsRequest) returns (UpdateUpcomingServiceChargeDetailsResponse);

  // List service charge details
  rpc ListServiceChargeDetails(ListServiceChargeDetailsRequest) returns (ListServiceChargeDetailsResponse);

  // List hit late pick-up rules
  // Use the current business time as the check out time to determine if there is a service charge hit.
  rpc ListHitLatePickUpRules(ListHitLatePickUpRulesRequest) returns (ListHitLatePickUpRulesResponse);

  // Add late pick-up fee
  // Add service charge fee to the appointment if there is a late pick up rule hit.
  rpc AddLatePickUpServiceChargeDetail(AddLatePickUpServiceChargeDetailRequest) returns (AddLatePickUpServiceChargeDetailResponse);
}
