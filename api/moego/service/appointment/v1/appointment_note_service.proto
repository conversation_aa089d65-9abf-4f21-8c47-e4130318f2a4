// @since 2024-02-13 11:13:08
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.appointment.v1;

import "moego/models/appointment/v1/appointment_note_defs.proto";
import "moego/models/appointment/v1/appointment_note_enums.proto";
import "moego/models/appointment/v1/appointment_note_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// get appointment_note request
message GetAppointmentNoteListRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the appointment id
  repeated int64 appointment_ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    min_items: 1
  }];
}

// get appointment_note request
message GetAppointmentNoteListResponse {
  // note
  repeated models.appointment.v1.AppointmentNoteModel notes = 1;
}

// get customer last note request
message GetCustomerLastNoteRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the customer id
  int64 customer_id = 2 [(validate.rules).int64 = {gt: 0}];
  // type
  moego.models.appointment.v1.AppointmentNoteType type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// get customer last note response
message GetCustomerLastNoteResponse {
  // note
  models.appointment.v1.AppointmentNoteModel note = 1;
}

// get appointment note request
message GetAppointmentNoteRequest {
  // the note id
  int64 id = 1;
}

// get appointment note response
message GetAppointmentNoteResponse {
  // note
  models.appointment.v1.AppointmentNoteModel note = 1;
}

// The request message for CreateAppointmentNote
message CreateAppointmentNoteRequest {
  // the appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // the creator account id
  int64 account_id = 2 [(validate.rules).int64 = {gt: 0}];

  // appointment note create definition
  moego.models.appointment.v1.AppointmentNoteCreateDef note = 3 [(validate.rules).message.required = true];
}

// The response message for CreateAppointmentNote
message CreateAppointmentNoteResponse {
  // the note id
  int64 id = 1;
}

// The request message for UpdateAppointmentNote
message UpdateAppointmentNoteRequest {
  // the update staff id
  int64 staff_id = 1 [(validate.rules).int64 = {gt: 0}];

  // appointment note update definition
  moego.models.appointment.v1.AppointmentNoteUpdateDef note = 2 [(validate.rules).message.required = true];
}

// The response message for UpdateAppointmentNote
message UpdateAppointmentNoteResponse {}

// The request message for ListAppointmentNotes
message ListAppointmentNotesRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // the filter
  message Filter {
    // the customer ids
    repeated int64 customer_ids = 1 [(validate.rules).repeated = {
      items: {
        int64: {gt: 0}
      }
      unique: true
    }];

    // the types of appointment note
    repeated moego.models.appointment.v1.AppointmentNoteType types = 2 [(validate.rules).repeated = {
      min_items: 0
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // the appointment ids
    repeated int64 appointment_ids = 3 [(validate.rules).repeated = {
      items: {
        int64: {gt: 0}
      }
      unique: true
    }];

    // the business ids
    repeated int64 business_ids = 4 [(validate.rules).repeated = {
      items: {
        int64: {gt: 0}
      }
      unique: true
    }];
  }
  // the request filter
  Filter filter = 2;
}

// the list appointment notes request
message ListAppointmentNotesResponse {
  // notes
  repeated models.appointment.v1.AppointmentNoteModel notes = 1;
}

// the appointment_note service
service AppointmentNoteService {
  // get appointment note list
  rpc GetAppointmentNoteList(GetAppointmentNoteListRequest) returns (GetAppointmentNoteListResponse);
  // get customer last note
  rpc GetCustomerLastNote(GetCustomerLastNoteRequest) returns (GetCustomerLastNoteResponse);

  // get appointment note
  rpc GetAppointmentNote(GetAppointmentNoteRequest) returns (GetAppointmentNoteResponse);

  // Create appointment note
  rpc CreateAppointmentNote(CreateAppointmentNoteRequest) returns (CreateAppointmentNoteResponse);

  // Update appointment note
  rpc UpdateAppointmentNote(UpdateAppointmentNoteRequest) returns (UpdateAppointmentNoteResponse);

  // List appointment notes
  rpc ListAppointmentNotes(ListAppointmentNotesRequest) returns (ListAppointmentNotesResponse);
}
