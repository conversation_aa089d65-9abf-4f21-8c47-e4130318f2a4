// @since 2024-02-13 11:27:17
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.appointment.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// Outbox event push request
message PushEventRequest {
  // start time, inclusive
  google.protobuf.Timestamp start = 1 [(validate.rules).timestamp = {required: true}];
  // end time, exclusive
  google.protobuf.Timestamp end = 2 [(validate.rules).timestamp = {required: true}];
}

// Outbox event push response
message PushEventResponse {}

// the outbox service
service OutboxService {
  // 推送指定时间范围内的消息记录
  rpc PushEvent(PushEventRequest) returns (PushEventResponse) {}
}
