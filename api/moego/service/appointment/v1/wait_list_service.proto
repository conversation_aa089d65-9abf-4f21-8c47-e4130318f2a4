// @since 2024-02-13 11:27:17
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.appointment.v1;

import "moego/models/appointment/v1/wait_list_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// create wait_list request
message GetWaitListByAppointmentRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the appointment id
  repeated int64 appointment_ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
}

// get wait_list request
message GetWaitListByAppointmentResponse {
  // wait list
  map<int64, models.appointment.v1.WaitListCalendarView> wait_list = 1;
}

// the wait_list service
service WaitListService {
  // get wait_list
  rpc GetWaitListByAppointment(GetWaitListByAppointmentRequest) returns (GetWaitListByAppointmentResponse);
}
