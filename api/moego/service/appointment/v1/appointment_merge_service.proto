syntax = "proto3";

package moego.service.appointment.v1;

import "moego/models/business_customer/v1/business_customer_merge_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// appointment merge client request
message MergeCustomerAppointmentDataRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // merge relation
  models.business_customer.v1.BusinessCustomerMergeRelationDef merge_relation = 2;
}

// appointment merge client response
message MergeCustomerAppointmentDataResponse {
  // success
  bool success = 1;
}

//appointment merge service
service AppointmentMergeService {
  //appointment merge customer data include : appt,note,report,service_operation,ob_review,package
  rpc MergeCustomerAppointmentData(MergeCustomerAppointmentDataRequest) returns (MergeCustomerAppointmentDataResponse);
}
