syntax = "proto3";

package moego.service.payment.v1;

import "moego/models/business_customer/v1/business_customer_merge_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v1;paymentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.payment.v1";

// payment merge client request
message MergeCustomerPaymentDataRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // merge relation
  models.business_customer.v1.BusinessCustomerMergeRelationDef merge_relation = 2;
}

// payment merge client response
message MergeCustomerPaymentDataResponse {
  // success
  bool success = 1;
}

//payment merge service
service PaymentMergeService {
  //payment merge customer data include : payment,refund
  rpc MergeCustomerPaymentData(MergeCustomerPaymentDataRequest) returns (MergeCustomerPaymentDataResponse);
}
