syntax = "proto3";

package moego.service.payment.v2;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v2;paymentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.payment.v2";

// Payment's operations & maintenance service.
service PaymentOpsService {
  // Import external Square customers and cards on file to Stripe.
  rpc ImportExternalSquareCofToStripe(ImportExternalSquareCofToStripeRequest) returns (ImportExternalSquareCofToStripeResponse);
}

// Request for ImportExternalSquareCofToStripe
message ImportExternalSquareCofToStripeRequest {
  // An entry is a mapping from a Square customer to a Stripe customer.
  message CofEntry {
    // The Stripe customer ID to import.
    string stripe_customer_id = 1;
    // The Stripe card on file ID to import (card_xx or pm_xx).
    string stripe_card_id = 2;
    // The card type (amex, visa, mastercard).
    string card_type = 3;
    // The last 4 digits of the card.
    string card_last4 = 4;
    // The card expiration month.
    string card_exp_month = 5;
    // The card expiration year.
    string card_exp_year = 6;
    // The card fingerprint.
    string card_fingerprint = 7;
  }

  // Company ID
  int64 company_id = 1;
  // Business ID
  int64 business_id = 2;
  // The entries to import.
  repeated CofEntry cof_entries = 3;
  // Whether to do a dry run.
  bool dry_run = 4;
}

// Response for ImportExternalSquareCofToStripe
message ImportExternalSquareCofToStripeResponse {}
