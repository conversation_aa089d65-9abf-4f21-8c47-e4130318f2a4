// (-- api-linter: core::0136::response-message-name=disabled --)
// (-- api-linter: core::0136::request-message-name=disabled --)

syntax = "proto3";

package moego.service.payment.v2;

import "google/protobuf/empty.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v2;paymentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.payment.v2";

// The payment task service.
service PaymentTaskService {
  // check payment status and retry payment
  rpc RetryPayment(google.protobuf.Empty) returns (google.protobuf.Empty);
  // check refund status and retry refund
  rpc RetryRefund(google.protobuf.Empty) returns (google.protobuf.Empty);
  // retry sync split payment
  rpc SyncSplitPayment(google.protobuf.Empty) returns (google.protobuf.Empty);
  // retry message delivery
  rpc DeliverMessage(google.protobuf.Empty) returns (google.protobuf.Empty);
}
