syntax = "proto3";

package moego.service.payment.v2;

import "moego/service/finance_gw/v1/webhook_downstream.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v2;paymentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.payment.v2";

// The payment webhook service.
service PaymentWebhookService {
  // Handle webhook event.
  // (-- api-linter: core::0136::response-message-name=disabled --)
  rpc HandleWebhookEvent(moego.service.finance_gw.v1.HandleWebhookEventRequest) returns (moego.service.finance_gw.v1.HandleWebhookEventResponse);
}
