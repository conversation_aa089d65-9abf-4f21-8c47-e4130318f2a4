syntax = "proto3";

package moego.service.permission.v1;

import "moego/models/permission/v1/permission_defs.proto";
import "moego/models/permission/v1/permission_enums.proto";
import "moego/models/permission/v1/permission_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/permission/v1;permissionsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.permission.v1";

// get role list request
message GetRoleListRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gte = 0];
  // token staff id
  int64 token_staff_id = 2 [(validate.rules).int64.gt = 0];
  // enterprise id 和 company id 二选一
  int64 enterprise_id = 3 [(validate.rules).int64.gte = 0];
}

// get role list response
message GetRoleListResponse {
  // role list
  repeated moego.models.permission.v1.RoleBriefView role_list = 1;
}

// get role detail request
message GetRoleDetailRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gte = 0];
  // token staff id
  optional int64 token_staff_id = 2 [(validate.rules).int64.gt = 0];
  // role id
  int64 role_id = 3 [(validate.rules).int64.gte = 0];
  // enterprise id 和 company id 二选一
  int64 enterprise_id = 4 [(validate.rules).int64.gte = 0];
  // permission category types
  // default: [models.permission.v1.PermissionCategoryType.MOEGO_PLATFORM]
  repeated models.permission.v1.PermissionCategoryType permission_category_types = 5;
}

// get role detail response
message GetRoleDetailResponse {
  // role detail
  moego.models.permission.v1.RoleModel role_detail = 1;
}

// list role details request
message ListRoleDetailsRequest {
  // Filter
  message Filter {
    // ids
    repeated int64 ids = 1;
    // enterprise ids
    // 和 company ids 是 Or 的关系，模型设计问题导致，后续争取统一
    repeated int64 enterprise_ids = 2;
    // company ids
    repeated int64 company_ids = 3;
    // permission category types
    // default: [models.permission.v1.PermissionCategoryType.MOEGO_PLATFORM]
    repeated models.permission.v1.PermissionCategoryType permission_category_types = 4;
  }
  // Filter
  Filter filter = 1 [(validate.rules).message = {required: true}];
}

// list role details response
message ListRoleDetailsResponse {
  // role list
  repeated moego.models.permission.v1.RoleModel roles = 1;
}

// create role request
message CreateRoleRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gte = 0];
  // 操作人，通常是 staff id，内部操作可以传字符串标识
  oneof identifier {
    option (validate.required) = true;
    // token staff id
    int64 token_staff_id = 2 [(validate.rules).int64.gt = 0];
    // internal operator
    string internal_operator = 3 [(validate.rules).string = {
      min_len: 1
      max_len: 255
    }];
  }
  // role name
  string role_name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // enterprise id 和 company id 两者只能传一个, 优先取enterprise id
  // 传 enterprise id 表示操作的 role 是 enterprise role
  optional int64 enterprise_id = 5 [(validate.rules).int64.gt = 0];
}

// create role response
message CreateRoleResponse {
  // the new role id
  int64 role_id = 1 [(validate.rules).int64.gt = 0];
  // role list after create
  repeated moego.models.permission.v1.RoleBriefView role_list = 2;
}

// update role request
message UpdateRoleRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gte = 0];
  // token staff id
  int64 token_staff_id = 2 [(validate.rules).int64.gt = 0];
  // role id
  int64 role_id = 3 [(validate.rules).int64.gt = 0];
  // role name
  string role_name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // enterprise id 和 company id 两者只能传一个, 优先取enterprise id
  // 传 enterprise id 表示操作的 role 是 enterprise role
  optional int64 enterprise_id = 5 [(validate.rules).int64.gt = 0];
}

// update role response
message UpdateRoleResponse {
  // role list after update
  repeated moego.models.permission.v1.RoleBriefView role_list = 1;
}

// delete role request
message DeleteRoleRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gte = 0];
  // token staff id
  int64 token_staff_id = 2 [(validate.rules).int64.gt = 0];
  // role id
  int64 role_id = 3 [(validate.rules).int64.gt = 0];
  // enterprise id 和 company id 两者只能传一个, 优先取enterprise id
  // 传 enterprise id 表示操作的 role 是 enterprise role
  optional int64 enterprise_id = 4 [(validate.rules).int64.gt = 0];
}

// delete role response
message DeleteRoleResponse {
  // role list after delete
  repeated moego.models.permission.v1.RoleBriefView role_list = 1;
}

// duplicate role request
message DuplicateRoleRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gte = 0];
  // token staff id
  int64 token_staff_id = 2 [(validate.rules).int64.gt = 0];
  // role id
  int64 role_id = 3 [(validate.rules).int64.gt = 0];
  // role name
  string role_name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // enterprise id 和 company id 两者只能传一个, 优先取enterprise id
  // 传 enterprise id 表示操作的 role 是 enterprise role
  optional int64 enterprise_id = 5 [(validate.rules).int64.gt = 0];
}

// duplicate role response
message DuplicateRoleResponse {
  // role list after duplicate
  repeated moego.models.permission.v1.RoleBriefView role_list = 1;
}

// edit permissions request
message EditPermissionsRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gte = 0];
  // 操作人，通常是 staff id，内部操作可以传字符串标识
  oneof identifier {
    option (validate.required) = true;
    // token staff id
    int64 token_staff_id = 2 [(validate.rules).int64.gt = 0];
    // internal operator
    string internal_operator = 3 [(validate.rules).string = {
      min_len: 1
      max_len: 255
    }];
  }
  // role id
  int64 role_id = 4 [(validate.rules).int64.gt = 0];
  // permission list in a category
  repeated moego.models.permission.v1.EditCategoryPermissionDef permission_category_list = 5;
  // enterprise id 和 company id 两者只能传一个, 优先取enterprise id
  // 传 enterprise id 表示操作的 role 是 enterprise role
  optional int64 enterprise_id = 6 [(validate.rules).int64.gt = 0];
}

// edit permissions response
message EditPermissionsResponse {
  // permission list after edit
  repeated moego.models.permission.v1.PermissionCategoryModel permission_category_list = 1;
}

// check permission request
message CheckPermissionRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gte = 0];
  // role id
  int64 role_id = 2 [(validate.rules).int64.gt = 0];
  // permission name list to check
  repeated string permission_name_list = 3 [(validate.rules).repeated = {
    items: {
      string: {
        min_len: 1
        max_len: 255
      }
    }
    min_items: 1
  }];
  // enterprise id 和 company id 两者只能传一个, 优先取enterprise id
  // 传 enterprise id 表示操作的 role 是 enterprise role
  optional int64 enterprise_id = 4 [(validate.rules).int64.gt = 0];
}

// check permission response
message CheckPermissionResponse {
  // permission name list that doesn't have permission
  repeated string no_permission_list = 1;
  // permission name list that need to check scope, key is permission name, value is the selected scope index
  map<string, int64> permission_with_scope_map = 2;
}

// init role will create 2 roles(admin, service provider), and assign permissions to them
message InitRoleRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
}

// init role response
message InitRoleResponse {
  // role id for admin
  int64 admin_role_id = 1;
  // role id for service provider
  int64 service_provider_role_id = 2;
}

// 将旧的权限映射到新的权限上，用于数据迁移，迁移完成后可以下线
message PermissionMappingRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // role id
  int64 role_id = 2 [(validate.rules).int64.gt = 0];
  // permission id list in old system
  repeated int64 old_permission_id_list = 3;
}

// permission mapping response
message PermissionMappingResponse {}

// get owner permission request
message GetOwnerPermissionRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
}

// get owner permission response
message GetOwnerPermissionResponse {
  // owner permission list
  repeated moego.models.permission.v1.PermissionCategoryModel permission_category_list = 1;
}

// init owner role for companies request
message InitOwnerRoleForCompaniesRequest {
  // company ids
  repeated int64 company_ids = 1;
}

// init owner role for companies response
message InitOwnerRoleForCompaniesResponse {
  // company id and role id map
  map<int64, int64> company_role_id_map = 1;
  // already initialized company ids
  repeated int64 already_initialized_company_ids = 2;
}

// init enterprise role request
message InitEnterpriseRoleRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64.gt = 0];
}

// init enterprise role response
message InitEnterpriseRoleResponse {
  // enterprise owner role id
  int64 owner_role_id = 1;
}

// copy permissions request
message CopyPermissionsRequest {
  // source role id
  int64 source_role_id = 1 [(validate.rules).int64.gt = 0];
  // target role id
  repeated int64 target_role_ids = 2 [(validate.rules).repeated.items.int64.gt = 0];
}

// copy permissions response
message CopyPermissionsResponse {}

// RetainRolePermissionsRequest
message RetainRolePermissionsRequest {
  // role ids
  repeated int64 role_ids = 1 [(validate.rules).repeated.items.int64.gt = 0];
  // permission ids
  repeated int64 retained_permission_ids = 2;
}

// RetainRolePermissionsResponse
message RetainRolePermissionsResponse {}

// permission service
service PermissionService {
  // get role list
  rpc GetRoleList(GetRoleListRequest) returns (GetRoleListResponse) {}
  // get role detail
  rpc GetRoleDetail(GetRoleDetailRequest) returns (GetRoleDetailResponse) {}
  // list role details
  rpc ListRoleDetails(ListRoleDetailsRequest) returns (ListRoleDetailsResponse) {}
  // create role
  rpc CreateRole(CreateRoleRequest) returns (CreateRoleResponse) {}
  // update role
  rpc UpdateRole(UpdateRoleRequest) returns (UpdateRoleResponse) {}
  // delete role
  rpc DeleteRole(DeleteRoleRequest) returns (DeleteRoleResponse) {}
  // duplicate role
  rpc DuplicateRole(DuplicateRoleRequest) returns (DuplicateRoleResponse) {}
  // edit permissions
  rpc EditPermissions(EditPermissionsRequest) returns (EditPermissionsResponse) {}
  // check permission
  rpc CheckPermission(CheckPermissionRequest) returns (CheckPermissionResponse) {}
  // init role
  rpc InitRole(InitRoleRequest) returns (InitRoleResponse) {}
  // permission mapping
  rpc PermissionMapping(PermissionMappingRequest) returns (PermissionMappingResponse) {}
  // get owner permission
  rpc GetOwnerPermission(GetOwnerPermissionRequest) returns (GetOwnerPermissionResponse) {}
  // Init owner role for companies
  rpc InitOwnerRoleForCompanies(InitOwnerRoleForCompaniesRequest) returns (InitOwnerRoleForCompaniesResponse);
  // init enterprise role
  rpc InitEnterpriseRole(InitEnterpriseRoleRequest) returns (InitEnterpriseRoleResponse) {}
  // copy permissions from one role to another
  rpc CopyPermissions(CopyPermissionsRequest) returns (CopyPermissionsResponse) {}
  // Remove any permissions not in the given list
  rpc RetainRolePermissions(RetainRolePermissionsRequest) returns (RetainRolePermissionsResponse);
}
