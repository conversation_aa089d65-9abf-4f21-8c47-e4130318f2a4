syntax = "proto3";

package moego.service.permission.v1;

import "google/protobuf/empty.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/permission/v1;permissionsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.permission.v1";

// migration service for permission
service MigrationService {
  // AddAccessClientScope
  rpc AddAccessClientScope(google.protobuf.Empty) returns (google.protobuf.Empty);
  // Add new setting permission
  rpc AddNewSettingPermission(google.protobuf.Empty) returns (google.protobuf.Empty);
}
