// @since 2023-04-07 09:48:36
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.metadata.v1;

import "google/protobuf/empty.proto";
import "moego/models/metadata/v1/metadata_defs.proto";
import "moego/models/metadata/v1/metadata_enums.proto";
import "moego/models/metadata/v1/metadata_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/metadata/v1;metadatasvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.metadata.v1";

// describe key groups response
message DescribeGroupsResponse {
  // all of the groups
  repeated string groups = 1;
}

// create key request
message CreateKeyRequest {
  // the key def
  moego.models.metadata.v1.KeyFullDef key_def = 1 [(validate.rules).message = {required: true}];
  // the operator id
  string internal_operator_id = 2 [(validate.rules).string = {
    min_len: 2
    max_len: 50
  }];
}

// create key response
message CreateKeyResponse {
  // the created key model
  moego.models.metadata.v1.KeyModel key = 1;
}

// update key request
message UpdateKeyRequest {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // the key def
  moego.models.metadata.v1.KeyPartialDef key_def = 2 [(validate.rules).message = {required: true}];
  // the operator id
  string internal_operator_id = 3 [(validate.rules).string = {
    min_len: 2
    max_len: 50
  }];
}

// get key request
message GetKeyRequest {
  // the key identifier
  oneof identifier {
    option (validate.required) = true;
    // the id, will not filter deleted
    int64 id = 1 [(validate.rules).int64 = {gt: 0}];
    // the name, will filter non deleted
    string name = 2 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
}

// get key response
message GetKeyResponse {
  // the key model
  moego.models.metadata.v1.KeyModel key = 1;
}

// describe keys request
message DescribeKeysRequest {
  // filter by group, empty will not filter
  optional string group = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // filter by owner type, 0 will not filter
  optional moego.models.metadata.v1.OwnerType owner_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // filter by name like
  optional string name_like = 3 [(validate.rules).string = {max_len: 50}];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 4;
}

// describe keys response
message DescribeKeysResponse {
  // the key list
  repeated moego.models.metadata.v1.KeyModel keys = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// delete key request
message DeleteKeyRequest {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // the operator
  string internal_operator_id = 2 [(validate.rules).string = {
    min_len: 2
    max_len: 50
  }];
}

// update value request
message UpdateValueRequest {
  // the key id
  int64 key_id = 1 [(validate.rules).int64 = {gt: 0}];

  // the owner id
  int64 owner_id = 2 [(validate.rules).int64 = {gt: 0}];

  // the value, skip this field will reset the value to default value
  optional string value = 3 [(validate.rules).string = {max_len: 1048576}];

  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // the operator id
    int64 operator_id = 4 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 5 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
}

// get value request
message GetValueRequest {
  // the key id
  int64 key_id = 1 [(validate.rules).int64 = {gt: 0}];

  // the owner id, if it is system, any value is allowed
  int64 owner_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// get value response
message GetValueResponse {
  // the value
  moego.models.metadata.v1.ValueModel value = 2;
}

// describe values request, only find values
message DescribeValuesRequest {
  // filter by key
  int64 key_id = 1 [(validate.rules).int64 = {gt: 0}];
  // filter by owner ids
  repeated int64 owner_ids = 2 [(validate.rules).repeated = {
    max_items: 200
    items: {
      int64: {gt: 0}
    }
    unique: true
    ignore_empty: true
  }];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// describe values response
message DescribeValuesResponse {
  // the values
  repeated moego.models.metadata.v1.ValueModel values = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// get all values for specified owners
message ExtractValuesRequest {
  // the specifier to filter keys
  oneof specifier {
    option (validate.required) = true;
    // filter by group
    string group = 1 [(validate.rules).string = {
      min_len: 1
      max_len: 50
    }];
    // the key id
    int64 key_id = 2 [(validate.rules).int64 = {gt: 0}];
    // filter by key, if specified key does not
    // exist or expired, will throw an not found exception.
    string key_name = 3 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }

  // the owner map, key is OwnerType
  map<int64, int64> owners = 4 [(validate.rules).map = {max_pairs: 10}];
}

// get all values for specified owners
message DescribeMetadataRequest {
  // the specifier to filter keys
  oneof specifier {
    option (validate.required) = true;
    // filter by group
    string group = 1 [(validate.rules).string = {
      min_len: 1
      max_len: 50
    }];
    // the key id
    int64 key_id = 2 [(validate.rules).int64 = {gt: 0}];
    // filter by key, if specified key does not
    // exist or expired, will throw an not found exception.
    string key_name = 3 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }

  // the owner map, key is OwnerType
  map<int64, int64> owners = 4 [(validate.rules).map = {max_pairs: 10}];
}

// get all values response
message ExtractValuesResponse {
  // the values map
  map<string, string> values = 1 [deprecated = true];
}

// get all values response
message DescribeMetadataResponse {
  // the key map
  map<string, moego.models.metadata.v1.KeyModel> key_map = 1;
  // the values map
  map<string, moego.models.metadata.v1.ValueModel> value_map = 2;
}

// extract values v2 request
message ExtractValuesV2Request {
  // the specifier to filter keys
  oneof specifier {
    option (validate.required) = true;
    // the key id
    int64 key_id = 1 [(validate.rules).int64 = {gt: 0}];
    // filter by key, if specified key does not
    // exist or expired, will throw an not found exception.
    string key_name = 2 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // owner list
  repeated moego.models.metadata.v1.OwnerModel owners = 3;
}

// extract values v2 response
message ExtractValuesV2Response {
  // result
  message Result {
    // owner
    moego.models.metadata.v1.OwnerModel owner = 1;
    // values
    string value = 2;
  }
  // results
  repeated Result results = 1;
}

// the metadata service
service MetadataService {
  // get all key groups
  rpc DescribeGroups(google.protobuf.Empty) returns (DescribeGroupsResponse);
  // create key
  rpc CreateKey(CreateKeyRequest) returns (CreateKeyResponse);
  // update key
  rpc UpdateKey(UpdateKeyRequest) returns (google.protobuf.Empty);
  // get a single key
  rpc GetKey(GetKeyRequest) returns (GetKeyResponse);
  // get all keys
  rpc DescribeKeys(DescribeKeysRequest) returns (DescribeKeysResponse);
  // delete a key
  rpc DeleteKey(DeleteKeyRequest) returns (google.protobuf.Empty);

  // update a value for an owner
  rpc UpdateValue(UpdateValueRequest) returns (google.protobuf.Empty);
  // get a value for owner
  rpc GetValue(GetValueRequest) returns (GetValueResponse);
  // get all values
  rpc DescribeValues(DescribeValuesRequest) returns (DescribeValuesResponse);
  // extract all values for specified owners, including system level keys,
  // if a field is not customized, will be filled with default value.
  rpc ExtractValues(ExtractValuesRequest) returns (ExtractValuesResponse);
  // describe metadata
  rpc DescribeMetadata(DescribeMetadataRequest) returns (DescribeMetadataResponse);
  // extract values v2
  rpc ExtractValuesV2(ExtractValuesV2Request) returns (ExtractValuesV2Response);
}
