syntax = "proto3";

package moego.service.finance_gw.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/finance_gw/v1;financegwsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.finance_gw.v1";

// Request for HandleKanmonEvent
message HandleKanmonEventRequest {
  // The webhook-signature header of the event request.
  string signature = 1 [(validate.rules).string.min_len = 1];
  // The webhook-id header of the event request.
  string webhook_id = 2;
  // The webhook-timestamp header parsed into int64.
  int64 webhook_timestamp = 3 [(validate.rules).int64.gt = 0];
  // The raw event body in JSON.
  bytes event_body = 4 [(validate.rules).bytes.min_len = 1];
}

// Response for HandleKanmonEvent
message HandleKanmonEventResponse {}

// The gateway service for Kanmon's webhooks.
service KanmonWebhookService {
  // Handle Kanmon's webhook event. The raw JSON event body is received.
  rpc HandleKanmonEvent(HandleKanmonEventRequest) returns (HandleKanmonEventResponse);
}
