syntax = "proto3";

package moego.service.finance_gw.v1;

import "google/protobuf/empty.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/finance_gw/v1;financegwsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.finance_gw.v1";

// Request for HandleStripeEvent
message HandleStripeEventRequest {
  // The signature of the event request
  string signature = 1 [(validate.rules).string.min_len = 1];
  // The raw event body in JSON.
  bytes event_body = 2 [(validate.rules).bytes.min_len = 1];
}

// The gateway service for Stripe's webhooks.
service StripeWebhookService {
  // Handle Stripe's webhook event. The raw JSON event body is received.
  rpc HandleStripeEvent(HandleStripeEventRequest) returns (google.protobuf.Empty);
}
