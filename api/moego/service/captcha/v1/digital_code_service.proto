syntax = "proto3";

package moego.service.captcha.v1;

import "google/protobuf/empty.proto";
import "moego/models/captcha/v1/verification_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/captcha/v1;captchasvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.captcha.v1";

// create digital code by email request
message CreateDigitalCodeByEmailRequest {
  // verification scenario
  moego.models.captcha.v1.VerificationScenario verification_scenario = 1;

  // email
  string email = 2 [(validate.rules).string = {
    max_len: 256
    email: true
  }];

  //  // recipient (name), optional
  //  string recipient;
}

// create digital code by SMS request
message CreateDigitalCodeBySMSRequest {
  // verification scenario
  moego.models.captcha.v1.VerificationScenario verification_scenario = 1;

  // phone number
  string phone_number = 2 [(validate.rules).string = {pattern: "^\\+[1-9]\\d{1,18}$"}];
}

// create digital code response
message CreateDigitalCodeResponse {
  // verification token
  string verification_token = 1;
}

// validate digital code request
message ValidateDigitalCodeRequest {
  // verification scenario
  moego.models.captcha.v1.VerificationScenario verification_scenario = 1;

  // verification token
  string verification_token = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 512
  }];

  // digital code
  string code = 3;

  // resource key, email or phone number
  string resource_key = 4;

  // keep alive
  bool keep_alive = 5;

  // renewal expiration time if code is valid and need keep alive
  bool renewal = 6;
}

// digital code service
service DigitalCodeService {
  // create digital code by email
  rpc CreateDigitalCodeByEmail(CreateDigitalCodeByEmailRequest) returns (CreateDigitalCodeResponse);

  // create digital code by SMS
  rpc CreateDigitalCodeBySMS(CreateDigitalCodeBySMSRequest) returns (CreateDigitalCodeResponse);

  // validate digital code
  rpc ValidateDigitalCode(ValidateDigitalCodeRequest) returns (google.protobuf.Empty);
}
