syntax = "proto3";

package moego.service.accounting.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/accounting/v1/accounting_enums.proto";
import "moego/models/accounting/v1/accounting_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/accounting/v1;accountingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.accounting.v1";

// GetVisibilityRequest
message GetVisibilityRequest {
  // company id
  int64 company_id = 1;
}

// GetVisibilityResponse
message GetVisibilityResponse {
  // visibility
  bool visible = 1;
  // visibility class
  moego.models.accounting.v1.VisibilityClass visibility_class = 2;
}

// AddToEnrollListRequest
message AddToEnrollListRequest {
  // company id
  repeated int64 company_ids = 1;
}

// AddToEnrollListResponse
message AddToEnrollListResponse {}

// GetEnrollListRequest
message GetEnrollListRequest {}

// GetEnrollListRequest
message GetEnrollListResponse {
  // company ids
  repeated int64 company_ids = 1;
}

// GetOnboardingStatusRequest
message GetOnboardingStatusRequest {
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 1;
  // company id
  int64 company_id = 2;
}

// GetOnboardingStatusResponse
message GetOnboardingStatusResponse {
  // onboarding status
  moego.models.accounting.v1.OnboardingStatus onboarding_status = 1;
}

// GetUnselectedBusinessesRequest
message GetUnselectedBusinessesRequest {
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 1;
  // company id
  int64 company_id = 2;
}

// GetUnselectedBusinessesResponse
message GetUnselectedBusinessesResponse {
  // if has completed selection
  bool has_completed_selection = 1;
  // businesses
  repeated int64 unselected_business_ids = 2;
}

// SetUnselectedBusinessesRequest
message SetUnselectedBusinessesRequest {
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 1;
  // company id
  int64 company_id = 2;
  // business ids
  repeated int64 unselected_business_ids = 3;
  // legal name
  string legal_name = 4;
  // us state
  moego.models.accounting.v1.USState state = 5;
  // entity type
  moego.models.accounting.v1.EntityType entity_type = 6;
}

// SetUnselectedBusinessesResponse
message SetUnselectedBusinessesResponse {}

// AddUnselectedBusinessesRequest
message AddUnselectedBusinessesRequest {
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 1;
  // company id
  int64 company_id = 2;
  // business id
  repeated int64 unselected_business_ids = 3;
}

// AddUnselectedBusinessesResponse
message AddUnselectedBusinessesResponse {}

// RemoveUnselectedBusinessesRequest
message RemoveUnselectedBusinessesRequest {
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 1;
  // company id
  int64 company_id = 2;
  // business id
  repeated int64 unselected_business_ids = 3;
}

// RemoveUnselectedBusinessesResponse
message RemoveUnselectedBusinessesResponse {}

// GetAuthTokenRequest
message GetAuthTokenRequest {
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
}

// ListSettingsRequest
message ListSettingsRequest {
  // filter
  message Filter {
    // channel type
    repeated moego.models.accounting.v1.ChannelType channel_types = 1;
    // company id
    repeated int64 company_ids = 2;
    // business id
    repeated int64 business_ids = 3;
  }

  // filter
  Filter filter = 1;
}

// ListSettingsResponse
message ListSettingsResponse {
  // settings
  repeated moego.models.accounting.v1.Setting settings = 1;
}

// GetAuthTokenResponse
message GetAuthTokenResponse {
  // channel business id
  string channel_business_id = 1;
  // token
  string token = 2;
  // expiration time
  google.protobuf.Timestamp expiration_time = 3;
}

// AccountingService
service AccountingService {
  // GetVisibility
  rpc GetVisibility(GetVisibilityRequest) returns (GetVisibilityResponse);
  // AddToEnrollList
  rpc AddToEnrollList(AddToEnrollListRequest) returns (AddToEnrollListResponse);
  // GetEnrollList
  rpc GetEnrollList(GetEnrollListRequest) returns (GetEnrollListResponse);

  // get onboarding status
  rpc GetOnboardingStatus(GetOnboardingStatusRequest) returns (GetOnboardingStatusResponse);

  // get unselected businesses
  rpc GetUnselectedBusinesses(GetUnselectedBusinessesRequest) returns (GetUnselectedBusinessesResponse);
  // set unselected businesses
  rpc SetUnselectedBusinesses(SetUnselectedBusinessesRequest) returns (SetUnselectedBusinessesResponse);
  // add unselected businesses
  rpc AddUnselectedBusinesses(AddUnselectedBusinessesRequest) returns (AddUnselectedBusinessesResponse);
  // remove unselected businesses
  rpc RemoveUnselectedBusinesses(RemoveUnselectedBusinessesRequest) returns (RemoveUnselectedBusinessesResponse);

  // list settings
  rpc ListSettings(ListSettingsRequest) returns (ListSettingsResponse);

  // get auth token
  rpc GetAuthToken(GetAuthTokenRequest) returns (GetAuthTokenResponse);

  // retry sync data
  rpc RetrySync(google.protobuf.Empty) returns (google.protobuf.Empty);
}
