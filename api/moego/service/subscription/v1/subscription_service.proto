syntax = "proto3";

package moego.service.subscription.v1;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "moego/models/billing/v1/subscription_models.proto";
import "moego/models/subscription/v1/subscription_models.proto";
import "moego/utils/v1/time_period.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/subscription/v1;subscriptionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.subscription.v1";

// SubscriptionService is the service for subscription
service SubscriptionService {
  // Create Subscription
  rpc CreateSubscription(CreateSubscriptionRequest) returns (CreateSubscriptionResponse);
  // Update Subscription
  rpc UpdateSubscription(UpdateSubscriptionRequest) returns (UpdateSubscriptionResponse);
  // Get Subscription
  rpc GetSubscriptions(GetSubscriptionsRequest) returns (GetSubscriptionsResponse);
  // Update Subscriptions
  rpc UpdateSubscriptions(UpdateSubscriptionsRequest) returns (UpdateSubscriptionsResponse);
  // ListSubscriptions
  rpc ListSubscriptions(ListSubscriptionsRequest) returns (ListSubscriptionsResponse);
  // ListBuyerSubscriptions
  rpc ListBuyerSubscriptions(ListBuyerSubscriptionsRequest) returns (ListBuyerSubscriptionsResponse);
  // CancelSubscription
  rpc CancelSubscription(CancelSubscriptionRequest) returns (CancelSubscriptionResponse);
  // RenewSubscription
  rpc RenewSubscription(RenewSubscriptionRequest) returns (RenewSubscriptionResponse);
  // PauseSubscription
  rpc PauseSubscription(PauseSubscriptionRequest) returns (PauseSubscriptionResponse);
  // ResumeSubscription
  rpc ResumeSubscription(ResumeSubscriptionRequest) returns (ResumeSubscriptionResponse);
  // StatSubscription
  rpc StatSubscription(StatSubscriptionRequest) returns (StatSubscriptionResponse);
  // GetSubscriptionReport
  rpc GetSubscriptionReport(GetSubscriptionReportRequest) returns (GetSubscriptionReportResponse);

  // PreviewPurchases
  rpc PreviewPurchases(PreviewPurchasesRequest) returns (PreviewPurchasesResponse);
  // ListPurchaseDetails
  rpc ListPurchaseDetails(ListPurchaseDetailsRequest) returns (ListPurchaseDetailsResponse);

  // CreateProduct
  rpc CreateProduct(CreateProductRequest) returns (CreateProductResponse);
  // ListProducts
  rpc ListProducts(ListProductsRequest) returns (ListProductsResponse);
  // UpdateProduct
  rpc UpdateProduct(UpdateProductRequest) returns (UpdateProductResponse);
  // PurchaseProducts
  rpc PurchaseProducts(PurchaseProductsRequest) returns (PurchaseProductsResponse);

  // CreatePrice
  rpc CreatePrice(CreatePriceRequest) returns (CreatePriceResponse);
  // ListPrices
  rpc ListPrices(ListPricesRequest) returns (ListPricesResponse);

  // CreateFeature
  rpc CreateFeature(CreateFeatureRequest) returns (CreateFeatureResponse);
  // ListFeatures
  rpc ListFeatures(ListFeaturesRequest) returns (ListFeaturesResponse);
  // UpdateFeature
  rpc UpdateFeature(UpdateFeatureRequest) returns (UpdateFeatureResponse);
  // delete feature
  rpc DeleteFeature(DeleteFeatureRequest) returns (DeleteFeatureResponse);

  // ListDiscounts
  rpc ListDiscounts(ListDiscountsRequest) returns (ListDiscountsResponse);

  // create license and entitlements
  rpc CreateLicenseAndEntitlements(CreateLicenseAndEntitlementsRequest) returns (CreateLicenseAndEntitlementsResponse);
  // ListLicenses
  rpc ListLicenses(ListLicensesRequest) returns (ListLicensesResponse);

  // list revisions
  rpc ListRevisions(ListRevisionsRequest) returns (ListRevisionsResponse);

  // ListEntitlements
  rpc ListEntitlements(ListEntitlementsRequest) returns (ListEntitlementsResponse);
  // UpdateEntitlements
  rpc UpdateEntitlements(UpdateEntitlementsRequest) returns (UpdateEntitlementsResponse);
  // ConsumeEntitlements
  rpc ConsumeEntitlements(ConsumeEntitlementsRequest) returns (ConsumeEntitlementsResponse);
  // ListLastRedeemTimeForCustomers
  rpc ListLastRedeemTimeForCustomers(ListLastRedeemTimeForCustomerRequest) returns (ListLastRedeemTimeForCustomersResponse);

  // RefreshExistedApplicationFee
  rpc RefreshExistedApplicationFee(RefreshExistedApplicationFeeRequest) returns (RefreshExistedApplicationFeeResponse);

  // 本质是consume package，但是为了实现目前membership 消费perks场景，这一期要这么做，后续会deprecated
  // ConsumePackages
  rpc ConsumePackages(ConsumePackagesRequest) returns (ConsumePackagesResponse);
  // 临时接口，查询所有购买过 membership 顾客的已激活订阅是否和系统内匹配
  rpc CheckMemberships(CheckMembershipsRequest) returns (CheckMembershipsResponse);
  // 临时接口，将所有激活的 membership 账期重置为和 stripe 一模一样
  rpc RefreshMemberships(RefreshMembershipsRequest) returns (RefreshMembershipsResponse);
}

// CreateSubscriptionRequest
message CreateSubscriptionRequest {
  // 拥有者，作为订阅的归属
  models.subscription.v1.User buyer = 1;
  // 卖家，作为计费金额的归属
  models.subscription.v1.User seller = 2;
  // 仅支持创建 TRIAL, PENDING 状态的订阅，默认为 PENDING
  models.subscription.v1.Subscription.Status status = 3;
  // 非 TRIAL 订阅必填
  optional string card_on_file_id = 4;
  // 创建订阅时购买一个 Plan Product，不可少买和多买
  // Billing Cycle 取 Plan Product 的 Price Billing Cycle
  repeated models.subscription.v1.Purchase purchases = 5;
  // 宽限期，不传则默认为不允许宽限，在支付失败后直接取消订阅
  moego.utils.v1.TimePeriod grace_period = 6;
  // 创建操作人，staff id
  optional int64 sell_operator_staff_id = 7 [(validate.rules).int64 = {gte: 0}];
  // 订阅开始时间，支持创建现在和未来的订阅
  google.protobuf.Timestamp start_at = 8;
  // 是否需要创建 license 和 entitlements，默认为 false,即默认会创建 license和entitlements
  optional bool without_license_and_entitlements = 9;
  // payment behavior, 默认 DEFAULT_INCOMPLETE
  optional models.billing.v1.SubscriptionModel.PaymentBehavior payment_behavior = 10;
}

// CreateSubscriptionResponse
message CreateSubscriptionResponse {
  // 订阅信息
  moego.models.subscription.v1.Subscription subscription = 1;
  // 前端调 stripe sdk 时需要通过 client_secret 来确认支付状态
  // To be deprecated
  string external_stripe_client_secret = 2;
}

// UpdateSubscriptionRequest
message UpdateSubscriptionRequest {
  // 订阅 ID
  int64 id = 1;
  // payment method
  optional string card_on_file_id = 2;
}

// UpdateSubscriptionResponse
message UpdateSubscriptionResponse {
  // 订阅信息
  moego.models.subscription.v1.Subscription subscription = 1;
}

// GetSubscriptionsRequest
message GetSubscriptionsRequest {
  // 订阅 ID
  repeated int64 ids = 1;
}

// GetSubscriptionsResponse
message GetSubscriptionsResponse {
  // 订阅信息
  repeated models.subscription.v1.Subscription subscriptions = 1;
}

// UpdateSubscriptionsRequest
message UpdateSubscriptionsRequest {
  // 批量修改的内容
  message Change {
    // 购买内容
    models.subscription.v1.Purchase purchase = 1;
    // 扣款卡 ID
    string card_on_file_id = 2;
    // 购买者
    models.subscription.v1.User buyer = 3;
  }
  // 批量修改的订阅过滤条件
  message Filter {
    // 套餐 ID
    repeated int64 plan_product_ids = 1;
    // 购买者
    repeated models.subscription.v1.User buyers = 2;
  }

  // 修改内容
  Change change = 1;
  // 过滤条件
  Filter filter = 2;
}

// UpdateSubscriptionsResponse
message UpdateSubscriptionsResponse {
  // 修改后的订阅信息
  repeated models.subscription.v1.Subscription subscriptions = 1;
}

// ListSubscriptionsRequest
message ListSubscriptionsRequest {
  // 分页信息
  utils.v2.PaginationRequest pagination = 1;
  // 过滤条件
  message Filter {
    // 购买者
    repeated models.subscription.v1.User buyers = 1;
    // 订阅状态
    repeated models.subscription.v1.Subscription.Status statuses = 2;
    // 套餐 ID
    repeated int64 plan_product_ids = 3;
    // 订阅 ID
    repeated int64 ids = 4;
  }
  // 过滤条件
  Filter filter = 2;
  // 排序条件
  message OrderBy {
    // 是否降序
    bool desc = 1;
    // 排序字段
    oneof field {
      // 订阅名称
      bool name = 2;
      // 订阅创建时间
      bool created_at = 3;
      // 订阅开始时间
      bool validity_start_time = 4;
    }
  }
  // 排序条件
  OrderBy order_by = 3;
}

// ListSubscriptionsResponse
message ListSubscriptionsResponse {
  // 分页信息
  utils.v2.PaginationResponse pagination = 1;
  // 订阅信息
  repeated models.subscription.v1.Subscription subscriptions = 2;
}

// ListBuyerSubscriptionsRequest
message ListBuyerSubscriptionsRequest {
  // 每个购买者最多返回的订阅数量
  int32 limit_per_buyer = 1;
  // 过滤条件
  message Filter {
    // 购买者
    repeated models.subscription.v1.User buyers = 1;
    // 订阅状态
    repeated models.subscription.v1.Subscription.Status statuses = 2;
  }
  // 过滤条件
  Filter filter = 2;
  // 排序条件
  message OrderBy {
    // 是否降序
    bool desc = 1;
    // 排序字段
    oneof field {
      // 订阅名称
      bool name = 2;
      // 订阅创建时间
      bool created_at = 3;
    }
  }
  // 排序条件
  OrderBy order_by = 3;
}

// ListBuyerSubscriptionsResponse
message ListBuyerSubscriptionsResponse {
  // 购买者订阅信息
  repeated models.subscription.v1.BuyerSubscription buyer_subscriptions = 1;
}

// CancelSubscriptionRequest
message CancelSubscriptionRequest {
  // 退款策略
  enum RefundPolicy {
    // 未定义
    REFUND_POLICY_UNSPECIFIED = 0;
    // 不退款
    NO_REFUND = 1;
    // 全额退款
    FULL_REFUND = 2;
    // 按比例退款
    PRORATED_REFUND = 3;
  }
  // 取消策略
  enum CancelPolicy {
    // 未定义
    CANCEL_POLICY_UNSPECIFIED = 0;
    // 立即取消
    IMMEDIATELY = 1;
    // 到期取消
    AT_PERIOD_END = 2;
  }
  // ID
  int64 id = 1;
  // 退款策略
  RefundPolicy refund_policy = 2;
  // 取消策略
  CancelPolicy cancel_policy = 3;
  // 取消原因
  message Reason {
    // 类型
    string kind = 1;
    // 详情
    string detail = 2;
  }
  // 取消原因
  Reason reason = 6;
}

// CancelSubscriptionResponse
message CancelSubscriptionResponse {
  // 订阅信息
  moego.models.subscription.v1.Subscription subscription = 1;
}

// RenewSubscriptionRequest
message RenewSubscriptionRequest {
  // ID
  int64 id = 1;
  // 已取消订阅必填
  optional string card_on_file_id = 2;
}

// RenewSubscriptionResponse
message RenewSubscriptionResponse {
  // 订阅信息
  moego.models.subscription.v1.Subscription subscription = 1;
  // 前端调 stripe sdk 时需要通过 client_secret 来确认支付状态
  // 仅有过期订阅 renew 会返回
  // To be deprecated
  string external_stripe_client_secret = 2;
}

// PauseSubscriptionRequest
message PauseSubscriptionRequest {
  // ID
  int64 id = 1;
  // 自动恢复时间
  google.protobuf.Timestamp auto_resume_at = 2;
}

// PauseSubscriptionResponse
message PauseSubscriptionResponse {
  // 订阅信息
  moego.models.subscription.v1.Subscription subscription = 1;
}

// ResumeSubscriptionRequest
message ResumeSubscriptionRequest {
  // ID
  int64 id = 1;
}

// ResumeSubscriptionResponse
message ResumeSubscriptionResponse {
  // 订阅信息
  moego.models.subscription.v1.Subscription subscription = 1;
}

// StatSubscriptionRequest
// 临时接口，用于统计订阅的状态
message StatSubscriptionRequest {
  // 产品 ID
  repeated int64 product_ids = 1;
}

// StatSubscriptionResponse
message StatSubscriptionResponse {
  // 统计信息
  repeated models.subscription.v1.SubscriptionStat stats = 1;
}

// PreviewPurchasesRequest
message PreviewPurchasesRequest {
  // 购买信息
  repeated models.subscription.v1.Purchase purchases = 1;
}

// PreviewPurchasesResponse
message PreviewPurchasesResponse {
  // 购买信息
  repeated models.subscription.v1.PurchaseDetail purchase_details = 1;
}

// ListPurchaseDetailsRequest
message ListPurchaseDetailsRequest {
  // 分页信息
  utils.v2.PaginationRequest pagination = 1;
  // 过滤条件
  message Filter {
    // 订阅 ID
    repeated int64 subscription_ids = 1;
  }
  // 过滤条件
  Filter filter = 2;
}

// ListPurchaseDetailsResponse
message ListPurchaseDetailsResponse {
  // 分页信息
  utils.v2.PaginationResponse pagination = 1;
  // 购买信息
  repeated models.subscription.v1.PurchaseDetail purchase_details = 2;
}

// CreateProductRequest
message CreateProductRequest {
  // 产品名称
  string name = 1;
  // 产品描述
  string description = 2 [(validate.rules).string = {max_len: 1500}];
  // 产品类型
  models.subscription.v1.Product.Type type = 3;
  // 卖家
  models.subscription.v1.User seller = 4;
  // 产品特性
  repeated int64 feature_ids = 5;
  // 购买限制
  models.subscription.v1.PurchaseLimit purchase_limit = 6;
  // 业务类型
  models.subscription.v1.Product.BusinessType business_type = 7;
  // extra
  models.subscription.v1.ProductExtra extra = 8;
}

// CreateProductResponse
message CreateProductResponse {
  // 产品信息
  models.subscription.v1.Product product = 1;
}

// ListProductsRequest
message ListProductsRequest {
  // 分页信息
  utils.v2.PaginationRequest pagination = 1;
  // 过滤条件
  message Filter {
    // 卖家
    repeated models.subscription.v1.User sellers = 1;
    // 产品类型
    repeated models.subscription.v1.Product.Type types = 2;
    // 业务类型
    repeated models.subscription.v1.Product.BusinessType business_types = 3;
    // id
    repeated int64 ids = 4;
  }
  // 过滤条件
  Filter filter = 2;
}

// ListProductsResponse
message ListProductsResponse {
  // 分页信息
  utils.v2.PaginationResponse pagination = 1;
  // 产品信息
  repeated models.subscription.v1.Product products = 2;
}

// UpdateProductRequest
message UpdateProductRequest {
  // ID
  int64 id = 1;
  // 产品名称
  string name = 2;
  // 产品描述
  string description = 3 [(validate.rules).string = {max_len: 1500}];
  // 产品类型
  models.subscription.v1.PurchaseLimit purchase_limit = 4;
  // 产品特性
  repeated int64 feature_ids = 5;
}

// UpdateProductResponse
message UpdateProductResponse {
  // 产品信息
  models.subscription.v1.Product product = 1;
}

// PurchaseProductsRequest
message PurchaseProductsRequest {
  // 购买者
  models.subscription.v1.User buyer = 1;
  // 卖家
  models.subscription.v1.User seller = 2;
  // 购买信息
  repeated models.subscription.v1.Purchase purchases = 3;
  // 订阅 ID
  int64 subscription_id = 4;
}

// PurchaseProductResponse
message PurchaseProductsResponse {}

// CreatePriceRequest
message CreatePriceRequest {
  // 产品 ID
  int64 product_id = 1;
  // 价格名称
  string name = 2;
  // 价格类型
  models.subscription.v1.Price.Type type = 3;
  // 价格
  google.type.Money unit_price = 4;
  // 计费周期
  moego.utils.v1.TimePeriod billing_cycle = 5;
  // 规则
  optional string rules = 6;
  // 预审
  models.subscription.v1.Price.Prequalification prequalification = 7;
}

// CreatePriceResponse
message CreatePriceResponse {
  // 价格信息
  models.subscription.v1.Price price = 1;
}

// ListPricesRequest
message ListPricesRequest {
  // 分页信息
  utils.v2.PaginationRequest pagination = 1;
  // 过滤条件
  message Filter {
    // 产品 ID
    repeated int64 product_ids = 1;
  }
  // 过滤条件
  Filter filter = 2;
  // 买家，用于检查购买资格，非必填
  optional models.subscription.v1.User buyer = 3;
}

// ListPricesResponse
message ListPricesResponse {
  // 分页信息
  utils.v2.PaginationResponse pagination = 1;
  // 价格信息
  repeated models.subscription.v1.Price prices = 2;
}

// CreateFeatureRequest
message CreateFeatureRequest {
  // 特性名称
  string name = 1;
  // 特性描述
  string description = 2;
  // 特性 key
  models.subscription.v1.Feature.Key key = 3;
  // 特性设置
  models.subscription.v1.Feature.Setting setting = 4;
}

// CreateFeatureResponse
message CreateFeatureResponse {
  // 特性信息
  models.subscription.v1.Feature feature = 1;
}

// ListFeaturesRequest
message ListFeaturesRequest {
  // 分页信息
  utils.v2.PaginationRequest pagination = 1;
  // 过滤条件
  message Filter {
    // id
    repeated int64 ids = 1;
  }
  // 过滤条件
  Filter filter = 2;
}

// ListFeaturesResponse
message ListFeaturesResponse {
  // 分页信息
  utils.v2.PaginationResponse pagination = 1;
  // 特性信息
  repeated models.subscription.v1.Feature features = 2;
}

// CreateFeatureRequest
message UpdateFeatureRequest {
  // 特性 ID
  int64 id = 1;
  // 特性设置
  models.subscription.v1.Feature.Setting setting = 2;
}

// CreateFeatureResponse
message UpdateFeatureResponse {
  // 特性信息
  models.subscription.v1.Feature feature = 1;
}

// DeleteFeatureRequest
message DeleteFeatureRequest {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// DeleteFeatureResponse
message DeleteFeatureResponse {}

// ListDiscountsRequest
message ListDiscountsRequest {
  // 分页信息
  utils.v2.PaginationRequest pagination = 1;
  // 过滤条件
  message Filter {
    // ID
    repeated int64 ids = 1;
    // Price ID
    repeated int64 price_ids = 2;
  }
  // 过滤条件
  Filter filter = 2;
}

// ListDiscountsResponse
message ListDiscountsResponse {
  // 分页信息
  utils.v2.PaginationResponse pagination = 1;
  // 优惠信息
  repeated models.subscription.v1.Discount discounts = 2;
}

// ListLicensesRequest
message ListLicensesRequest {
  // 分页信息
  utils.v2.PaginationRequest pagination = 1;
  // 过滤条件
  message Filter {
    // 订阅 ID
    repeated int64 subscription_ids = 1;
    // 用户 ID
    repeated models.subscription.v1.User owners = 2;
    // 状态
    repeated models.subscription.v1.License.Status statuses = 3;
  }
  // 过滤条件
  Filter filter = 2;
}

// ListLicensesResponse
message ListLicensesResponse {
  // 分页信息
  utils.v2.PaginationResponse pagination = 1;
  // 许可证信息
  repeated models.subscription.v1.License licenses = 2;
}

// ListEntitlementsRequest
message ListEntitlementsRequest {
  // 分页信息
  utils.v2.PaginationRequest pagination = 1;
  // 过滤条件
  message Filter {
    // 许可证 ID
    repeated int64 licence_ids = 1;
    // feature key
    repeated models.subscription.v1.Feature.Key feature_keys = 2;
    // feature id
    repeated int64 feature_ids = 3;
    // entitlement id
    repeated int64 entitlement_ids = 4;
  }
  // 过滤条件
  Filter filter = 2;
}

// ListEntitlementsResponse
message ListEntitlementsResponse {
  // 分页信息
  utils.v2.PaginationResponse pagination = 1;
  // 权益信息
  repeated models.subscription.v1.Entitlement entitlements = 2;
}

// UpdateEntitlementsRequest
message UpdateEntitlementsRequest {
  // 需要更新的权益者
  models.subscription.v1.User user = 1;
  // features
  repeated models.subscription.v1.Feature features = 2;
  // related JSON information attached when modifying entitlement
  // when calling upstream, need to serialize the json into a string first
  // this JSON data will be stored directly in the database.
  optional string related = 3;
}

// UpdateEntitlementsResponse
message UpdateEntitlementsResponse {
  // 是否成功。如果为 false，表示没有可用的权益
  bool success = 1;
}

// ListRevisionsRequest
message ListRevisionsRequest {
  // revision id, can empty
  repeated int64 id = 1 [(validate.rules).repeated = {
    items: {
      int64: {gte: 0}
    }
  }];
  // type, can empty
  repeated models.subscription.v1.Revision.Type type = 2;
  // details json
  // for example, if you want to query the revision of a certain customer id
  // then pass "customer_id": "4396"
  map<string, string> detail_keys = 3;
  // page
  utils.v2.PaginationRequest pagination = 4;
}

// ListRevisionsResponse
message ListRevisionsResponse {
  // revisions
  repeated models.subscription.v1.Revision revisions = 1;
  // page
  utils.v2.PaginationResponse pagination = 2;
}

// ConsumeEntitlementsRequest
message ConsumeEntitlementsRequest {
  // 所需要消费的 feature
  message Feature {
    // feature key
    models.subscription.v1.Feature.Key key = 1;
    // setting
    Setting setting = 2;
    // feature id
    optional int64 feature_id = 3;
  }
  // count
  message Count {
    // 需要消费的数量
    int64 amount = 1;
    // 单位
    string unit = 2;
  }
  // setting
  message Setting {
    // setting
    oneof setting {
      // count
      Count count = 1;
    }
  }

  // 消费权益者
  models.subscription.v1.User user = 1;
  // 所需要消费的 feature
  repeated Feature features = 2;
}

// ConsumeEntitlementsResponse
message ConsumeEntitlementsResponse {
  // 是否成功。如果为 false，表示没有可用的权益
  bool success = 1;
}

// ConsumePackageRequest
message ConsumePackagesRequest {
  // 所需要消费的 feature
  message Entitlement {
    // entitlement id
    int64 entitlement_id = 1 [(validate.rules).int64 = {gt: 0}];
    // setting
    Setting setting = 2;
  }

  // count
  message Count {
    // 需要消费的数量
    int64 amount = 1;
    // 单位
    string unit = 2;
  }
  // setting
  message Setting {
    // setting
    oneof setting {
      // count
      Count count = 1;
    }
  }

  // 消费权益者
  models.subscription.v1.User user = 1;
  // 所需要消费的 entitlement
  repeated Entitlement entitlements = 2;
}

// ConsumePackagesResponse
message ConsumePackagesResponse {
  // 是否成功。如果为 false，表示没有可用的权益
  bool success = 1;
}

// ListLastRedeemTimeForCustomerRequest
message ListLastRedeemTimeForCustomerRequest {
  // subscription ids
  repeated int64 subscription_ids = 1;
  // customer ids
  repeated int64 customer_ids = 2;
}

// ListLastRedeemTimeForCustomersResponse
message ListLastRedeemTimeForCustomersResponse {
  // 用户 ID 和最后兑换时间的映射
  message CustomerLastRedeemTime {
    // 用户 ID
    int64 customer_id = 1;
    // 最后兑换时间
    google.protobuf.Timestamp last_redeem_time = 2;
  }
  // 用户 ID 和最后兑换时间的映射
  repeated CustomerLastRedeemTime customer_last_redeem_times = 1;
}

// GetSubscriptionReportRequest
message GetSubscriptionReportRequest {
  // 产品 ID
  int64 product_id = 1;
}

// GetSubscriptionReportResponse
message GetSubscriptionReportResponse {
  // 统计信息
  models.subscription.v1.Report content = 1;
}

// RefreshExistedApplicationFeeRequest
message RefreshExistedApplicationFeeRequest {}

// RefreshExistedApplicationFeeResponse
message RefreshExistedApplicationFeeResponse {}

// CreateLicenseAndEntitlementsRequest
message CreateLicenseAndEntitlementsRequest {
  // subscription id
  int64 subscription_id = 1;
  // product id
  int64 product_id = 2;
  // feature ids
  repeated int64 feature_ids = 3;
  // 买家
  moego.models.subscription.v1.User buyer = 4;
  // license statuses
  moego.models.subscription.v1.License.Status license_status = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// CreateLicenseAndEntitlementsResponse
message CreateLicenseAndEntitlementsResponse {
  //license id
  int64 license_id = 1;
  //entitlement ids
  repeated int64 entitlement_ids = 2;
}

// CheckMembershipsRequest
message CheckMembershipsRequest {}

// CheckMembershipsResponse
message CheckMembershipsResponse {
  // subscriptions
  repeated models.subscription.v1.Subscription subscriptions = 1;
}

// RefreshMembershipsRequest
message RefreshMembershipsRequest {}

// RefreshMembershipsResponse
message RefreshMembershipsResponse {
  // subscriptions
  repeated models.subscription.v1.Subscription subscriptions = 1;
}
