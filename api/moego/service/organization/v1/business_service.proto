syntax = "proto3";

package moego.service.organization.v1;

import "moego/models/business/v1/business_models.proto";
import "moego/models/organization/v1/close_date_defs.proto";
import "moego/models/organization/v1/location_defs.proto";
import "moego/models/organization/v1/location_models.proto";
import "moego/models/organization/v1/payment_method_defs.proto";
import "moego/models/organization/v1/working_hour_defs.proto";
import "moego/utils/v2/list.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1;organizationsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.organization.v1";

// get company id request
message GetCompanyIdRequest {
  // business id
  int64 business_id = 1;
}

// get company id response
message GetCompanyIdResponse {
  // company id
  int64 company_id = 1;
}

// batch get company id request
message BatchGetCompanyIdRequest {
  // business id list
  repeated int64 business_ids = 1 [(validate.rules).repeated = {min_items: 1}];
}

// batch get company id response
message BatchGetCompanyIdResponse {
  // business id to company id
  map<int64, int64> business_company_id_map = 1;
}

// params to create a location
message CreateLocationRequest {
  // location to create
  models.organization.v1.CreateLocationDef location = 1 [(validate.rules).message = {required: true}];
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 token_staff_id = 2 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 3 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // company id from token
  int64 token_company_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// result to create a location
message CreateLocationResponse {
  // location id
  int64 id = 1;
}

// params to update a location
message UpdateLocationRequest {
  // location to update
  models.organization.v1.UpdateLocationDef location = 1 [(validate.rules).message = {required: true}];
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 token_staff_id = 2 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 3 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // company id from token
  int64 token_company_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// result to update a location
message UpdateLocationResponse {
  // update result
  bool success = 1;
}

// params to get location list, currently only support get all
message GetLocationListRequest {
  // company id from token
  int64 token_company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff id, optional
  optional int64 token_staff_id = 2 [(validate.rules).int64 = {gt: 0}];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 3;
}

// result to get location list
message GetLocationListResponse {
  // location list
  repeated models.organization.v1.LocationBriefView location = 1;
  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// params to get location detail
message GetLocationDetailRequest {
  // location id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id from token
  optional int64 token_company_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// result to get location detail
message GetLocationDetailResponse {
  // location detail
  models.organization.v1.LocationModel location = 1;
}

// update online preference request
message UpdateOnlinePreferenceRequest {
  // online preference
  models.organization.v1.UpdateOnlinePreferenceDef online_preference = 1 [(validate.rules).message = {required: true}];
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 token_staff_id = 2 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 3 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // company id from token
  int64 token_company_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// update online preference response
message UpdateOnlinePreferenceResponse {
  // update result
  bool success = 1;
}

// get working hour list request
message GetWorkingHourListRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id from token
  int64 token_company_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get working hour list response
message GetWorkingHourListResponse {
  // working hour list
  models.organization.v1.WorkingHoursDef working_hour = 1;
}

// update working hour request
message UpdateWorkingHourRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // working hour list
  models.organization.v1.WorkingHoursDef working_hour = 2 [(validate.rules).message = {required: true}];
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 token_staff_id = 3 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 4 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // company id from token
  int64 token_company_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// update working hour response
message UpdateWorkingHourResponse {
  // update result
  bool success = 1;
}

// add closed date request
message AddClosedDateRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // closed date
  models.organization.v1.CloseDateDef closed_date = 2 [(validate.rules).message = {required: true}];
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 token_staff_id = 3 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 4 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // company id from token
  int64 token_company_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// add closed date response
message AddClosedDateResponse {
  // closed date id
  int64 id = 1;
}

// update closed date request
message UpdateClosedDateRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // closed date
  models.organization.v1.CloseDateDef closed_date = 2 [(validate.rules).message = {required: true}];
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 token_staff_id = 3 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 4 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // company id from token
  int64 token_company_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// update closed date response
message UpdateClosedDateResponse {
  // update result
  bool success = 1;
}

// delete closed date request
message DeleteClosedDateRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // closed date id
  int64 id = 2 [(validate.rules).int64 = {gt: 0}];
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 token_staff_id = 3 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 4 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // company id from token
  int64 token_company_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// delete closed date response
message DeleteClosedDateResponse {
  // delete result
  bool success = 1;
}

// get closed date list request
message GetClosedDateListRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id from token
  int64 token_company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // year
  optional int32 year = 3 [(validate.rules).int32 = {gt: 0}];
}

// get closed date list response
message GetClosedDateListResponse {
  // closed date list
  repeated models.organization.v1.CloseDateDef closed_date = 1;
}

// get closed holiday list request
message GetClosedHolidayListRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id from token
  int64 token_company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // year
  optional string year = 3 [(validate.rules).string.len = 4];
}

// get closed holiday list response
message GetClosedHolidayListResponse {
  // closed holiday list
  repeated models.organization.v1.CloseDateDef closed_date = 1;
}

// edit closed holiday
message EditClosedHolidayRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 token_staff_id = 2 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 3 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // company id from token
  int64 token_company_id = 4 [(validate.rules).int64 = {gt: 0}];
  // start date
  string start_date = 5 [(validate.rules).string.len = 10];
  // end date
  string end_date = 6 [(validate.rules).string.len = 10];
  // close or open
  bool is_closed = 7;
  // holiday description
  string description = 8;
}

// edit closed holiday response
message EditClosedHolidayResponse {
  // update result
  bool success = 1;
}

// mass edit closed holiday
message MassEditClosedHolidayRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // closed date list
  repeated models.organization.v1.ClosedHolidayMassEditDef closed_holiday = 2;
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 token_staff_id = 3 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 4 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // company id from token
  int64 token_company_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// mass edit closed holiday response
message MassEditClosedHolidayResponse {
  // update result
  bool success = 1;
}

// get working location list by companies staff request
message GetWorkingLocationListByCompaniesStaffRequest {
  // param
  message Param {
    // company id
    int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
    // staff id,use to check permission
    int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
  }
  // param list
  repeated Param params = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 500
  }];
}

// get working location list by companies staff response
message GetWorkingLocationListByCompaniesStaffResponse {
  // location list
  message LocationList {
    // location list
    repeated models.organization.v1.LocationModel locations = 1;
  }
  // company id to location list
  map<int64, LocationList> company_locations_map = 1;
}

// get staff's working location id request
message GetWorkingLocationIdsByStaffIdsRequest {
  // staff ids
  repeated int64 staff_id = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get staff's working location id response
message GetWorkingLocationIdsByStaffIdsResponse {
  // StaffLocationIds
  message StaffLocationIds {
    // staff id
    int64 staff_id = 1;
    // location id list
    repeated int64 location_id = 2;
  }
  // staff id to location id list
  repeated StaffLocationIds staff_location_ids = 1;
}

// get working location current date time request
message GetWorkingLocationDateTimeRequest {
  // business ids
  repeated int64 business_ids = 1;
}

// get working location current date time response
message GetWorkingLocationDateTimeResponse {
  // location_date_time
  repeated models.organization.v1.LocationDateTimeDef location_date_time = 1;
}

// batch get location id list request
message BatchGetLocationIdListRequest {
  // company id list
  repeated int64 company_id = 1 [(validate.rules).repeated.min_items = 1];
}

// batch get location id list response
message BatchGetLocationIdListResponse {
  // company id to location id list
  map<int64, utils.v2.Int64List> company_location_id_list_map = 1;
}

// legacy api, get business info with owner email request
message GetBusinessInfoWithOwnerEmailRequest {
  // business id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// legacy api, get business info with owner email response
message GetBusinessInfoWithOwnerEmailResponse {
  // business model
  models.business.v1.BusinessModel business = 1;
}

// batch get business owner account id request
message BatchGetBusinessOwnerAccountIdRequest {
  // business id list
  repeated int64 business_ids = 1 [(validate.rules).repeated = {unique: true}];
  // get all businesses
  bool get_all_businesses = 2;
}

// batch get business owner account id response
message BatchGetBusinessOwnerAccountIdResponse {
  // business id to owner account id
  map<int64, int64> business_owner_account_id_map = 1;
}

// get location list by enterprise staff request
message GetLocationListByEnterpriseStaffRequest {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64 = {gt: 0}];
  // enterprise id
  int64 enterprise_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get location list by enterprise staff response
message GetLocationListByEnterpriseStaffResponse {
  // location list
  repeated models.organization.v1.LocationModel locations = 1;
}

// batch get business payment method request
message ListPaymentMethodsRequest {
  // company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id list
  repeated int64 business_ids = 2 [(validate.rules).repeated = {unique: true}];
}

// batch get business payment method response
message ListPaymentMethodsResponse {
  // business id to payment method list
  map<int64, models.organization.v1.PaymentMethodListDef> business_payment_methods_map = 1;
}

// list business working hours request
message ListBusinessWorkingHoursRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// list business working hours response
message ListBusinessWorkingHoursResponse {
  // business id to working hours
  map<int64, models.organization.v1.WorkingHoursDef> business_working_hours_map = 1;
}

// list locations
message ListLocationsRequest {
  // filter, each field will be used as AND , under the same field will be used as OR
  message Filter {
    // location ids
    repeated int64 ids = 1 [(validate.rules).repeated = {
      items: {
        int64: {gt: 0}
      }
    }];
    // company ids
    repeated int64 company_ids = 3 [(validate.rules).repeated = {
      items: {
        int64: {gt: 0}
      }
    }];
  }
  // pagination, not set for get all
  utils.v2.PaginationRequest pagination = 1;
  // filter
  Filter filter = 2;
}

// list locations response
message ListLocationsResponse {
  // pagination
  utils.v2.PaginationResponse pagination = 1;
  // location list
  repeated models.organization.v1.LocationModel locations = 2;
}

// business service
service BusinessService {
  // get company id
  rpc GetCompanyId(GetCompanyIdRequest) returns (GetCompanyIdResponse);
  // batch get company id
  rpc BatchGetCompanyId(BatchGetCompanyIdRequest) returns (BatchGetCompanyIdResponse);
  // create location
  rpc CreateLocation(CreateLocationRequest) returns (CreateLocationResponse);
  // update location
  rpc UpdateLocation(UpdateLocationRequest) returns (UpdateLocationResponse);
  // get location list
  rpc GetLocationList(GetLocationListRequest) returns (GetLocationListResponse);
  // get location detail
  rpc GetLocationDetail(GetLocationDetailRequest) returns (GetLocationDetailResponse);
  // update online preference
  rpc UpdateOnlinePreference(UpdateOnlinePreferenceRequest) returns (UpdateOnlinePreferenceResponse);
  // get working hour list
  rpc GetWorkingHourList(GetWorkingHourListRequest) returns (GetWorkingHourListResponse);
  // update working hour
  rpc UpdateWorkingHour(UpdateWorkingHourRequest) returns (UpdateWorkingHourResponse);
  // add closed date
  rpc AddClosedDate(AddClosedDateRequest) returns (AddClosedDateResponse);
  // update closed date
  rpc UpdateClosedDate(UpdateClosedDateRequest) returns (UpdateClosedDateResponse);
  // delete closed date
  rpc DeleteClosedDate(DeleteClosedDateRequest) returns (DeleteClosedDateResponse);
  // get closed date list
  rpc GetClosedDateList(GetClosedDateListRequest) returns (GetClosedDateListResponse);
  // get closed holiday list
  rpc GetClosedHolidayList(GetClosedHolidayListRequest) returns (GetClosedHolidayListResponse);
  // edit closed holiday
  rpc EditClosedHoliday(EditClosedHolidayRequest) returns (EditClosedHolidayResponse);
  // mass edit closed holiday
  rpc MassEditClosedHoliday(MassEditClosedHolidayRequest) returns (MassEditClosedHolidayResponse);
  // get working location list by companies staff
  rpc GetWorkingLocationListByCompaniesStaff(GetWorkingLocationListByCompaniesStaffRequest) returns (GetWorkingLocationListByCompaniesStaffResponse);
  // get staff's working location id
  rpc GetWorkingLocationIdsByStaffIds(GetWorkingLocationIdsByStaffIdsRequest) returns (GetWorkingLocationIdsByStaffIdsResponse);
  // get working location current date time
  rpc GetWorkingLocationDateTime(GetWorkingLocationDateTimeRequest) returns (GetWorkingLocationDateTimeResponse);
  // batch get location id list
  rpc BatchGetLocationIdList(BatchGetLocationIdListRequest) returns (BatchGetLocationIdListResponse);
  // legacy api, get business info with owner email
  rpc GetBusinessInfoWithOwnerEmail(GetBusinessInfoWithOwnerEmailRequest) returns (GetBusinessInfoWithOwnerEmailResponse);
  // batch get business owner account id
  rpc BatchGetBusinessOwnerAccountId(BatchGetBusinessOwnerAccountIdRequest) returns (BatchGetBusinessOwnerAccountIdResponse);
  // get location list by enterprise staff
  rpc GetLocationListByEnterpriseStaff(GetLocationListByEnterpriseStaffRequest) returns (GetLocationListByEnterpriseStaffResponse);
  // batch get business payment methods
  rpc ListPaymentMethods(ListPaymentMethodsRequest) returns (ListPaymentMethodsResponse);
  // list business working hours
  rpc ListBusinessWorkingHours(ListBusinessWorkingHoursRequest) returns (ListBusinessWorkingHoursResponse);
  // list locations
  rpc ListLocations(ListLocationsRequest) returns (ListLocationsResponse);
}
