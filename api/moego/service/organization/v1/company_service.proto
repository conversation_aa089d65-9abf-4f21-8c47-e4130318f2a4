syntax = "proto3";

package moego.service.organization.v1;

import "moego/models/organization/v1/clock_in_out_setting_defs.proto";
import "moego/models/organization/v1/clock_in_out_setting_model.proto";
import "moego/models/organization/v1/company_defs.proto";
import "moego/models/organization/v1/company_models.proto";
import "moego/models/organization/v1/country_defs.proto";
import "moego/models/organization/v1/location_defs.proto";
import "moego/models/organization/v1/location_enums.proto";
import "moego/models/organization/v1/migrate_enums.proto";
import "moego/models/organization/v1/tax_defs.proto";
import "moego/models/organization/v1/tax_models.proto";
import "moego/models/organization/v1/time_zone.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1;organizationsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.organization.v1";

// create company request
message CreateCompanyRequest {
  // account id
  int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];
  // location info
  models.organization.v1.CreateLocationDef location = 2 [(validate.rules).message = {required: true}];
  // how to know us
  optional models.organization.v1.SourceType source = 3;
  // country
  optional models.organization.v1.CountryDef country = 4;
  // time zone
  optional models.organization.v1.TimeZone time_zone = 5;
  // know about us, if source is other, this field is required
  optional string know_about_us = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // phone number
  string phone_number = 7 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // currency code
  string currency_code = 8 [(validate.rules).string.len = 3];
  // currency symbol
  string currency_symbol = 9 [(validate.rules).string.min_len = 1];
  //company type
  optional int32 company_type = 10;
}

// create company response
message CreateCompanyResponse {
  // company id
  int64 company_id = 1;
  // business id
  int64 business_id = 2;
}

// query companies by ids request
message QueryCompaniesByIdsRequest {
  // company id
  repeated int64 company_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 500
    items: {
      int64: {not_in: 0}
    }
  }];
}

// query companies by ids response
message QueryCompaniesByIdsResponse {
  // company id to company model, if company not exist or be deleted, it will not be contained in this map
  map<int64, models.organization.v1.CompanyModel> company_id_to_company = 1;
}

// update company preference setting request
message UpdateCompanyPreferenceSettingRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // preference setting
  models.organization.v1.UpdateCompanyPreferenceSettingDef preference_setting = 2;
  // token staff id
  int64 token_staff_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// update company preference setting response
message UpdateCompanyPreferenceSettingResponse {
  // success or not
  bool success = 1;
}

// get company preference setting request
message GetCompanyPreferenceSettingRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get company preference setting response
message GetCompanyPreferenceSettingResponse {
  // preference setting
  models.organization.v1.CompanyPreferenceSettingModel preference_setting = 1;
}

// is moego pay enable request
message IsMoegoPayEnableRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// is moego pay enable response
message IsMoegoPayEnableResponse {
  // is moego pay enable
  bool is_moego_pay_enable = 1;
}

// is retail enable request
message IsRetailEnableRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id, if not set, will query all business in this company
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// is retail enable response
message IsRetailEnableResponse {
  // is retail enable
  bool is_retail_enable = 1;
}

// add tax rule request
message AddTaxRuleRequest {
  // tax rule
  models.organization.v1.TaxRuleDef tax_rule = 1 [(validate.rules).message = {required: true}];
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 token_staff_id = 2 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 3 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // company id from token
  int64 token_company_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// add tax rule response
message AddTaxRuleResponse {
  // tax rule id
  int64 id = 1;
}

// update tax rule request
message UpdateTaxRuleRequest {
  // tax rule id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // tax rule
  models.organization.v1.TaxRuleDef tax_rule = 2 [(validate.rules).message = {required: true}];
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 token_staff_id = 3 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 4 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // company id from token
  int64 token_company_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// update tax rule response
message UpdateTaxRuleResponse {
  // update result
  bool success = 1;
}

// delete tax rule request
message DeleteTaxRuleRequest {
  // tax rule id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 token_staff_id = 2 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 3 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // company id from token
  int64 token_company_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// delete tax rule response
message DeleteTaxRuleResponse {
  // delete result
  bool success = 1;
}

// get tax rule list request
message GetTaxRuleListRequest {
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 token_staff_id = 1 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 2 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // company id from token
  int64 token_company_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// get tax rule list response
message GetTaxRuleListResponse {
  // tax rule list
  repeated models.organization.v1.TaxRuleModel rule = 1;
}

// get business id for mobile location request
message GetBusinessIdForMobileRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get business id for mobile location response
message GetBusinessIdForMobileResponse {
  // business id
  int64 business_id = 1;
}

// set company migrate status request
message SetCompanyMigrateStatusRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // migrate status
  models.organization.v1.MigrateStatus migrate_status = 2;
}

// set company migrate status response
message SetCompanyMigrateStatusResponse {}

// mark company as migrated response
message MarkCompanyAsMigratedResponse {}

// is company migrate request
message IsCompanyMigrateRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id, 可选 , not support now
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// is company migrate response
message IsCompanyMigrateResponse {
  // is company migrate
  bool is_company_migrate = 1;
}

// is companies migrate request
message IsCompaniesMigrateRequest {
  // company id
  repeated int64 company_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 500
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
}

// is companies migrate response
message IsCompaniesMigrateResponse {
  // is companies migrate
  map<int64, bool> is_companies_migrate_map = 1;
}

// get clock in/out setting request
message GetClockInOutSettingRequest {
  // token company id
  int64 token_company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get clock in out setting response
message GetClockInOutSettingResponse {
  // clock in/out setting
  models.organization.v1.ClockInOutSettingModel clock_in_out_setting = 1;
}

// update clock in/out setting request
message UpdateClockInOutSettingRequest {
  // token company id
  int64 token_company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // token staff id, operator id
  int64 token_staff_id = 2 [(validate.rules).int64 = {gt: 0}];
  // preserved 3-5 for future use

  // update clock in/out setting params
  models.organization.v1.UpdateClockInOutSettingDef clock_in_out_setting = 6 [(validate.rules).message = {required: true}];
}

// update clock in/out setting response
message UpdateClockInOutSettingResponse {
  // update result
  bool success = 1;
}

// list companies by enterprise id request
message ListCompaniesByEnterpriseIdRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 2;
}

// list companies by enterprise id response
message ListCompaniesByEnterpriseIdResponse {
  // company models
  repeated models.organization.v1.CompanyModel companies = 1;
  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

//company question query
message CompanyQuestionRecordQueryRequest {
  //company id
  int64 company_id = 1;
}

//company question result
message CompanyQuestionRecordQueryResponse {
  //is have fill question
  bool is_fill_question = 1;
}

// save company question record
message CompanyQuestionRecordRequest {
  // pets per month
  string pet_per_month = 1 [(validate.rules).string.max_len = 50];
  //total location number
  optional string total_locations = 2 [(validate.rules).string.max_len = 50];
  //total van number
  optional string total_vans = 3 [(validate.rules).string.max_len = 50];
  //move from other software
  string move_from = 4 [(validate.rules).string.max_len = 50];
  //source from
  string source_from = 5 [(validate.rules).string.max_len = 50];
  //other source from
  optional string source_from_other = 6 [(validate.rules).string.max_len = 50];
  // company id
  int64 company_id = 7 [(validate.rules).int64 = {gt: 0}];
}

// save company question record result
message CompanyQuestionRecordResponse {
  // update result
  bool success = 1;
}

// modify company account sorting request
message SortCompanyRequest {
  // account id
  int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];
  // ids
  repeated int64 ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    min_items: 1
    max_items: 255
    unique: true
  }];
}

// modify company account sorting response
message SortCompanyResponse {}

// query company list  request
message ListCompanyRequest {
  //the account id
  optional int64 account_id = 1 [(validate.rules).int64.gt = 0];
  //the company id
  optional int64 company_id = 2 [(validate.rules).int64.gt = 0];
  //the company name
  optional string name = 3;
  //the company country
  optional string country = 4;
  // the enterprise id
  optional int64 enterprise_id = 5 [(validate.rules).int64.gt = 0];

  // the pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// query company list  response
message ListCompanyResponse {
  //company list
  repeated moego.models.organization.v1.CompanyModel companies = 1;

  //pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// qurey company list  request
message UpdateCompanyRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // company name
  optional string name = 2 [(validate.rules).string = {max_len: 255}];
  // enable square
  optional bool enable_square = 3;
  // enable stripe reader
  optional bool enable_stripe_reader = 4;
  // change company account_id
  optional int64 account_id = 5 [(validate.rules).int64.gte = 0];
  // location num
  optional int32 location_num = 6;
  // van num
  optional int32 van_num = 7;
  // enterprise id
  optional int64 enterprise_id = 8 [(validate.rules).int64.gte = 0];
  // level
  optional int32 level = 9 [(validate.rules).int32.gte = 0];
}

// update company list  response
message UpdateCompanyResponse {
  //update result
  bool result = 1;
}

// list company preference setting request
message ListCompanyPreferenceSettingsRequest {
  // company ids
  repeated int64 company_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 500
    items: {
      int64: {gt: 0}
    }
  }];
}

// list company preference setting response
message ListCompanyPreferenceSettingsResponse {
  // company preference setting list
  map<int64, models.organization.v1.CompanyPreferenceSettingModel> company_preference_map = 1;
}

// create company from enterprise hub request
message CreateCompanyFromEnterpriseHubRequest {
  // account id if set it will create a company owner with this account id
  int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];

  // company id
  int64 template_company_id = 2;
  // if first name & last name has value ,should create new own staff which is not bind account id
  optional string email = 3 [(validate.rules).string = {email: true}];
  // first name
  optional string first_name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // last name
  optional string last_name = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];

  // location num
  optional int32 location_num = 6;
  // van num
  optional int32 van_num = 7;

  // phone number
  optional string phone_number = 8 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // enterprise id
  int64 enterprise_id = 9 [(validate.rules).int64.gt = 0];
  // name
  string name = 10;
}

// company service
service CompanyService {
  // create company
  rpc CreateCompany(CreateCompanyRequest) returns (CreateCompanyResponse);
  // query companies by ids
  rpc QueryCompaniesByIds(QueryCompaniesByIdsRequest) returns (QueryCompaniesByIdsResponse);
  // is moego pay enable
  rpc IsMoegoPayEnable(IsMoegoPayEnableRequest) returns (IsMoegoPayEnableResponse);
  // update company preference setting
  rpc UpdateCompanyPreferenceSetting(UpdateCompanyPreferenceSettingRequest) returns (UpdateCompanyPreferenceSettingResponse);
  // get company preference setting
  rpc GetCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest) returns (GetCompanyPreferenceSettingResponse);
  // is retail enable
  rpc IsRetailEnable(IsRetailEnableRequest) returns (IsRetailEnableResponse);
  // add tax rule
  // deprecated, use TaxRuleService.CreateTaxRule instead
  rpc AddTaxRule(AddTaxRuleRequest) returns (AddTaxRuleResponse);
  // update tax rule
  // deprecated, use TaxRuleService.UpdateTaxRule instead
  rpc UpdateTaxRule(UpdateTaxRuleRequest) returns (UpdateTaxRuleResponse);
  // delete tax rule
  // deprecated, use TaxRuleService.DeleteTaxRule instead
  rpc DeleteTaxRule(DeleteTaxRuleRequest) returns (DeleteTaxRuleResponse);
  // get tax rule list
  // deprecated, use TaxRuleService.ListTaxRule instead
  rpc GetTaxRuleList(GetTaxRuleListRequest) returns (GetTaxRuleListResponse);
  // get business id for mobile location
  rpc GetBusinessIdForMobile(GetBusinessIdForMobileRequest) returns (GetBusinessIdForMobileResponse);
  // is company migrate
  rpc IsCompanyMigrate(IsCompanyMigrateRequest) returns (IsCompanyMigrateResponse);
  // get clock in/out setting
  rpc GetClockInOutSetting(GetClockInOutSettingRequest) returns (GetClockInOutSettingResponse);
  // update clock in/out setting
  rpc UpdateClockInOutSetting(UpdateClockInOutSettingRequest) returns (UpdateClockInOutSettingResponse);
  // is companies migrate
  rpc IsCompaniesMigrate(IsCompaniesMigrateRequest) returns (IsCompaniesMigrateResponse);
  // set company migrate status
  rpc SetCompanyMigrateStatus(SetCompanyMigrateStatusRequest) returns (SetCompanyMigrateStatusResponse);
  // list companies by enterprise id
  rpc ListCompaniesByEnterpriseId(ListCompaniesByEnterpriseIdRequest) returns (ListCompaniesByEnterpriseIdResponse);
  //query company question status
  rpc GetCompanyQuestionRecord(CompanyQuestionRecordQueryRequest) returns (CompanyQuestionRecordQueryResponse) {}
  //save company question
  rpc CompanyQuestionRecordSave(CompanyQuestionRecordRequest) returns (CompanyQuestionRecordResponse) {}
  // sort company sorting
  rpc SortCompany(SortCompanyRequest) returns (SortCompanyResponse) {}
  // query company list
  rpc ListCompany(ListCompanyRequest) returns (ListCompanyResponse) {}
  // list company preference setting
  rpc ListCompanyPreferenceSettings(ListCompanyPreferenceSettingsRequest) returns (ListCompanyPreferenceSettingsResponse);
  // update company list
  rpc UpdateCompany(UpdateCompanyRequest) returns (UpdateCompanyResponse) {}
  // create company from enterprise hub
  rpc CreateCompanyFromEnterpriseHub(CreateCompanyFromEnterpriseHubRequest) returns (CreateCompanyResponse) {}
}
