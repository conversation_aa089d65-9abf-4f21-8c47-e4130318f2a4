// @since 2024-03-19 10:44:46
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.service.organization.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1;organizationsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.organization.v1";

// get company current time request
message GetCompanyCurrentDayAndTimeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
}

// get company current time response
message GetCompanyCurrentDayAndTimeResponse {
  // company id
  int64 company_id = 1;
  // current date
  string current_date = 2;
  // current minutes
  int32 current_minutes = 3;
  // local date time
  google.protobuf.Timestamp local_date_time = 4;
}

// date time service
service DateTimeService {
  // get company current time
  rpc GetCompanyCurrentDayAndTime(GetCompanyCurrentDayAndTimeRequest) returns (GetCompanyCurrentDayAndTimeResponse);
}
