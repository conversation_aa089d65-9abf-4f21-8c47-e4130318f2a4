syntax = "proto3";

package moego.service.organization.v1;

import "google/type/latlng.proto";
import "moego/models/organization/v1/staff_tracking.proto";
import "moego/models/organization/v1/tenant.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1;organizationsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.organization.v1";

// upload Staff tracking request
message CreateStaffTrackingRequest {
  // 必填 company_id
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
  // staff id
  int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
  // device id
  string device_id = 3;
  // coordinate, include latitude and longitude
  google.type.LatLng coordinate = 4 [(validate.rules).message.required = true];
}

// upload Staff tracking response
message CreateStaffTrackingResponse {
  //result
  bool result = 1;
}

// list Staff tracking request
message ListStaffTrackingRequest {
  // 必填 company_id
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
  // staff id list
  repeated int64 staff_ids = 2 [(validate.rules).repeated = {min_items: 1}];
}

// list Staff tracking response
message ListStaffTrackingResponse {
  //staff_trackings
  repeated moego.models.organization.v1.StaffTrackingModel staff_trackings = 1;
}

//task clean staff tracking request
message CleanStaffTrackingRequest {}

//task clean staff tracking response
message CleanStaffTrackingResponse {}

// staff tracking service
service StaffTrackingService {
  // upload Staff tracking
  rpc CreateStaffTracking(CreateStaffTrackingRequest) returns (CreateStaffTrackingResponse);
  // Staff tracking list
  rpc ListStaffTracking(ListStaffTrackingRequest) returns (ListStaffTrackingResponse);
  // data cleansing timed tasks
  rpc CleanStaffTracking(CleanStaffTrackingRequest) returns (CleanStaffTrackingResponse);
}
