syntax = "proto3";

package moego.service.activity_log.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/activity_log/v1/activity_log_models.proto";
import "moego/utils/v1/pagination_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/activity_log/v1;activitylogsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.activity_log.v1";

// Search activity logs with pagination request
message SearchActivityLogPageInput {
  // operator id
  repeated string operator_id = 1 [(validate.rules).repeated = {ignore_empty: true}];
  // resource type
  repeated string resource_type = 2 [(validate.rules).repeated = {
    items: {
      string: {
        min_len: 0
        max_len: 100
      }
    }
  }];
  // resource id
  repeated string resource_id = 3 [(validate.rules).repeated = {ignore_empty: true}];
  // action
  repeated string action = 4 [(validate.rules).repeated = {
    items: {
      string: {
        min_len: 0
        max_len: 100
      }
    }
  }];
  // owner id
  repeated string owner_id = 5 [(validate.rules).repeated = {ignore_empty: true}];
  // timestamp (inclusive)
  optional google.protobuf.Timestamp start_time = 6 [(validate.rules).timestamp = {
    gt: {seconds: 0}
  }];
  // timestamp (inclusive)
  optional google.protobuf.Timestamp end_time = 7 [(validate.rules).timestamp = {
    gt: {seconds: 0}
  }];
  // page
  moego.utils.v1.PaginationRequest page = 8 [deprecated = true];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 10;
  // business id
  optional int64 business_id = 9 [(validate.rules).int64 = {gte: 0}];
  // whether to query root activity logs, for backward compatibility, default is true
  optional bool query_root = 11;
}

// Search activity logs with pagination response
message SearchActivityLogPageOutput {
  // activity log list
  repeated moego.models.activity_log.v1.ActivityLogModelSimpleView activity_logs = 1;
  // page
  moego.utils.v1.PaginationResponse page = 2 [deprecated = true];
  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;
}

// Get activity log details request
message GetActivityLogDetailsInput {
  // activity log id
  string id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
}

// Get activity log details response
message GetActivityLogDetailsOutput {
  // current record
  moego.models.activity_log.v1.ActivityLogModel activity_log = 1;
  // affected activity logs
  repeated moego.models.activity_log.v1.ActivityLogModel effects = 2;
}

// Search operators with pagination request
message SearchOperatorPageInput {
  // operator name
  optional string operator_name = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 100
  }];
  // page
  moego.utils.v1.PaginationRequest page = 2 [deprecated = true];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 4;
  // business id
  optional int64 business_id = 3 [(validate.rules).int64 = {gte: 0}];
}

// Search operators with pagination response
message SearchOperatorPageOutput {
  // operator list
  repeated moego.models.activity_log.v1.Operator operators = 1;
  // page
  moego.utils.v1.PaginationResponse page = 2 [deprecated = true];
  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;
}

// Search resources with pagination request
message SearchResourceTypePageInput {
  // resource name
  optional string resource_type = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 100
  }];
  // page
  moego.utils.v1.PaginationRequest page = 2 [deprecated = true];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 4;
  // business id
  optional int64 business_id = 3 [(validate.rules).int64 = {gte: 0}];
}

// Search resources with pagination response
message SearchResourceTypePageOutput {
  // resource list
  repeated string resource_types = 1;
  // page
  moego.utils.v1.PaginationResponse page = 2 [deprecated = true];
  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;
}

// Search actions with pagination request
message SearchActionPageInput {
  // action
  optional string action = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 100
  }];
  // page
  moego.utils.v1.PaginationRequest page = 2 [deprecated = true];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 4;
  // business id
  optional int64 business_id = 3 [(validate.rules).int64 = {gte: 0}];
}

// Search actions with pagination response
message SearchActionPageOutput {
  // action list
  repeated string actions = 1;
  // page
  moego.utils.v1.PaginationResponse page = 2 [deprecated = true];
  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;
}

// Search owners with pagination request
message SearchOwnerPageInput {
  // owner name
  optional string owner_name = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 100
  }];
  // page
  moego.utils.v1.PaginationRequest page = 2 [deprecated = true];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 4;
  // business id
  optional int64 business_id = 3 [(validate.rules).int64 = {gte: 0}];
}

// Search owners with pagination response
message SearchOwnerPageOutput {
  // owner list
  repeated moego.models.activity_log.v1.Owner owners = 1;
  // page
  moego.utils.v1.PaginationResponse page = 2 [deprecated = true];
  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;
}

// List activity logs details request
message ListActivityLogDetailsRequest {
  // activity log ids
  repeated string ids = 1 [(validate.rules).repeated = {
    items: {
      string: {
        min_len: 1
        max_len: 50
      }
    }
  }];
  // whether to query root activity logs, for backward compatibility, default is true
  optional bool query_root = 2;
}

// List activity logs details response
message ListActivityLogDetailsResponse {
  // activity log list
  repeated moego.models.activity_log.v1.ActivityLogModel activity_logs = 1;
}

// Create ActivityLog request
message CreateActivityLogRequest {
  // company id 和 business id 二选一，或者都存在
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id 和 company id 二选一，或者都存在
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // operator id, 暂时只支持 staff id
  optional string operator_id = 3 [(validate.rules).string = {max_len: 200}];
  // time
  google.protobuf.Timestamp time = 4;
  // is root
  bool is_root = 5;
  // action
  string action = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // resource type
  moego.models.activity_log.v1.Resource.Type resource_type = 7 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // resource id
  optional string resource_id = 8 [(validate.rules).string = {max_len: 1000}];
  // JSON string
  optional string details = 9 [(validate.rules).string = {max_len: 100000}];
  // class name，历史字段，只用于 Java
  optional string class_name = 10 [(validate.rules).string = {max_len: 200}];
}

// Create ActivityLog response
message CreateActivityLogResponse {}

// Activity log service
service ActivityLogService {
  // Create an Activity Log
  rpc CreateActivityLog(CreateActivityLogRequest) returns (CreateActivityLogResponse);

  // Search activity logs with pagination
  rpc SearchActivityLogPage(SearchActivityLogPageInput) returns (SearchActivityLogPageOutput);

  // Get activity log details
  rpc GetActivityLogDetails(GetActivityLogDetailsInput) returns (GetActivityLogDetailsOutput);

  // List activity logs details, not include affected activity logs
  rpc ListActivityLogDetails(ListActivityLogDetailsRequest) returns (ListActivityLogDetailsResponse);

  // Search operators with pagination
  rpc SearchOperatorPage(SearchOperatorPageInput) returns (SearchOperatorPageOutput);

  // Search resource types with pagination
  rpc SearchResourceTypePage(SearchResourceTypePageInput) returns (SearchResourceTypePageOutput);

  // Search actions with pagination
  rpc SearchActionPage(SearchActionPageInput) returns (SearchActionPageOutput);

  // Search owners with pagination
  rpc SearchOwnerPage(SearchOwnerPageInput) returns (SearchOwnerPageOutput);
}
