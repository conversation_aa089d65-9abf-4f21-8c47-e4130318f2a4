syntax = "proto3";

package moego.service.promotion.v1;

import "google/type/money.proto";
import "moego/models/promotion/v1/coupon.proto";
import "moego/models/promotion/v1/promotion.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/promotion/v1;promotionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.promotion.v1";

// PromotionService is the service for promotion.
service PromotionService {
  // recommend coupons
  rpc RecommendCoupons(RecommendCouponsRequest) returns (RecommendCouponsResponse) {}
  // preview coupons
  rpc PreviewCoupons(PreviewCouponsRequest) returns (PreviewCouponsResponse) {}
  // redeem coupons
  rpc RedeemCoupons(RedeemCouponsRequest) returns (RedeemCouponsResponse) {}
  // refund coupons
  rpc RefundCoupons(RefundCouponsRequest) returns (RefundCouponsResponse) {}
  // search coupons
  rpc SearchCoupons(SearchCouponsRequest) returns (SearchCouponsResponse) {}
}

// recommend coupons request
message RecommendCouponsRequest {
  // targets 优惠券目标项目列表
  repeated models.promotion.v1.CouponApplicationTarget targets = 1;
  // customer id
  int64 customer_id = 2;
  // search condition
  models.promotion.v1.CouponSearchCondition search_condition = 3;
}

// recommend coupons response
message RecommendCouponsResponse {
  // recommended coupons 推荐的优惠券
  repeated models.promotion.v1.CouponUsage recommended_coupons = 1;
  // all valid coupons 所有可用的优惠券
  repeated models.promotion.v1.Coupon available_coupons = 2;
  // estimated_discount_amount 预估优惠金额
  google.type.Money estimated_discount_amount = 3;
  // unavailable_coupons 不可用的优惠券
  repeated models.promotion.v1.Coupon unavailable_coupons = 4;
}

// preview coupons request
message PreviewCouponsRequest {
  // targets 优惠券目标项目列表
  repeated models.promotion.v1.CouponUsage coupon_usages = 1;
  // customer_id 客户ID
  int64 customer_id = 2;
}

// PreviewCouponsResponse 预览优惠券响应
message PreviewCouponsResponse {
  // deductions 目标抵扣结果列表
  repeated models.promotion.v1.TargetDeduction deductions = 1;
  // total_discount_amount 总优惠金额
  google.type.Money total_discount_amount = 2;
}

// redeem coupons request
message RedeemCouponsRequest {
  // order id
  int64 order_id = 1;
  // coupon redeem details
  repeated models.promotion.v1.CouponRedeem redeems = 2;
  // customer id
  int64 customer_id = 3;
  // scene 场景
  oneof redeem_scene {
    // 订单场景
    int64 appointment_id = 4;
  }
  // 订单收入 -- discount 核销使用
  google.type.Money order_sales = 15;
}

// redeem coupons response
message RedeemCouponsResponse {
  // revision 核销产生的记录
  //  repeated models.promotion.v1.CouponRevision revisions = 1;
}

// refund coupons request
message RefundCouponsRequest {
  // 幂等 key
  string idempotence_key = 1;
  // 需要退款的核销记录
  repeated models.promotion.v1.CouponRevision revisions = 2;
}

// refund coupons response
message RefundCouponsResponse {
  // 生成的退款记录
  repeated models.promotion.v1.CouponRevision revisions = 1;
}

// search coupons request
message SearchCouponsRequest {
  // search condition
  models.promotion.v1.CouponSearchCondition search_condition = 1;
}

// search coupons response
message SearchCouponsResponse {
  // 搜索结果
  repeated models.promotion.v1.Coupon coupons = 1;
}
