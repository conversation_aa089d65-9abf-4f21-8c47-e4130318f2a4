syntax = "proto3";

package moego.service.google_partner.v1;

import "google/protobuf/empty.proto";
import "moego/models/google_partner/v1/google_reserve_integration_enums.proto";
import "moego/models/google_partner/v1/google_reserve_integration_models.proto";
import "validate/validate.proto";

// TODO(Freeman): change to googlePartnerServiceV1
option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/google_partner/v1;googlepartnersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.google_partner.v1";

// update Google reserve integration request param
message UpdateGoogleReserveIntegrationRequest {
  // business id
  int32 business_id = 1 [(validate.rules).int32 = {gt: 0}];
  // enable
  optional bool enabled = 2;
  // status
  optional moego.models.google_partner.v1.GoogleReserveIntegrationStatus status = 3;
}

// get Google reserve integration request param
message GetGoogleReserveIntegrationRequest {
  // business id
  int32 business_id = 1 [(validate.rules).int32 = {gt: 0}];
}

// insert Google reserve integration request param
message InsertGoogleReserveIntegrationRequest {
  // business id
  int32 business_id = 1 [(validate.rules).int32 = {gt: 0}];
  // enable
  optional bool enabled = 2;
  // status
  optional moego.models.google_partner.v1.GoogleReserveIntegrationStatus status = 3;
}

// refresh Google reserve integration status request param
message RefreshGoogleReserveIntegrationStatusRequest {}

// refresh Google reserve integration status response
message RefreshGoogleReserveIntegrationStatusResponse {
  // unmatched business ids
  repeated int32 unmatched_business_ids = 1;
  // matched business ids
  repeated int32 matched_business_ids = 2;
}

// list Google reserve integration response
message ListGoogleReserveIntegrationResponse {
  // Google reserve integration list
  repeated moego.models.google_partner.v1.GoogleReserveIntegrationModel models = 1;
}

// delete Google reserve integration request param
message DeleteGoogleReserveIntegrationRequest {
  // business id
  repeated int32 business_id = 1 [(validate.rules).repeated = {
    items: {
      int32: {gt: 0}
    }
  }];
}

// delete Google reserve integration response
message DeleteGoogleReserveIntegrationResponse {
  // count
  int32 count = 1;
}

// Google reserve integration service
service GoogleReserveIntegrationService {
  // get Google reserve integration
  // if not found, throw NOT_FOUND(5)
  rpc GetGoogleReserveIntegration(GetGoogleReserveIntegrationRequest) returns (moego.models.google_partner.v1.GoogleReserveIntegrationModel) {}
  // insert Google reserve integration
  rpc InsertGoogleReserveIntegration(InsertGoogleReserveIntegrationRequest) returns (moego.models.google_partner.v1.GoogleReserveIntegrationModel) {}
  // update Google reserve integration
  rpc UpdateGoogleReserveIntegration(UpdateGoogleReserveIntegrationRequest) returns (moego.models.google_partner.v1.GoogleReserveIntegrationModel) {}
  // refresh Google reserve integration status by Querying Merchant Status via the API
  // see https://developers.google.com/actions-center/verticals/appointments/redirect/partner-portal/inventory/merchant-status-query
  rpc RefreshGoogleReserveIntegrationStatus(RefreshGoogleReserveIntegrationStatusRequest) returns (RefreshGoogleReserveIntegrationStatusResponse) {}
  // list Google reserve integration
  rpc ListGoogleReserveIntegration(google.protobuf.Empty) returns (ListGoogleReserveIntegrationResponse) {}
  // delete Google reserve integration
  rpc DeleteGoogleReserveIntegration(DeleteGoogleReserveIntegrationRequest) returns (DeleteGoogleReserveIntegrationResponse) {}
}
