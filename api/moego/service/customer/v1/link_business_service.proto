syntax = "proto3";

package moego.service.customer.v1;

import "google/protobuf/empty.proto";
import "moego/models/business/v1/business_models.proto";
import "moego/models/message/v1/business_twilio_model.proto";
import "moego/models/online_booking/v1/business_ob_config_models.proto";
import "moego/models/online_booking/v1/business_ob_gallery_models.proto";
import "moego/models/online_booking/v1/business_ob_profile_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/customer/v1;customersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.customer.v1";

// client portal link business output
message ClientPortalLinkBusinessOutput {
  // business info
  moego.models.business.v1.BusinessModel business_info = 1;
  // online booking profile
  moego.models.online_booking.v1.BusinessOBClientPortalView ob_profile = 2;
  // online booking gallery first image
  moego.models.online_booking.v1.BusinessOBGalleryClientPortalView ob_gallery_first_image = 3;
  // online booking config
  moego.models.online_booking.v1.BusinessOBConfigClientPortalView ob_config = 4;
  // business twilio number
  moego.models.message.v1.BusinessTwilioNumberView business_twilio = 5;
}

// client portal link business list output
message ClientPortalLinkBusinessListOutput {
  // link business list
  repeated ClientPortalLinkBusinessOutput link_business = 1;
}

// link customer input
message LinkCustomerInput {
  // customer id
  int64 customer_id = 1 [(validate.rules).int64 = {
    ignore_empty: true
    gt: 0
  }];
  // account id
  int64 account_id = 2 [(validate.rules).int64 = {gt: 0}];
  // customer code
  string customer_code = 3 [(validate.rules).string = {
    ignore_empty: true
    max_len: 10
  }];
}

// get link business input
message GetLinkBusinessInput {
  // account id
  int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];
}

//  link service
service LinkBusinessService {
  // link customer
  rpc LinkCustomerAndPet(LinkCustomerInput) returns (google.protobuf.Empty);
  // get link business
  rpc GetLinkBusiness(GetLinkBusinessInput) returns (ClientPortalLinkBusinessListOutput);
}
