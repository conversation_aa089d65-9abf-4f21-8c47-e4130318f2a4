// @since 2022-07-04 09:59:57
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.customer.v1;

import "moego/models/customer/v1/customer_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/customer/v1;customersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.customer.v1";

// get customer request
message GetCustomerRequest {
  // business id
  int64 business_id = 1;
  // customer id
  int64 customer_id = 2;
}

// query customer input
message GetCustomerListRequest {
  // business id is required
  int64 business_id = 1;
  // id set
  repeated int64 ids = 2;
}

// query customer name view response
message GetCustomerNameViewListResponse {
  // customer name view list
  repeated moego.models.customer.v1.CustomerModelNameView customer_name_view = 1;
}

// customer service
service CustomerService {
  // query customer name view list
  rpc GetCustomerNameViewList(GetCustomerListRequest) returns (GetCustomerNameViewListResponse);

  // get customer model
  rpc GetCustomer(GetCustomerRequest) returns (moego.models.customer.v1.CustomerModel);
}
