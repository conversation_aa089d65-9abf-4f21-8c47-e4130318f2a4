// @since 2023-06-30 19:20:37
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.llm.v1;

import "moego/models/llm/v1/conversation_defs.proto";
import "moego/models/llm/v1/conversation_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/llm/v1;llmsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.llm.v1";

// the request for complete a conversation
message CompleteConversationRequest {
  // the application, for ai assistant, should be 'ai-assistant'
  string application = 1 [(validate.rules).string = {
    in: ["ai-assistant"]
  }];
  // the conversation id
  int64 conversation_id = 2 [(validate.rules).int64 = {gt: 0}];
  // the temperature, default is 1
  optional double temperature = 3 [(validate.rules).double = {
    gte: 0
    lte: 2
  }];
  // the messages
  repeated moego.models.llm.v1.ConversationMessageDef messages = 4 [(validate.rules).repeated = {
    min_items: 1
    max_items: 50
  }];
}

// the conversation service
service ConversationService {
  // complete a conversation
  rpc CompleteConversation(CompleteConversationRequest) returns (moego.models.llm.v1.ConversationCompletionModel);
}
