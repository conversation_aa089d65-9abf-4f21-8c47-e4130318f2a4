syntax = "proto3";

package moego.service.open_platform.v1;

import "moego/models/open_platform/v1/obfuscate_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/open_platform/v1;openplatformsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.open_platform.v1";

// ObfuscateService
service ObfuscateService {
  // batch encode id
  rpc BatchEncodeID(BatchEncodeIDRequest) returns (BatchEncodeIDResponse) {}
  // batch decode id
  rpc BatchDecodeID(BatchDecodeIDRequest) returns (BatchDecodeIDResponse) {}
}

// batch encode request
message BatchEncodeIDRequest {
  // case and id
  message Param {
    // id
    int64 id = 1 [(validate.rules).int64 = {gt: 0}];
    // case
    moego.models.open_platform.v1.ObfuscateCase case = 2 [(validate.rules).enum = {not_in: 0}];
  }
  // params
  repeated Param params = 1 [(validate.rules).repeated = {min_items: 1}];
}

// batch encode response
message BatchEncodeIDResponse {
  // results
  message Result {
    // id
    int64 id = 1;
    // encoded id, if case is not supported, encoded_id will be empty
    optional string encoded_id = 2;
    // case
    moego.models.open_platform.v1.ObfuscateCase case = 3;
  }
  // results
  repeated Result results = 1;
}

// batch decode request
message BatchDecodeIDRequest {
  // param
  message Param {
    // encoded id
    string encoded_id = 1 [(validate.rules).string = {min_len: 1}];
    // case
    moego.models.open_platform.v1.ObfuscateCase case = 2 [(validate.rules).enum = {not_in: 0}];
  }
  // params
  repeated Param params = 1 [(validate.rules).repeated = {min_items: 1}];
}

// batch decode response
message BatchDecodeIDResponse {
  // results
  message Result {
    // id, if encoded_id is not valid, id will be empty
    optional int64 id = 1;
    // encoded id
    string encoded_id = 2;
    // case
    moego.models.open_platform.v1.ObfuscateCase case = 3;
  }
  // results
  repeated Result results = 1;
}
