syntax = "proto3";

package moego.service.open_platform.v1;

import "moego/models/open_platform/v1/oauth_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/open_platform/v1;openplatformsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.open_platform.v1";

// OAuthService is a service for authorization, authentication, and oauth user information management.
service OAuthService {
  // Authenticate checks the validity of the given API key or OAuth2.0 access token.
  rpc Authenticate(AuthenticateRequest) returns (AuthenticateResponse) {}
  // CreateAPIKey creates a new API key for the given user.
  rpc CreateAPIKey(CreateAPIKeyRequest) returns (CreateAPIKeyResponse) {}
  // DeleteAPIKey deletes the given API key.
  rpc DeleteAPIKey(DeleteAPIKeyRequest) returns (DeleteAPIKeyResponse) {}
}

// AuthenticateRequest
message AuthenticateRequest {
  // credential is the API key or OAuth2.0 access token.
  oneof credential {
    option (validate.required) = true;
    // apikey
    string apikey = 1 [(validate.rules).string = {
      min_len: 1
      max_len: 64
      ignore_empty: true
    }];
    // oauth credential
    OAuthCredential oauth = 3;
    // oauth2 callback code
    string code = 4;
  }

  // session info
  SessionInfo session_info = 100;
}

// Session Info
message SessionInfo {
  // company_id
  int64 company_id = 10;
  // business_id
  int64 business_id = 11;
  // account_id
  int64 account_id = 12;
}

// OAuthCredential
message OAuthCredential {
  // client_id is the unique identifier of the client.
  string client_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 32
  }];
  // open_id is the unique identifier of the user.
  string open_id = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 32
    ignore_empty: true
  }];
  // access_token is the OAuth2.0 access token.
  string access_token = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
}

// AuthenticateResponse
message AuthenticateResponse {
  // user is the user information.
  moego.models.open_platform.v1.User user = 1;
  // scopes is the list of scopes that the user has.
  repeated string scopes = 2;
  // restrictions is the restrictions of the Credential.
  moego.models.open_platform.v1.Restrictions restrictions = 3;
}

// CreateAPIKeyRequest
message CreateAPIKeyRequest {
  // name is the name of the API key.
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }];
  // restrictions is the restrictions of the API key.
  optional moego.models.open_platform.v1.Restrictions restrictions = 2;
  // enterprise_id is the unique identifier of the enterprise.
  // 临时参数，仅用于内部初始化数据
  int64 enterprise_id = 10 [(validate.rules).int64 = {gt: 0}];
}

// CreateAPIKeyResponse
message CreateAPIKeyResponse {
  // api_key is the created API key.
  moego.models.open_platform.v1.APIKey api_key = 1;
}

// DeleteAPIKeyRequest
message DeleteAPIKeyRequest {
  // id is the unique identifier of the API key.
  string id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 32
  }];
}

// DeleteAPIKeyResponse
message DeleteAPIKeyResponse {}
