syntax = "proto3";

package moego.service.open_platform.v1;

import "google/type/interval.proto";
import "moego/models/event_bus/v1/event_defs.proto";
import "moego/models/open_platform/v1/oauth_models.proto";
import "moego/models/open_platform/v1/webhook_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/open_platform/v1;openplatformsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.open_platform.v1";

// WebhookService is a service for managing webhook configurations and event deliveries.
service WebhookService {
  // CreateWebhook creates a new webhook configuration.
  rpc CreateWebhook(CreateWebhookRequest) returns (CreateWebhookResponse);

  // GetWebhook retrieves a webhook by its ID.
  rpc GetWebhook(GetWebhookRequest) returns (GetWebhookResponse);

  // UpdateWebhook updates an existing webhook configuration.
  rpc UpdateWebhook(UpdateWebhookRequest) returns (UpdateWebhookResponse);

  // DeleteWebhook deletes a webhook configuration.
  rpc DeleteWebhook(DeleteWebhookRequest) returns (DeleteWebhookResponse);

  // ListWebhooks lists all webhooks associated with an enterprise and client.
  rpc ListWebhooks(ListWebhooksRequest) returns (ListWebhooksResponse);

  // ListWebhooksByEvent lists webhooks that subscribe to a specific event.
  rpc ListWebhooksByEvent(ListWebhooksByEventRequest) returns (ListWebhooksByEventResponse);

  // ListWebhooksByClient lists webhooks associated with a specific client.
  rpc ListWebhooksByClient(ListWebhooksByClientRequest) returns (ListWebhooksByClientResponse);

  // GetWebhookDelivery retrieves a specific webhook event delivery log.
  rpc GetWebhookDelivery(GetWebhookDeliveryRequest) returns (GetWebhookDeliveryResponse);

  // ListWebhookDeliveries lists all event deliveries for a given webhook.
  rpc ListWebhookDeliveries(ListWebhookDeliveriesRequest) returns (ListWebhookDeliveriesResponse);

  // BatchUpsertWebhookDeliveries creates or updates multiple webhook delivery logs in bulk.
  rpc BatchUpsertWebhookDeliveries(BatchUpsertWebhookDeliveriesRequest) returns (BatchUpsertWebhookDeliveriesResponse);

  // Quota-related methods
  // GetWebhookQuota
  rpc GetWebhookQuota(GetWebhookQuotaRequest) returns (GetWebhookQuotaResponse);
  // UpdateWebhookQuota
  rpc UpdateWebhookQuota(UpdateWebhookQuotaRequest) returns (UpdateWebhookQuotaResponse);

  // ListWebhooksAndQuotas lists all webhooks and quotas.
  rpc ListWebhooksAndQuotas(ListWebhooksAndQuotasRequest) returns (ListWebhooksAndQuotasResponse);
}

// CreateWebhookRequest
message CreateWebhookRequest {
  // enterprise_id is the ID of the enterprise owning the webhook.
  int64 enterprise_id = 1 [(validate.rules).int64.gt = 0];

  // client_id is the ID of the associated client.
  string client_id = 2 [(validate.rules).string.min_len = 1];

  // endpoint_url is the URL where the webhook will be delivered.
  string endpoint_url = 3 [(validate.rules).string.uri = true];

  // event_types is the list of event types this webhook subscribes to.
  // If it is empty, no events will be subscribed to
  repeated models.event_bus.v1.EventType event_types = 4 [(validate.rules).repeated.min_items = 1];

  // List of organizations the webhook is subscribed to.
  // If empty, the webhook is subscribed to all organizations.
  repeated moego.models.open_platform.v1.Organization organizations = 5;

  // secret_token is an optional HMAC token used to sign payloads.
  optional string secret_token = 6;

  // content_type specifies the Content-Type header when delivering payloads.
  moego.models.open_platform.v1.Webhook.ContentType content_type = 7;

  // is_active indicates whether the webhook is currently active.
  optional bool is_active = 8;

  // Whether to verify SSL certificates when delivering payloads.
  // Default: true (recommended for security)
  optional bool verify_ssl = 9;

  // Custom HTTP headers to include when delivering payloads.
  map<string, moego.models.open_platform.v1.Webhook.HeaderValues> headers = 10;
}

// CreateWebhookResponse
message CreateWebhookResponse {
  // webhook is the created webhook configuration.
  moego.models.open_platform.v1.Webhook webhook = 1;
}

// GetWebhookRequest
message GetWebhookRequest {
  // id is the unique identifier of the webhook.
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// GetWebhookResponse
message GetWebhookResponse {
  // webhook is the retrieved webhook configuration.
  moego.models.open_platform.v1.Webhook webhook = 1;
}

// UpdateWebhookRequest
message UpdateWebhookRequest {
  // id is the unique identifier of the webhook.
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // endpoint_url is the URL where the webhook will be delivered.
  string endpoint_url = 2 [(validate.rules).string.uri = true];

  // event_types is the list of event types this webhook subscribes to.
  repeated models.event_bus.v1.EventType event_types = 3 [(validate.rules).repeated.min_items = 1];

  // List of organizations the webhook is subscribed to.
  // If empty, the webhook is subscribed to all organizations.
  repeated moego.models.open_platform.v1.Organization organizations = 4;

  // secret_token is an optional HMAC token used to sign payloads.
  optional string secret_token = 5;

  // content_type specifies the Content-Type header when delivering payloads.
  moego.models.open_platform.v1.Webhook.ContentType content_type = 6;

  // is_active indicates whether the webhook is currently active.
  optional bool is_active = 7;

  // Whether to verify SSL certificates when delivering payloads.
  // Default: true (recommended for security)
  optional bool verify_ssl = 8;

  // Custom HTTP headers to include when delivering payloads.
  map<string, moego.models.open_platform.v1.Webhook.HeaderValues> headers = 9;
}

// UpdateWebhookResponse
message UpdateWebhookResponse {
  // webhook is the updated webhook configuration.
  moego.models.open_platform.v1.Webhook webhook = 1;
}

// DeleteWebhookRequest
message DeleteWebhookRequest {
  // id is the unique identifier of the webhook.
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// DeleteWebhookResponse
message DeleteWebhookResponse {}

// ListWebhooksRequest
message ListWebhooksRequest {
  // enterprise_id filters webhooks by enterprise.
  int64 enterprise_id = 1 [(validate.rules).int64.gt = 0];

  // client_id filters webhooks by client.
  optional string client_id = 2 [(validate.rules).string.min_len = 1];

  // Filter
  message Filter {
    // filter by active status
    optional bool is_active = 1;
    // filter by event types (corrected validation)
    repeated models.event_bus.v1.EventType event_types = 2 [
      (validate.rules).repeated.min_items = 0, // 可选：设置最小数量
      (validate.rules).repeated.items.enum.defined_only = true // 对每个元素进行 enum 校验
    ];
    //  filter by created time
    google.type.Interval created_time = 3;
    //  filter by updated time
    google.type.Interval updated_time = 4;
  }

  // filter options for webhooks
  optional Filter filter = 3;
  // order by
  optional moego.utils.v2.OrderBy order_by = 4;

  // pagination options
  moego.utils.v2.PaginationRequest pagination = 5;
}

// ListWebhooksResponse
message ListWebhooksResponse {
  // webhooks is the list of matching webhook configurations.
  repeated moego.models.open_platform.v1.Webhook webhooks = 1;

  // pagination metadata
  moego.utils.v2.PaginationResponse pagination = 2;
}

// ListWebhooksByEventRequest defines the request for listing webhooks by event name.
message ListWebhooksByEventRequest {
  // enterprise_id filters webhooks by enterprise.
  optional int64 enterprise_id = 1 [(validate.rules).int64.gte = 0];

  // event_type is the event type to filter webhooks.
  models.event_bus.v1.EventType event_type = 2 [(validate.rules).enum.defined_only = true];
}

// ListWebhooksByEventResponse defines the response for listing webhooks by event name.
message ListWebhooksByEventResponse {
  // webhooks is the list of matching webhook configurations.
  repeated moego.models.open_platform.v1.Webhook webhooks = 1;
}

// ListWebhooksByClientRequest defines the request for listing webhooks by client ID.
message ListWebhooksByClientRequest {
  // enterprise_id filters webhooks by enterprise.
  int64 enterprise_id = 1 [(validate.rules).int64.gt = 0];

  // client_id is the ID of the associated client.
  string client_id = 2 [(validate.rules).string.min_len = 1];
}

// ListWebhooksByClientResponse defines the response for listing webhooks by client ID.
message ListWebhooksByClientResponse {
  // webhooks is the list of matching webhook configurations.
  repeated moego.models.open_platform.v1.Webhook webhooks = 1;
}

// GetWebhookDeliveryRequest
message GetWebhookDeliveryRequest {
  // id is the unique identifier of the webhook event delivery.
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// GetWebhookDeliveryResponse
message GetWebhookDeliveryResponse {
  // delivery is the retrieved webhook event delivery log.
  moego.models.open_platform.v1.WebhookDelivery delivery = 1;
}

// ListWebhookDeliveriesRequest
message ListWebhookDeliveriesRequest {
  // webhook_id filters deliveries by the webhook.
  int64 webhook_id = 1 [(validate.rules).int64.gt = 0];

  // Filter defines advanced filtering options
  message Filter {
    // filter by event types (corrected validation)
    repeated models.event_bus.v1.EventType event_types = 1 [
      (validate.rules).repeated.min_items = 0,
      (validate.rules).repeated.items.enum.defined_only = true
    ];
    // filter by success status
    optional bool success = 2;

    // filter by delivery time range
    google.type.Interval delivery_time = 3;
  }

  // filter options for deliveries
  optional Filter filter = 2;

  // order by field
  optional moego.utils.v2.OrderBy order_by = 3;

  // pagination options
  moego.utils.v2.PaginationRequest pagination = 4;
}

// ListWebhookDeliveriesResponse
message ListWebhookDeliveriesResponse {
  // deliveries is the list of matching webhook event deliveries.
  repeated moego.models.open_platform.v1.WebhookDelivery deliveries = 1;

  // pagination metadata
  moego.utils.v2.PaginationResponse pagination = 2;
}

// BatchUpsertWebhookDeliveriesRequest defines the request for bulk creation or update of webhook deliveries.
message BatchUpsertWebhookDeliveriesRequest {
  // List of webhook deliveries to upsert.
  repeated moego.models.open_platform.v1.WebhookDelivery deliveries = 1 [
    (validate.rules).repeated.min_items = 1,
    (validate.rules).repeated.items.message.required = true
  ];
}

// BatchUpsertWebhookDeliveriesResponse returns the result of a batch operation.
message BatchUpsertWebhookDeliveriesResponse {
  // Number of successfully processed deliveries.
  int32 success_count = 1;

  // Optional list of errors (if any).
  repeated string errors = 2;

  // List of deliveries that were processed successfully.
  repeated moego.models.open_platform.v1.WebhookDelivery deliveries = 3;
}

// GetWebhookQuotaRequest
message GetWebhookQuotaRequest {
  // client_id filters quota by the client.
  string client_id = 1;
}

// GetWebhookQuotaResponse
message GetWebhookQuotaResponse {
  // quota is the retrieved webhook quota configuration.
  moego.models.open_platform.v1.WebhookQuotaConfig quota = 1;
}

// UpdateWebhookQuotaRequest
message UpdateWebhookQuotaRequest {
  // client_id filters quota by the client.
  string client_id = 1;
  // Maximum number of webhooks allowed for this client.
  int32 max_webhooks = 2;
  // Maximum number of days to retain delivery logs for this client.
  int32 max_delivery_retention_days = 3;
  // Maximum number of push events allowed per minute for this client.
  int32 max_push_per_minute = 4;
  // Maximum number of push events allowed per day for this client.
  int32 max_push_per_day = 5;
  // Maximum number of push events allowed per month for this client.
  int32 max_push_per_month = 6;
}

// UpdateWebhookQuotaResponse
message UpdateWebhookQuotaResponse {
  // quota is the retrieved webhook quota configuration.
  moego.models.open_platform.v1.WebhookQuotaConfig quota = 1;
}

// ListWebhooksAndQuotasRequest
message ListWebhooksAndQuotasRequest {
  // enterprise_id filters webhooks by enterprise.
  int64 enterprise_id = 1 [(validate.rules).int64.gt = 0];

  // client_ids filters webhooks by client.
  repeated string client_ids = 2;
}

// ListWebhooksAndQuotasResponse
message ListWebhooksAndQuotasResponse {
  // webhooks is the list of webhooks.
  repeated moego.models.open_platform.v1.Webhook webhooks = 1;
  // quotas is the list of webhook quotas.
  repeated moego.models.open_platform.v1.WebhookQuotaConfig quotas = 2;
}
