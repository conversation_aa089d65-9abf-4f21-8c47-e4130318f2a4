syntax = "proto3";

package moego.service.notification.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/notification/v1/notification_defs.proto";
import "moego/models/notification/v1/notification_enums.proto";
import "moego/models/notification/v1/notification_extra_defs.proto";
import "moego/models/notification/v1/notification_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/notification/v1;notificationsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.notification.v1";

// create inbox notification request
message CreateInboxNotificationRequest {
  // notification source
  models.notification.v1.NotificationSource source = 2 [(validate.rules).enum = {defined_only: true}];
  // sender id
  int64 sender_id = 3;
  // receiver id
  int64 receiver_id = 4 [(validate.rules).int64 = {gt: 0}];
  // title
  optional string title = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // content
  optional string content = 6 [(validate.rules).string = {max_len: 1048576}];
  // notification method
  models.notification.v1.NotificationMethod method = 7 [(validate.rules).enum = {defined_only: true}];
  // notification type
  models.notification.v1.NotificationType type = 8 [(validate.rules).enum = {defined_only: true}];
  // extra info
  models.notification.v1.NotificationExtraDef extra = 9;
  // app push
  optional models.notification.v1.AppPushDef app_push = 10;
}

// create inbox notification response
message CreateInboxNotificationResponse {
  // notification id
  int64 notification_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// read inbox notification request
message ReadInboxNotificationRequest {
  // notification id
  int64 notification_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// read inbox notification response
message ReadInboxNotificationResponse {
  // read time
  google.protobuf.Timestamp read_time = 1;
}

// delete inbox notification request
message DeleteInboxNotificationRequest {
  // notification id
  int64 notification_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete inbox notification response
message DeleteInboxNotificationResponse {
  // deleted time
  google.protobuf.Timestamp deleted_time = 1;
}

// get notification list request
message GetNotificationListRequest {
  // notification type
  repeated models.notification.v1.NotificationType types = 1 [(validate.rules).repeated = {
    ignore_empty: true
    min_items: 0
    items: {
      enum: {defined_only: true}
    }
  }];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 2;
  // sort by
  repeated moego.models.notification.v1.NotificationSortDef sorts = 3;
  // notification source
  repeated models.notification.v1.NotificationSource sources = 4 [(validate.rules).repeated = {
    ignore_empty: true
    min_items: 0
    items: {
      enum: {defined_only: true}
    }
  }];
  // notification method
  repeated models.notification.v1.NotificationMethod methods = 5 [(validate.rules).repeated = {
    ignore_empty: true
    min_items: 0
    items: {
      enum: {defined_only: true}
    }
  }];
  // receiver id
  int64 receiver_id = 6 [(validate.rules).int64 = {gt: 0}];
}

// get notification list response
message GetNotificationListResponse {
  // notification list
  repeated models.notification.v1.NotificationModel notifications = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// the notification service
service NotificationService {
  // Create inbox notification
  rpc CreateInboxNotification(CreateInboxNotificationRequest) returns (CreateInboxNotificationResponse);

  // Read inbox notification
  rpc ReadInboxNotification(ReadInboxNotificationRequest) returns (ReadInboxNotificationResponse);

  // Delete inbox notification
  rpc DeleteInboxNotification(DeleteInboxNotificationRequest) returns (DeleteInboxNotificationResponse);

  // Get notification list
  rpc GetNotificationList(GetNotificationListRequest) returns (GetNotificationListResponse);
}
