// @since 2023-09-11 14:34:19
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.marketing.v1;

import "google/protobuf/empty.proto";
import "moego/models/marketing/v1/discount_code_defs.proto";
import "moego/models/marketing/v1/discount_code_enums.proto";
import "moego/models/marketing/v1/discount_code_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/marketing/v1;marketingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.marketing.v1";

// generate discount code input
message GenerateDiscountCodeInput {
  // business id
  int64 business_id = 1;
  // company id
  int64 company_id = 2;
}

// generate discount code output
message GenerateDiscountCodeOutput {
  // discount code
  string discount_code = 1;
}

// check discount code input
message CheckDiscountCodeInput {
  // code
  string discount_code = 1 [(validate.rules).string = {
    min_len: 4
    max_len: 20
  }];
  // business id
  int64 business_id = 2;
  // company id
  int64 company_id = 3;
}

// check discount code output
message CheckDiscountCodeOutput {
  // is duplicate
  bool is_duplicate = 1;
}

// create discount_code input
message CreateDiscountCodeInput {
  // discount code
  string discount_code = 1 [(validate.rules).string = {
    min_len: 4
    max_len: 100
  }];
  // description
  optional string description = 2 [(validate.rules).string = {max_len: 200}];
  // discount amount
  double amount = 3 [(validate.rules).double = {
    gt: 0
    lt: *********
  }];
  // discount type
  moego.models.marketing.v1.DiscountCodeType type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // start date
  string start_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end date
  optional string end_date = 6 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // allowed all thing
  bool allowed_all_thing = 7;
  // allowed all services
  bool allowed_all_services = 8;
  // allowed services
  repeated int64 service_ids = 9;
  // allowed add ons
  repeated int64 add_on_ids = 10;
  // allowed all products
  bool allowed_all_products = 11;
  // allowed products
  repeated int64 product_ids = 12;

  // allowed all clients
  bool allowed_all_clients = 13;
  // allowed new clients
  bool allowed_new_clients = 14;
  // clients group
  string clients_group = 15 [(validate.rules).string = {
    min_len: 0
    max_len: 10000
  }];
  // allowed clients
  repeated int64 client_ids = 16;

  // limit usage
  int32 limit_usage = 17 [(validate.rules).int32 = {
    gte: 0
    lt: 1000000
  }];
  // limit number per client
  int32 limit_number_per_client = 18 [(validate.rules).int32 = {
    gte: 0
    lt: 1000000
  }];
  // limit budget
  int32 limit_budget = 19 [(validate.rules).int32 = {
    gte: 0
    lt: 1000000
  }];

  // auto apply association
  bool auto_apply_association = 20;
  // enable online booking
  bool enable_online_booking = 21;
  // business id
  int64 business_id = 22;
  // company id
  int64 company_id = 23;

  // allowed locations
  repeated int64 location_ids = 24;
  // expiry def
  moego.models.marketing.v1.ExpiryDef expiry_def = 25;
}

// create discount code output
message CreateDiscountCodeOutput {
  // id
  int64 id = 1;
}

// edit discount_code input
message EditDiscountCodeInput {
  // discount code
  string discount_code = 1 [(validate.rules).string = {
    min_len: 4
    max_len: 20
  }];
  // description
  string description = 2 [(validate.rules).string = {max_len: 200}];
  // discount amount
  double amount = 3 [(validate.rules).double = {
    gt: 0
    lt: *********
  }];
  // discount type
  moego.models.marketing.v1.DiscountCodeType type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // start date
  string start_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end date (blank string for no end date)
  string end_date = 6;

  // allowed all thing
  bool allowed_all_thing = 7;
  // allowed all services
  bool allowed_all_services = 8;
  // allowed services
  repeated int64 service_ids = 9;
  // allowed add ons
  repeated int64 add_on_ids = 10;
  // allowed all products
  bool allowed_all_products = 11;
  // allowed products
  repeated int64 product_ids = 12;

  // allowed all clients
  bool allowed_all_clients = 13;
  // allowed new clients
  bool allowed_new_clients = 14;
  // clients group
  string clients_group = 15 [(validate.rules).string = {
    min_len: 0
    max_len: 10000
  }];
  // allowed clients
  repeated int64 client_ids = 16;

  // limit usage
  int32 limit_usage = 17 [(validate.rules).int32 = {
    gte: 0
    lt: 1000000
  }];
  // limit number per client
  int32 limit_number_per_client = 18 [(validate.rules).int32 = {
    gte: 0
    lt: 1000000
  }];
  // limit budget
  int32 limit_budget = 19 [(validate.rules).int32 = {
    gte: 0
    lt: 1000000
  }];

  // auto apply association
  bool auto_apply_association = 20;
  // enable online booking
  bool enable_online_booking = 21;

  // unique id
  int64 id = 22;
  // business id
  int64 business_id = 23;
  // company id
  int64 company_id = 24;

  // allowed locations
  repeated int64 location_ids = 25;

  // expiry def
  optional moego.models.marketing.v1.ExpiryDef expiry_def = 26;
}

// edit discount code output
message EditDiscountCodeOutput {
  // id
  int64 id = 1;
}

// get discount_code input
message GetDiscountCodeInput {
  // code id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2;
  // company id
  int64 company_id = 3;
}

// get discount_code output
message GetDiscountCodeOutput {
  // discount code model
  moego.models.marketing.v1.DiscountCodeModel discount_code_model = 1;
  // discount code summary
  moego.models.marketing.v1.DiscountCodeSummaryDef discount_code_summary_def = 2;
}

// get discount_code input
message GetDiscountCodeByCodeInput {
  // discount code
  string discount_code = 1 [(validate.rules).string = {
    min_len: 4
    max_len: 20
  }];
  // business id
  int64 business_id = 2;
  // company id
  int64 company_id = 3;
}

// get discount_code output
message GetDiscountCodeByCodeOutput {
  // discount code model
  moego.models.marketing.v1.DiscountCodeModel discount_code_model = 1;
}

// get discount code input
message GetDiscountCodeListInput {
  // pagination response
  moego.utils.v2.PaginationRequest pagination = 1 [(validate.rules).message.required = true];
  // status
  repeated moego.models.marketing.v1.DiscountCodeStatus status = 2 [(validate.rules).repeated = {max_items: 5}];
  // code
  optional string discount_code = 3 [(validate.rules).string = {max_len: 20}];
  // id
  repeated int64 ids = 4;
  // business id
  int64 business_id = 5;
  // company id
  int64 company_id = 6;
}

// get discount code output
message GetDiscountCodeListOutput {
  // pagination response
  moego.utils.v2.PaginationResponse pagination = 1;
  // discount code list
  repeated moego.models.marketing.v1.DiscountCodeCompositeView discount_code_composite_views = 2;
}

// get discount code log input
message GetDiscountCodeLogListInput {
  // pagination request
  moego.utils.v2.PaginationRequest pagination = 1 [(validate.rules).message.required = true];
  // code id
  int64 id = 2 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 3;
  // company id
  int64 company_id = 4;
}

// get discount code log output
message GetDiscountCodeLogListOutput {
  // pagination response
  moego.utils.v2.PaginationResponse pagination = 1;
  // discount code log list
  repeated moego.models.marketing.v1.DiscountCodeLogCompositeView discount_code_log_composite_views = 2;
}

// get discount code log input
message GetDiscountCodeLogOverviewInput {
  // code id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2;
  // company id
  int64 company_id = 3;
}

// get discount code log output
message GetDiscountCodeLogOverviewOutput {
  // total usage
  int32 total_usage = 1;
  // total client
  int32 total_client = 2;
  // discount sales
  double discount_sales = 3;
  // invoice sales
  double invoice_sales = 4;
}

// archive discount code input
message ChangeStatusInput {
  // code id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // status
  moego.models.marketing.v1.DiscountCodeStatus status = 2;
  // business id
  int64 business_id = 3;
  // staff id
  int64 staff_id = 4;
  // company id
  int64 company_id = 5;
  // expiry def, if change to active, may need to reset expiry time
  optional moego.models.marketing.v1.ExpiryDef expiry_def = 6;
}

// check discount code valid for customer input
message CheckDiscountCodeValidForCustomerInput {
  // code name
  string code_name = 1 [(validate.rules).string = {max_len: 20}];
  // service ids
  repeated int64 service_ids = 2;
  // customer id
  optional int64 customer_id = 3;
  // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
  optional string name = 4;
  // Customized URL domain, demo URL: crazycutepetspa.moego.online
  optional string domain = 5;
  // business id
  optional int64 business_id = 6;
  // appointment date
  optional string appointment_date = 7 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // company id
  optional int64 company_id = 8;
}

// check discount code valid for customer output
message CheckDiscountCodeValidForCustomerOutput {
  // discount code model online booking view
  moego.models.marketing.v1.DiscountCodeModelOnlineBookingView discount_code_model_online_booking_view = 1;
}

// archive discount code output
message ChangeStatusOutput {
  // success
  bool success = 1;
}

// get available discount code list input
message GetAvailableDiscountListInput {
  // pagination request
  moego.utils.v2.PaginationRequest pagination = 1 [(validate.rules).message.required = true];
  // code name
  optional string code_name = 2 [(validate.rules).string = {max_len: 20}];
  // business id
  int64 business_id = 3;
  // customer id
  int64 customer_id = 4;
  // items
  repeated moego.models.marketing.v1.Item items = 5;
  // used discount code ids
  repeated int64 used_discount_code_ids = 6;
  // source id
  int64 source_id = 7;
  // company id
  int64 company_id = 8;
}

// get available discount code list output
message GetAvailableDiscountListOutput {
  // pagination response
  moego.utils.v2.PaginationResponse pagination = 1;
  // discount code list
  repeated moego.models.marketing.v1.DiscountCodeCompositeView discount_code_composite_views = 2;
}

// get available discount code list input
message GetAvailableDiscountListForExistingInvoiceInput {
  // business id
  int64 business_id = 3;
  // customer id
  int64 customer_id = 4;
  // items
  repeated moego.models.marketing.v1.Item items = 5;
  // will use discount code ids
  repeated int64 will_use_discount_code_ids = 6;
  // used discount code ids
  repeated int64 used_discount_code_ids = 7;
  // appointment date
  optional string appointment_date = 8 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // company id
  int64 company_id = 9;
}

// get available discount code list output
message GetAvailableDiscountListForExistingInvoiceOutput {
  // discount code list
  repeated moego.models.marketing.v1.DiscountCodeModel discount_code_models = 1;
}

// auto apply discount code input
message AutoApplyDiscountCodeInput {
  // business id
  int64 business_id = 1;
  // customer id
  int64 customer_id = 2;
  // appointment id
  int64 source_id = 3;
  // order id
  int64 order_id = 4;
  // staff id
  int64 staff_id = 5;
  // items
  repeated moego.models.marketing.v1.Item items = 6;
  // appointment date
  string appointment_date = 7 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // company id
  int64 company_id = 8;
}

// auto apply discount code output
message AutoApplyDiscountCodeOutput {
  // success
  bool success = 1;
  // discount code
  moego.models.marketing.v1.DiscountCodeModel discount_code_model = 2;
}

// use discount code input
message UseDiscountCodeInput {
  // business id
  int64 business_id = 1;
  // customer id
  int64 customer_id = 2;
  // appointment id
  int64 source_id = 3;
  // order id
  int64 order_id = 4;
  // staff id
  int64 staff_id = 5;
  // redeem type
  moego.models.marketing.v1.RedeemType redeem_type = 6;
  // discount code usage
  repeated DiscountCodeUsage discount_code_usages = 7;
  // invoice sales
  double invoice_sales = 8;
  // company id
  int64 company_id = 9;
}

// discount code usage
message DiscountCodeUsage {
  // object id
  repeated int64 object_ids = 1;
  // item type
  string type = 2;
  // discount sales amount
  double discount_sales_amount = 3;
  // discount code id
  int64 discount_code_id = 4;
}

// delete discount code log input
message DeleteDiscountCodeLogInput {
  // redeem id
  int64 redeem_id = 1;
  // id
  repeated int64 ids = 2;
}

// get business discount code config input
message GetBusinessDiscountCodeConfigInput {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1 [(validate.rules).string = {max_len: 255}];
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2 [(validate.rules).string = {max_len: 255}];
  }
  // business id
  optional int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // company id
  optional int64 company_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// get business discount code config output
message GetBusinessDiscountCodeConfigOutput {
  // business has valid discount code
  bool has_valid_discount_code = 1;
}

// get business discount code config input
message GetDiscountCodeConfigInput {
  // the unique identifier of the business, such as online booking name, domain or business id
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1 [(validate.rules).string = {max_len: 255}];
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2 [(validate.rules).string = {max_len: 255}];
    // business id
    int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  }
}

// get business discount code config output
message GetDiscountCodeConfigOutput {
  // business has valid discount code
  bool has_valid_discount_code = 1;
}

// migrate discount code id input
message MigrateDiscountCodeInput {
  // company id
  int64 company_id = 1;
  // staff id map
  map<int64, int64> staff_id_map = 2;
  // service id map
  map<int64, int64> service_id_map = 3;
  // client id map
  map<int64, int64> client_id_map = 4;
  // pet id map
  map<int64, int64> pet_id_map = 5;
}

// migrate discount code id output
message MigrateDiscountCodeInputOutput {}

// the discount_code service
service DiscountCodeService {
  // generate discount code
  rpc GenerateDiscountCode(GenerateDiscountCodeInput) returns (GenerateDiscountCodeOutput);
  // check discount code
  rpc CheckDiscountCode(CheckDiscountCodeInput) returns (CheckDiscountCodeOutput);
  // create discount code
  rpc CreateDiscountCode(CreateDiscountCodeInput) returns (CreateDiscountCodeOutput);
  // edit discount code
  rpc EditDiscountCode(EditDiscountCodeInput) returns (EditDiscountCodeOutput);
  // get discount code
  rpc GetDiscountCode(GetDiscountCodeInput) returns (GetDiscountCodeOutput);
  // get discount code by code
  rpc GetDiscountCodeByCode(GetDiscountCodeByCodeInput) returns (GetDiscountCodeByCodeOutput);
  // get discount code list
  rpc GetDiscountCodeList(GetDiscountCodeListInput) returns (GetDiscountCodeListOutput);
  // get discount code log overview
  rpc GetDiscountCodeLogOverview(GetDiscountCodeLogOverviewInput) returns (GetDiscountCodeLogOverviewOutput);
  // get discount code log list
  rpc GetDiscountCodeLogList(GetDiscountCodeLogListInput) returns (GetDiscountCodeLogListOutput);
  // change status
  rpc ChangeStatus(ChangeStatusInput) returns (ChangeStatusOutput);
  // check discount code valid for customer
  rpc CheckDiscountCodeValidForCustomer(CheckDiscountCodeValidForCustomerInput) returns (CheckDiscountCodeValidForCustomerOutput);
  // get available discount code list
  rpc GetAvailableDiscountList(GetAvailableDiscountListInput) returns (GetAvailableDiscountListOutput);
  // get available discount code list for existing invoice
  rpc GetAvailableDiscountListForExistingInvoice(GetAvailableDiscountListForExistingInvoiceInput) returns (GetAvailableDiscountListForExistingInvoiceOutput);
  // auto apply discount code
  rpc AutoApplyDiscountCode(AutoApplyDiscountCodeInput) returns (AutoApplyDiscountCodeOutput);
  // delete discount code log
  rpc DeleteDiscountCodeLog(DeleteDiscountCodeLogInput) returns (google.protobuf.Empty);
  //  use discount code
  rpc UseDiscountCode(UseDiscountCodeInput) returns (google.protobuf.Empty);
  //  refresh discount code status
  rpc RefreshDiscountCodeStatus(google.protobuf.Empty) returns (google.protobuf.Empty);
  // get business discount code config
  // Deprecated: replace to GetDiscountCodeConfig
  rpc GetBusinessDiscountCodeConfig(GetBusinessDiscountCodeConfigInput) returns (GetBusinessDiscountCodeConfigOutput);
  // get business discount code config
  rpc GetDiscountCodeConfig(GetDiscountCodeConfigInput) returns (GetDiscountCodeConfigOutput);
  // migrate discount code
  rpc MigrateDiscountCode(MigrateDiscountCodeInput) returns (MigrateDiscountCodeInputOutput);
}
