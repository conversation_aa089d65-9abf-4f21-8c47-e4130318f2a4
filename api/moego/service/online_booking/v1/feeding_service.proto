syntax = "proto3";

package moego.service.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/online_booking/v1/feeding_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// Create Feeding request
message CreateFeedingRequest {
  // service detail type, 1: boarding, 2: daycare
  //  optional int32 service_detail_type = 3 [(validate.rules).int32 = {gt: 0}];
  // Feeding time.
  repeated moego.models.online_booking.v1.FeedingModel.FeedingSchedule time = 4;
  // Feeding amount, must be greater than 0.
  optional double amount = 5 [deprecated = true];
  // Feeding unit.
  optional string unit = 6 [(validate.rules).string = {max_len: 2048}];
  // Food type.
  optional string food_type = 7 [(validate.rules).string = {max_len: 2048}];
  // Food source.
  optional string food_source = 8 [(validate.rules).string = {max_len: 2048}];
  // Feeding instructions.
  optional string instruction = 9 [(validate.rules).string = {max_len: 2048}];
  // createdAt
  optional google.protobuf.Timestamp created_at = 10;
  // updatedAt
  optional google.protobuf.Timestamp updated_at = 11;
  // feeding note
  optional string note = 12 [(validate.rules).string = {max_len: 255}];
  // Feeding amount, such as 1.2, 1/2, 1 etc.
  optional string amount_str = 13 [(validate.rules).string = {max_len: 255}];
}

// Feeding service
service FeedingService {}

// Create Feeding request list
message CreateFeedingRequestList {
  // values
  repeated CreateFeedingRequest values = 1;
}
