// @since 2024-10-11 12:09:44
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.online_booking.v1;

import "moego/models/offering/v1/service_enum.proto";
import "moego/models/online_booking/v1/automation_defs.proto";
import "moego/models/online_booking/v1/automation_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// get automation setting request
message GetAutomationSettingRequest {
  // the business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // the company id
  int64 company_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// get automation setting response
message GetAutomationSettingResponse {
  // the automation
  moego.models.online_booking.v1.AutomationSettingModel automation_setting = 1;
}

// create automation setting request
message UpdateAutomationSettingRequest {
  // the business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // enable auto accept
  bool enable_auto_accept = 3;
  // auto accept condition
  moego.models.online_booking.v1.AutomationConditionDef auto_accept_condition = 4;
  // the company id
  int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];
  // the staff id
  int64 staff_id = 6 [(validate.rules).int64 = {gt: 0}];
}

// create automation setting response
message UpdateAutomationSettingResponse {
  // the updated automation
  moego.models.online_booking.v1.AutomationSettingModel automation_setting = 1;
}

// the automation service
service AutomationService {
  // get automation setting
  rpc GetAutomationSetting(GetAutomationSettingRequest) returns (GetAutomationSettingResponse);
  // update automation setting
  rpc UpdateAutomationSetting(UpdateAutomationSettingRequest) returns (UpdateAutomationSettingResponse);
}
