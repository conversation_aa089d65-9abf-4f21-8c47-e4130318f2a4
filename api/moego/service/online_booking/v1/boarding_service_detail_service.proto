syntax = "proto3";

package moego.service.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// Create BoardingServiceDetail request
message CreateBoardingServiceDetailRequest {
  // The id of pet, associated with the current service
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
  // The id of lodging, associated with the current service
  optional int64 lodging_id = 3;
  // The id of current service
  int64 service_id = 4 [(validate.rules).int64 = {gt: 0}];
  // The price of current service
  optional double service_price = 6;
  // taxId
  optional int64 tax_id = 7;
  // The pet arrival date of the service, yyyy-MM-dd
  // 当 boarding service detail 加入 waitlist 时，这个参数可以为空
  optional string start_date = 8 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // The pet arrival time of the service, unit minute, 540 means 09:00
  // 当 boarding service detail 加入 waitlist 时，这个参数可以为空
  optional int32 start_time = 9 [(validate.rules).int32 = {gte: 0}];
  // The pet pickup date of the service, yyyy-MM-dd
  // 当 boarding service detail 加入 waitlist 时，这个参数可以为空
  optional string end_date = 10 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // The pet pickup time of the service, unit minute, 540 means 09:00
  // 当 boarding service detail 加入 waitlist 时，这个参数可以为空
  optional int32 end_time = 11 [(validate.rules).int32 = {gte: 0}];
  // createdAt
  optional google.protobuf.Timestamp created_at = 12;
  // updatedAt
  optional google.protobuf.Timestamp updated_at = 13;
}

// BoardingServiceDetail service
service BoardingServiceDetailService {}

// Update BoardingServiceDetail request
message UpdateBoardingServiceDetailRequest {
  // The id of boarding service detail
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // The start date of the service, yyyy-MM-dd
  optional string start_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // The start time of the service, unit minute, 540 means 09:00
  optional int32 start_time = 3 [(validate.rules).int32 = {gte: 0}];
  // The end date of the service, yyyy-MM-dd
  optional string end_date = 4 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // The end time of the service, unit minute, 540 means 09:00
  optional int32 end_time = 5 [(validate.rules).int32 = {gte: 0}];
}
