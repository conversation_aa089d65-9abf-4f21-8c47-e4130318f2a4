syntax = "proto3";

package moego.service.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/utils/v1/struct.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// Create BoardingAddOnDetail request
message CreateBoardingAddOnDetailRequest {
  // The id of pet, associated with the current add-on
  int64 pet_id = 3 [(validate.rules).int64 = {gt: 0}];
  // The id of current add-on service
  int64 add_on_id = 4 [(validate.rules).int64 = {gt: 0}];
  // The specific dates of the add-on service
  repeated string specific_dates = 5 [(validate.rules).repeated = {
    items: {
      string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
    }
  }];
  // whether the add-on service is everyday, not include checkout day
  // deprecated. use date_type instead
  optional bool is_everyday = 6 [deprecated = true];
  // The price of current add-on service
  optional double service_price = 7;
  // taxId
  optional int64 tax_id = 8;
  // duration
  optional int32 duration = 9;
  // createdAt
  optional google.protobuf.Timestamp created_at = 10;
  // updatedAt
  optional google.protobuf.Timestamp updated_at = 11;
  // quantity per day
  optional int32 quantity_per_day = 12;
  // date type
  optional models.appointment.v1.PetDetailDateType date_type = 13;
  // start date
  optional google.type.Date start_date = 14;
}

// BoardingAddOnDetail service
service BoardingAddOnDetailService {}

// Update BoardingAddOnDetail response
message UpdateBoardingAddOnDetailRequest {
  // The id of boarding add-on detail
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // date type
  optional models.appointment.v1.PetDetailDateType date_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // The specific dates of the add-on service. VValid only when date_type is PET_DETAIL_DATE_SPECIFIC_DATE
  optional moego.utils.v1.StringListValue specific_dates = 3;
  // quantity per day
  optional int32 quantity_per_day = 4 [(validate.rules).int32 = {gte: 0}];
  // start date
  // 当 date_type 为 PET_DETAIL_DATE_SPECIFIC_DATE 时使用
  optional string start_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
}
