syntax = "proto3";

package moego.service.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "moego/models/online_booking/v1/grooming_add_on_detail_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// Create GroomingAddOnDetail request
message CreateGroomingAddOnDetailRequest {
  // The id of pet, associated with the current service
  int64 pet_id = 3 [(validate.rules).int64 = {gt: 0}];
  // The id of staff, associated with the current service
  optional int64 staff_id = 4;
  // The id of add-on service, aka. grooming service id
  int64 add_on_id = 5 [(validate.rules).int64 = {gt: 0}];
  // The time of current service, unit minute
  optional int32 service_time = 6;
  // The price of current service
  optional double service_price = 7;
  // The start date of the service, yyyy-MM-dd
  optional string start_date = 8 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // The start time of the service, unit minute, 540 means 09:00
  optional int32 start_time = 9;
  // The end date of the service, yyyy-MM-dd
  optional string end_date = 10 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // The end time of the service, unit minute, 540 means 09:00
  optional int32 end_time = 11;
  // createdAt
  optional google.protobuf.Timestamp created_at = 12;
  // updatedAt
  optional google.protobuf.Timestamp updated_at = 13;
}

// Get GroomingAddOnDetail response
message GetGroomingAddOnDetailResponse {
  // Existing record
  optional models.online_booking.v1.GroomingAddOnDetailModel record = 1;
}

// Update GroomingAddOnDetail response
message UpdateGroomingAddOnDetailRequest {
  // The id of grooming add-on detail
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // The id of booking request
  optional int64 booking_request_id = 2 [(validate.rules).int64 = {gt: 0}];
  // The id of service detail
  optional int64 service_detail_id = 3 [(validate.rules).int64 = {gt: 0}];
  // The id of pet, associated with the current service
  optional int64 pet_id = 4 [(validate.rules).int64 = {gt: 0}];
  // The id of staff, associated with the current service
  optional int64 staff_id = 5 [(validate.rules).int64 = {gt: 0}];
  // The id of add-on service, aka. grooming service id
  optional int64 add_on_id = 6 [(validate.rules).int64 = {gt: 0}];
  // The time of current service, unit minute
  optional int32 service_time = 7 [(validate.rules).int32 = {gte: 0}];
  // The price of current service
  optional double service_price = 8 [(validate.rules).double = {gte: 0}];
  // The start date of the service, yyyy-MM-dd
  optional string start_date = 9 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // The start time of the service, unit minute, 540 means 09:00
  optional int32 start_time = 10 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // The end date of the service, yyyy-MM-dd
  optional string end_date = 11 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // The end time of the service, unit minute, 540 means 09:00
  optional int32 end_time = 12 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // createdAt
  optional google.protobuf.Timestamp created_at = 13;
  // updatedAt
  optional google.protobuf.Timestamp updated_at = 14;
}

// GroomingAddOnDetail service
service GroomingAddOnDetailService {
  // Create a record, return inserted id.
  rpc CreateGroomingAddOnDetail(CreateGroomingAddOnDetailRequest) returns (google.protobuf.Int64Value) {}
  // Get a record by id, not include deleted record.
  rpc GetGroomingAddOnDetail(google.protobuf.Int64Value) returns (GetGroomingAddOnDetailResponse) {}
  // Update a record by id, return updated rows.
  rpc UpdateGroomingAddOnDetail(UpdateGroomingAddOnDetailRequest) returns (google.protobuf.Int32Value) {}
  // Delete a record by id, return deleted rows.
  rpc DeleteGroomingAddOnDetail(google.protobuf.Int64Value) returns (google.protobuf.Int32Value) {}
}
