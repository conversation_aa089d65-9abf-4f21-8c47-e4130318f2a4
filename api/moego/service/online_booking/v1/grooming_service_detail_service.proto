syntax = "proto3";

package moego.service.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// Create GroomingServiceDetail request
message CreateGroomingServiceDetailRequest {
  // The id of pet, associated with the current service
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
  // The id of staff, associated with the current service
  optional int64 staff_id = 3;
  // The id of current service
  int64 service_id = 4 [(validate.rules).int64 = {gt: 0}];
  // The time of current service, unit minute
  optional int32 service_time = 5;
  // The price of current service
  optional double service_price = 6;
  // The start date of the service, yyyy-MM-dd
  optional string start_date = 9 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // The start time of the service, unit minute, 540 means 09:00
  optional int32 start_time = 10 [(validate.rules).int32 = {gte: 0}];
  // The end date of the service, yyyy-MM-dd
  optional string end_date = 11 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // The end time of the service, unit minute, 540 means 09:00
  optional int32 end_time = 12 [(validate.rules).int32 = {gte: 0}];
  // createdAt
  optional google.protobuf.Timestamp created_at = 13;
  // updatedAt
  optional google.protobuf.Timestamp updated_at = 14;
  // date type
  optional moego.models.appointment.v1.PetDetailDateType date_type = 15;
}

// Update GroomingServiceDetail request
message UpdateGroomingServiceDetailRequest {
  // The id of grooming service detail
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // The id of staff, associated with the current service
  optional int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
  // The price of current service
  optional double service_price = 6 [(validate.rules).double = {gte: 0}];
  // The start date of the service, yyyy-MM-dd
  optional string start_date = 9 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // The start time of the service, unit minute, 540 means 09:00
  optional int32 start_time = 10 [(validate.rules).int32 = {gte: 0}];
  // The end date of the service, yyyy-MM-dd
  optional string end_date = 11 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // The end time of the service, unit minute, 540 means 09:00
  optional int32 end_time = 12 [(validate.rules).int32 = {gte: 0}];
  // date type
  optional moego.models.appointment.v1.PetDetailDateType date_type = 15;
}

// GroomingServiceDetail service
service GroomingServiceDetailService {}
