// @since 2024-04-08 15:28:27
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.service.online_booking.v1;

import "google/type/date.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/online_booking/v1/ob_availability_setting_defs.proto";
import "moego/models/online_booking/v1/ob_availability_setting_enums.proto";
import "moego/models/online_booking/v1/ob_availability_setting_models.proto";
import "moego/models/organization/v1/tenant.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// get boarding service availability setting request
message GetBoardingServiceAvailabilitySettingRequest {
  // the tenant id
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
}

// get boarding service availability setting response
message GetBoardingServiceAvailabilitySettingResponse {
  // the boarding service availability setting
  models.online_booking.v1.BoardingServiceAvailabilityModel boarding_service_availability_setting = 1;
}

// update boarding service availability setting request
message UpdateBoardingServiceAvailabilitySettingRequest {
  // the tenant id
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
  // the boarding service availability setting
  models.online_booking.v1.BoardingServiceAvailabilityUpdateDef boarding_service_availability_setting = 2 [(validate.rules).message.required = true];
  // staff id
  optional int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// update boarding service availability setting response
message UpdateBoardingServiceAvailabilitySettingResponse {
  // the boarding service availability setting
  models.online_booking.v1.BoardingServiceAvailabilityModel boarding_service_availability_setting = 1;
}

// get daycare service availability setting request
message GetDaycareServiceAvailabilitySettingRequest {
  // the tenant id
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
}

// get daycare service availability setting response
message GetDaycareServiceAvailabilitySettingResponse {
  // the daycare service availability setting
  models.online_booking.v1.DaycareServiceAvailabilityModel daycare_service_availability_setting = 1;
}

// update daycare service availability setting request
message UpdateDaycareServiceAvailabilitySettingRequest {
  // the tenant id
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
  // the daycare service availability setting
  models.online_booking.v1.DaycareServiceAvailabilityUpdateDef daycare_service_availability_setting = 2 [(validate.rules).message.required = true];
  // staff id
  optional int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// update daycare service availability setting response
message UpdateDaycareServiceAvailabilitySettingResponse {
  // the daycare service availability setting
  models.online_booking.v1.DaycareServiceAvailabilityModel daycare_service_availability_setting = 1;
}

// query available pet type request
message QueryAvailablePetTypeRequest {
  // the tenant id
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 2 [(validate.rules).enum.defined_only = true];
}

// query available pet type response
message QueryAvailablePetTypeResponse {
  // available pet type
  repeated models.customer.v1.PetType available_pet_type = 1;
}

// query available booking date range request
message QueryAvailableBookingDateRangeRequest {
  // the tenant id
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 2 [(validate.rules).enum.defined_only = true];
}

// query available booking date range response
message QueryAvailableBookingDateRangeResponse {
  // from date
  google.type.Date from_date = 1;
  // to date
  google.type.Date to_date = 2;
}

// get evaluation service availability request
message GetEvaluationServiceAvailabilityRequest {
  // the tenant id
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
}

// get evaluation service availability response
message GetEvaluationServiceAvailabilityResponse {
  // evaluation service availability
  moego.models.online_booking.v1.EvaluationServiceAvailabilityModel availability = 1;
}

// update evaluation service availability request
message UpdateEvaluationServiceAvailabilityRequest {
  // the tenant id
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
  // evaluation service availability
  moego.models.online_booking.v1.EvaluationServiceAvailabilityUpdateDef availability = 2;
  // staff id
  optional int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// update evaluation service availability response
message UpdateEvaluationServiceAvailabilityResponse {
  // evaluation service availability
  moego.models.online_booking.v1.EvaluationServiceAvailabilityModel availability = 1;
}

// get grooming service availability request
message GetGroomingServiceAvailabilityRequest {
  // the tenant id
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
}

// get grooming service availability response
message GetGroomingServiceAvailabilityResponse {
  // grooming service availability
  moego.models.online_booking.v1.GroomingServiceAvailabilityModel availability = 1;
}

// update grooming service availability request
message UpdateGroomingServiceAvailabilityRequest {
  // the tenant id
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
  // grooming service availability
  moego.models.online_booking.v1.GroomingServiceAvailabilityUpdateDef availability = 2;
  // staff id
  optional int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// update grooming service availability response
message UpdateGroomingServiceAvailabilityResponse {
  // grooming service availability
  moego.models.online_booking.v1.GroomingServiceAvailabilityModel availability = 1;
}

// get accepted customer setting request
// deprecated by pc since /2025/6/20 use OBAvailableDateTimeService.GetAvailableDateTime instead.
message ListAvailableBookingTimeRangeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id, optional
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service item types
  repeated models.offering.v1.ServiceItemType service_item_type = 3 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// get accepted customer setting response
message ListAvailableBookingTimeRangeResponse {
  // time ranges
  repeated TimeRange time_ranges = 1;

  // time range
  message TimeRange {
    // service item type
    models.offering.v1.ServiceItemType service_item_type = 1;
    // pick up time range
    moego.models.online_booking.v1.ArrivalPickUpTimeDef arrival_pick_up_time_range = 2;
  }
}

// get accepted customer setting request
message ListAcceptedCustomerSettingRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id, optional
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service item types
  repeated models.offering.v1.ServiceItemType service_item_types = 3 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// get accepted customer setting response
message ListAcceptedCustomerSettingResponse {
  // accepted customer types
  repeated AcceptCustomerType accept_customer_types = 1;

  // accept customer type
  message AcceptCustomerType {
    // service item type
    models.offering.v1.ServiceItemType service_item_type = 1;
    // accepted customer type
    moego.models.online_booking.v1.AcceptCustomerType accept_customer_type = 2;
  }
}

// update accepted customer setting request
message UpdateAcceptedCustomerSettingRequest {
  // the tenant id
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
  // accept customer type
  moego.models.online_booking.v1.AcceptCustomerType accept_customer_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service item types
  repeated models.offering.v1.ServiceItemType service_item_types = 3 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    max_items: 10
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // staff id
  optional int64 staff_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// update accepted customer setting response
message UpdateAcceptedCustomerSettingResponse {}

// List arrival pick up time overrides request
message ListArrivalPickUpTimeOverridesRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business ids, optional
  repeated int64 business_ids = 2 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // service item types, optional
  repeated models.offering.v1.ServiceItemType service_item_types = 3 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 8;
}

// List arrival pick up time overrides  response
message ListArrivalPickUpTimeOverridesResponse {
  // arrival/pick up time overrides
  repeated moego.models.online_booking.v1.ArrivalPickUpTimeOverrideModel overrides = 1;
  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// batch create arrival pick up time override request
message BatchCreateArrivalPickUpTimeOverrideRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // arrival/pick up time overrides
  repeated CreateDef overrides = 4;

  // arrival/pick up time override request
  message CreateDef {
    // arrival time/pick up time
    moego.models.online_booking.v1.TimeRangeType type = 1;
    // start date
    google.type.Date start_date = 2;
    // end date
    google.type.Date end_date = 3;
    // is available
    bool is_available = 4;
    // time range
    repeated moego.models.online_booking.v1.DayTimeRangeDef day_time_ranges = 5;
  }
}

// batch create arrival pick up time override response
message BatchCreateArrivalPickUpTimeOverrideResponse {
  // arrival/pick up time overrides
  repeated moego.models.online_booking.v1.ArrivalPickUpTimeOverrideModel overrides = 1;
}

// batch delete arrival pick up time override request
message BatchDeleteArrivalPickUpTimeOverrideRequest {
  // arrival/pick up time override ids
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
  // company id
  optional int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// batch delete arrival pick up time override response
message BatchDeleteArrivalPickUpTimeOverrideResponse {}

// batch update arrival pick up time override request
message BatchUpdateArrivalPickUpTimeOverrideRequest {
  // arrival time overrides
  repeated UpdateDef overrides = 1;
  // company id
  optional int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];

  // arrival/pick up time override update request
  message UpdateDef {
    // id
    int64 id = 1 [(validate.rules).int64 = {gt: 0}];
    // start date
    optional google.type.Date start_date = 2;
    // end date
    optional google.type.Date end_date = 3;
    // is available
    optional bool is_available = 4;
    // day time range list
    optional moego.models.online_booking.v1.DayTimeRangeDefList day_time_ranges = 5;
  }
}

// batch update arrival pick up time override response
message BatchUpdateArrivalPickUpTimeOverrideResponse {
  // arrival/pick up time overrides
  repeated moego.models.online_booking.v1.ArrivalPickUpTimeOverrideModel overrides = 1;
}

// List capacity overrides request
message ListCapacityOverridesRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id, optional
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// List capacity overrides response
message ListCapacityOverridesResponse {
  // capacity overrides
  repeated moego.models.online_booking.v1.CapacityOverrideModel capacity_overrides = 1;
}

// create capacity override request
message CreateCapacityOverrideRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id, optional
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // capacity override
  moego.models.online_booking.v1.CapacityOverrideDef capacity_override = 4 [(validate.rules).message.required = true];
}

// create capacity override response
message CreateCapacityOverrideResponse {
  // capacity overrides
  repeated moego.models.online_booking.v1.CapacityOverrideModel capacity_overrides = 1;
}

// delete capacity override request
message DeleteCapacityOverrideRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id, optional
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // capacity override id
  int64 id = 3 [(validate.rules).int64 = {gt: 0}];
}

// delete capacity override response
message DeleteCapacityOverrideResponse {
  // capacity overrides
  repeated moego.models.online_booking.v1.CapacityOverrideModel capacity_overrides = 1;
}

// update capacity override request
message UpdateCapacityOverrideRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id, optional
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // capacity override
  moego.models.online_booking.v1.CapacityOverrideDef capacity_override = 3 [(validate.rules).message.required = true];
}

// update capacity override response
message UpdateCapacityOverrideResponse {
  // capacity overrides
  repeated moego.models.online_booking.v1.CapacityOverrideModel capacity_overrides = 1;
}

// the ob_availability_setting service
service OBAvailabilitySettingService {
  // get boarding service availability setting
  rpc GetBoardingServiceAvailabilitySetting(GetBoardingServiceAvailabilitySettingRequest) returns (GetBoardingServiceAvailabilitySettingResponse) {}
  // update boarding service availability setting
  rpc UpdateBoardingServiceAvailabilitySetting(UpdateBoardingServiceAvailabilitySettingRequest) returns (UpdateBoardingServiceAvailabilitySettingResponse) {}
  // get daycare service availability setting
  rpc GetDaycareServiceAvailabilitySetting(GetDaycareServiceAvailabilitySettingRequest) returns (GetDaycareServiceAvailabilitySettingResponse) {}
  // update daycare service availability setting
  rpc UpdateDaycareServiceAvailabilitySetting(UpdateDaycareServiceAvailabilitySettingRequest) returns (UpdateDaycareServiceAvailabilitySettingResponse) {}
  // query available pet type
  rpc QueryAvailablePetType(QueryAvailablePetTypeRequest) returns (QueryAvailablePetTypeResponse) {}
  // query available booking date range
  rpc QueryAvailableBookingDateRange(QueryAvailableBookingDateRangeRequest) returns (QueryAvailableBookingDateRangeResponse) {}

  // get evaluation service availability
  rpc GetEvaluationServiceAvailability(GetEvaluationServiceAvailabilityRequest) returns (GetEvaluationServiceAvailabilityResponse) {}
  // update evaluation service availability
  rpc UpdateEvaluationServiceAvailability(UpdateEvaluationServiceAvailabilityRequest) returns (UpdateEvaluationServiceAvailabilityResponse) {}

  // get grooming service availability¡
  rpc GetGroomingServiceAvailability(GetGroomingServiceAvailabilityRequest) returns (GetGroomingServiceAvailabilityResponse) {}
  // update grooming service availability
  rpc UpdateGroomingServiceAvailability(UpdateGroomingServiceAvailabilityRequest) returns (UpdateGroomingServiceAvailabilityResponse) {}

  // list available booking time range
  rpc ListAvailableBookingTimeRange(ListAvailableBookingTimeRangeRequest) returns (ListAvailableBookingTimeRangeResponse) {}

  // list accepted customer setting
  rpc ListAcceptedCustomerSetting(ListAcceptedCustomerSettingRequest) returns (ListAcceptedCustomerSettingResponse) {}
  // update accepted customer setting, temporary for migration settings
  rpc UpdateAcceptedCustomerSetting(UpdateAcceptedCustomerSettingRequest) returns (UpdateAcceptedCustomerSettingResponse) {}

  // list arrival pick up time overrides
  rpc ListArrivalPickUpTimeOverrides(ListArrivalPickUpTimeOverridesRequest) returns (ListArrivalPickUpTimeOverridesResponse) {}
  // batch create arrival pick up time override
  rpc BatchCreateArrivalPickUpTimeOverride(BatchCreateArrivalPickUpTimeOverrideRequest) returns (BatchCreateArrivalPickUpTimeOverrideResponse) {}
  // batch delete arrival pick up time override
  rpc BatchDeleteArrivalPickUpTimeOverride(BatchDeleteArrivalPickUpTimeOverrideRequest) returns (BatchDeleteArrivalPickUpTimeOverrideResponse) {}
  // batch update arrival pick up time override
  rpc BatchUpdateArrivalPickUpTimeOverride(BatchUpdateArrivalPickUpTimeOverrideRequest) returns (BatchUpdateArrivalPickUpTimeOverrideResponse) {}

  // list capacity override
  rpc ListCapacityOverrides(ListCapacityOverridesRequest) returns (ListCapacityOverridesResponse) {}
  // create capacity override
  rpc CreateCapacityOverride(CreateCapacityOverrideRequest) returns (CreateCapacityOverrideResponse) {}
  // delete capacity override
  rpc DeleteCapacityOverride(DeleteCapacityOverrideRequest) returns (DeleteCapacityOverrideResponse) {}
  // update capacity override
  rpc UpdateCapacityOverride(UpdateCapacityOverrideRequest) returns (UpdateCapacityOverrideResponse) {}
}
