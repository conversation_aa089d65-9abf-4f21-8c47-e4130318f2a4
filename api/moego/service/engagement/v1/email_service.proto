syntax = "proto3";

package moego.service.engagement.v1;

import "moego/models/engagement/v1/email_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/engagement/v1;engagementsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.engagement.v1";

// EmailService is a service that provides email-related functionality.
service EmailService {
  // GetSenderEmail gets the sender email.
  rpc GetSenderEmail(GetSenderEmailRequest) returns (GetSenderEmailResponse) {}
  // SendConfirmEmail sends a confirmation email.
  rpc SendConfirmEmail(SendConfirmEmailRequest) returns (SendConfirmEmailResponse) {}
  // ConfirmSenderEmail confirms the sender email.
  rpc ConfirmSenderEmail(ConfirmSenderEmailRequest) returns (ConfirmSenderEmailResponse) {}
  // FetchDNSConfigs fetches the DNS configurations.
  rpc FetchDNSConfigs(FetchDNSConfigsRequest) returns (FetchDNSConfigsResponse) {}
  // VerifySenderEmail
  rpc VerifySenderEmail(VerifySenderEmailRequest) returns (VerifySenderEmailResponse) {}
  // SaveSenderEmail
  rpc SaveSenderEmail(SaveSenderEmailRequest) returns (SaveSenderEmailResponse) {}
}

// GetSenderEmailRequest is a request to get the sender email.
message GetSenderEmailRequest {
  // business_id is the ID of the business.
  int64 business_id = 1;
}

// GetSenderEmailResponse is a response to get the sender email.
message GetSenderEmailResponse {
  // sender_email is the sender email.
  optional moego.models.engagement.v1.SenderEmailModel sender_email = 1;
  // SenderEmailUsageType
  moego.models.engagement.v1.SenderEmailUsageType sender_email_usage_type = 2;
}

// SendConfirmEmailRequest is a request to send a confirmation email.
message SendConfirmEmailRequest {
  // business id
  int64 business_id = 1;
  // email address
  string email = 2;
}

// SendConfirmEmailResponse is a response to send a confirmation email.
message SendConfirmEmailResponse {
  // success is true if the email was sent successfully.
  bool success = 1;
  // error is the error message if the email was not sent successfully.
  optional string error = 2;
}

// ConfirmSenderEmailRequest is a request to confirm the sender email.
message ConfirmSenderEmailRequest {
  // business id
  int64 business_id = 1;
  // email address
  string email = 2;
  // confirmation code
  string confirmation_code = 3;
}

// ConfirmSenderEmailResponse is a response to confirm the sender email.
message ConfirmSenderEmailResponse {
  // success is true if the email was confirmed successfully.
  bool success = 1;
  // error is the error message if the email was not confirmed successfully.
  optional string error = 2;
}

// FetchDNSConfigsRequest is a request to fetch the DNS configurations.
message FetchDNSConfigsRequest {
  // business id
  int64 business_id = 1;
  // email address
  optional string email = 2;
}

// FetchDNSConfigsResponse is a response to fetch the DNS configurations.
message FetchDNSConfigsResponse {
  // dns_records is the DNS records.
  repeated moego.models.engagement.v1.DNSRecordModel dns_records = 1;
}

// VerifySenderEmailRequest is a request to verify the sender email.
message VerifySenderEmailRequest {
  // business id
  int64 business_id = 1;
  // email address, 传了的话就校验指定 email, 没传的话校验 business 当前 email
  optional string email = 2;
}

// VerifySenderEmailResponse is a response to verify the sender email.
message VerifySenderEmailResponse {
  // success is true if the email was verified successfully.
  bool success = 1;
  // error is the error message if the email was not verified successfully.
  optional string error = 2;
}

// SaveSenderEmailRequest is a request to save the sender email.
message SaveSenderEmailRequest {
  // business id
  int64 business_id = 1;
  // email address
  optional string email = 2;
  // name
  optional string name = 3;
  // type: default 时上面两个字段可以不传，否则需要传 email 和 name
  moego.models.engagement.v1.SenderEmailUsageType type = 4;
}

// SaveSenderEmailResponse is a response to save the sender email.
message SaveSenderEmailResponse {
  // success is true if the email was saved successfully.
  bool success = 1;
  // error is the error message if the email was not saved successfully.
  optional string error = 2;
  // sender_email is the sender email.
  optional moego.models.engagement.v1.SenderEmailModel sender_email = 3;
}
