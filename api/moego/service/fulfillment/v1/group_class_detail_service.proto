// @since 2025-04-07 15:32:56
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.fulfillment.v1;

import "google/type/date.proto";
import "moego/models/fulfillment/v1/group_class_detail_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/fulfillment/v1;fulfillmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.fulfillment.v1";

// list group_class_detail request
message ListGroupClassDetailsRequest {
  // The filter
  Filter filter = 1 [(validate.rules).message.required = true];

  // The filter message
  message Filter {
    // The fulfillment id
    repeated int64 fulfillment_ids = 1 [(validate.rules).repeated = {
      min_items: 0
      max_items: 1000
      items: {
        int64: {gt: 0}
      }
    }];

    // The group class instance id
    repeated int64 group_class_instance_ids = 2 [(validate.rules).repeated = {
      min_items: 0
      max_items: 1000
      items: {
        int64: {gt: 0}
      }
    }];

    // The pet ID
    repeated int64 pet_ids = 3 [(validate.rules).repeated = {
      min_items: 0
      max_items: 1000
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];

    // The status
    repeated moego.models.fulfillment.v1.GroupClassDetailModel.Status statuses = 4 [(validate.rules).repeated = {
      min_items: 0
      max_items: 10
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];
  }
}

// list group_class_detail response
message ListGroupClassDetailsResponse {
  // the group_class_detail
  repeated moego.models.fulfillment.v1.GroupClassDetailModel group_class_details = 1;
}

// The request message for listing pets enrolled in a group class instance
message ListEnrolledPetsRequest {
  // The group class instance id
  repeated int64 group_class_instance_id = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// The response message for listing pets enrolled in a group class instance
message ListEnrolledPetsResponse {
  // the group_class_detail
  repeated moego.models.fulfillment.v1.GroupClassDetailModel group_class_details = 1;
}

// The request message for listing unchecked in pets
message ListUncheckInPetsRequest {
  // The company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // The business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];

  // The session date
  google.type.Date date = 3;

  // The session id
  optional int64 group_class_session_id = 4 [(validate.rules).int64.gt = 0];
}

// The response message for listing unchecked in pets
message ListUncheckInPetsResponse {
  // The unchecked in pets and sessions
  repeated PetSession pet_sessions = 1;

  // The pet session
  message PetSession {
    // The pet id
    int64 pet_id = 1;

    // The group class session id
    int64 group_class_session_id = 2;
  }
}

// The request for CheckInGroupClassSession
message CheckInGroupClassSessionRequest {
  // The business ID
  int64 business_id = 1 [(validate.rules).int64.gt = 0];

  // The pet's ID
  repeated int64 pet_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // The group class session ID
  optional int64 group_class_session_id = 3 [(validate.rules).int64.gt = 0];

  // The company ID
  int64 company_id = 4 [(validate.rules).int64.gt = 0];

  // The check in staff ID
  int64 check_in_staff_id = 5 [(validate.rules).int64.gt = 0];
}

// The response for CheckInGroupClassSession
message CheckInGroupClassSessionResponse {
  // Checked in pet IDs
  repeated int64 checked_in_pet_ids = 1;

  // Checked in group class instance ID
  repeated int64 checked_in_instance_ids = 2;
}

// the group_class_detail service
service GroupClassDetailService {
  // list group_class_detail
  rpc ListGroupClassDetails(ListGroupClassDetailsRequest) returns (ListGroupClassDetailsResponse);

  // List pets enrolled in a group class instance
  rpc ListEnrolledPets(ListEnrolledPetsRequest) returns (ListEnrolledPetsResponse);

  // List uncheck in pets
  rpc ListUncheckInPets(ListUncheckInPetsRequest) returns (ListUncheckInPetsResponse);

  // Check in group class session
  // It will be retrieved within the current business day
  rpc CheckInGroupClassSession(CheckInGroupClassSessionRequest) returns (CheckInGroupClassSessionResponse);
}
