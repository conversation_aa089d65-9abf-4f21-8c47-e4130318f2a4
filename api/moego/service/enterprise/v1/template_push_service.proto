syntax = "proto3";

package moego.service.enterprise.v1;

import "moego/models/enterprise/v1/template_push.proto";
import "moego/models/organization/v1/organization_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1;enterprisesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.enterprise.v1";

// TemplatePushService
service TemplatePushService {
  // ListTemplatePushMappings
  rpc ListTemplatePushMappings(ListTemplatePushMappingsRequest) returns (ListTemplatePushMappingsResponse) {}
  // UpsertTemplatePushMapping
  rpc UpsertTemplatePushMapping(UpsertTemplatePushMappingRequest) returns (UpsertTemplatePushMappingResponse) {}
}

// ListTemplatePushMappingsRequest
message ListTemplatePushMappingsRequest {
  // filter
  message Filter {
    // target organization type
    repeated moego.models.organization.v1.OrganizationType target_organization_types = 1 [(validate.rules).repeated = {max_items: 1000}];
    // target organization ids
    repeated int64 target_organization_ids = 2 [(validate.rules).repeated = {max_items: 1000}];
    // template types
    repeated moego.models.enterprise.v1.TemplateType template_types = 3 [(validate.rules).repeated = {max_items: 1000}];
    // template ids
    repeated int64 template_ids = 4 [(validate.rules).repeated = {max_items: 1000}];
  }
  // filter
  Filter filter = 1;
}

// ListTemplatePushMappingsResponse
message ListTemplatePushMappingsResponse {
  // template push mappings
  repeated moego.models.enterprise.v1.TemplatePushMapping template_push_mappings = 1;
}

// UpsertTemplatePushMappingRequest
message UpsertTemplatePushMappingRequest {
  // upsert def, template_type, template_id and target_company_id are used to identify the mapping
  // if the mapping exists, target_id will be updated, otherwise a new mapping will be created
  // template type
  moego.models.enterprise.v1.TemplateType template_type = 1;
  // template id
  int64 template_id = 2 [(validate.rules).int64 = {gt: 0}];
  // target organization type
  moego.models.organization.v1.OrganizationType target_organization_type = 3 [(validate.rules).enum = {not_in: 0}];
  // target organization id
  int64 target_organization_id = 4 [(validate.rules).int64 = {gt: 0}];
  // target id
  int64 target_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// UpsertTemplatePushMappingResponse
message UpsertTemplatePushMappingResponse {
  // template push mapping
  moego.models.enterprise.v1.TemplatePushMapping template_push_mapping = 1;
}
