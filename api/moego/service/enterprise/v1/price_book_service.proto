syntax = "proto3";

package moego.service.enterprise.v1;

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "moego/models/enterprise/v1/price_book_models.proto";
import "moego/models/enterprise/v1/template_push.proto";
import "moego/models/enterprise/v1/tenant_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1;enterprisesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.enterprise.v1";

// price book service
service PriceBookService {
  // list price books
  rpc ListPriceBooks(ListPriceBooksRequest) returns (ListPriceBooksResponse);
  // create and update existed categories
  rpc SaveServiceCategories(SaveServiceCategoriesRequest) returns (SaveServiceCategoriesResponse) {}
  // list service categories
  rpc ListServiceCategories(ListServiceCategoriesRequest) returns (ListServiceCategoriesResponse) {}
  // list pet breeds
  rpc ListPetBreeds(ListPetBreedsRequest) returns (ListPetBreedsResponse) {}
  // list pet types
  rpc ListPetTypes(ListPetTypesRequest) returns (ListPetTypesResponse) {}
  // create a service
  rpc CreateService(CreateServiceRequest) returns (CreateServiceResponse) {}
  // get a service
  rpc GetService(GetServiceRequest) returns (GetServiceResponse) {}
  // list services
  rpc ListServices(ListServicesRequest) returns (ListServicesResponse) {}
  // update a service
  rpc UpdateService(UpdateServiceRequest) returns (UpdateServiceResponse) {}
  // delete a service
  rpc DeleteService(DeleteServiceRequest) returns (DeleteServiceResponse) {}
  // sort services
  rpc SortServices(SortServicesRequest) returns (SortServicesResponse) {}
  // list service change histories
  rpc ListServiceChangeHistories(ListServiceChangeHistoriesRequest) returns (ListServiceChangeHistoriesResponse) {}
  // list service changes
  rpc ListServiceChanges(ListServiceChangesRequest) returns (ListServiceChangesResponse) {}
  // push service changes
  rpc PushServiceChanges(PushServiceChangesRequest) returns (PushServiceChangesResponse) {}
}

// ListPriceBooksRequest
message ListPriceBooksRequest {
  // enterprise id
  int64 enterprise_id = 1;
}

// ListPriceBooksResponse
message ListPriceBooksResponse {
  // price books
  repeated moego.models.enterprise.v1.PriceBook price_books = 1;
}

// SaveServiceCategoriesRequest
message SaveServiceCategoriesRequest {
  // enterprise id
  int64 enterprise_id = 1;
  // categories
  repeated moego.models.enterprise.v1.ServiceCategory categories = 2;
  // service type
  moego.models.offering.v1.ServiceType service_type = 3;
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 4;
}

// SaveServiceCategoriesResponse
message SaveServiceCategoriesResponse {}

// ListServiceCategoriesRequest
message ListServiceCategoriesRequest {
  // filter
  message Filter {
    // enterprise ids
    repeated int64 enterprise_ids = 1;
    // service item type
    repeated moego.models.offering.v1.ServiceItemType service_item_types = 2;
    // price book ids
    repeated int64 price_book_ids = 3;
    // service type
    repeated moego.models.offering.v1.ServiceType service_types = 4;
  }
  // page
  utils.v2.PaginationRequest pagination = 1;
  // filter
  Filter filter = 2;
}

// ListServiceCategoriesResponse
message ListServiceCategoriesResponse {
  // service categories
  repeated moego.models.enterprise.v1.ServiceCategory service_categories = 1;
}

// ListPetBreedsRequest
message ListPetBreedsRequest {
  // enterprise id
  int64 enterprise_id = 1;
}

// ListPetBreedsResponse
message ListPetBreedsResponse {
  // pet breeds
  repeated moego.models.enterprise.v1.PetBreed pet_breeds = 1;
}

// ListPetTypesRequest
message ListPetTypesRequest {
  // enterprise id
  int64 enterprise_id = 1;
}

// ListPetTypesResponse
message ListPetTypesResponse {
  // pet breeds
  repeated moego.models.enterprise.v1.PetType pet_types = 1;
}

// CreateServiceRequest
message CreateServiceRequest {
  // enterprise id
  int64 enterprise_id = 1;
  // price book
  moego.models.enterprise.v1.PriceBook price_book = 2;
  // name
  string name = 3;
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 4;
  // category
  moego.models.enterprise.v1.ServiceCategory category = 5;
  // description
  string description = 6;
  // inactive
  bool inactive = 7;
  // color
  string color = 8;
  // sort
  int64 sort = 9;
  // price
  google.type.Money price = 10;
  // service price unit
  moego.models.offering.v1.ServicePriceUnit service_price_unit = 11;
  // 万分位税率
  int64 tax_rate = 12;
  // duration
  google.protobuf.Duration duration = 13;
  // max duration
  google.protobuf.Duration max_duration = 14;
  // limitation
  moego.models.enterprise.v1.Service.Limitation limitation = 15;
  // service type
  moego.models.offering.v1.ServiceType service_type = 16;
  // images
  repeated string images = 17;
}

// CreateServiceResponse
message CreateServiceResponse {
  // service
  moego.models.enterprise.v1.Service service = 1;
}

// GetServiceRequest
message GetServiceRequest {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// GetServiceResponse
message GetServiceResponse {
  // service
  moego.models.enterprise.v1.Service service = 1;
}

// ListServicesRequest
message ListServicesRequest {
  // filter
  message Filter {
    // enterprise ids
    repeated int64 enterprise_ids = 1;
    // service item type
    repeated moego.models.offering.v1.ServiceItemType service_item_types = 2;
    // inactive
    bool inactive = 3;
    // category ids
    repeated int64 category_ids = 4;
    // price book ids
    repeated int64 price_book_ids = 5;
    // service type
    repeated moego.models.offering.v1.ServiceType service_types = 6;
  }
  // page
  utils.v2.PaginationRequest pagination = 1;
  // filter
  Filter filter = 2;
}

// ListServicesResponse
message ListServicesResponse {
  // page
  utils.v2.PaginationResponse pagination = 1;
  // services
  repeated moego.models.enterprise.v1.Service services = 2;
}

// UpdateServiceRequest
message UpdateServiceRequest {
  // id
  int64 id = 1;
  // name
  optional string name = 2;
  // category id
  moego.models.enterprise.v1.ServiceCategory service_category = 3;
  // description
  optional string description = 4;
  // inactive
  bool inactive = 5;
  // color
  optional string color = 6;
  // price
  google.type.Money price = 7;
  // service price unit
  moego.models.offering.v1.ServicePriceUnit service_price_unit = 8;
  // 万分位税率
  optional int64 tax_rate = 9;
  // duration
  google.protobuf.Duration duration = 10;
  // max duration
  google.protobuf.Duration max_duration = 11;
  // limitation
  moego.models.enterprise.v1.Service.Limitation limitation = 12;
  // images
  repeated string images = 13;
}

// UpdateServiceResponse
message UpdateServiceResponse {
  // service
  moego.models.enterprise.v1.Service service = 1;
}

// DeleteServiceRequest
message DeleteServiceRequest {
  // id
  int64 id = 1;
}

// DeleteServiceResponse
message DeleteServiceResponse {}

// SortServicesRequest
message SortServicesRequest {
  // service category sort
  message ServiceCategorySort {
    // id
    int64 id = 1;
    // enterprise id
    repeated int64 service_ids = 2;
  }
  // sorted services
  repeated ServiceCategorySort service_category_sorts = 1;
}

// SortServicesResponse
message SortServicesResponse {}

// ListServiceChangeHistoriesRequest
message ListServiceChangeHistoriesRequest {
  // filter
  message Filter {
    // enterprise ids
    repeated int64 enterprise_ids = 1;
    // service ids
    repeated int64 service_ids = 2;
  }
  // order by
  message OrderBy {
    // field
    enum Field {
      // 未指定
      FIELD_UNSPECIFIED = 0;
      // 创建时间
      UPDATED_AT = 1;
    }
    // field
    Field field = 1;
    // asc
    bool asc = 2;
  }
  // page
  utils.v2.PaginationRequest pagination = 1;
  // filter
  Filter filter = 2;
  // order by
  OrderBy order_by = 3 [deprecated = true];
  // template push change order by
  repeated moego.models.enterprise.v1.TemplatePushChangeHistoryOrderBy order_bys = 4;
}

// ListServiceChangeHistoriesResponse
message ListServiceChangeHistoriesResponse {
  // page
  utils.v2.PaginationResponse pagination = 1;
  // service change histories
  repeated moego.models.enterprise.v1.ServiceChangeHistory service_change_histories = 2;
}

// ListServiceChangesRequest
message ListServiceChangesRequest {
  // filter
  message Filter {
    // enterprise ids
    repeated int64 enterprise_ids = 1;
    // service ids
    repeated int64 service_ids = 2;
    // history ids
    repeated int64 history_ids = 3;
  }
  // page
  utils.v2.PaginationRequest pagination = 1;
  // filter
  Filter filter = 2;
}

// ListServiceChangesResponse
message ListServiceChangesResponse {
  // page
  utils.v2.PaginationResponse pagination = 1;
  // service changes
  repeated moego.models.enterprise.v1.ServiceChange service_changes = 2;
}

// PushServiceChangesRequest
message PushServiceChangesRequest {
  // enterprise id
  int64 enterprise_id = 1;
  // service ids
  repeated int64 service_ids = 2;
  // targets
  repeated moego.models.enterprise.v1.TenantObject targets = 3;
  // effective date
  google.protobuf.Timestamp effective_date = 4;
  // apply to booked services
  bool apply_to_booked_services = 5;
}

// PushServiceChangesResponse
message PushServiceChangesResponse {
  // success company ids
  repeated int64 success_company_ids = 1;
  // failed company ids
  repeated int64 failed_company_ids = 2;
}
