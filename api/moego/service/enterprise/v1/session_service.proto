syntax = "proto3";

package moego.service.enterprise.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1;enterprisesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.enterprise.v1";

// InitSessionRequest
message InitSessionRequest {
  // parent session id, if not set, it will be a new session
  optional int64 session_id = 1 [(validate.rules).int64.gt = 0];
  // account id, if session_id is not set, use it to create a new session
  int64 account_id = 2;
}

// EnterpriseSessionData
message EnterpriseSessionData {
  // enterprise id
  int64 enterprise_id = 1;
  // enterprise staff id
  int64 staff_id = 2;
}

// InitSessionResponse
message InitSessionResponse {
  // session id, if session_id is not set in request, it will be a new session id
  int64 session_id = 1;
  // account id
  int64 account_id = 2;
  // session data
  EnterpriseSessionData session_data = 3;
}

// EnterpriseSessionService
service EnterpriseSessionService {
  // InitSession, create a new session or inherit from parent session with enterprise session data
  rpc InitSession(InitSessionRequest) returns (InitSessionResponse);
}
