syntax = "proto3";

package moego.service.split_payment.v1;

import "google/protobuf/empty.proto";
import "google/type/money.proto";
import "moego/models/split_payment/v1/split_payment_enums.proto";
import "moego/models/split_payment/v1/split_payment_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/split_payment/v1;splitpaymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.split_payment.v1";

// split payment request
message SplitPaymentRequest {
  // split entity - payment
  moego.models.split_payment.v1.SplitEntity split_entity = 1 [(validate.rules).message.required = true];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // total amount
  google.type.Money total_amount = 3 [(validate.rules).message.required = true];
  // platform split list
  repeated moego.models.split_payment.v1.SplitDetailModel platform_split_list = 4;
  // currency
  string currency = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 32
  }];
  // transfer group
  optional string transfer_group = 6;
  // source channel payment id
  optional string source_channel_payment_id = 7;
  // source channel payment type (PaymentIntent, Charge)
  optional string source_channel_payment_type = 8;
  // source channel payment method (bank_account, card)
  optional string source_channel_payment_method = 9;
  // vendor
  moego.models.split_payment.v1.Vendor vendor = 10;
}

// split payment response
message SplitPaymentResponse {
  // split payment result
  bool result = 1;
  // result code
  string result_code = 2;
  // result msg
  string result_msg = 3;
}

// reverse split payment request
message ReverseSplitPaymentRequest {
  // refund /dispute
  moego.models.split_payment.v1.SplitEntity reverse_split_entity = 1 [(validate.rules).message.required = true];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // reverse amount
  google.type.Money reverse_amount = 3 [(validate.rules).message.required = true];
  // platform reverse list
  repeated moego.models.split_payment.v1.SplitDetailModel platform_reverse_list = 4;
  // currency
  string currency = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 32
  }];
  // transfer group
  optional string transfer_group = 6;
  // source channel payment id
  optional string source_channel_reverse_id = 7;
  // source channel reverse type (Refund, Charge, TransferReversal)
  optional string source_channel_reverse_type = 8;
  // source channel reverse method (bank_account, card)
  optional string source_channel_reverse_method = 9;
  // vendor
  moego.models.split_payment.v1.Vendor vendor = 10;
  // source split entity
  moego.models.split_payment.v1.SplitEntity source_split_entity = 11 [(validate.rules).message.required = true];
}

// reverse split payment response
message ReverseSplitPaymentResponse {
  // reverse split payment result
  bool result = 1;
  // result code
  string result_code = 2;
  // result msg
  string result_msg = 3;
}

// get split details request
message GetSplitDetailsRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // payment id
  int64 payment_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get split details response
message GetSplitDetailsResponse {
  // split detail list
  repeated moego.models.split_payment.v1.SplitDetailModel split_detail_list = 1;
}

// get refund details request
message GetRefundDetailsRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // refund amount
  google.type.Money amount = 2 [(validate.rules).message.required = true];
  // payment id
  int64 payment_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// get refund details response
message GetRefundDetailsResponse {
  // refund detail list
  repeated moego.models.split_payment.v1.SplitDetailModel refund_detail_list = 1;
}

// batch get detail request
message BatchGetDetailRequest {
  // external transaction id list
  repeated string external_transaction_id_list = 1;
  // external type
  moego.models.split_payment.v1.ExternalType external_type = 2;
}

// batch get detail response
message BatchGetDetailResponse {
  // split detail list
  repeated moego.models.split_payment.v1.SplitDetailModel detail_list = 1;
}

// AcquireSplitAmountRequest is the request for getting the split details
message AcquireSplitAmountRequest {
  // split record id
  string split_record_id = 1;
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // the amount to be split
  google.type.Money amount = 3;
  // payment id
  int64 payment_id = 4;
}

// AcquireSplitAmountResponse is the response for getting the split details
message AcquireSplitAmountResponse {
  // the split details
  repeated moego.models.split_payment.v1.SplitDetailModel split_list = 1;
}

// AcquireReverseSplitAmountRequest is the request for getting the reverse split details
message AcquireReverseSplitAmountRequest {
  // split reverse record id
  string split_reverse_record_id = 1;
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // the amount to be split
  google.type.Money amount = 3;
}

// AcquireReverseSplitAmountResponse is the response for getting the reverse split details
message AcquireReverseSplitAmountResponse {
  // the split details
  repeated moego.models.split_payment.v1.SplitDetailModel split_list = 1;
}

// The service for split payment
service SplitPaymentService {
  // split payment
  rpc SplitPayment(SplitPaymentRequest) returns (SplitPaymentResponse);
  // reverse split payment
  rpc ReverseSplitPayment(ReverseSplitPaymentRequest) returns (ReverseSplitPaymentResponse);
  // sync reverse split payment
  rpc SyncReverseSplitPayment(ReverseSplitPaymentRequest) returns (ReverseSplitPaymentResponse);
  // get split payment details
  rpc GetSplitDetailList(GetSplitDetailsRequest) returns (GetSplitDetailsResponse);
  // get refund details
  rpc GetRefundDetailList(GetRefundDetailsRequest) returns (GetRefundDetailsResponse);
  // retry split payment for task
  rpc RetrySplitPaymentForTask(google.protobuf.Empty) returns (google.protobuf.Empty);
  // batch get detail
  rpc BatchGetDetailList(BatchGetDetailRequest) returns (BatchGetDetailResponse);
  // retry batch charge
  rpc RetryBatchChargeForTask(google.protobuf.Empty) returns (google.protobuf.Empty);
  // batch charge
  rpc BatchChargeForTask(google.protobuf.Empty) returns (google.protobuf.Empty);
}
