syntax = "proto3";

package moego.service.agreement.v1;

import "moego/models/agreement/v1/agreement_enums.proto";
import "moego/models/agreement/v1/agreement_models.proto";
import "moego/utils/v1/status_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/agreement/v1;agreementsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.agreement.v1";

// check agreement input
message CheckAgreementRequest {
  // id
  int64 id = 1;
  // business id
  int64 business_id = 2;
}

// get agreement input
message GetAgreementRequest {
  // id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // company id, if set, will check the company id instead of business id
  optional int64 company_id = 3;
}

// delete agreement request
message DeleteAgreementRequest {
  // id
  int64 id = 1;
  // business id
  int64 business_id = 2;
}

// DeleteAgreementResponse
message DeleteAgreementResponse {
  // number of delete
  int32 number = 1;
}

// CheckAgreementResponse
message CheckAgreementResponse {
  // result of check
  bool result = 1;
  // message
  string msg = 2;
}

// GetAgreementListRequest
message GetAgreementListRequest {
  // business id
  int64 business_id = 1;
  // agreement id list
  repeated int64 ids = 2;
  // status: normal, deleted
  optional moego.utils.v1.Status status = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service type, see definition in ServiceType
  optional int32 service_types = 4 [(validate.rules).int32 = {gt: 0}];
}

// InitAgreementRequest
message InitAgreementRequest {
  // business id
  int64 business_id = 1;
  // business name
  optional string business_name = 2 [(validate.rules).string = {max_len: 256}];
  // creator id
  optional int64 creator_id = 3;
  // company id
  int64 company_id = 4;
}

// AddAgreementRequest
message AddAgreementRequest {
  // business id
  int64 business_id = 1;
  // creator id
  int64 creator_id = 2;
  // signed policy, see definition in SignedPolicy
  moego.models.agreement.v1.SignedPolicy signed_policy = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service type, see definition in ServiceType
  int32 service_types = 4 [(validate.rules).int32 = {gt: 0}];
  // agreement title
  optional string agreement_title = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // agreement content
  optional string agreement_content = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 1048576
  }];
  // template for send sms
  optional string sms_template = 7 [(validate.rules).string = {
    min_len: 1
    max_len: 65536
  }];
  // email template title
  optional string email_template_title = 8 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // email template body
  optional string email_template_body = 9 [(validate.rules).string = {
    min_len: 1
    max_len: 131072
  }];
  // business name
  optional string business_name = 10 [(validate.rules).string = {max_len: 256}];
  // company id
  int64 company_id = 11;
}

// UpdateAgreementRequest
message UpdateAgreementRequest {
  // agreement id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // signed policy, see definition in SignedPolicy
  optional moego.models.agreement.v1.SignedPolicy signed_policy = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // agreement title
  optional string agreement_title = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // agreement content
  optional string agreement_content = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 1048576
  }];
  // template for send sms
  optional string sms_template = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 65536
  }];
  // email template title
  optional string email_template_title = 7 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // email template body
  optional string email_template_body = 8 [(validate.rules).string = {
    min_len: 1
    max_len: 131072
  }];
  // whether to update last_required_time
  optional bool update_last_required_time = 9;
}

// UpdateServiceTypeRequest
message UpdateServiceTypeRequest {
  // agreement id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // services type, see the enum definition in ServiceType
  moego.models.agreement.v1.ServiceType service_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // true is set the service_type, false is cancel the service_type
  bool set_or_cancel = 4;
}

// agreement model simple view list
message GetAgreementListResponse {
  // agreement list
  repeated moego.models.agreement.v1.AgreementModelSimpleView agreement_simple_view = 1;
}

// agreement model content view list
message GetAgreementContentListResponse {
  // agreement list
  repeated moego.models.agreement.v1.AgreementModelContentView agreement_content_view = 1;
}

// GetAgreementSignStatusListRequest
message GetAgreementSignStatusListRequest {
  // business id
  int64 business_id = 1;
  // customer id
  int64 customer_id = 2;
  // target id
  optional int64 target_id = 3;
  // services type, see the enum definition in ServiceType
  optional moego.models.agreement.v1.ServiceType service_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// GetAgreementSignStatusListResponse
message GetAgreementSignStatusListResponse {
  // agreement sign status view list
  repeated moego.models.agreement.v1.AgreementSignStatusView agreement_status_view = 1;
}

// BatchGetAgreementSignStatusRequest
message BatchGetAgreementUnsignedAppointmentRequest {
  // business id
  int64 business_id = 1;
  // customer id with appointment id list
  repeated CustomerWithAppointmentId customer_with_appointment_id = 2;

  // customer with appointment
  message CustomerWithAppointmentId {
    // customer id
    int64 customer_id = 1;
    // appointment id
    int64 appointment_id = 2;
  }
}

// BatchGetAgreementSignStatusResponse
message BatchGetAgreementUnsignedAppointmentResponse {
  // need sign map, key - customer id, value - whether need to sign
  repeated int64 appointment_id = 1;
}

// Get agreement content list by company response
message GetAgreementContentListByCompanyResponse {
  // agreement list
  repeated moego.models.agreement.v1.AgreementModelContentView agreement_content_view = 1;
}

// GetAgreementListByCompanyRequest
message GetAgreementListByCompanyRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // agreement id list
  repeated int64 ids = 2;
  // status: normal, deleted
  optional moego.utils.v1.Status status = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service type, see definition in ServiceType
  optional int32 service_types = 4 [(validate.rules).int32 = {gt: 0}];
  // business ids
  repeated int64 business_ids = 5 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    max_items: 500
    unique: true
    ignore_empty: true
  }];
}

// GetAgreementListByCompanyResponse
message GetAgreementListByCompanyResponse {
  // agreement list
  repeated moego.models.agreement.v1.AgreementModelSimpleView agreement_simple_view = 1;
}

// ListAgreementsRequest
message ListAgreementsRequest {
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 1;
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // agreement id list
  repeated int64 ids = 3 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    max_items: 500
    unique: true
    ignore_empty: true
  }];
  // status: normal, deleted
  optional moego.utils.v1.Status status = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service type, see definition in ServiceType
  optional int32 service_types = 5 [(validate.rules).int32 = {gt: 0}];
  // business ids
  repeated int64 business_ids = 6 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    max_items: 500
    unique: true
    ignore_empty: true
  }];
}

// ListAgreementsResponse
message ListAgreementsResponse {
  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 1;
  // appointment detail
  repeated models.agreement.v1.AgreementModel agreements = 2;
}

// ListUnsignedAgreement request
message ListUnsignedAgreementRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business ids
  repeated int64 business_ids = 2 [(validate.rules).repeated = {
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
  // customer id, 如果没有 customer id, 则返回所有 agreement
  optional int64 customer_id = 3 [(validate.rules).int64 = {gt: 0}];
  // service type, see definition in ServiceType
  optional int32 service_types = 4 [(validate.rules).int32 = {gt: 0}];
}

// ListUnsignedAgreement response
message ListUnsignedAgreementResponse {
  // agreement list
  repeated moego.models.agreement.v1.AgreementModel agreements = 1;
}

// ListUnsignedAgreementByCustomersRequest
message ListUnsignedAgreementByCustomersRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // list of business ids
  repeated int64 business_ids = 2 [(validate.rules).repeated = {
    max_items: 100
    items: {
      int64: {gt: 0}
    }
  }];
  // list of customer ids
  repeated int64 customer_ids = 3 [(validate.rules).repeated = {
    max_items: 100
    items: {
      int64: {gt: 0}
    }
  }];
  // service type, see definition in ServiceType
  optional int32 service_types = 4 [(validate.rules).int32 = {gt: 0}];
}

// ListUnsignedAgreementByCustomersResponse
message ListUnsignedAgreementByCustomersResponse {
  // agreement list
  repeated CustomerAgreementView customer_agreements = 1;

  // CustomerAgreementView
  message CustomerAgreementView {
    // customer id
    int64 customer_id = 1;
    // list of agreements
    repeated moego.models.agreement.v1.AgreementModelSimpleView agreements = 2;
  }
}

// AgreementService
service AgreementService {
  // check agreement
  rpc CheckAgreement(CheckAgreementRequest) returns (CheckAgreementResponse);

  // get one agreement
  rpc GetAgreement(GetAgreementRequest) returns (moego.models.agreement.v1.AgreementModel);

  // query agreement list
  rpc GetAgreementList(GetAgreementListRequest) returns (GetAgreementListResponse);

  // query agreement detail list
  rpc GetAgreementContentList(GetAgreementListRequest) returns (GetAgreementContentListResponse);

  // get agreement list for whether need to sign
  rpc GetAgreementSignStatusList(GetAgreementSignStatusListRequest) returns (GetAgreementSignStatusListResponse);

  // init a agreement
  rpc InitAgreement(InitAgreementRequest) returns (moego.models.agreement.v1.AgreementModel);

  // add a agreement
  rpc AddAgreement(AddAgreementRequest) returns (moego.models.agreement.v1.AgreementModel);

  // update a agreement
  rpc UpdateAgreement(UpdateAgreementRequest) returns (moego.models.agreement.v1.AgreementModel);

  // update agreement service type
  rpc UpdateAgreementServiceType(UpdateServiceTypeRequest) returns (moego.models.agreement.v1.AgreementModelSimpleView);

  // delete a agreement
  rpc DeleteAgreement(DeleteAgreementRequest) returns (DeleteAgreementResponse);

  // batch get agreement sign status
  rpc BatchGetAgreementUnsignedAppointment(BatchGetAgreementUnsignedAppointmentRequest) returns (BatchGetAgreementUnsignedAppointmentResponse);

  // get agreement content list by company
  rpc GetAgreementContentListByCompany(GetAgreementListByCompanyRequest) returns (GetAgreementContentListByCompanyResponse);

  // get agreement list by company
  rpc GetAgreementListByCompany(GetAgreementListByCompanyRequest) returns (GetAgreementListByCompanyResponse);

  // list agreements
  rpc ListAgreements(ListAgreementsRequest) returns (ListAgreementsResponse);

  // list customer unsigned agreement
  // 如果没有 customer id, 则返回所有 agreement
  rpc ListUnsignedAgreement(ListUnsignedAgreementRequest) returns (ListUnsignedAgreementResponse);

  // list unsigned agreement by customers
  rpc ListUnsignedAgreementByCustomers(ListUnsignedAgreementByCustomersRequest) returns (ListUnsignedAgreementByCustomersResponse);
}
