syntax = "proto3";

package moego.service.agreement.v1;

import "moego/models/agreement/v1/agreement_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/agreement/v1;agreementsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.agreement.v1";

// the agreement migrate service
service AgreementMigrateService {
  // upsert agreement
  rpc UpsertAgreement(UpsertAgreementRequest) returns (UpsertAgreementResponse);
}

// upsert agreement request
message UpsertAgreementRequest {
  // business_ids
  repeated int64 business_ids = 1;
  // agreement title, used for search
  string agreement_title = 2;
  // agreement content
  string agreement_content = 3;
  // service type
  repeated moego.models.agreement.v1.ServiceType service_types = 4;
  // signed policy
  moego.models.agreement.v1.SignedPolicy signed_policy = 5;
  // source
  moego.models.agreement.v1.Source source = 6;
}

// upsert agreement response
message UpsertAgreementResponse {}
