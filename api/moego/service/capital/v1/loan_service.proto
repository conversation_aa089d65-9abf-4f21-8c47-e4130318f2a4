syntax = "proto3";

package moego.service.capital.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "moego/models/capital/v1/loan_enums.proto";
import "moego/models/capital/v1/loan_models.proto";
import "moego/service/split_payment/v1/split_payment_service.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/capital/v1;capitalsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.capital.v1";

// The event payload of a loan offer event. It is basically a "partial" version of LoanOfferModel.
message LoanOfferEventPayload {
  // The name of the channel that created the offer.
  moego.models.capital.v1.LoanChannel channel_name = 1;
  // The ID of the account in the channel.
  string channel_account_id = 2 [(validate.rules).string.max_len = 64];
  // The ID of the offer in the channel.
  string channel_offer_id = 3 [(validate.rules).string.max_len = 64];
  // The type of the offer, e.g. MCA, Term Loan.
  moego.models.capital.v1.LoanOfferType offer_type = 4;
  // The unix timestamp in seconds when the offer was created.
  google.protobuf.Timestamp created_at = 5 [(validate.rules).timestamp.gte.seconds = 0];
  // The unix timestamp in seconds when the offer expires.
  google.protobuf.Timestamp expire_at = 6 [(validate.rules).timestamp.gte.seconds = 0];
  // The offered terms of the offer.
  LoanOfferEventLoanTerms offered_terms = 7;
  // The accepted terms of the offer.
  optional LoanOfferEventLoanTerms accepted_terms = 8;
  // Financing product identifier. e.g. Standard, Refill.
  moego.models.capital.v1.LoanProductType product_type = 9;
  // The status of the offer in the channel.
  // This field is a backup for original status field from the channel, so we use string type instead of enum.
  // Possible values for Stripe channel: "delivered", "accepted", "canceled", "expired", "fully_repaid", "paid_out",
  // "rejected", "replaced", "undelivered".
  // Kanmon doesn't have a status field for an offer, but only some for issued product. Note that REFINANCED is not
  // supported yet so will not pass validation.
  string channel_status = 10 [(validate.rules).string = {
    max_len: 20
    in: [
      // Begin: Kanmon
      "",
      "CURRENT",
      "LATE",
      "FULLY_PAID",
      "CLOSED",
      "DEFAULTED",
      // Begin: Stripe
      "delivered",
      "accepted",
      "canceled",
      "expired",
      "fully_repaid",
      "paid_out",
      "rejected",
      "replaced",
      "undelivered"
    ]
  }];
}

// LoanOfferEventLoanTerms
message LoanOfferEventLoanTerms {
  // Amount of financing offered, in minor units.
  double advance_amount = 1;
  // Type of campaign, e.g. "newly_eligible_user", "previously_eligible_user", "repeat_user"
  // Not available for the accepted terms.
  optional moego.models.capital.v1.LoanCampaignType campaign_type = 2;
  // Currency code, e.g. "usd", "cad"
  string currency = 3;
  // Fixed fee amount, in minor units.
  double fee_amount = 4;
  // Populated when the product_type is refill.
  // Represents the discount rate percentage on remaining fee on the existing loan.
  // When the financing_offer is paid out, the previous_financing_fee_discount_amount
  // will be computed as the multiple of this rate and the remaining fee.
  // Not available for the accepted terms.
  optional double previous_financing_fee_discount_rate = 5;
  // Per-transaction rate at which Stripe will withhold funds to repay the financing.
  double withhold_rate = 6;
  // Populated when the product type of the offer is refill. Represents the discount amount on remaining premium for the
  // existing loan at payout time.
  // Not available for the offered terms.
  optional double previous_financing_fee_discount_amount = 7;
  // Interest rate, available for Term Loan.
  double interest_rate = 9 [(validate.rules).double.gte = 0];
  // Duration months
  int64 duration_months = 10 [(validate.rules).int64.gte = 0];
  // Interest amount.
  double interest_amount = 11 [(validate.rules).double.gte = 0];
}

// Request for GetLoanEligibilityByEntity
message GetLoanEligibilityRequest {
  // The ID of the business
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
}

// Response for GetLoanEligibility
message GetLoanEligibilityResponse {
  // Whether the specified loan user is eligible for MoeGo capital.
  bool is_eligible = 1;
  // Ineligible reason.
  moego.models.capital.v1.LoanIneligibleReason ineligible_reason = 2;
}

// Request for GetLoanEligibilityFlags
message GetLoanEligibilityFlagsRequest {
  // The ID of the business
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
}

// Response for GetLoanEligibilityFlags
message GetLoanEligibilityFlagsResponse {
  // Whether the user has setup MGP
  bool is_mgp_setup = 1;
  // Whether the user has MGP primary type
  bool is_mgp_primary = 2;
  // Whether the user has MGP primary type long enough
  bool is_mgp_primary_long_enough = 3;
}

// Request for GetOfferList
message GetOfferListRequest {
  // The ID of the business
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
  // The status of the loan offer
  optional moego.models.capital.v1.LoanOfferStatus status = 2;
}

// Response for GetOfferList
message GetOfferListResponse {
  // The list of loan offers.
  repeated moego.models.capital.v1.LoanOfferModel offers = 1;
}

// Request for GetOfferListByCompany
message GetOfferListByCompanyRequest {
  // The ID of the company
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
}

// Response for GetOfferListByCompany
message GetOfferListCompanyResponse {
  // The list of loan offers.
  repeated moego.models.capital.v1.LoanOfferModel offers = 1;
}

// Request for GetOfferByChannel
message GetOfferByChannelRequest {
  // The channel
  moego.models.capital.v1.LoanChannel channel_name = 1;
  // The channel ID of the loan offer
  string channel_offer_id = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }];
}

// Response for GetOfferByChannel
message GetOfferByChannelResponse {
  // The loan offer
  moego.models.capital.v1.LoanOfferModel offer = 1;
}

// Request for CreateLink
message CreateLinkRequest {
  // type of account link
  enum AccountLinkType {
    // Unspecified
    ACCOUNT_LINK_TYPE_UNSPECIFIED = 0;
    // link to apply an offer
    APPLY_OFFER = 1;
    // link to review eligibility and to prove eligibility
    ELIGIBILITY_REVIEW = 2;
    // link to manually repayment
    MANUALLY_REPAY = 3;
  }

  // link type
  AccountLinkType type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // The ID of the loan offer
  optional string offer_id = 3 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 64
  }];
  // The URL to redirect to after the user finishes operations at the apply link site. Require to be validated for
  // APPLY_OFFER, ELIGIBILITY_REVIEW and MANUALLY_REPAY.
  string return_url = 4 [(validate.rules).string = {
    uri: true
    ignore_empty: true
  }];
  // The URL to redirect to if the old link expires. Require to be validated for APPLY_OFFER, ELIGIBILITY_REVIEW and
  // MANUALLY_REPAY.
  string refresh_url = 5 [(validate.rules).string = {
    uri: true
    ignore_empty: true
  }];
}

// Response for CreateLink
message CreateLinkResponse {
  // The URL of the apply link
  string url = 1 [(validate.rules).string = {
    uri: true
    ignore_empty: true
  }];
  // Time when the URL or token will expire, in second timestamp. 0 if it never expires.
  google.protobuf.Timestamp expire_at = 2 [(validate.rules).timestamp.gte.seconds = 0];
  // The token to connect to the link
  optional string connect_token = 3 [(validate.rules).string = {min_len: 1}];
}

// Request for ProcessOfferEvent
message ProcessOfferEventRequest {
  // The type of a loan offer event
  enum OfferEventType {
    // Unspecified
    OFFER_EVENT_TYPE_UNSPECIFIED = 0;
    // The event type that a loan offer is created
    CREATED = 1;
    // The event type that the user submits their offer application
    ACCEPTED = 2;
    // The event type that the capital approves the offer application and funds are paid out to the user
    PAID_OUT = 3;
    // The event type that the user fully repays the financing balance
    FULLY_REPAID = 4;
    // The event type that the user cancels the financing offer
    CANCELED = 5;
    // The event type that the user’s application isn’t approved
    REJECTED = 6;
    // The event type that a loan offer expires and is no longer available
    EXPIRED = 7;
    // The event type that a business is prequalified
    PREQUALIFIED = 8;
  }

  // The event type.
  OfferEventType event_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // The loan channel.
  moego.models.capital.v1.LoanChannel channel_name = 2;
  // The related updated LoanOffer payload.
  LoanOfferEventPayload offer = 3;
}

// Request for ProcessPrequalificationEvent
message ProcessPrequalificationEventRequest {
  // The business ID, given to the channel as a platform-side ID (might be obfuscated).
  string platform_business_id = 1 [(validate.rules).string.min_len = 1];
  // The loan channel.
  moego.models.capital.v1.LoanChannel channel_name = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // The loan product type.
  moego.models.capital.v1.LoanOfferType product_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Prequalified or not.
  bool is_prequalified = 4;
  // Prequalified amount.
  google.type.Money prequalified_amount = 5;
}

// Response for ProcessPrequalificationEvent
message ProcessPrequalificationEventResponse {}

// Request for ProcessAccountEventRequest
message ProcessAccountEventRequest {
  // The account event type
  enum AccountEventType {
    // Unspecified
    ACCOUNT_EVENT_TYPE_UNSPECIFIED = 0;
    // New channel account is created.
    CREATED = 1;
    // Account is onboarded.
    ONBOARDED = 2;
    // Input required.
    INPUT_REQUIRED = 3;
  }

  // Event type.
  AccountEventType event_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // The business ID, given to the channel as a platform-side ID (might be obfuscated).
  string platform_business_id = 2 [(validate.rules).string.min_len = 1];
  // The loan channel.
  moego.models.capital.v1.LoanChannel channel_name = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Requirements, vary by the channel. Available only for INPUT_REQUIRED.
  repeated string requirements = 4;
}

// Response for ProcessAccountEventRequest
message ProcessAccountEventResponse {}

// Request for SyncOfferAndTransactionList
message SyncOfferAndTransactionListRequest {
  // offer_id
  string offer_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }];
}

// Request for GetRepayments
message GetRepaymentListRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
  // The ID of the loan offer
  string offer_id = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }];
  // Pagination query
  moego.utils.v2.PaginationRequest pagination = 3;
}

// Response for GetRepayments
message GetRepaymentListResponse {
  // The list of repayment records.
  repeated moego.models.capital.v1.LoanOfferRepaymentTransactionModel repayments = 1;
  // Pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// Request for GetRepaymentInterval
message GetRepaymentIntervalsRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
  // The ID of the loan offer
  string offer_id = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }];
}

// Response for GetRepaymentInterval
message GetRepaymentIntervalsResponse {
  // The intervals of the loan offer
  repeated moego.models.capital.v1.LoanOfferIntervalModel intervals = 1;
}

// Request for BatchGetRepaymentInterval
message BatchGetRepaymentIntervalsRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
  // The ID of the loan offer
  repeated string offer_ids = 2;
}

// Response for BatchGetBatchRepaymentInterval
message BatchGetRepaymentIntervalsResponse {
  // The intervals of the loan offers
  repeated moego.models.capital.v1.LoanOfferIntervalModel intervals = 1;
}

// Request for GetNotableUpdates
message GetNotableUpdatesRequest {
  // used to get notable info
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
}

// Response for GetNotableUpdates
message GetNotableUpdatesResponse {
  // Whether the user has updates
  bool have_updates = 1;
  // Notable updates for offers
  repeated moego.models.capital.v1.NotableOfferUpdate notable_offer_updates = 2;
}

// Request for GetOfferNotableUpdates
message GetOfferNotableUpdatesRequest {
  // Business ID
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
  // The ID of the offer
  string offer_id = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }];
}

// Response for GetOfferNotableUpdates
message GetOfferNotableUpdatesResponse {
  // Whether the user has updates
  bool have_updates = 1;
}

// Request for DismissNotableUpdates
message DismissNotableUpdatesRequest {
  // used to dismiss  notable info
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
}

// Request for DismissOfferNotableUpdate
message DismissOfferNotableUpdateRequest {
  // used to dismiss  notable info
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
  // The ID of the loan offer, to dismiss the corresponding offer update
  optional string offer_id = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }];
}

// Response for DismissOfferNotableUpdate
message DismissOfferNotableUpdateResponse {}

// Request for GetOnboardingStatus
message GetOnboardingStatusRequest {
  // The channel.
  moego.models.capital.v1.LoanChannel channel_name = 1;
  // The ID of the business
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
}

// Response for GetOnboardingStatus
message GetOnboardingStatusResponse {
  // The onboarding status of the business
  moego.models.capital.v1.LoanOnboardingStatus onboarding_status = 1;
  // The offer type of the business has chosen
  moego.models.capital.v1.LoanOfferType offer_type = 2;
}

// Request for GetOfferDetail
message GetOfferDetailRequest {
  // The ID of the business
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
  // The ID of the loan offer
  string offer_id = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }];
}

// Response for GetOfferDetail
message GetOfferDetailResponse {
  // The loan offer
  moego.models.capital.v1.LoanOfferModel offer = 1;
}

// Request for DeliverPendingMessages
message DeliverPendingMessagesRequest {}

// Response for DeliverPendingMessages
message DeliverPendingMessagesResponse {}

// The service for Loan domain.
service LoanService {
  // Handle external offer update.
  rpc ProcessOfferEvent(ProcessOfferEventRequest) returns (google.protobuf.Empty);
  // Handle external prequalification event.
  rpc ProcessPrequalificationEvent(ProcessPrequalificationEventRequest) returns (ProcessPrequalificationEventResponse);
  // Handle loan account input required event.
  rpc ProcessAccountEvent(ProcessAccountEventRequest) returns (ProcessAccountEventResponse);
  // Get the eligibility flags for current business/company/etc.
  rpc GetLoanEligibilityFlags(GetLoanEligibilityFlagsRequest) returns (GetLoanEligibilityFlagsResponse);
  // Get the eligibility information for current business/company/etc.
  rpc GetLoanEligibility(GetLoanEligibilityRequest) returns (GetLoanEligibilityResponse);
  // Get all Loan Offers for current entity in all status.
  rpc GetOfferList(GetOfferListRequest) returns (GetOfferListResponse);
  // Get all Loan Offers for current company in all status.
  rpc GetOfferListByCompany(GetOfferListByCompanyRequest) returns (GetOfferListCompanyResponse);
  // Get offer by channel type and channel id.
  rpc GetOfferByChannel(GetOfferByChannelRequest) returns (GetOfferByChannelResponse);
  // Create an apply link for a Loan Offer.
  rpc CreateLink(CreateLinkRequest) returns (CreateLinkResponse);
  // Reconcile with vendor to sync Offer records
  rpc SyncOfferAndTransactionList(SyncOfferAndTransactionListRequest) returns (google.protobuf.Empty);
  // Get the repayments for specific Loan Offer.
  rpc GetRepaymentList(GetRepaymentListRequest) returns (GetRepaymentListResponse);
  // Get all intervals for the loan offer.
  rpc GetRepaymentIntervals(GetRepaymentIntervalsRequest) returns (GetRepaymentIntervalsResponse);
  // Get (flatten) intervals for the given offer IDs.
  rpc BatchGetRepaymentIntervals(BatchGetRepaymentIntervalsRequest) returns (BatchGetRepaymentIntervalsResponse);
  // Get updates worth noting.
  rpc GetNotableUpdates(GetNotableUpdatesRequest) returns (GetNotableUpdatesResponse);
  // Dismiss notable updates, if any.
  rpc DismissNotableUpdates(DismissNotableUpdatesRequest) returns (google.protobuf.Empty);
  // Dismiss an offer's notable updates, if any.
  rpc DismissOfferNotableUpdate(DismissOfferNotableUpdateRequest) returns (DismissOfferNotableUpdateResponse);
  // Get the onboarding status for current business/company/etc.
  rpc GetOnboardingStatus(GetOnboardingStatusRequest) returns (GetOnboardingStatusResponse);
  // Get the offer detail for the offer.
  rpc GetOfferDetail(GetOfferDetailRequest) returns (GetOfferDetailResponse);

  // ---------------------- for split payment -------------------------
  // AcquireSplitAmount is used to get the split amount for the offer.
  rpc AcquireSplitAmount(moego.service.split_payment.v1.AcquireSplitAmountRequest) returns (moego.service.split_payment.v1.AcquireSplitAmountResponse);
  // AcquireReverseSplitAmount is used to get the reverse split amount for the offer.
  rpc AcquireReverseSplitAmount(moego.service.split_payment.v1.AcquireReverseSplitAmountRequest) returns (moego.service.split_payment.v1.AcquireReverseSplitAmountResponse);

  // ---------------------- Task -------------------------
  // Save all offers from all channels
  rpc SaveAllOffer(google.protobuf.Empty) returns (google.protobuf.Empty);
  // Reconcile with vendor to sync Offer records
  rpc SyncAllOfferAndTransactionList(google.protobuf.Empty) returns (google.protobuf.Empty);
  // Evaluate eligibility for all loan accounts.
  rpc EvaluateAccountEligibility(google.protobuf.Empty) returns (google.protobuf.Empty);
  // Deliver all pending messages.
  rpc DeliverPendingMessages(DeliverPendingMessagesRequest) returns (DeliverPendingMessagesResponse);
}
