// @since 2023-05-27 21:28:41
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.admin_permission.v1;

import "moego/models/admin_permission/v1/role_models.proto";
import "moego/models/admin_permission/v1/role_permission_defs.proto";
import "moego/models/admin_permission/v1/role_permission_models.proto";
import "moego/utils/v1/struct.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/admin_permission/v1;adminpermissionsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.admin_permission.v1";

// create role req
message CreateRoleRequest {
  // name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // description
  string description = 2 [(validate.rules).string = {max_len: 255}];

  // owner id
  string owner_id = 14 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // operator id
  string operator_id = 15 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
}

// crate role res
message CreateRoleResponse {
  // role
  moego.models.admin_permission.v1.RoleModel role = 1;
}

// get role list
message DescribeRolesRequest {
  // filter by ids
  optional moego.utils.v1.Int64ListValue ids = 1;
  // filter by names
  optional moego.utils.v1.StringListValue names = 2;

  // filter by owner id
  optional string owner_id = 14 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// get role list res
message DescribeRolesResponse {
  // roles
  repeated moego.models.admin_permission.v1.RoleModel roles = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// get role req
message GetRoleRequest {
  // identifier
  oneof identifier {
    option (validate.required) = true;
    // id
    int64 id = 1 [(validate.rules).int64 = {gt: 0}];
    // name
    string name = 2 [(validate.rules).string = {
      min_len: 1
      max_len: 50
    }];
  }

  // filter by owner id
  optional string owner_id = 15 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
}

// get role res
message GetRoleResponse {
  // role model
  moego.models.admin_permission.v1.RoleModel role = 1;
}

// update role req
message UpdateRoleRequest {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // name
  optional string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // description
  optional string description = 3 [(validate.rules).string = {max_len: 255}];

  // filter by owner id
  optional string owner_id = 14 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // operator id
  string operator_id = 15 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
}

// update role res
message UpdateRoleResponse {
  // role
  moego.models.admin_permission.v1.RoleModel role = 1;
}

// delete role req
message DeleteRoleRequest {
  // role id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];

  // filter by owner id
  optional string owner_id = 14 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // operator id
  string operator_id = 15 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
}

// delete role res
message DeleteRoleResponse {}

// create role permission req
message CreateRolePermissionRequest {
  // role id
  int64 role_id = 1 [(validate.rules).int64 = {gt: 0}];
  // perm point
  string permission = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // filters
  repeated moego.models.admin_permission.v1.FilterDef filters = 3 [(validate.rules).repeated = {max_items: 100}];

  // filter role by owner id
  optional string owner_id = 14 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // operator id
  string operator_id = 15 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
}

// create role perm res
message CreateRolePermissionResponse {
  // created role permission
  moego.models.admin_permission.v1.RolePermissionModel role_permission = 1;
}

// update role permission req
message UpdateRolePermissionRequest {
  // for update, id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];

  // perm point
  string permission = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // filters
  repeated moego.models.admin_permission.v1.FilterDef filters = 3 [(validate.rules).repeated = {max_items: 100}];

  // filter role by owner id
  optional string owner_id = 14 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // operator id
  string operator_id = 15 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
}

// update role perm res
message UpdateRolePermissionResponse {
  // updated role permission
  moego.models.admin_permission.v1.RolePermissionModel role_permission = 1;
}

// delete role perm
message DeleteRolePermissionRequest {
  // role permission id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];

  // filter by owner id
  optional string owner_id = 14 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // operator id
  string operator_id = 15 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
}

// delete role permission res
message DeleteRolePermissionResponse {}

// get role permissions req
message DescribeRolePermissionsRequest {
  // role id
  int64 role_id = 1 [(validate.rules).int64 = {gt: 0}];
  // owner id
  optional string owner_id = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
}

// describe role permissions res
message DescribeRolePermissionsResponse {
  // role permissions
  repeated moego.models.admin_permission.v1.RolePermissionModel role_permissions = 1;
}

// the role service
service RoleService {
  // create role
  rpc CreateRole(CreateRoleRequest) returns (CreateRoleResponse);
  // get roles
  rpc DescribeRoles(DescribeRolesRequest) returns (DescribeRolesResponse);
  // get role detail
  rpc GetRole(GetRoleRequest) returns (GetRoleResponse);
  // update role
  rpc UpdateRole(UpdateRoleRequest) returns (UpdateRoleResponse);
  // delete role
  rpc DeleteRole(DeleteRoleRequest) returns (DeleteRoleResponse);

  // create role perm point
  // disallowed state: the perm point or its mutually exclusive points is already in use
  rpc CreateRolePermission(CreateRolePermissionRequest) returns (CreateRolePermissionResponse);
  // get role perm point list
  rpc DescribeRolePermissions(DescribeRolePermissionsRequest) returns (DescribeRolePermissionsResponse);
  // update role perm point
  // disallowed state: the perm point or its mutually exclusive points is already in use by others
  rpc UpdateRolePermission(UpdateRolePermissionRequest) returns (UpdateRolePermissionResponse);
  // delete rol perm point
  rpc DeleteRolePermission(DeleteRolePermissionRequest) returns (DeleteRolePermissionResponse);
}
