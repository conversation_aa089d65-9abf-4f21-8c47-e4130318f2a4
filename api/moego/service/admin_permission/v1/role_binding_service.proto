// @since 2023-05-27 21:29:19
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.admin_permission.v1;

import "moego/models/admin_permission/v1/role_binding_models.proto";
import "moego/models/admin_permission/v1/role_permission_models.proto";
import "moego/utils/v1/struct.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/admin_permission/v1;adminpermissionsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.admin_permission.v1";

// update account role binding
message UpdateRoleBindingsRequest {
  // account id
  string account_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // role ids
  repeated int64 role_ids = 2 [(validate.rules).repeated = {
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // operator
  string operator_id = 15 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
}

// update account role binding response
message UpdateRoleBindingsResponse {}

// describe role bindings request
message DescribeRoleBindingsRequest {
  // filter by account id
  optional moego.utils.v1.StringListValue account_ids = 1;
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// describe role bindings
message DescribeRoleBindingsResponse {
  // account role bindings
  repeated moego.models.admin_permission.v1.RoleBindingsModel role_bindings = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// describe permissions
message DescribeAccountPermissionsRequest {
  // account id
  string account_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // filter by permission points
  repeated string permissions = 2 [(validate.rules).repeated = {
    max_items: 1000
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 100
      }
    }
  }];
}

// describe account permissions
message DescribeAccountPermissionsResponse {
  // permission points
  repeated moego.models.admin_permission.v1.RolePermissionModel permissions = 1;
}

// the role_binding service
service RoleBindingService {
  // fully update account role bindings
  rpc UpdateRoleBindings(UpdateRoleBindingsRequest) returns (UpdateRoleBindingsResponse);
  // describe role bindings list
  rpc DescribeRoleBindings(DescribeRoleBindingsRequest) returns (DescribeRoleBindingsResponse);
  // filter specified permissions
  rpc DescribeAccountPermissions(DescribeAccountPermissionsRequest) returns (DescribeAccountPermissionsResponse);
}
