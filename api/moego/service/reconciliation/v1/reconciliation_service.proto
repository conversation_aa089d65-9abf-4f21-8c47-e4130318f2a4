// @since 2024-07-24 10:22:15
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.reconciliation.v1;

import "google/protobuf/empty.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/reconciliation/v1;reconciliationsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.reconciliation.v1";

// the execute reconciliation request
message ExecuteReconciliationRequest {
  // execute type
  enum Type {
    // unknown
    TYPE_UNSPECIFIED = 0;
    // order, execute order unilateral reconciliation
    TYPE_ORDER_UNILATERAL = 1;
    // payment, execute payment unilateral reconciliation
    TYPE_PAYMENT_UNILATERAL = 2;
    // split payment
    TYPE_SPLIT_PAYMENT_UNILATERAL = 3;
    // split payment reversal reconciliation
    TYPE_SPLIT_PAYMENT_REVERSAL_UNILATERAL = 4;
    // refund reconciliation
    TYPE_REFUND_UNILATERAL = 5;
  }
  // reconciliation type
  Type type = 1;
  // start time
  // the reconciliation task will reconcile the data from ${start_time} to ${end_time}
  //
  // if the ${start_time} is not set, default value is 10 minutes ago
  optional int64 start_time = 2;
  // end time
  // if the ${end_time} is not set, default value is now
  //
  // the ${end_time} must be greater than ${start_time},
  // if ${end_time} less than ${start_time}, both parameters will be set to default values
  // that is from 10 minutes ago until now
  optional int64 end_time = 3;
}

// the reconciliation service
service ReconciliationService {
  // execute reconciliation
  rpc ExecuteReconciliation(ExecuteReconciliationRequest) returns (google.protobuf.Empty);
}
