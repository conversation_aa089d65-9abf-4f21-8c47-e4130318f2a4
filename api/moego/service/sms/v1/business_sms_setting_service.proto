// @since 2023-06-20 17:13:10
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.sms.v1;

import "moego/models/sms/v1/business_setting_models.proto";
import "moego/models/sms/v1/sms_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/sms/v1;smssvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.sms.v1";

//one busines sms setting query request
message BusinessSmsSettingOneQueryRequest {
  // the business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

//busines sms setting response
message BusinessSmsSettingOneQueryResponse {
  //setting model
  models.sms.v1.BusinessSmsSettingModel setting = 1;
}

//multi busines sms setting query request
message BusinessSmsSettingMultiQueryRequest {
  // the business id
  repeated int64 business_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    items: {
      int64: {gt: 0}
    }
  }];
}

//multi busines sms setting response
message BusinessSmsSettingMultiQueryResponse {
  //setting model map
  repeated models.sms.v1.BusinessSmsSettingModel settings = 1;
}

//multi busines sms setting query request
message BusinessSmsSettingMultiQueryByNumberRequest {
  // the business id
  string number = 1 [(validate.rules).string = {max_len: 50}];
}

//multi busines sms setting response
message BusinessSmsSettingMultiQueryByNumberResponse {
  //setting model map
  repeated models.sms.v1.BusinessSmsSettingModel settings = 1;
}

//business sms setting update request
message BusinessSmsSettingUpdateRequest {
  // the business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  //business call handle type
  optional int32 call_handle_type = 6 [(validate.rules).int32 = {gt: -1}];
  //business call phone number
  optional string call_phone_number = 7 [(validate.rules).string = {max_len: 20}];
  //business call reply type
  optional int32 call_reply_type = 8 [(validate.rules).int32 = {gt: -1}];
  //business reply message body
  optional string reply_message = 9 [(validate.rules).string = {max_len: 1000}];
  // twilio number
  optional string twilio_number = 10 [(validate.rules).string = {max_len: 20}];
}

//busines sms setting response
message BusinessSmsSettingUpdateResponse {}

// assign phone number to business request
message InitBusinessSmsSettingRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  //is create sub
  moego.models.sms.v1.InitTypeEnum init_type = 2 [(validate.rules).enum = {defined_only: true}];
}

//assign response void
message InitBusinessSmsSettingResponse {}

//recycling number empty request
message RecyclingNumberTaskRequest {}

//recycling number empty response
message RecyclingNumberTaskResponse {}

// business setting
service BusinessSmsSettingService {
  // query one business sms setting
  rpc QueryOneBusinessSmsSetting(BusinessSmsSettingOneQueryRequest) returns (BusinessSmsSettingOneQueryResponse);
  // query multi business sms setting
  rpc QueryMultiBusinessSmsSetting(BusinessSmsSettingMultiQueryRequest) returns (BusinessSmsSettingMultiQueryResponse);
  //query business setting by number
  rpc QueryMultiBusinessSmsSettingByNumber(BusinessSmsSettingMultiQueryByNumberRequest) returns (BusinessSmsSettingMultiQueryByNumberResponse);
  //business sms setting update
  rpc UpdateBusinessSmsSetting(BusinessSmsSettingUpdateRequest) returns (BusinessSmsSettingUpdateResponse);
  //init business sms setting
  rpc InitBusinessSmsSetting(InitBusinessSmsSettingRequest) returns (InitBusinessSmsSettingResponse);
  //recycling number
  rpc RecyclingNumberTask(RecyclingNumberTaskRequest) returns (RecyclingNumberTaskResponse);
}
