syntax = "proto3";

package moego.service.file.v2;

import "moego/models/file/v2/file_defs.proto";
import "moego/models/file/v2/file_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/file/v2;filesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.file.v2";

// GetUploadPresignedUrlRequest
message GetUploadPresignedUrlRequest {
  // account id for the creator
  int64 creator_id = 1 [(validate.rules).int64 = {gt: 0}];
  // usage for the file ,eg: photo,avatar...
  string usage = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // file md5 after base64
  string md5 = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // file name(with extension)
  string file_name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 200
  }];
  // file size byte
  int64 file_size_byte = 5 [(validate.rules).int64 = {
    gte: 0
    lte: *********
  }];
  // owner type,eg:staff,pet...
  string owner_type = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // owner id
  int64 owner_id = 7 [(validate.rules).int64 = {gt: 0}];
  // source
  oneof source {
    option (validate.required) = true;
    // platform
    moego.models.file.v2.PlatformSourceDef platform = 8;
    // tenant
    moego.models.file.v2.TenantSourceDef tenant = 9;
  }

  // file metadata
  map<string, string> metadata = 14;
}

// GetPresignedUrlResponse
message GetUploadPresignedUrlResponse {
  // presigned url for upload
  string presigned_url = 1;
  // access url for download
  string access_url = 2;
  // deprecated, already contains in the metadata
  string content_type = 3 [deprecated = true];
  // file id
  int64 file_id = 4;

  // the headers used for later uploads, must contain Content-Type and Content-MD5.
  map<string, string> metadata = 8;
}

// CreateExportTaskRequest
message CreateExportFileRequest {
  // account id for the creator
  int64 creator_id = 1 [(validate.rules).int64 = {gt: 0}];
  // usage for the file, must be one of FileUsage
  string usage = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // file name(with extension)
  string file_name = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // owner type,eg:staff,pet...
  string owner_type = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // owner id
  int64 owner_id = 5 [(validate.rules).int64 = {gt: 0}];
  // source
  oneof source {
    option (validate.required) = true;
    // platform
    moego.models.file.v2.PlatformSourceDef platform = 6;
    // tenant
    moego.models.file.v2.TenantSourceDef tenant = 7;
  }
  // content disposition for download, deprecated, please set in metadata.
  optional string content_disposition = 8 [
    (validate.rules).string = {
      min_len: 1
      max_len: 256
    },
    deprecated = true
  ];
  // file metadata
  map<string, string> metadata = 14;
}

// CreateExportTaskResponse
message CreateExportFileResponse {
  // file id
  int64 file_id = 1;
}

// QueryFileRequest
message QueryFileRequest {
  // file id
  int64 file_id = 1;
}

// QueryFileResponse
message QueryFileResponse {
  // file info
  moego.models.file.v2.FileModel file = 1;
}

// UploadExportFileRequest
message UploadExportFileRequest {
  // file id
  int64 file_id = 1;
  // file content
  bytes file_content = 2;
}

// UploadExportFileResponse
message UploadExportFileResponse {
  //file status
  moego.models.file.v2.FileStatus status = 1;
}

// UpdateFileStatusRequest
message UpdateFileStatusRequest {
  // file id
  int64 file_id = 1;
  // file status
  models.file.v2.FileStatus status = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// UpdateFileStatusResponse
message UpdateFileStatusResponse {}

// upload file
message UploadFileRequest {
  // account id for the creator
  int64 creator_id = 1;
  // usage for the file ,eg: photo,avatar...
  string usage = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // file content
  bytes file_content = 3 [(validate.rules).bytes = {
    min_len: 1
    max_len: *********
  }];
  // file name(with extension)
  string file_name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // owner type,eg:staff,pet...
  string owner_type = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // owner id
  int64 owner_id = 6 [(validate.rules).int64 = {gt: 0}];
  // source
  oneof source {
    option (validate.required) = true;
    // platform
    moego.models.file.v2.PlatformSourceDef platform = 7;
    // tenant
    moego.models.file.v2.TenantSourceDef tenant = 8;
  }
  // content disposition for download, deprecated, please set in metadata.
  optional string content_disposition = 9 [
    (validate.rules).string = {
      min_len: 1
      max_len: 256
    },
    deprecated = true
  ];
  // file metadata
  map<string, string> metadata = 14;
}

// UploadFileResponse
message UploadFileResponse {
  // file id
  int64 file_id = 1;
  // access url for download
  string access_url = 2;
}

// FlushFileRequest
message FlushFileRequest {
  // file identity
  oneof file_identity {
    // file id
    int64 file_id = 1;
    // aws s3 full path, like: s3://bucket/key
    string aws_s3_path = 2;
  }
}

// FlushFileResponse
message FlushFileResponse {
  // file info
  moego.models.file.v2.FileModel file = 1;
}

// FileService
service FileService {
  // get presigned url for upload file
  rpc GetUploadPresignedUrl(GetUploadPresignedUrlRequest) returns (GetUploadPresignedUrlResponse);
  // CreateExportFile: create a file record before upload it to s3,should use UploadExportFile to upload the file
  rpc CreateExportFile(CreateExportFileRequest) returns (CreateExportFileResponse);
  // QueryFile
  rpc QueryFile(QueryFileRequest) returns (QueryFileResponse);
  // UploadExportFile: usually used after CreateExportFile
  rpc UploadExportFile(UploadExportFileRequest) returns (UploadExportFileResponse);
  // UploadFile
  rpc UploadFile(UploadFileRequest) returns (UploadFileResponse);
  // UpdateFileStatus: usually used after upload file with presigned URL, deprecated, please use `FlushFile`
  rpc UpdateFileStatus(UpdateFileStatusRequest) returns (UpdateFileStatusResponse) {
    option deprecated = true;
  }
  // flush file status
  rpc FlushFile(FlushFileRequest) returns (FlushFileResponse);
}
