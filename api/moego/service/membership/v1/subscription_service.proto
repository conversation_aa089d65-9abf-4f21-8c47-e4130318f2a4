// @since 2024-06-14 13:53:26
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.membership.v1;

import "google/type/dayofweek.proto";
import "moego/models/business_customer/v1/business_customer_merge_defs.proto";
import "moego/models/membership/v1/sell_link_defs.proto";
import "moego/models/membership/v1/sell_link_models.proto";
import "moego/models/membership/v1/subscription_defs.proto";
import "moego/models/membership/v1/subscription_models.proto";
import "moego/models/organization/v1/tenant.proto";
import "moego/models/subscription/v1/subscription_models.proto";
import "moego/service/subscription/v1/subscription_service.proto";
import "moego/utils/v2/operation_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/membership/v1;membershipsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.membership.v1";

// the subscription service
service SubscriptionService {
  // create sell link
  rpc CreateSellLink(CreateSellLinkRequest) returns (CreateSellLinkResponse);
  // get sell link
  rpc GetSellLink(GetSellLinkRequest) returns (GetSellLinkResponse);
  // delete sell link
  rpc DeleteSellLink(DeleteSellLinkRequest) returns (DeleteSellLinkResponse);

  // create subscription
  rpc CreateSubscription(CreateSubscriptionRequest) returns (CreateSubscriptionResponse);
  // update subscription
  rpc UpdateSubscription(UpdateSubscriptionRequest) returns (UpdateSubscriptionResponse);
  // get subscription
  rpc GetSubscription(GetSubscriptionRequest) returns (GetSubscriptionResponse);
  // list subscription
  rpc ListSubscriptions(ListSubscriptionsRequest) returns (ListSubscriptionsResponse);
  // list customer subscription
  rpc ListCustomerSubscriptions(ListCustomerSubscriptionsRequest) returns (ListCustomerSubscriptionsResponse);
  // update subscription
  rpc CancelSubscription(CancelSubscriptionRequest) returns (CancelSubscriptionResponse);
  // delete subscription
  rpc RenewSubscription(RenewSubscriptionRequest) returns (RenewSubscriptionResponse);
  // PauseSubscription
  rpc PauseSubscription(PauseSubscriptionRequest) returns (PauseSubscriptionResponse);
  // ResumeSubscription
  rpc ResumeSubscription(ResumeSubscriptionRequest) returns (ResumeSubscriptionResponse);
  // merge subscriptions
  rpc MergeSubscriptions(MergeSubscriptionsRequest) returns (MergeSubscriptionsResponse);
  // list membership's buyers
  rpc ListBuyers(ListBuyersRequest) returns (ListBuyersResponse);
  // list multiple membership's buyers
  rpc ListMembershipsBuyers(ListMembershipsBuyersRequest) returns (ListMembershipsBuyersResponse);
  // get membership buyer report
  rpc GetBuyerReport(GetBuyerReportRequest) returns (GetBuyerReportResponse);
  // list payment history
  rpc ListPaymentHistory(ListPaymentHistoryRequest) returns (ListPaymentHistoryResponse);
  // Create OB Request
  rpc CreateOBRequestSetting(CreateOBRequestSettingRequest) returns (CreateOBRequestSettingResponse);
  // Get OB Request
  rpc GetOBRequestSetting(GetOBRequestSettingRequest) returns (GetOBRequestSettingResponse);
}

// create sell link request
message CreateSellLinkRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // link def
  moego.models.membership.v1.SellLinkCreateDef sell_link_def = 3 [(validate.rules).message = {required: true}];
  // operation
  moego.utils.v2.OperationRequest operation = 15;
}

// create sell link response
message CreateSellLinkResponse {
  // link
  moego.models.membership.v1.SellLinkModel sell_link = 1;
  // uuid
  string public_token = 2;
}

// create sell link request
message GetSellLinkRequest {
  // the identifier
  oneof identifier {
    option (validate.required) = true;
    // by id
    int64 id = 1 [(validate.rules).int64 = {gt: 0}];
    // by public token
    string public_token = 2 [(validate.rules).string = {
      min_len: 10
      max_len: 100
    }];
  }
  // filter by company id
  optional int64 company_id = 3 [(validate.rules).int64 = {gt: 0}];
  // business id
  optional int64 business_id = 4 [(validate.rules).int64 = {gt: 0}];
  // allow expired
  optional bool allow_expired = 5;
}

// get sell link response
message GetSellLinkResponse {
  // link
  moego.models.membership.v1.SellLinkModel sell_link = 1;
}

// delete sell link request
message DeleteSellLinkRequest {
  // id to delete
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete sell link response
message DeleteSellLinkResponse {}

// create subscription request
message CreateSubscriptionRequest {
  // the subscription def
  moego.models.membership.v1.SubscriptionCreateDef subscription_def = 1 [(validate.rules).message = {required: true}];

  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // sell link id
  optional int64 sell_link_id = 4 [(validate.rules).int64 = {gt: 0}];

  // operation
  moego.utils.v2.OperationRequest operation = 15;

  // sell operator staff id
  optional int64 sell_operator_staff_id = 16 [(validate.rules).int64 = {gte: 0}];
}

// create subscription response
message CreateSubscriptionResponse {
  // the created subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
  // the external client secret id
  string external_client_secret_id = 2;
}

// update subscription request
message UpdateSubscriptionRequest {
  // the subscription id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // the subscription def
  moego.models.membership.v1.SubscriptionUpdateDef subscription_def = 2 [(validate.rules).message = {required: true}];
}

// update subscription response
message UpdateSubscriptionResponse {
  // the updated subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
}

// get subscription request
message GetSubscriptionRequest {
  // customer membership
  message CustomerMembershipIdentifier {
    // customer id
    int64 customer_id = 1 [(validate.rules).int64 = {gt: 0}];
    // membership id
    int64 membership_id = 2 [(validate.rules).int64 = {gt: 0}];
  }
  // the identifier
  oneof identifier {
    option (validate.required) = true;
    // by the id
    int64 id = 1 [(validate.rules).int64 = {gt: 0}];
    // buy customer + membership id
    CustomerMembershipIdentifier customer_membership = 2;
  }

  // filter by company id
  optional int64 company_id = 3 [(validate.rules).int64 = {gt: 0}];
  // filter by status
  repeated moego.models.membership.v1.SubscriptionModel.Status status_in = 4 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // do not emit error if not found
  optional bool silent = 5;
}

// get subscription response
message GetSubscriptionResponse {
  // the subscription
  optional moego.models.membership.v1.SubscriptionModel subscription = 1;
}

// list subscription request
message ListSubscriptionsRequest {
  // the tenant
  moego.models.organization.v1.Tenant tenant = 1 [(validate.rules).message = {required: true}];

  // customer id
  repeated int64 customer_id_in = 2 [(validate.rules).repeated = {
    unique: true
    max_items: 1000
  }];

  // membership id
  repeated int64 membership_id_in = 3 [(validate.rules).repeated = {
    unique: true
    max_items: 1000
  }];

  // status id
  repeated moego.models.membership.v1.SubscriptionModel.Status status_in = 4 [(validate.rules).repeated = {
    unique: true
    max_items: 1000
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];

  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// list subscription response
message ListSubscriptionsResponse {
  // membership subscription list
  moego.models.membership.v1.MembershipSubscriptionListModel result = 1;
}

// list customer subscription
message ListCustomerSubscriptionsRequest {
  // the tenant
  optional moego.models.organization.v1.Tenant tenant = 1;
  // max numbers of subscriptions to return for each customer
  int32 max_subscriptions = 2 [(validate.rules).int32 = {
    gt: 0
    lt: 100
  }];
  // customer ids
  repeated int64 customer_ids = 3 [(validate.rules).repeated = {
    unique: true
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
  // subscription status
  repeated moego.models.membership.v1.SubscriptionModel.Status status_in = 4 [(validate.rules).repeated = {
    unique: true
    max_items: 1000
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// list customer subscription response
message ListCustomerSubscriptionsResponse {
  // customer subscription list
  message CustomerWithSubscriptionList {
    // customer id
    int64 customer_id = 1;
    // customer subscription list
    repeated moego.models.membership.v1.MembershipSubscriptionModel subscriptions = 2;
    // total count of subscriptions
    int32 total_count = 3;
  }
  // customer subscription list
  repeated CustomerWithSubscriptionList customer_subscriptions = 1;
}

// create subscription request
message CancelSubscriptionRequest {
  // id
  int64 id = 1;
  // the tenant
  optional moego.models.organization.v1.Tenant tenant = 3;
  // 退款策略，默认不退款
  moego.service.subscription.v1.CancelSubscriptionRequest.RefundPolicy refund_policy = 4;
  // 取消策略，默认到期取消
  moego.service.subscription.v1.CancelSubscriptionRequest.CancelPolicy cancel_policy = 5;
  // operation
  moego.utils.v2.OperationRequest operation = 15;
}

// create subscription response
message CancelSubscriptionResponse {
  // the updated subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
}

// get subscription request
message RenewSubscriptionRequest {
  // id
  int64 id = 1;
  // the tenant
  optional moego.models.organization.v1.Tenant tenant = 3;
  // operation
  moego.utils.v2.OperationRequest operation = 15;
  // required for cancelled subscription
  optional string card_on_file_id = 2;
}

// get subscription response
message RenewSubscriptionResponse {
  // the updated subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
  // the external client secret id
  string external_client_secret_id = 2;
}

// pause subscription request
message PauseSubscriptionRequest {
  // id
  int64 id = 1;
  // auto resume
  moego.models.membership.v1.SubscriptionModel.AutoResumeSetting auto_resume_setting = 2;
}

// pause subscription response
message PauseSubscriptionResponse {
  // the updated subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
}

// resume subscription request
message ResumeSubscriptionRequest {
  // id
  int64 id = 1;
}

// resume subscription response
message ResumeSubscriptionResponse {
  // the updated subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
}

// merge subscriptions response
message MergeSubscriptionsRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // merge relation
  models.business_customer.v1.BusinessCustomerMergeRelationDef merge_relation = 2;
}

// merge subscriptions request
message MergeSubscriptionsResponse {
  // success
  bool success = 1;
}

// list membership buyers request
message ListBuyersRequest {
  // the membership id
  int64 membership_id = 1 [(validate.rules).int64 = {gt: 0}];
  // filter
  optional moego.models.membership.v1.SubscriptionBuyerListFilter filter = 2;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3;
}

// list membership buyers response
message ListBuyersResponse {
  // the buyers
  repeated moego.models.membership.v1.SubscriptionBuyerView buyers = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// list memberships buyers request
message ListMembershipsBuyersRequest {
  // membership ids
  repeated int64 membership_ids = 1 [(validate.rules).repeated = {
    unique: true
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
  // filter
  optional moego.models.membership.v1.SubscriptionBuyerListFilter filter = 2;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3;
}

// list memberships buyers response
message ListMembershipsBuyersResponse {
  // buyers
  repeated moego.models.membership.v1.SubscriptionBuyerView buyers = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// get membership buyer report request
message GetBuyerReportRequest {
  // the membership id
  int64 membership_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get membership buyer report response
message GetBuyerReportResponse {
  // the buyer report
  moego.models.subscription.v1.Report content = 1;
}

// list invoice history request
message ListPaymentHistoryRequest {
  // filter
  moego.models.membership.v1.PaymentHistoryItemFilter filter = 1;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2;
}

// list payment history response
message ListPaymentHistoryResponse {
  // the views
  repeated models.membership.v1.PaymentHistoryItemView history_views = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// CreateOBRequestRequest
message CreateOBRequestSettingRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // subscription id
  int64 subscription_id = 3 [(validate.rules).int64.gt = 0];
  // daya of week
  repeated google.type.DayOfWeek days_of_week = 4;
  // pet id
  int64 pet_id = 5 [(validate.rules).int64.gte = 0];
}

// CreateOBRequestResponse
message CreateOBRequestSettingResponse {
  // ob request setting
  moego.models.membership.v1.OBRequestSettingModel ob_request_setting = 1;
}

// GetOBRequestSettingRequest
message GetOBRequestSettingRequest {
  // subscription id
  int64 subscription_id = 1 [(validate.rules).int64.gt = 0];
}

// GetOBRequestResponse
message GetOBRequestSettingResponse {
  // ob request setting
  optional moego.models.membership.v1.OBRequestSettingModel ob_request_setting = 1;
}
