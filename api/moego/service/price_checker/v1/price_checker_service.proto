// @since 2023-06-16 16:48:23
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.price_checker.v1;

import "google/protobuf/empty.proto";
import "moego/models/price_checker/v1/price_checker_defs.proto";
import "moego/models/price_checker/v1/price_checker_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/price_checker/v1;pricecheckersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.price_checker.v1";

// get state output
message GetStateOutput {
  // state
  repeated string state = 1;
}

// get city input
message GetCityInput {
  // state
  string state = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
}

// get city output
message GetCityOutput {
  // city
  repeated string city = 1;
}

// get price distribution input
message GetBasicInfoInput {
  // state
  string state = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // city
  optional string city = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // business mode
  moego.models.price_checker.v1.BusinessMode business_mode = 3 [(validate.rules).enum = {defined_only: true}];
  // pet type
  moego.models.price_checker.v1.PetType pet_type = 4 [(validate.rules).enum = {defined_only: true}];
}

// get basic info output
message GetBasicInfoOutput {
  // ticket amount
  int32 ticket_amount = 1;
  // pet amount
  int32 pet_amount = 2;
  // average hourly rate
  int32 average_hourly_rate = 3;
  // price distribution
  moego.models.price_checker.v1.PriceDistribution price_distribution = 4;
  // breed price distribution
  repeated moego.models.price_checker.v1.BreedPriceDistribution breed_price_distribution = 5;
}

// get price distribution input
message GetPriceDistributionInput {
  // state
  string state = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // city
  optional string city = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // business mode
  moego.models.price_checker.v1.BusinessMode business_mode = 3 [(validate.rules).enum = {defined_only: true}];
  // pet type
  moego.models.price_checker.v1.PetType pet_type = 4 [(validate.rules).enum = {defined_only: true}];
}

// get price distribution output
message GetPriceDistributionOutput {
  //  pet price distribution
  repeated moego.models.price_checker.v1.PetPriceDistribution pet_price_distribution = 1;
}

// post price distribution input
message PostPriceDistributionInput {
  // country
  string country = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // state
  string state = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // city
  optional string city = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // business mode
  moego.models.price_checker.v1.BusinessMode business_mode = 4 [(validate.rules).enum = {defined_only: true}];
  // pet type
  moego.models.price_checker.v1.PetType pet_type = 5 [(validate.rules).enum = {defined_only: true}];
  // business position
  moego.models.price_checker.v1.BusinessPosition business_position = 6 [(validate.rules).enum = {defined_only: true}];
  // pet price distribution submit
  repeated moego.models.price_checker.v1.PetPriceDistributionSubmit pet_price_distribution_submit = 7;
  // email
  string email = 8 [(validate.rules).string.email = true];
  // alert threshold
  int32 alert_threshold = 9 [(validate.rules).int32 = {
    gt: 0
    lt: 1000
  }];
}

// post price distribution output
message PostPriceDistributionOutput {
  // success
  bool success = 1;
}

// start task input
message StartTaskInput {
  // business id
  int32 business_id = 1 [(validate.rules).int32.gte = 0];
  // start date
  string start_date = 2 [(validate.rules).string.pattern = "^\\d{4}-\\d{2}-\\d{2}$"];
  // end date
  string end_date = 3 [(validate.rules).string.pattern = "^\\d{4}-\\d{2}-\\d{2}$"];
}

// unsubscribe email input
message UnsubscribeEmailInput {
  // id
  string id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 128
  }];
}

// unsubscribe email output
message UnsubscribeEmailOutput {
  // success
  bool success = 1;
}

// price checker service
service PriceCheckerService {
  // get state
  rpc GetState(google.protobuf.Empty) returns (GetStateOutput) {}
  // get city
  rpc GetCity(GetCityInput) returns (GetCityOutput) {}
  // get basic info
  rpc GetBasicInfo(GetBasicInfoInput) returns (GetBasicInfoOutput) {}
  // get price distribution
  rpc GetPriceDistribution(GetPriceDistributionInput) returns (GetPriceDistributionOutput) {}
  // post price distribution
  rpc PostPriceDistribution(PostPriceDistributionInput) returns (PostPriceDistributionOutput) {}
  // start task
  rpc StartTask(StartTaskInput) returns (google.protobuf.Empty) {}
  // unsubscribe email
  rpc UnsubscribeEmail(UnsubscribeEmailInput) returns (UnsubscribeEmailOutput) {}
}
