syntax = "proto3";

package moego.service.risk_control.v1;

import "moego/models/risk_control/v1/verification_code_defs.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/risk_control/v1;riskcontrolsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.risk_control.v1";

// create verification code request
message CreateCodeRequest {
  // account identifier
  models.risk_control.v1.VerificationIdentifierDef identifier = 1;
}

// create verification code response
message CreateCodeResponse {
  // verification code
  models.risk_control.v1.VerificationCodeDef verification = 1;
  // expiration seconds
  int32 expiration_seconds = 3;
}

// verify code request
message VerifyCodeRequest {
  // verification code
  models.risk_control.v1.VerificationCodeDef verification = 1;
}

// verify code response
message VerifyCodeResponse {
  // is exist
  bool is_exist = 1;
}

// the verification code service
service VerificationCodeService {
  // Create verification code
  rpc Create(CreateCodeRequest) returns (CreateCodeResponse);
  // Check verification code and destroy it
  rpc Verify(VerifyCodeRequest) returns (VerifyCodeResponse);
  // Check whether the verification code are consistent
  rpc Check(VerifyCodeRequest) returns (VerifyCodeResponse);
}
