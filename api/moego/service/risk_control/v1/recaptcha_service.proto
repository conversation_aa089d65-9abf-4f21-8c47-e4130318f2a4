// @since 2023-09-05 17:03:16
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.risk_control.v1;

import "moego/models/risk_control/v1/recaptcha_defs.proto";
import "moego/models/risk_control/v1/recaptcha_enums.proto";
import "moego/models/risk_control/v1/risk_control_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/risk_control/v1;riskcontrolsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.risk_control.v1";

// challenge input
message RecaptchaChallengeInput {
  // the recaptcha challenge def
  moego.models.risk_control.v1.RecaptchaDef recaptcha_def = 1 [(validate.rules).message = {required: true}];
  // ob session id
  int64 session_id = 2;
}

// challenge output
message RecaptchaChallengeOutput {
  // risk control token
  string risk_control_token = 1;
}

// risk control verify input
message RiskControlVerifyInput {
  // risk control def
  moego.models.risk_control.v1.RiskControlDef risk_control = 1 [(validate.rules).message = {required: true}];
  // verify method
  oneof verify_method {
    option (validate.required) = true;
    // google recaptcha input
    RecaptchaInput recaptcha = 2;
  }
}

// google recaptcha input
message RecaptchaInput {
  // session id
  int64 session_id = 1;
  // recaptcha action
  moego.models.risk_control.v1.RecaptchaAction action = 2;
  // recaptcha applicable version
  repeated moego.models.risk_control.v1.RecaptchaVersion versions = 3;
}

// risk control verify output
message RiskControlVerifyOutput {
  // verify result
  bool success = 1;
}

// the recaptcha service
service RecaptchaService {
  // challenge
  rpc Challenge(RecaptchaChallengeInput) returns (RecaptchaChallengeOutput);
  // verify risk control
  rpc Verify(RiskControlVerifyInput) returns (RiskControlVerifyOutput);
}
