syntax = "proto3";

package moego.service.auto_message.v1;

import "moego/models/auto_message/v1/auto_message_defs.proto";
import "moego/models/auto_message/v1/auto_message_models.proto";
import "moego/models/message/v1/message_template_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/auto_message/v1;automessagesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.auto_message.v1";

// init auto message config request
message InitAutoMessageRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// init auto message config response
message InitAutoMessageResponse {}

// update auto message config request
message UpdateAutoMessageConfigRequest {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id. used for unauthorized verification
  optional int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // is auto message enabled
  optional bool is_enabled = 3;
  // is sms enabled
  optional bool is_sms_enabled = 4;
  // is email enabled
  optional bool is_email_enabled = 5;
  // minutes offset. can be positive or negative, 0 for unset
  optional int32 minutes_offset = 6;
  // minutes at. 0 for unset
  optional int32 minutes_at = 7 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // sms template id. 0 for unset
  optional int64 sms_template_id = 8 [(validate.rules).int64 = {gt: 0}];
  // email template id. 0 for unset
  optional int64 email_template_id = 9 [(validate.rules).int64 = {gt: 0}];
  // is app enabled
  optional bool is_app_enabled = 10;
  // app template id. 0 for unset
  optional int64 app_template_id = 11 [(validate.rules).int64 = {gt: 0}];
}

// update auto message config response
message UpdateAutoMessageConfigResponse {
  // auto message detail
  models.auto_message.v1.AutoMessageConfigModel auto_message = 1;
}

// get auto message config list request
message GetAutoMessageConfigListRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // auto message use cases. empty for empty result; null for all
  optional moego.models.message.v1.MessageTemplateUseCaseEnumList use_cases = 3;
}

// get auto message config list response
message GetAutoMessageConfigListResponse {
  // auto message list
  repeated models.auto_message.v1.AutoMessageConfigModel auto_messages = 1;
}

// get auto message config detail request
message GetAutoMessageConfigDetailRequest {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id. used for unauthorized verification
  optional int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get auto message config detail response
message GetAutoMessageConfigDetailResponse {
  // auto message detail
  models.auto_message.v1.AutoMessageConfigModel auto_message = 1;
}

// get auto message config list by business ids request
message GetAutoMessageConfigByBusinessIdsRequest {
  // business id.empty for empty result
  repeated int64 business_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
  }];
  // auto message use cases. empty for empty result; null for all
  optional moego.models.message.v1.MessageTemplateUseCaseEnumList use_cases = 3;
}

// get auto message config list by business ids response
message GetAutoMessageConfigByBusinessIdsResponse {
  // auto message list
  repeated models.auto_message.v1.AutoMessageConfigModel auto_messages = 1;
}

// update appointment auto message config request
message UpdateAppointmentAutoMsgConfigRequest {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id. used for unauthorized verification
  optional int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // is auto message enabled
  optional bool is_enabled = 3;
  // is sms enabled
  optional bool is_sms_enabled = 4;
  // is email enabled
  optional bool is_email_enabled = 5;
  // minutes offset. can be positive or negative, 0 for unset
  optional int32 minutes_offset = 6;
  // minutes at
  optional int32 minutes_at = 7 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // appointment configs for appointment auto message
  optional models.auto_message.v1.ServiceTypeConfigDefList templates = 8;
  // is app enabled
  optional bool is_app_enabled = 9;
}

// update appointment auto message config response
message UpdateAppointmentAutoMsgConfigResponse {
  // auto message detail
  models.auto_message.v1.AppointmentAutoMsgConfigModel auto_message = 1;
}

// get appointment auto message config list request
message GetAppointmentAutoMsgConfigListRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // auto message types. empty for empty result; null for all
  optional moego.models.message.v1.MessageTemplateUseCaseEnumList use_cases = 3;
}

// get appointment auto message config list response
message GetAppointmentAutoMsgConfigListResponse {
  // auto message list
  repeated models.auto_message.v1.AppointmentAutoMsgConfigModel auto_messages = 1;
}

// get appointment auto message config detail request
message GetAppointmentAutoMsgConfigDetailRequest {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id. used for unauthorized verification
  optional int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get appointment auto message config detail response
message GetAppointmentAutoMsgConfigDetailResponse {
  // auto message detail
  models.auto_message.v1.AppointmentAutoMsgConfigModel auto_message = 1;
}

// get appointment auto message config list by business ids request
message GetAppointmentAutoMsgConfigByBusinessIdsRequest {
  // business id. empty for empty result
  repeated int64 business_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
  }];
  // auto message use cases. empty for empty result; null for all
  optional moego.models.message.v1.MessageTemplateUseCaseEnumList use_cases = 3;
}

// get appointment auto message config list by business ids response
message GetAppointmentAutoMsgConfigByBusinessIdsResponse {
  // auto message list
  repeated models.auto_message.v1.AppointmentAutoMsgConfigModel auto_messages = 1;
}

// transfer msg config by business request
message MessageTransferByBusinessRequest {
  // business ids to transfer. empty for empty result
  repeated int64 business_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
  }];
}

// transfer msg config by business response
message MessageTransferByBusinessResponse {}

// batch init auto message config request
message BatchInitAutoMessageRequest {
  // business id
  repeated int64 business_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
  }];
}

// batch init auto message config response
message BatchInitAutoMessageResponse {}

// init c app auto message request
message InitCAppAutoMessageRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // override
  bool is_override = 2;
}

// init c app auto message response
message InitCAppAutoMessageResponse {}

// the auto message config service
service AutoMessageConfigService {
  // init auto message config. for business initialization
  rpc InitAutoMessage(InitAutoMessageRequest) returns (InitAutoMessageResponse);
  // update auto message config
  rpc UpdateAutoMessageConfig(UpdateAutoMessageConfigRequest) returns (UpdateAutoMessageConfigResponse);
  // get auto message config list
  rpc GetAutoMessageConfigList(GetAutoMessageConfigListRequest) returns (GetAutoMessageConfigListResponse);
  // get auto message config detail
  rpc GetAutoMessageConfigDetail(GetAutoMessageConfigDetailRequest) returns (GetAutoMessageConfigDetailResponse);
  // get auto message config list by business ids. for reminder task
  rpc GetAutoMessageConfigByBusinessIds(GetAutoMessageConfigByBusinessIdsRequest) returns (GetAutoMessageConfigByBusinessIdsResponse);
  // update appointment auto message config
  rpc UpdateAppointmentAutoMsgConfig(UpdateAppointmentAutoMsgConfigRequest) returns (UpdateAppointmentAutoMsgConfigResponse);
  // get appointment auto message config list
  rpc GetAppointmentAutoMsgConfigList(GetAppointmentAutoMsgConfigListRequest) returns (GetAppointmentAutoMsgConfigListResponse);
  // get appointment auto message detail
  rpc GetAppointmentAutoMsgConfigDetail(GetAppointmentAutoMsgConfigDetailRequest) returns (GetAppointmentAutoMsgConfigDetailResponse);
  // get appointment auto message config list by business ids. for reminder task
  rpc GetAppointmentAutoMsgConfigByBusinessIds(GetAppointmentAutoMsgConfigByBusinessIdsRequest) returns (GetAppointmentAutoMsgConfigByBusinessIdsResponse);
  // transfer msg config by business. for auto message service initialization
  rpc MessageTransferByBusiness(MessageTransferByBusinessRequest) returns (MessageTransferByBusinessResponse);
  // batch init auto message config. for auto message service initialization
  rpc BatchInitAutoMessage(BatchInitAutoMessageRequest) returns (BatchInitAutoMessageResponse);
  // init c app auto message config.
  rpc InitCAppAutoMessage(InitCAppAutoMessageRequest) returns (InitCAppAutoMessageResponse);
}
