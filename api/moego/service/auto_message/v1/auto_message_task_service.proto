// @since 2024-06-05 11:24:51
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.auto_message.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/auto_message/v1/auto_message_task_defs.proto";
import "moego/models/auto_message/v1/auto_message_task_models.proto";
import "moego/models/message/v1/message_template_enums.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/auto_message/v1;automessagesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.auto_message.v1";

// batch upsert auto_message_task request
message BatchUpsertAutoMessageTaskRequest {
  // tasks
  repeated models.auto_message.v1.AutoMessageTaskUpsertDef tasks = 1 [(validate.rules).repeated = {min_items: 1}];
}

// batch upsert auto message task response
message BatchUpsertAutoMessageTaskResponse {}

// list  auto message task request
message ListAutoMessageTaskRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // auto message use cases
  optional moego.models.message.v1.MessageTemplateUseCaseEnumList use_cases = 3;
  // start time
  optional google.protobuf.Timestamp start_time = 4 [(validate.rules).timestamp.gte.seconds = 0];
  // end time
  optional google.protobuf.Timestamp end_time = 5 [(validate.rules).timestamp.gte.seconds = 0];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 7;
}

// list auto message task response
message ListAutoMessageTaskResponse {
  // tasks
  repeated models.auto_message.v1.AutoMessageTaskModel tasks = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// run auto message task request
message RunAutoMessageTaskRequest {
  // business list
  repeated TaskBusinessInfo businesses = 1 [(validate.rules).repeated = {min_items: 1}];

  // business info
  message TaskBusinessInfo {
    // business id
    int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
    // company id
    int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
    // time format type
    int32 time_format_type = 3 [(validate.rules).int32 = {gte: 0}];
    // date format
    string date_format = 4 [(validate.rules).string = {max_len: 50}];
    // date format type
    int32 date_format_type = 5 [(validate.rules).int32 = {gte: 0}];
    // timezone name
    string timezone_name = 6 [(validate.rules).string = {max_len: 50}];
  }
}

// run auto_message_task response
message RunAutoMessageTaskResponse {}

// the auto_message_task service
service AutoMessageTaskService {
  // batch upsert auto message task
  rpc BatchUpsertAutoMessageTask(BatchUpsertAutoMessageTaskRequest) returns (BatchUpsertAutoMessageTaskResponse);
  // list auto message task
  rpc ListAutoMessageTask(ListAutoMessageTaskRequest) returns (ListAutoMessageTaskResponse);
  // run auto message task
  rpc RunAutoMessageTask(RunAutoMessageTaskRequest) returns (RunAutoMessageTaskResponse);
}
