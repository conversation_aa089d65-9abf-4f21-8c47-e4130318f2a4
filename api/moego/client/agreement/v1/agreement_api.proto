syntax = "proto3";

package moego.client.agreement.v1;

import "moego/models/agreement/v1/agreement_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/agreement/v1;agreementapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.agreement.v1";

// get ob required agreement list request
message GetOnlineBookingRequiredAgreementListRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get ob required agreement list response
message GetOnlineBookingRequiredAgreementListResponse {
  // required agreement list
  repeated moego.models.agreement.v1.AgreementModelClientView required_agreements = 1;
}

// agreement service
service AgreementService {
  // get agreement list
  rpc GetOnlineBookingRequiredAgreementList(GetOnlineBookingRequiredAgreementListRequest) returns (GetOnlineBookingRequiredAgreementListResponse);
}
