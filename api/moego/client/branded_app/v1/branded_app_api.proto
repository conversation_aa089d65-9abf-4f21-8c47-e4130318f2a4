syntax = "proto3";

package moego.client.branded_app.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/branded_app/v1;brandedAppApiV1";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.branded_app.v1";

// The params for GetBrandedAppConfig
message GetBrandedAppConfigParams {
  // the branded app id
  string branded_app_id = 1 [(validate.rules).string = {max_len: 255}];
}

// The result for GetBrandedAppConfig
message GetBrandedAppConfigResult {
  // the branded app config
  BrandedAppConfig config = 3;

  // The branded app config
  message BrandedAppConfig {
    // Android download link
    string android_download_link = 1;
    // iOS download link
    string ios_download_link = 2;
  }
}

// get branded config for OB params
message GetBrandedAppConfigForOBParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// get branded config for OB result
message GetBrandedAppConfigForOBResult {
  // branded app id
  string branded_app_id = 1;
}

// branded_app service
service BrandedAppService {
  // Get the branded app config
  rpc GetBrandedAppConfig(GetBrandedAppConfigParams) returns (GetBrandedAppConfigResult);
  // Get the branded app config for OB
  rpc GetBrandedAppConfigForOB(GetBrandedAppConfigForOBParams) returns (GetBrandedAppConfigForOBResult);
}
