syntax = "proto3";

package moego.client.message.v2;

import "moego/models/message/v2/message_enums.proto";
import "moego/models/message/v2/message_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/message/v2;messageapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.message.v2";

// Sender view
message SenderView {
  // 发起方的角色
  models.message.v2.Role role = 1;
  // 发起方的 ID
  // 结合 role 决定是什么 ID
  uint64 id = 2;
  // 头像 URL
  string avatar_path = 3;
  // First Name
  string first_name = 4;
  // Last Name
  string last_name = 5;
}

// Message view
message MessageView {
  // 消息发起方
  SenderView sender = 1;
  // 消息
  models.message.v2.MessageModel message = 2;
}

// Chat view
message ChatView {
  // ------ COPY from ChatModel BEGIN -----
  // 对话 ID
  uint64 id = 1;
  // 对话所属 Business 的 Company ID
  uint64 company_id = 2;
  // 对话所属 Business 的 ID
  uint64 business_id = 3;
  // 对话所属 Customer 的 ID
  uint64 customer_id = 4;

  // 对话的状态
  models.message.v2.ChatStatus chat_status = 11;
  // 是否星标
  bool is_starred = 12;
  // 对话中最后一条消息的 ID
  uint64 last_msg_id = 21;
  // 对话中最后一条消息的创建时间
  uint64 last_msg_create_time = 22;
  // 打星标时间戳，毫秒
  uint64 star_time = 31;
  // 封锁时间戳，毫秒
  uint64 block_time = 32;
  // 创建时间戳，毫秒
  uint64 create_time = 33;
  // 更新时间戳，毫秒
  uint64 update_time = 34;
  // 仅用作占位，不会给到外部已删除的 chat
  // uint64 delete_time = 35;

  // ------  COPY from ChatModel END  -----

  // ------ View 特有的字段 -----

  // 最后一条消息
  MessageView last_msg = 51;
  // Chat 的头像
  string avatar_path = 52;
  // Chat 的名字
  string name = 53;
  // Chat 中未读消息的数量
  uint64 unread_message_count = 54;
}
