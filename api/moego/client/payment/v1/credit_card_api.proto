syntax = "proto3";

package moego.client.payment.v1;

import "moego/models/payment/v1/credit_card_defs.proto";
import "moego/models/payment/v1/credit_card_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/payment/v1;paymentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.payment.v1";

// get credit card list request
message GetCreditCardListRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get credit card list response
message GetCreditCardListResponse {
  // credit card list
  repeated moego.models.payment.v1.CreditCardModel cards = 1;
}

// The params message for ListCreditCards
message ListCreditCardsParams {
  // Selected company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result message for ListCreditCards
message ListCreditCardsResult {
  // credit card list
  repeated moego.models.payment.v1.CreditCardModel cards = 1;
}

// The params message for CreateCreditCard
message CreateCreditCardParams {
  // Selected company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Credit card
  models.payment.v1.CreditCardCreateDef card = 2 [(validate.rules).message = {required: true}];
}

// The result message for CreateCreditCard
message CreateCreditCardResult {
  // id of the credit card
  string id = 1;
}

// credit card service
service CreditCardService {
  // get credit card list
  rpc GetCreditCardList(GetCreditCardListRequest) returns (GetCreditCardListResponse);

  // List credit cards
  // Applies to account structure after migration
  rpc ListCreditCards(ListCreditCardsParams) returns (ListCreditCardsResult);

  // Create a new credit card
  // Applies to account structure after migration
  rpc CreateCreditCard(CreateCreditCardParams) returns (CreateCreditCardResult);
}
