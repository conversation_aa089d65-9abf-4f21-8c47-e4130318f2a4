syntax = "proto3";

package moego.client.payment.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/payment/v1;paymentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.payment.v1";

// The params message for GetCancellationFee
message GetCancellationFeeParams {
  // Cancelled appointment id
  int64 appointment_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// The result message for GetCancellationFee
message GetCancellationFeeResult {
  // cancellation fee
  double cancellation_fee = 1;
  // convenience fee
  double tax_and_fees = 2;
  // total fee
  double total = 3;
}

// Cancellation service
service CancellationService {
  // Prepare cancellation
  // Get cancellation fee, convenience fee
  rpc GetCancellationFee(GetCancellationFeeParams) returns (GetCancellationFeeResult);
}
