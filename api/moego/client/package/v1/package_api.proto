// @since 2025-03-15 14:21:42
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.client.package.v1;

import "moego/models/business_customer/v1/business_customer_defs.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/business_customer/v1/business_customer_pet_defs.proto";
import "moego/models/offering/v1/service_models.proto";
import "moego/models/order/v1/order_detail_models.proto";
import "moego/models/organization/v1/company_models.proto";
import "moego/models/organization/v1/location_models.proto";
import "moego/models/package/v1/customer_package_models.proto";
import "moego/models/package/v1/package_models.proto";
import "moego/models/payment/v1/credit_card_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/package/v1;packageapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.pkg.v1";

// the package service
service PackageService {
  // list packages
  rpc ListPackages(ListPackagesParams) returns (ListPackagesResult) {}
  // list customer packages, only return the packages that the customer has purchased
  rpc ListCustomerPackages(ListCustomerPackagesParams) returns (ListCustomerPackagesResult) {}
  // create sell link
  rpc CreateSellLink(CreateSellLinkParams) returns (CreateSellLinkResult) {}
  // get sell link public info
  rpc GetSellLinkPublicInfo(GetSellLinkPublicInfoParams) returns (GetSellLinkPublicInfoResult) {}
}

// list packages
message ListPackagesParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // only return active packages
  bool only_active = 3;
  // pagination, default size is 20
  optional moego.utils.v2.PaginationRequest pagination = 4;
}

// list packages result
message ListPackagesResult {
  // packages
  repeated moego.models.package.v1.PackageModel packages = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
  // services
  repeated moego.models.offering.v1.ServiceBriefView services = 3;
}

// list customer packages params
message ListCustomerPackagesParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // filter
  message Filter {
    // is expired
    optional bool is_expired = 1;
  }
  // filter
  Filter filter = 3;
  // pagination, default size is 200
  optional moego.utils.v2.PaginationRequest pagination = 4;
}

// list customer packages result
message ListCustomerPackagesResult {
  // packages
  repeated moego.models.package.v1.CustomerPackageModel packages = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
  // services
  repeated moego.models.offering.v1.ServiceBriefView services = 3;
}

// create sell link params
message CreateSellLinkParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // items
  message Item {
    // package id
    int64 package_id = 1 [(validate.rules).int64 = {gt: 0}];
    // quantity
    int32 quantity = 2 [(validate.rules).int32 = {gt: 0}];
  }
  // items
  repeated Item items = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 200
  }];
  // customer with pet info (only for new customer)
  message CustomerWithPetInfo {
    // customer with additional info
    moego.models.business_customer.v1.BusinessCustomerWithAdditionalInfoCreateDef customer_with_additional_info = 1 [(validate.rules).message = {required: true}];

    // pets with additional info
    repeated moego.models.business_customer.v1.BusinessCustomerPetWithAdditionalInfoCreateDef pets_with_additional_info = 2 [(validate.rules).repeated = {
      max_items: 20
      items: {
        message: {required: true}
      }
    }];
  }
  // customer with pet info
  optional CustomerWithPetInfo customer_with_pet_info = 4;
}

// create sell link result
message CreateSellLinkResult {
  // sell link
  string sell_link = 1;
}

// get sell link public info params
message GetSellLinkPublicInfoParams {
  // the sell link public token
  string public_token = 1 [(validate.rules).string = {
    min_len: 20
    max_len: 100
  }];
}

// get sell link public info result
message GetSellLinkPublicInfoResult {
  // location info
  moego.models.organization.v1.LocationModelPublicView business = 1;
  // customer info
  moego.models.business_customer.v1.BusinessCustomerModelPublicView customer = 2;
  // customer card on files
  repeated moego.models.payment.v1.CreditCardModelPublicView customer_cards = 3;
  // order detail
  moego.models.order.v1.OrderDetailModel order_detail = 4;
  // package info
  repeated moego.models.package.v1.PackageModel packages = 5;
  // company preference
  moego.models.organization.v1.CompanyPreferenceSettingModel company_preference = 6;
  // fee
  message Fee {
    // convenience fee
    double convenience_fee = 1;
  }
  // fee
  Fee fee = 7;
}
