syntax = "proto3";

package moego.client.customer.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/customer/v1;customerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.customer.v1";

// The params for GetAnonymousInfo
message GetAnonymousInfoParams {
  // the customer code
  string customer_code = 1 [(validate.rules).string = {
    min_len: 8
    max_len: 8
  }];
}

// The result for GetAnonymousInfo
message GetAnonymousInfoResult {
  // TODO customer profile

  // the branded app config
  BrandedAppConfig config = 3;

  // The branded app config
  message BrandedAppConfig {
    // Android download link
    string android_download_link = 1;
    // iOS download link
    string ios_download_link = 2;
  }
}

// customer service
service CustomerService {
  // get anonymous info
  rpc GetAnonymousInfo(GetAnonymousInfoParams) returns (GetAnonymousInfoResult);
}
