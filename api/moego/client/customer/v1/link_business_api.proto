syntax = "proto3";

package moego.client.customer.v1;

import "moego/models/business/v1/business_models.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/online_booking/v1/business_ob_gallery_models.proto";
import "moego/models/risk_control/v1/verification_code_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/customer/v1;customerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.customer.v1";

// get link business list params
message GetLinkBusinessListParams {}

// get link business list result
message GetLinkBusinessListResult {
  // link business list
  repeated GetLinkBusinessResult link_businesses = 1;
}

// get link business result
message GetLinkBusinessResult {
  // business info
  moego.models.business.v1.BusinessModel business = 1;
  // online booking gallery first image
  moego.models.online_booking.v1.BusinessOBGalleryClientPortalView ob_gallery = 3;
}

// link business params
message LinkBusinessParams {
  // invitation code
  string invitation_code = 2;
}

// link business result
message LinkBusinessResult {}

// send verification code params
message SendVerificationCodeParams {
  // account identifier
  oneof identifier {
    option (validate.required) = true;
    // email
    string email = 1 [(validate.rules).string = {
      email: true
      max_len: 100
    }];
    // phone number
    string phone_number = 2 [(validate.rules).string = {pattern: "^\\+[1-9]\\d{1,18}$"}];
  }
}

// send verification code result
message SendVerificationCodeResult {
  // verification token
  string token = 1;
}

// check verification code params
message CheckVerificationCodeParams {
  // verification code
  models.risk_control.v1.VerificationCodeDef verification = 1;
}

// check verification code result
message CheckVerificationCodeResult {}

// get invitation business info params
message GetInvitationBusinessInfoParams {
  // invitation code
  string invitation_code = 1;
}

// get invitation business info result
message GetInvitationBusinessInfoResult {
  // business country code
  string country_code = 1;
}

// get customer profile params
message GetCustomerProfileParams {
  // invitation code
  string invitation_code = 1;
  // phone number
  string phone_number = 2 [(validate.rules).string = {pattern: "^\\+[1-9]\\d{1,18}$"}];
}

// get customer profile result
message GetCustomerProfileResult {
  // customer profile
  moego.models.business_customer.v1.BusinessCustomerModelLinkView customer = 1;
}

// link business service
service LinkBusinessService {
  // get link business list
  rpc GetLinkBusinessList(GetLinkBusinessListParams) returns (GetLinkBusinessListResult);
  // link business
  rpc LinkBusiness(LinkBusinessParams) returns (LinkBusinessResult);
  // check business invitation code and phone number
  rpc GetInvitationBusinessInfo(GetInvitationBusinessInfoParams) returns (GetInvitationBusinessInfoResult);
  // get phone number profile
  rpc GetCustomerProfile(GetCustomerProfileParams) returns (GetCustomerProfileResult);
  // send verification code
  rpc SendVerificationCode(SendVerificationCodeParams) returns (SendVerificationCodeResult);
  // check verification code
  rpc CheckVerificationCode(CheckVerificationCodeParams) returns (CheckVerificationCodeResult);
}
