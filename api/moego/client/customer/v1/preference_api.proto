syntax = "proto3";

package moego.client.customer.v1;

import "moego/models/customer/v1/customer_preference_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/customer/v1;customerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.customer.v1";

// get preference params
message GetPreferenceParams {}

// get preference result
message GetPreferenceResult {
  // preference detail
  models.customer.v1.CustomerPreferenceView preference = 1;
}

// Preference set up
service PreferenceService {
  // get preference detail, contains currency, date format, time format, weight unit, distance unit etc.
  rpc GetPreference(GetPreferenceParams) returns (GetPreferenceResult);
}
