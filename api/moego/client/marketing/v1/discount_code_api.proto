syntax = "proto3";

package moego.client.marketing.v1;

import "moego/models/marketing/v1/discount_code_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/marketing/v1;marketingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.marketing.v1";

// get business discount code config params
message GetDiscountCodeConfigParams {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get business discount code config result
message GetDiscountCodeConfigResult {
  // business has valid discount code
  bool has_valid_discount_code = 1;
}

// check discount code params
message CheckDiscountCodeParams {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // discount code
  string discount_code = 2 [(validate.rules).string = {max_len: 20}];
  // service ids, include the ID collection of services and add-ons
  repeated int64 service_ids = 3 [(validate.rules).repeated = {
    min_items: 1
    items: {
      int64: {gt: 0}
    }
  }];
  // appointment date, used to verify the specific appointment datetime within the validity period of Discount Code
  optional string appointment_date = 4 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
}

// check discount code result
message CheckDiscountCodeResult {
  // discount code detail info
  models.marketing.v1.DiscountCodeModelOnlineBookingView discount_code = 1;
}

// the discount code service for marketing
service DiscountCodeService {
  // Get the OB Discount Code available for the specified business
  rpc GetDiscountCodeConfig(GetDiscountCodeConfigParams) returns (GetDiscountCodeConfigResult);
  // Verify whether the discount code is available
  rpc CheckDiscountCode(CheckDiscountCodeParams) returns (CheckDiscountCodeResult);
}
