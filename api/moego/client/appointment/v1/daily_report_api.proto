syntax = "proto3";

package moego.client.appointment.v1;

import "moego/models/appointment/v1/daily_report_defs.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.appointment.v1";

// list daily report config params
message ListDailyReportConfigParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // pet id
  int64 pet_id = 3 [(validate.rules).int64 = {gt: 0}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 4;
}

// list daily report config result
message ListDailyReportConfigResult {
  // report list
  repeated models.appointment.v1.DailyReportConfigDef report_configs = 1;
  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// daily report service
service DailyReportService {
  // list daily report config
  rpc ListDailyReportConfig(ListDailyReportConfigParams) returns (ListDailyReportConfigResult);
}
