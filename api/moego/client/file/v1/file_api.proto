syntax = "proto3";

package moego.client.file.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/file/v1;fileapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.file.v1";

// get upload presigned url params
message GetUploadPresignedUrlParams {
  // usage for the file ,eg: photo,avatar...
  string usage = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // file md5 after base64
  string md5 = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // file name(with extension)
  string file_name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // file size byte
  int64 file_size_byte = 5 [(validate.rules).int64 = {
    gte: 0
    lte: 10485760
  }];
  // owner type,eg:staff,pet...
  string owner_type = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // owner id
  int64 owner_id = 7 [(validate.rules).int64 = {gt: 0}];
}

// get upload presigned url result
message GetUploadPresignedUrlResult {
  // presigned url for upload
  string presigned_url = 1;
  // access url for download
  string access_url = 2;
  // content-type,should add it into header when uploading file
  string content_type = 3;
}

// FileService
service FileService {
  /*
     get presigned url and use put method to upload file with header 'content-md5: md5' and 'content_type: content_type'.
     set header 'x-amz-acl: public-read' to let the file be public
  */
  rpc GetUploadPresignedUrl(GetUploadPresignedUrlParams) returns (GetUploadPresignedUrlResult);
}
