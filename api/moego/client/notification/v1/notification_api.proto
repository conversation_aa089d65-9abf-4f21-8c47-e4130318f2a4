syntax = "proto3";

package moego.client.notification.v1;

import "moego/models/notification/v1/notification_defs.proto";
import "moego/models/notification/v1/notification_enums.proto";
import "moego/models/notification/v1/notification_models.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/notification/v1;notificationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.notification.v1";

// get notification list params
message GetNotificationListParams {
  // notification type
  repeated models.notification.v1.NotificationType types = 1;
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 2;
  // sort by
  repeated moego.models.notification.v1.NotificationSortDef sorts = 3;
}

// get notification list result
message GetNotificationListResult {
  // notification list
  repeated models.notification.v1.NotificationModelClientView notifications = 1;
  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 4;
}

// notification service
service NotificationService {
  // get notification list
  rpc GetNotificationList(GetNotificationListParams) returns (GetNotificationListResult);
}
