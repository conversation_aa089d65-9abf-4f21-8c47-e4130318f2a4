syntax = "proto3";

package moego.client.grooming.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "moego/models/grooming/v1/grooming_report_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/grooming/v1;groomingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.grooming.v1";

// get grooming report list request
message GetGroomingReportListRequest {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64.gt = 0];
}

// get grooming report list response
message GetGroomingReportListResponse {
  // grooming report
  repeated moego.models.grooming.v1.GroomingReportModelClientView reports = 1;
}

//  list grooming report card params
message ListGroomingReportCardParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // pet id
  int64 pet_id = 3 [(validate.rules).int64 = {gt: 0}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 4;
}

//  list grooming report card result
message ListGroomingReportCardResult {
  // grooming report card list
  repeated GroomingReportCardDef report_configs = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

//  grooming report card def
message GroomingReportCardDef {
  // report card id
  int64 report_card_id = 1;
  // send time
  google.protobuf.Timestamp send_time = 2;
  // uuid
  string uuid = 3;
  // pet id
  int64 pet_id = 4;
  // appointment id
  int64 appointment_id = 5;
  // service date, date of report
  google.type.Date service_date = 6;
}

// grooming report service
service GroomingReportService {
  // get grooming report list
  rpc GetGroomingReportList(GetGroomingReportListRequest) returns (GetGroomingReportListResponse);
  // list grooming report card
  rpc ListGroomingReportCard(ListGroomingReportCardParams) returns (ListGroomingReportCardResult);
}
