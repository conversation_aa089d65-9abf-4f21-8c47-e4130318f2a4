syntax = "proto3";

package moego.client.grooming.v2;

import "moego/models/agreement/v1/agreement_record_defs.proto";
import "moego/models/online_booking/v1/payments_defs.proto";
import "moego/models/online_booking/v1/selected_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/grooming/v2;groomingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.grooming.v2";

// create booking request params
message CreateBookingRequestParams {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet selected service list
  // Contains the main service and multiple add-ons selected by each pet
  repeated moego.models.online_booking.v1.SelectedPetServiceDef selected_pet_services = 2 [(validate.rules).repeated = {min_items: 1}];
  // selected staff id
  // This param may be empty if staff selection is not enabled or the address is not within the service area and
  // the "Allow all clients to submit request without date and time" switch is turned on.
  optional int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
  // selected appointment date
  // This param may be empty if the address is not within the service area and
  // the "Allow all clients to submit request without date and time" switch is turned on.
  optional string appointment_date = 4 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // selected appointment start time, in minutes
  // This param may be empty if display "Date only" or the address is not within the service area and
  // the "Allow all clients to submit request without date and time" switch is turned on.
  optional int32 appointment_start_time = 5 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // additional note
  optional string additional_note = 7 [(validate.rules).string = {max_len: 65535}];
  // sign agreements
  repeated moego.models.agreement.v1.AgreementRecordDef sign_agreements = 6;
  // payment, If payments is not turned on, it will not be required.
  oneof payment {
    // card on file, one star shield
    // To require clients to input valid credit card on file when submitting booking requests.
    moego.models.online_booking.v1.CardOnFileDef card_on_file = 11;
    // prepay, four stars shield
    // To require clients to pay up to the full amount of your service fee when submitting booking requests.
    moego.models.online_booking.v1.PrepayDef prepay = 12;
    // pre-auth, three stars shield
    // To require a valid card on file upon booking.
    // The card will be pre-authorized for the client's ticket amount 24 hours prior to the appointment, safeguarding revenue and reducing no-shows.
    moego.models.online_booking.v1.PreAuthDef pre_auth = 13;
  }
  // discount code
  optional string discount_code = 8 [(validate.rules).string = {max_len: 20}];

  // selected address id, default is primary address
  optional int64 address_id = 9 [(validate.rules).int64 = {gt: 0}];
}

// create booking request result
message CreateBookingRequestResult {
  // auto accept flag
  bool is_auto_accept = 1;
}

// pre submit booking request result
message PreSubmitBookingRequestResult {
  // result
  bool result = 1;
}

// booking request service
service BookingRequestService {
  // pre submit online booking request
  // If staff and date time are selected, the corresponding time will be locked to prevent other clients from selecting it.
  // It can prevent multiple clients from choosing the same time and causing some order payment failures.
  rpc PreSubmitBookingRequest(CreateBookingRequestParams) returns (PreSubmitBookingRequestResult);
  // create booking request
  rpc CreateBookingRequest(CreateBookingRequestParams) returns (CreateBookingRequestResult);
}
