syntax = "proto3";

package moego.client.online_booking.v2;

import "moego/service/appointment/v2/appointment_service.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v2;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.online_booking.v2";

// Appointment api for OBC
service AppointmentAPI {
  // Update appointment
  rpc UpdateAppointment(UpdateAppointmentParams) returns (UpdateAppointmentResult);
}

// Update appointment params
message UpdateAppointmentParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // appointment id
  int64 appointment_id = 3 [(validate.rules).int64.gt = 0];

  // appointment to update
  optional moego.service.appointment.v2.UpdateAppointmentRequest.Appointment appointment = 5;

  // pet details to update
  repeated moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail pet_details = 4;
}

// Update appointment result
message UpdateAppointmentResult {}
