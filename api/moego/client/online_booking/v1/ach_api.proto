syntax = "proto3";

package moego.client.online_booking.v1;

import "moego/models/payment/v1/ach_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.online_booking.v1";

// ACH service
service ACHService {
  // add ACH on file
  rpc AddACHOnFile(AddACHOnFileParams) returns (AddACHOnFileResult);
  // delete credit card
  rpc DeleteACHOnFile(DeleteACHOnFileParams) returns (DeleteACHOnFileResult);
  // list ACH on files
  rpc ListACHOnFiles(ListACHOnFilesParams) returns (ListACHOnFilesResult);
  // create ACH setup intent
  rpc CreateACHSetupIntent(CreateACHSetupIntentParams) returns (CreateACHSetupIntentResult);
}

// create ACH setup intent params
message CreateACHSetupIntentParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// create ACH setup intent result
message CreateACHSetupIntentResult {
  // setup intent id
  string setup_intent_id = 1;
  // client secret
  string client_secret = 2;
}

// add ACH on file params
message AddACHOnFileParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // payment method id
  string payment_method_id = 3 [(validate.rules).string = {min_len: 1}];
}

// add ACH on file result
message AddACHOnFileResult {
  // ach view
  moego.models.payment.v1.ACHView ach_view = 1;
}

// delete credit card params
message DeleteACHOnFileParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // payment method id
  string payment_method_id = 3 [(validate.rules).string = {min_len: 1}];
}

// delete credit card result
message DeleteACHOnFileResult {}

// list ACH on files params
message ListACHOnFilesParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// list ACH on files result
message ListACHOnFilesResult {
  // ach view list
  repeated moego.models.payment.v1.ACHView ach_views = 1;
}
