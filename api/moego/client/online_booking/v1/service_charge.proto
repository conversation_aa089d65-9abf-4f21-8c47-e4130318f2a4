syntax = "proto3";

package moego.client.online_booking.v1;

import "moego/models/business_customer/v1/business_pet_schedule_setting_enums.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/order/v1/service_charge_model.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.online_booking.v1";

// Get feeding and medication charge params
message GetFeedingMedicationChargeParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // schedule type
  moego.models.business_customer.v1.BusinessPetScheduleType schedule_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // food source ids
  repeated int64 food_source_ids = 4 [(validate.rules).repeated = {
    min_items: 0
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Get feeding and medication charge result
message GetFeedingMedicationChargeResult {
  // service charge list
  repeated moego.models.order.v1.FeedingMedicationChargeView service_charges = 1;
}

// service charge service
service ServiceChargeService {
  // Get feeding and medication charge
  rpc GetFeedingMedicationCharge(GetFeedingMedicationChargeParams) returns (GetFeedingMedicationChargeResult);
}
