syntax = "proto3";

package moego.client.online_booking.v1;

import "moego/models/business_customer/v1/business_customer_pet_defs.proto";
import "moego/models/business_customer/v1/business_customer_pet_models.proto";
import "moego/models/business_customer/v1/business_pet_vaccine_record_models.proto";
import "moego/models/business_customer/v1/business_pet_vaccine_request_defs.proto";
import "moego/models/business_customer/v1/business_pet_vaccine_request_models.proto";
import "moego/models/customer/v1/customer_pet_vaccine_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.online_booking.v1";

// PetService is the service for pet management.
service PetService {
  // CreatePet creates a new pet.
  rpc CreatePet(CreatePetParams) returns (CreatePetResult) {}
  // GetPet gets a pet by id.
  rpc GetPet(GetPetParams) returns (GetPetResult) {}
  // ListPets lists all pets.
  rpc ListPets(ListPetsParams) returns (ListPetsResult) {}
  // UpdatePet updates a pet by id.
  rpc UpdatePet(UpdatePetParams) returns (UpdatePetResult) {}

  // submit pet vaccine request
  rpc SubmitPetVaccineRequest(SubmitPetVaccineRequestParams) returns (SubmitPetVaccineRequestResult);

  // list pet vaccine expiration status
  rpc ListPetVaccineExpirationStatus(ListPetVaccineExpirationStatusParams) returns (ListPetVaccineExpirationStatusResult);
}

// CreatePetParams is the params for creating a pet.
message CreatePetParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // pet with additional info
  moego.models.business_customer.v1.BusinessCustomerPetWithAdditionalInfoCreateDef def = 3 [(validate.rules).message.required = true];
}

// CreatePetResult is the result for creating a pet.
message CreatePetResult {
  // pet model
  moego.models.business_customer.v1.BusinessCustomerPetModel pet = 1;
}

// GetPetParams is the params for getting a pet.
message GetPetParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // pet id
  int64 id = 3 [(validate.rules).int64.gt = 0];
}

// GetPetResult is the result for getting a pet.
message GetPetResult {
  // pet model
  moego.models.business_customer.v1.BusinessCustomerPetModel pet = 1;
}

// ListPetsParams is the params for listing
message ListPetsParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // include vaccine records in the result, default is false (not include)
  bool include_vaccine_records = 3;
  // include pending vaccine requests in the result, default is false (not include)
  bool include_pending_vaccine_requests = 4;
  // include passed away pets in the result, default is false (not include)
  bool include_passed_away = 5;
}

// ListPetsResult is the result for listing
message ListPetsResult {
  // pet models
  repeated moego.models.business_customer.v1.BusinessCustomerPetModel pets = 1;
  // vaccine records
  repeated models.business_customer.v1.BusinessPetVaccineRecordBindingModel vaccine_records = 2;
  // pending vaccine requests
  repeated models.business_customer.v1.BusinessPetVaccineRequestBindingModel pending_vaccine_requests = 3;
}

// UpdatePetParams is the params for updating a pet.
message UpdatePetParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // pet id
  int64 id = 3 [(validate.rules).int64.gt = 0];
  // pet with additional info
  moego.models.business_customer.v1.BusinessCustomerPetUpdateDef def = 4 [(validate.rules).message.required = true];
  // pet vaccine list
  // deprecated. vaccines should be submitted via SubmitPetVaccineRecordRequest method
  repeated moego.models.customer.v1.VaccineDef vaccines = 5 [deprecated = true];
  // Whether to send notification to business, default is false
  optional bool send_notification = 6;
}

// UpdatePetResult is the result for updating a pet.
message UpdatePetResult {
  // pet model
  moego.models.business_customer.v1.BusinessCustomerPetModel pet = 1;
}

// SubmitPetVaccineRequestParams is the params for submitting pet vaccine request
message SubmitPetVaccineRequestParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // pet id
  int64 pet_id = 3 [(validate.rules).int64 = {gt: 0}];
  // pet vaccine request id.
  // if set, update the existing pet vaccine request
  optional int64 id = 4 [(validate.rules).int64 = {gt: 0}];
  // pet vaccine request
  models.business_customer.v1.BusinessPetVaccineRequestCreateDef pet_vaccine_request = 5 [(validate.rules).message.required = true];
}

// SubmitPetVaccineRequestResult is the result for submitting pet vaccine request
message SubmitPetVaccineRequestResult {}

// ListPetVaccineExpirationStatusParams is the params for listing pet vaccine expiration status
message ListPetVaccineExpirationStatusParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// ListPetVaccineExpirationStatusResult is the result for listing pet vaccine expiration status
message ListPetVaccineExpirationStatusResult {
  // pet vaccine expiration status map (pet 维度),
  // key is pet id, value is pet vaccine expiration status
  map<int64, PetVaccineExpirationStatus> pet_vaccine_expiration_status = 1;

  // pet vaccine record expiration status map (pet vaccine record 维度),
  // key is pet vaccine record id, value is pet vaccine expiration status
  map<int64, PetVaccineExpirationStatus> pet_vaccine_record_expiration_status = 2;
}

// pet vaccine expiration status
// 这个枚举是给 OBC portal 定制的，不是 vaccine record 里定义的，所以放在这里
enum PetVaccineExpirationStatus {
  // unspecified
  PET_VACCINE_EXPIRATION_STATUS_UNSPECIFIED = 0;
  // no record
  PET_VACCINE_EXPIRATION_STATUS_NO_RECORD = 1;
  // not expired
  PET_VACCINE_EXPIRATION_STATUS_NOT_EXPIRED = 2;
  // expired
  PET_VACCINE_EXPIRATION_STATUS_EXPIRED = 3;
  // expired soon (expired before next appointment)
  PET_VACCINE_EXPIRATION_STATUS_EXPIRED_BEFORE_NEXT_APPOINTMENT = 4;
  // expired soon (expired in 30 days)
  PET_VACCINE_EXPIRATION_STATUS_EXPIRED_IN_30_DAYS = 5;
}
