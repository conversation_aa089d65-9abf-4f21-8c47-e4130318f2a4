syntax = "proto3";

package moego.client.online_booking.v1;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/business_customer/v1/business_customer_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.online_booking.v1";

// Business customer service
service BusinessCustomerService {
  // Create customer
  rpc CreateCustomer(CreateCustomerParams) returns (CreateCustomerResult);
  // Update customer
  rpc UpdateCustomer(UpdateCustomerParams) returns (UpdateCustomerResult);
  // Verify phone number
  rpc VerifyPhoneNumber(VerifyPhoneNumberParams) returns (VerifyPhoneNumberResult);
  // Confirm phone number
  rpc ConfirmPhoneNumber(ConfirmPhoneNumberParams) returns (ConfirmPhoneNumberResult);
  // Change login phone number
  rpc ChangeLoginPhoneNumber(ChangeLoginPhoneNumberParams) returns (ChangeLoginPhoneNumberResult);
}

// Create customer params
message CreateCustomerParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // customer
  Customer customer = 3 [(validate.rules).message = {required: true}];

  // customer def
  message Customer {
    // first name
    string first_name = 1 [(validate.rules).string = {max_len: 50}];
    // last name
    string last_name = 2 [(validate.rules).string = {max_len: 50}];
    // phone number
    string phone_number = 3 [(validate.rules).string = {max_len: 20}];
    // email
    string email = 4 [(validate.rules).string = {
      ignore_empty: true
      email: true
      max_len: 50
    }];
    // Birthday
    optional google.protobuf.Timestamp birthday = 5;
    // Additional info
    optional AdditionalInfo additional_info = 6;
    // Address
    optional Address address = 7;
    // Answers map
    map<string, google.protobuf.Value> answers_map = 8;

    // OB additional info
    message AdditionalInfo {
      // Referral source
      optional int32 referral_source_id = 1 [(validate.rules).int32 = {gt: 0}];
      // Referral source desc
      optional string referral_source_desc = 2 [(validate.rules).string = {max_len: 255}];
      // Preferred groomer
      optional int32 preferred_groomer_id = 3 [(validate.rules).int32 = {gte: 0}];
      // Preferred frequency
      optional int32 preferred_frequency_day = 4 [(validate.rules).int32 = {gte: 0}];
      // Preferred frequency type (0-by days, 1-by weeks)
      optional int32 preferred_frequency_type = 5 [(validate.rules).int32 = {gte: 0}];
      // Preferred days of the week
      repeated int32 preferred_day = 6;
      // Preferred times of the day
      repeated int32 preferred_time = 7;
    }

    // address
    message Address {
      // address 1
      optional string address1 = 1 [(validate.rules).string = {
        min_len: 1
        max_len: 255
      }];
      // address 2
      optional string address2 = 2 [(validate.rules).string = {
        min_len: 0
        max_len: 255
      }];
      // city
      optional string city = 3 [(validate.rules).string = {
        min_len: 0
        max_len: 255
      }];
      // state
      optional string state = 4 [(validate.rules).string = {
        min_len: 0
        max_len: 255
      }];
      // zip code
      optional string zipcode = 5 [(validate.rules).string = {
        min_len: 0
        max_len: 50
      }];
      // country
      optional string country = 6 [(validate.rules).string = {
        min_len: 1
        max_len: 255
      }];
      // lat
      optional string lat = 7 [(validate.rules).string = {max_len: 50}];
      // lng
      optional string lng = 8 [(validate.rules).string = {max_len: 50}];
    }
  }
}

// Create customer result
message CreateCustomerResult {}

// Update customer params
message UpdateCustomerParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // customer id
  int64 id = 3 [(validate.rules).int64.gt = 0];
  // customer update def
  models.business_customer.v1.BusinessCustomerUpdateDef customer = 4 [(validate.rules).message = {required: true}];
  // Whether to send notification to business, default is false
  optional bool send_notification = 5;
}

// Update customer result
message UpdateCustomerResult {}

// Verify phone number params
message VerifyPhoneNumberParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // customer id, optional
  // 如果传了，则会校验 primary contact 是否跟 phone number 一致
  optional int64 id = 3 [(validate.rules).int64 = {
    gt: 0
    ignore_empty: true
  }];
  // phone number
  string phone_number = 4 [(validate.rules).string = {pattern: "^\\d{1,18}$"}];
}

// Verify phone number result
message VerifyPhoneNumberResult {
  // verification token, used for verification together with the verification code
  string token = 1;
}

// Confirm phone number params
message ConfirmPhoneNumberParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // phone number
  string phone_number = 3 [(validate.rules).string = {pattern: "^\\d{1,18}$"}];
  // verification token, used for verification together with the verification code
  string token = 4;
  // verification code
  string code = 5 [(validate.rules).string = {pattern: "^[0-9]{6}$"}];
}

// Confirm phone number result
message ConfirmPhoneNumberResult {
  // success
  bool success = 1;
}

// Change login phone number params
message ChangeLoginPhoneNumberParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // phone number
  string phone_number = 3 [(validate.rules).string = {pattern: "^\\d{1,18}$"}];
  // verification token, used for verification together with the verification code
  string token = 4;
  // verification code
  string code = 5 [(validate.rules).string = {pattern: "^[0-9]{6}$"}];
}

// Change login phone number result
message ChangeLoginPhoneNumberResult {
  // success
  bool success = 1;
}
