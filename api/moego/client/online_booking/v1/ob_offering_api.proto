// @since 2024-04-11 12:16:15
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.client.online_booking.v1;

import "moego/models/offering/v1/pricing_rule_defs.proto";
import "moego/models/offering/v1/pricing_rule_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/offering/v1/service_models.proto";
import "moego/models/offering/v2/pricing_rule_models.proto";
import "moego/models/online_booking/v1/ob_offering_defs.proto";
import "moego/models/online_booking/v1/ob_offering_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.online_booking.v1";

// get applicable offerings request
message GetApplicableOfferingsRequest {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 3;
  // care type id
  optional int64 care_type_id = 4;
}

// get applicable offerings response
message GetApplicableOfferingsResponse {
  // service category list
  repeated models.online_booking.v1.OBOfferingCategoryView service_categories = 1;
  // add on list
  repeated models.online_booking.v1.OBOfferingCategoryView addon_categories = 2;
}

// list pricing rule params
message ListPricingRuleParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // filter
  optional moego.models.offering.v1.ListPricingRuleFilter filter = 3;

  // date selection type
  oneof date_selection_type {
    // date range
    moego.models.online_booking.v1.ServiceDateRangeDef date_range = 11;
    // date list
    moego.models.online_booking.v1.ServiceDateListDef date_list = 12;
  }
}

// list pricing rule result
message ListPricingRuleResult {
  // the pricing rule list
  repeated moego.models.offering.v1.PricingRuleModel pricing_rules = 1 [deprecated = true];
  // the pricing rule list
  repeated moego.models.offering.v2.PricingRule pricing_rules_v2 = 2;
  // service models
  repeated moego.models.offering.v1.ServiceModel services = 3;
}

// the offering service
service OBOfferingService {
  // get applicable offerings
  rpc GetApplicableOfferings(GetApplicableOfferingsRequest) returns (GetApplicableOfferingsResponse);
  // list pricing rule
  rpc ListPricingRule(ListPricingRuleParams) returns (ListPricingRuleResult);
}
