syntax = "proto3";

package moego.client.online_booking.v1;

import "moego/models/membership/v1/membership_defs.proto";
import "moego/models/membership/v1/redeem_models.proto";
import "moego/models/membership/v1/subscription_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.online_booking.v1";

// the membership service
service MembershipService {
  // list available membership
  rpc ListAvailableMemberships(ListAvailableMembershipsParams) returns (ListAvailableMembershipsResult);
  // list subscriptions
  rpc ListSubscriptions(ListSubscriptionsParams) returns (ListSubscriptionsResult);
  // list membership perks
  rpc ListMembershipPerks(ListMembershipPerksParams) returns (ListMembershipPerksResult);
  // list redeem history
  rpc ListRedeemHistory(ListRedeemHistoryParams) returns (ListRedeemHistoryResult);
}

// list membership params
message ListAvailableMembershipsParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// list membership result
message ListAvailableMembershipsResult {
  // the benefits
  repeated BenefitDetail benefits = 1;
  // the available memberships benefit
  message BenefitDetail {
    // service id
    int64 service_id = 1;
    // target type
    models.membership.v1.TargetType type = 2;
    // is free
    bool is_free = 3;
    // discount unit
    optional models.membership.v1.DiscountUnit discount_unit = 4;
    // discount value
    optional double discount_value = 5;
  }
}

// list subscriptions
message ListSubscriptionsParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // filter by status
  repeated moego.models.membership.v1.SubscriptionModel.Status status_in = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 4;
}

// list subscription result
message ListSubscriptionsResult {
  // the subscriptions
  repeated moego.models.membership.v1.MembershipSubscriptionModel membership_subscriptions = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// list membership perks
message ListMembershipPerksParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // membership id
  int64 membership_id = 3;
}

// list membership perks result
message ListMembershipPerksResult {
  // the perk cycle items
  repeated models.membership.v1.PerkCycleItemDef perk_cycle_item = 1;
  // the perk in membership cycle
  repeated models.membership.v1.IncludeBenefitView membership_perks = 2;
}

// list redeem history params
message ListRedeemHistoryParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // the membership id
  int64 membership_id = 3 [(validate.rules).int64 = {gt: 0}];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 4;
}

// list redeem history result
message ListRedeemHistoryResult {
  // the redeem history
  repeated moego.models.membership.v1.RedeemHistory redeem_history = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}
