syntax = "proto3";

package moego.admin.agreement.v1;

import "moego/models/agreement/v1/agreement_enums.proto";
import "moego/models/agreement/v1/agreement_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/agreement/v1;agreementapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.agreement.v1";

// get agreement input
message GetAgreementParams {
  // id
  int64 id = 1;
  // business id
  int64 business_id = 2;
}

// get agreement model list params
message GetAgreementListParams {
  // business id
  optional int64 business_id = 1 [(validate.rules).int64.gte = 0];
  // service type
  optional int64 service_type = 2 [(validate.rules).int64.gt = 0];
  // agreement id list
  repeated int64 ids = 3;
  // status: normal, deleted
  optional int32 status = 4 [(validate.rules).int32.gt = 0];
}

// agreement model list
message GetAgreementListResult {
  // agreement list
  repeated moego.models.agreement.v1.AgreementModelSimpleView agreement_simple_view = 1;
  // signed policy map
  map<int32, string> signed_policy_map = 2;
  // status map
  map<int32, string> status_map = 3;
  // service type map
  map<int32, string> service_type_map = 4;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// delete agreement params
message DeleteAgreementParams {
  // id
  int64 id = 1;
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gte = 0];
}

// DeleteAgreementResponse
message DeleteAgreementResult {
  // number of delete
  int32 number = 1;
}

// UpdateAgreementParams
message UpdateAgreementParams {
  // agreement id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // signed policy, see definition in SignedPolicy
  optional moego.models.agreement.v1.SignedPolicy signed_policy = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // agreement title
  optional string agreement_title = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // agreement content
  optional string agreement_content = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 1048576
  }];
  // template for send sms
  optional string sms_template = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 65536
  }];
  // email template title
  optional string email_template_title = 7 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // email template body
  optional string email_template_body = 8 [(validate.rules).string = {
    min_len: 1
    max_len: 131072
  }];
  // whether to update last_required_time
  optional bool update_last_required_time = 9;
}

// AddAgreementParams
message AddAgreementParams {
  // business id
  int64 business_id = 1;
  // creator id
  int64 creator_id = 2;
  // signed policy, see definition in SignedPolicy
  moego.models.agreement.v1.SignedPolicy signed_policy = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service type, see definition in ServiceType
  int32 service_types = 4 [(validate.rules).int32 = {gt: 0}];
  // agreement title
  optional string agreement_title = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // agreement content
  optional string agreement_content = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 1048576
  }];
  // template for send sms
  optional string sms_template = 7 [(validate.rules).string = {
    min_len: 1
    max_len: 65536
  }];
  // email template title
  optional string email_template_title = 8 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // email template body
  optional string email_template_body = 9 [(validate.rules).string = {
    min_len: 1
    max_len: 131072
  }];
  // business name
  optional string business_name = 10 [(validate.rules).string = {max_len: 256}];
}

// Agreement admin API
service AgreementService {
  // get agreement
  rpc GetAgreement(GetAgreementParams) returns (moego.models.agreement.v1.AgreementModel);
  // get agreement list
  rpc GetAgreementList(GetAgreementListParams) returns (GetAgreementListResult);
  // delete agreement
  rpc DeleteAgreement(DeleteAgreementParams) returns (DeleteAgreementResult);
  // update a agreement
  rpc UpdateAgreement(UpdateAgreementParams) returns (moego.models.agreement.v1.AgreementModel);
  // add a agreement
  rpc AddAgreement(AddAgreementParams) returns (moego.models.agreement.v1.AgreementModel);
}
