// @since 2-23-11-14
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.agreement.v1;

import "moego/models/agreement/v1/agreement_enums.proto";
import "moego/models/agreement/v1/agreement_record_models.proto";
import "moego/utils/v1/status_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/agreement/v1;agreementapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.agreement.v1";

// get agreement record list for customer Params
message GetRecordListParams {
  // business id
  int64 business_id = 1;
  // customer id
  optional int64 customer_id = 2;
  // agreement id
  optional int64 agreement_id = 3;
  // target id by service type
  optional int64 target_id = 4;
  // associated service type: see definition in ServiceType
  optional int32 service_types = 5 [(validate.rules).int32 = {gt: 0}];
  // status: normal, deleted
  optional moego.utils.v1.Status status = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // signed status
  optional moego.models.agreement.v1.SignedStatus signed_status = 7 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // signed type
  optional moego.models.agreement.v1.SignedType signed_type = 8 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // source type
  optional moego.models.agreement.v1.SourceType source_type = 9 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // the page info
  moego.utils.v2.PaginationRequest pagination = 10;
}

// query record simple view Result
message GetRecordListResult {
  // record list
  repeated moego.models.agreement.v1.AgreementRecordSimpleView agreement_record_simple_view = 1;
  // page info
  moego.utils.v2.PaginationResponse pagination = 2;
  // signed status map
  map<int32, string> signed_status_map = 3;
  // service type map
  map<int32, string> service_type_map = 4;
  // agreement map, key is agreement id, value: agreement title
  map<int64, string> agreement_map = 5;
  // status type map
  map<int32, string> signed_type_map = 6;
  // status type map
  map<int32, string> source_type_map = 7;
}

// get record input
message GetRecordParams {
  // business id
  optional int64 business_id = 1;
  // agreement record id
  optional int64 id = 2;
  // agreement record uuid
  optional string uuid = 3 [(validate.rules).string.pattern = "^[0-9a-f]{32}$"];
}

// GetRecentSignedAgreementListParams
message GetRecentSignedAgreementListParams {
  // business id
  int64 business_id = 1;
  // customer id
  int64 customer_id = 2;
  // sign type
  optional moego.models.agreement.v1.SignedType signed_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // associated service type: see definition in ServiceType
  optional int32 service_types = 4 [(validate.rules).int32 = {gt: 0}];
}

// GetRecentSignedAgreementListResult
message GetRecentSignedAgreementListResult {
  // agreement with recent signed record list
  repeated moego.models.agreement.v1.AgreementWithRecentRecordsView agreement_recent_view = 1;
}

// AgreementRecordService
service AgreementRecordService {
  // get agreement with recent signed record list
  rpc GetRecentSignedAgreementList(GetRecentSignedAgreementListParams) returns (GetRecentSignedAgreementListResult);

  // query agreement record list
  rpc GetRecordList(GetRecordListParams) returns (GetRecordListResult);

  // get an agreement record detail by id
  rpc GetRecord(GetRecordParams) returns (moego.models.agreement.v1.AgreementRecordModel);

  // get an agreement record by id
  rpc GetRecordSimpleView(GetRecordParams) returns (moego.models.agreement.v1.AgreementRecordSimpleView);
}
