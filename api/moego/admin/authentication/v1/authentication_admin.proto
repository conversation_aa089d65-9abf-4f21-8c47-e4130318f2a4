syntax = "proto3";

package moego.admin.authentication.v1;

import "moego/models/account/v1/account_models.proto";
import "moego/models/admin_permission/v1/role_models.proto";
import "moego/models/admin_permission/v1/role_permission_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/authentication/v1;authenticationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.authentication.v1";

// GetAuthenticationAccountInfoParams
message GetAccountInfoParams {}

// GetAuthenticationAccountInfoResult
message GetAccountInfoResult {
  // account
  optional moego.models.account.v1.AccountModel account = 1;
  // roles
  repeated moego.models.admin_permission.v1.RoleModel roles = 2;
  // role permissions
  repeated moego.models.admin_permission.v1.RolePermissionModel role_permissions = 3;
}

// GetLoginUrlParams
message GetLoginUrlParams {
  // the login return url
  string redirect_url = 1 [(validate.rules).string = {
    uri: true
    max_len: 255
  }];
}

// GetLoginUrlResult
message GetLoginUrlResult {
  // login url
  string login_url = 1;
}

// google login params
message GoogleLoginParams {
  // the credential
  string credential = 1;
}

// keycloak login params
message KeycloakLoginParams {
  // token issued by keycloak
  string code = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 1000
  }];
  // the state for csrf validate
  string state = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // the login return url
  string redirect_url = 3 [(validate.rules).string = {
    uri: true
    max_len: 255
  }];
}

// LoginParams
message LoginParams {
  // login source
  oneof source {
    option (validate.required) = true;
    // google login
    GoogleLoginParams google = 1;
    // keycloak login
    KeycloakLoginParams keycloak = 2;
  }
}

// LoginResult
message LoginResult {}

// authentication admin service
// Deprecated: should be AuthorizationService
service AuthenticationService {
  option deprecated = true;
  // GetAuthenticationAccountInfo
  rpc GetAccountInfo(GetAccountInfoParams) returns (GetAccountInfoResult);
  // GetLoginUrl
  rpc GetLoginUrl(GetLoginUrlParams) returns (GetLoginUrlResult);
  // Login
  rpc Login(LoginParams) returns (LoginResult);
}
