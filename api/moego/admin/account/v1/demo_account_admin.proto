// @since 2023-06-24 15:07:57
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.account.v1;

import "google/protobuf/struct.proto";
import "moego/models/account/v1/account_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/account/v1;accountapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.account.v1";

// CreateDemoAccountParams is the request params of CreateDemoAccount
message CreateDemoAccountParams {
  // email is the email of the account
  string email = 1 [(validate.rules).string = {
    email: true
    max_len: 50
    min_len: 1
  }];
  // first_name is the first name of the account
  string first_name = 2 [(validate.rules).string = {
    max_len: 50
    min_len: 1
  }];
  // last_name is the last name of the account
  string last_name = 3 [(validate.rules).string = {
    max_len: 50
    min_len: 1
  }];
  // business_name is the business name of the account
  string business_name = 4 [(validate.rules).string = {
    max_len: 50
    min_len: 1
  }];
  // address1 is the address1 of the account
  string address1 = 5 [(validate.rules).string = {max_len: 255}];
  // address2 is the address2 of the account
  string address_city = 6 [(validate.rules).string = {max_len: 50}];
  // state
  string address_state = 7 [(validate.rules).string = {max_len: 50}];
  // country
  string address_country = 8 [(validate.rules).string = {max_len: 50}];
  // zipcode
  string address_zipcode = 9 [(validate.rules).string = {max_len: 50}];
  // business_type
  int32 app_type = 10 [(validate.rules).int32 = {
    in: [
      0,
      1,
      2
    ]
  }];
  // lat
  double address_lat = 11 [(validate.rules).double = {
    gte: -90
    lte: 90
  }];
  // lng
  double address_lng = 12 [(validate.rules).double = {
    gte: -180
    lte: 180
  }];
  // source
  int32 source = 13;
  // phone_number
  string phone_number = 14 [(validate.rules).string = {max_len: 50}];
  // website
  string website = 15 [(validate.rules).string = {
    max_len: 255
    uri: true
    ignore_empty: true
  }];
  // know_about_us
  string know_about_us = 16 [(validate.rules).string = {max_len: 50}];
  // country
  string country = 17 [(validate.rules).string = {max_len: 50}];
  // country_alpha2_code
  string country_alpha2_code = 18 [(validate.rules).string = {
    len: 2
    ignore_empty: true
  }];
  // how_many_locations
  int32 how_many_locations = 19;
  // how_many_staff
  int32 how_many_staff = 20;
  // timezone_name
  string timezone_name = 21 [(validate.rules).string = {max_len: 50}];
  // timezone_seconds
  int32 timezone_seconds = 22;
  // currency_symbol
  string currency_symbol = 23 [(validate.rules).string = {max_len: 50}];
  // currency_code
  string currency_code = 24 [(validate.rules).string = {max_len: 50}];
  // date_format_type
  int32 date_format_type = 25;
  // source_from
  int32 source_from = 26;
  // appt_per_week
  int32 appt_per_week = 27;
  // business_years
  int32 business_years = 28;
  // move_from
  int32 move_from = 29;
  // retail_enable
  int32 retail_enable = 30;
  // password
  string password = 31 [(validate.rules).string = {
    min_len: 12
    max_len: 50
    ignore_empty: true
  }];

  // van num
  optional int32 vans_num = 32;
  // location num
  optional int32 location_num = 33;
  // enable square
  bool square_enabled = 34;

  // enable boarding daycare
  bool enable_boarding_daycare = 35;
}

// CreateDemoAccountResult is the response result of CreateDemoAccount
message CreateDemoAccountResult {
  // account
  moego.models.account.v1.AccountModel account = 1;
  // business
  google.protobuf.Struct deprecated_business = 2;
  // staff
  google.protobuf.Struct deprecated_staff = 3;
  // subscription
  google.protobuf.Struct deprecated_subscription_state = 4;
  // company
  google.protobuf.Struct deprecated_company = 5;
}

// UpdateDemoAccountParams
message UpdateDemoAccountParams {
  // account id
  int64 account_id = 1;
  // company id
  int64 company_id = 2;

  // van num
  optional int32 vans_num = 32;
  // location num
  optional int32 location_num = 33;
  // retail_enable
  optional bool retail_enabled = 30;
  // enable square
  optional bool square_enabled = 34;
}

// UpdateDemoAccountResult
message UpdateDemoAccountResult {}

// DescribeDemoAccountsParams is the request params of DescribeDemoAccounts
message DescribeDemoAccountsParams {
  // email
  optional string email_like = 1 [(validate.rules).string = {max_len: 50}];

  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// DescribeDemoAccountsResult is the response result of DescribeDemoAccounts
message DescribeDemoAccountsResult {
  // accounts
  repeated moego.models.account.v1.AccountModel accounts = 1;
  // deprecated business map, key is account id
  map<int64, google.protobuf.Struct> deprecated_business_map = 2;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// the demo_account service
service DemoAccountService {
  // CreateDemoAccount create a demo account
  rpc CreateDemoAccount(CreateDemoAccountParams) returns (CreateDemoAccountResult);
  // DescribeDemoAccounts describe demo accounts
  rpc DescribeDemoAccounts(DescribeDemoAccountsParams) returns (DescribeDemoAccountsResult);
  // UpdateDemoAccount
  rpc UpdateDemoAccount(UpdateDemoAccountParams) returns (UpdateDemoAccountResult);
}
