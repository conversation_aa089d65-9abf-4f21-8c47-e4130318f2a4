// @since 2023-07-04 13:37:28
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.price_checker.v1;

import "google/protobuf/struct.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/price_checker/v1;pricecheckerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.price_checker.v1";

// get data_query request
message GetDataQueryRequest {
  // sql
  string sql = 1 [(validate.rules).string = {max_len: 1000}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2;
}

// get data_query response
message GetDataQueryResponse {
  // result
  repeated google.protobuf.Struct result = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// the data_query service
service DataQueryService {
  // get data_query
  rpc GetDataQuery(GetDataQueryRequest) returns (GetDataQueryResponse);
}
