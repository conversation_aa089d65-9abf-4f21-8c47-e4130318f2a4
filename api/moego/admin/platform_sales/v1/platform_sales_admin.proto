// @since 2-24-04-10

syntax = "proto3";

package moego.admin.platform_sales.v1;

import "google/protobuf/timestamp.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/platform_sales/v1;platformsalesapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.platform_sales.v1";

// Subscription plan
enum SubscriptionPlan {
  // Unspecified
  SUBSCRIPTION_PLAN_UNSPECIFIED = 0;
  // Growth - Grooming
  GROWTH_GROOMING = 1;
  // Ultimate - Grooming
  ULTIMATE_GROOMING = 2;
  // Growth - Boarding & Daycare
  GROWTH_BOARDING_DAYCARE = 3;
  // Ultimate - Boarding & Daycare
  ULTIMATE_BOARDING_DAYCARE = 4;
  // Enterprise - Boarding & Daycare
  ENTERPRISE_BOARDING_DAYCARE = 5;
}

// Custom rate approval status
enum CustomRateApprovalStatus {
  // unspecified
  CUSTOM_RATE_APPROVAL_STATUS_UNSPECIFIED = 0;
  // ignored
  CUSTOM_RATE_APPROVAL_STATUS_IGNORED = 1;
  // pending
  CUSTOM_RATE_APPROVAL_STATUS_PENDING = 2;
  // approved
  CUSTOM_RATE_APPROVAL_STATUS_APPROVED = 3;
  // rejected
  CUSTOM_RATE_APPROVAL_STATUS_REJECTED = 4;
}

// platform sales record
message PlatformSalesRecord {
  // id
  int64 id = 1;
  // code
  string code = 2;
  // accountId
  int64 account_id = 3;
  // agreementId
  int64 agreement_id = 4;
  // companyId
  int64 company_id = 5;
  // email
  string email = 6;
  // companyType
  int32 company_type = 7;
  // subDiscount
  int32 sub_discount = 8;
  // subCouponValidMonth
  int32 sub_coupon_valid_month = 9;
  // subCouponCode
  string sub_coupon_code = 10;
  // needHardware
  int32 need_hardware = 11;
  // hardwareDiscount
  int32 hardware_discount = 12;
  // hardwareCode
  string hardware_code = 13;
  // vansNum
  int32 vans_num = 14;
  // locationNum
  int32 location_num = 15;
  // agreementRecordUuid
  string agreement_record_uuid = 16;
  // signedTime
  google.protobuf.Timestamp signed_time = 17;
  // orderShippingStatus
  string order_shipping_status = 18;
  // premiumType
  int32 premium_type = 19;
  // status
  int32 status = 20;
  //sales status
  int32 sales_status = 21;
  // createdAt
  google.protobuf.Timestamp created_at = 22;
  // updatedAt
  google.protobuf.Timestamp updated_at = 23;
  // link
  optional string link = 24;
  // show monthly term
  int32 show_monthly_term = 25;
  //show annually term
  int32 show_annually_term = 26;
  //creator
  string creator = 27;
  //show hardware
  int32 show_hardware = 28;
  //is bd plan
  int32 is_bd_plan = 29;
  // terminal card processing rate
  string terminal_card_rate = 30;
  // non - terminal card processing rate
  string non_terminal_card_rate = 31;
  // minimum monthly transaction volume
  string min_monthly_transaction = 32;
  //is customer rate
  int32 is_custom_rate = 33;
  // show accounting
  int32 show_accounting = 34;
  // tier
  string tier = 35;
  // subscription plan
  SubscriptionPlan subscription_plan = 36;
  // custom rate approval status
  CustomRateApprovalStatus custom_rate_approval_status = 37;
  // opportunity id
  string opportunity_id = 38;
}

// create platform sales link params
message CreatePlatformSalesLinkParams {
  // email
  string email = 1 [(validate.rules).string = {
    email: true
    max_len: 64
  }];
  // subscription term
  optional int32 sub_term = 2 [(validate.rules).int32 = {gte: 0}];
  // 5=5% 10=10% number=number%
  optional int32 sub_price_discount = 3;
  // need hardware
  bool need_hardware = 4;
  // hardware discount
  optional int32 hardware_discount = 5;
  // van number
  optional int32 vans_num = 6 [(validate.rules).int32 = {gte: 0}];
  // location number
  optional int32 location_num = 7 [(validate.rules).int32 = {gte: 0}];
  //0 mobile 1salon  3 mobile and salon
  optional int32 company_type = 8 [(validate.rules).int32 = {gte: 0}];
  //2 or 3, use subscription_plan instead
  int32 premium_type = 9 [
    deprecated = true,
    (validate.rules).int32 = {gte: 0}
  ];
  // show monthly term
  bool show_monthly_term = 10;
  //show annually term
  bool show_annually_term = 11;
  //creator
  string creator = 12;
  //show hardware
  bool show_hardware = 13;
  //is bd plan, use subscription_plan instead
  bool is_bd_plan = 14 [deprecated = true];
  // terminal card processing rate
  optional string terminal_card_rate = 15 [(validate.rules).string = {max_len: 255}];
  // non - terminal card processing rate
  optional string non_terminal_card_rate = 16 [(validate.rules).string = {max_len: 255}];
  // minimum monthly transaction volume
  optional string min_monthly_transaction = 17 [(validate.rules).string = {max_len: 255}];
  //is customer rate
  bool is_custom_rate = 18;
  // show accounting
  bool show_accounting = 19;
  // subscription plan
  SubscriptionPlan subscription_plan = 20;
  // tier
  string tier = 21;
  // preset custom rate
  optional PresetCustomRate preset_custom_rate = 22;
  // preset minimum monthly transaction volume, different from min_monthly_transaction
  optional string preset_min_monthly_transaction = 23 [(validate.rules).string = {max_len: 255}];

  // opportunity id
  string opportunity_id = 24;

  // preset custom rate
  message PresetCustomRate {
    // option
    string option = 1;
    // terminal card rate
    string terminal_card_rate = 2 [(validate.rules).string = {max_len: 255}];
    // non-terminal card rate
    string non_terminal_card_rate = 3 [(validate.rules).string = {max_len: 255}];
    // spif
    string spif = 4 [(validate.rules).string = {max_len: 32}];
  }
}

// create platform sales link result
message CreatePlatformSalesLinkResult {
  // link
  string link = 1;
}

// get platform sales record list params
message GetPlatformSalesRecordListParams {
  // code
  optional string code = 1 [(validate.rules).string = {max_len: 64}];
  // email
  optional string email = 2 [(validate.rules).string = {max_len: 64}];
  // agreement_uuid
  optional string agreement_record_uuid = 3 [(validate.rules).string = {max_len: 32}];
  // account
  optional int64 account_id = 4 [(validate.rules).int64 = {gte: 0}];
  // status
  optional int32 status = 5 [(validate.rules).int32 = {gte: 0}];
  // status
  optional string creator = 6 [(validate.rules).string = {max_len: 50}];
  // custom rate approval status
  repeated CustomRateApprovalStatus custom_rate_approval_statuses = 7;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// get platform sales record list result
message GetPlatformSalesRecordListResult {
  // platform sales record list
  repeated PlatformSalesRecord platform_sales_records = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// delete platform sales record params
message DeletePlatformSalesRecordParams {
  // id
  int64 id = 1;
}

// delete platform sales record result
message DeletePlatformSalesRecordResult {}

// update platform sales record params
message UpdatePlatformSalesRecordParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // custom rate approval status
  optional CustomRateApprovalStatus custom_rate_approval_status = 2 [(validate.rules).enum = {
    not_in: [0]
  }];
}

// update platform sales record result
message UpdatePlatformSalesRecordResult {}

// platform sales service
service PlatformSalesService {
  // create platform sales link
  rpc CreatePlatformSalesLink(CreatePlatformSalesLinkParams) returns (CreatePlatformSalesLinkResult);
  // get platform sales record list
  rpc GetPlatformSalesRecordList(GetPlatformSalesRecordListParams) returns (GetPlatformSalesRecordListResult);
  // delete platform sales record
  rpc DeletePlatformSalesRecord(DeletePlatformSalesRecordParams) returns (DeletePlatformSalesRecordResult);
  // update platform sales record
  rpc UpdatePlatformSalesRecord(UpdatePlatformSalesRecordParams) returns (UpdatePlatformSalesRecordResult);
}
