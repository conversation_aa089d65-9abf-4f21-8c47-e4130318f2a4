syntax = "proto3";

package moego.admin.review_booster.v1;

import "google/protobuf/struct.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/review_booster/v1;reviewboosterapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.review_booster.v1";

// query appointments params
message ListReviewBoosterRecordParams {
  //the business id
  int64 business_id = 1;
  //the customer id
  optional int64 customer_id = 2 [(validate.rules).int64.gt = 0];

  // the pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// query appointments result
message ListReviewBoosterRecordResult {
  // the review booster record
  repeated google.protobuf.Struct review_booster_records = 1;
  // the customer name info
  map<int64, moego.models.business_customer.v1.BusinessCustomerNameStatusView> customers = 2;
  // the pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// delete appointments params
message UpdateReviewBoosterRecordParams {
  // the review booster record id
  int64 record_id = 1 [(validate.rules).int64.gt = 0];
  // score
  optional int64 score = 2 [
    (validate.rules).int64.gte = 0,
    (validate.rules).int64.lte = 5
  ];
}

// delete appointments result
message UpdateReviewBoosterRecordResult {}

// the appointment service
service ReviewBoosterAdminService {
  // query review booster record list
  rpc ListReviewBoosterRecord(ListReviewBoosterRecordParams) returns (ListReviewBoosterRecordResult);
  // update review booster record
  rpc UpdateReviewBoosterRecord(UpdateReviewBoosterRecordParams) returns (UpdateReviewBoosterRecordParams);
}
