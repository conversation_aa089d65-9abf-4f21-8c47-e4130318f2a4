// @since 2023-04-07 09:48:36
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.metadata.v1;

import "google/protobuf/empty.proto";
import "moego/models/metadata/v1/metadata_defs.proto";
import "moego/models/metadata/v1/metadata_enums.proto";
import "moego/models/metadata/v1/metadata_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/metadata/v1;metadataapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.metadata.v1";

// describe groups response
message DescribeGroupsResult {
  // the group list
  repeated string groups = 1;
}

// describe keys request
message DescribeKeysParams {
  // filter by group, empty will not filter
  optional string group = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // filter by owner type, 0 will not filter
  optional moego.models.metadata.v1.OwnerType owner_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // filter by name like
  optional string name_like = 3 [(validate.rules).string = {max_len: 50}];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 4;
}

// describe keys response
message DescribeKeysResult {
  // the keys
  repeated moego.models.metadata.v1.KeyModel keys = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// get key request
message GetKeyParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get key response
message GetKeyResult {
  // the key
  moego.models.metadata.v1.KeyModel key = 1;
}

// create key request
message CreateKeyParams {
  // the key def
  moego.models.metadata.v1.KeyFullDef key_def = 1 [(validate.rules).message = {required: true}];
}

// create key response
message CreateKeyResult {
  // the key
  moego.models.metadata.v1.KeyModel key = 1;
}

// update key request
message UpdateKeyParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // the key def
  moego.models.metadata.v1.KeyPartialDef key_def = 2 [(validate.rules).message = {required: true}];
}

// delete key params
message DeleteKeyParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// describe values request
message DescribeValuesParams {
  // key id
  int64 key_id = 1 [(validate.rules).int64 = {gt: 0}];
  // owner id
  repeated int64 owner_ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    max_items: 500
    ignore_empty: true
  }];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// describe values responses
message DescribeValuesResult {
  // owners
  map<int64, Owner> owners = 1;
  // values
  repeated moego.models.metadata.v1.ValueModel values = 2;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;

  // the owner
  message Owner {
    // name
    string name = 1;
    // email
    string email = 2;
  }
}

// get value request
message GetValueParams {
  // key
  int64 key_id = 1 [(validate.rules).int64 = {gt: 0}];
  // value
  int64 owner_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get value response
message GetValueResult {
  // the key
  moego.models.metadata.v1.KeyModel key = 1;
  // the value
  moego.models.metadata.v1.ValueModel value = 2;
}

// update value request
message BatchUpdateValueParams {
  // key
  int64 key_id = 1 [(validate.rules).int64 = {gt: 0}];
  // owner
  repeated int64 owner_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 500
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // value
  optional string value = 3 [(validate.rules).string = {max_len: 1024}];
}

// the metadata service
// Deprecated: should be MetadataService
service MetadataAdminService {
  option deprecated = true;
  // describe groups
  rpc DescribeGroups(google.protobuf.Empty) returns (DescribeGroupsResult);

  // describe keys
  rpc DescribeKeys(DescribeKeysParams) returns (DescribeKeysResult);
  // get key
  rpc GetKey(GetKeyParams) returns (GetKeyResult);
  // create key
  rpc CreateKey(CreateKeyParams) returns (CreateKeyResult);
  // update key
  rpc UpdateKey(UpdateKeyParams) returns (google.protobuf.Empty);
  // delete key
  rpc DeleteKey(DeleteKeyParams) returns (google.protobuf.Empty);

  // describe values
  rpc DescribeValues(DescribeValuesParams) returns (DescribeValuesResult);
  // get value
  rpc GetValue(GetValueParams) returns (GetValueResult);
  // batch update value
  rpc BatchUpdateValue(BatchUpdateValueParams) returns (google.protobuf.Empty);
}
