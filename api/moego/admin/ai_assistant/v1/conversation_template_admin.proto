// @since 2023-07-03 12:43:02
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.ai_assistant.v1;

import "google/protobuf/empty.proto";
import "moego/models/ai_assistant/v1/conversation_template_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/ai_assistant/v1;aiassistantapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.ai_assistant.v1";

// UpsertConversationTemplateParams is the request message for UpsertConversationTemplate
message UpsertConversationTemplateParams {
  // the id, set means update
  optional int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // the scenario
  string scenario = 2 [(validate.rules).string = {
    min_len: 2
    max_len: 64
  }];
  // the template
  string template = 3 [(validate.rules).string = {max_len: 8192}];
  // the trailer_template
  string trailer_template = 4 [(validate.rules).string = {max_len: 8192}];
  // the temperature, default is 1
  double temperature = 5 [(validate.rules).double = {
    gte: 0
    lte: 2
  }];
}

// DescribeConversationTemplatesParams is the request message for DescribeConversationTemplates
message DescribeConversationTemplatesParams {
  // the scenario
  optional string scenario = 1 [(validate.rules).string = {max_len: 64}];
  // include deleted
  bool include_deleted = 2;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3;
}

// DescribeConversationTemplatesResult is the response message for DescribeConversationTemplates
message DescribeConversationTemplatesResult {
  // conversation templates
  repeated moego.models.ai_assistant.v1.ConversationTemplateModel conversation_templates = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// DeleteConversationTemplateRequest is the request message for DeleteConversationTemplate
message DeleteConversationTemplateParams {
  // the id
  string scenario = 1 [(validate.rules).string = {max_len: 64}];
}

// the conversation_template service
service ConversationTemplateService {
  // insert or update conversation template
  rpc UpsertConversationTemplate(UpsertConversationTemplateParams) returns (moego.models.ai_assistant.v1.ConversationTemplateModel) {}
  // describe conversation templates
  rpc DescribeConversationTemplates(DescribeConversationTemplatesParams) returns (DescribeConversationTemplatesResult) {}
  // delete conversation template
  rpc DeleteConversationTemplate(DeleteConversationTemplateParams) returns (google.protobuf.Empty) {}
}
