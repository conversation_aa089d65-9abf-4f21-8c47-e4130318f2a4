syntax = "proto3";

package moego.admin.platform_care.v1;

import "moego/models/payment/v1/price_plan_conf_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/platform_care/v1;platformcareapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.platform_care.v1";

// platform care service
service ChangePlanService {
  // change plan
  rpc ChangePlan(ChangePlanParams) returns (ChangePlanResult);
  // list plans
  rpc ListPlans(ListPlansParams) returns (ListPlansResult);
  // downgrade plan immediately
  rpc DowngradePlanImmediately(DowngradePlanParams) returns (DowngradePlanResult);
}

// downgrade plan params
message DowngradePlanParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
}

// downgrade plan result
message DowngradePlanResult {
  // success
  bool success = 1;
}

// change plan params
message ChangePlanParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // location num
  int64 location_num = 2 [(validate.rules).int64.gte = 0];
  // van num
  int64 van_num = 3 [(validate.rules).int64.gte = 0];
  // stripe plan id
  string stripe_plan_id = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 150
  }];
}

// change plan result
message ChangePlanResult {}

// list plans params
message ListPlansParams {}

// ListPlansResult
message ListPlansResult {
  // plans
  repeated moego.models.payment.v1.PricePlanConf plan_confs = 1;
}
