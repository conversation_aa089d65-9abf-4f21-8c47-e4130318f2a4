syntax = "proto3";

package moego.admin.online_booking.v1;

import "moego/models/account/v1/session_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.online_booking.v1";

// check ob session params
message CheckSessionParams {}

// check ob session result
message CheckSessionResult {
  // ob main session
  moego.models.account.v1.SessionModel main_session = 1;
  // ob sub sessions, key is ob name, value is ob sub session
  map<string, moego.models.account.v1.SessionModel> sub_sessions = 2;
}

// impersonate params
message ImpersonateParams {}

// impersonate result
message ImpersonateResult {
  // ob main session
  moego.models.account.v1.SessionModel main_session = 1;
  // ob sub sessions, key is ob name, value is ob sub session
  map<string, moego.models.account.v1.SessionModel> sub_sessions = 2;
}

// remove impersonate params
message RemoveImpersonateParams {}

// remove impersonate result
message RemoveImpersonateResult {
  // ob main session
  moego.models.account.v1.SessionModel main_session = 1;
  // ob sub sessions, key is ob name, value is ob sub session
  map<string, moego.models.account.v1.SessionModel> sub_sessions = 2;
}

// online booking service
service OnlineBookingService {
  // check ob session
  rpc CheckSession(CheckSessionParams) returns (CheckSessionResult);

  // create ob impersonate session
  rpc Impersonate(ImpersonateParams) returns (ImpersonateResult);

  // remove ob impersonate session
  rpc RemoveImpersonate(RemoveImpersonateParams) returns (RemoveImpersonateResult);
}
