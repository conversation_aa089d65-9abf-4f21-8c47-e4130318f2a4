syntax = "proto3";

package moego.admin.pay_ops.v1;

import "google/protobuf/timestamp.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/pay_ops/v1;payopsapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.pay_ops.v1";

// enterprise custom fee
message EnterpriseCustomFee {
  // ID
  int64 id = 1;
  // account id
  int64 account_id = 2;
  // email
  string email = 3;
  // enterprise id
  int64 enterprise_id = 4;
  // enterprise name
  string enterprise_name = 5;
  // customized min vol
  int64 customized_min_vol = 6;
  // online fee rate
  double online_fee_rate = 7;
  // online fee cents
  uint32 online_fee_cents = 8;
  // reader fee rate
  double reader_fee_rate = 9;
  // reader fee cents
  uint32 reader_fee_cents = 10;
  // create time
  google.protobuf.Timestamp create_time = 11;
  // update time
  google.protobuf.Timestamp update_time = 12;
}

// list company custom fee params
message ListEnterpriseCustomFeesParams {
  // enterprise ids
  optional int64 enterprise_id = 1;

  // pagination
  moego.utils.v2.PaginationRequest pagination = 2;
}

// list enterprise custom fee params
message ListEnterpriseCustomFeesResult {
  // enterprise custom fees
  repeated EnterpriseCustomFee enterprise_custom_fees = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// add enterprise custom fee params
message CreateEnterpriseCustomFeeParams {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // online fee rate
  double online_fee_rate = 3 [(validate.rules).double = {
    gt: 0
    lt: 4
  }];
  // online fee cents
  uint32 online_fee_cents = 4 [(validate.rules).uint32 = {
    gte: 0
    lte: 100
  }];
  // reader fee rate
  double reader_fee_rate = 5 [(validate.rules).double = {
    gt: 0
    lt: 4
  }];
  // reader fee cents
  uint32 reader_fee_cents = 6 [(validate.rules).uint32 = {
    gte: 0
    lte: 100
  }];
  // customized min vol
  optional int64 customized_min_vol = 7 [(validate.rules).int64 = {gt: 1000}];
}

// update enterprise custom fee params
message UpdateEnterpriseCustomFeeParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // enterprise id
  int64 enterprise_id = 2 [(validate.rules).int64 = {gt: 0}];
  // customized min vol
  optional int64 customized_min_vol = 3 [(validate.rules).int64 = {gt: 1000}];
  // online fee rate
  double online_fee_rate = 4 [(validate.rules).double = {
    gt: 0
    lt: 4
  }];
  // online fee cents
  uint32 online_fee_cents = 5 [(validate.rules).uint32 = {
    gte: 0
    lte: 100
  }];
  // reader fee rate
  double reader_fee_rate = 6 [(validate.rules).double = {
    gt: 0
    lt: 4
  }];
  // reader fee cents
  uint32 reader_fee_cents = 7 [(validate.rules).uint32 = {
    gte: 0
    lte: 100
  }];
}

// delete enterprise custom fee params
message DeleteEnterpriseCustomFeeParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete enterprise customFee result
message DeleteEnterpriseCustomFeeResult {
  // success
  bool success = 1;
}

// custom fee service
service EnterpriseCustomFeeService {
  // get company custom fee list
  rpc ListEnterpriseCustomFees(ListEnterpriseCustomFeesParams) returns (ListEnterpriseCustomFeesResult);
  // create company custom fee
  rpc CreateEnterpriseCustomFee(CreateEnterpriseCustomFeeParams) returns (EnterpriseCustomFee);
  // update company custom fee
  rpc UpdateEnterpriseCustomFee(UpdateEnterpriseCustomFeeParams) returns (EnterpriseCustomFee);
  // delete company custom fee
  rpc DeleteEnterpriseCustomFee(DeleteEnterpriseCustomFeeParams) returns (DeleteEnterpriseCustomFeeResult);
}
