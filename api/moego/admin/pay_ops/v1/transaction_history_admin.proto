// @since 2-24-1-02
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.pay_ops.v1;

import "google/protobuf/empty.proto";
import "moego/models/pay_ops/v1/transaction_history_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/pay_ops/v1;payopsapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.pay_ops.v1";

// select transaction history list params
message GetTransactionHistoryListParams {
  // payment intent id
  optional string payment_intent_id = 1;
  // invoice id
  optional string invoice_id = 2;
  // business id
  optional int64 business_id = 3;
  // client name or customer id
  // must be used in conjunction with text type
  // if text type is 1, client text is customer id, otherwise client text is client name
  // if text is client, the api will retrieve the customer id based on the client name
  optional string client_text = 4;
  // customer id
  optional int32 text_type = 5;
  // amount range min
  optional double amount_min = 6 [(validate.rules).double = {gte: 0.0}];
  // amount range max
  optional double amount_max = 7 [(validate.rules).double = {gte: 0.0}];
  // square payment method
  optional string square_payment_method = 8;
  // stripe payment method
  optional string stripe_payment_method = 9;
  // payment create time range
  optional string payment_create_time = 10;
  // vendor
  optional string vendor = 11;
  // status
  optional string status = 12;

  // pagination
  moego.utils.v2.PaginationRequest pagination = 100;
}

// transaction history list result
message GetTransactionHistoryListResult {
  // simple view
  repeated moego.models.pay_ops.v1.TransactionHistoryModel transaction_list = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// static value result
message GetStaticValueModel {
  // dispute reason type
  map<int32, string> stripe_payment_method_map = 1;
  // dispute status type
  map<int32, string> square_payment_method_map = 2;
  // status type
  map<int32, string> payment_status_map = 3;
}

// get transaction history view params
message GetTransactionHistoryViewParams {
  // transaction history id
  string payment_intent_id = 1;
}

// refund Transaction History Params
message RefundTransactionHistoryParams {
  // payment intent id
  optional string payment_intent_id = 1;
  // refund amount
  double refund_amount = 2 [(validate.rules).double = {gte: 0.0}];
  // refund reason
  optional string refund_reason = 3;
  // charge id
  optional string charge_id = 4;
}

// refund Transaction History result
message RefundTransactionHistoryResult {
  // refund  id
  string refund_id = 1;
}

// business transaction history list params
message BusinessTransactionHistoryListParams {
  // company id
  int64 company_id = 1;
}

// business transaction history list result
message BusinessTransactionHistoryListResult {
  // simple view
  repeated moego.models.pay_ops.v1.StripeChargeModel transaction_list = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// get stripe payment intent params
message GetPaymentIntentParams {
  // intent id
  string payment_intent = 1;
}

// get payment intent result
message GetPaymentIntentResult {
  // json value
  string payment_intent_result = 1;
}

// transaction history service
service TransactionHistoryService {
  // get transaction history list
  rpc GetTransactionHistoryList(GetTransactionHistoryListParams) returns (GetTransactionHistoryListResult);

  // get transaction history view
  rpc GetTransactionHistoryView(GetTransactionHistoryViewParams) returns (moego.models.pay_ops.v1.TransactionHistoryModel);

  // get static value
  rpc GetStaticValue(google.protobuf.Empty) returns (GetStaticValueModel);

  // transaction history refund
  rpc RefundTransactionHistory(RefundTransactionHistoryParams) returns (RefundTransactionHistoryResult);

  // get business transaction history list
  rpc GetBusinessTransactionHistoryList(BusinessTransactionHistoryListParams) returns (BusinessTransactionHistoryListResult);
  //fetch stripe payment intent
  rpc GetStripePaymentIntentView(GetPaymentIntentParams) returns (GetPaymentIntentResult);
}
