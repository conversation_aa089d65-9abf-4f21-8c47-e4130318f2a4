// @since 2-24-1-12
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.pay_ops.v1;

import "google/protobuf/empty.proto";
import "moego/models/pay_ops/v1/verification_enums.proto";
import "moego/models/pay_ops/v1/verification_model.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/pay_ops/v1;payopsapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.pay_ops.v1";

// SearchAccountParams is the params for search account
message SearchAccountParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
}

// update verification info params
message UpdateAccountInfoParams {
  // stripe id
  string id = 1;

  /**
   * Contact infomation details
   */
  // company name
  optional string company_name = 2 [(validate.rules).string.max_len = 255];
  // business profile name
  optional string business_profile_name = 3 [(validate.rules).string.max_len = 255];
  // business profile email
  optional string email = 4 [(validate.rules).string = {email: true}];
  // business profile url
  optional string business_profile_url = 5 [(validate.rules).string.max_len = 255];
  // business profile support url
  optional string business_profile_support_url = 6 [
    (validate.rules).string.min_len = 1,
    (validate.rules).string.max_len = 255
  ];
  // business profile address
  optional string business_profile_address = 7;
  // company address city
  optional string company_address_city = 8;
  // company address country
  optional string company_address_country = 9;
  // company address line1
  optional string company_address_line1 = 10;
  // company address line2
  optional string company_address_line2 = 11;
  // company address postal code
  optional string company_address_postal_code = 12;
  // company address state
  optional string company_address_state = 13;
  // company phone
  optional string company_phone = 14;
  // business profile number
  optional string business_profile_support_phone = 15;

  /**
   * business details
   */
  // employer identification number, tax id
  optional string tax_id = 16;
  // industry
  optional string industry = 17;
  // company verification document
  optional string company_verification_document = 18;
  // business registration file
  optional string settings_payment_statement_descriptor = 19;
  // statement descriptor prefix
  optional string settings_card_payments_statement_descriptor_prefix = 20;

  // business type
  optional moego.models.pay_ops.v1.StripeBusinessType business_type = 21;
  // company struct
  optional moego.models.pay_ops.v1.StripeCompanyStruct company_struct = 22;
}

// update account info result
message UpdateAccountInfoResult {
  // account id
  string id = 1;
}

// update account person
message UpdateAccountPersonParams {
  // first name
  optional string first_name = 1;
  // last name
  optional string last_name = 2;
  // email address
  optional string email_address = 3;
  // maiden name
  optional string maiden_name = 4;
  // job title
  optional string job_title = 5;
  // dob, date of birth
  int64 date_of_birth = 6;
  // phone number
  optional string phone = 7;
  // home address city
  optional string home_address_city = 8;
  // company address country
  optional string home_address_country = 9;
  // company address line1
  optional string home_address_line1 = 10;
  // company address line2
  optional string home_address_line2 = 11;
  // company address postal code
  optional string home_address_postal_code = 12;
  // company address state
  optional string home_address_state = 13;
  // ssn last 4
  optional string ssn_last4 = 14;
  // percent ownership of the business
  optional double percent_ownership = 15;
  // identity document
  optional string identity_document = 16;
  // account id
  string account_id = 17;
  // person id
  string person_id = 18;
}

// get stripe enums result
message GetStripeEnumResult {
  // stripe company struct list
  repeated string stripe_company_struct = 1;
  // stripe business type list
  repeated string stripe_business_type = 2;
}

// set default external account params
message SetDefaultExternalAccountParams {
  // stripe account id
  string stripe_account_id = 1;
  // stripe bank account id
  string stripe_bank_account_id = 2;
}

// set default external account result
message SetDefaultExternalAccountResult {
  // stripe bank account id
  string id = 1;
}

// delete external account params
message DeleteExternalAccountParams {
  // stripe account id
  string stripe_account_id = 1 [(validate.rules).string = {
    max_len: 50
    min_len: 1
  }];
  // stripe bank account id
  string stripe_bank_account_id = 2 [(validate.rules).string = {
    max_len: 50
    min_len: 1
  }];
}

// delete external account result
message DeleteExternalAccountResult {
  // stripe bank account id
  string id = 1;
}

// VerificationService is the service for verification
service VerificationService {
  // search account
  rpc SearchStripeAccount(SearchAccountParams) returns (moego.models.pay_ops.v1.StripeAccount);

  // update account info api
  rpc UpdateAccountInfo(UpdateAccountInfoParams) returns (UpdateAccountInfoResult);

  // update account person
  rpc UpdateAccountPerson(UpdateAccountPersonParams) returns (UpdateAccountInfoResult);

  // get stripe enum
  rpc GetStripeEnum(google.protobuf.Empty) returns (GetStripeEnumResult);

  // set default external account
  rpc SetDefaultExternalAccount(SetDefaultExternalAccountParams) returns (SetDefaultExternalAccountResult);

  // Delete external account
  rpc DeleteExternalAccount(DeleteExternalAccountParams) returns (DeleteExternalAccountResult);
}
