// @since 2024-08-12 17:31:10
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.pay_ops.v1;

import "google/protobuf/empty.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/pay_ops/v1;payopsapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.pay_ops.v1";

// traffic switch params, for split payment/invoice
message TrafficSwitchRequest {
  // business id
  optional int32 business_id = 1;
  // ratio
  optional int32 ratio = 2;
  // loan switch
  optional string loan_switch = 3;
}

// traffic switch response
message TrafficSwitchResponse {
  // traffic switch result
  string result = 1;
}

// all split traffic switch config
message GetAllSplitTrafficSwitchConfigResponse {
  // json value
  string traffic_switch_config = 1;
}

// all invoice reinvent traffic switch config
message GetAllInvoiceReinventTrafficSwitchConfigResponse {
  // json value
  string traffic_switch_config = 1;
}

// traffic switch control service
service TrafficSwitchService {
  // add to split payment whitelist
  rpc AddToSplitPaymentWhitelist(TrafficSwitchRequest) returns (TrafficSwitchResponse);
  // delete from split payment whitelist
  rpc DeleteFromSplitPaymentWhitelist(TrafficSwitchRequest) returns (TrafficSwitchResponse);
  // add to split payment blacklist
  rpc AddToSplitPaymentBlacklist(TrafficSwitchRequest) returns (TrafficSwitchResponse);
  // delete from split payment blacklist
  rpc DeleteFromSplitPaymentBlacklist(TrafficSwitchRequest) returns (TrafficSwitchResponse);
  // set split payment traffic ratio
  rpc SetSplitPaymentTrafficRatio(TrafficSwitchRequest) returns (TrafficSwitchResponse);
  // get all split traffic switch config
  rpc GetAllSplitTrafficSwitchConfig(google.protobuf.Empty) returns (GetAllSplitTrafficSwitchConfigResponse);
  // set split payment loan switch
  rpc SetSplitPaymentLoanSwitch(TrafficSwitchRequest) returns (TrafficSwitchResponse);
  // add to invoice reinvent whitelist
  rpc AddToInvoiceReinventWhitelist(TrafficSwitchRequest) returns (TrafficSwitchResponse);
  // get all invoice reinvent config
  rpc GetAllInvoiceReinventTrafficSwitchConfig(google.protobuf.Empty) returns (GetAllInvoiceReinventTrafficSwitchConfigResponse);
}
