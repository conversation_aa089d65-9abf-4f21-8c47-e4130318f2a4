// @since 2024-08-14 10:50:32
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.pay_ops.v1;

import "google/protobuf/empty.proto";
import "google/type/interval.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/pay_ops/v1;payopsapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.pay_ops.v1";

// list quick book setting params
message ListQuickBooksSettingParams {
  // business id
  optional int64 business_id = 1 [(validate.rules).int64.gt = 0];
  // company id
  optional int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // receipt status, only 0 or 1
  optional int32 receipt_status = 3 [(validate.rules).int32 = {
    in: [
      0,
      1
    ]
  }];
  // enable sync status
  optional int32 enable_sync_status = 4 [(validate.rules).int32 = {
    in: [
      0,
      1
    ]
  }];
  // user version
  optional int32 user_version = 5 [(validate.rules).int32 = {
    in: [
      0,
      1
    ]
  }];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 6;
}

// list quick book setting result
message ListQuickBooksSettingResult {
  // quick book setting
  message MoeQbSetting {
    // Auto-increment primary key
    uint32 id = 1;
    // Business ID
    int32 business_id = 2;
    // Associated connect ID
    int32 connect_id = 3;
    // Whether to enable synchronization: 1 - enable, 0 - disable (default)
    bool enable_sync = 4;
    // Scheduled start synchronization date (default is creation time)
    string sync_begin_date = 5;
    // The minimum sync date after setting changes; the earliest synchronized date
    string min_sync_date = 6;
    // Record status: 1 - active, 2 - expired
    bool status = 7;
    // Creation timestamp
    int64 create_time = 8;
    // Update timestamp
    int64 update_time = 9;
    // Company ID
    int64 company_id = 10;
    // Tax sync type: 1 - in line, 2 - in total
    int32 tax_sync_type = 11;
    // Marks the version of QB sync: 0 - legacy user, 1 - incremental user
    int32 user_version = 12;
    // Last disconnected time
    string last_disconnected_time = 13;
    // Whether to sync sales receipt: 0 - do not sync, 1 - sync
    bool sales_receipt_enable = 14;
  }
  // quick book setting list
  repeated MoeQbSetting moe_qb_settings = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// modify receipt status request
message UpdateQuickBooksSettingParams {
  // business id
  int64 business_id = 1;
  // receipt status, 0 = close, 1 = open
  optional int32 receipt_status = 2;
  // user version
  optional int32 user_version = 3;
  // tax sync type
  optional int32 tax_sync_type = 4;
}

// Compensate params
message CompensateParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // invoices
  message Invoices {
    // invoices
    repeated int32 ids = 1 [(validate.rules).repeated = {
      max_items: 100
      unique: true
      items: {
        int32: {gt: 0}
      }
    }];
  }
  // Compensate target
  oneof compensate_target {
    // time range
    google.type.Interval time_range = 2;
    // invoice ids
    Invoices invoices = 3;
  }
}

// CompensateResult
message CompensateResult {
  // Result
  repeated string compensate_msg = 1;
}

// list invoice
message ListSyncInvoicesParams {
  // business id
  int32 business_id = 1 [(validate.rules).int32 = {gt: 0}];
  //time range
  google.type.Interval time_range = 2;
  // invoice id
  int32 invoice_id = 3 [(validate.rules).int32 = {
    gt: 0
    ignore_empty: true
  }];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 10;
}

// Result
message ListSyncInvoicesResult {
  // quick book invoice information
  message QuickBookInvoice {
    // auto increment id
    int32 id = 1;
    // business id
    int32 business_id = 2;
    // company id
    int64 company_id = 3;
    // invoice id
    int32 invoice_id = 4;
    // invoice type
    string invoice_type = 5;
    // grooming id
    string grooming_id = 6;
    // quick book invoice id
    string qb_invoice_id = 7;
    // quick book invoice status
    string qb_invoice_status = 8;
    // total amount of invoice
    string total_amount = 9;
    // paid amount of invoice
    string pay_amount = 10;
    // payment status
    int32 pay_status = 11;
    // create timestamp
    int64 create_time = 12;
    // update timestamp
    int64 update_time = 13;
    // associated payments
    repeated QuickBookPayment association_payments = 14;
  }

  // quick book payment information
  message QuickBookPayment {
    // auto increment id
    int32 id = 1;
    // business id
    int32 business_id = 2;
    // company id
    int64 company_id = 3;
    // invoice id
    int32 invoice_id = 4;
    // paid amount
    string paid_amount = 5;
    // payment id
    int32 payment_id = 6;
    // quick book invoice id
    string qb_invoice_id = 7;
    // quick book payment id
    string qb_payment_id = 8;
    // quick book payment status
    string qb_payment_status = 9;
    // create timestamp
    int64 create_time = 10;
    // update timestamp
    int64 update_time = 11;
  }
  // invoices
  repeated QuickBookInvoice invoices = 1;

  // page
  moego.utils.v2.PaginationResponse pagination = 2;
}

// the quick_book service
service QuickBookService {
  // list quick book setting record
  rpc ListQuickBooksSetting(ListQuickBooksSettingParams) returns (ListQuickBooksSettingResult);

  // modify receipt status
  rpc UpdateQuickBooksSetting(UpdateQuickBooksSettingParams) returns (google.protobuf.Empty);

  // Compensate
  rpc Compensate(CompensateParams) returns (CompensateResult);

  // list quick book sync records invoices
  rpc ListSyncInvoices(ListSyncInvoicesParams) returns (ListSyncInvoicesResult);
}
