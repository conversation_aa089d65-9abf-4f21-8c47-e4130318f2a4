// @since 23-09-02
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.pay_ops.v1;

import "google/protobuf/timestamp.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/pay_ops/v1;payopsapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.pay_ops.v1";

// company custom fee
message CompanyCustomFee {
  // ID
  int64 id = 1;
  // account id
  int64 account_id = 2;
  // email
  string email = 3;
  // company id
  int64 company_id = 4;
  // company name
  string company_name = 5;
  // customized min vol
  int64 customized_min_vol = 6;
  // online fee rate
  double online_fee_rate = 7;
  // online fee cents
  int32 online_fee_cents = 8;
  // reader fee rate
  double reader_fee_rate = 9;
  // reader fee cents
  int32 reader_fee_cents = 10;
  // create time
  google.protobuf.Timestamp create_time = 11;
  // update time
  google.protobuf.Timestamp update_time = 12;
}

// business payment setting custom fee entry
message BusinessPaymentSettingView {
  // id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // allow custom rate
  int32 allow_custom_rate = 10;
  // close custom rate
  int32 close_custom_rate = 11;
}

// business custom fee params
message BusinessPaymentSettingParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// business custom fee result
message BusinessPaymentSettingResult {
  // business payment setting view list
  repeated BusinessPaymentSettingView business_payment_settings = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// company custom fee result
message CompanyCustomFeeResult {
  // company custom fee list
  repeated CompanyCustomFee company_custom_fees = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// company custom fee params
message CompanyCustomFeeParams {
  // company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // enterprise id
  optional int64 enterprise_id = 2 [(validate.rules).int64 = {gt: 0}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// add company custom fee params
message AddCompanyCustomFeeParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // online fee rate
  double online_fee_rate = 3 [(validate.rules).double = {
    gt: 0
    lt: 4
  }];
  // online fee cents
  uint32 online_fee_cents = 4 [(validate.rules).uint32 = {
    gte: 0
    lte: 100
  }];
  // reader fee rate
  double reader_fee_rate = 5 [(validate.rules).double = {
    gt: 0
    lt: 4
  }];
  // reader fee cents
  uint32 reader_fee_cents = 6 [(validate.rules).uint32 = {
    gte: 0
    lte: 100
  }];
  // customized min vol
  optional int64 customized_min_vol = 7 [(validate.rules).int64 = {gt: 1000}];
}

// update company custom fee params
message UpdateCompanyCustomFeeParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // customized min vol
  int64 customized_min_vol = 2 [(validate.rules).int64 = {gte: 0}];
  // online fee rate
  double online_fee_rate = 3 [(validate.rules).double = {
    gt: 0
    lt: 4
  }];
  // online fee cents
  uint32 online_fee_cents = 4 [(validate.rules).uint32 = {
    gte: 0
    lte: 100
  }];
  // reader fee rate
  double reader_fee_rate = 5 [(validate.rules).double = {
    gt: 0
    lt: 4
  }];
  // reader fee cents
  uint32 reader_fee_cents = 6 [(validate.rules).uint32 = {
    gte: 0
    lte: 100
  }];
}

// delete company custom fee params
message DeleteCompanyCustomFeeParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// create company custom fee params
message ListCompanyCustomFeesParams {
  // filter
  message Filter {
    // enterprise id
    int64 enterprise_id = 1;
  }
  // filter
  optional Filter filter = 1;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2;
}

// create company custom fee params
message ListCompanyCustomFeesResult {
  // company custom fee list
  repeated CompanyCustomFee company_custom_fees = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// custom fee service
service CustomFeeService {
  // get company custom fee list
  rpc GetCompanyCustomFeeList(CompanyCustomFeeParams) returns (CompanyCustomFeeResult);
  // create company custom fee
  rpc CreateCompanyCustomFee(AddCompanyCustomFeeParams) returns (CompanyCustomFee);
  // update company min vol
  rpc UpdateCompanyCustomFee(UpdateCompanyCustomFeeParams) returns (CompanyCustomFee);
  // get business payment setting list
  rpc GetBusinessPaymentSettingList(BusinessPaymentSettingParams) returns (BusinessPaymentSettingResult);
  // delete company custom fee
  rpc DeleteCompanyCustomFee(DeleteCompanyCustomFeeParams) returns (CompanyCustomFee);
  // list company custom fee list
  rpc ListCompanyCustomFees(ListCompanyCustomFeesParams) returns (ListCompanyCustomFeesResult);
}
