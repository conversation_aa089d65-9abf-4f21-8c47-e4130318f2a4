syntax = "proto3";

package moego.admin.pay_ops.v1;

import "google/protobuf/timestamp.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/pay_ops/v1;payopsapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.pay_ops.v1";

// moego contract api
service MoegoPayContractApi {
  // create contract
  rpc CreateContract(CreateContractParams) returns (CreateContractResult);
  // list contracts
  rpc ListContracts(ListContractsParams) returns (ListContractsResult);
  // delete contract
  rpc DeleteContract(DeleteContractParams) returns (DeleteContractResult);
}

// create contract params
message CreateContractParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // terminal percentage
  string terminal_percentage = 2 [(validate.rules).string = {min_len: 1}];

  // terminal fixed
  string terminal_fixed = 3 [(validate.rules).string = {min_len: 1}];

  // non-terminal percentage
  string non_terminal_percentage = 4 [(validate.rules).string = {min_len: 1}];

  // non-terminal fixed
  string non_terminal_fixed = 5 [(validate.rules).string = {min_len: 1}];

  // Minimum Monthly Transaction Commitment
  string min_volume = 6 [(validate.rules).string = {min_len: 1}];
}

// create contract result
message CreateContractResult {
  // contract id
  string id = 1;
}

// list contracts params
message ListContractsParams {
  // pagination
  moego.utils.v2.PaginationRequest pagination = 1;

  // owner email
  optional string owner_email = 2;
  // creator
  optional string creator = 3;
}

// list contracts result
message ListContractsResult {
  // contract list
  repeated MoegoPayContract contracts = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// delete contract params
message DeleteContractParams {
  // contract id
  string id = 1 [(validate.rules).string = {min_len: 1}];
}

// delete contract result
message DeleteContractResult {}

// MoegoPayContract
message MoegoPayContract {
  // id
  string id = 1;
  // template id
  string template_id = 2;
  // metadata
  Metadata metadata = 3;
  // params
  Parameters parameters = 4;
  // content
  string content = 5;
  // creator
  string creator = 6;
  // create time
  google.protobuf.Timestamp create_time = 7;
  // update time
  google.protobuf.Timestamp update_time = 8;
  // sign time
  optional google.protobuf.Timestamp sign_time = 9;

  // metadata
  message Metadata {
    // company id
    int64 company_id = 1;
    // account id
    int64 account_id = 2;
  }

  // params
  message Parameters {
    // company name
    string company_name = 1;
    // owner name
    string owner_name = 2;
    // owner email
    string owner_email = 3;
    // 终端收款手续费百分比，数字格式，如 "2.30" 表示 2.30%
    string terminal_percentage = 4;
    // 终端收款固定手续费，单位为 USD，数字格式，如 "5.10" 表示 $5.10
    string terminal_fixed = 5;
    // 非终端收款手续费百分比，数字格式
    string non_terminal_percentage = 6;
    // 非终端收款固定手续费，单位为 USD，数字格式
    string non_terminal_fixed = 7;
    // 每月最低交易额要求，单位为 USD，数字格式
    string min_volume = 8;
  }
}
