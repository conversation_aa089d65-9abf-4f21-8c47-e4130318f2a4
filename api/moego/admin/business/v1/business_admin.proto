// @since 2023-06-21 18:45:38
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.business.v1;

import "google/protobuf/struct.proto";
import "moego/models/account/v1/account_models.proto";
import "moego/utils/v2/option_message.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/business/v1;businessapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.business.v1";

// the business params
message DescribeBusinessesParams {
  // owner account id
  optional int64 owner_id = 1;
  // name prefix
  optional string name_like = 2 [(validate.rules).string = {max_len: 50}];
  // company id
  optional int64 company_id = 3;
  // business id
  optional int64 business_id = 5;
  // country
  optional string country = 4 [(validate.rules).string = {max_len: 50}];

  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// the business result
message DescribeBusinessesResult {
  // the business list without shape
  repeated google.protobuf.Struct deprecated_businesses = 1 [deprecated = true];
  // owner account map, key is account id
  map<int64, moego.models.account.v1.AccountModelSearchView> account_map = 2;
  // owner staff map, key is business id
  map<int64, google.protobuf.Struct> deprecated_staff_map = 3 [deprecated = true];
  // company map, key is company id
  map<int64, google.protobuf.Struct> deprecated_company_map = 4 [deprecated = true];
  // company map, key is business id
  map<int64, google.protobuf.Struct> phone_number_info_map = 5;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// search business params
message SearchBusinessParams {
  // the search term
  string term = 1 [(validate.rules).string = {max_len: 100}];
}

// search business result
message SearchBusinessResult {
  // options
  repeated moego.utils.v2.Option options = 1;
}

// the business service
service BusinessService {
  // describe businesses
  rpc DescribeBusinesses(DescribeBusinessesParams) returns (DescribeBusinessesResult);

  // search business
  rpc SearchBusiness(SearchBusinessParams) returns (SearchBusinessResult);
}
