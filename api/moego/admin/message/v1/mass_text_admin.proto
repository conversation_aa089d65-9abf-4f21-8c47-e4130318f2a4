// @since 2023-07-06 13:57:02
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.message.v1;

import "google/protobuf/struct.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/message/v1;messageapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.message.v1";

// DescribeMassTextsParams is the params for DescribeMassTexts
message DescribeMassTextsParams {
  // business id
  optional int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff id
  optional int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 15 [(validate.rules).message = {required: true}];
}

// DescribeMassTextsResult is the result for DescribeMassTexts
message DescribeMassTextsResult {
  // deprecated mass texts
  repeated google.protobuf.Struct mass_texts = 1 [deprecated = true];
  // deprecated business map, key is business id
  map<int64, google.protobuf.Struct> business_map = 2 [deprecated = true];
  // deprecated staff map, key is staff id
  map<int64, google.protobuf.Struct> staff_map = 3 [deprecated = true];
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// the mass_text service
service MassTextService {
  // describe mass texts
  rpc DescribeMassTexts(DescribeMassTextsParams) returns (DescribeMassTextsResult);
}
