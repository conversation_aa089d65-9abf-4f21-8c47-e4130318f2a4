// @since 23-09-02
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.company.v1;

import "moego/models/account/v1/account_models.proto";
import "moego/models/organization/v1/company_models.proto";
import "moego/models/pay_ops/v1/billing_models.proto";
import "moego/models/payment/v1/price_plan_conf_models.proto";
import "moego/utils/v2/option_message.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/company/v1;companyapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.company.v1";

// search company params
message SearchCompanyParams {
  // the search term
  string term = 1 [(validate.rules).string = {max_len: 100}];
}

// search company result
message SearchCompanyResult {
  // options
  repeated moego.utils.v2.Option options = 1;
}

// query company list params
message ListCompanyParams {
  //the account id
  optional int64 account_id = 1 [(validate.rules).int64.gt = 0];
  //the company id
  optional int64 company_id = 2 [(validate.rules).int64.gt = 0];
  //the company name
  optional string name = 3;
  //the company country
  optional string country = 4;
  // the enterprise id
  optional int64 enterprise_id = 5 [(validate.rules).int64.gt = 0];

  // the pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// query company list result
message ListCompanyResult {
  //company list
  repeated moego.models.organization.v1.CompanyModel companies = 1;
  //account by id
  map<int64, moego.models.account.v1.AccountModelRelevantView> accounts = 2;
  // company permission states
  map<int64, moego.models.pay_ops.v1.CompanyPermissionStateModel> company_permission_states = 3;
  // permission plan conf
  map<int64, moego.models.payment.v1.PricePlanConf> price_plan_conf = 4;
  // the enterprise owner account, key is enterprise id
  map<int64, moego.models.account.v1.AccountModelRelevantView> enterprise_owner_accounts = 5;

  // the pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

//update company params
message UpdateCompanyParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // company name
  optional string name = 2 [(validate.rules).string = {max_len: 255}];
  // enable square
  optional bool enable_square = 3;
  // enable stripe reader
  optional bool enable_stripe_reader = 4;
}

//update company result
message UpdateCompanyResult {}

// remove free account params
message RemoveFreeAccountParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
}

// remove free account result
message RemoveFreeAccountResult {}

// reactivate company params
message ReactivateCompanyParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
}

// reactivate company result
message ReactivateCompanyResult {}

// deactivate company params
message DeactivateCompanyParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
}

// deactivate company result
message DeactivateCompanyResult {}

// company service
service CompanyService {
  // search company
  rpc SearchCompany(SearchCompanyParams) returns (SearchCompanyResult);
  // query company list
  rpc ListCompany(ListCompanyParams) returns (ListCompanyResult);
  // update company
  rpc UpdateCompany(UpdateCompanyParams) returns (UpdateCompanyResult);
  // remove free account
  rpc RemoveFreeAccount(RemoveFreeAccountParams) returns (RemoveFreeAccountResult);
  // reactivate company
  rpc ReactivateCompany(ReactivateCompanyParams) returns (ReactivateCompanyResult);
  // deactivate company
  rpc DeactivateCompany(DeactivateCompanyParams) returns (DeactivateCompanyResult);
}
