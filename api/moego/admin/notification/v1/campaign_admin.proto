syntax = "proto3";

package moego.admin.notification.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "moego/models/business/v1/business_models.proto";
import "moego/models/reporting/v1/metrics_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/notification/v1;notificationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.notification.v1";

// the notification campaign service
service CampaignService {
  // start notification campaign
  rpc StartNotificationCampaign(StartNotificationCampaignParams) returns (google.protobuf.Empty);
  // search notification targets
  rpc SearchNotificationCampaignTargets(SearchNotificationCampaignTargetsParams) returns (SearchNotificationCampaignTargetsResult);
}

// StartNotificationCampaignParams
message StartNotificationCampaignParams {
  // marketing campaign notification
  MarketingCampaignNotification notification = 1 [(validate.rules).message = {required: true}];
  // target
  oneof target {
    option (validate.required) = true;
    // business list
    BusinessList list = 2;
    // business filter
    BusinessFilter filter = 3;
  }
  // staff filter
  StaffFilter staff_filter = 10;
}

// MarketingCampaignNotification
message MarketingCampaignNotification {
  // id
  int64 id = 1;
  // title
  string title = 2 [(validate.rules).string = {min_len: 1}];
  // body
  string body = 3 [(validate.rules).string = {min_len: 1}];
  // mobile_title
  optional string mobile_title = 4 [(validate.rules).string = {min_len: 1}];
  // mobile_body
  optional string mobile_body = 5 [(validate.rules).string = {min_len: 1}];
  // extra
  optional MarketingCampaignExtraData extra = 6;
  // author
  optional string author = 7;
}

// MarketingCampaignExtraData
message MarketingCampaignExtraData {
  // campaign url
  optional string campaign_url = 1 [(validate.rules).string = {
    uri: true
    min_len: 1
  }];
  // icon url
  optional string icon_url = 2 [(validate.rules).string = {
    uri: true
    min_len: 1
  }];
}

// BusinessList
message BusinessList {
  // ids
  repeated int64 ids = 1 [(validate.rules).repeated = {min_items: 1}];
}

// BusinessFilter
// https://moego.atlassian.net/browse/TECH-681
message BusinessFilter {
  // Business Type: mobile/salon
  optional moego.models.reporting.v1.BusinessType business_type = 1;
  // Business Country
  optional string business_country = 2;
  // Monthly Rev: amount of finished invoice during last 30 days
  optional moego.utils.v2.Range monthly_rev = 3;
  // Total Units: # of locations and vans purchased in the latest plan
  optional moego.utils.v2.Range total_units = 4;
  // Total Staff: # of staff on current staff list
  optional moego.utils.v2.Range total_staff = 5;
  // Total Appt: # of appointments created on calendar of last 30 days
  optional moego.utils.v2.Range total_appt = 6;
  // Total Finished Appt: # of appointments marked as finished in the last 30 days
  optional moego.utils.v2.Range total_finished_appt = 7;
  // Total Msg Usage: # of msg estimated to be sent in the current billing period
  optional moego.utils.v2.Range total_msg_usage = 8;
  // MoeGo Pay Status: Uninitialized/Operated
  optional moego.models.reporting.v1.MoeGoPayStatus moego_pay_status = 9;
  // MoeGo Pay Count: # of transactions through MoeGo Pay last 30 days
  optional moego.utils.v2.Range moego_pay_count = 10;
  // MoeGo Pay Transactions: amount of transactions through MoeGo Pay last 30 days
  optional moego.utils.v2.Range moego_pay_transactions = 11;
  // OB Status: Not Enabled/ OB 2.0
  optional moego.models.reporting.v1.OBStatus ob_status = 12;
  // OB Request: # of requests submitted through OB in the last 30 days
  optional moego.utils.v2.Range ob_requests = 13;
  // Van Credits: # of vans purchased
  optional moego.utils.v2.Range van_credits = 14;
  // Van Created: # of vans set up
  optional moego.utils.v2.Range van_created = 15;
  // Location Credits: # of locations purchased
  optional moego.utils.v2.Range location_credits = 16;
  // Location Created: # of locations set up
  optional moego.utils.v2.Range location_created = 17;
  // Permission Level: 101, 102, 1001, 1002, 1003
  repeated string permission_level = 18 [(validate.rules).repeated = {
    items: {
      string: {
        in: [
          "100",
          "101",
          "102",
          "103",
          "1000",
          "1001",
          "1002",
          "1003"
        ]
      }
    }
  }];
  // Role: Owner 所有数据默认为 company owner，不需要过滤
  // optional Role role = 19;
  // Metricized At: UTC data Time of yesterday
  google.protobuf.Timestamp metricized_at = 19 [(validate.rules).timestamp = {required: true}];
}

// StaffFilter
message StaffFilter {
  // can access online booking and wait list
  optional google.protobuf.BoolValue can_access_booking_requests = 1;
}

// SearchNotificationCampaignTargetsParams
message SearchNotificationCampaignTargetsParams {
  // filter
  optional BusinessFilter filter = 1;
  // order by
  repeated moego.utils.v2.OrderBy order_bys = 2;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3 [(validate.rules).message = {required: true}];
}

// SearchNotificationCampaignTargetsResult
message SearchNotificationCampaignTargetsResult {
  // business infos
  repeated NotificationTargetInfo businesses = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// notification target info
message NotificationTargetInfo {
  // business info
  moego.models.business.v1.BusinessModel business = 1;
  // business metrics
  moego.models.reporting.v1.BusinessMetricsModel metrics = 2;
}
