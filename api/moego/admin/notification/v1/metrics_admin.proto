syntax = "proto3";

package moego.admin.notification.v1;

import "google/type/interval.proto";
import "moego/models/business/v1/business_models.proto";
import "moego/models/reporting/v1/reporting_models.proto";
import "moego/service/reporting/v1/metrics_service.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/notification/v1;notificationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.notification.v1";

// the notification metrics service
service MetricsService {
  // Search Campaign Metrics
  rpc SearchCampaignMetrics(SearchCampaignMetricsParams) returns (SearchCampaignMetricsResult);
  // Search Campaign Reports
  rpc SearchCampaignReports(SearchCampaignReportsParams) returns (SearchCampaignReportsResult);
}

// Search Campaign Metrics Params
message SearchCampaignMetricsParams {
  // group by
  moego.service.reporting.v1.GroupBy group_by = 1;
  // order by
  repeated moego.utils.v2.OrderBy order_bys = 2;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3 [(validate.rules).message = {required: true}];
  // filter
  oneof filter {
    // campaign filter
    CampaignFilter campaign_filter = 4;
    // staff campaign filter
    StaffCampaignFilter staff_campaign_filter = 5;
  }
}

// CampaignFilter
message CampaignFilter {
  // campaign starter
  optional string starter = 1;
  // campaign started at
  optional google.type.Interval started_at = 2;
  // campaign notification title
  optional string notification_title = 3;
  // campaign notification body
  optional string notification_body = 4;
}

// StaffCampaignFilter
message StaffCampaignFilter {
  // receiver name
  optional string receiver_name = 1;
}

// Search Campaign Metrics Result
message SearchCampaignMetricsResult {
  // pagination
  moego.utils.v2.PaginationResponse pagination = 1;
  // metrics
  repeated moego.service.reporting.v1.CampaignMetrics metrics = 2;
}

// SearchCampaignReportsParams
message SearchCampaignReportsParams {
  // pagination
  moego.utils.v2.PaginationRequest pagination = 1 [(validate.rules).message = {required: true}];
  // filter
  CampaignReportsFilter filter = 2;
}

// CampaignReportsFilter
message CampaignReportsFilter {
  // campaign id
  optional int64 campaign_id = 1 [(validate.rules).int64 = {gt: 0}];
  // receiver staff id
  optional int64 receiver_staff_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// SearchCampaignReportsResult
message SearchCampaignReportsResult {
  // pagination
  moego.utils.v2.PaginationResponse pagination = 1;
  // reports
  repeated CampaignReport reports = 2;
}

// CampaignReport
message CampaignReport {
  // business info
  moego.models.business.v1.BusinessModel business = 1;
  // report
  moego.models.reporting.v1.CampaignReportModel report = 2;
}
