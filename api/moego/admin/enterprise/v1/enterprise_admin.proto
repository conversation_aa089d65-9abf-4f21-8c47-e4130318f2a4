syntax = "proto3";

package moego.admin.enterprise.v1;

import "moego/admin/account/v1/demo_account_admin.proto";
import "moego/models/enterprise/v1/enterprise_models.proto";
import "moego/models/organization/v1/company_models.proto";
import "moego/models/payment/v1/payer_defs.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/option_message.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/enterprise/v1;enterpriseapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.enterprise.v1";

// describe enterprises request params
message DescribeEnterprisesParams {
  // owner account id
  optional int64 account_id = 1 [(validate.rules).int64 = {not_in: 0}];
  // not support now
  optional string name_like = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // id
  optional int64 id = 3 [(validate.rules).int64 = {not_in: 0}];
  // not support now
  optional bool include_deleted = 4;
  // not support now
  optional moego.utils.v2.OrderBy order_by = 14;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// describe enterprises result
message DescribeEnterprisesResult {
  // enterprises
  repeated moego.models.enterprise.v1.EnterpriseModel enterprises = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// create demo enterprise request params
message CreateDemoEnterpriseParams {
  // name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // owner email
  string owner_email = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 100
    email: true
  }];
  // template franchisee
  moego.admin.account.v1.CreateDemoAccountParams template_franchisee = 3 [(validate.rules).message = {required: true}];
}

// create demo enterprise result
message CreateDemoEnterpriseResult {
  // enterprise
  moego.models.enterprise.v1.EnterpriseModel enterprise = 1;
}

// create demo franchisee request params
message CreateDemoFranchiseeParams {
  // CreateDemoAccountParams
  moego.admin.account.v1.CreateDemoAccountParams account = 1;
}

// create demo franchisee result
message CreateDemoFranchiseeResult {
  // todo
}

// update enterprise request params
message UpdateEnterpriseParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {not_in: 0}];
  // name
  optional string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // owner account id
  optional int64 account_id = 3 [(validate.rules).int64 = {not_in: 0}];
}

// update enterprise result
message UpdateEnterpriseResult {
  // enterprise
  moego.models.enterprise.v1.EnterpriseModel enterprise = 1;
}

// describe enterprise company infos request params
message DescribeCompanyInfosByEnterpriseParams {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {not_in: 0}];
  // need bill payer setting
  bool need_bill_payer_setting = 2;
  // order by
  optional moego.utils.v2.OrderBy order_by = 14;
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// describe enterprise company infos result
message DescribeCompaniesByEnterpriseResult {
  // company info with bill payer setting
  message CompanyWithBillPayerSetting {
    // company info
    moego.models.organization.v1.CompanyModel company = 1;
    // bill payer setting
    optional moego.models.payment.v1.BillPayerSettingDef bill_payer_setting = 2;
  }
  // company infos
  repeated CompanyWithBillPayerSetting company_infos = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// update company bill payer setting request params
message UpdateCompanyBillPayerSettingParams {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {not_in: 0}];
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {not_in: 0}];
  // bill payer setting
  repeated moego.models.payment.v1.BillPayerSettingDef bill_payer_setting = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 10
  }];
}

// update company bill payer setting result
message UpdateCompanyBillPayerSettingResult {}

// link enterprise and companies request params
message LinkEnterpriseAndCompaniesParams {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {not_in: 0}];
  // company id
  repeated int64 company_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      int64: {not_in: 0}
    }
  }];
}

// link enterprise and companies result
message LinkEnterpriseAndCompaniesResult {}

// unlink enterprise and companies request params
message UnLinkEnterpriseAndCompaniesParams {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {not_in: 0}];
  // company id
  repeated int64 company_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      int64: {not_in: 0}
    }
  }];
}

// unlink enterprise and companies result
message UnLinkEnterpriseAndCompaniesResult {}

// search enterprise params
message SearchEnterpriseParams {
  // the search enterprise by account id or account email
  string term = 1 [(validate.rules).string = {max_len: 100}];
}

// search company result
message SearchEnterpriseResult {
  // options
  repeated moego.utils.v2.Option options = 1;
}

// export enterprise companies excel params
message ExportEnterpriseCompaniesExcelParams {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {not_in: 0}];
}

// export enterprise companies excel result
message ExportEnterpriseCompaniesExcelResult {
  // access url
  string access_url = 1;
}

// enterprise admin service
service EnterpriseService {
  // describe enterprises
  rpc DescribeEnterprises(DescribeEnterprisesParams) returns (DescribeEnterprisesResult) {}
  // create demo enterprise
  rpc CreateDemoEnterprise(CreateDemoEnterpriseParams) returns (CreateDemoEnterpriseResult) {}
  // create demo franchisee
  rpc CreateDemoFranchisee(CreateDemoFranchiseeParams) returns (CreateDemoFranchiseeResult) {}
  // update enterprise
  rpc UpdateEnterprise(UpdateEnterpriseParams) returns (UpdateEnterpriseResult) {}
  // describe enterprise company infos
  rpc DescribeCompaniesByEnterprise(DescribeCompanyInfosByEnterpriseParams) returns (DescribeCompaniesByEnterpriseResult) {}
  // update company bill payer setting, will call server-payment
  rpc UpdateCompanyBillPayerSetting(UpdateCompanyBillPayerSettingParams) returns (UpdateCompanyBillPayerSettingResult) {}
  // link enterprise and companies
  rpc LinkEnterpriseAndCompanies(LinkEnterpriseAndCompaniesParams) returns (LinkEnterpriseAndCompaniesResult) {}
  // unlink enterprise and companies
  rpc UnLinkEnterpriseAndCompanies(UnLinkEnterpriseAndCompaniesParams) returns (UnLinkEnterpriseAndCompaniesResult) {}
  // search enterprise
  rpc SearchEnterprise(SearchEnterpriseParams) returns (SearchEnterpriseResult);
  // export enterprise companies to excel
  rpc ExportEnterpriseCompaniesExcel(ExportEnterpriseCompaniesExcelParams) returns (ExportEnterpriseCompaniesExcelResult) {}
}
