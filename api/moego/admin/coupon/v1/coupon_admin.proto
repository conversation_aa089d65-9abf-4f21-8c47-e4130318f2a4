// @since 2-23-10-07
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.coupon.v1;

import "moego/models/coupon/v1/coupon_enums.proto";
import "moego/models/coupon/v1/coupon_model.proto";
import "moego/utils/v2/option_message.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/coupon/v1;couponapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.coupon.v1";

// create new coupon params
message CreateNewCouponParams {
  // valid month for the coupon [1 to 100]
  int64 valid_month = 1 [(validate.rules).int64 = {
    gte: 1
    lte: 100
  }];

  // custom name for the coupon
  string custom_name = 2;

  // discount percentage, an integer from 1 to 100
  // default value is 50, it can be omitted in the front-end
  int32 percent_off = 3 [(validate.rules).int32 = {
    gte: 1
    lte: 100
  }];

  // category of coupon
  // used to distinguish which business the coupon belongs to
  moego.models.coupon.v1.CouponBusinessCategory business_category = 4;
}

// create new coupon result
message CreateNewCouponResult {
  // stripe coupon id
  int64 id = 1;
}

// create new coupon params
message CreateCouponParams {
  // Specifies how long the discount will be in effect
  optional int64 valid_month = 1 [(validate.rules).int64 = {
    gte: 0
    lte: 100
  }];

  // name for the coupon,can't be null
  string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];

  // discount percentage, an integer from 1 to 100
  // default value is 50, it can be omitted in the front-end
  optional int32 percent_off = 3 [(validate.rules).int32 = {
    gte: 1
    lte: 100
  }];

  // category of coupon
  // used to distinguish which business the coupon belongs to
  moego.models.coupon.v1.CouponBusinessCategory business_category = 4 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];

  // discount amount
  optional double discount_amount = 5;

  // Specifies how long the discount will be in effect if used on a subscription. Defaults to once
  string duration = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];

  //Limit the date range when customers can redeem this coupon
  optional string redeem_by = 7;

  //Limit the total number of times this coupon can be redeemed
  optional int32 max_redeem = 8;
}

// create new coupon result
message CreateCouponResult {
  // stripe coupon id
  int64 id = 1;
}

// query coupon list params
message QueryCouponListParams {
  // Title of the coupon, supports fuzzy search
  optional string code = 1;

  // Lower limit of the discount percentage, range 0 to 100
  optional double percent_off_left = 2 [(validate.rules).double = {
    gte: 0
    lte: 100
  }];

  // Upper limit of the discount percentage, range 0 to 100
  optional double percent_off_right = 3 [(validate.rules).double = {
    gte: 0
    lte: 100
  }];

  // 1 - valid, 0 - invalid; if null, all will be queried
  optional int32 valid = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 1
  }];

  // 0 - unassigned, 1 - assigned, 2 - used
  optional int32 status = 5 [(validate.rules).int32 = {
    gte: 0
    lte: 2
  }];

  // Validity period in months, can be specified from 1 to 100
  optional int32 valid_month = 6 [(validate.rules).int32 = {
    gte: 1
    lte: 100
  }];

  // filter by business category
  optional moego.models.coupon.v1.CouponBusinessCategory business_category = 7;

  // stripe coupon id
  optional string stripe_coupon_id = 8;

  // pagination params
  moego.utils.v2.PaginationRequest pagination = 15;
}

// query coupon list result
message QueryCouponListResult {
  // coupon list
  repeated moego.models.coupon.v1.CouponSimpleView coupons = 1;

  // business category map
  map<int32, string> business_category_map = 2;

  // pagination result
  moego.utils.v2.PaginationResponse pagination = 15;
}

// query coupon params
message QueryCouponParams {
  // coupon id
  int64 id = 1;
}

// update coupon params
message UpdateCouponParams {
  // id
  int64 id = 1;
  // status, Delete
  optional int32 status = 2;
}

// search coupon by term params
message SearchCouponByTermParams {
  // the search term
  string term = 1 [(validate.rules).string = {max_len: 100}];
}

// search coupon result
message SearchCouponByTermResult {
  // options
  repeated moego.utils.v2.Option options = 1;
}

// deleted coupon params
message DeleteCouponParams {
  // coupon id
  int64 id = 1;
}

// coupon service
service CouponService {
  // add new coupon (deprecated)
  rpc CreateNewCoupon(CreateNewCouponParams) returns (CreateNewCouponResult) {
    option deprecated = true;
  }

  // add new coupon
  rpc CreateCoupon(CreateCouponParams) returns (CreateCouponResult);
  // query coupon list
  rpc QueryCouponList(QueryCouponListParams) returns (QueryCouponListResult);
  // get coupon
  rpc QueryCoupon(QueryCouponParams) returns (moego.models.coupon.v1.CouponModel);
  // update coupon
  rpc UpdateCoupon(UpdateCouponParams) returns (moego.models.coupon.v1.CouponModel);
  // query coupon by term
  rpc SearchCouponByTerm(SearchCouponByTermParams) returns (SearchCouponByTermResult);
  // delete coupon
  rpc DeleteCoupon(DeleteCouponParams) returns (moego.models.coupon.v1.CouponModel);
}
