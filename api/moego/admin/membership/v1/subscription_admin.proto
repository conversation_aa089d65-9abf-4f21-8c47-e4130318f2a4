// @since 2024-06-14 13:53:26
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.membership.v1;

import "moego/models/membership/v1/subscription_defs.proto";
import "moego/models/membership/v1/subscription_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/membership/v1;membershipapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.membership.v1";

// create subscription params
message CreateSubscriptionParams {
  // the subscription def
  moego.models.membership.v1.SubscriptionCreateDef subscription_def = 1 [(validate.rules).message = {required: true}];
}

// create subscription result
message CreateSubscriptionResult {
  // the created subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
}

// get subscription params
message GetSubscriptionParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get subscription result
message GetSubscriptionResult {
  // the subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
}

// list subscription params
message ListSubscriptionsParams {
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// list subscription result
message ListSubscriptionsResult {
  // the subscription
  repeated moego.models.membership.v1.SubscriptionModel subscriptions = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// create subscription params
message UpdateSubscriptionParams {
  // id
  int64 id = 1;
  // the subscription def
  moego.models.membership.v1.SubscriptionUpdateDef subscription_def = 2 [(validate.rules).message = {required: true}];
}

// create subscription result
message UpdateSubscriptionResult {
  // the updated subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
}

// get subscription params
message DeleteSubscriptionParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get subscription result
message DeleteSubscriptionResult {}

// the subscription service
service SubscriptionService {
  // create subscription
  rpc CreateSubscription(CreateSubscriptionParams) returns (CreateSubscriptionResult);
  // get subscription
  rpc GetSubscription(GetSubscriptionParams) returns (GetSubscriptionResult);
  // list subscription
  rpc ListSubscriptions(ListSubscriptionsParams) returns (ListSubscriptionsResult);
  // update subscription
  rpc UpdateSubscription(UpdateSubscriptionParams) returns (UpdateSubscriptionResult);
  // delete subscription
  rpc DeleteSubscription(DeleteSubscriptionParams) returns (DeleteSubscriptionResult);
}
