syntax = "proto3";

package moego.enterprise.map.v1;

import "moego/models/grooming/v1/zip_code_models.proto";
import "moego/models/map/v1/google_map_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/map/v1;mapapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.map.v1";

// list zip code params
message ListZipCodeParams {
  // zip code
  repeated string zip_codes = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
  }];
}

// list zip code result
message ListZipCodeResult {
  // zip codes
  repeated models.grooming.v1.ZipCodeModel zip_codes = 1;
}

// search zip code params
message SearchZipCodeParams {
  // prefix
  optional string prefix = 1 [(validate.rules).string = {max_len: 100}];
}

// search zip code result
message SearchZipCodeResult {
  // zip codes
  repeated models.grooming.v1.ZipCodeModel zip_codes = 1;
}

// ListGooglePlaceParams
message ListGooglePlaceParams {
  // place id list
  repeated string place_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 1024
      }
    }
  }];

  // reserved ...
}

// ListGooglePlaceResult
message ListGooglePlaceResult {
  // google place list
  repeated moego.models.map.v1.GooglePlace places = 1;
}

// MapService
service MapService {
  // list zip code
  rpc ListZipCode(ListZipCodeParams) returns (ListZipCodeResult);
  // search zip code
  rpc SearchZipCode(SearchZipCodeParams) returns (SearchZipCodeResult);
  // get google place list
  rpc ListGooglePlace(ListGooglePlaceParams) returns (ListGooglePlaceResult);
}
