syntax = "proto3";

package moego.enterprise.price_book.v1;

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "moego/models/enterprise/v1/price_book_models.proto";
import "moego/models/enterprise/v1/tenant_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/service/enterprise/v1/price_book_service.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/price_book/v1;pricebookapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.price_book.v1";

// price book service
service PriceBookService {
  // list price books
  rpc ListPriceBooks(ListPriceBooksParams) returns (ListPriceBooksResult);
  // create and update existed categories
  rpc SaveServiceCategories(SaveServiceCategoriesParams) returns (SaveServiceCategoriesResult) {}
  // list service categories
  rpc ListServiceCategories(ListServiceCategoriesParams) returns (ListServiceCategoriesResult) {}
  // list pet breeds
  rpc ListPetBreeds(ListPetBreedsParams) returns (ListPetBreedsResult) {}
  // list pet types
  rpc ListPetTypes(ListPetTypesParams) returns (ListPetTypesResult) {}
  // create a service
  rpc CreateService(CreateServiceParams) returns (CreateServiceResult) {}
  // get a service
  rpc GetService(GetServiceParams) returns (GetServiceResult) {}
  // list services
  rpc ListServices(ListServicesParams) returns (ListServicesResult) {}
  // update a service
  rpc UpdateService(UpdateServiceParams) returns (UpdateServiceResult) {}
  // delete a service
  rpc DeleteService(DeleteServiceParams) returns (DeleteServiceResult) {}
  // sort services
  rpc SortServices(SortServicesParams) returns (SortServicesResult) {}
  // list service change histories
  rpc ListServiceChangeHistories(ListServiceChangeHistoriesParams) returns (ListServiceChangeHistoriesResult) {}
  // list service changes
  rpc ListServiceChanges(ListServiceChangesParams) returns (ListServiceChangesResult) {}
  // push service changes
  rpc PushServiceChanges(PushServiceChangesParams) returns (PushServiceChangesResult) {}
}

// ListPriceBooksParamas
message ListPriceBooksParams {}

// ListPriceBooksResult
message ListPriceBooksResult {
  // price books
  repeated moego.models.enterprise.v1.PriceBook price_books = 1;
}

// SaveServiceCategoriesParams
message SaveServiceCategoriesParams {
  // categories
  repeated moego.models.enterprise.v1.ServiceCategory categories = 1;
  // service type
  moego.models.offering.v1.ServiceType service_type = 2 [(validate.rules).enum = {not_in: 0}];
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 3;
}

// SaveServiceCategoriesResult
message SaveServiceCategoriesResult {}

// ListServiceCategoriesParams
message ListServiceCategoriesParams {
  // page
  utils.v2.PaginationRequest pagination = 1;
  // filter
  moego.service.enterprise.v1.ListServiceCategoriesRequest.Filter filter = 2;
}

// ListServiceCategoriesResult
message ListServiceCategoriesResult {
  // service categories
  repeated moego.models.enterprise.v1.ServiceCategory service_categories = 1;
}

// ListPetBreedsParams
message ListPetBreedsParams {}

// ListPetBreedsResult {
message ListPetBreedsResult {
  // pet breeds
  repeated moego.models.enterprise.v1.PetBreed pet_breeds = 1;
}

// list pet types params
message ListPetTypesParams {}

// list pet types result
message ListPetTypesResult {
  // pet type list
  repeated moego.models.enterprise.v1.PetType pet_types = 1;
}

// CreateServiceParams
message CreateServiceParams {
  // price book
  moego.models.enterprise.v1.PriceBook price_book = 1;
  // name
  string name = 2;
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 3;
  // category
  moego.models.enterprise.v1.ServiceCategory category = 4;
  // description
  string description = 5;
  // inactive
  bool inactive = 6;
  // color
  string color = 7;
  // sort
  int64 sort = 8;
  // price
  google.type.Money price = 9;
  // service price unit
  moego.models.offering.v1.ServicePriceUnit service_price_unit = 10;
  // 万分位税率
  int64 tax_rate = 11;
  // duration
  google.protobuf.Duration duration = 12;
  // max duration
  google.protobuf.Duration max_duration = 13;
  // limitation
  moego.models.enterprise.v1.Service.Limitation limitation = 14;
  // service type
  moego.models.offering.v1.ServiceType service_type = 15 [(validate.rules).enum = {not_in: 0}];
  // images
  repeated string images = 16;
}

// CreateServiceResult
message CreateServiceResult {
  // service
  moego.models.enterprise.v1.Service service = 1;
}

// GetServiceParams
message GetServiceParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// GetServiceResult
message GetServiceResult {
  // service
  moego.models.enterprise.v1.Service service = 1;
}

// ListServicesParams
message ListServicesParams {
  // page
  utils.v2.PaginationRequest pagination = 1;
  // filter
  moego.service.enterprise.v1.ListServicesRequest.Filter filter = 2;
}

// ListServicesResult
message ListServicesResult {
  // page
  utils.v2.PaginationResponse pagination = 1;
  // services
  repeated moego.models.enterprise.v1.Service services = 2;
}

// UpdateServiceParams
message UpdateServiceParams {
  // id
  int64 id = 1;
  // name
  optional string name = 2;
  // category id
  moego.models.enterprise.v1.ServiceCategory service_category = 3;
  // description
  optional string description = 4;
  // inactive
  bool inactive = 5;
  // color
  optional string color = 6;
  // price
  google.type.Money price = 7;
  // service price unit
  moego.models.offering.v1.ServicePriceUnit service_price_unit = 8;
  // 万分位税率
  optional int64 tax_rate = 9;
  // duration
  google.protobuf.Duration duration = 10;
  // max duration
  google.protobuf.Duration max_duration = 11;
  // limitation
  moego.models.enterprise.v1.Service.Limitation limitation = 12;
  // images
  repeated string images = 13;
}

// UpdateServiceResult
message UpdateServiceResult {
  // service
  moego.models.enterprise.v1.Service service = 1;
}

// DeleteServiceParams
message DeleteServiceParams {
  // id
  int64 id = 1;
}

// DeleteServiceResult
message DeleteServiceResult {}

// SortServicesParams
message SortServicesParams {
  // sorted services
  repeated moego.service.enterprise.v1.SortServicesRequest.ServiceCategorySort service_category_sorts = 1;
}

// SortServicesResult
message SortServicesResult {}

// ListServiceChangeHistoriesParams
message ListServiceChangeHistoriesParams {
  // page
  utils.v2.PaginationRequest pagination = 1;
  // filter
  moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.Filter filter = 2;
  // order by
  optional moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.OrderBy order_by = 3;
}

// ListServiceChangeHistoriesResult
message ListServiceChangeHistoriesResult {
  // page
  utils.v2.PaginationResponse pagination = 1;
  // service change history
  repeated moego.models.enterprise.v1.ServiceChangeHistory service_change_histories = 2;
}

// ListServiceChangesParams
message ListServiceChangesParams {
  // page
  utils.v2.PaginationRequest pagination = 1;
  // filter
  moego.service.enterprise.v1.ListServiceChangesRequest.Filter filter = 2;
}

// ListServiceChangesResult
message ListServiceChangesResult {
  // page
  utils.v2.PaginationResponse pagination = 1;
  // service changes
  repeated moego.models.enterprise.v1.ServiceChange service_changes = 2;
}

// PushServiceChangesParams
message PushServiceChangesParams {
  // service ids
  repeated int64 service_ids = 1;
  // targets
  repeated moego.models.enterprise.v1.TenantObject targets = 2;
  // effective date
  google.protobuf.Timestamp effective_date = 3;
  // apply to booked services
  bool apply_to_booked_services = 4;
}

// PushServiceChangesResult
message PushServiceChangesResult {
  // success company ids
  repeated int64 success_company_ids = 1;
  // failed company ids
  repeated int64 failed_company_ids = 2;
}
