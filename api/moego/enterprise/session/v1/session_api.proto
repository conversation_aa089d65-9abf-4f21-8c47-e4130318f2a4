syntax = "proto3";

package moego.enterprise.session.v1;

import "moego/models/account/v1/account_models.proto";
import "moego/models/enterprise/v1/enterprise_models.proto";
import "moego/models/enterprise/v1/staff_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/session/v1;sessionapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.session.v1";

// GetSessionDataParams
message GetSessionDataParams {}

// GetSessionDataResult
message GetSessionDataResult {
  // session id
  int64 session_id = 1;
  // account info
  models.account.v1.AccountModel account = 2;
  // staff info
  models.enterprise.v1.StaffModel staff = 3;
  // enterprise info
  models.enterprise.v1.EnterpriseModel enterprise = 4;
}

// SessionService
service SessionService {
  // GetSessionData
  rpc GetSessionData(GetSessionDataParams) returns (GetSessionDataResult) {}
}
