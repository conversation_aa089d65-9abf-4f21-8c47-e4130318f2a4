syntax = "proto3";

package moego.enterprise.report.v1;

import "google/protobuf/empty.proto";
import "google/type/calendar_period.proto";
import "google/type/interval.proto";
import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/diagram_model.proto";
import "moego/models/reporting/v2/report_def.proto";
import "moego/models/reporting/v2/report_meta_def.proto";
import "moego/models/reporting/v2/report_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/report/v1;reportapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.report.v1";

// ReportService is the service for reporting reports
service EnterpriseReportService {
  // Query report pages returns a list of report pages
  rpc QueryReportPages(QueryReportPagesRequest) returns (QueryReportPagesResponse);
  // Mark report favorite marks/removes a report as favorite, return the reports with new sequence
  rpc MarkReportFavorite(MarkReportFavoriteRequest) returns (MarkReportFavoriteResponse);
  // Save report customized config
  rpc SaveReportCustomizeConfig(SaveReportCustomizeConfigRequest) returns (google.protobuf.Empty);
  // Query metadata of reports
  rpc QueryReportMetas(QueryReportMetasRequest) returns (QueryReportsMetasResponse);
  // Fetch report data
  rpc FetchReportData(FetchReportDataRequest) returns (FetchReportDataResponse);
  // Export report data
  rpc ExportReportData(ExportReportDataRequest) returns (ExportReportDataResponse);

  // Common page api
  rpc QueryPages(moego.models.reporting.v2.QueryPageMetaParams) returns (moego.models.reporting.v2.QueryPageMetaResult);
  // Common meta api
  rpc QueryMetas(moego.models.reporting.v2.QueryMetasParams) returns (moego.models.reporting.v2.QueryMetasResult);
  // Common fetch data api
  rpc FetchData(moego.models.reporting.v2.FetchDataParams) returns (moego.models.reporting.v2.FetchDataResult);
  // Common export data api
  rpc ExportData(moego.models.reporting.v2.ExportDataParams) returns (moego.models.reporting.v2.ExportDataResult);
}

// QueryReportsRequest
message QueryReportPagesRequest {
  // The tabs to query reports page
  repeated moego.models.reporting.v2.ReportPage.Tab tabs = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// QueryReportsResponse
message QueryReportPagesResponse {
  // The list of report pages
  repeated moego.models.reporting.v2.ReportPage pages = 1;
}

// MarkReportAsFavoriteRequest
message MarkReportFavoriteRequest {
  // The favorite action enum
  enum Action {
    // Unspecified favorite actions
    FAVORITE_ACTION_UNSPECIFIED = 0;
    // A favorite action to add a report to favorites
    ADD = 1;
    // A favorite action to remove a report from favorites
    REMOVE = 2;
  }

  // The report to mark as favorite
  string diagram_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // The action to take
  Action action = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// MarkReportFavoriteResponse
message MarkReportFavoriteResponse {
  // Mark result
  bool result = 1;
}

// SaveReportCustomizeConfigRequest
message SaveReportCustomizeConfigRequest {
  // The report to save customized configs
  string diagram_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // Customize configs to save
  moego.models.reporting.v2.TableCustomizedConfig customized_config = 2;
}

// QueryReportsMetaRequest
message QueryReportMetasRequest {
  // The report to query meta data, if empty, return all reports' meta data
  repeated string diagram_ids = 1 [(validate.rules).repeated.items.string = {
    min_len: 1
    max_len: 100
  }];
  // tenant ids
  repeated uint64 tenants_ids = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
  // all tenants
  bool all_tenants = 3;
}

// QueryReportsMetaRequest
message QueryReportsMetasResponse {
  // The list of report metadata
  repeated moego.models.reporting.v2.TableMeta report_metas = 1;
}

// Describe a request to fetch report data
message FetchReportDataRequest {
  // diagram id
  string diagram_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // The tenant id
  repeated uint64 tenants_ids = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
  // group by period, could be day, week, month, year etc., default day.
  optional google.type.CalendarPeriod group_by_period = 3;
  // current period
  google.type.Interval current_period = 4 [(validate.rules).message = {required: true}];
  // previous period
  optional google.type.Interval previous_period = 5;
  // Filters
  repeated models.reporting.v2.FilterRequest filters = 6;
  // The group by field key
  repeated string group_by_field_keys = 7;
  // The pagination request
  moego.utils.v2.PaginationRequest pagination = 8;
  // The order by config
  repeated moego.utils.v2.OrderBy order_bys = 9;
  // The filter groups
  repeated moego.models.reporting.v2.FilterRequestGroup filter_groups = 10;
  // All tenants
  bool all_tenants = 11;
}

// Describe a response to fetch report data
message FetchReportDataResponse {
  // The report data
  moego.models.reporting.v2.TableData table_data = 1;
  // The pagination response
  moego.utils.v2.PaginationResponse pagination = 2;
}

// ExportReportDataRequest
message ExportReportDataRequest {
  // diagram id
  string diagram_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // filters
  repeated moego.models.reporting.v2.FilterRequest filters = 2;
  // tenant ids
  repeated uint64 tenants_ids = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
  // current period
  google.type.Interval current_period = 4 [(validate.rules).message = {required: true}];
  // previous period
  optional google.type.Interval previous_period = 5;
  // group by field key
  repeated string group_by_field_keys = 6;
  // order by params
  repeated moego.utils.v2.OrderBy order_bys = 7;
  // group by period, could be day, week, month, year etc., default day.
  optional google.type.CalendarPeriod group_by_period = 8;
  // filter groups
  repeated moego.models.reporting.v2.FilterRequestGroup filter_groups = 9;
  // is all tenants
  bool all_tenants = 10;
}

// ExportReportDataResponse
message ExportReportDataResponse {
  // file id
  int64 file_id = 1;
}
