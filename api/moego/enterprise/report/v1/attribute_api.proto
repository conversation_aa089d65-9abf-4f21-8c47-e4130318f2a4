syntax = "proto3";

package moego.enterprise.report.v1;

import "moego/models/reporting/v2/attribute_def.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/report/v1;reportapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.report.v1";

// Attribute API for enterprise
service EnterpriseAttributeService {
  // Get dimensions
  rpc GetDimensions(moego.models.reporting.v2.GetDimensionsParams) returns (moego.models.reporting.v2.GetDimensionsResult);
  // Get metrics categories
  rpc GetMetricsCategories(moego.models.reporting.v2.GetMetricsCategoriesParams) returns (moego.models.reporting.v2.GetMetricsCategoriesResult);
}
