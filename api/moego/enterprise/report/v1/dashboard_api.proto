syntax = "proto3";

package moego.enterprise.report.v1;

import "google/type/interval.proto";
import "moego/models/reporting/v2/dashboard_model.proto";
import "moego/models/reporting/v2/diagram_model.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/report/v1;reportapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.report.v1";

// DashboardService is the service for reporting dashboards
service EnterpriseDashboardService {
  // QueryDashboardPages returns meta data of dashboard
  rpc QueryDashboardPages(QueryDashboardPagesRequest) returns (QueryDashboardPagesResponse);
  // FetchDashboardData fetches dashboard diagram data
  rpc FetchDashboardData(FetchDashboardDataRequest) returns (FetchDashboardDataResponse);
}

// Describe pages of dashboard
message QueryDashboardPagesRequest {
  // The tabs of query dashboard page
  repeated moego.models.reporting.v2.DashboardPage.Tab tabs = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // tenant ids
  repeated uint64 tenants_ids = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
  // Whether to fetch data for all tenants
  bool all_tenants = 3;
}

// Describe pages of dashboard
message QueryDashboardPagesResponse {
  // The list of dashboard pages
  repeated moego.models.reporting.v2.DashboardPage dashboard_pages = 1;
  // The customized config
  moego.models.reporting.v2.TableCustomizedConfig customized_config = 2;
}

// Describe a request to fetch dashboard diagram data
message FetchDashboardDataRequest {
  // diagram ids
  repeated string diagram_ids = 1 [(validate.rules).repeated.items.string = {
    min_len: 1
    max_len: 100
  }];
  // current interval
  google.type.Interval current_period = 4 [(validate.rules).message = {required: true}];
  // previous interval
  optional google.type.Interval previous_period = 5;
  // The tenant id
  repeated uint64 tenants_ids = 6 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
  // Whether to fetch data for all tenants
  bool all_tenants = 7;
}

// Describe a response to fetch dashboard diagram data
message FetchDashboardDataResponse {
  // The dashboard diagram data
  repeated moego.models.reporting.v2.DiagramData diagram_data = 1;
}
