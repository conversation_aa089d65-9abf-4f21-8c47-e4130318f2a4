syntax = "proto3";

package moego.enterprise.enterprise.v1;

import "moego/models/enterprise/v1/enterprise_defs.proto";
import "moego/models/enterprise/v1/enterprise_enums.proto";
import "moego/models/enterprise/v1/enterprise_models.proto";
import "moego/models/enterprise/v1/option_models.proto";
import "moego/models/enterprise/v1/tenant_group_models.proto";
import "moego/models/enterprise/v1/tenant_template_defs.proto";
import "moego/models/enterprise/v1/tenant_template_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/enterprise/v1;enterpriseapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.enterprise.v1";

// GetEnterpriseParams
message GetEnterpriseParams {
  // id
  optional int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // need tenant template
  optional bool need_tenant_template = 2;
}

// GetEnterpriseResult
message GetEnterpriseResult {
  // enterprise
  models.enterprise.v1.EnterpriseModel enterprise = 1;
  // tenant template
  repeated models.enterprise.v1.TenantTemplateModel tenant_template = 2;
}

// CreateEnterpriseParams
message CreateEnterpriseParams {
  // enterprise
  models.enterprise.v1.CreateEnterpriseDef enterprise = 1;
  // tenant template
  models.enterprise.v1.CreateTenantTemplateDef tenant_template = 2;
}

// CreateEnterpriseResult
message CreateEnterpriseResult {
  // enterprise
  models.enterprise.v1.EnterpriseModel enterprise = 1;
}

// UpdateEnterpriseParams
message UpdateEnterpriseParams {
  // enterprise id
  optional int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // enterprise setting def
  optional models.enterprise.v1.UpdateEnterpriseDef enterprise = 2;
  // optional
  optional models.enterprise.v1.UpdateTenantTemplateDef tenant_template = 3;
}

// UpdateEnterpriseResult
message UpdateEnterpriseResult {
  // enterprise
  models.enterprise.v1.EnterpriseModel enterprise = 1;
  // tenant template
  optional models.enterprise.v1.TenantTemplateModel tenant_template = 2;
}

// list tenant template params
message ListTenantTemplateParams {
  // enterprise id
  optional int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // filter
  message Filter {
    // tenant id
    repeated int64 tenant_id = 1;
    // status
    repeated models.enterprise.v1.TenantTemplateModel.Status status = 2;
    // type
    repeated models.enterprise.v1.TenantTemplateModel.Type type = 3;
  }
  // filter
  optional Filter filter = 2;
}

// list tenant templates result
message ListTenantTemplateResult {
  // tenant templates
  repeated models.enterprise.v1.TenantTemplateModel tenant_templates = 1;
}

// list tenant group params
message ListTenantGroupParams {
  // enterprise id
  optional int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // filter
  message Filter {
    // tenant id
    repeated int64 tenant_id = 1;
  }
  // filter
  optional Filter filter = 2;
}

// list tenant group result
message ListTenantGroupResult {
  // tenant groups
  repeated models.enterprise.v1.TenantGroupModel tenant_groups = 1;
}

// get option params
message GetOptionParams {}

// get option result
message GetOptionResult {
  // option
  models.enterprise.v1.OptionModel option = 1;
}

// sync franchisee params
message SyncFranchiseeParams {}

// sync franchisee result
message SyncFranchiseeResult {}

// GetEnterprisePreferenceSettingParams
message GetEnterprisePreferenceSettingParams {}

// GetEnterprisePreferenceSettingResult
message GetEnterprisePreferenceSettingResult {
  // tenant text mappings
  repeated TenantTextMapping tenant_text_mappings = 1;
  // tenant text mapping
  message TenantTextMapping {
    // type
    models.enterprise.v1.TenantTextType type = 1;
    // value
    string value = 2;
  }
}

// EnterpriseService
service EnterpriseService {
  // CreateEnterprise
  rpc CreateEnterprise(CreateEnterpriseParams) returns (CreateEnterpriseResult);
  // UpdateEnterprise
  rpc UpdateEnterprise(UpdateEnterpriseParams) returns (UpdateEnterpriseResult);
  // GetEnterprise
  rpc GetEnterprise(GetEnterpriseParams) returns (GetEnterpriseResult);
  // list tenant templates
  rpc ListTenantTemplates(ListTenantTemplateParams) returns (ListTenantTemplateResult);
  // list tenant group
  rpc ListTenantGroup(ListTenantGroupParams) returns (ListTenantGroupResult);
  // GetOptions
  rpc GetOption(GetOptionParams) returns (GetOptionResult);
  // SyncFranchisee
  rpc SyncFranchisee(SyncFranchiseeParams) returns (SyncFranchiseeResult);
  // GetEnterprisePreferenceSetting
  rpc GetEnterprisePreferenceSetting(GetEnterprisePreferenceSettingParams) returns (GetEnterprisePreferenceSettingResult);
}
