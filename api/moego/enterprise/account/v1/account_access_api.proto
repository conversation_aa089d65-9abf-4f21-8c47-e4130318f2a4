syntax = "proto3";

package moego.enterprise.account.v1;

import "google/protobuf/empty.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/account/v1;accountapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.account.v1";

// account access API
service AccountAccessService {
  // Logout the current account.
  //
  // Error codes:
  // - CODE_SESSION_NOT_EXIST: the session cannot be found or has been deleted.
  rpc Logout(google.protobuf.Empty) returns (google.protobuf.Empty);
}
