syntax = "proto3";

package moego.enterprise.staff.v1;

import "moego/models/enterprise/v1/staff_defs.proto";
import "moego/models/enterprise/v1/staff_models.proto";
import "moego/models/organization/v1/staff_defs.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/staff/v1;staffapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.staff.v1";

// create staff params
message CreateStaffParams {
  // staff profile
  models.enterprise.v1.CreateStaffProfile profile = 1;
  // resources
  models.enterprise.v1.ResourceDef resources = 2;
  // send invite link params
  optional models.organization.v1.SendInviteLinkParamsDef invite_link = 3;
}

// create staff response
message CreateStaffResult {
  // staff
  models.enterprise.v1.StaffModel staff = 1;
  // resources
  models.enterprise.v1.ResourceDef resources = 2;
  // staff email
  models.organization.v1.StaffEmailDef staff_email = 3;
}

// list staff params
message ListStaffsParams {
  // pagination
  moego.utils.v2.PaginationRequest pagination = 1 [(validate.rules).message = {required: true}];
  // order by
  optional moego.utils.v2.OrderBy order_by = 2;
  // filter
  message Filter {
    // role id
    repeated int64 role_ids = 1 [(validate.rules).repeated = {
      max_items: 1000
      unique: true
    }];
    // keyword
    optional string keyword = 2;
  }
  // filter
  optional Filter filter = 3;
}

// list staff
message ListStaffsResult {
  // staff list
  repeated models.enterprise.v1.StaffModel staffs = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
  // staff email
  map<int64, models.organization.v1.StaffEmailDef> id_to_staff_emails = 3;
  // staff assign tenant nums
  map<int64, int64> staff_to_assign_tenant_nums = 4;
}

// update staff params
message UpdateStaffParams {
  // staff id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // staff profile
  optional models.enterprise.v1.UpdateStaffProfile profile = 2;
  // resources
  optional models.enterprise.v1.ResourceDef resources = 3;
  // send invite link params
  optional models.organization.v1.SendInviteLinkParamsDef invite_link = 4;
}

// update staff response
message UpdateStaffResult {
  // staff profile
  models.enterprise.v1.StaffModel staff = 1;
  // resources
  models.enterprise.v1.ResourceDef resources = 2;
  // staff email
  optional models.organization.v1.StaffEmailDef staff_email = 3;
}

// get staff
message GetStaffParams {
  // staff id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// get staff result
message GetStaffResult {
  // staff profile
  models.enterprise.v1.StaffModel staff = 1;
  // resources
  models.enterprise.v1.ResourceDef resources = 2;
  // staff email
  optional models.organization.v1.StaffEmailDef staff_email = 3;
}

// delete staff
message DeleteStaffParams {
  // staff id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// delete staff result
message DeleteStaffResult {}

// send invite link staff
message SendStaffInviteLinkParams {
  // send invite link def
  models.organization.v1.SendInviteLinkDef invite_link = 1;
}

// send invite link staff result
message SendStaffInviteLinkResult {}

// unlink staff
message UnlinkStaffParams {
  // staff id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// unlink staff result
message UnlinkStaffResult {}

// staff service
service StaffService {
  // create staff
  rpc CreateStaff(CreateStaffParams) returns (CreateStaffResult);
  // update staff
  rpc UpdateStaff(UpdateStaffParams) returns (UpdateStaffResult);
  // get staff
  rpc GetStaff(GetStaffParams) returns (GetStaffResult);
  // delete staff
  rpc DeleteStaff(DeleteStaffParams) returns (DeleteStaffResult);
  // list staff
  rpc ListStaffs(ListStaffsParams) returns (ListStaffsResult);
  // send invite link
  rpc SendStaffInviteLink(SendStaffInviteLinkParams) returns (SendStaffInviteLinkResult);
  // unlink staff
  rpc UnlinkStaff(UnlinkStaffParams) returns (UnlinkStaffResult);
}
