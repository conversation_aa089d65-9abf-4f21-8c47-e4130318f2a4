syntax = "proto3";

package moego.enterprise.configuration.v1;

import "moego/models/enterprise/v1/configuration_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/configuration/v1;configurationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.configuration.v1";

// configuration service
service ConfigurationService {
  // publish template, apply change to franchisees
  rpc PublishTemplate(PublishTemplateParams) returns (PublishTemplateResult) {}
  // list template
  rpc ListTemplate(ListTemplateParams) returns (ListTemplateResult) {}
  // list template publish record
  rpc ListTemplatePublishRecord(ListTemplatePublishRecordParams) returns (ListTemplatePublishRecordResult) {}
}

// publish template request
message PublishTemplateParams {
  // source type
  SourceType source_type = 1;
  // source_ids
  repeated int64 source_ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    max_items: 100
  }];
  // source type
  enum SourceType {
    // buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
    // template
    TEMPLATE = 0;
    // template record
    TEMPLATE_RECORD = 1;
  }
}

// publish template response
message PublishTemplateResult {}

// tab
enum Tab {
  // buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
  // Unspecified tab
  TAB_UNSPECIFIED = 0;
  // agreement
  AGREEMENT = 1;
  // intake form
  INTAKE_FORM = 2;
  // client & pet
  CLIENT_AND_PET = 3;
}

// list template request
message ListTemplateParams {
  // filter
  message Filter {
    // tab
    repeated Tab tabs = 1 [(validate.rules).repeated = {
      items: {
        enum: {
          defined_only: true
          not_in: 0
        }
      }
    }];
    // name_like, fuzzy search
    string name_like = 2 [(validate.rules).string.max_len = 200];
  }
  // filter
  Filter filter = 1;
}

// list template response
message ListTemplateResult {
  // result
  message Result {
    // tab
    Tab tab = 1;
    // template
    repeated moego.models.enterprise.v1.ConfigurationTemplate templates = 2;
  }
  // result
  repeated Result results = 1;
}

// list template publish record request, order by published time desc
message ListTemplatePublishRecordParams {
  // filter
  message Filter {
    // template id
    int64 template_id = 1 [(validate.rules).int64 = {gt: 0}];
  }
  // pagination
  optional utils.v2.PaginationRequest pagination = 1 [(validate.rules).message = {required: true}];
  // filter
  Filter filter = 2;
}

// list template publish record response
message ListTemplatePublishRecordResult {
  // template publish record
  repeated moego.models.enterprise.v1.ConfigurationTemplatePublishRecord template_publish_records = 1;
  // pagination
  utils.v2.PaginationResponse pagination = 2;
}
