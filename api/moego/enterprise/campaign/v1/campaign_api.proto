syntax = "proto3";

package moego.enterprise.campaign.v1;

import "moego/models/enterprise/v1/campaign_models.proto";
import "moego/models/enterprise/v1/tenant_models.proto";
import "moego/models/message/v1/message_template_models.proto";
import "moego/service/enterprise/v1/campaign_service.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/campaign/v1;campaignapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.campaign.v1";

// campaign service
service CampaignService {
  // Send a test email
  rpc SendTestEmail(SendTestEmailParams) returns (SendTestEmailResult) {}
  // Create a campaign template
  rpc CreateTemplate(CreateTemplateParams) returns (CreateTemplateResult) {}
  // Get a campaign template
  rpc GetTemplate(GetTemplateParams) returns (GetTemplateResult) {}
  // Update a campaign template
  rpc UpdateTemplate(UpdateTemplateParams) returns (UpdateTemplateResult) {}
  // List campaign templates
  rpc ListTemplates(ListTemplatesParams) returns (ListTemplatesResult) {}
  // Push campaign templates to tenants
  rpc PushTemplates(PushTemplatesParams) returns (PushTemplatesResult) {}
  // List template variables
  rpc ListTemplatePlaceholders(ListTemplatePlaceholdersParams) returns (ListTemplatePlaceholdersResult);
}

// SendTestEmailParams
message SendTestEmailParams {
  // email subject
  string subject = 1 [(validate.rules).string.max_len = 200];
  // email content
  string content = 2 [(validate.rules).string.max_len = 100000];
  // recipient email
  string recipient_email = 3 [(validate.rules).string.email = true];
}

// SendTestEmailResult
message SendTestEmailResult {}

// CreateTemplateParams
message CreateTemplateParams {
  // campaign template
  string name = 1;
  // description
  string description = 2;
  // type
  moego.models.enterprise.v1.Campaign.Type type = 3;
  // cover
  string cover = 4;
  // subject
  string subject = 5;
  // content
  string content = 6;
}

// CreateTemplateResult
message CreateTemplateResult {
  // campaign template
  moego.models.enterprise.v1.CampaignTemplate template = 1;
}

// GetTemplateParams
message GetTemplateParams {
  // campaign template id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// GetTemplateResult
message GetTemplateResult {
  // campaign template
  moego.models.enterprise.v1.CampaignTemplate template = 1;
}

// UpdateTemplateParams
message UpdateTemplateParams {
  // campaign template id
  int64 id = 1;
  // campaign template
  optional string name = 2;
  // description
  optional string description = 3;
  // cover
  optional string cover = 4;
  // subject
  optional string subject = 5;
  // content
  optional string content = 6;
}

// UpdateTemplateResult
message UpdateTemplateResult {
  // campaign template
  moego.models.enterprise.v1.CampaignTemplate template = 1;
}

// ListTemplatesParams
message ListTemplatesParams {
  // page
  utils.v2.PaginationRequest pagination = 1;
  // filter
  service.enterprise.v1.ListTemplatesRequest.Filter filter = 2;
}

// ListTemplatesResult
message ListTemplatesResult {
  // page
  utils.v2.PaginationResponse pagination = 1;
  // campaign templates
  repeated moego.models.enterprise.v1.CampaignTemplate templates = 2;
}

// PushTemplatesParams
message PushTemplatesParams {
  // campaign template ids
  repeated int64 ids = 1;
  // tenant ids
  repeated models.enterprise.v1.TenantObject targets = 2;
}

// PushTemplatesResult
message PushTemplatesResult {
  // success
  bool success = 1;
}

// ListTemplatePlaceholdersParams
message ListTemplatePlaceholdersParams {
  // campaign type
  models.enterprise.v1.Campaign.Type type = 1;
}

// ListTemplatePlaceholdersResult
message ListTemplatePlaceholdersResult {
  // template placeholders
  repeated moego.models.message.v1.MessageTemplatePlaceholderSimpleView placeholders = 1;
}
