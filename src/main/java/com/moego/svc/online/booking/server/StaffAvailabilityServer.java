package com.moego.svc.online.booking.server;

import com.moego.idl.models.organization.v1.AvailabilityType;
import com.moego.idl.models.organization.v1.SlotAvailabilityDay;
import com.moego.idl.models.organization.v1.StaffAvailabilityDef;
import com.moego.idl.models.organization.v1.TimeAvailabilityDay;
import com.moego.idl.service.activity_log.v1.ActivityLogServiceGrpc;
import com.moego.idl.service.online_booking.v1.GetStaffAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.GetStaffAvailabilityResponse;
import com.moego.idl.service.online_booking.v1.GetStaffAvailabilityStatusRequest;
import com.moego.idl.service.online_booking.v1.GetStaffAvailabilityStatusResponse;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.idl.service.online_booking.v1.UpdateStaffAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.UpdateStaffAvailabilityResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.online.booking.entity.StaffAvailability;
import com.moego.svc.online.booking.entity.StaffAvailabilitySlotDay;
import com.moego.svc.online.booking.entity.StaffAvailabilityTimeDay;
import com.moego.svc.online.booking.helper.BusinessHelper;
import com.moego.svc.online.booking.service.StaffAvailabilityDayHourService;
import com.moego.svc.online.booking.service.StaffAvailabilityInitService;
import com.moego.svc.online.booking.service.StaffAvailabilityService;
import com.moego.svc.online.booking.utils.OBAvailabilityDayHourUtils;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;

@GrpcService
@RequiredArgsConstructor
public class StaffAvailabilityServer extends OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceImplBase {

    private final StaffAvailabilityService staffAvailabilityService;
    private final StaffAvailabilityDayHourService staffAvailabilityDayHourService;
    private final StaffAvailabilityInitService staffAvailabilityInitService;
    private final ActivityLogServiceGrpc.ActivityLogServiceBlockingStub activityLogServiceStub;
    private final BusinessHelper businessHelper;

    @Override
    public void getStaffAvailability(
            GetStaffAvailabilityRequest request, StreamObserver<GetStaffAvailabilityResponse> responseObserver) {
        if (request.getCompanyId() == 0 || request.getBusinessId() == 0) {
            responseObserver.onNext(GetStaffAvailabilityResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        staffAvailabilityInitService.checkStaffAvailabilityWithInit(
                request.getCompanyId(), request.getBusinessId(), request.getStaffIdListList());

        Map<Long, List<SlotAvailabilityDay>> slotDayMap = Map.of();
        Map<Long, List<TimeAvailabilityDay>> timeDayMap = Map.of();
        if (!request.hasAvailabilityType() || request.getAvailabilityType() == AvailabilityType.BY_TIME) {
            timeDayMap = staffAvailabilityDayHourService.getStaffAvailabilityTimeDayDetails(
                    request.getBusinessId(), request.getStaffIdListList());
        }
        if (!request.hasAvailabilityType() || request.getAvailabilityType() == AvailabilityType.BY_SLOT) {
            slotDayMap = staffAvailabilityDayHourService.getStaffAvailabilitySlotDayDetails(
                    request.getBusinessId(), request.getStaffIdListList());
        }

        List<StaffAvailability> staffAvailabilities =
                staffAvailabilityService.getStaffAvailabilities(request.getBusinessId(), request.getStaffIdListList());

        GetStaffAvailabilityResponse.Builder respBuilder = GetStaffAvailabilityResponse.newBuilder();
        final Map<Long, List<SlotAvailabilityDay>> finalSlotDayMap = slotDayMap;
        final Map<Long, List<TimeAvailabilityDay>> finalTimeDayMap = timeDayMap;
        staffAvailabilities.forEach(staffAvailability -> {
            var staffAvailabilityBuilder = respBuilder
                    .addStaffAvailabilityListBuilder()
                    .setStaffId(staffAvailability.getStaffId())
                    .setIsAvailable(staffAvailability.getIsAvailable());

            List<SlotAvailabilityDay> slotAvailabilityDays = finalSlotDayMap.get(staffAvailability.getStaffId());
            if (slotAvailabilityDays != null) {
                staffAvailabilityBuilder.addAllSlotAvailabilityDayList(slotAvailabilityDays);
            }
            List<TimeAvailabilityDay> timeAvailabilityDays = finalTimeDayMap.get(staffAvailability.getStaffId());
            if (timeAvailabilityDays != null) {
                staffAvailabilityBuilder.addAllTimeAvailabilityDayList(timeAvailabilityDays);
            }
        });

        responseObserver.onNext(respBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateStaffAvailability(
            UpdateStaffAvailabilityRequest request, StreamObserver<UpdateStaffAvailabilityResponse> responseObserver) {
        if (request.getStaffAvailabilityListList().isEmpty()) {
            responseObserver.onNext(UpdateStaffAvailabilityResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        var staffIds = request.getStaffAvailabilityListList().stream()
                .map(StaffAvailabilityDef::getStaffId)
                .toList();

        staffAvailabilityInitService.checkStaffAvailabilityWithInit(
                request.getCompanyId(), request.getBusinessId(), staffIds);

        // 1. add staff availability or init
        List<StaffAvailability> staffAvailabilities = request.getStaffAvailabilityListList().stream()
                .map(staffAvailability -> OBAvailabilityDayHourUtils.buildStaffAvailability(
                        request.getCompanyId(), request.getBusinessId(), staffAvailability))
                .toList();
        staffAvailabilityService.batchUpdate(staffAvailabilities);

        // 2. build & upsert staff slot/time day
        Map<String, StaffAvailabilitySlotDay> availabilitySlotDayMap =
                staffAvailabilityDayHourService.getAvailabilitySlotDayMapByStaffIds(request.getBusinessId(), staffIds);
        Map<String, StaffAvailabilityTimeDay> availabilityTimeDayMap =
                staffAvailabilityDayHourService.getAvailabilityTimeDayMapByStaffIds(request.getBusinessId(), staffIds);

        request.getStaffAvailabilityListList().forEach(staffAvailability -> {
            if (!CollectionUtils.isEmpty(staffAvailability.getSlotAvailabilityDayListList())) {
                staffAvailabilityDayHourService.updateStaffAvailabilitySlotDays(
                        request.getCompanyId(), request.getBusinessId(), staffAvailability, availabilitySlotDayMap);
            }

            if (!CollectionUtils.isEmpty(staffAvailability.getTimeAvailabilityDayListList())) {
                staffAvailabilityDayHourService.updateStaffAvailabilityTimeDays(
                        request.getCompanyId(), request.getBusinessId(), staffAvailability, availabilityTimeDayMap);
            }
        });

        responseObserver.onNext(UpdateStaffAvailabilityResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void getStaffAvailabilityStatus(
            GetStaffAvailabilityStatusRequest request,
            StreamObserver<GetStaffAvailabilityStatusResponse> responseObserver) {
        if (request.getBusinessId() == 0) {
            responseObserver.onNext(GetStaffAvailabilityStatusResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        long companyId = businessHelper.mustGetCompanyId(request.getBusinessId());

        staffAvailabilityInitService.checkStaffAvailabilityWithInit(
                companyId, request.getBusinessId(), request.getStaffIdListList());

        List<StaffAvailability> staffAvailabilityList =
                staffAvailabilityService.getStaffAvailabilities(request.getBusinessId(), request.getStaffIdListList());

        var staffAvailabilityMap = staffAvailabilityList.stream()
                .collect(Collectors.toMap(StaffAvailability::getStaffId, StaffAvailability::getIsAvailable));

        GetStaffAvailabilityStatusResponse resp = GetStaffAvailabilityStatusResponse.newBuilder()
                .putAllStaffAvailability(staffAvailabilityMap)
                .build();

        responseObserver.onNext(resp);
        responseObserver.onCompleted();
    }
}
