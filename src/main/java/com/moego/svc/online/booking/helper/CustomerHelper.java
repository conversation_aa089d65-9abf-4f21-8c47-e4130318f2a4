package com.moego.svc.online.booking.helper;

import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetCustomerInfoRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/1/15
 */
@Component
@RequiredArgsConstructor
public class CustomerHelper {

    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub customerStub;

    /**
     * Get customer info by id, throw exception if not found.
     *
     * @param customerId customer id
     * @return customer info
     */
    public BusinessCustomerInfoModel mustGetCustomer(long customerId) {
        var resp = customerStub.getCustomerInfo(
                GetCustomerInfoRequest.newBuilder().setId(customerId).build());
        if (!resp.hasCustomer()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Customer not found: " + customerId);
        }
        return resp.getCustomer();
    }
}
