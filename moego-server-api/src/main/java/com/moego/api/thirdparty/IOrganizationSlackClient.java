package com.moego.api.thirdparty;

import com.moego.api.thirdparty.param.Tier2NotifySlackParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2022/7/22
 */
@FeignClient(name = "organization-slack-client", url = "https://hooks.slack.com/")
public interface IOrganizationSlackClient {
    @PostMapping("/triggers/T011CF3CMJN/7018875866515/24ba6f4084fc8dfda1ccf462bbbc95d6")
    void sendNewSignUp(@RequestBody Tier2NotifySlackParam param);

    @PostMapping("/triggers/T011CF3CMJN/7005760813522/cc190c9c5f478a02332569d6e3751a3d")
    void sendTier2Info(@RequestBody Tier2NotifySlackParam param);
}
