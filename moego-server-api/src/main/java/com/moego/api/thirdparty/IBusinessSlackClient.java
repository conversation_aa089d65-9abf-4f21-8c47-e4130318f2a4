package com.moego.api.thirdparty;

import com.moego.server.business.params.ReferralBonusClaimParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2022/7/22
 */
@FeignClient(name = "slack-client", url = "${referral.referee.slackWorkflowsBaseUrl}")
public interface IBusinessSlackClient {
    @PostMapping("${referral.referee.slackBonusClaimUrl}")
    void sendBonusClaim(@RequestBody ReferralBonusClaimParams bonusClaimParams);
}
