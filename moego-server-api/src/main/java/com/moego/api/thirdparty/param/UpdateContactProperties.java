package com.moego.api.thirdparty.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.moego.common.enums.BusinessConst;
import com.moego.common.enums.HubspotConst;
import com.moego.common.utils.StringMoegoUtil;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateContactProperties {
    private String firstname;
    private String lastname;
    // company type
    // Single location Multi locations
    // Single van Multi vans
    // Hubrid
    private String business_type;
    // company id
    private Long company_id;
    // company name
    private String company;
    // Phone Number
    private String phone;
    // country
    private String country;
    // time zone
    // DateUtil.hubspotConvertTimeZone()
    private String hs_timezone;
    // address
    private String address;
    // Website URL
    private String website;
    // How many pets do you take care of per month?
    private String how_many_pets_do_you_take_care_of_per_month_;
    // How many locations do you have?
    private String how_many_locations_do_you_have_;
    // How many vans do you have?
    private String how_many_vans_do_you_have_;
    // What did you use before?
    private String what_did_you_use_before_;
    // <How did you hear about us?>
    private String how_did_you_know_about_us_;
    private String how_did_you_hear_about_moego_;
    // role
    private String role__intercom_;
    // tier
    private String tier;

    public void setBusinessType(Byte companyType, Integer locationNum, Integer vansNum) {
        if (BusinessConst.APP_TYPE_MOBILE.equals(companyType)) {
            business_type =
                    (locationNum > 1) ? HubspotConst.BUSINESS_TYPE_MULTI_VANS : HubspotConst.BUSINESS_TYPE_SINGLE_VAN;
            return;
        } else if (BusinessConst.APP_TYPE_SALON.equals(companyType)) {
            business_type =
                    (vansNum > 1) ? HubspotConst.BUSINESS_TYPE_MULTI_SALON : HubspotConst.BUSINESS_TYPE_SINGLE_SALON;
            return;
        }
        business_type = HubspotConst.BUSINESS_TYPE_HYBRID;
    }

    public void setPetPerMonth(String petPerMonth) {
        how_many_pets_do_you_take_care_of_per_month_ =
                findHubspotValueByInput(petPerMonth, HubspotConst.ALL_PET_PER_MONTH_OPTIONS);
    }

    public void setHowManyLocations(String moegoInput) {
        how_many_locations_do_you_have_ = findHubspotValueByInput(moegoInput, HubspotConst.ALL_LOCATION_OPTIONS);
    }

    public void setHowManyVans(String moegoInput) {
        how_many_vans_do_you_have_ = findHubspotValueByInput(moegoInput, HubspotConst.ALL_VAN_OPTIONS);
    }

    public void setUseBefore(String moegoInput) {
        what_did_you_use_before_ = findHubspotValueByInput(moegoInput, HubspotConst.ALL_USE_BEFORE_OPTIONS);
    }

    public void setKnowAboutUs(String sourceFrom, String otherInput) {
        how_did_you_know_about_us_ = sourceFrom;
        if (HubspotConst.KNOW_ABOUT_US_OTHER.equals(sourceFrom)) {
            how_did_you_hear_about_moego_ = otherInput;
        }
    }

    private String findHubspotValueByInput(String input, String[] hubspotOptions) {
        var submitValue = StringMoegoUtil.compressAndLowerCase(input);
        for (String hubspotOption : hubspotOptions) {
            if (submitValue.equals(StringMoegoUtil.compressAndLowerCase(hubspotOption))) {
                return hubspotOption;
            }
        }
        return null;
    }
}
