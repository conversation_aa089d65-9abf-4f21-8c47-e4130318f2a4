package com.moego.api.thirdparty;

import com.moego.api.thirdparty.dto.AbcKamCameraListDto;
import com.moego.api.thirdparty.param.AbcKamLoginParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "abckam-client", url = "https://abckam.com/moegopet/api/", contextId = "IAbckamClient")
public interface IAbckamClient {
    @PostMapping(value = "/post/login.php")
    String login(@RequestBody AbcKamLoginParam loginParam);

    @GetMapping(value = "/post/moegopetid.php")
    AbcKamCameraListDto queryCameraList(@RequestParam String id, @RequestHeader("Authorization") String authorization);
}
