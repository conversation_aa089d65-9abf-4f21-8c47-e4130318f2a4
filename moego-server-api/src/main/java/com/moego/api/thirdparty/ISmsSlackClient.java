package com.moego.api.thirdparty;

import com.moego.server.message.params.SlackTextParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "message-slack-client", url = "${slack.workflows.baseUrl}", contextId = "ISmsSlackClient")
public interface ISmsSlackClient {
    @PostMapping("${slack.workflows.eventPublisher}")
    void notifyThreadTwilioA2pEvent(@RequestBody SlackTextParam param);
}
