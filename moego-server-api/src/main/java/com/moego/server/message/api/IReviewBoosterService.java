package com.moego.server.message.api;

import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.server.message.dto.ListReviewBoosterRecordsDTO;
import com.moego.server.message.dto.ReviewBoosterRecordDTO;
import com.moego.server.message.dto.ReviewBoosterSummaryDTO;
import com.moego.server.message.params.ListReviewBoosterRecordsParams;
import com.moego.server.message.params.ReviewBoosterSummaryParams;
import java.util.List;
import java.util.Set;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IReviewBoosterService {
    /**
     * TODO(account structure): company 迁移后，查询 company 维度的数据
     *
     * List customer id by filter review booster
     *
     * @param params filter params
     * @return customer id set
     */
    @PostMapping("/service/message/review/booster/filter")
    Set<Integer> listCustomerIdByFilter(@RequestBody ClientsFilterDTO params);

    /**
     * Get review booster records by appointment id
     *
     * @param businessId    business id
     * @param customerId    customer id
     * @param appointmentId appointment id
     * @return review booster record
     */
    @PostMapping("/service/message/review/booster/getReviewBoosterListByAppointmentId")
    List<ReviewBoosterRecordDTO> getReviewBoosterListByAppointmentId(
            @RequestParam Integer businessId, @RequestParam Integer customerId, @RequestParam Integer appointmentId);

    /**
     * Create review booster record by appointment
     *
     * @param dto review booster record
     * @return
     */
    @PostMapping("/service/message/review/booster/createReviewBooster")
    Boolean createReviewBooster(@RequestBody ReviewBoosterRecordDTO dto);

    /**
     * Get review booster summary
     */
    @PostMapping("/service/message/review/booster/reviewBoosterSummary")
    ReviewBoosterSummaryDTO reviewBoosterSummary(@RequestBody ReviewBoosterSummaryParams params);

    @PostMapping("/service/message/review/record/list")
    ListReviewBoosterRecordsDTO listReviewBoosterRecords(@RequestBody ListReviewBoosterRecordsParams params);
}
