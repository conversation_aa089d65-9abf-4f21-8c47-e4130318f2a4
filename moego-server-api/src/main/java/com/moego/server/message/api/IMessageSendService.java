package com.moego.server.message.api;

import com.moego.server.message.dto.MessageDetailDTO;
import com.moego.server.message.enums.MessageMethodTypeEnum;
import com.moego.server.message.params.SendMessageParams;
import com.moego.server.message.params.SendMessagesParams;
import com.moego.server.message.params.SendServiceMessageParams;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Validated
public interface IMessageSendService {

    /**
     * 发送auto message
     * 类似接口： /message/send/services/message
     *
     * @param params
     * @return
     */
    @PostMapping("/service/message/autoMessage/send")
    String sendAutoMessage(@RequestBody @Validated SendServiceMessageParams params);

    /*
     * Send ready for pickup message
     * 返回值表示错误原因，空字符串表示成功
     */
    @PostMapping("/service/message/pickup/send")
    public String sendReadyForPickupMessageV2(
            @RequestParam(value = "businessId", required = false) Integer businessId,
            @RequestParam(value = "staffId", required = false) Integer staffId,
            @RequestParam(value = "customerId", required = false) Integer customerId,
            @RequestParam(value = "groomingId", required = false) Integer groomingId,
            @RequestParam(value = "messageMethod", required = false) MessageMethodTypeEnum messageMethod);

    /**
     * send message to customer
     */
    @PostMapping("/service/message/send/sendMessageToCustomer")
    String sendMessageToCustomer(@RequestBody SendMessageParams params);

    /**
     * send message to customer
     */
    @PostMapping("/service/message/send/sendMessageToCustomerV2")
    MessageDetailDTO sendMessageToCustomerV2(@RequestBody @Valid SendMessageParams params);

    /**
     * Send a message from business to a specific phone number.
     *
     * @param param {@link SendParam}
     * @return true if success
     */
    @PostMapping("/service/message/send")
    boolean send(@RequestBody @Valid SendParam param);

    /**
     * @param businessId business id
     * @param to         to phone number, country code is optional,
     *                   if not provided, will use business's country code
     * @param content    message content
     */
    @Builder(toBuilder = true)
    record SendParam(@NotNull Integer businessId, @NotBlank String to, @NotNull String content) {}

    /**
     * send service message to customer
     */
    @PostMapping("/service/message/send/sendServicesMessageToCustomer")
    String sendServicesMessageToCustomer(@RequestBody SendMessagesParams params);

    /**
     * send service message to customer with result
     */
    @PostMapping("/service/message/send/sendServicesMessageToCustomerWithResult")
    MessageDetailDTO sendServicesMessageToCustomerWithResult(@RequestBody SendMessagesParams params);

    /**
     * Send notification c-app msg to b-web and b-app.
     *
     * @see <a href="https://moego.atlassian.net/browse/IFRBE-133">IFRBE-133</a>
     */
    @PostMapping("/service/message/send/sendNotificationToBusiness")
    void sendNotificationToBusiness(@RequestBody SendToBusinessParam param);

    /**
     * Send read notification c-app msg to b-web and b-app.
     *
     * @see <a href="https://moego.atlassian.net/browse/IFRBE-155">IFRBE-155</a>
     */
    @PostMapping("/service/message/send/sendReadNotificationToBusiness")
    void sendReadNotificationToBusiness(@RequestBody SendToBusinessParam param);

    /**
     * @param customerId company Id
     * @param businessId business id
     * @param customerId customerId
     * @param messageId  message id
     */
    @Builder(toBuilder = true)
    record SendToBusinessParam(
            Integer companyId,
            @NotNull Integer businessId,
            @NotBlank Integer customerId,
            @NotNull Integer messageId,
            String messageUUID) {}
}
