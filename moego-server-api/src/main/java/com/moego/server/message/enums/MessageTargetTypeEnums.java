package com.moego.server.message.enums;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020-09-07 01:12
 */
public enum MessageTargetTypeEnums {
    //    消息触发类型：1:thread_id,2:batch_id
    TARGET_TYPE_THREAD(1, "thread_id", "", true),
    TARGET_TYPE_BATCH(2, "batch_id", "", true),
    TARGET_TYPE_AUTO_PICKUP(20, "pick_up", "", true),
    TARGET_TYPE_AUTO_AGREEMENT(30, "agreement", "", true),
    TARGET_TYPE_AGREEMENT(31, "agreement", "", true),
    TARGET_TYPE_REVIEW(40, "review", "", true),
    TARGET_TYPE_REVIEW_REPLY(41, "review booster reply", "", true),
    REMINDER_APPOINTMENT_FIRST(501, "review booster appointment", "", true),
    REMINDER_APPOINTMENT_SECOND(502, "review booster appointment", "", true),
    REMINDER_APPOINTMENT_REMIND(503, "review booster appointment", "", true),
    REMINDER_PET_BIRTHDAY(51, "review booster birthday", "", true),
    REMINDER_REBOOK(52, "review booster rebook", "", true),
    AUTO_APPOINTMENT_BOOK(60, "auto appointment book", "", true),
    AUTO_APPOINTMENT_RESCHEDULED(61, "auto appointment rebook", "", true),
    AUTO_APPOINTMENT_CANCELLED(62, "auto appointment rebook", "", true),
    AUTO_APPOINTMENT_CONFIRMED_BY_CLIENT(63, "auto appointment is confirmed by client", "", true),
    AUTO_APPOINTMENT_CANCELLED_BY_CLIENT(64, "auto appointment is cancelled by client", "", true),
    AUTO_RECEIPT(65, "auto receipt", "", true),
    PAY_ONLINE(66, "pay online message", "", true),
    COF_LINK(67, "unsubmitted cof link reminder message", "", true),
    AUTO_APPOINTMENT_MOVED_TO_WAIT_LIST(68, "auto appointment moved to wait list", "", true),
    FORGET_PASSWORD(100, "Forget password", "", true),
    BUSINESS_DAILY(101, "business daily", "", true),
    VERIFICATION_CODE(102, "verification code", "{businessName} verification code: {verifyCode}", true),
    OB_BUSINESS_EMAIL(103, "ob send to business email", "", true),
    OB_CLIENT_EMAIL(104, "ob send to client email", "", true),
    OB_CLIENT_MESSAGE(105, "ob send to client message", "", true),
    CALENDAR_REMINDER(150, "mobile calendar push reminder", "", true),
    GROOMING_REPORT(151, "grooming report", "", true),
    ABANDONED_SCHEDULE_MESSAGE(152, "abandoned schedule message", "", true),
    DAILY_REPORT(153, "daily report message", "", true),
    ENGAGEMENT_VOICE_AUTO_REPLY(600, "engagement voice auto reply", "", true),
    WORKFLOW(700, "workflow", "", true),
    OPTOUT_REPLY(800, "optout reply", "", true);

    private Integer value;
    private String desc;
    private String emailTemplate;
    private boolean sendBeforeGetSetCommonInfo;

    MessageTargetTypeEnums(Integer value, String desc, String emailTemplate, boolean sendBeforeGetSetCommonInfo) {
        this.value = value;
        this.desc = desc;
        this.emailTemplate = emailTemplate;
        this.sendBeforeGetSetCommonInfo = sendBeforeGetSetCommonInfo;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public String getEmailTemplate() {
        return emailTemplate;
    }

    public boolean isSendBeforeGetSetCommonInfo() {
        return sendBeforeGetSetCommonInfo;
    }

    public static MessageTargetTypeEnums getMessageTargetTypeEnumByValue(Integer value) {
        for (MessageTargetTypeEnums valueEnum : values()) {
            if (valueEnum.getValue().equals(value)) {
                return valueEnum;
            }
        }
        return TARGET_TYPE_THREAD;
    }

    /**
     * 是否是first、second、third appointment reminder
     *
     * @param targetType
     * @return
     */
    public static Boolean isAppointmentReminder(Integer targetType) {
        return (Objects.equals(MessageTargetTypeEnums.REMINDER_APPOINTMENT_FIRST.getValue(), targetType)
                || Objects.equals(MessageTargetTypeEnums.REMINDER_APPOINTMENT_SECOND.getValue(), targetType)
                || Objects.equals(MessageTargetTypeEnums.REMINDER_APPOINTMENT_REMIND.getValue(), targetType));
    }

    /**
     * need to notification app client
     *
     * @param targetType message target type
     * @return need notification
     */
    public static boolean needNotificationAppClient(Integer targetType) {
        return Objects.equals(targetType, MessageTargetTypeEnums.AUTO_APPOINTMENT_BOOK.getValue())
                || Objects.equals(targetType, MessageTargetTypeEnums.AUTO_APPOINTMENT_RESCHEDULED.getValue())
                || Objects.equals(targetType, MessageTargetTypeEnums.AUTO_APPOINTMENT_CANCELLED.getValue());
    }

    private static final Map<AutoMessageTemplateEnum, MessageTargetTypeEnums> AUTO_TARGET_MAP = Map.of(
            AutoMessageTemplateEnum.APPOINTMENT_BOOK, MessageTargetTypeEnums.AUTO_APPOINTMENT_BOOK,
            AutoMessageTemplateEnum.APPOINTMENT_RESCHEDULED, MessageTargetTypeEnums.AUTO_APPOINTMENT_RESCHEDULED,
            AutoMessageTemplateEnum.APPOINTMENT_CANCELLED, MessageTargetTypeEnums.AUTO_APPOINTMENT_CANCELLED,
            AutoMessageTemplateEnum.READY_PICK_UP, MessageTargetTypeEnums.TARGET_TYPE_AUTO_PICKUP,
            AutoMessageTemplateEnum.APPOINTMENT_CONFIRMED_BY_CLIENT,
                    MessageTargetTypeEnums.AUTO_APPOINTMENT_CONFIRMED_BY_CLIENT,
            AutoMessageTemplateEnum.APPOINTMENT_CANCELLED_BY_CLIENT,
                    MessageTargetTypeEnums.AUTO_APPOINTMENT_CANCELLED_BY_CLIENT,
            AutoMessageTemplateEnum.RECEIPT, MessageTargetTypeEnums.AUTO_RECEIPT,
            AutoMessageTemplateEnum.PAY_ONLINE, MessageTargetTypeEnums.PAY_ONLINE,
            AutoMessageTemplateEnum.APPOINTMENT_MOVED_TO_WAIT_LIST,
                    MessageTargetTypeEnums.AUTO_APPOINTMENT_MOVED_TO_WAIT_LIST);

    public static MessageTargetTypeEnums toTargetType(AutoMessageTemplateEnum autoMessage) {
        return AUTO_TARGET_MAP.get(autoMessage);
    }
}
