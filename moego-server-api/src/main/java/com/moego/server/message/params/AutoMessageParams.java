package com.moego.server.message.params;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/9/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutoMessageParams {

    private Integer businessId;

    @NotNull
    private Integer targetType;

    @NotNull
    private Integer targetId;
}
