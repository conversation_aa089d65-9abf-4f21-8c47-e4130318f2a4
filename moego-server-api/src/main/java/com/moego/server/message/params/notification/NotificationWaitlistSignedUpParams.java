package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraWaitlistSignedUpDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationWaitlistSignedUpParams extends NotificationParams {
    private String title = "New waitlist signed up";
    private String type = NotificationEnum.TYPE_ACTIVITY_WAITLIST_SIGNED_UP;
    private NotificationExtraWaitlistSignedUpDto webPushDto;
}
