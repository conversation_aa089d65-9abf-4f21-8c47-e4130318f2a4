package com.moego.server.message.params;

import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
public class BatchAutoTemplateSendParams {
    private Integer businessId;

    private List<AppointmentCustomer> appointmentCustomers;

    //    1 book 2 rescheduled, 3 cancel
    private Integer autoTemplateType;

    @Data
    @Builder
    public static class AppointmentCustomer {
        private Integer appointmentId;
        private Integer customerId;
    }
}
