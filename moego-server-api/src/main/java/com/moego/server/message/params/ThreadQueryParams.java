package com.moego.server.message.params;

import com.moego.common.params.PageParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2020-06-27 20:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ThreadQueryParams extends PageParams {

    private Integer businessId;
    private Integer staffId;
    private Integer queryType;
    private String keyword;
    // 用逗号链接的 TARGET_TYPE_XXX，空表示不筛选。
    private String targetTypeList;

    public static final Integer QUERY_TYPE_OPEN = 1;
    public static final Integer QUERY_TYPE_CLOSE = 2;
    public static final Integer QUERY_TYPE_STARS = 3;
    public static final Integer QUERY_TYPE_UNREAD = 4;

    public static final Integer TARGET_TYPE_CONVERSATION = 1;
    public static final Integer TARGET_TYPE_REVIEW = 2;
    public static final Integer TARGET_TYPE_APPOINTMENT_CONFIRMATION = 3;
}
