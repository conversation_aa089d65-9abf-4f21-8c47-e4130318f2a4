package com.moego.server.message.enums;

public enum MessageDetailEnum {
    MESSAGE_SYSTEM_STAFF_ID(0, "system default staffId"),
    //    '1-msg，2-email 4-call, 5-app',
    MESSAGE_METHOD_MSG(1, "msg"),
    MESSAGE_METHOD_EMAIL(2, "email"),
    MESSAGE_METHOD_CALL(4, "call"),
    MESSAGE_METHOD_APP(5, "app"),
    //    类型：1-商户发送，2-客户发送
    SEND_BY_BUSINESS(1, "商户发送"),
    SEND_BY_CUSTOMER(2, "客户发送"),
    //    类型：1-text，2-pic
    MESSAGE_TYPE_TEXT(1, "text"),
    MESSAGE_TYPE_PIC(2, "pic"),

    //    1-auto msg/email/call，2-customer email 3-employee send
    MESSAGE_SOURCE_AUTO(1, "auto msg/email/call"),
    MESSAGE_SOURCE_CUTOMER_EMAIL(2, "customer email "),
    MESSAGE_SOURCE_EMPLOYEE(3, "employee send"),
    MESSAGE_SOURCE_MESSAGE_CENTER(4, "message center send"),
    MESSAGE_SOURCE_CODE(5, "verification code"),
    MESSAGE_SOURCE_VOICE_REPLY(6, "voice auto reply"),
    MESSAGE_SOURCE_AI_ASSISTANT_REPLY(7, "ai assistant reply"),
    MESSAGE_SOURCE_WORKFLOW(8, "workflow"),
    MESSAGE_SOURCE_OPTOUT(9, "optout"),

    //    状态：1-正常，2-已删除
    MESSAGE_STATUS_NORMAL(1, "正常"),
    MESSAGE_STATUS_DELETE(2, "已删除"),
    //    1-成功，2-失败
    MESSAGE_SEND_STATUS_SUCCESS(1, "成功"),
    MESSAGE_SEND_STATUS_FAIL(2, "失败"),
    MESSAGE_SEND_STATUS_PENDING(3, "pending"),
    //    是否已读：1-已读，2-未读
    MESSAGE_READ_STATUS_READED(1, "已读"),
    MESSAGE_READ_STATUS_UNREAD(2, "未读"),
    //    customer是否已读：1-已读，2-未读
    MESSAGE_CUSTOMER_READ(1, "已读"),
    MESSAGE_CUSTOMER_UNREAD(2, "未读"),
    //    态：0-正常，1-customer删除，2-business删除
    MESSAGE_DELETE_BY_CUSTOMER(1, "customer删除"),
    MESSAGE_DELETE_BY_BUSINESS(2, "business删除");

    private Integer value;
    private String desc;

    private MessageDetailEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    // Static method to find value by description
    public static Integer getValueByDesc(String desc) {
        for (MessageDetailEnum item : MessageDetailEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getValue();
            }
        }
        return null;
    }

    // Static method to find value by name
    public static Integer getValueByName(String name) {
        for (MessageDetailEnum item : MessageDetailEnum.values()) {
            if (item.name().equals(name)) {
                return item.getValue();
            }
        }
        return null;
    }

    // Static method to find method name by value
    private static final String methodPrefixName = "MESSAGE_METHOD";

    public static String getMethodNameByValue(Integer value) {
        for (MessageDetailEnum item : MessageDetailEnum.values()) {
            if (!item.name().startsWith(methodPrefixName)) {
                continue;
            }
            if (item.getValue().equals(value)) {
                return item.name();
            }
        }
        return null;
    }

    // Static method to find source name by value
    private static final String sourcePrefixName = "MESSAGE_SOURCE";

    public static String getSourceNameByValue(Integer value) {
        for (MessageDetailEnum item : MessageDetailEnum.values()) {
            if (!item.name().startsWith(sourcePrefixName)) {
                continue;
            }
            if (item.getValue().equals(value)) {
                return item.name();
            }
        }
        return null;
    }
}
