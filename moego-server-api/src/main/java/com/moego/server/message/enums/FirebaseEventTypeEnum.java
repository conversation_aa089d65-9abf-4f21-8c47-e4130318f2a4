package com.moego.server.message.enums;

/**
 * <AUTHOR>
 * @date 2020-07-12 15:10
 */
public enum FirebaseEventTypeEnum {
    MESSAGE_SEND_SUCCESS("message_send_success"),
    MESSAGE_SEND_FAIL("message_send_fail"),
    MESSAGE_READ("message_read"),
    MESSAGE_DELETE("message_delete");

    private String eventType;

    private FirebaseEventTypeEnum(String eventType) {
        this.eventType = eventType;
    }

    public String getEventType() {
        return eventType;
    }
}
