package com.moego.server.message.api;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface IVerifyService {
    /**
     * 校验某个 business 的 某个 customer 登录时的手机短信验证码，验证通过返回 customerId，不通过则报错
     * @param businessId
     * @param phoneNumber customer 登录填写的手机号
     * @param code customer 输入的验证码
     * @return customerId
     */
    @PostMapping("/service/message/verify/business/customer/phone/code")
    Boolean verifyPhoneCode(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("customerId") Integer customerId,
            @RequestParam("phoneNumber") String phoneNumber,
            @RequestParam("code") String code);
}
