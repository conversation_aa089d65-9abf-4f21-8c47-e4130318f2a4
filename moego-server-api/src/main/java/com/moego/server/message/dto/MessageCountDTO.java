package com.moego.server.message.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageCountDTO {

    private Integer companyId;
    private Integer businessId;

    private Integer totalMessage;
    private Integer totalMessageUsed;
    private Integer usedAutoMessage;
    private Integer used2wayMessage;
    private Integer usedCall;
    private Boolean isUnlimited;
}
