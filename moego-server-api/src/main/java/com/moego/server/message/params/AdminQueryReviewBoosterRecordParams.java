package com.moego.server.message.params;

import com.moego.common.utils.Pagination;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class AdminQueryReviewBoosterRecordParams {

    @NotNull
    private Integer businessId;

    private Integer customerId;

    @NotNull
    @Valid
    private Pagination pagination;
}
