package com.moego.server.message.params;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-08-09 15:15
 */
@Data
public class MessageReminderParams {

    private Integer id;
    private Integer businessId;
    private Integer reminderType;
    private String reminderBody;
    private Integer beforeDay;
    private Integer atHour;

    @Schema(description = "when to send after a specified event")
    private Integer afterMinute;
}
