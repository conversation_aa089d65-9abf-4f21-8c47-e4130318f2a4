package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraOBReqestDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationOBRequestParams extends NotificationParams {

    private String title = "New booking requested";
    private String type = NotificationEnum.TYPE_ACTIVITY_ONLINE_BOOKING_REQUEST;
    private Boolean isNotifyBusinessAllStaff = true;
    private NotificationExtraOBReqestDto webPushDto;
    private String mobilePushTitle = "Online booking request";
    private String mobilePushBody = "{customerFullName} {Date}{Time} with {staffFirstName}";
    private Boolean isAppointmentRelated = true;
}
