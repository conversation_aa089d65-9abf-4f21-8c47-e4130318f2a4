package com.moego.server.message.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MessageDetailDTO {

    private Integer id;
    private Integer targetType;
    private Integer targetId;
    private String messageSid;
    private Integer businessId;
    private Long companyId;
    private Integer staffId;
    private Integer customerId;
    private String phoneNumber;
    private String messageText;
    private String mediaUrl1;
    private Integer type;
    private Integer method;
    private Integer isRead;
    private Integer customerIsRead;
    private Integer readTime;
    private Integer status;
    private Integer createTime;
    private Integer deleteTime;
    private Integer unconfirmApptid;
    private Integer isSuccessed;
    private Integer errorCode;
    private String errorMessage;
    private Integer source;
    private String contactName;
    private Integer messageType;
    private Integer deleteBy;
    private Integer numSegments;
    private String reversePhone;
    private String mimeType;
}
