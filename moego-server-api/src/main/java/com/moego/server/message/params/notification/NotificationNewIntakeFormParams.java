package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraNewIntakeFormDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationNewIntakeFormParams extends NotificationParams {

    private String title = "New Intake form submitted";
    private String type = NotificationEnum.TYPE_ACTIVITY_NEW_INTAKE_FORM;
    private Boolean isNotifyBusinessAllStaff = true;
    private NotificationExtraNewIntakeFormDto webPushDto;
    private String mobilePushTitle = "New intake form submitted";
    private String mobilePushBody = "{customerFullName} - {intakeFormName}";
}
