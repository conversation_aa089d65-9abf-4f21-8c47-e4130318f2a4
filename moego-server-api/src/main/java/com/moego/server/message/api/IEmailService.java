package com.moego.server.message.api;

import com.microtripit.mandrillapp.lutung.view.MandrillMessageStatus;
import com.moego.common.response.ResponseResult;
import com.moego.server.message.params.MailSendParams;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2020-06-16 21:53
 */
public interface IEmailService {
    @PostMapping("/service/message/email/send")
    ResponseResult<MandrillMessageStatus[]> sendEmail(@RequestBody MailSendParams mailSendParams);
}
