package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraA2pRequiredDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SystemA2pRequiredParams extends NotificationParams {

    private String title = "Action required to finish A2P 10DLC Registration";
    private String type = NotificationEnum.TYPE_SYSTEM_A2P_REGISTER_REQUIRED;
    private NotificationExtraA2pRequiredDto webPushDto;
    private Boolean isNotifyBusinessOwner = true;
    private String mobilePushTitle = "Action required to finish A2P 10DLC Registration";
    private String mobilePushBody = "To prevent messages from being filtered, please register your business number.";
}
