package com.moego.server.message.api;

import com.moego.server.message.dto.MoeTwilioNumberDTO;
import com.moego.server.message.dto.VerificationCodeDTO;
import com.moego.server.message.enums.VerificationCodeScenarioEnum;
import com.moego.server.message.params.VerificationCodeParams;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IMoeGoVerificationCodeService {
    /**
     * MoeGo 平台给指定手机发送验证码
     * @param scenario 发送场景
     * @param phone 接收的手机号, E164 格式
     * @return 如果发送成功则返回本次请求的 token 作为核销凭证, 发送失败则抛异常
     */
    @PostMapping("/service/message/customer/account/phone/code")
    String sendClientAccountPhoneCode(
            @RequestParam("scenario") VerificationCodeScenarioEnum scenario, @RequestParam("phone") String phone);

    /**
     * MoeGo 平台给指定邮箱发送验证码
     * @param scenario 发送场景
     * @param email 接收的邮箱
     * @return 如果发送成功则返回本次请求的 token 作为核销凭证, 发送失败则抛异常
     */
    @PostMapping("/service/message/customer/account/email/code")
    String sendClientAccountEmailCode(
            @RequestParam("scenario") VerificationCodeScenarioEnum scenario, @RequestParam("email") String email);

    /**
     * 校验 MoeGo 平台发送的验证码
     * @param scenario 发送场景
     * @param account 请求验证码的账号标识 (邮箱或手机号)
     * @param code 输入的验证码
     * @param token 本次验证码对应的请求的 token
     * @param renewal 是否需要延长有效期, 为 true 时, 如果校验成功会刷新有效期; 为 false 时, 如果校验成功会删除验证码
     * @return true: 校验成功, false: 校验失败 (可能是验证码错误/服务器错误等)
     */
    @DeleteMapping("/service/message/customer/account/code")
    boolean verifyCode(
            @RequestParam("scenario") VerificationCodeScenarioEnum scenario,
            @RequestParam("account") String account,
            @RequestParam("code") String code,
            @RequestParam("token") String token,
            @RequestParam("renewal") Boolean renewal);

    /**
     * send verification code to phone or email
     *
     * @param params phone number or email params
     * @return sent result
     */
    @PostMapping("/service/message/client-portal/sendVerificationCode")
    VerificationCodeDTO sendVerificationCode(@RequestBody VerificationCodeParams params);

    /**
     * 随机获取一个 MoeGo 电话号码
     * @param countryCode 国家代码
     * @return MoeGo 电话号码
     */
    @PostMapping("/service/message/client-portal/getMoeGoPhoneNumberRandomly")
    MoeTwilioNumberDTO getMoeGoPhoneNumberRandomly(@RequestParam("countryCode") String countryCode);
}
