package com.moego.server.message.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class DailyReportSendEmailParams {

    @Schema(description = "需要发送的 reportId")
    private Long id;

    private Long companyId;

    private Long businessId;

    private Long staffId;

    @Schema(description = "send email list")
    private List<String> recipientEmailList;
}
