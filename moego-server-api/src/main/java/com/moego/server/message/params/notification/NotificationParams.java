package com.moego.server.message.params.notification;

import com.moego.server.business.dto.StaffInfoWithNotificationDto;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class NotificationParams {

    private String title = "";
    private String body = "";
    private String type = "";
    private Object webPushDto;

    // mobile推送需要的参数
    private Boolean isSendMobilePush = true;
    private String mobilePushTitle = "";
    private String mobilePushBody = "";
    private Map<String, Object> mobilePushData;

    // business_id
    private Integer businessId = 0;
    // 操作的tokenAccountId
    private Integer accountId = 0;
    // 操作的tokenStaffId
    private Integer tokenStaffId = 0;

    // 各个type本应该发送的staffId列表(相关联的staffIdList)
    private Set<Integer> staffIdList;
    // 是否单独通知owner
    private Boolean isNotifyBusinessOwner = false;
    // 是否通知所有staff(包含owner)
    private Boolean isNotifyBusinessAllStaff = false;
    // 是否跟预约有关
    private Boolean isAppointmentRelated = false;

    // 最终计算后需要发送的staffIdList
    private List<Integer> needSendStaffIdList;
    private Map<Integer, StaffInfoWithNotificationDto> staffInfoMap;
    // 在变量内记录staffId和将要发送的notificationId
    private Map<Integer, Integer> staffIdNotificationIdMap;
}
