package com.moego.server.message.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class ReviewBoosterForGroomingReportParams {

    @Schema(description = "当前 report id")
    @NotBlank
    private String reportUuid;

    @Schema(description = "review 分数，必填：1-5")
    @NotNull
    @Max(5)
    @Min(1)
    private Integer score;

    @Schema(description = "review 填写内容，可以不填")
    @Size(max = 1000)
    private String reviewContent;
}
