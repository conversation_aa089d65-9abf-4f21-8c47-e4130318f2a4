package com.moego.server.message.dto;

import java.util.Date;
import lombok.Builder;

@Builder(toBuilder = true)
public record NotificationCampaignRecordDTO(
        Long campaignId,
        String starter,
        Date startedAt,
        Long notificationId,
        String notificationTitle,
        String notificationBody,
        Long notificationRecordId,
        Long receiverAccountId,
        Long receiverBusinessId,
        Long receiverCompanyId,
        Long receiverStaffId,
        String receiverName) {}
