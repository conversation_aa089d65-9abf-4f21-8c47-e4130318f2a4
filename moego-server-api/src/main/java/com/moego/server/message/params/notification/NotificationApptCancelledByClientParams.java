package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraApptCommonDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationApptCancelledByClientParams extends NotificationParams {

    private String title = "Appointment cancelled by client";
    private String type = NotificationEnum.TYPE_ACTIVITY_APPT_CANCELLED_BY_CLIENT;
    private NotificationExtraApptCommonDto webPushDto;
    private String mobilePushTitle = "Appointment cancelled by client";
    private String mobilePushBody = "{customerFullName} {Date}{Time} with {staffFirstName}";
    private Boolean isAppointmentRelated = true;
}
