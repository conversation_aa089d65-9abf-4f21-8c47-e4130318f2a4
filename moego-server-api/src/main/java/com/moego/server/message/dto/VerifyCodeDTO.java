package com.moego.server.message.dto;

import com.moego.server.message.enums.VerificationCodeScenarioEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/9/4
 */
@Data
@Accessors(chain = true)
public class VerifyCodeDTO {
    private VerificationCodeScenarioEnum scenario;
    private Integer businessId;
    private Integer customerId;
    private String account;
    private String code;
    private String token;
}
