package com.moego.server.message.params;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-06-27 21:02
 */
@Data
public class ThreadUpdateParams {

    private Integer businessId;
    private Integer staffId;

    @NotEmpty
    private List<Integer> ids;

    @NotNull
    private Integer updateType;

    public static final Integer UPDATE_TYPE_OPEN = 1;
    public static final Integer UPDATE_TYPE_CLOSE = 2;
    public static final Integer UPDATE_TYPE_STARS = 3;
    public static final Integer UPDATE_TYPE_UNREAD = 4;
    public static final Integer UPDATE_TYPE_BLOCK = 5;
    public static final Integer UPDATE_TYPE_REMOVE = 6;
    public static final Integer UPDATE_TYPE_REMOVE_START = 7;
    public static final Integer UPDATE_TYPE_READ = 8;
}
