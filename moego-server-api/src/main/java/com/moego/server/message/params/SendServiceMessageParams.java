package com.moego.server.message.params;

import com.moego.server.message.enums.AutoMessageTemplateEnum;
import com.moego.server.message.enums.MessageMethodTypeEnum;
import com.moego.server.message.enums.MessageTargetTypeEnums;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/9/18
 */
@Data
@Builder
public class SendServiceMessageParams {
    private Long companyId; // 应传尽传

    @NotNull
    private Integer businessId;

    private Integer staffId;
    private Integer customerId;
    private Integer groomingId;

    @NotNull
    private Integer targetId;

    @NotNull
    private AutoMessageTemplateEnum autoMessageTemplate;

    @NotNull
    private MessageTargetTypeEnums targetTypeEnums;

    private MessageMethodTypeEnum messageMethod;
}
