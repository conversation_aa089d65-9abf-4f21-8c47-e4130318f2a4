package com.moego.server.message.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "ArrivalWindowSettingDto")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RepeatExpitySettingDto {

    @NotNull
    @Schema(description = "过期前预约数量，0时不做过期查询")
    private Integer expiryApptNum;
}
