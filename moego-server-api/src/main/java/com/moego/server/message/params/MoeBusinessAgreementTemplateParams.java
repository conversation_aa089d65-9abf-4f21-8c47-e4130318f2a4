package com.moego.server.message.params;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-09-09 00:54
 */
@Data
public class MoeBusinessAgreementTemplateParams {

    private Integer id;
    private Integer businessId;
    private Integer agreementId;
    private String emailTitle;
    private String emailBody;
    private String smsBody;
    private Integer byStaffId;

    @Override
    public String toString() {
        return ("{" + "\"id\":"
                + id
                + ",\"businessId\":"
                + businessId
                + ",\"agreementId\":"
                + agreementId
                + ",\"emailTitle\":\""
                + emailTitle
                + '\"'
                + ",\"emailBody\":\""
                + emailBody
                + '\"'
                + ",\"smsBody\":\""
                + smsBody
                + '\"'
                + ",\"byStaffId\":"
                + byStaffId
                + "}");
    }
}
