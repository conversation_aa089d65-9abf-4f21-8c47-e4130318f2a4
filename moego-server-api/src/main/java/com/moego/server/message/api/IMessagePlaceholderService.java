package com.moego.server.message.api;

import com.moego.server.message.params.MessagePlaceholderParams;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface IMessagePlaceholderService {

    @PostMapping("/service/message/placeholder/process")
    String process(@RequestBody @Valid MessagePlaceholderParams params);
}
