package com.moego.server.message.params.notification.ob;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraOBReqestDto;
import com.moego.common.enums.ClientApptConst;
import com.moego.server.message.params.notification.NotificationParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class NotificationOBRequestReceivedParams extends NotificationParams {

    private String title = ClientApptConst.OBRequestNotificationTitle.RECEIVED_BY_CLIENT;
    private String type = NotificationEnum.TYPE_ACTIVITY_ONLINE_BOOKING_REQUEST;
    private Boolean isNotifyBusinessAllStaff = true;
    private NotificationExtraOBReqestDto webPushDto;
    private String mobilePushTitle = ClientApptConst.OBRequestNotificationTitle.RECEIVED_BY_CLIENT;
    private String mobilePushBody = "{customerFullName} {Date}{Time} with {staffFirstName}";
    private String mobilePushBodyWithoutStaff = "{customerFullName} {Date}{Time}";
    private Boolean isAppointmentRelated = true;
}
