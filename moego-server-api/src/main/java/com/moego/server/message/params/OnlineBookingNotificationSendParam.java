package com.moego.server.message.params;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OnlineBookingNotificationSendParam {

    @NotNull
    private Integer businessId;

    @NotNull
    private Integer groomingId;

    /**
     * bookingRequestId 和 groomingId 二选一，保持向后兼容，优先使用 groomingId
     */
    private Long bookingRequestId;

    private Integer customerId;
    private Integer ownerStaffId;
    // 发送需要数据 getData后已写入
    private List<String> notificationEmailList; // 接收方 email list
    private String businessEmail; // email template 展示的 business email
    private String clientPhoneNumber;
    private String clientEmail;
    // 标签及其变量
    private String staffName;
    private String clientName;
    private String clientFirstName;
    private String clientLastName;

    private String clientPrimaryAddress;
    private String businessName;
    // cache data

    private Boolean sendToBusiness;

    private OnlineBookWaitingNotifyParams.AutoAssign autoAssign;
}
