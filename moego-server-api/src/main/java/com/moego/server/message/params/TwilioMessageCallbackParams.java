package com.moego.server.message.params;

import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020-07-05 11:49
 */
@Data
@ToString
public class TwilioMessageCallbackParams {

    // error response:
    //
    // ErrorCode=30006&SmsSid=SMe09a50d0823d435d8e86feefce1e92d3&SmsStatus=undelivered&MessageStatus=undelivered&To=%2B15142736056&MessageSid=SMe09a50d0823d435d8e86feefce1e92d3&AccountSid=AC426f9d098219020db0785ee68fd472f7&From=%2B14508230552&ApiVersion=2010-04-01
    // success response:
    //
    // SmsSid=SMc72e531131274aaaa4444a29bd2a8897&SmsStatus=sent&MessageStatus=sent&To=%2B12135093215&MessageSid=SMc72e531131274aaaa4444a29bd2a8897&AccountSid=AC426f9d098219020db0785ee68fd472f7&From=%2B12024994978&ApiVersion=2010-04-01

    public static final int SMS_STATUS_UNKNOWN = 0; // 状态未知
    public static final int SMS_STATUS_SENDING = 1; // 发送中
    public static final int SMS_STATUS_DELIVERED = 2; // 发送成功
    public static final int SMS_STATUS_FAILED = 3; // 发送失败
    /**
     * e.g.: 1
     * 存储在 webhook 上面的 id int 数字
     */
    private Integer id;
    /**
     * e.g.: 1,2,3,4,5
     * 存储在 webhook 上面的 ids 逗号结构字符串，需要额外解析
     */
    private String ids;
    /**
     * 将 id 和 ids 结果，合并到 idlist 内，后续只能使用 messageDetailIdList
     */
    private List<Integer> messageDetailIdList;

    private String smsSid;
    private String smsStatus;
    private String messageStatus;
    private String to;
    private String messageSid;
    private String accountSid;
    private String from;
    private String apiVersion;
    private Integer errorCode;
    private String errorMessage;

    public Integer smsSendStatus() {
        if ("sent".equals(smsStatus) || "queued".equals(smsStatus)) {
            return SMS_STATUS_SENDING;
        }
        if ("delivered".equals(smsStatus)) {
            return SMS_STATUS_DELIVERED;
        }
        if ("undelivered".equals(smsStatus) || "failed".equals(smsStatus)) {
            return SMS_STATUS_FAILED;
        }

        return SMS_STATUS_UNKNOWN;
    }

    public static boolean isSendSuccessed(int smsSendStatus) {
        return TwilioMessageCallbackParams.SMS_STATUS_DELIVERED == smsSendStatus;
    }

    public static boolean isSendFailed(int smsSendStatus) {
        return TwilioMessageCallbackParams.SMS_STATUS_FAILED == smsSendStatus;
    }

    public Boolean isRead() {
        return "read".equalsIgnoreCase(messageStatus);
    }

    public Boolean isDelete() {
        return "delete".equalsIgnoreCase(messageStatus);
    }
}
