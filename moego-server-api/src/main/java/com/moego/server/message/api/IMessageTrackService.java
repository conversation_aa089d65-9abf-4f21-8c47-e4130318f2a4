package com.moego.server.message.api;

import com.moego.server.message.dto.MessageTrackDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface IMessageTrackService {
    @GetMapping("/service/message/track/firstCall")
    MessageTrackDTO getFirstCall(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    @GetMapping("/service/message/track/firstSms")
    MessageTrackDTO getFirstSms(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);
}
