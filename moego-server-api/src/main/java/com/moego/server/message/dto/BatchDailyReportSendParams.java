package com.moego.server.message.dto;

import com.moego.idl.models.appointment.v1.SendMethod;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class BatchDailyReportSendParams {

    @Schema(description = "需要发送的 reportIds")
    private List<Long> ids;

    private Long companyId;

    private Long businessId;

    private Long staffId;

    @Schema(description = "send method")
    private SendMethod sendMethod;
}
