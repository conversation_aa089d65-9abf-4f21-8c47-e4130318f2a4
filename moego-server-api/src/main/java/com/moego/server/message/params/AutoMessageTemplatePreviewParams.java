package com.moego.server.message.params;

import com.moego.server.message.enums.AutoMessageTemplateEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/11/22
 */
@Data
@Accessors(chain = true)
public class AutoMessageTemplatePreviewParams {

    private Integer businessId;

    private Integer appointmentId;

    private AutoMessageTemplateEnum templateType;
}
