/*
 * @since 2023-11-29 16:43:19
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.message.api;

import com.moego.server.message.dto.BatchGetMessageUsageReportDTO;
import com.moego.server.message.params.BatchGetMessageUsageReportParams;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface IMessageReportService {
    @PostMapping("/service/message/report/batchGetMessageUsageReport")
    @NotNull
    BatchGetMessageUsageReportDTO batchGetMessageUsageReport(
            @RequestBody @Valid @NotNull BatchGetMessageUsageReportParams params);
}
