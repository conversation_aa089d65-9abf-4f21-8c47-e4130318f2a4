package com.moego.server.message.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ReviewBoosterRecordDetailDTO {

    private AppointmentInfo appointmentInfo;
    private CustomerInfo customerInfo;
    private List<PetInfo> petInfoList;
    private ReviewBoosterRecordDTO reviewDetail;
    private List<StaffInfo> staffInfoList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AppointmentInfo {

        private Integer appointmentId;
        private String appointmentDateTime;
        private Integer status;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CustomerInfo {

        private Integer customerId;
        private String firstName;
        private String lastName;
        private String phoneNumber;
        private String avatarPath;
        private Byte status;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PetInfo {

        private Integer petId;
        private String petName;
        private Integer petTypeId;
        private String petBreed;
        private String avatarPath;
        private Integer lifeStatus;
        private Byte status;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class StaffInfo {

        private Integer staffId;
        private String firstName;
        private String lastName;
        private String avatarPath;
        private Byte status;
    }
}
