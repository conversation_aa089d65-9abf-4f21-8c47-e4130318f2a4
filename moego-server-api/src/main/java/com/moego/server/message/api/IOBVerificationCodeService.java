package com.moego.server.message.api;

import com.moego.server.message.dto.SendEmailDTO;
import com.moego.server.message.dto.SendPhoneDTO;
import com.moego.server.message.dto.VerifyCodeDTO;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface IOBVerificationCodeService {
    /**
     * 商家给指定手机发送验证码
     *
     * @param sendPhoneDTO 发送的手机号码
     * @return 如果发送成功则返回本次请求的 token 作为核销凭证, 发送失败则抛异常
     */
    @PostMapping("/service/message/ob/phone-code")
    String sendPhoneVerificationCode(@RequestBody SendPhoneDTO sendPhoneDTO);

    /**
     * 商家给指定邮箱发送验证码
     *
     * @param sendEmailDTO 发送的邮箱
     * @return 如果发送成功则返回本次请求的 token 作为核销凭证, 发送失败则抛异常
     */
    @PostMapping("/service/message/ob/email-code")
    String sendEmailVerificationCode(@RequestBody SendEmailDTO sendEmailDTO);

    /**
     * 给指定手机生成 verification token，仅可用于 US new client 或非 US 的 client
     *
     * @param sendPhoneDTO 手机号码
     * @return 直接返回本次请求的 token 作为核销凭证
     */
    @PostMapping("/service/message/ob/phone-token")
    String generatePhoneToken(@RequestBody SendPhoneDTO sendPhoneDTO);

    /**
     * 校验 OB 发送的手机验证码
     *
     * @param verifyCodeDTO 验证码信息
     * @return true: 校验成功, false: 校验失败 (可能是验证码错误/服务器错误等)
     */
    @DeleteMapping("/service/message/ob/phone-code")
    Boolean verifyPhoneCode(@RequestBody VerifyCodeDTO verifyCodeDTO);

    /**
     * 校验 OB 发送的邮箱验证码
     *
     * @param verifyCodeDTO 验证码信息
     * @return true: 校验成功, false: 校验失败 (可能是验证码错误/服务器错误等)
     */
    @DeleteMapping("/service/message/ob/email-code")
    Boolean verifyEmailCode(@RequestBody VerifyCodeDTO verifyCodeDTO);

    /**
     * 验证指定手机的 verification token，仅可用于 US new client 或非 US 的 client
     *
     * @param verifyCodeDTO 验证码信息
     * @return true: 校验成功, false: 校验失败 (可能是验证码错误/服务器错误等)
     */
    @DeleteMapping("/service/message/ob/phone-token")
    Boolean verifyPhoneToken(@RequestBody VerifyCodeDTO verifyCodeDTO);
}
