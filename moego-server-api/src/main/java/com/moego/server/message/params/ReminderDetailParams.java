package com.moego.server.message.params;

import com.moego.common.params.PageParams;
import com.moego.server.message.enums.ReminderTypeEnum;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2020-08-16 11:37
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReminderDetailParams extends PageParams implements Serializable {

    private Long companyId;
    private Integer businessId;
    private ReminderTypeEnum reminderTypeEnum;
    private Integer status;

    // 统计appoint 总数时是否根据appoint 去重
    private Boolean sumRemoveDuplicateAppoint;
}
