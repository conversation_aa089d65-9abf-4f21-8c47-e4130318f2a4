package com.moego.server.message.api;

import com.moego.server.message.params.HardwareOrderSummaryParams;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IGroomingEmailService {
    @PostMapping("/service/message/email/sendInvoiceConfirmEmail")
    Boolean sendInvoiceConfirmEmail(@RequestParam("invoiceId") Integer invoiceId);

    @PostMapping("/service/message/email/sendHardwareSummaryEmail")
    Boolean sendHardwareSummaryEmail(@RequestBody @Valid HardwareOrderSummaryParams hardwareOrderSummaryParams);
}
