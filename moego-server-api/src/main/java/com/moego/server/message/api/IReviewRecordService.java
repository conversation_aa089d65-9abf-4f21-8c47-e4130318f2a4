package com.moego.server.message.api;

import com.moego.server.message.dto.ReviewBoosterRecordDTO;
import java.util.Collection;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
public interface IReviewRecordService {

    /**
     * List review booster record by ids.
     *
     * @param ids review booster record ids
     * @return review booster record list
     */
    @PostMapping("/service/message/listByIds")
    List<ReviewBoosterRecordDTO> listByIds(@RequestBody Collection<Integer> ids);
}
