package com.moego.server.message.params;

import com.moego.common.params.PageParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2020-10-08 04:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MoeBusinessReviewBoosterParamsRecord extends PageParams {
    // token business id
    private Integer businessId;
    private Integer customerId;
    // account structure,user for searching specific business
    private Integer searchBusinessId;
}
