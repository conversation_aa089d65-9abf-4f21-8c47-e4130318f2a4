package com.moego.server.message.params;

import com.moego.common.utils.GsonUtil;
import java.util.Map;
import lombok.Data;

@Data
public class MoegoSendPushParams<T> {

    // 分组类型, 可以是 business, staff, account
    private String groupType;
    // 组的 id, 如果是 business, 就是 business 的 id, 对应 business 下面的所有 staff
    // 的长连接都会收到此推送, 其它的组含义以此类推
    private Integer groupId;
    // action 为前后端约定的一个字符串, 类似于 url
    private String action;
    // 元数据, 类似于 http 中的 header
    private Map<String, String> meta;
    // 实际负载, JSON 序列化的字符串
    private String payload;
    // 为了方便, 允许设置未序列化的对象, 调用时会自动序列化到 payload 中
    private T data;

    @Override
    public String toString() {
        T data = this.data;
        this.data = null;
        if (this.payload == null && data != null) {
            this.payload = GsonUtil.toJson(data, false);
        }
        String str = GsonUtil.toJson(this, false);
        this.data = data;
        return str;
    }
}
