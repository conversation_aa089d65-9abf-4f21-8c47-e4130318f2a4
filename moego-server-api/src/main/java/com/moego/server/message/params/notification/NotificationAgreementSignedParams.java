package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraAgreementSignedDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationAgreementSignedParams extends NotificationParams {

    private String title = "Agreement signed";
    private String type = NotificationEnum.TYPE_ACTIVITY_AGREEMENT_SIGNED;
    private Boolean isNotifyBusinessAllStaff = true;
    private NotificationExtraAgreementSignedDto webPushDto;
    private String mobilePushTitle = "Agreement signed";
    private String mobilePushBody = "{customerFullName} - {agreementTitle}";
}
