package com.moego.server.message.params;

import com.moego.common.utils.Pagination;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.util.List;
import lombok.Builder;

@Builder(toBuilder = true)
public record ListReviewBoosterRecordsParams(
        Long businessId,
        Long customerId,
        List<@Max(3) @Min(1) Byte> sources,
        List<@Positive Long> staffIds,
        @NotNull Pagination pagination) {}
