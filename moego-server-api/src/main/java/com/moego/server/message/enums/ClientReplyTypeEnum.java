package com.moego.server.message.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum ClientReplyTypeEnum {
    CONFIRM_APPOINTMENT("Confirm Appointment"),
    CANCEL_APPOINTMENT("Cancel Appointment");

    private final String value;

    public static ClientReplyTypeEnum fromString(String value) {
        for (ClientReplyTypeEnum clientReplyTypeEnum : ClientReplyTypeEnum.values()) {
            if (clientReplyTypeEnum.getValue().equals(value)) {
                return clientReplyTypeEnum;
            }
        }
        return null;
    }

    @JsonValue
    public String getValue() {
        return value;
    }
}
