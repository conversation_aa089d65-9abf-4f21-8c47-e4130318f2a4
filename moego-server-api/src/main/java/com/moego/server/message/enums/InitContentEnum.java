package com.moego.server.message.enums;

/**
 * <AUTHOR>
 * @date 2020-10-18 17:52
 */
public enum InitContentEnum {
    AUTO_REPLAY("Hi {customerName}, this is {storeName}, we will reply as soon as we can. Thanks!"),
    AUTO_REPLAY_TIME_RANGE(
            "{\"Sunday\":[[0,1440]],\"Monday\":[[0,1440]],\"Tuesday\":[[0,1440]],\"Wednesday\":[[0,1440]],\"Thursday\":[[0,1440]],\"Friday\":[[0,1440]],\"Saturday\":[[0,1440]]}"),
    AUTO_TEMPLATE_APPOINTMENT_BOOK("From {storeName}, {customerName}, your appointment is booked for {Date} {Time}."),
    AUTO_TEMPLATE_APPOINTMENT_RESCHEDULED(
            "From {storeName}, {customerName}, your appointment is rescheduled for {Date} {Time}."),
    AUTO_TEMPLATE_APPOINTMENT_CANCELLED(
            "From {storeName}, {customerName}, your appointment for {Date} {Time} has been cancelled."),
    AUTO_TEMPLATE_READY_PICK_UP("From {storeName}, {customerName}, {petName} is ready for pick up. "),
    AUTO_TEMPLATE_ETA("Hello {customerName}, I'm on my way now."),
    REMINDER_APPOINT_FIRST(
            "From {storeName}, {customerName}, your next appointment is coming at {Date} {Time}. Please {replyYconfirm}."),
    REMINDER_APPOINT_SECOND(
            "From {storeName}, {customerName}, your next appointment is coming at {Date} {Time}. Please {replyYconfirm}."),
    REMINDER_PET_BIRTHDAY("From {storeName}, Happy birthday to {petName}!"),
    REMINDER_REBOOK(
            "From {storeName}, Hello {customerName}, it has been almost {xxDays} since {petName} last service.\n Would you like to book for a new appointment?\n"),
    REMINDER_APPOINT_REMIND("From {storeName}, {customerName}, your next appointment is coming at {Date} {Time}."),
    RECEIPT_TEMPLATE("Here's your receipt @{storeName}. Thanks! {receiptLink}"),
    PAY_ONLINE_TEMPLATE("Please pay your open invoice through our secured portal: {payOnlineLink}"),
    COF_LINK_TEMPLATE(
            "{customerName}, Your Card on File information hasn't been submitted yet. Please take a moment to complete this at your earliest convenience. Thank you!"),
    AUTO_TEMPLATE_APPOINTMENT_CONFIRMED_BY_CLIENT("Your appointment for {Date} {Time} has been confirmed."),
    AUTO_TEMPLATE_APPOINTMENT_CANCELLED_BY_CLIENT("Your appointment for {Date} {Time} has been cancelled.");

    private String value;

    InitContentEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
