package com.moego.server.message.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-10-08 03:03
 */
@Data
public class MoeBusinessReviewBoosterParams {

    private Integer id;
    private Integer businessId;
    private String reviewBody;
    private Integer positiveScore;
    private String positiveBody;
    private String positiveYelp;
    private String positiveFacebook;
    private String positiveGoogle;
    private String negativeBody;

    @Schema(description = "是否开启自动发送 1开启 0关闭，默认0")
    private Byte enableAuto;

    @Schema(description = "自动发送等待分钟数，0~10080(7d)")
    @Min(0)
    @Max(10080)
    private Integer autoWaitingMins;
}
