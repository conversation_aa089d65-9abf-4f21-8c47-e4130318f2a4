package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraWaitlistAvailableDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationWaitlistAvailableParams extends NotificationParams {
    private String title = "Waitlist slot opened up";
    private String body = "A slot has become available for one or more of your waitlist clients";
    private String type = NotificationEnum.TYPE_ACTIVITY_WAITLIST_AVAILABLE;
    private NotificationExtraWaitlistAvailableDto webPushDto;
}
