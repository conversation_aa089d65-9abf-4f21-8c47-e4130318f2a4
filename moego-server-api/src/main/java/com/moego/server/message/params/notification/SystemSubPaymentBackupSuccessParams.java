package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraSubPaymentSuccessDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SystemSubPaymentBackupSuccessParams extends NotificationParams {

    private String title = "Subscription payment succeeded";
    private String type = NotificationEnum.TYPE_SYSTEM_SUB_PAYMENT_SUCCESS;
    private NotificationExtraSubPaymentSuccessDto webPushDto;
    private Boolean isNotifyBusinessOwner = true;
    private String mobilePushTitle = "${totalPaidAmount} subscription fee charged from backup card ending in {LAST4}";
    private String mobilePushBody = "Primary card charge failed. Please check it as soon as possible.";
}
