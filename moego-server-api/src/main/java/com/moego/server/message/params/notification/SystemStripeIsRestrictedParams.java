package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraApptCommonDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SystemStripeIsRestrictedParams extends NotificationParams {

    private String title = "Stripe is restricted";
    private String type = NotificationEnum.TYPE_SYSTEM_STRIPE_RESTRICTED;
    private NotificationExtraApptCommonDto webPushDto;
    private Boolean isNotifyBusinessOwner = true;
    private String mobilePushTitle = "";
    private String mobilePushBody = "";
}
