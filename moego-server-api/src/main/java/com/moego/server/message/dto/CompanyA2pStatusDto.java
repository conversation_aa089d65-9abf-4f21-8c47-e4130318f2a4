package com.moego.server.message.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CompanyA2pStatusDto {

    @Schema(description = "No required  Action Required   Pending   Verified   Failed")
    private String a2pStatus;

    @Schema(description = "may by null")
    private String failReason;

    @Schema(description = "1 default 2 Brand Reviewing 3 Campaign Reviewing")
    private Integer step;

    private Boolean withEin;

    private BigDecimal a2pFee;

    private Boolean isPaid;

    @Schema(description = "是否需要联系cs支持")
    private Boolean needSupport;
}
