package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraApptCommonDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationApptConfirmedByClientParams extends NotificationParams {

    private String title = "Appointment confirmed by client";
    private String type = NotificationEnum.TYPE_ACTIVITY_APPT_CONFIRMED_BY_CLIENT;
    private NotificationExtraApptCommonDto webPushDto;
    private String mobilePushTitle = "Appointment confirmed by client";
    private String mobilePushBody = "{customerFullName} {Date}{Time} with {staffFirstName}";
    private Boolean isAppointmentRelated = true;
}
