/*
 * @since 2023-11-29 16:43:45
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.message.client;

import com.moego.server.message.api.IMessageReportService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(value = "moego-message-server", url = "${moego.server.url.message}", contextId = "IMessageReportClient")
public interface IMessageReportClient extends IMessageReportService {}
