package com.moego.server.message.api;

import com.moego.server.message.params.ReminderDismissParams;
import java.util.List;
import java.util.Set;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface IReminderDismissService {
    /**
     * 获取所有流失用户（lapsed client）id, 返回 id 为 customer id
     * 使用 businessIds 查询，调用方保证 businessIds 都是同一个 company 的
     * @return all customer ids
     */
    @GetMapping("/service/message/reminderDismiss/get/dismissIds")
    List<Integer> getDismissIds(
            @SpringQueryMap ReminderDismissParams reminderDismissParams,
            @RequestParam("companyId") Long companyId,
            @RequestParam(value = "businessIds", required = false) Set<Integer> businessIds);
}
