package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraSubPaymentFailedDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SystemSubPaymentFailedParams extends NotificationParams {

    private String title = "Your subscription payment was unsuccessful";
    private String type = NotificationEnum.TYPE_SYSTEM_SUB_PAYMENT_FAIL;
    private NotificationExtraSubPaymentFailedDto webPushDto;
    private Boolean isNotifyBusinessOwner = true;
    private String mobilePushTitle = "Your subscription payment was unsuccessful";
    private String mobilePushBody =
            "Due on {Date}. Please update your credit card details to prevent account suspension.";
}
