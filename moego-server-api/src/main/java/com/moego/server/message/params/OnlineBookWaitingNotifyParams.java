package com.moego.server.message.params;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
public class OnlineBookWaitingNotifyParams {

    public static final String TYPE_DECLINE = "decline";
    public static final String TYPE_WAITING = "move";
    public static final String TYPE_ACCEPT = "accept";
    public static final String TYPE_SUBMIT = "submit";

    @Nullable
    private Integer groomingId;
    /**
     * bookingRequestId 和 groomingId 二选一，优先使用 groomingId
     */
    @Nullable
    private Long bookingRequestId;

    /**
     * client 是否发送email
     */
    private Boolean isSendEmail;
    /**
     * client 是否发送sms
     */
    private Boolean isSendText;
    /**
     * client 是否发送 app message
     */
    private Boolean isSendAppMessage;
    /**
     * business 是否发送 email
     */
    private Boolean isBusinessSendEmail = false;

    /**
     * 必须是上面static静态值的一个
     */
    @NotNull
    private String type;

    private Long companyId; // 应传尽传

    @NotNull
    private Integer businessId;

    /**
     * 如果是 auto assign 的 staff 和 appointmentTime，在商家 accept 之前，发送给 client 的通知都需要将这两个值设置为 pending
     */
    @Nullable
    @Hidden
    private AutoAssign autoAssign;

    @Data
    @Accessors(chain = true)
    public static class AutoAssign {
        @Nullable
        private Integer staffId;

        @Nullable
        private Integer appointmentTime;
    }
}
