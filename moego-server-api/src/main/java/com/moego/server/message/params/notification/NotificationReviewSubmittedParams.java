package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraReviewSubmittedDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationReviewSubmittedParams extends NotificationParams {

    private String title = "Review submitted";
    private String type = NotificationEnum.TYPE_ACTIVITY_REVIEW_SUBMITTED;
    private Boolean isNotifyBusinessAllStaff = true;
    private NotificationExtraReviewSubmittedDto webPushDto;
    private String mobilePushTitle = "Review replied";
    private String mobilePushBody = "\"{reviewScore}\" reviewed by {customerFullName}";
}
