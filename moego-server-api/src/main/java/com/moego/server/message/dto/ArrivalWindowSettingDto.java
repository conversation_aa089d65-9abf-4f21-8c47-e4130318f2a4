package com.moego.server.message.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "ArrivalWindowSettingDto")
public class ArrivalWindowSettingDto {

    private Integer id;

    private Integer businessId;

    @NotNull
    @Schema(description = "是否打开 1打开 0关闭")
    private Integer status;

    @NotNull
    @Schema(description = "arrival before 分钟数")
    private Integer arrivalBefore;

    @NotNull
    @Schema(description = "arrival after 分钟数")
    private Integer arrivalAfter;

    @JsonIgnore
    /**
     * 执行auto message解析时，用来判断是否需要发送
     */
    private Boolean arrivalWindowStatus;
}
