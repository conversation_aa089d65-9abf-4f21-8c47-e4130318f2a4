package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraMarketingCampaignDTO;
import com.moego.server.business.dto.MoeStaffDto;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @see <a href="https://moego.atlassian.net/browse/TECH-681">TECH-681</a>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StartNotificationCampaignParams extends NotificationParams {
    private String starter;
    private String title = "New marketing campaign title";
    private String body = "New marketing campaign body";
    private String mobilePushTitle = "New marketing campaign mobile push title";
    private String mobilePushBody = "New marketing campaign mobile push body";
    private String type = NotificationEnum.TYPE_SYSTEM_PLATFORM_MARKETING_CAMPAIGN;
    // 需要批量向 business owners 发送通知
    private List<MoeStaffDto> owners;
    private Boolean isNotifyBusinessOwner = true;
    private Boolean isSendMobilePush = true;

    private NotificationExtraMarketingCampaignDTO webPushDto;
}
