package com.moego.server.message.api;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface IReviewBoosterTaskService {
    @GetMapping("/service/message/review/booster/task")
    void beginReviewBoosterTask();

    @PostMapping("/service/message/review/booster/auto/check")
    void reviewBoosterForGroomingIdCheck(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("groomingId") Integer groomingId,
            @RequestParam("checkOutTime") Long checkOutTime);
}
