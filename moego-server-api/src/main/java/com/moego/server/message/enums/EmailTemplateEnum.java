package com.moego.server.message.enums;

/**
 * <AUTHOR>
 * @date 2020-09-03 23:05
 */
public enum EmailTemplateEnum {
    APPOINTMENT_REMINDER("Appointment reminder", "Your appointment has been booked!"),
    FORGET_PASSWORD("Forget password", "Reset password - MoeGo"),
    READY_NOTIFICATION("Ready notification", "{PetName} is ready for pickup!");

    private String name;
    private String subject;

    EmailTemplateEnum(String name, String subject) {
        this.name = name;
        this.subject = subject;
    }

    public String getSubject() {
        return subject;
    }

    public String getName() {
        return name;
    }
}
