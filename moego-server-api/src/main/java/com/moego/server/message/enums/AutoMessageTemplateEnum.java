package com.moego.server.message.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum AutoMessageTemplateEnum {
    //    1:appointment book;
    //    2:appointment rescheduled;
    //    3:appointment cancelled;
    //    4:ready for pickup;
    //    5:send ETA
    //    6:appointment is confirmed by client
    //    7:appointment is cancelled by client
    APPOINTMENT_BOOK(1, "appointment booked"),
    APPOINTMENT_RESCHEDULED(2, "appointment rescheduled"),
    APPOINTMENT_CANCELLED(3, "appointment cancelled"),
    READY_PICK_UP(4, "ready for pickup"),
    ETA(5, "eta"),
    APPOINTMENT_CONFIRMED_BY_CLIENT(6, "appointment was confirmed by client"),
    APPOINTMENT_CANCELLED_BY_CLIENT(7, "appointment was cancelled by client"),
    RECEIPT(8, "receipt"),
    PAY_ONLINE(9, "pay online"),
    APPOINTMENT_MOVED_TO_WAIT_LIST(10, "appointment moved to wait list");

    private final Integer value;
    private final String desc;

    AutoMessageTemplateEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static List<Integer> getAllValue() {
        return Arrays.stream(AutoMessageTemplateEnum.values())
                .map(AutoMessageTemplateEnum::getValue)
                .collect(Collectors.toList());
    }

    public static AutoMessageTemplateEnum fromValue(Integer value) {
        return Arrays.stream(AutoMessageTemplateEnum.values())
                .filter(autoMessageTemplateEnum ->
                        autoMessageTemplateEnum.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
}
