/*
 * @since 2023-07-06 17:03:06
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.message.dto;

import lombok.Builder;

@Builder(toBuilder = true)
public record MassTextDTO(
        Integer id,
        Integer businessId,
        Integer staffId,
        Integer batchSize,
        Integer status,
        String messageText,
        Integer createTime,
        Integer updateTime,
        Byte isFinish) {}
