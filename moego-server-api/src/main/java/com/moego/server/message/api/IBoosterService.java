package com.moego.server.message.api;

import com.moego.server.message.dto.AdminQueryReviewBoosterRecordResult;
import com.moego.server.message.dto.CustomerAvgRateDTO;
import com.moego.server.message.dto.ReviewBoosterDTO;
import com.moego.server.message.dto.ReviewBoosterRecordDTO;
import com.moego.server.message.params.AdminQueryReviewBoosterRecordParams;
import com.moego.server.message.params.AdminUpdateReviewBoosterRecordParams;
import com.moego.server.message.params.GetReviewBoosterRecordParams;
import com.moego.server.message.params.MoeBusinessReviewBoosterParams;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IBoosterService {

    /**
     * TODO(account structure): company 迁移后，查询 company 维度的数据
     */
    @PostMapping("/service/message/reviewBooster/getAvgReviewRate")
    List<CustomerAvgRateDTO> getAvgReviewRate(@RequestBody List<Integer> customerIds);

    /*
     * Deprecated
     * use getAppointmentReviewRecord, can get review record by source
     */
    @Deprecated
    @GetMapping("/service/message/reviewBooster/getReviewRecord")
    ReviewBoosterRecordDTO getReviewRecord(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("customerId") Integer customerId,
            @RequestParam("groomingId") Integer groomingId);

    @PostMapping("/service/message/reviewBooster/record/getAppointmentReviewRecord")
    List<ReviewBoosterRecordDTO> getAppointmentReviewRecord(@Valid @RequestBody GetReviewBoosterRecordParams params);

    @PostMapping("/service/message/reviewBooster/record")
    List<ReviewBoosterRecordDTO> getAppointmentReviewRecords(
            @RequestParam("businessId") Integer businessId,
            @RequestParam(value = "source", required = false) Byte source,
            @RequestBody List<Integer> groomingIds);

    @GetMapping("/service/message/getReviewBoosterConfig")
    ReviewBoosterDTO getReviewBoosterConfig(@RequestParam("businessId") Integer businessId);

    @PutMapping("/service/message/updateReviewBoosterConfig")
    ReviewBoosterDTO updateReviewBoosterConfig(@Valid @RequestBody MoeBusinessReviewBoosterParams params);

    @PostMapping("/service/message/reviewBooster/adminQueryReviewBoosterRecord")
    AdminQueryReviewBoosterRecordResult adminQueryReviewBoosterRecord(
            @Valid @RequestBody AdminQueryReviewBoosterRecordParams params);

    @PutMapping("/service/message/reviewBooster/adminUpdateReviewBoosterRecord")
    Boolean adminUpdateReviewBoosterRecord(@Valid @RequestBody AdminUpdateReviewBoosterRecordParams params);
}
