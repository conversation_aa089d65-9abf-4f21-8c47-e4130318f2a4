package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraDisputeDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationDisputeParams extends NotificationParams {

    private String title = "Payment disputed";
    private String type = NotificationEnum.TYPE_ACTIVITY_PAYMENT_DISPUTE;
    private Boolean isNotifyBusinessAllStaff = true;
    private NotificationExtraDisputeDto webPushDto;
    private String mobilePushTitle = "Payment disputed";
    private String mobilePushBody = "A {totalPaidAmount} payment is being disputed by {customerFullName}";
}
