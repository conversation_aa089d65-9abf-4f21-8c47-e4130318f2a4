package com.moego.server.payment.dto;

import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
// used by enterprise-api-v1, don't make any breaking changes
public class EnterpriseSubscriptionConfigDTO {
    private Long enterpriseId;
    private String stripeSubscriptionsId;
    private Integer level;
    private Integer locationNum;
    private Integer vansNum;
    private Long beginDate;
    private Long expireDate;
    private Integer currentPlanId;
    private Byte autoRenew;
    private Integer nextPlanId;
    private Byte chargeStatus;
    private String chargeMsg;
    private Long chargeFailedTime;
    private Byte payCompanySubscription;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Integer usedLocationNum;
    private Integer usedVanNum;
}
