package com.moego.server.payment.params.billing;

import com.moego.common.utils.Pagination;
import com.moego.server.payment.enums.CouponBusinessCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/12/6
 */
@Builder(toBuilder = true)
@Setter
@Getter
public class QueryCouponParams {

    @Schema(description = "title of the coupon, supports fuzzy search")
    private String code;

    @Min(0)
    @Max(100)
    private BigDecimal percentOffLeft;

    @Min(0)
    @Max(100)
    private BigDecimal percentOffRight;

    @Schema(description = "1 - valid, 0 - invalid, if it is null, all will be queried")
    @Builder.Default
    private Integer valid = 1;

    @Schema(description = "0 - unassigned, 1 - assigned, 2 - used")
    private Integer status;

    @Min(1)
    @Max(12)
    @Schema(description = "validity period, with a granularity of months, can be specified from 1 to 12")
    private Integer validMonth;

    @Schema(description = "indicate which business the coupon belongs to")
    private CouponBusinessCategoryEnum businessCategory;

    @Schema(description = "stripe coupon id, supports fuzzy search")
    private String stripeCouponId;

    private Pagination pagination;
}
