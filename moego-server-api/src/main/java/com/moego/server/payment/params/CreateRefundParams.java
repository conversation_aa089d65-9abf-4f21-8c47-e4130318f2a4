package com.moego.server.payment.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.moego.idl.models.order.v1.OrderModel;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class CreateRefundParams {

    @NotNull
    private Integer paymentId;

    @NotNull
    private BigDecimal amount;

    @Size(max = 192)
    @Schema(
            description =
                    "refund reason, moego table column size is 1023, but square refund api param(reason) limit is 192")
    private String reason;

    private Integer refundOrderPaymentId; // RefundReferenceId?

    @JsonIgnore
    private OrderModel order;
}
