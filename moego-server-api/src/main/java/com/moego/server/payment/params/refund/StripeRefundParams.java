package com.moego.server.payment.params.refund;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/1/4
 */
@Builder(toBuilder = true)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class StripeRefundParams {
    @Schema(
            description =
                    "A positive integer in cents representing how much of this charge to refund. Can refund only up to the remaining, unrefunded amount of the charge.")
    Long amount;

    @Schema(description = "The identifier of the PaymentIntent to refund.")
    String paymentIntent;

    @Schema(
            description =
                    """
        String indicating the reason for the refund.\s
        If set, possible values are duplicate, fraudulent,
         and requested_by_customer. If you believe the charge to be fraudulent,
          specifying fraudulent as the reason will add the associated card and email to your block lists,
           and will also help us improve our fraud detection algorithms.
        """)
    String reason;

    @Schema(description = "The identifier of the charge to refund.")
    String charge;
}
