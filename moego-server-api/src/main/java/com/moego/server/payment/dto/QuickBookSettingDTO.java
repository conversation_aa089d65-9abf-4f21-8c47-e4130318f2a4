package com.moego.server.payment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/8/19
 */
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class QuickBookSettingDTO {
    private Integer id;
    private Integer businessId;
    private Integer connectId;
    private Byte enableSync;
    private String syncBeginDate;
    private String minSyncDate;
    private Integer status;
    private Long createTime;
    private Long updateTime;
    private Long companyId;
    private Integer taxSyncType;
    private Integer userVersion;
    private String lastDisconnectedTime;
    private Byte salesReceiptEnable;
}
