package com.moego.server.payment.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/1/10
 */
@Data
public class CardRequestParams {

    @NotNull
    @Schema(description = "通过卡信息去stripe平台获取到的token值")
    String stripeToken;

    String firstName;
    String lastName;
    String phone;
    String email;

    @JsonIgnore
    Integer businessId;
}
