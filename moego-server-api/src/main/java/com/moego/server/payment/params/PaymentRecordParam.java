package com.moego.server.payment.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/6/27
 */
@Data
public class PaymentRecordParam {
    // 商家信息
    @NotNull
    private Long companyId;

    @NotNull
    private Integer businessId;

    private Integer staffId;
    // customer 信息
    @NotNull
    private String module;

    @NotNull
    private Integer customerId;

    private String paidBy;
    private String description;
    // 订单
    @NotNull
    private Integer invoiceId;
    // 支付信息
    @NotNull
    private String vendor;

    @Schema(description = "默认 Credit Card")
    private String method;

    @Schema(description = "默认 1")
    private Integer methodId;

    @Schema(description = "支付金额，单位为美元， 保留两位小数，正常应该与 payment intent id 查询到的 amount 相等")
    @NotNull
    private BigDecimal amount;

    @NotNull
    private String stripeIntentId;

    @Schema(description = "小费金额")
    private BigDecimal tips;

    @Schema(description = "status")
    private Byte status;
}
