package com.moego.server.payment.dto;

import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundChannelDTO {
    private List<CanRefundChannel> channelList;

    private BigDecimal refundAmount;

    private Boolean isCombination;

    private Integer invoiceId;
}
