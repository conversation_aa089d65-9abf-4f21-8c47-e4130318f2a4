package com.moego.server.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/3/26 4:18 PM
 */
@Data
@Schema(description = "商家扣费记录")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChargeDTO {

    @Schema(description = "扣费时间戳")
    private Long created;

    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "扣费描述")
    private String description;

    @Schema(description = "收据链接")
    private String receiptUrl;

    @Schema(description = "扣费单位")
    private String currency;

    @Schema(description = "扣费Id")
    private String chargeId;

    @Schema(description = "扣费卡号")
    private String paymentMethod;

    @Schema(description = "扣费状态")
    private String status;
}
