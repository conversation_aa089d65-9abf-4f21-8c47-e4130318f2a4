package com.moego.server.payment.dto;

import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/1/8
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class PayDetailDTO {
    private Integer id;
    private Integer businessId;
    private Integer paymentId;
    private Integer orderId;
    private String stripeIntentId;
    private BigDecimal amount;
    private BigDecimal grossSales;
    private BigDecimal discount;
    private BigDecimal tax;
    private BigDecimal tips;
    private BigDecimal bookingFee;
    private BigDecimal convenienceFee;
    private Boolean isDeposit;
    private Date createTime;
    private Date updateTime;
    private Long companyId;
}
