package com.moego.server.payment.dto;

import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.customer.dto.CustomerInfoDto;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/1/9
 */
@Data
public class CardClientDto {

    @Schema(description = "1:valid 2:submitted 3:expired")
    Integer submitted;

    MoeBusinessDto businessDTO;
    CustomerInfoDto customerDTO;
    // IPetService
    List<CustomerPetDetailDTO> petsDto;
    PaymentSettingForClientDTO paymentSetting;
    Boolean redirectToOB;
    String bookOnlineName;
}
