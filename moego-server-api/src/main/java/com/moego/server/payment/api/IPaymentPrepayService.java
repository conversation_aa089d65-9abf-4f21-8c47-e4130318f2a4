package com.moego.server.payment.api;

import com.moego.common.dto.PaymentSummary;
import com.moego.server.payment.dto.PaymentIntentDTO;
import com.moego.server.payment.params.BookOnlinePayParams;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2023/10/10
 */
public interface IPaymentPrepayService {

    /**
     * Create payment intent
     *
     * @param businessId business id
     * @return payment intent
     */
    @PostMapping("/service/payment/payment-intent/create")
    PaymentIntentDTO createPaymentIntent(@RequestParam Integer businessId);

    /**
     * Prepay
     *
     * @param businessId business id
     * @param params     prepay params
     * @return prepay result
     */
    @PostMapping("/service/payment/pay-online/prepay")
    PaymentSummary.PaymentDto prepay(@RequestParam Integer businessId, @RequestBody BookOnlinePayParams params);
}
