package com.moego.server.payment.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class CreateRefundByPaymentIdParams {

    @NotNull
    private Integer paymentId;

    private BigDecimal amount;

    @Size(max = 192)
    @Schema(
            description =
                    "refund reason, moego table column size is 1023, but square refund api param(reason) limit is 192")
    private String reason;

    @Schema(description = "退款时，额外退booking fee的金额")
    private BigDecimal bookingFee;
}
