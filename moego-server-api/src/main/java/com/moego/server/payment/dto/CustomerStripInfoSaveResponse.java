package com.moego.server.payment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerStripInfoSaveResponse {

    private Integer businessId;
    private Integer customerId;
    private String stripeCustomerId;
    private String stripeBizAccountId;

    private String paymentMethodId;

    private String cardNumber;
    private String cardType;
}
