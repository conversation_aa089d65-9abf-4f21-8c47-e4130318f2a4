package com.moego.server.payment.dto.stripe;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/1/5
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HardwareOrderCollection {
    String object;
    List<HardwareOrderDetail> data;
    boolean hasMore;
    String url;
}
