package com.moego.server.payment.dto;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class PreAuthDTO {

    private Integer preAuthId;

    private Integer paymentId;

    private Integer ticketId;

    private Integer customerId;

    private BigDecimal preAuthAmount;

    private Long preAuthTime;

    private Integer preAuthStatus;

    private String preAuthPaymentMethod;

    private String preAuthFailedMessage;

    private String preAuthCardNumber;
    // default false
    private Boolean inBSPD = false;

    private Long preAuthFinishTime;

    private Boolean isCapture;

    private PreAuthFeesDTO feesDetail;

    private String method;
    private String cardType;
    private String cardNumber;
    private String expMonth;
    private String expYear;
    private String cardFunding;
}
