package com.moego.server.payment.enums;

import lombok.Getter;

@Getter
public enum DisputeStatusTypeEnum {
    UNSPECIFIED(0, "unspecified"),
    WARNING_NEEDS_RESPONSE(1, "warning_needs_response"),
    WARNING_UNDER_REVIEW(2, "warning_under_review"),
    WARNING_CLOSED(3, "warning_closed"),
    NEEDS_RESPONSE(4, "needs_response"),
    UNDER_REVIEW(5, "under_review"),
    WON(6, "won"),
    LOST(7, "lost");

    private final int value;
    private final String description;

    DisputeStatusTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static DisputeStatusTypeEnum fromValue(int value) {
        for (DisputeStatusTypeEnum e : DisputeStatusTypeEnum.values()) {
            if (e.value == value) {
                return e;
            }
        }
        return null;
    }
}
