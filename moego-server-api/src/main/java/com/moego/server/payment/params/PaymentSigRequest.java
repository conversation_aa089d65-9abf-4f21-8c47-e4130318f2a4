package com.moego.server.payment.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/7/27 11:31 AM
 */
@Data
public class PaymentSigRequest {

    @NotNull
    @Min(1)
    @Schema(description = "moego payment table primary id")
    private Integer primaryId;

    @Size(max = 255)
    private String signature;
}
