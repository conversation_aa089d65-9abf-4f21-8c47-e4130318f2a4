package com.moego.server.payment.dto;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/9/8
 */
@Data
@Accessors(chain = true)
@Builder
public class AdminCustomizedPaymentSettingView {

    private Integer id;
    private Integer accountId;
    private String email;
    private Integer companyId;
    private String companyName;
    private Long customizedMinVol;
    private BigDecimal onlineFeeRate;
    private Integer onlineFeeCents;
    private BigDecimal readerFeeRate;
    private Integer readerFeeCents;
    private Date createTime;
    private Date updateTime;
}
