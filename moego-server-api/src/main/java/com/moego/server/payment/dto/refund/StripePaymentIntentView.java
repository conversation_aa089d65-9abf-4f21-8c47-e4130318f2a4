package com.moego.server.payment.dto.refund;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/1/5
 */
@Getter
@Setter
@NoArgsConstructor
@Builder(toBuilder = true)
@AllArgsConstructor
public class StripePaymentIntentView {
    @Schema(description = "amount")
    Long amount;

    @Schema(description = "amount_capturable")
    Long amountCapturable;

    @Schema(description = "amount_details")
    Object amountDetails;

    @Schema(description = "amount_received")
    Long amountReceived;

    @Schema(description = "application_fee_amount")
    Long applicationFeeAmount;

    @Schema(description = "Settings to configure compatible payment methods from the Stripe Dashboard")
    Object automaticPaymentMethods;
    /**
     * ID of the Connect application that created the PaymentIntent.
     */
    @Schema(description = "application")
    String application;

    @Schema(description = "canceled_at")
    Long canceledAt;

    @Schema(description = "cancellation_reason")
    String cancellationReason;

    @Schema(description = "capture_method")
    String captureMethod;

    @Schema(description = "client_secret")
    String clientSecret;

    @Schema(description = "confirmation_method")
    String confirmationMethod;

    @Schema(description = "created")
    Long created;

    @Schema(description = "currency")
    String currency;

    @Schema(description = "ID of the Customer this PaymentIntent belongs to, if one exists.")
    String customer;

    @Schema(description = "An arbitrary string attached to the object. Often useful for displaying to users.")
    String description;

    @Schema(description = "id")
    String id;

    @Schema(description = "ID of the invoice that created this PaymentIntent, if it exists.")
    String invoice;

    @Schema(description = "last_payment_error")
    Object lastPaymentError;

    @Schema(description = "The latest charge created by this PaymentIntent.")
    String latestCharge;

    @Schema(description = "livemode")
    Boolean livemode;

    @Schema(description = "metadata")
    Map<String, String> metadata;

    @Schema(description = "next_action")
    Object nextAction;

    @Schema(description = "object")
    String object;

    @Schema(
            description = "The account (if any) for which the funds of the PaymentIntent are intended."
                    + " See the PaymentIntents use case for connected accounts for details.")
    String onBehalfOf;

    @Schema(description = "ID of the payment method used in this PaymentIntent.")
    String paymentMethod;

    @Schema(description = "Payment-method-specific configuration for this PaymentIntent.")
    Object paymentMethodOptions;

    @Schema(description = "payment_method_types")
    List<String> paymentMethodTypes;

    @Schema(description = "If present, this property tells you about the processing state of the payment.")
    Object processing;

    @Schema(
            description = "Email address that the receipt for the resulting payment will be sent to. "
                    + "If receipt_email is specified for a payment in live mode,"
                    + " a receipt will be sent regardless of your email settings.")
    String receiptEmail;

    @Schema(description = "ID of the review associated with this PaymentIntent, if any.")
    String review;

    @Schema(description = "setup_future_usage")
    String setupFutureUsage;

    @Schema(description = "shipping")
    Object shipping;

    @Schema(description = "source")
    Object source;

    @Schema(description = "statement_descriptor")
    String statementDescriptor;

    @Schema(description = "statement_descriptor_suffix")
    String statementDescriptorSuffix;

    @Schema(description = "status")
    String status;

    @Schema(
            description =
                    "The data that automatically creates a Transfer after the payment finalizes. Learn more about the use case for connected accounts.")
    Object transferData;

    @Schema(description = "transfer_group")
    String transferGroup;
}
