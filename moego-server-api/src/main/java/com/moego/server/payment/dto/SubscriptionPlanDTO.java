package com.moego.server.payment.dto;

import com.moego.common.dto.NewPricingLevelDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/3/17 10:21 AM
 */
@Data
public class SubscriptionPlanDTO {

    @Schema(description = "primary id")
    private Integer id;

    private String stripePlanId;

    @Schema(description = "0: monthly 1:yearly")
    private Byte planType;

    @Schema(description = "unit price")
    private BigDecimal price;

    private String title;
    private String description;

    @Schema(description = "corresponding unit for this stripe plan")
    private Integer businessNum;

    @Schema(description = "1: moego 2.0 plan  2: new pricing from 2022.10")
    private Byte isNewPricing;

    @Schema(description = "0：mobile/van； 1： salon")
    private Byte businessType;

    @Schema(description = "tier level")
    private NewPricingLevelDto planTier;

    private Integer level;
    private String planName;
}
