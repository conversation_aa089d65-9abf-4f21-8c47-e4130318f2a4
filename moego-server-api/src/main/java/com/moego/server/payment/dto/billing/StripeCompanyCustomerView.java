package com.moego.server.payment.dto.billing;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@Getter
@Setter
public class StripeCompanyCustomerView {
    private Integer id;

    private Integer companyId;

    private String stripeCustomerId;

    private String object;

    private Integer createdTime;

    private Integer updatedTime;

    private String currency;

    private String email;

    private String livemode;

    private String metadata;

    private Boolean status;
}
