package com.moego.server.payment.params;

import lombok.Data;

@Data
public class CreateEnterpriseSubscriptionConfigParams {
    private Long enterpriseId;
    private String stripeSubscriptionsId;
    private Integer level;
    private Integer locationNum;
    private Integer vansNum;
    private Long beginDate;
    private Long expireDate;
    private Integer currentPlanId;
    private Byte autoRenew;
    private Integer nextPlanId;
    private Byte chargeStatus;
    private String chargeMsg;
    private Long chargeFailedTime;
    private Byte payCompanySubscription;
}
