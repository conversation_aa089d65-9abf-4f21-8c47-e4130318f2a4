package com.moego.server.payment.client;

import com.moego.server.payment.api.IPaymentMsgDeliveryService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @since 2024/9/30
 */
@FeignClient(
        value = "moego-payment-server",
        url = "${moego.server.url.payment}",
        contextId = "IPaymentMsgDeliveryClient")
public interface IPaymentMsgDeliveryClient extends IPaymentMsgDeliveryService {}
