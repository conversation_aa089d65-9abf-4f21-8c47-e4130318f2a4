package com.moego.server.payment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CardDTO {

    private String id;
    private String cardBrand;
    private Integer expMonth;
    private Integer expYear;
    private String last4;
    private String cardHolderName;
    private String cardType;

    private Integer customerId;

    private String fingerprint;
}
