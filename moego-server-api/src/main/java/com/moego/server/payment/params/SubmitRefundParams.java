package com.moego.server.payment.params;

import com.moego.server.payment.dto.CanRefundChannel;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubmitRefundParams {
    @NotNull
    private Integer invoiceId;

    @NotNull
    @Valid
    List<CanRefundChannel> refunds;

    private String refundReason;

    @NotNull
    private BigDecimal refundAmount;
}
