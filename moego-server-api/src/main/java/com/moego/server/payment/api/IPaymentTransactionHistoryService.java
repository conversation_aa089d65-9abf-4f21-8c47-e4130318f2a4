package com.moego.server.payment.api;

import com.moego.server.payment.dto.ChargeDTO;
import com.moego.server.payment.dto.PayDetailDTO;
import com.moego.server.payment.dto.refund.StripePaymentIntentView;
import com.moego.server.payment.dto.refund.TransactionHistoryListDTO;
import com.moego.server.payment.params.refund.StripeRefundParams;
import com.moego.server.payment.params.refund.TransactionHistoryListParams;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2024/1/2
 */
public interface IPaymentTransactionHistoryService {

    @PostMapping("/service/payment/transaction/history/select/list")
    TransactionHistoryListDTO getTransactionHistoryList(@RequestBody @Validated TransactionHistoryListParams params);

    @PostMapping("/service/payment/transaction/history/select")
    StripePaymentIntentView getTransactionHistoryById(@RequestParam("id") String id);

    @PostMapping("/service/payment/transaction/history/refund")
    String transactionHistoryRefund(@RequestBody @Validated StripeRefundParams params);

    @PostMapping("/service/payment/transaction/history/business/list")
    List<ChargeDTO> getBusinessChargeList(@RequestParam("companyId") Long companyId);

    @PostMapping("/service/payment/transaction/history/pay/detail/list")
    List<PayDetailDTO> getPayDetailsByPaymentIds(@RequestBody List<Integer> paymentIds);

    @GetMapping("/service/payment/transaction/history/pay/intent/detail")
    String getPaymentIntentDetail(@RequestParam String paymentIntentId);

    @PostMapping("/service/payment/transaction/history/select/intent/list")
    TransactionHistoryListDTO getTransactionHistoryListByIntent(@RequestBody List<String> paymentIntentIds);
}
