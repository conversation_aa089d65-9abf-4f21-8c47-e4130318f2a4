package com.moego.server.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/6/20
 */
@Builder(toBuilder = true)
@Data
public class AssociationAccountDTO {
    @Schema(description = "square token response dto")
    private GetSquareTokenResponse squareTokenResponse;

    @Schema(description = "stripe metadata")
    AssociationStripeDTO stripeAccount;
}
