package com.moego.server.payment.client;

import com.moego.server.payment.api.IPaymentVerificationService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @since 2024/1/13
 */
@FeignClient(
        value = "moego-payment-server",
        url = "${moego.server.url.payment}",
        contextId = "IPaymentVerificationClient")
public interface IPaymentVerificationClient extends IPaymentVerificationService {}
