package com.moego.server.payment.params.stripe;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/3/12
 */
@Setter
@Getter
@Builder(toBuilder = true)
public class QueryPayoutListSummaryParams {
    @NotNull(message = "businessId 不能为空")
    private Integer businessId;

    @NotNull(message = "startCreatedDate 不能为空")
    private String startCreatedDate;

    @NotNull(message = "endCreatedDate 不能为空")
    private String endCreatedDate;
}
