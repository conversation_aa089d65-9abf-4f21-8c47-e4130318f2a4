package com.moego.server.payment.dto.dispute;

import java.util.Date;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/12/14
 */
@Builder(toBuilder = true)
@Getter
@Setter
public class StripeDisputeDetailView {
    private Integer id;
    private Integer businessId;
    private String disputeId;
    private Integer paymentId;
    private String paymentIntentId;
    private String paymentMethod;
    private Long amount;
    private String currency;
    private String status;
    private String reason;
    private String customer;
    private Long chargedOn;
    private Long disputedOn;
    private Long respondedOn;
    private Date createdAt;
    private Date updatedAt;
    private Date deletedAt;
}
