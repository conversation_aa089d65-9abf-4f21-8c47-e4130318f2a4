package com.moego.server.payment.dto;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/11/1
 */
@Data
@Accessors(chain = true)
public class CustomizedPaymentSettingVO {

    private Integer id;
    private Integer companyId;
    private Long customizedMinVol;
    private BigDecimal onlineFeeRate;
    private Integer onlineFeeCents;
    private BigDecimal readerFeeRate;
    private Integer readerFeeCents;
    private Date createTime;
    private Date updateTime;
}
