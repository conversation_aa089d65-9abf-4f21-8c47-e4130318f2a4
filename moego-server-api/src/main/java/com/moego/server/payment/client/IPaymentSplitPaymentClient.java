package com.moego.server.payment.client;

import com.moego.server.payment.api.IPaymentSplitPaymentService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @since 2024/7/18
 */
@FeignClient(
        value = "moego-payment-server",
        url = "${moego.server.url.payment}",
        contextId = "IPaymentSplitPaymentClient")
public interface IPaymentSplitPaymentClient extends IPaymentSplitPaymentService {}
