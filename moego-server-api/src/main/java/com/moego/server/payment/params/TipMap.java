package com.moego.server.payment.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class TipMap {

    @Schema(description = "by percentage 3个tip配置")
    @Valid
    private PercentageTipMap percentageConfig;

    @Schema(description = "by amount 3个tip配置")
    @Valid
    private AmountTipMap amountConfig;

    @Data
    public static class AmountTipMap {

        @Min(0)
        private BigDecimal low;

        @Min(0)
        private BigDecimal medium;

        @Min(0)
        private BigDecimal high;
    }

    @Data
    public static class PercentageTipMap {

        @Max(100)
        @Min(1)
        private Integer low;

        @Max(100)
        @Min(1)
        private Integer medium;

        @Max(100)
        @Min(1)
        private Integer high;
    }
}
