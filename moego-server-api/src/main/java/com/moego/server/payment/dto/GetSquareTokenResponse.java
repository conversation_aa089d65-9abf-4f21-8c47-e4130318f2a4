package com.moego.server.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetSquareTokenResponse {

    private Integer businessId;
    private String expiresAt;
    private String merchantId;
    private String defaultLocationId;

    @Schema(description = "moego platform integrated application's id")
    private String appId;

    @Schema(description = "是否与square connected")
    private Boolean squareConnected;

    @Schema(description = "是否有在square白名单内")
    private Boolean squareAccess;
}
