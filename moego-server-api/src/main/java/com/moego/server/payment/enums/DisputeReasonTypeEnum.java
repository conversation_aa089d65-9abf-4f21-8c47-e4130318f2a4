package com.moego.server.payment.enums;

import lombok.Getter;

@Getter
public enum DisputeReasonTypeEnum {
    UNSPECIFIED(0, "unspecified"),
    BANK_CANNOT_PROCESS(1, "bank_cannot_process"),
    CHECK_RETURNED(2, "check_returned"),
    CREDIT_NOT_PROCESSED(3, "credit_not_processed"),
    CUSTOMER_INITIATED(4, "customer_initiated"),
    DEBIT_NOT_AUTHORIZED(5, "debit_not_authorized"),
    DUPLICATE(6, "duplicate"),
    FRAUDULENT(7, "fraudulent"),
    GENERAL(8, "general"),
    INCORRECT_ACCOUNT_DETAILS(9, "incorrect_account_details"),
    INSUFFICIENT_FUNDS(10, "insufficient_funds"),
    PRODUCT_NOT_RECEIVED(11, "product_not_received"),
    PRODUCT_UNACCEPTABLE(12, "product_unacceptable"),
    SUBSCRIPTION_CANCELED(13, "subscription_canceled"),
    UNRECOGNIZED(14, "unrecognized");

    private final int value;
    private final String description;

    DisputeReasonTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static DisputeReasonTypeEnum fromValue(int value) {
        for (DisputeReasonTypeEnum e : DisputeReasonTypeEnum.values()) {
            if (e.value == value) {
                return e;
            }
        }
        return null;
    }
}
