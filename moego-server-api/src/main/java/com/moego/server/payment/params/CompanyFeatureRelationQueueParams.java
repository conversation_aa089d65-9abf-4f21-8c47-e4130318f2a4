package com.moego.server.payment.params;

import com.moego.common.utils.Pagination;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/10/8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyFeatureRelationQueueParams {
    @Min(1)
    Integer companyId;

    String code;

    Byte enable;

    @Builder.Default
    Byte deleted = 0;

    Pagination pagination;
}
