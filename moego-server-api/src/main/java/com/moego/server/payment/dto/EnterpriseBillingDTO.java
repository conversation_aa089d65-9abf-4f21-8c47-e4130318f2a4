package com.moego.server.payment.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.moego.server.business.dto.EnterpriseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class EnterpriseBillingDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(type = "string", description = "enterprise id")
    private Long enterpriseId;

    EnterpriseDto enterprise;
    EnterpriseBillingDataDTO billingData;
}
