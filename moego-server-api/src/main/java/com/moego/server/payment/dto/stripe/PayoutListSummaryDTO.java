package com.moego.server.payment.dto.stripe;

import java.math.BigDecimal;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/3/12
 */
@Setter
@Getter
@Builder(toBuilder = true)
public class PayoutListSummaryDTO {

    @Builder.Default
    private BigDecimal totalCollected = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal totalPayout = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal netSales = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal grossSales = BigDecimal.ZERO;
}
