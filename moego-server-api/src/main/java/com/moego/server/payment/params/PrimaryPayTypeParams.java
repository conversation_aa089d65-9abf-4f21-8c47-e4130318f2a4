package com.moego.server.payment.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrimaryPayTypeParams {
    @NotNull
    private Integer businessId;

    @NotNull
    @Schema(description = "首选信用卡支付方式 0 默认; 1 stripe; 2 square")
    @Min(0)
    @Max(2)
    private Byte primaryPayType;
}
