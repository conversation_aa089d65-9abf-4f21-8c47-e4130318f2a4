package com.moego.server.payment.params;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/12/15
 */
@Data
@Builder(toBuilder = true)
public class StripeChargeDisputeParams {
    // customer info
    private String customerEmailAddress;
    private String customerName;
    private String billingAddress;
    private String customerPurchaseIp;
    private String customerSignature;
    private String creditVoucher;
    private String govermentOrder;
    private String termsDisclosure;

    @Schema(
            description =
                    "(ID of a file upload) Any communication with the customer that you feel is relevant to your case. "
                            + "Examples include emails proving that the customer received the product or service, "
                            + "or demonstrating their use of or satisfaction with the product or service.")
    private String customerCommunication;

    @Schema(
            description =
                    "ny server or activity logs showing proof that the customer accessed or downloaded the purchased digital product."
                            + " This information should include IP addresses, corresponding timestamps, and any detailed recorded activity. Has a maximum character count of 20,000.")
    private String accessActivityLog;

    @Schema(description = "file id for subscription")
    private String cancellationPolicy;

    @Schema(
            description =
                    "An explanation of how and when the customer was shown your refund policy prior to purchase. Has a maximum character count of 20,000.")
    private String cancellationPolicyDisclosure;

    @Schema(
            description =
                    "A justification for why the customer’s subscription was not canceled. Has a maximum character count of 20,000.")
    private String cancellationRebuttal;

    // refund group: file id/disclosure/explanation
    private String refundPolicy;
    private String refundPolicyDisclosure;
    private String refundRefusalExplanation;

    // charge group
    @Schema(
            description =
                    "(ID of a file upload) Documentation for the prior charge that can uniquely identify the charge,"
                            + " such as a receipt, shipping label, work order, etc. "
                            + "This document should be paired with a similar document from the disputed payment that proves the two payments are separate.")
    private String duplicateChargeDocumentation;

    private String duplicateChargeExplanation;
    private String duplicateChargeId;

    @Schema(
            description =
                    "A description of the product or service that was sold. Has a maximum character count of 20,000.")
    private String productDescription;

    @Schema(
            description =
                    "(ID of a file upload) Any receipt or message sent to the customer notifying them of the charge.")
    private String receipt;

    @Schema(
            description =
                    "The date on which the customer received or began receiving the purchased service, in a clear human-readable format.")
    private String serviceDate;

    @Schema(
            description =
                    "(ID of a file upload) Documentation showing proof that a service was provided to the customer."
                            + " This could include a copy of a signed contract, work order, or other form of written agreement.")
    private String serviceDocumentation;
    // extra info
    @Schema(description = "(ID of a file upload) Any additional evidence or statements.")
    private String uncategorizedFile;

    @Schema(description = "Any additional evidence or statements. Has a maximum character count of 20,000.")
    private String uncategorizedText;

    // shipping group
    private String shippingAddress;
    private String shippingCarrier;
    private String shippingDate;

    @Schema(
            description =
                    "(ID of a file upload) Documentation showing proof that a product was shipped to the customer at the same address the customer provided to you."
                            + " This could include a copy of the shipment receipt, shipping label, etc. It should show the customer’s full shipping address, if possible.")
    private String shippingDocumentation;

    private String shippingTrackingNumber;

    private Map<String, Object> evidenceExtra;

    private Map<String, Object> extra;
}
