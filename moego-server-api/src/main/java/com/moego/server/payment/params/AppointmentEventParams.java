package com.moego.server.payment.params;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class AppointmentEventParams {

    private Integer businessId;
    private Long companyId;

    private Integer ticketId;

    private Integer invoiceId;

    private BigDecimal preAuthAmount;

    private String preAuthPaymentMethod;

    private String preAuthCardNumber;

    private Integer customerId;

    private String apptDateStr;

    private Integer apptTime;

    private Boolean preAuthStatus;

    private String event;

    private Integer appointmentSource;

    private Boolean releasePreAuth;

    private List<Integer> ticketIds;
}
