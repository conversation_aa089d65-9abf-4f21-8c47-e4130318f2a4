package com.moego.server.payment.dto.refund;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/1/2
 */
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class TransactionHistoryView {
    // unique identifier for the payment
    private Long id;
    // module name
    private String module;
    // module invoice id
    private Integer invoiceId;
    // customer id
    private Integer customerId;
    // staff id
    private Integer staffId;
    // payment method
    private String method;
    // payment amount
    private BigDecimal amount;
    // status
    private String status;
    // create time
    private Long createTime;
    // update time
    private Long updateTime;
    // check number
    private String checkNumber;
    // card type
    private String cardType;
    // card number
    private String cardNumber;
    // expiry month
    private String expMonth;
    // expiry year
    private String expYear;
    // signature
    private String signature;
    // paid by
    private String paidBy;
    // description
    private String description;
    // method id
    private Integer methodId;
    // stripe payment intent id
    private String stripeIntentId;
    // stripe client secret
    private String stripeClientSecret;
    // business id
    private Integer businessId;
    // stripe charge id
    private String stripeChargeId;
    // is online
    private Boolean isOnline;
    // currency, e.g. usd refer to https://en.wikipedia.org/wiki/ISO_4217
    private String currency;
    // grooming id
    private Long groomingId;
    // square customer id
    private String squareCustomerId;
    // square location id, not null if it is square payment
    private String locationId;
    // square payment method: 1 - card, 2 - card on file, 3- terminal, 4 - reader sdk
    private String squarePaymentMethod;
    // square device id if paid by terminal
    private String deviceId;
    // square terminal payment checkout id
    private String squareCheckoutId;
    // credit card vendor
    private String vendor;
    // merchant id when taking payment
    private String merchant;
    // square cancel reason
    private String cancelReason;
    // tips for square
    private Double tips;
    // processing fee from stripe or square, sync from webhook
    private Double processingFee;
    // 1:yes 0:no
    private Integer isDeposit;
    // stripe payment method: 1 - card, 2 - card on file, 3- bluetooth, 4 - smart reader
    private String stripePaymentMethod;
    // synced from stripe: credit/debit/prepay/unknown
    private String cardFunding;
    // company id
    private Long companyId;
}
