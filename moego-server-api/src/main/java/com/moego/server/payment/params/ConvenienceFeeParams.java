package com.moego.server.payment.params;

import com.moego.common.enums.PaymentStripeStatus;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/7/4
 */
@Data
@Accessors(chain = true)
public class ConvenienceFeeParams {
    private Long orderId;
    private Long businessId;
    private BigDecimal remainAmount;
    /**
     * @see PaymentStripeStatus
     */
    private Byte paymentMethod;
}
