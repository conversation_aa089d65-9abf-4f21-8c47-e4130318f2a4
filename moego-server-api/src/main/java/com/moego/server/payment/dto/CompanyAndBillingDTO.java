package com.moego.server.payment.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.moego.server.business.dto.StaffCompanyDto;
import com.moego.server.message.dto.BusinessLineDto;
import com.moego.server.message.dto.EmailReportItem;
import com.moego.server.message.dto.MessageReportItem;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/3/12 11:56 AM
 */
@Data
public class CompanyAndBillingDTO {

    Integer companyId;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(type = "string", description = "company owner id")
    Long companyOwnerId;

    String companyName;
    Integer locationNum;
    Integer vansNum;
    String country;
    Integer subscriptionAmount;

    BusinessLineDto businessLine;

    @Schema(description = "locations under this company")
    List<StaffCompanyDto> businesses;

    @Schema(description = "billing data for company owner, or this filed will be null")
    BillingDataDTO companyBillingData;

    @SuppressFBWarnings("BX_UNBOXING_IMMEDIATELY_REBOXED")
    public void setSubscriptionAmount(Integer subAmount) {
        this.subscriptionAmount = subAmount == null ? 0 : subAmount;
    }

    @Schema(description = "AS: company 是否迁移 1-迁移前 2-迁移后")
    Integer version;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(type = "string", description = "enterprise id")
    Integer enterpriseId;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(type = "string", description = "enterprise owner id")
    Long enterpriseOwnerId;

    @Schema(description = "message report item for company")
    MessageReportItem messageReportItem;

    @Schema(description = "email report item for company")
    EmailReportItem emailReportItem;
}
