package com.moego.server.payment.api;

import com.moego.server.payment.dto.AdminCustomizedPaymentSettingDTO;
import com.moego.server.payment.dto.AdminEnterpriseCustomizedPaymentSettingDTO;
import com.moego.server.payment.dto.CustomizedPaymentSettingVO;
import com.moego.server.payment.params.CompanyCustomFeeQueryParams;
import com.moego.server.payment.params.EnterpriseCustomFeeQueryParams;
import com.moego.server.payment.params.InsertCustomizedPaymentSettingParams;
import com.moego.server.payment.params.InsertEnterpriseCustomizedPaymentSettingParams;
import com.moego.server.payment.params.SyncEnterpriseCustomizedPaymentSettingParams;
import com.moego.server.payment.params.UpdateCustomizedPaymentSettingParams;
import com.moego.server.payment.params.UpdateEnterpriseCustomizedPaymentSettingParams;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import java.util.Set;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2022/11/1
 */
@Validated
public interface IPaymentCustomizedSettingService {
    /**
     * Get customized payment setting by company id.
     *
     * @param companyId company id
     * @return customized payment setting
     */
    @GetMapping("/service/payment/customize-payment-setting")
    CustomizedPaymentSettingVO getByCompanyId(@RequestParam @Min(0) Integer companyId);

    @PostMapping("/service/payment/customize-payment-setting/query")
    AdminCustomizedPaymentSettingDTO queryByPages(@RequestBody CompanyCustomFeeQueryParams request);
    /**
     * Get by id.
     *
     * @param id id
     * @return customized payment setting
     */
    @GetMapping("/service/payment/customize-payment-setting/{id}")
    CustomizedPaymentSettingVO getById(@PathVariable @Min(0) Integer id);

    /**
     * Insert a customized payment setting record.
     *
     * @param request insert request params
     * @return number of rows affected
     */
    @PostMapping("/service/payment/customize-payment-setting")
    int insert(@RequestBody @Valid InsertCustomizedPaymentSettingParams request);

    /**
     * Update a customized payment setting record.
     *
     * @param request update request params
     * @return number of rows affected
     */
    @PutMapping("/service/payment/customize-payment-setting")
    int update(@RequestBody @Valid UpdateCustomizedPaymentSettingParams request);

    /**
     * Batch delete customized payment setting.
     *
     * @param ids ids
     * @return number of rows affected
     */
    @DeleteMapping("/service/payment/customize-payment-setting")
    int delete(@RequestBody Set<@Min(0) Integer> ids);

    @GetMapping("/service/payment/customize-payment-setting/vol/check")
    void triggerDailyVolCheck();

    // 手动补偿,根据vol_check表主键id
    @GetMapping("/service/payment/customize-payment-setting/vol/check/{id}")
    void triggerDailyVolCheckById(@PathVariable Integer id);

    @PostMapping("/service/payment/enterprise/setting/query")
    AdminEnterpriseCustomizedPaymentSettingDTO listEnterpriseCusPaySettings(
            @RequestBody @Valid EnterpriseCustomFeeQueryParams request);

    @PostMapping("/service/payment/enterprise/setting/insert")
    int insertEnterpriseCusPaySetting(@RequestBody @Valid InsertEnterpriseCustomizedPaymentSettingParams request);

    @PutMapping("/service/payment/enterprise/setting/update")
    int updateEnterpriseCusPaySetting(@RequestBody @Valid UpdateEnterpriseCustomizedPaymentSettingParams request);

    @DeleteMapping("/service/payment/enterprise/setting/delete")
    int deleteEnterpriseCusPaySetting(@RequestParam Long id);

    @PostMapping("/service/payment/enterprise/setting/sync")
    int syncEnterpriseCusPaySetting(@RequestBody @Valid SyncEnterpriseCustomizedPaymentSettingParams request);
}
