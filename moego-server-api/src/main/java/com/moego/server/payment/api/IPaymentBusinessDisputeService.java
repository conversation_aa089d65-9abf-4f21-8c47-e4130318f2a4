package com.moego.server.payment.api;

import com.moego.server.payment.dto.StripeDisputeInfo;
import com.moego.server.payment.dto.dispute.StripeDisputeDetailDTO;
import com.moego.server.payment.params.StripeChargeDisputeParams;
import com.moego.server.payment.params.dispute.QueryStripeDisputeParams;
import com.moego.server.payment.params.dispute.UploadFileParams;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2023/12/14
 */
public interface IPaymentBusinessDisputeService {

    @PostMapping("/service/payment/business-dispute/list")
    StripeDisputeDetailDTO queryStripeDisputeList(@RequestBody @Validated QueryStripeDisputeParams params);

    @PostMapping("/service/payment/business-dispute/upload/file")
    String uploadDisputeDocument(@RequestBody @Validated UploadFileParams params);

    @PostMapping("/service/payment/business-dispute/update/load-evidence")
    StripeDisputeInfo updateLoadEvidence(
            @RequestParam("disputeId") String disputeId,
            @RequestBody @Validated StripeChargeDisputeParams evidenceParams);

    @PostMapping("/service/payment/business-dispute/get/dispute-info/dispute-id")
    StripeDisputeInfo getDisputeEvidenceInfoByDisputeId(@RequestParam("disputeId") String disputeId);
}
