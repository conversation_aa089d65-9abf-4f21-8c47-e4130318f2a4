package com.moego.server.payment.params;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.moego.server.payment.enums.CouponBusinessCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/26
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CouponParams {

    @NotNull
    String customName;

    @Min(1)
    @Max(100)
    @Schema(description = "折扣比例，整数，1到100，（默认值50，前端可不传）")
    Integer percentOff;

    // 这里限制最大1000刀，前端透传的金额转换为stripe的参数需要*100，所有这里限制为100000
    @Max(100000)
    @Schema(
            description =
                    "折扣金额，A positive integer representing the amount to subtract from an invoice total (required if percent_off is not passed)")
    Integer amountOff;

    @Schema(
            description =
                    "Specifies how long the discount will be in effect if used on a subscription. Defaults to once")
    String duration;

    @Max(100)
    @Min(1)
    Long validMonth;

    @Schema(
            description =
                    "Unix timestamp specifying the last time at which the coupon can be redeemed. After the redeem_by date, the coupon can no longer be applied to new customers")
    Long redeemBy;

    @Schema(
            description =
                    "A positive integer specifying the number of times the coupon can be redeemed before it’s no longer valid. For example, you might have a 50% off coupon that the first 20 readers of your blog can use")
    Long maxRedemptions;

    @Schema(description = "coupon 所属业务")
    CouponBusinessCategoryEnum businessCategory;
}
