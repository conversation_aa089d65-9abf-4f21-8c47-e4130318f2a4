package com.moego.server.payment.params.stripe;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/1/15
 */
@Data
@Builder(toBuilder = true)
public class StripePersonParams {
    /**
     * The account the person is associated with.
     */
    @Schema(description = "account")
    String account;

    /**
     * The person id
     */
    String id;

    /**
     * The person's address.
     */
    @Schema(description = "address")
    StripeAddressParams address;

    /**
     * One or more documents that demonstrate proof that this person is authorized to represent the company.
     * file id list
     */
    @Schema(description = "document")
    List<String> identityDocument;
    /**
     * The person's date of birth.
     */
    @Schema(description = "dob")
    Dob dob;
    /**
     * The person's email address.
     */
    @Schema(description = "email")
    String email;
    /**
     * Specifies which fields in the response should be expanded.
     */
    @Schema(description = "expand")
    List<String> expand;
    /**
     * The person's first name.
     */
    @Schema(description = "first_name")
    String firstName;
    /**
     * A list of alternate names or aliases that the person is known by.
     */
    @Schema(description = "full_name_aliases")
    List<String> fullNameAliases;
    /**
     * The person's gender (International regulations require either &quot;male&quot; or
     * &quot;female&quot;).
     */
    @Schema(description = "gender")
    String gender;
    /**
     * The person's ID number, as appropriate for their country. For example, a social security number
     * in the U.S., social insurance number in Canada, etc. Instead of the number itself, you can also
     * provide a <a href="https://stripe.com/docs/js/tokens_sources/create_token?type=pii">PII token
     * provided by Stripe.js</a>.
     */
    @Schema(description = "id_number")
    String idNumber;
    /**
     * The person's secondary ID number, as appropriate for their country, will be used for enhanced
     * verification checks. In Thailand, this would be the laser code found on the back of an ID card.
     * Instead of the number itself, you can also provide a <a
     * href="https://stripe.com/docs/js/tokens_sources/create_token?type=pii">PII token provided by
     * Stripe.js</a>.
     */
    @Schema(description = "id_number_secondary")
    String idNumberSecondary;
    /**
     * The person's last name.
     */
    @Schema(description = "last_name")
    String lastName;
    /**
     * The person's maiden name.
     */
    @Schema(description = "maiden_name")
    String maidenName;
    /**
     * Set of <a href="https://stripe.com/docs/api/metadata">key-value pairs</a> that you can attach
     * to an object. This can be useful for storing additional information about the object in a
     * structured format. Individual keys can be unset by posting an empty value to them. All keys can
     * be unset by posting an empty value to {@code metadata}.
     */
    @Schema(description = "metadata")
    Object metadata;
    /**
     * The country where the person is a national. Two-letter country code (<a
     * href="https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2">ISO 3166-1 alpha-2</a>), or
     * &quot;XX&quot; if unavailable.
     */
    @Schema(description = "nationality")
    String nationality;
    /**
     * A <a href="https://stripe.com/docs/connect/account-tokens">person token</a>, used to securely
     * provide details to the person.
     */
    @Schema(description = "person_token")
    String personToken;
    /**
     * The person's phone number.
     */
    @Schema(description = "phone")
    String phone;

    /**
     * The relationship that this person has with the account's legal entity.
     */
    @Schema(description = "relationship")
    Relationship relationship;

    /**
     * The last four digits of the person's Social Security number (U.S. only).
     */
    @Schema(description = "ssn_last_4")
    String ssnLast4;

    /**
     * The person's verification status.
     */
    @Schema(description = "verification")
    Verification verification;

    @Data
    @Builder(toBuilder = true)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Dob {
        /**
         * The day of birth, between 1 and 31.
         */
        @Schema(description = "day")
        Long day;
        /**
         * The month of birth, between 1 and 12.
         */
        @Schema(description = "month")
        Long month;
        /**
         * The four-digit year of birth.
         */
        @Schema(description = "year")
        Long year;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Relationship {
        /**
         * Whether the person is a director of the account's legal entity. Directors are typically
         * members of the governing board of the company, or responsible for ensuring the company meets
         * its regulatory obligations.
         */
        @Schema(description = "director")
        Boolean director;
        /**
         * Whether the person has significant responsibility to control, manage, or direct the
         * organization.
         */
        @Schema(description = "executive")
        Boolean executive;

        /**
         * Whether the person is an owner of the account’s legal entity.
         */
        @Schema(description = "owner")
        Boolean owner;
        /**
         * The percent owned by the person of the account's legal entity.
         */
        @Schema(description = "percent_ownership")
        BigDecimal percentOwnership;
        /**
         * Whether the person is authorized as the primary representative of the account. This is the
         * person nominated by the business to provide information about themselves, and general
         * information about the account. There can only be one representative at any given time. At the
         * time the account is created, this person should be set to the person responsible for opening
         * the account.
         */
        @Schema(description = "representative")
        Boolean representative;
        /**
         * The person's title (e.g., CEO, Support Engineer).
         */
        @Schema(description = "title")
        String title;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Verification {
        /**
         * A document showing address, either a passport, local ID card, or utility bill from a
         * well-known utility company.
         */
        @Schema(description = "additional_document")
        AdditionalDocument additionalDocument;
        /**
         * An identifying document, either a passport or local ID card.
         */
        @Schema(description = "document")
        Document document;

        @Data
        @Builder(toBuilder = true)
        @NoArgsConstructor
        @AllArgsConstructor
        public static class AdditionalDocument {
            /**
             * The back of an ID returned by a <a href="https://stripe.com/docs/api#create_file">file
             * upload</a> with a {@code purpose} value of {@code identity_document}. The uploaded file
             * needs to be a color image (smaller than 8,000px by 8,000px), in JPG, PNG, or PDF format,
             * and less than 10 MB in size.
             */
            @Schema(description = "back")
            String back;
            /**
             * The front of an ID returned by a <a href="https://stripe.com/docs/api#create_file">file
             * upload</a> with a {@code purpose} value of {@code identity_document}. The uploaded file
             * needs to be a color image (smaller than 8,000px by 8,000px), in JPG, PNG, or PDF format,
             * and less than 10 MB in size.
             */
            @Schema(description = "front")
            String front;
        }

        @Data
        @Builder(toBuilder = true)
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Document {
            /**
             * The back of an ID returned by a <a href="https://stripe.com/docs/api#create_file">file
             * upload</a> with a {@code purpose} value of {@code identity_document}. The uploaded file
             * needs to be a color image (smaller than 8,000px by 8,000px), in JPG, PNG, or PDF format,
             * and less than 10 MB in size.
             */
            @Schema(description = "back")
            String back;
            /**
             * The front of an ID returned by a <a href="https://stripe.com/docs/api#create_file">file
             * upload</a> with a {@code purpose} value of {@code identity_document}. The uploaded file
             * needs to be a color image (smaller than 8,000px by 8,000px), in JPG, PNG, or PDF format,
             * and less than 10 MB in size.
             */
            @Schema(description = "front")
            String front;
        }
    }
}
