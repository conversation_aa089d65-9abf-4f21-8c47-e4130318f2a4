package com.moego.server.payment.client;

import com.moego.server.payment.api.IPaymentTransactionHistoryService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @since 2024/1/2
 */
@FeignClient(
        value = "moego-payment-server",
        url = "${moego.server.url.payment}",
        contextId = "IPaymentTransactionHistoryClient")
public interface IPaymentTransactionHistoryClient extends IPaymentTransactionHistoryService {}
