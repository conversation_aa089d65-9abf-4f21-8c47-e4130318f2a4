package com.moego.server.payment.params;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class UpdateInvoiceAndCaptureParams {

    @NotNull
    private Integer businessId;

    @NotNull
    private Integer customerId;

    @NotNull
    private Integer ticketId;

    @NotNull
    private Integer invoiceId;

    @NotNull
    private Boolean needCapture;
}
