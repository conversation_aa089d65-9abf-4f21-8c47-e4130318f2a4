package com.moego.server.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(description = "邮件包购买结果")
public class EmailBillingDTO {

    @Schema(description = "是否自动购买：  0：不自动购买  1：自动购买")
    private Byte autoReload;

    @Schema(description = "邮件 credit 数量")
    Integer amount;

    @Schema(description = "对应价格")
    BigDecimal price;

    @Schema(description = "email支付主体 1-company 2-enterprise")
    private Integer payerType;
}
