package com.moego.server.payment.params;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class UpdateEnterpriseCustomizedPaymentSettingParams {

    @Min(1)
    private Long id;

    @Min(1)
    private Long enterpriseId;

    @Min(0)
    private Long customizedMinVol;

    @Min(0)
    @Max(100)
    private BigDecimal onlineFeeRate;

    @Min(0)
    @Max(100)
    private Integer onlineFeeCents;

    @Min(0)
    @Max(100)
    private BigDecimal readerFeeRate;

    @Min(0)
    @Max(100)
    private Integer readerFeeCents;
}
