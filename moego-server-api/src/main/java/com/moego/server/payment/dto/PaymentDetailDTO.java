package com.moego.server.payment.dto;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class PaymentDetailDTO {
    private Integer id;
    private String module;
    private Integer invoiceId;
    private Integer customerId;
    private Integer staffId;
    private String method;
    private BigDecimal amount;
    private Byte status;
    private Long createTime;
    private Long updateTime;
    private String checkNumber;
    private String cardType;
    private String cardNumber;
    private String expMonth;
    private String expYear;
    private String signature;
    private String paidBy;
    private String description;
    private Integer methodId;
    private String stripeIntentId;
    private String stripeClientSecret;
    private Integer businessId;
    private String stripeChargeId;
    private Boolean isOnline;
    private String currency;
    private Integer groomingId;
    private String squareCustomerId;
    private String locationId;
    private Byte squarePaymentMethod;
    private String deviceId;
    private String squareCheckoutId;
    private String vendor;
    private String merchant;
    private String cancelReason;
    private BigDecimal tips;
    private BigDecimal processingFee;
    private Byte isDeposit;
    private Byte stripePaymentMethod;
    private String cardFunding;
    private Long companyId;
    private BigDecimal grossSales;
    private BigDecimal discount;
    private BigDecimal tax;
    private BigDecimal bookingFee;
    private BigDecimal convenienceFee;
}
