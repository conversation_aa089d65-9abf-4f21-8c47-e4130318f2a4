package com.moego.server.payment.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/11/8
 */
@Getter
public enum MonitorLinkStatusTaskTypeEnum {
    DAY_CONVERSATION(1, "Daily statistics"),
    WEEKLY_CONVERSATION(2, "weekly statistics"),
    WEEKLY_SUCCESS(3, "success statistics"),
    ;
    private final Integer code;
    private final String desc;

    MonitorLinkStatusTaskTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
