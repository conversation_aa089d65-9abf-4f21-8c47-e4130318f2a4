package com.moego.server.payment.dto;

import com.moego.common.dto.NewPricingLevelDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/2/22 3:29 PM
 * php old version:
 * {
 *     "code":200,
 *     "message":"Operation is successful !",
 *     "timestamp":1613978600,
 *     "data":{
 *         "is_subscription":1,
 *         "current_plan":"cool solo - $39.00/Month",
 *         "started_date":"02/10/2021",
 *         "status":"Ends on 03/10/2021",
 *         "card":"Yi Done Expiration date 01/22",
 *         "remind":"Your subscription has been canceled and will not renew after 03/10/2021",
 *         "is_new_pricing":"1",
 *         "is_auto_reload":1,
 *         "auto_reload_plan":"$25.00 for 500 messages",
 *         "charge_list":[
 *             "02/18/2021 $25.00 for 500 messages",
 *             "02/18/2021 $10.00 for 100 messages",
 *             "02/18/2021 $10.00 for 100 messages"
 *         ],
 *         "msg_plan_id":"3",
 *         "stripe_subscriptions_id":"sub_HG1cczU5L19r4d"
 *     }
 * }
 */
@Data
@Schema(description = "data object")
public class BillingDataDTO {

    private Integer companyId;

    @Schema(description = "当月套餐开始日期")
    private Long beginDate;

    @Schema(description = "当月套餐结束日期 ")
    private Long expireDate;

    @Schema(description = "订阅套餐显示名字")
    @Deprecated
    private String title;

    @Schema(description = "套餐价格")
    private BigDecimal price;

    private int vansNum;
    private int salonNum;

    @Schema(description = "是否自动续费，0-不自动续费，1-自动续费")
    private Byte autoRenew;

    @Schema(description = "credit card info")
    @Deprecated
    private CompanyCardDTO cardInfo;

    @Schema(description = "all cards in the company")
    private List<CompanyCardDTO> cardList;

    @Schema(description = "isSubscription=true : 表示已订阅")
    private Boolean isSubscription;

    @Schema(description = "套餐类型，0-月套餐，1-年套餐")
    private Byte planType;

    @Schema(description = "短信包购买信息")
    private MessageBillingDTO msgBilling;

    @Schema(description = "邮件包购买信息")
    private EmailBillingDTO emailBilling;

    @Schema(description = "商家扣费记录")
    private List<ChargeDTO> charges;

    @Schema(description = "公司permissionLevel: 0:free  1:39begin  2:69rising")
    @Deprecated
    private Integer permissionLevel;

    @Schema(description = "charge failed time")
    private Long chargeFailedTime;

    private Long firstChargeFailedTime;

    @Schema(description = "planVersion and premiumType")
    private NewPricingLevelDto planTier;

    private Boolean isDowngrade;
    private PlanData downgradePlanData;

    @Schema(description = "付费状态下，即首次付费时间")
    private Long createTime;

    private Long updateTime;

    @Schema(description = "修改套餐前/旧套餐的详情")
    private NewPricingLevelDto previousPlanTier;

    @Schema(description = "当前套餐的详情，cancel 后保持不变")
    private NewPricingLevelDto currentPlanTier;

    @Schema(description = "订阅的支付主体 1-company 2-enterprise")
    private Integer payerType;
}
