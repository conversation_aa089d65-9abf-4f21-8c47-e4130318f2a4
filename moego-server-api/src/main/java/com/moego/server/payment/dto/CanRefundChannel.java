package com.moego.server.payment.dto;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CanRefundChannel {
    private String paymentMethod;

    @NotNull
    private Integer paymentId;

    @NotNull
    private BigDecimal canRefundAmount;
}
