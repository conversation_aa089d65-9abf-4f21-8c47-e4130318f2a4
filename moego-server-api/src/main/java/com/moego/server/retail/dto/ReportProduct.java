package com.moego.server.retail.dto;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class ReportProduct {

    private Integer productId;
    private String productName;
    private String description;
    private Integer soldNum;
    // product 单个进货成本
    private BigDecimal supplierCost;
    // product 总的进货成本 = supplierCost * soldNum
    private BigDecimal totalCost;
    // product 总的 sale 金额，不包括 tips、tax、discount
    private BigDecimal grossSale;
    // product 总的利润金额，grossSale - supplierCost
    private BigDecimal grossProfit;

    private Integer stockNum;
}
