package com.moego.server.retail.dto;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class ProductInfoDTO {

    private Integer id;

    private Integer businessId;

    private String name;

    private String imageUrl;

    private String description;

    private String sku;

    private Integer supplierId;

    private Integer categoryId;

    private BigDecimal supplyPrice;

    private BigDecimal taxRate;

    private BigDecimal retailPrice;

    private BigDecimal specialPrice;

    private Boolean enableStaffCommission;

    private Long createTime;

    private Long updateTime;

    private Integer taxId;

    private Integer stock;

    private Boolean deleted;

    private String barcode;

    private String categoryName;

    private String supplierName;
}
