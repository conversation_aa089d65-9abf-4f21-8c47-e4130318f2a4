package com.moego.server.retail.param;

import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class CreateProductParams {

    private Long companyId;

    private Integer businessId;

    @Size(max = 191)
    private String name;

    @Size(max = 511)
    private String imageUrl;

    @Size(max = 1023)
    private String description;

    @Size(max = 50)
    private String sku;

    private Integer supplierId;
    private Integer categoryId;
    private BigDecimal supplyPrice;
    private Integer taxId;
    private BigDecimal retailPrice;
    private BigDecimal specialPrice;

    @Size(max = 128)
    private String barcode;

    private Boolean enableStaffCommission;
    private Integer stock;
    private Integer addedStock;
}
