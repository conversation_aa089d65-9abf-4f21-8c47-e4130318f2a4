package com.moego.server.retail.api;

import com.moego.server.retail.dto.CreateCategoryResult;
import com.moego.server.retail.dto.CreateProductResult;
import com.moego.server.retail.dto.CreateSupplierResult;
import com.moego.server.retail.dto.DescribeProductsDTO;
import com.moego.server.retail.dto.ListCategoriesResult;
import com.moego.server.retail.dto.ListProductsResult;
import com.moego.server.retail.dto.ListSuppliersResult;
import com.moego.server.retail.dto.ProductInfoDTO;
import com.moego.server.retail.param.CreateCategoryParams;
import com.moego.server.retail.param.CreateProductParams;
import com.moego.server.retail.param.CreateSupplierParams;
import com.moego.server.retail.param.DescribeProductsParams;
import com.moego.server.retail.param.ListCategoriesParams;
import com.moego.server.retail.param.ListProductsParams;
import com.moego.server.retail.param.ListSuppliersParams;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IProductService {
    @PostMapping("/service/retail/product/list")
    List<ProductInfoDTO> getProductInfoList(
            @RequestParam("businessId") Integer businessId, @RequestBody List<Integer> productIdList);

    @PostMapping("/service/retail/product/describeProducts")
    DescribeProductsDTO describeProducts(@RequestBody @Valid DescribeProductsParams describeProductsParams);

    @PostMapping("/service/retail/product/listProducts")
    ListProductsResult listProducts(@RequestBody @Valid ListProductsParams params);

    @PostMapping("/service/retail/product/listCategories")
    ListCategoriesResult listCategories(@RequestBody @Valid ListCategoriesParams params);

    @PostMapping("/service/retail/product/listSuppliers")
    ListSuppliersResult listSuppliers(@RequestBody @Valid ListSuppliersParams params);

    @PostMapping("/service/retail/product/createProduct")
    CreateProductResult createProduct(@RequestBody @Valid CreateProductParams params);

    @PostMapping("/service/retail/product/createCategory")
    CreateCategoryResult createCategory(@RequestBody @Valid CreateCategoryParams params);

    @PostMapping("/service/retail/product/createSupplier")
    CreateSupplierResult createSupplier(@RequestBody @Valid CreateSupplierParams params);
}
