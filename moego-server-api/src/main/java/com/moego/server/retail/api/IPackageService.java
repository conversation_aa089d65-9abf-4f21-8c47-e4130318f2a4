package com.moego.server.retail.api;

import com.moego.server.retail.dto.CreatePackageResult;
import com.moego.server.retail.dto.ListPackagesResult;
import com.moego.server.retail.param.CreatePackageParams;
import com.moego.server.retail.param.GetPackageParams;
import com.moego.server.retail.param.ListPackagesByIdsParams;
import com.moego.server.retail.param.ListPackagesParams;
import com.moego.server.retail.result.GetPackageResult;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface IPackageService {
    @PostMapping("/service/retail/package/get")
    GetPackageResult getPackage(@RequestBody @Valid GetPackageParams params);

    @PostMapping("/service/retail/package/listPackages")
    ListPackagesResult listPackages(@RequestBody @Valid ListPackagesParams params);

    @PostMapping("/service/retail/package/listPackagesByIds")
    ListPackagesResult listPackagesByIds(@RequestBody @Valid ListPackagesByIdsParams params);

    @PostMapping("/service/retail/package/createPackage")
    CreatePackageResult createPackage(@RequestBody @Valid CreatePackageParams params);
}
