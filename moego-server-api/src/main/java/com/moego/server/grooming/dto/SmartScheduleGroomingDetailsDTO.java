package com.moego.server.grooming.dto;

import com.moego.idl.models.offering.v1.ServiceItemType;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/8/19 3:44 PM
 */
@Data
public class SmartScheduleGroomingDetailsDTO {

    // pet_detail_id or evaluation_id
    private Integer id;
    private Integer petId;
    private Integer groomingId;
    private Long startTime;
    private Long endTime;
    private Integer serviceId;
    private Integer staffId;
    private Integer customerId;
    private String appointmentDate;
    private String startDate;
    private ServiceItemType serviceItemType;
    private Boolean isBlock = false;
}
