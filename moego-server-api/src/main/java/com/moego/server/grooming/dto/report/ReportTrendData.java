package com.moego.server.grooming.dto.report;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Set;

public record ReportTrendData(
        String startDate,
        String endDate,
        Map<String, BigDecimal> collectedRevMap,
        Map<String, BigDecimal> expectedRevMap,
        Map<String, Integer> apptNumMap,
        Map<String, Set<String>> petAndGroomIdMap,
        Map<String, Set<Integer>> clientIdMap) {}
