package com.moego.server.grooming.dto;

import lombok.Data;

/**
 * @deprecated by <PERSON>, 这个类已经不满足当前 package service 的模型（多个 service 共享 quantity），use {@link GroomingPackageServiceDTO} instead
 */
@Data
@Deprecated(since = "2024/10/28")
public class GroomingPackageServiceInfoDTO {

    private Integer packageId;
    private Integer packageServiceId;
    private String packageName;
    private Integer serviceId;
    private String serviceName;
    private Integer remainingQuantity;
}
