package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.AbandonedScheduleMessageSettingDTO;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import java.util.Collection;
import java.util.List;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@Validated
public interface IAbandonedScheduleMessageSettingService {

    /**
     * List {@link AbandonedScheduleMessageSettingDTO} by businessIds,
     * if businessIds is empty, return all.
     *
     * @param condition {@link ListByCondition}
     * @return {@link AbandonedScheduleMessageSettingDTO} list
     */
    @PostMapping("/service/grooming/abandoned-schedule-message-setting/listByCondition")
    List<AbandonedScheduleMessageSettingDTO> listByCondition(@RequestBody @Valid ListByCondition condition);

    @Builder(toBuilder = true)
    record ListByCondition(@Nullable Collection<Integer> businessIdIn, @Nullable Collection<Boolean> isEnabledIn) {}
}
