package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.CustomerPetMergeRelationDTO;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface IGroomingMergeService {
    @PostMapping("/service/grooming/merge/customer")
    void mergeGroomingObConfigPackage(@RequestBody @Valid CustomerPetMergeRelationDTO MergeRelation);
}
