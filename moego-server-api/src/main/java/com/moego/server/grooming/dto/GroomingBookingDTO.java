package com.moego.server.grooming.dto;

import com.moego.server.grooming.dto.waitlist.WaitListCompatibleDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class GroomingBookingDTO {

    private Integer groomingId;
    private Long businessId;
    private Long createTime;
    private String appointmentDate;
    private Integer appointmentStartTime;
    private Integer appointmentEndTime;
    private Integer bookOnlineStatus;
    private Byte isPaid;
    private Integer customerId;
    private String customerFirstName;
    private String customerLastName;
    private String phoneNumber;
    private Integer outOfArea;
    private Integer status;
    private String colorCode;
    private Integer groomingCount;

    private List<GroomingCustomerPetdetailDTO> petList;

    @Schema(description = "已支付金额")
    private BigDecimal paidAmount;

    @Schema(description = "预先支付的金额")
    private BigDecimal prepaidAmount;

    @Schema(description = "退款金额")
    private BigDecimal refundAmount;

    @Schema(description = "预支付金额占总金额的比例")
    private Double prepayRate;

    @Schema(description = "预支付记录状态，参考BookOnlineDepositConst")
    private Byte prepayStatus;

    private Boolean isNewCustomer;
    private String avatarPath;
    private Integer staffId;
    private String staffFirstName;
    private String staffLastName;

    @Schema(description = "has customer profile request update")
    private boolean hasRequestUpdate;

    private Boolean noStartTime;

    private Boolean enablePreAuth;
    // 老版 ob waitList 查询接口兼容字段
    private WaitListCompatibleDTO waitListDTO;
}
