package com.moego.server.grooming.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class CustomerGroomingAppointmentDTO {

    private Integer id;
    private Integer customerId;
    private String orderId;
    private String appointmentDate;
    private Integer appointmentStartTime;
    private Integer appointmentEndTime;

    private List<CustomerGroomingAppointmentPetDetailDTO> petDetails;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(type = "string", description = "business id")
    private Integer businessId;
}
