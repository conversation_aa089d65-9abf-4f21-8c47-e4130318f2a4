package com.moego.server.grooming.dto;

import com.moego.server.retail.dto.PackageInfoDto;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class GroomingPackageServiceDTO {

    private Integer id;
    private Integer packageId;
    /**
     * @deprecated by <PERSON>, use {@link #services} instead
     */
    @Deprecated(since = "2024/10/25")
    private Integer serviceId;
    /**
     * @deprecated by <PERSON>, use {@link #services} instead
     */
    @Deprecated(since = "2024/10/25")
    private String serviceName;
    /**
     * @deprecated by <PERSON>, use {@link #services} instead
     */
    @Deprecated(since = "2024/10/25")
    private BigDecimal serviceUnitPrice;

    private List<PackageInfoDto.Service> services;

    private Integer totalQuantity;
    private Integer remainingQuantity;
}
