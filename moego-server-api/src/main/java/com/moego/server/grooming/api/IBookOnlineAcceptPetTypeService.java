package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.CompanyBusinessIdDTO;
import com.moego.server.grooming.dto.ob.BookOnlineAcceptPetTypeDTO;
import com.moego.server.grooming.params.ob.BookOnlineAcceptPetTypeUpdateParams;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface IBookOnlineAcceptPetTypeService {

    /**
     * DONE(account structure)
     */
    @PostMapping("/service/grooming/ob/accept/pet/type/get")
    BookOnlineAcceptPetTypeDTO getAcceptPetType(@RequestBody CompanyBusinessIdDTO companyBusinessIdDTO);

    /**
     * DONE(account structure)
     */
    @PostMapping("/service/grooming/ob/accept/pet/type/update")
    Boolean updateAcceptPetTypeList(@RequestBody BookOnlineAcceptPetTypeUpdateParams params);
}
