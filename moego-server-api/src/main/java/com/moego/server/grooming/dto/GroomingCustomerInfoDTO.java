package com.moego.server.grooming.dto;

import java.util.List;
import lombok.Data;

@Data
public class GroomingCustomerInfoDTO {

    private Integer customerId;
    private String lastName;
    private String firstName;
    private String phoneNumber;
    private String ownerPhoneNumber;
    private String clientColor;
    private String clientFullAddress;
    private String email;
    private Integer preferredFrequencyDay;
    private Byte preferredFrequencyType;
    private List<String> tagList;
    private String questionJson;
    private String formJson;
    private Boolean isNewCustomer;

    private Integer referralSourceId;
    private String referralSourceDesc;
    private Integer preferredGroomerId;
    private Integer[] preferredDay;
    private Integer[] preferredTime;
    private Byte sendAutoEmail;
    private Byte sendAutoMessage;
    private Boolean isDeleted;
    private Boolean hasPetParentAppAccount;
}
