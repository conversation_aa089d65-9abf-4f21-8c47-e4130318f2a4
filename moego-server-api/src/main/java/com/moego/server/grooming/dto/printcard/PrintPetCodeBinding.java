package com.moego.server.grooming.dto.printcard;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class PrintPetCodeBinding {
    @Schema(description = "pet code id")
    Long petCodeId;

    @Schema(description = "description")
    String description;

    @Schema(description = "pet code unique comment")
    String uniqueComment;

    @Schema(description = "pet code color")
    String color;

    @Schema(description = "pet code abbreviation")
    String abbreviation;
}
