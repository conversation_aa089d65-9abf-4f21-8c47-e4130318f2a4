package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.GroomingPackageDTO;
import com.moego.server.grooming.dto.GroomingPackageServiceDTO;
import com.moego.server.grooming.dto.GroomingPackageUseDetailDTO;
import com.moego.server.grooming.params.GetPackageParams;
import com.moego.server.grooming.params.GetPackageServicesParams;
import com.moego.server.grooming.params.GetPackagesParams;
import com.moego.server.grooming.params.UpdatePackageParams;
import com.moego.server.grooming.params.UpdatePackageServiceParams;
import com.moego.server.grooming.result.GetPackageResult;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface IGroomingPackageService {
    @PostMapping("/service/grooming/package/get")
    GetPackageResult getPackage(@RequestBody @Valid GetPackageParams getPackageParams);

    @PostMapping("/service/grooming/package/invalidPackageUsedHistory")
    Boolean invalidPackageUsedHistory(@RequestBody List<Integer> invoiceIds);

    @PostMapping("/service/grooming/package/getPackages")
    List<GroomingPackageDTO> getPackages(@RequestBody @Valid GetPackagesParams getPackagesParams);

    @PostMapping("/service/grooming/package/getPackageServices")
    List<GroomingPackageServiceDTO> getPackageServices(
            @RequestBody @Valid GetPackageServicesParams getPackageServicesParams);

    @PostMapping("/service/grooming/package/getGroomingPackageDetail")
    List<GroomingPackageUseDetailDTO> getGroomingPackageDetail(@RequestBody List<Long> groomingPackageIdList);

    @PostMapping("/service/grooming/package/updatePackage")
    Boolean updatePackage(@RequestBody @Valid UpdatePackageParams updatePackageParams);

    @PostMapping("/service/grooming/package/updatePackageService")
    Boolean updatePackageService(@RequestBody @Valid UpdatePackageServiceParams updatePackageServiceParams);
}
