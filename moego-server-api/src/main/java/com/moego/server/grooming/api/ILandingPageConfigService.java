package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.LandingPageConfigDTO;
import java.util.Collection;
import java.util.Map;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
public interface ILandingPageConfigService {
    /**
     * Get landing page configs by business ids.
     *
     * @param businessIds business ids
     * @return key: business id, value: landing page config
     */
    @PostMapping("/service/grooming/landing-page-configs/byBusinessIds")
    Map<Integer, LandingPageConfigDTO> getLandingPageConfigsByBusinessIds(@RequestBody Collection<Integer> businessIds);
}
