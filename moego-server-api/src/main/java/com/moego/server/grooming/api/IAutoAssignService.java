package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.AutoAssignDTO;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
public interface IAutoAssignService {
    /**
     * Get auto-assign record for appointmentId.
     *
     * @param appointmentId appointment id
     * @return auto assign record
     */
    @GetMapping("/service/grooming/getAutoAssign")
    @Nullable
    AutoAssignDTO getAutoAssign(@RequestParam Integer appointmentId);

    /**
     * List auto-assign records for appointmentIds.
     *
     * @param appointmentIds appointment ids
     * @return auto assign records
     */
    @PostMapping("/service/grooming/listAutoAssign")
    List<AutoAssignDTO> listAutoAssign(@RequestBody Collection<Integer> appointmentIds);
}
