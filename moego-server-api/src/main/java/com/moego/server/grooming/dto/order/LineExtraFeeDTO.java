package com.moego.server.grooming.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LineExtraFeeDTO {

    @Schema(description = "记录id")
    private Long id;

    @Schema(description = "订单id")
    private Long orderId;

    @Schema(description = " item id")
    private Long orderItemId;

    @Schema(description = "生效类型：all-订单级别，service - service级别，product - product级别 ，item-单个item")
    private String applyType;

    @Schema(description = "是否删除，正常删除的不会下发")
    private Boolean isDeleted;

    @Schema(description = "fee类型，目前只有：convenienceFee")
    private String feeType;

    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "费用名称")
    private String name;

    @Schema(description = "费用描述")
    private String description;

    @Schema(description = "汇总到订单总费用类型：add/subtract")
    private String collectType;

    @Schema(description = "添加的staff id")
    private Long applyBy;

    @Schema(description = "添加顺序，添加多个的时候会决定计算顺序")
    private Integer applySequence;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}
