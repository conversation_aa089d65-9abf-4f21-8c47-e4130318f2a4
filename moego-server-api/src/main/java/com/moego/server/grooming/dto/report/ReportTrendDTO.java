package com.moego.server.grooming.dto.report;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 5/24/21 5:54 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportTrendDTO {

    private ReportTrendPeriodDTO collectedRev;

    private ReportTrendPeriodDTO expectedRev;

    private ReportTrendPeriodDTO apptNum;

    private ReportTrendPeriodDTO petNum;

    private ReportTrendPeriodDTO clientNum;
}
