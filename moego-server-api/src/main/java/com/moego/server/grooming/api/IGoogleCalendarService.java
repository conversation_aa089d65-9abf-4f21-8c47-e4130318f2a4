package com.moego.server.grooming.api;

import com.moego.server.grooming.params.SyncAppointmentParams;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IGoogleCalendarService {
    /**
     * 从google calendar导入预约
     */
    @GetMapping("/service/grooming/google/calendar/task/import")
    void taskBeginImport();

    /**
     * 导出 google calendar 预约
     */
    @GetMapping("/service/grooming/google/calendar/task/export")
    void taskBeginExport();

    @GetMapping("/service/grooming/google/calendar/test/sync/one")
    Boolean testSyncOneGrooming(@RequestParam("groomingId") Integer groomingId);

    @GetMapping("/service/grooming/google/calendar/test/one/import")
    void testBatchSyncEvent(@RequestParam("gcCalendarId") Integer gcCalendarId);

    @PostMapping("/service/grooming/google/calendar/staff/delete")
    void deleteStaffCalendar(@RequestParam("businessId") Integer businessId, @RequestParam("staffId") Integer staffId);

    @PostMapping("/service/grooming/google-calendar/checkBusinessHaveGoogleCalendarSync")
    Boolean checkBusinessHaveGoogleCalendarSync(@RequestBody SyncAppointmentParams params);
}
