package com.moego.server.grooming.dto.ob;

import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/7/3
 */
@Data
@Accessors(chain = true)
public class BookOnlineQuestionSaveDTO {

    private Integer businessId;
    private Long companyId;

    private Integer customerId;

    /**
     * Key: custom_xxx
     * Value: answer
     */
    private Map<String, Object> clientCustomQuestionMap;

    /**
     * Key: petId
     * Value: custom question answer
     */
    private Map<Integer, Map<String, Object>> petCustomQuestionMap;
}
