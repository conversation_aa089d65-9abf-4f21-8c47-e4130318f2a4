package com.moego.server.grooming.dto;

import com.moego.common.enums.GroomingAppointmentEnum;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class GroomingInvoiceDTO {

    private Integer groomingId;
    private Integer businessId;
    private Integer customerId;
    private BigDecimal subTotal;
    private BigDecimal tax;
    private BigDecimal total;
    private List<InvoiceServiceDTO> serviceInvoices;

    /**
     * appointment source
     *
     * @see GroomingAppointmentEnum
     * 22168-book online
     * 22018-web
     * 17216-android
     * 17802-ios
     * 19826-google calendar
     */
    private Integer source;
}
