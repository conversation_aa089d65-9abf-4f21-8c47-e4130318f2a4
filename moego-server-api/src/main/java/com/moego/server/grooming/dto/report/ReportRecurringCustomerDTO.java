package com.moego.server.grooming.dto.report;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/1/31 5:53 PM
 */
@Data
public class ReportRecurringCustomerDTO {

    @Schema(description = "customer primary id")
    private Integer customerId;

    @Schema(description = "该client在本business中创建的时间，时间格式按business setting设置的展示")
    private String dateJoined;

    @Schema(description = "client First name")
    private String firstName;

    @Schema(description = "client Last name")
    private String lastName;

    @Schema(description = "client email，若没有则空")
    private String email;

    @Schema(description = "1-正常 2-删除")
    private Byte status;

    @Schema(description = "0-正常 1-inactive")
    private Byte inactive;

    @Schema(description = "client Primary contact")
    private String primaryContact;

    @Schema(description = "client Additional contact")
    private List<String> additionalContact;

    @Schema(description = "client address")
    private List<String> addresses;

    @Schema(description = "以用户查表日期为准，选中时间内该client实收金额加总，等于totalPayment减去totalRefund")
    private BigDecimal collectedRevenue;

    @Schema(description = "以用户查表日期为准，选中时间内该client payment amount 金额加总，包含partial pay")
    private BigDecimal totalPayment;

    @Schema(description = "以用户查表日期为准，选中时间内该client refund amount 金额加总")
    private BigDecimal totalRefund;

    @Schema(description = "以用户查表日期为准，选中时间内该client 上一次服务的时间区间，按计划时间")
    private String lastService;

    private Integer lastApptId;

    @Schema(description = "以用户查表日期为准，选中时间内该client 下一次服务的时间区间，按计划时间")
    private String nextService;

    private Integer nextApptId;
}
