package com.moego.server.grooming.api;

import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.grooming.dto.BookOnlineAvailableStaffDTO;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface IBookOnlineAvailableStaffService {

    /**
     * 新接口做了拆分
     * 查询 ob sync，使用 IBookOnlineAvailableStaffService bookOnlineAvailableTimeSync
     * 查询 staff available 使用 svc-online-booking getStaffAvailabilityStatus
     * @param businessId
     * @return
     */
    @GetMapping("/service/grooming/book-online-available-staff/getBookOnlineAvailableStaffByBusinessId")
    @Deprecated
    List<BookOnlineAvailableStaffDTO> listBookOnlineAvailableStaffByBusinessId(@RequestParam Integer businessId);

    @GetMapping("/service/grooming/book-online-available-staff/bookOnlineAvailableTimeSync")
    Boolean bookOnlineAvailableTimeSync(@RequestParam Integer businessId);

    @GetMapping("/service/grooming/book-online-available-staff/getAvailableStaffListInAvailabilityType")
    List<MoeStaffDto> getAvailableStaffListInAvailabilityType(@RequestParam Integer businessId);
}
