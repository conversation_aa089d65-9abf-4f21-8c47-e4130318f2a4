package com.moego.server.grooming.dto.ss;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmartScheduleVO {

    private Integer businessId;
    private String date;
    private Integer staffId;
    List<ScheduleTimeSlot> availableRange;
    ScheduleTimeSlot firstSlot;
    StaffCheckStatus staffCheckStatus;
}
