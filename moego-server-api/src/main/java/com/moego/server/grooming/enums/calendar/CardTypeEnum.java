package com.moego.server.grooming.enums.calendar;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
@Schema(description = "card type", type = "integer")
public enum CardTypeEnum {
    APPOINTMENT(1),
    SERVICE(2),
    OPERATION(3),
    BLOCK(4);

    private final int value;

    @JsonValue
    public int getValue() {
        return value;
    }

    public static CardTypeEnum fromValue(int value) {
        for (CardTypeEnum cardTypeEnum : CardTypeEnum.values()) {
            if (cardTypeEnum.getValue() == value) {
                return cardTypeEnum;
            }
        }
        return null;
    }
}
