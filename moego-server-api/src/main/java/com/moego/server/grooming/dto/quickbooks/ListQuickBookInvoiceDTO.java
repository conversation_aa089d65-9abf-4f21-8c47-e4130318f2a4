package com.moego.server.grooming.dto.quickbooks;

import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 * @since 2024/10/30
 */
@Builder(toBuilder = true)
public class ListQuickBookInvoiceDTO {

    List<QuickBookInvoiceDTO> quickBookInvoices;

    Integer count;

    public List<QuickBookInvoiceDTO> getQuickBookInvoices() {
        return quickBookInvoices;
    }

    public void setQuickBookInvoices(List<QuickBookInvoiceDTO> quickBookInvoices) {
        this.quickBookInvoices = quickBookInvoices;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
