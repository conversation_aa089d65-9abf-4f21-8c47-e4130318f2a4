package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.DepositDto;
import com.moego.server.grooming.params.DepositVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2021/11/3 6:21 PM
 */
public interface IDepositService {
    @PostMapping("/service/grooming/deposit/paid")
    Integer updateDepositPaid(@RequestParam("invoiceId") Integer invoiceId);

    @PostMapping("/service/grooming/deposit/createOrUpdate")
    String createOrUpdateDeposit(@RequestBody DepositVo depositVo);

    @GetMapping("/service/grooming/deposit/info")
    DepositDto getDepositByInvoiceId(@RequestParam("invoiceId") Integer invoiceId);
}
