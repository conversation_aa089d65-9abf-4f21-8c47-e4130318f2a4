package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class PreAuthAmountDTO {

    private BigDecimal serviceTotal;

    private BigDecimal taxAmount;

    @Schema(description = "本次支付的 booking fee")
    @Deprecated(since = "2024-09-25", forRemoval = true)
    private BigDecimal bookingFee;

    @Schema(description = "初始化processing fee")
    private BigDecimal initProcessingFee;

    private BigDecimal serviceChargeAmount;

    @Deprecated(since = "2025-05-30", forRemoval = true)
    private List<ServiceChargeDTO> serviceChargeList;

    private List<ApplyServiceChargeDTO> applyServiceChargeList;

    @Schema(description = "本次支付的折扣金额")
    private BigDecimal discountAmount;

    @Schema(description = "使用的 membership ids")
    private List<Long> usedMembershipIds;
}
