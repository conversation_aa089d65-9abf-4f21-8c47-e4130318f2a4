package com.moego.server.grooming.dto;

import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import lombok.Builder;

/**
 * <AUTHOR>
 * @since 2023/7/16
 */
@Builder
public record CustomerHasRequestDTO(
        /**
         * Business existing customer profile.
         */
        CustomerProfileRequestDTO existingProfile,
        /**
         * Customer profile request.
         */
        CustomerProfileRequestDTO profileRequest,
        /**
         * Merged customer profile.
         * conflict fields will be overwritten by profile request.
         */
        CustomerProfileRequestDTO mergedProfile,
        /**
         * Has request update.
         */
        boolean hasRequestUpdate) {}
