package com.moego.server.grooming.dto.ob;

import com.moego.server.grooming.dto.PetApplicableServiceDTO;
import com.moego.server.grooming.dto.ServiceCategoryDTO;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class OBServiceListDto {

    private List<ServiceCategoryDTO> serviceList;
    private List<ServiceCategoryDTO> addonsList;
    private Map<Integer, List<ServiceCategoryDTO>> petServiceList;
    private Map<Integer, List<ServiceCategoryDTO>> petAddonsList;
    // 根据pet信息筛选出合适的Service列表
    private List<PetApplicableServiceDTO> applicableServiceList;
}
