package com.moego.server.grooming.dto;

import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.WaitListStatusEnum;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class GroomingAppointmentDto {
    private Integer id;
    private String orderId;
    private Integer businessId;
    private Integer customerId;
    private String appointmentDate;
    private Integer appointmentStartTime;
    private Integer appointmentEndTime;
    private Byte isWaitingList;
    private Integer moveWaitingBy;
    private Long confirmedTime;
    private Long checkInTime;
    private Long checkOutTime;
    private Long canceledTime;
    private Byte status;
    private Integer isBlock;
    private Byte bookOnlineStatus;
    private Integer customerAddressId;
    private Integer repeatId;
    private Byte isPaid;
    private String colorCode;
    private Byte noShow;
    private BigDecimal noShowFee;
    private Byte isPustNotification;
    private Byte cancelByType;
    private Integer cancelBy;
    private Byte confirmByType;
    private Integer confirmBy;
    private Integer createdById;
    private Byte outOfArea;
    private Integer isDeprecate;
    private Long createTime;
    private Long updateTime;
    private Integer source;
    private String oldAppointmentDate;
    private Integer oldAppointmentStartTime;
    private Integer oldAppointmentEndTime;
    private Integer oldApptId;
    private Byte scheduleType;
    private String sourcePlatform;
    private Long readyTime;
    private Integer pickupNotificationSendStatus;
    private String pickupNotificationFailedReason;
    private AppointmentStatusEnum statusBeforeCheckin;
    private AppointmentStatusEnum statusBeforeReady;
    private AppointmentStatusEnum statusBeforeFinish;
    private Boolean noStartTime;
    private Long updatedById;
    private Long companyId;
    private Boolean isAutoAccept;
    private WaitListStatusEnum waitListStatus;
    private String appointmentEndDate;
    private Integer serviceTypeInclude;
}
