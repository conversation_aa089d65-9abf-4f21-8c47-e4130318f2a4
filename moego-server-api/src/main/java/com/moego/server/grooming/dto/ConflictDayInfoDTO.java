package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ConflictDayInfoDTO {

    private String date;

    private Boolean isNotConflict;

    private Integer ConflictType; // 1,work time 冲突。2，预约冲突，3，block冲突

    private Integer staffId;

    @Schema(description = "repeat appt的时间，经过smart schedule后，这个时间和原来的appt start time可能不一样")
    private Integer startTime;

    private Integer duration;

    @Schema(description = "schedule类型，1-重复规则计算出来的不冲突的时间，2-经过smart schedule后的时间")
    private Integer scheduleType;

    @Schema(description = "是否是 history appt")
    private Boolean isHistory;
}
