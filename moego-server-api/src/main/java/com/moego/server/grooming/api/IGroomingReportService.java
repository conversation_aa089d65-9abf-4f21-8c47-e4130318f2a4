package com.moego.server.grooming.api;

import com.moego.common.dto.PageDTO;
import com.moego.server.grooming.dto.ReportServiceProduct;
import com.moego.server.grooming.dto.ReportWebSale;
import com.moego.server.grooming.dto.report.DescribeAppointmentReportsDTO;
import com.moego.server.grooming.dto.report.DescribeOperationReportsDTO;
import com.moego.server.grooming.dto.report.DescribePetDetailReportsDTO;
import com.moego.server.grooming.dto.report.EmployeeContribute;
import com.moego.server.grooming.dto.report.GroomingMobileOverviewDTO;
import com.moego.server.grooming.dto.report.MobileSummaryDTO;
import com.moego.server.grooming.dto.report.PayrollReportCountDTO;
import com.moego.server.grooming.dto.report.PayrollReportCountV2DTO;
import com.moego.server.grooming.dto.report.ReportApptsNumberDTO;
import com.moego.server.grooming.dto.report.ReportCollectedRevenueDTO;
import com.moego.server.grooming.dto.report.ReportExpectedRevenueDTO;
import com.moego.server.grooming.dto.report.ReportPaymentSummaryDto;
import com.moego.server.grooming.dto.report.ReportRecurringCustomerDTO;
import com.moego.server.grooming.dto.report.ReportTrendDTO;
import com.moego.server.grooming.dto.report.ReportTrendDataDTO;
import com.moego.server.grooming.dto.report.ReportWebAppointment;
import com.moego.server.grooming.dto.report.ReportWebEmployee;
import com.moego.server.grooming.dto.report.ReportWebSaleService;
import com.moego.server.grooming.dto.report.ScanAppointmentReportsDTO;
import com.moego.server.grooming.params.ReportWebApptsRequest;
import com.moego.server.grooming.params.report.DescribeAppointmentReportsParams;
import com.moego.server.grooming.params.report.DescribeOperationReportsParams;
import com.moego.server.grooming.params.report.DescribePetDetailReportsParams;
import com.moego.server.grooming.params.report.GetDashboardOverviewParams;
import com.moego.server.grooming.params.report.GetReportParams;
import com.moego.server.grooming.params.report.GetReportTrendParams;
import com.moego.server.grooming.params.report.QueryPayrollReportByPageParams;
import com.moego.server.grooming.params.report.ScanAppointmentReportsParams;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IGroomingReportService {
    @GetMapping("/service/grooming/report/mobile/summary")
    MobileSummaryDTO getMobileDashboardSummary(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    @PostMapping("/service/grooming/report/mobile/overview")
    GroomingMobileOverviewDTO getMobileDashboardOverview(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate,
            @RequestBody List<EmployeeContribute> employees);

    @PostMapping("/service/grooming/report/v2/mobile/overview/")
    GroomingMobileOverviewDTO getMobileDashboardOverviewV2(@RequestBody GetDashboardOverviewParams params);

    @GetMapping("/service/grooming/report/mobile/apptNum")
    ReportApptsNumberDTO getReportApptsNumber(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    @PostMapping("/service/grooming/report/web/appt")
    List<ReportWebAppointment> queryReportWebAppts(@RequestBody ReportWebApptsRequest request);

    @GetMapping("/service/grooming/report/web/employee")
    List<ReportWebEmployee> queryReportWebEmployee(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("reportId") Integer reportId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    @GetMapping("/service/grooming/report/web/payroll")
    List<ReportWebEmployee> queryPayrollReport(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    @GetMapping("/service/grooming/report/web/payroll/page")
    PageDTO<ReportWebEmployee> queryPayrollReportByPage(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("staffId") Integer staffId,
            @RequestParam("type") Byte type,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate,
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize);

    @PostMapping("/service/grooming/report/web/payroll/page/v2")
    PageDTO<ReportWebEmployee> queryPayrollReportByPageV2(@Valid @RequestBody QueryPayrollReportByPageParams params);

    @PostMapping("/service/grooming/report/web/payroll/count")
    List<PayrollReportCountDTO> getStaffPayrollReportCounts(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate,
            @RequestBody List<Integer> staffIds);

    @PostMapping("/service/grooming/report/web/service/payroll/count")
    List<PayrollReportCountV2DTO> getStaffServicePayrollReportCounts(@RequestBody GetReportParams params);

    @PostMapping("/service/grooming/report/web/tips/payroll/count")
    List<PayrollReportCountV2DTO> getStaffTipsPayrollReportCounts(@RequestBody GetReportParams params);

    @GetMapping("/service/grooming/report/web/payroll/all")
    List<ReportWebEmployee> queryPayrollReportAll(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("staffId") Integer staffId,
            @RequestParam("type") Byte type,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    @GetMapping("/service/grooming/report/web/sale")
    List<ReportWebSale> queryReportWebSale(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("reportId") Integer reportId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    /**
     * web端 查询sale and appointments report
     *
     * @param requestParam
     * @return
     */
    @PostMapping("/service/grooming/report/web/sale/appt")
    List<ReportWebAppointment> queryReportWebSaleAndAppt(@RequestBody ReportWebApptsRequest requestParam);

    /**
     * web端 查询sale by service
     *
     * @param businessId
     * @param startDate
     * @param endDate
     * @return
     */
    @GetMapping("/service/grooming/report/web/sale/service")
    List<ReportWebSaleService> queryReportWebByService(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    @GetMapping("/service/grooming/report/web/product")
    List<ReportServiceProduct> queryReportWebServiceProduct(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    /**
     * 获取dashboard 中趋势的统计
     *
     * @param businessId
     * @param startDate
     * @param endDate
     * @return
     */
    @GetMapping("/service/grooming/report/dashboard/trend")
    ReportTrendDTO getReportTrend(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    @PostMapping("/service/grooming/report/dashboard/trend/data")
    ReportTrendDataDTO getReportTrendData(@RequestBody GetReportTrendParams params);

    /**
     * 获取dashboard 中 expected rev ，employee 纬度
     *
     * @param businessId
     * @param staffId    不传参数查询全部
     * @param startDate
     * @param endDate
     * @return
     */
    @GetMapping("/service/grooming/report/dashboard/expected")
    List<ReportExpectedRevenueDTO> getExpectedRevReport(
            @RequestParam("businessId") Integer businessId,
            @RequestParam(value = "staffId", required = false) Integer staffId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    /**
     * 获取dashboard 中 collected rev ，employee 纬度
     *
     * @param businessId
     * @param staffId    不传参数查询全部
     * @param startDate
     * @param endDate
     * @return
     */
    @GetMapping("/service/grooming/report/dashboard/collected")
    List<ReportCollectedRevenueDTO> getCollectedRevReport(
            @RequestParam("businessId") Integer businessId,
            @RequestParam(value = "staffId", required = false) Integer staffId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    /**
     * 获取 payment method report ，可根据 employee 纬度统计
     *
     * @param businessId
     * @param staffId    不传参数查询全部
     * @param startDate
     * @param endDate
     * @return
     */
    @GetMapping("/service/grooming/report/payment")
    List<ReportPaymentSummaryDto> getPaymentReport(
            @RequestParam("businessId") Integer businessId,
            @RequestParam(value = "staffId", required = false) Integer staffId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    @GetMapping("/service/grooming/report/client/recurring")
    List<ReportRecurringCustomerDTO> getRecurringClientReport(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    @PostMapping("/service/grooming/reporting/describeAppointmentReports")
    DescribeAppointmentReportsDTO describeAppointmentReports(
            @RequestBody @Valid DescribeAppointmentReportsParams params);

    @PostMapping("/service/grooming/reporting/describePetDetailReports")
    DescribePetDetailReportsDTO describePetDetailReports(@RequestBody @Valid DescribePetDetailReportsParams params);

    @PostMapping("/service/grooming/reporting/describeOperationReports")
    DescribeOperationReportsDTO describeOperationReports(@RequestBody @Valid DescribeOperationReportsParams params);

    @PostMapping("/service/grooming/reporting/scanAppointmentReports")
    ScanAppointmentReportsDTO scanAppointmentReports(@RequestBody @Valid ScanAppointmentReportsParams params);
}
