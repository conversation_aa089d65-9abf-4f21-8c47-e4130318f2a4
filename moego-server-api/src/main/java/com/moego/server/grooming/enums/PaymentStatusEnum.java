package com.moego.server.grooming.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
@Schema(description = "payment status", type = "integer")
public enum PaymentStatusEnum {
    FULLY_PAID(1),
    UNPAID(2),
    PARTIAL_PAID(3),
    PREPAID(4);

    private final int value;

    @JsonValue
    public int getValue() {
        return value;
    }

    public static PaymentStatusEnum fromValue(int value) {
        for (PaymentStatusEnum paymentStatusEnum : PaymentStatusEnum.values()) {
            if (paymentStatusEnum.getValue() == value) {
                return paymentStatusEnum;
            }
        }
        return null;
    }
}
