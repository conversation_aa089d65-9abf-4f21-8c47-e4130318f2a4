package com.moego.server.grooming.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class CustomerPetMergeRelationDTO {

    @NotNull
    private Long companyId;

    @JsonIgnore
    private List<Long> allLocationIds;

    @NotNull
    private Integer targetCustomerId;

    @NotEmpty
    private List<Integer> sourceCustomerIds;

    private List<PetMergeRelationDTO> petMergeRelations;
}
