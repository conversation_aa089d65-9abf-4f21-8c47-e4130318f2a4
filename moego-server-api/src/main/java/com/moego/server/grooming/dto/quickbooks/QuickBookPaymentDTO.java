package com.moego.server.grooming.dto.quickbooks;

import java.math.BigDecimal;
import lombok.Builder;

/**
 * <AUTHOR>
 * @since 2024/10/30
 */
@Builder(toBuilder = true)
public class QuickBookPaymentDTO {
    private Integer id;
    private Integer businessId;
    private Long companyId;
    private Integer invoiceId;
    private BigDecimal paidAmount;
    private Integer paymentId;
    private String qbInvoiceId;
    private String qbPaymentId;
    private String qbPaymentStatus;
    private Long createTime;
    private Long updateTime;

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getQbPaymentStatus() {
        return qbPaymentStatus;
    }

    public void setQbPaymentStatus(String qbPaymentStatus) {
        this.qbPaymentStatus = qbPaymentStatus;
    }

    public String getQbPaymentId() {
        return qbPaymentId;
    }

    public void setQbPaymentId(String qbPaymentId) {
        this.qbPaymentId = qbPaymentId;
    }

    public String getQbInvoiceId() {
        return qbInvoiceId;
    }

    public void setQbInvoiceId(String qbInvoiceId) {
        this.qbInvoiceId = qbInvoiceId;
    }

    public Integer getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(Integer paymentId) {
        this.paymentId = paymentId;
    }

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    public Integer getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}
