package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.PreAuthAmountDTO;
import com.moego.server.grooming.dto.PrepayAmountDTO;
import com.moego.server.grooming.dto.ob.AvailableStaffDTO;
import com.moego.server.grooming.dto.ob.OBServiceDTO;
import com.moego.server.grooming.dto.ob.OBServiceListDto;
import com.moego.server.grooming.dto.ob.OBTimeSlotDTO;
import com.moego.server.grooming.dto.ob.ServiceOBSettingDTO;
import com.moego.server.grooming.dto.ob.StaffAvailableDateDTO;
import com.moego.server.grooming.dto.ob.StaffFirstAvailableDateDTO;
import com.moego.server.grooming.params.PreAuthAmountParams;
import com.moego.server.grooming.params.PrepayAmountParams;
import com.moego.server.grooming.params.ob.AvailableStaffParams;
import com.moego.server.grooming.params.ob.ServiceOBSettingQueryParams;
import java.util.List;
import java.util.Map;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2023/10/8
 */
public interface IOnlineBookingService {
    /**
     * Get available service list
     *
     * @param params selected pets
     * @return available service list
     */
    @PostMapping("/service/grooming/online-booking/getAvailableServiceList")
    OBServiceListDto getAvailableServiceList(@RequestBody OBServiceDTO params);

    /**
     * Get available staff list
     *
     * @param params selected pets and services
     * @return available staff list
     */
    @PostMapping("/service/grooming/online-booking/getAvailableStaffList")
    AvailableStaffDTO getAvailableStaffList(@RequestBody AvailableStaffParams params);

    /**
     * Get staff first available date
     *
     * @param params selected pets, services and staffs
     * @return staff first available date list
     */
    @PostMapping("/service/grooming/online-booking/getStaffFirstAvailableDateList")
    List<StaffFirstAvailableDateDTO> getStaffFirstAvailableDateList(@RequestBody OBTimeSlotDTO params);

    /**
     * Get staff available date and time slot list
     *
     * @param params selected pets, services and staffs
     * @return staff available date list
     */
    @PostMapping("/service/grooming/online-booking/getStaffAvailableDateList")
    List<StaffAvailableDateDTO> getStaffAvailableDateList(@RequestBody OBTimeSlotDTO params);

    /**
     * Get prepay amount detail
     *
     * @param businessId selected business id
     * @param params     selected pets, services
     * @return prepay amount detail
     */
    @PostMapping("/service/grooming/online-booking/getPrepayAmount")
    PrepayAmountDTO getPrepayAmount(@RequestParam Integer businessId, @RequestBody PrepayAmountParams params);

    /**
     * Get prepay amount detail
     *
     * @param businessId selected business id
     * @param guid       prepay guid
     * @return prepay amount detail
     */
    @PostMapping("/service/grooming/online-booking/getPrepayAmountByGuid")
    PrepayAmountDTO getPrepayAmountByGuid(@RequestParam Integer businessId, @RequestParam String guid);

    /**
     * Get pre-auth amount detail
     *
     * @param businessId selected business id
     * @param params     selected pets, services
     * @return pre-auth amount detail
     */
    @PostMapping("/service/grooming/online-booking/getPreAuthAmount")
    PreAuthAmountDTO getPreAuthAmount(@RequestParam Integer businessId, @RequestBody PreAuthAmountParams params);

    /**
     * query service ob setting
     * TODO: migrate to offering or ob service
     */
    @PostMapping("/service/grooming/online-booking/getServiceOBSetting")
    Map<Long, ServiceOBSettingDTO> getServiceOBSetting(@RequestBody ServiceOBSettingQueryParams serviceIds);

    /**
     * Check whether this appointment can be auto accepted.
     *
     * @param appointmentId appointment id
     * @return whether can auto accept
     */
    @PostMapping("/service/grooming/online-booking/canAutoAccept")
    @Deprecated(forRemoval = true)
    boolean canAutoAccept(@RequestParam("appointmentId") long appointmentId);

    /**
     * Grooming online booking 的 auto accept 逻辑，这个接口会处理 accept BookingRequest，capture PaymentIntent，send notification 等逻辑。
     *
     * <p> 这个接口的调用场景有两个：
     * <p> 1. 在不需要 payment 的 submit BookingRequest 场景里，在 server-grooming 直接调用。
     * <p> 2. 在需要 payment 的 submit BookingRequest 场景里，在 confirm PaymentIntent 的回调里调用。
     *
     * @param appointmentId appointment id
     * @return whether this appointment is auto accepted
     */
    @PostMapping("/service/grooming/online-booking/triggerBookingRequestAutoAccepted")
    boolean triggerBookingRequestAutoAccepted(@RequestParam("appointmentId") long appointmentId);
}
