package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class OrderItemPetEvaluationDetailDTO {

    private Integer petId;
    private String petName;
    private String petBreed;
    private Integer serviceId;
    private Integer serviceTime;
    private BigDecimal servicePrice;
    private Long startTime;
    private Integer serviceItemType;
    // 本条 pet detail 等价的 order line item 中 quantity数量
    private Integer quantity;
    // staff信息
    private Integer staffId;
}
