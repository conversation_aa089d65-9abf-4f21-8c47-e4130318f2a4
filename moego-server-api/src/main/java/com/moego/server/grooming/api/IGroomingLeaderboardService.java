package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.LeaderboardRankInfoDTO;
import com.moego.server.grooming.dto.report.LeaderboardStaffReportDTO;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
public interface IGroomingLeaderboardService {
    @GetMapping("/service/grooming/leaderboard/staff")
    List<LeaderboardStaffReportDTO> getStaffForLeaderboard(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate,
            @RequestParam("staffIdList") List<Integer> staffIdList);

    @GetMapping("/service/grooming/leaderboard/rank")
    List<LeaderboardRankInfoDTO> getRankForLeaderboard(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate,
            @RequestParam("type") String type);
}
