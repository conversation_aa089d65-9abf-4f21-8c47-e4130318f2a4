package com.moego.server.grooming.dto.report;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class ReportWebClient {

    private Integer clientId;
    private String firstName;
    private String lastName;
    private String primaryNumber;
    private String email;
    private String additionalContactName;
    private String additionalContactNumber;
    private String address1;
    private String address2;
    private String city;
    private String state;
    private String zipcode;
    private String country;
    private String status;

    private BigDecimal totalSale;
    private BigDecimal totalPaid;
    private BigDecimal totalTips;
    private BigDecimal totalApptSale;
    private BigDecimal totalProductSale;
    private BigDecimal totalPackageSale;
    private BigDecimal totalSpending;
    private BigDecimal totalUnpaid;

    private Integer totalAppts;
    private Integer totalUpcoming;
    private Integer totalFinished;
    private Integer totalCancelled;
    private Integer totalNoShow;

    private Integer totalVisits;
    private Integer firstVisit;
    private Integer lastVisit;
    private Integer lastRepeatFrequency;

    private String preferredGroomer;
    private String lastGroomer;
    private String lastApptDate;
    private String nextApptDate;

    private Integer totalClients;
    private Integer totalNewClients;
    private Integer recurringClients;
    private Integer nonRecurringClients;
    private Integer recurringRate;
    private Integer nonRecurringRate;
    private Integer totalPets;

    private Integer reviewPoints;
    private Integer totalReviews;
    private Double avgReview;

    private String referralSource;
    private String agreementName;
}
