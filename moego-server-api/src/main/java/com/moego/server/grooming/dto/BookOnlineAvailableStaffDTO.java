package com.moego.server.grooming.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BookOnlineAvailableStaffDTO {
    private Integer id;
    private Integer businessId;
    private Integer staffId;
    private Byte byWorkingHourEnable;
    private Byte bySlotEnable;
    private Long createTime;
    private Long updateTime;
    private Byte syncWithWorkingHour;
}
