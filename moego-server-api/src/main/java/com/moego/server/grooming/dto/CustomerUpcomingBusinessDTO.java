package com.moego.server.grooming.dto;

import java.util.List;
import lombok.Data;

@Data
public class CustomerUpcomingBusinessDTO {

    private List<CustomerUpComingAppointDTO> upComingAppoint;

    /**
     * 对于 as 迁移后的 company 的 customer，useCompanyInfo = true，前端应当使用 company 的数据进行展示
     */
    private boolean useCompanyInfo;

    private String companyName;
    private String currencySymbol;

    /**
     * 对于 as 迁移前的 company 的 customer，以下 business 信息是 customer 所在的 business 信息。
     * 对于 as 迁移后的 company 的 customer，以下 business 信息是 next appt 的 business 信息，
     * 如果没有 next appt，则是 customer 的 preferred business 信息
     */
    private String BusinessName;

    private String BusinessAddress;
    private String BusinessEmail;

    /**
     * 迁移后不再需要
     */
    @Deprecated
    private String BusinessHeadImg;
    /**
     * 迁移后不再需要，改用 currencySymbol
     */
    @Deprecated
    private String BusinessSymbol;

    /**
     * as 迁移后，这俩 timezone 相关的字段前端都不需要了
     */
    @Deprecated
    private String timezoneName;

    @Deprecated
    private Integer timezoneSeconds;

    private Boolean isShowServicePrice;

    private Integer customerId;

    /**
     * 前端会根据这个值判断是否需要隐藏 business address （根据是否 salon 来判断是否展示商家的地址）
     */
    private Byte appType;
}
