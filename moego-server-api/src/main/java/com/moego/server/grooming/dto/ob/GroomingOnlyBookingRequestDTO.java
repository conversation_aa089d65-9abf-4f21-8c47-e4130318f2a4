package com.moego.server.grooming.dto.ob;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/3/26
 */
@Data
@Accessors(chain = true)
public class GroomingOnlyBookingRequestDTO {
    private Long companyId;
    private Integer businessId;
    private Integer customerId;
    private Integer appointmentId;
    private String startDate;
    private Integer startTime;
    private String endDate;
    private Integer endTime;
    /**
     * SUBMITTED = 1;
     * WAIT_LIST = 2;
     * SCHEDULED = 3;
     * DECLINED = 4;
     */
    private Integer status;
    /**
     * has moe_book_online_deposit record
     */
    private Boolean isPrepaid;

    private String additionalNote;
    /**
     * RESERVE_WITH_GOOGLE
     * PET_PARENT_APP
     */
    private String sourcePlatform;

    private Long createdAt;
    private Long updatedAt;
    private Long deletedAt;

    private List<GroomingServiceDetailDTO> groomingServiceDetails;

    private GroomingAutoAssignDTO autoAssign;

    private Integer serviceTypeInclude;

    @Data
    public static class GroomingServiceDetailDTO {
        private Integer petId;
        private Integer staffId;
        private Integer serviceId;
        private Integer serviceTime;
        private BigDecimal servicePrice;
        /**
         * // only for this appointment
         *   ONLY_THIS = 1;
         *   // do not save
         *   DO_NOT_SAVE = 2;
         *   // for this and following appointments
         *   THIS_AND_FUTURE = 3;
         *   // for all upcoming appointments
         *   ALL_UPCOMING = 4;
         */
        private Integer scopeTypePrice;

        private Integer scopeTypeTime;
        private String startDate;
        private Integer startTime;
        private String endDate;
        private Integer endTime;
        private Long createdAt;
        private Long updatedAt;
    }

    @Data
    public static class GroomingAutoAssignDTO {
        private Integer staffId;
        private Integer startTime;
        private Date createdAt;
        private Date updatedAt;
    }
}
