package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.PetCustomizedServiceDTO;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
public interface IPetCustomizedServiceService {

    @GetMapping("/service/grooming/pet-customized-service/getPetCustomizedServiceById")
    PetCustomizedServiceDTO getPetCustomizedServiceById(
            @RequestParam("petCustomizedServiceId") int petCustomizedServiceId);

    /**
     * 获取 pet 的所有 custom services。
     *
     * @param petId pet id
     * @return custom services
     */
    @PostMapping("/service/grooming/pet-customized-service/listByPetId")
    List<PetCustomizedServiceDTO> listByPetId(@RequestParam("petId") int petId);
}
