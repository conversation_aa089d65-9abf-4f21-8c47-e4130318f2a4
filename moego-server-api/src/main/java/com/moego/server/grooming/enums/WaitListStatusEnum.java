package com.moego.server.grooming.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Schema(description = "wait list status", type = "integer")
public enum WaitListStatusEnum {
    APPTONLY((byte) 0), // 有 appt 无 waitlist
    WAITLISTONLY((byte) 1), // 无 appt 有 waitlist
    APPTANDWAITLIST((byte) 2); // 有 appt 有 waitlist

    private final Byte value;

    @JsonValue
    public Byte getValue() {
        return value;
    }

    public static WaitListStatusEnum fromValue(int value) {
        for (WaitListStatusEnum status : WaitListStatusEnum.values()) {
            if (status.getValue().equals((byte) value)) {
                return status;
            }
        }
        return APPTONLY;
    }
}
