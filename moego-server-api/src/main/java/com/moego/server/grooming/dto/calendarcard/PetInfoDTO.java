package com.moego.server.grooming.dto.calendarcard;

import com.moego.server.customer.dto.MoePetCodeInfoDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.Data;

@Data
public class PetInfoDTO {
    private Integer petId;
    private String petName;
    private String petBreedName;
    private List<MoePetCodeInfoDTO> petCodeList;
    private List<ServiceInfoDTO> serviceList;
    private List<VaccineAlertDTO> vaccineAlerts;

    public void addService(ServiceInfoDTO serviceInfoDTO) {
        if (Objects.isNull(serviceList)) {
            serviceList = new ArrayList<>();
        }
        serviceList.add(serviceInfoDTO);
    }

    public PetInfoDTO(Integer petId, String petName) {
        this.petId = petId;
        this.petName = petName;
        this.petCodeList = new ArrayList<>();
        this.serviceList = new ArrayList<>();
        this.vaccineAlerts = new ArrayList<>();
    }
}
