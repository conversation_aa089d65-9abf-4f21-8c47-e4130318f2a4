package com.moego.server.grooming.dto;

import com.moego.idl.models.offering.v1.ServiceItemType;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class ServiceChargeDTO {

    private Long id;
    private Long businessId;
    private String name;
    private String description;
    private BigDecimal price;
    private Integer taxId;
    private Boolean isMandatory;
    private Boolean isActive;
    private Boolean isDeleted;
    private Long createdBy;
    private Long updatedBy;
    private Date createdAt;
    private Date updatedAt;
    private BigDecimal taxAmount;
    private List<ServiceItemType> serviceItemTypes;
}
