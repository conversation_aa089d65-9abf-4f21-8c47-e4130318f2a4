package com.moego.server.grooming.dto;

import lombok.Data;

@Data
public class CustomerRebookReminderDTO {
    private Integer businessId; // grooming id 对应的 business id
    private Integer customerId; // id
    private Integer groomingId; // 预约id. company 维度下的预约
    private String customerFirstName;
    private String customerLastName;
    private String appointmentDate; // 预约日期
    private Integer appointmentStartTime; // 预约开始时间
    private Integer appointmentEndTime; // 预约结束时间
    private Integer preferredFrequencyDay; //
    private String expectedServiceDate;
    private String avatarPath;
}
