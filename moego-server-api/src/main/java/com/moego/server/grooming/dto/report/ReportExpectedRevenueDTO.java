package com.moego.server.grooming.dto.report;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 5/21/21 4:14 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportExpectedRevenueDTO {

    private Integer staffId;

    private String staffName;

    private BigDecimal totalExpectedRevWithTipsAndTax;

    private BigDecimal totalExpectedRev;

    private BigDecimal totalTips;

    private BigDecimal totalTax;

    private BigDecimal totalDiscount;

    private Integer apptNum;

    private Integer petNum;
}
