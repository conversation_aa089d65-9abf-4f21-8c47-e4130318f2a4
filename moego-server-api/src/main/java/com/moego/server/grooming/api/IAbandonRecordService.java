package com.moego.server.grooming.api;

import com.moego.api.common.Range;
import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.common.params.CustomerIdsParams;
import com.moego.server.grooming.dto.AbandonRecordDTO;
import com.moego.server.grooming.dto.ob.AssociateCustomerAndPetDTO;
import com.moego.server.grooming.dto.ob.AssociateCustomerDTO;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Validated
public interface IAbandonRecordService {

    /**
     * Get an abandon record by id, not include deleted record.
     *
     * @param id abandon record id
     * @return abandon record
     */
    @GetMapping("/service/grooming/ob/abandoned-record/{id}")
    @Nullable
    AbandonRecordDTO get(@PathVariable("id") Integer id);

    /**
     * Update an abandon record by id, ignore null value.
     *
     * @param param {@link AbandonRecordDTO}
     * @return affected rows
     */
    @PostMapping("/service/grooming/ob/abandoned-record/update")
    int update(@RequestBody AbandonRecordDTO param);

    @PostMapping("/service/grooming/ob/abandoned-client/associate")
    Boolean associateCustomer(@RequestBody AssociateCustomerDTO associateCustomerDTO);

    /**
     * TODO(account structure): 如果 business id 对应的 company id 已迁移，
     * 则把 company 的所有 business 对应的 abandon record 都进行关联
     * 否则，只关联当前 business
     */
    @PostMapping("/service/grooming/ob/abandoned-record/associate")
    Boolean associateCustomerAndPet(@RequestBody AssociateCustomerAndPetDTO associateCustomerAndPetDTO);

    /**
     * TODO(account structure): company 迁移后，删除 company 维度的数据
     */
    @PostMapping("/service/grooming/ob/abandoned-record/delete")
    Integer deleteAbandonRecords(@RequestParam Integer businessId, @RequestParam Integer customerId);

    @PostMapping("/service/grooming/ob/abandoned-record/deleteRecordsWhenSubmitBookingRequest")
    Integer deleteRecordsWhenSubmitBookingRequest(
            @RequestParam Integer businessId, @RequestParam Integer customerId, @RequestParam String phoneNumber);

    /**
     * TODO(account structure): company 迁移后，查询 company 维度的数据
     */
    @PostMapping("/service/grooming/ob/abandoned-client/filter")
    Set<Integer> listCustomerIdByFilter(@RequestBody ClientsFilterDTO clientsFilterDTO);

    @PostMapping("/service/grooming/ob/abandoned-client/texted")
    Boolean updateLastTextedTime(
            @RequestParam Integer businessId, @RequestParam Integer customerId, @RequestParam Long textedTime);

    @PostMapping("/service/grooming/ob/abandoned-client/emailed")
    Boolean updateLastEmailedTime(@RequestBody CustomerIdsParams params, @RequestParam Long emailedTime);

    /**
     * 获取指定时间范围内的 abandon 记录，这个接口的使用场景只有在发送 notification 的时候，
     * <p> 一般情况下查询 abandon 记录使用 {@link #listByCondition(ListByConditionParam)}。
     *
     * @param param {@link ListAbandonRecordByAbandonTimeParam}
     * @return abandon records
     */
    @PostMapping("/service/grooming/ob/abandoned-client/listAbandonRecordByAbandonTime")
    List<AbandonRecordDTO> listAbandonRecordByAbandonTime(@RequestBody ListAbandonRecordByAbandonTimeParam param);

    /**
     * 获取最新的一条 abandon 记录
     */
    @PostMapping("/service/grooming/ob/abandoned-client/getLatestAbandonRecord")
    AbandonRecordDTO getLatestAbandonRecord();

    /**
     * List abandon records by condition, not include deleted records.
     *
     * @param param query condition
     * @return abandon records
     */
    @PostMapping("/service/grooming/ob/abandoned-record/listByCondition")
    List<AbandonRecordDTO> listByCondition(@RequestBody @Valid ListByConditionParam param);

    /**
     * @param abandonStepIn   {@link com.moego.server.grooming.dto.AbandonRecordDTO.AbandonStep}
     * @param abandonStatusIn {@link com.moego.server.grooming.dto.AbandonRecordDTO.AbandonStatus}
     */
    @Builder(toBuilder = true)
    record ListByConditionParam(
            @NotEmpty Set<Integer> businessIdsIn,
            Range<Long> abandonTimeRange,
            Set<String> abandonStepIn,
            Set<String> abandonStatusIn,
            Set<String> leadTypeIn,
            Boolean isSendScheduleMessage,
            Boolean isNotificationSent) {}

    /**
     * @param abandonTimeGe abandon time greater than or equal
     * @param abandonTimeLe abandon time less than or equal
     */
    @Builder(toBuilder = true)
    record ListAbandonRecordByAbandonTimeParam(@NotNull Long abandonTimeGe, @NotNull Long abandonTimeLe) {}

    /**
     * 将 customer 最近一次的 abandon 记录标记为已恢复，关联到对应的 appointment
     *
     * @param businessId    business id
     * @param customerId    customer id
     * @param appointmentId appointment id
     * @return true if update successfully
     */
    @PostMapping("/service/grooming/ob/abandoned-record/updateAbandonRecordToRecoveredByCustomer")
    Boolean updateAbandonRecordToRecoveredByCustomer(
            @RequestParam @NotNull Integer businessId,
            @RequestParam @NotNull Integer customerId,
            @RequestParam @NotNull Long appointmentId);

    /**
     * 15 分钟触发一次，每次扫描 20分钟的未发送数据
     */
    @GetMapping("/service/grooming/ob/abandoned-record/abandonRecordSendEvent")
    void abandonRecordSendEvent();
}
