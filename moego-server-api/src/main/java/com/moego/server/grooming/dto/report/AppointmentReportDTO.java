package com.moego.server.grooming.dto.report;

import com.moego.server.grooming.enums.AppointmentStatusEnum;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class AppointmentReportDTO {
    private Integer id;
    private String orderId;
    private Integer businessId;
    private Integer customerId;
    private String appointmentDate;
    private Integer appointmentStartTime;
    private Integer appointmentEndTime;
    private Byte isWaitingList;
    private Integer moveWaitingBy;
    private Long confirmedTime;
    private Long checkInTime;
    private Long checkOutTime;
    private Long canceledTime;
    // 1未确认 2确认 3已完成 4已取消
    private Byte status;
    // 是否为block 1-是 2-否
    private Integer isBlock;
    // 1 未确认 2 confirm 3 waitinglist 4 cancle
    private Byte bookOnlineStatus;
    private Integer customerAddressId;
    private Integer repeatId;
    // 是否已完全支付 1是 2否( 部分支付为2)
    private Byte isPaid;
    private String colorCode;
    // 用户爽约 1-爽约 2-没有爽约
    private Byte noShow;
    private BigDecimal noShowFee;
    // 是否推送过通知 1-是 2-否; 数据库列名甚至 typo 了
    private Byte isPustNotification;
    // 0-by business; 1-by customer reply msg; 2-by delete pet
    private Byte cancelByType;
    private Integer cancelBy;
    // 0-by business; 1-by customer reply msg
    private Byte confirmByType;
    private Integer confirmBy;
    private Integer createdById;
    private Byte outOfArea;
    private Integer isDeprecate;
    private Long createTime;
    private Long updateTime;
    // 22168-book-online 22018-web;17216-android;17802-ios
    private Integer source;
    private String oldAppointmentDate;
    private Integer oldAppointmentStartTime;
    private Integer oldAppointmentEndTime;
    private Integer oldApptId;
    // 1-普通 repeat，2-ss repeat
    private Byte scheduleType;
    // Possible values: GOOGLE; INSTAGRAM; FACEBOOK; and other relevant platforms.
    private String sourcePlatform;
    private Long readyTime;
    // 发送 ready for pickup 通知的状态; 0 - 未发送; 1 - 已发送; 2 - 发送失败
    private Integer pickupNotificationSendStatus;
    private String pickupNotificationFailedReason;
    private AppointmentStatusEnum statusBeforeCheckin;
    private AppointmentStatusEnum statusBeforeReady;
    private AppointmentStatusEnum statusBeforeFinish;
    private Boolean noStartTime;
    private Long updatedById;
    private Long companyId;
    private Boolean isAutoAccept;
}
