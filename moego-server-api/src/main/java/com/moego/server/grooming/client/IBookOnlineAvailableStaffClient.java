package com.moego.server.grooming.client;

import com.moego.server.grooming.api.IBookOnlineAvailableStaffService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-grooming-server",
        url = "${moego.server.url.grooming}",
        contextId = "IBookOnlineAvailableStaffClient")
public interface IBookOnlineAvailableStaffClient extends IBookOnlineAvailableStaffService {}
