package com.moego.server.grooming.dto.printcard;

import com.moego.server.grooming.dto.LodgingInfo;
import java.util.List;

public record ActivityCardAddOnColumn(
        Integer petId,
        String petName,
        Integer petTypeId,
        String breed,
        String gender,
        String avatarPath,
        String weight,
        List<PrintPetCodeBinding> petCodeBindings,
        String addOnName,
        Long serviceId,
        String associatedServiceName,
        Integer startTime,
        String staffName,
        Integer quantityPerDay,
        String clientFirstName,
        String clientLastName,
        Long lodgingTypeId,
        Long lodgingUnitId,
        String lodgingUnitName,
        List<LodgingInfo> lodgingInfos,
        String ticketComments) {}
