package com.moego.server.grooming.dto;

import com.moego.server.grooming.enums.PackageActivityEnum;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class GroomingPackageHistoryDTO {

    private Integer id;
    private Integer invoiceId;
    private Integer packageId;
    private Integer packageServiceId;
    private Integer serviceId;
    private Integer quantity;
    private String appointmentDate;
    private Long useTime;
    private Integer groomingId;
    private String confirmationId;
    private String packageName;
    private String packageDesc;
    private BigDecimal packagePrice;
    private Long purchaseTime;
    private Long startTime;
    private Long endTime;
    private Integer retailInvoiceItemId;
    private Byte status;
    private PackageActivityEnum activityType;
    private String serviceName;
    private String afterExtendExpireDate;
    private Integer staffId;
}
