package com.moego.server.grooming.dto;

import lombok.Data;

@Data
public class MoeGroomingAppointmentDTO {

    private Integer id;
    private String orderId;
    private Integer businessId;
    private Integer customerId;
    private String appointmentDate;
    private String appointmentEndDate;
    private Integer appointmentStartTime;
    private Integer appointmentEndTime;
    private Byte isWaitingList;
    private Integer moveWaitingBy;
    private Long confirmedTime;
    private Long checkInTime;
    private Long checkOutTime;
    private Long canceledTime;
    private Byte status;
    private Byte bookOnlineStatus;
    private Integer customerAddressId;
    private Integer repeatId;
    private Byte isPaid;
    private String colorCode;
    private Byte noShow;
    private Byte isPustNotification;
    private Byte cancelByType;
    private Integer cancelBy;
    private Byte confirmByType;
    private Integer confirmBy;
    private Integer createdById;
    private Byte outOfArea;
    private Long createTime;
    private Long updateTime;
    private Integer source;
    private Integer isBlock;
}
