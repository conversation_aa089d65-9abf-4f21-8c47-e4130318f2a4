package com.moego.server.grooming.dto.calendarcard;

import com.moego.server.business.dto.CertainAreaDTO;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class ClientDTO {
    private Long clientId;
    private String customerLastName;
    private String customerFirstName;
    private String clientColor;
    private Boolean isNewClient;
    private String fullAddress;
    private List<CertainAreaDTO> areas;

    // client full address(包含 city zipcode)
    private String address1;
    private String address2;
    private String country;
    private String state;
    // City
    private String city;
    // Zipcode
    private String zipcode;
    private String lat;
    private String lng;
}
