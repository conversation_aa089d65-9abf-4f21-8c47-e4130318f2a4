package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class OrderItemPetDetailDTO {

    private Integer petDetailId;
    private Integer petId;
    private String petName;
    private Integer serviceId;
    private Integer serviceTime;
    private BigDecimal servicePrice;
    private Long startTime;
    // 本条 pet detail 等价的 order line item 中 quantity数量
    private Integer quantity;
    // pet breed name
    private String petBreed;
    // staff信息
    private Integer staffId;
    private String staffFirstName;
    private String staffLastName;

    private List<GroomingServiceOperationDTO> operationList;

    private Integer serviceItemType;
    private Long lodgingId;
    private String lodgingUnitName;
    private String lodgingTypeName;

    private List<LodgingInfo> lodgingInfos;
}
