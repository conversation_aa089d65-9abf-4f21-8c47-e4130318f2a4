package com.moego.server.grooming.dto.groomingreport;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * Grooming report template 记录，moe_grooming_report_template 表对象
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GroomingReportTemplateDTO {

    private Integer id;
    private Integer businessId;

    @Schema(description = "thank you message")
    private String thankYouMessage;

    @Schema(description = "theme color")
    private String themeColor;

    @Schema(description = "light theme color")
    private String lightThemeColor;

    @Schema(description = "default theme code")
    private String themeCode;

    @Schema(description = "show showcase section")
    private Boolean showShowcase;

    @Schema(description = "show overall feedback section")
    private Boolean showOverallFeedback;

    @Schema(description = "required before photo")
    private Boolean requireBeforePhoto;

    @Schema(description = "required after photo, unused, reserved field")
    private Boolean requireAfterPhoto;

    @Schema(description = "show pet condition when fill in")
    private Boolean showPetCondition;

    @Schema(description = "show staff name")
    private Boolean showServiceStaffName;

    @Schema(description = "show next appointment section")
    private Boolean showNextAppointment;

    @Schema(description = "next appointment datetime format type: 1-only date, 2-date and time")
    private Byte nextAppointmentDateFormatType;

    @Schema(description = "show review booster on report page")
    private Boolean showReviewBooster;

    @Schema(description = "show yelp review icon")
    private Boolean showYelpReview;

    @Schema(description = "yelp review icon jump link")
    private String yelpReviewLink;

    @Schema(description = "show google review icon")
    private Boolean showGoogleReview;

    @Schema(description = "google review icon jump link")
    private String googleReviewLink;

    @Schema(description = "show facebook review icon")
    private Boolean showFacebookReview;

    @Schema(description = "facebook review icon jump link")
    private String facebookReviewLink;

    @Schema(description = "last publish time")
    private Long lastPublishTime;

    private Integer updateBy;
    private Long createTime;
    private Long updateTime;

    @Schema(description = "customized grooming report title")
    private String title;

    @Schema(description = "feedback, pet condition question list")
    private TemplateQuestions questions;

    public record TemplateQuestions(
            List<GroomingReportQuestionDTO> feedbacks, List<GroomingReportQuestionDTO> petConditions) {}
}
