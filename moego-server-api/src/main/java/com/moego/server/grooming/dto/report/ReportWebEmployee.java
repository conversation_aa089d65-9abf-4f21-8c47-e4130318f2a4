package com.moego.server.grooming.dto.report;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportWebEmployee {

    /**
     * full report
     */
    private String employeeName;

    private Integer staffId;

    /**
     * 服务过的宠物数量，在所有预约中去重统计
     */
    private Integer servicedPetNum;

    private BigDecimal totalServicesAmount;

    private BigDecimal totalAddOnsAmount;

    private BigDecimal totalProductAmount;

    private BigDecimal collectedServicePrice;

    private BigDecimal collectedProductPrice;

    private BigDecimal serviceCommission;

    private BigDecimal collectedAddonPrice;

    private BigDecimal addonCommission;

    private BigDecimal tipsCommission;

    private BigDecimal totalPayroll;

    /**
     * 已支付的金额（包括tips和tax，discount）
     */
    private BigDecimal collectedRevenue;

    private BigDecimal totalPayment;

    private BigDecimal totalRefund;

    // 净实收收入 = payment - convenienceFee - tax - tips - refund
    private BigDecimal netSaleRevenue;

    private BigDecimal unpaidRevenue;

    private BigDecimal totalTips;

    private BigDecimal totalTax;

    private Integer totalApptsAll;

    private Integer totalAppts;

    /**
     * 服务过的宠物次数，在单个预约中去重统计，一个预约中的一个宠物计算为一次
     */
    private Integer totalPets;

    private Integer totalClients;

    private Long totalServiceMinute;

    private Integer totalWorkingMinute;

    private Integer totalBlockMinute;

    private Double totalServiceHour;

    private Double avgMinutePerPet;

    private String status;

    /**
     * payroll report
     */
    private String apptDateTime;

    private String apptDate;

    private Integer apptTime;

    private String ownerName;

    private Integer ownerId;

    private BigDecimal revenue;

    private Integer petNum;

    private BigDecimal tips;

    private BigDecimal tax;

    private BigDecimal discount;

    private BigDecimal orderTotalTips;

    /**
     * 预约支付状态，参考 GroomingUtil.getAppPaidDesc 方法
     */
    private String paymentStatus;

    // clock in/out report
    private Integer totalWorkingHour;

    private Double avgHourPerDay;

    private Integer aptId;

    /**
     * 预约状态，参考 GroomingUtil.getApptStatus 方法
     */
    private String apptStatus;

    private List<String> services;

    private List<String> addOns;

    /**
     * product item names
     */
    private List<String> products;

    private List<Integer> petIds;

    private List<String> petNames;

    private Integer customerId;

    private String clientType;

    private String clientAddress;

    private Integer invoiceId;

    private Map<String, BigDecimal> paymentMap;

    /**
     * dashboard 统计中使用
     * 已收入的tips ：Fully paid&partially paid的 appt
     */
    private BigDecimal collectedTips;

    /**
     * 已收入的tax：Fully paid&partially paid的 appt
     */
    private BigDecimal collectedTax;

    /**
     * 已收入的discount：Fully paid&partially paid的 appt
     */
    private BigDecimal collectedDiscount;

    /**
     * 被标记为finished的appt-涉及该staff-booking ID加总
     */
    private Integer finishedApptNum;

    /**
     *被标记为partial/fully paid的appt-涉及该staff-booking ID加总
     */
    private Integer paidApptNum;

    /**
     * 被标记为finished的appt-涉及staff-pet加总
     */
    private Integer finishedPetNum;

    /**
     * 不包括tips，tax和discount，在collectedRev的基础上减去tips和tax，再加上discount
     */
    private BigDecimal collectedRevAll;

    /**
     * 记录当前 staff 每个 service/add-on 对应的实收金额，commissionCalculationBase = actualPayment 时计算 Commission 使用
     */
    @Builder.Default
    private final Map<Integer, BigDecimal> serviceCollectedMap = new HashMap<>();

    @Builder.Default
    private final Map<Integer, BigDecimal> addonCollectedMap = new HashMap<>();
    /**
     * finish 预约的 service 应收金额 map，commissionCalculationBase = finish appointment 时计算 Commission 使用
     */
    @Builder.Default
    private final Map<Integer, BigDecimal> serviceExpectedMap = new HashMap<>();

    @Builder.Default
    private final Map<Integer, BigDecimal> addonExpectedMap = new HashMap<>();

    /**
     * finish 预约的应收金额
     */
    private BigDecimal finishExpectedServicePrice;

    private BigDecimal finishExpectedAddonPrice;

    private BigDecimal finishExpectedTips;

    /**
     * 命中 payroll exception 的 service/add-on 总实收金额，计算Commission时使用
     */
    private BigDecimal exceptionServiceBase;

    private BigDecimal exceptionAddonBase;
}
