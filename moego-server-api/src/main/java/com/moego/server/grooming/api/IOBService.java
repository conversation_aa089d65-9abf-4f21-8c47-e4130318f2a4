package com.moego.server.grooming.api;

import com.moego.common.params.CustomerIdsParams;
import com.moego.server.grooming.dto.CustomerHasRequestDTO;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 这个类放的全是迁移 OB submit 接口时遇到的实在迁移不动的接口 ：）
 *
 * <p> 除非你知道你在干什么，否则不要使用这个类！
 *
 * <AUTHOR>
 */
public interface IOBService {

    /**
     * 保存 OB submit 时的 agreements。
     *
     * @param param {@link SaveAgreementForObSubmitParam}
     * @return dummy value, always true
     */
    @PostMapping("/service/grooming/ob-submit/saveAgreementForObSubmit")
    boolean saveAgreementForObSubmit(@RequestBody SaveAgreementForObSubmitParam param);

    /**
     * Save card for OB submit, aka. Card on File (cof).
     *
     * @param param {@link SaveCardParam}
     * @return {@link SaveCardResult}
     */
    @PostMapping("/service/grooming/ob-submit/saveCard")
    SaveCardResult saveCard(@RequestBody SaveCardParam param);

    @Builder
    record SaveAgreementForObSubmitParam(List<Agreement> agreements, int bookingRequestId) {
        @Builder
        public record Agreement(
                Integer agreementId,
                Byte agreementConfirmed,
                String signature,
                String agreementHeader,
                String agreementContent) {}
    }

    @Builder
    record SaveCardParam(int businessId, int customerId, String chargeToken, boolean isNewCustomer) {}

    @Builder
    record SaveCardResult(
            Integer businessId,
            Integer customerId,
            String stripeCustomerId,
            String stripeBizAccountId,
            String paymentMethodId,
            String cardNumber,
            String cardType) {}

    @PostMapping("/service/grooming/listCustomerHasRequestUpdate")
    Map<Integer, CustomerHasRequestDTO> listCustomerHasRequestUpdate(@RequestBody CustomerIdsParams params);

    @PostMapping("/service/grooming/doAutoAssign")
    AutoAssignResult doAutoAssign(@RequestBody BookOnlineSubmitParams obParams);

    @Builder
    record AutoAssignResult(Integer staffId, Integer appointmentTime) {}
}
