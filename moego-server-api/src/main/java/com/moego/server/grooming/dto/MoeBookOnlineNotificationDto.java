package com.moego.server.grooming.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MoeBookOnlineNotificationDto {

    private Integer id;
    private Integer businessId;
    private Byte submitClientType;
    private Byte submitBusinessType;
    private String submitEmailSubjectTemplate;
    private Byte acceptClientType;
    private String acceptEmailSubjectTemplate;
    private Byte acceptBusinessType;
    private Byte autoMoveClientType;
    private Byte autoMoveBusinessType;
    private String autoMoveEmailSubjectTemplate;
    private Byte declineClientType;
    private Byte declineBusinessType;
    private String declineEmailSubjectTemplate;
    private String submitTemplate;
    private String submitEmailContentTemplate;
    private String acceptTemplate;
    private String acceptEmailContentTemplate;
    private String autoMoveTemplate;
    private String autoMoveEmailContentTemplate;
    private String declineTemplate;
    private String declineEmailContentTemplate;
    private String notificationEmail;
}
