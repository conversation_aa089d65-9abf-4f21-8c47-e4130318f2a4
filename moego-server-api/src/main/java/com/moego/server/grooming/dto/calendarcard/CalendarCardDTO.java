package com.moego.server.grooming.dto.calendarcard;

import com.moego.server.grooming.dto.AutoAssignDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.BookingTypeEnum;
import com.moego.server.grooming.enums.PaymentStatusEnum;
import com.moego.server.grooming.enums.calendar.CardTypeEnum;
import com.moego.server.payment.dto.PreAuthDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CalendarCardDTO {
    @Schema(description = "card unique id, 唯一标识一张卡片，用于前端操作")
    private String cardId;

    @Schema(description = "card id, 针对不同的 card Type，id 表示不同的实体主键")
    private Long id;

    @Schema(description = "预约状态，1 - unconfirmed，2 - confirmed，3 - finished，4 - cancelled，5 - ready，6 - checkin")
    private AppointmentStatusEnum appointmentStatus;

    private Long appointmentId;
    private Long staffId;

    @Schema(description = "支付状态，1 - fully paid，2 - unpaid，3 - partial paid")
    private PaymentStatusEnum paymentStatus;

    @Schema(description = "卡片类型，1 - appointment，2 - service，3 - operation")
    private CardTypeEnum cardType;

    @Schema(description = "book 来源类型，0 - 普通预约，1 - online booking")
    private BookingTypeEnum bookingType;

    private PreAuthDTO preAuthInfo;
    private Boolean requiredSign; // 是否签署agreement true不需要签署

    private String alertNotes;
    private String ticketComments;

    private String date;
    private String endDate;
    private Long startTime;
    private Long endTime;
    private Long repeatId;
    private String colorCode;

    private Boolean isAutoAccept;
    private AutoAssignDTO autoAssign;

    private ClientDTO clientInfo;

    private List<PetInfoDTO> petList;

    private BigDecimal estimatedTotalPrice;

    private BigDecimal paidAmount;
    private BigDecimal prepaidAmount;

    @Schema(description = "预支付记录状态，参考BookOnlineDepositConst")
    private Byte prepayStatus;

    // 一张卡片可能对应多个petDetailId，reschedule 的时候需要知道哪些 petDetailId 需要被 reschedule
    private List<Integer> petDetailIds;

    private DraggableInfoDTO draggableInfo;

    public void addPet(PetInfoDTO petInfoDTO) {
        if (petList == null) {
            petList = new ArrayList<>();
        }
        petList.add(petInfoDTO);
    }

    public CalendarCardDTO(
            Long id,
            CardTypeEnum cardTypeEnum,
            String date,
            String endDate,
            Long startTime,
            Long endTime,
            Long staffId,
            String colorCode,
            BookingTypeEnum bookingTypeEnum) {
        this.id = id;
        this.cardType = cardTypeEnum;
        this.date = date;
        this.endDate = endDate;
        this.startTime = startTime;
        this.endTime = endTime;
        this.staffId = staffId;
        this.colorCode = colorCode;
        this.bookingType = bookingTypeEnum;
        this.petList = new ArrayList<>();
    }
}
