package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TipSplitDetailDTO {

    @Schema(description = "订单id")
    private Long orderId;

    @Schema(description = "tip split method: 1-by service, 2-by equally, 3-customized")
    private Integer splitMethod;

    @Schema(description = "customized tip type: 1-amount, 2-percentage")
    private Integer customizedType;

    @Schema(description = "staff按三种分配方式的金额")
    private List<StaffTipAmountDTO> staffTipAmountList;

    @Schema(description = "tips to business")
    BigDecimal businessTipAmount;

    @Getter
    @Setter
    public static class StaffTipAmountDTO {

        @Schema(description = "staff id")
        Integer staffId;

        @Schema(description = "不同的Split method对应的tips金额")
        Map<String, AmountPercentagePair> amountMap;
    }
}
