package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.OBAvailableTimeWithNonAvailableDTO;
import com.moego.server.grooming.dto.ob.OBTimeSlotQueryDTO;
import java.util.Map;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface ITimeSlotsService {

    @PostMapping("/timeslots/getAvailableTimeBySlot")
    Map<String, OBAvailableTimeWithNonAvailableDTO> getAvailableTimeBySlot(
            @RequestBody OBTimeSlotQueryDTO obTimeSlotQueryDTO);
}
