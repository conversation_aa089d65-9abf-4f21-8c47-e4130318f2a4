package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class GroomingPackageInfoDTO {

    @Schema(description = "package history 总结果数")
    private Integer packageHistoryCount;

    @Schema(description = "package service 结果列表")
    private List<GroomingPackageServiceDTO> packageServices;

    @Schema(description = "package history 分页结果列表")
    private List<GroomingPackageHistoryDTO> packageHistories;
}
