package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.GroomingQuestionDTO;
import com.moego.server.grooming.dto.ob.BookOnlineQuestionSaveDTO;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2023/7/3
 */
public interface IBookOnlineQuestionService {

    /**
     * List questions by condition, not include deleted questions.
     *
     * @param param condition
     * @return list of questions
     */
    @PostMapping("/service/grooming/book-online-questions/listByCondition")
    List<GroomingQuestionDTO> listByCondition(@RequestBody ListByConditionParam param);

    /**
     * TODO: account structure 是否查询 company 维度的 question save
     *
     * @param businessId
     * @param customerId
     * @return
     */
    @GetMapping("/service/grooming/ob/getCustomerLatestQuestionSave")
    BookOnlineQuestionSaveDTO getCustomerLatestQuestionSave(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    @GetMapping("/service/grooming/ob/listCustomerLatestQuestionSave")
    List<BookOnlineQuestionSaveDTO> listCustomerLatestQuestionSave(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerIds") List<Integer> customerIds);

    @PostMapping("/service/grooming/ob/upsertCustomerQuestionSave")
    Boolean upsertCustomerQuestionSave(
            @RequestBody BookOnlineQuestionSaveDTO questionSaveDTO,
            @RequestParam("autoAcceptConflict") Boolean autoAcceptConflict);

    @PostMapping("/service/grooming/ob/saveCustomerQuestionSave")
    Boolean saveCustomerQuestionSave(@RequestBody BookOnlineQuestionSaveDTO questionSaveDTO);

    @Builder
    record ListByConditionParam(@NotNull Integer businessId, @Nullable Integer type) {}
}
