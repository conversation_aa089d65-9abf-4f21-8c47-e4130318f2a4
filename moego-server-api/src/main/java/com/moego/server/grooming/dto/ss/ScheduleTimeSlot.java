package com.moego.server.grooming.dto.ss;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ScheduleTimeSlot {

    private Integer startTime;
    private Integer endTime;

    private Integer driveInMinutes;
    private Double driveInMiles;
    private Integer driveOutMinutes;
    private Double driveOutMiles;

    private Integer addUpMinutes;

    private Boolean isDriveFromStart;
    private Boolean isDriveToEnd;
}
