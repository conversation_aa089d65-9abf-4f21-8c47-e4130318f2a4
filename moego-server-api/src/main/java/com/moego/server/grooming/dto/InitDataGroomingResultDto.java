package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class InitDataGroomingResultDto {

    private Integer fullLargeServiceId;
    private BigDecimal fullLargeServicePrice;
    private Integer fullLargeServiceTime;
    private Integer fullLargeServiceType;

    private Integer fullMediumServiceId;
    private BigDecimal fullMediumServicePrice;
    private Integer fullMediumServiceTime;
    private Integer fullMediumServiceType;

    private Integer fullSmallServiceId;
    private BigDecimal fullSmallServicePrice;
    private Integer fullSmallServiceTime;
    private Integer fullSmallServiceType;
}
