package com.moego.server.grooming.dto;

import jakarta.annotation.Nullable;
import lombok.Data;

@Data
public class AppointmentTrackingViewDTO {

    private Long appointmentId;

    private Integer staffLocationStatus;
    // staff who share location, 0 means no staff share location
    private Long locationSharingStaffId;
    // device id of the staff who share location
    private String locationSharingDeviceId;
    // the last time when staff location status is change to transit
    private Long lastInTransitAt;
    // estimated travel seconds from staff current location to customer location
    private Long estimatedTravelSeconds;
    // last estimate time for estimated_travel_seconds
    private Long lastEstimateAt;
    // travel distance from staff current location to customer location
    private Long travelDistance;
    // delay status from staff current location to customer location
    // see api/moego/models/appointment/v1/appointment_tracking.proto.DelayedStatus
    private Integer delayedStatus;
    // estimated travel seconds from address of last in transit appointment to customer location
    private Long estimatedTravelSecondsFromLastInTransit;
    // last estimate time for estimated_travel_seconds_from_last_in_transit
    private Long fromLastInTransitLastEstimateAt;
    // last in transit appointment of the staff
    private Long lastInTransitAppointmentId;
    // travel distance from address of last in transit appointment to customer location
    private Long travelDistanceFromLastInTransit;
    // delay status from address of last in transit appointment to customer location
    // see api/moego/models/appointment/v1/appointment_tracking.proto.DelayedStatus
    private Integer delayedStatusFromLastInTransit;

    private Address customerAddress;

    @Nullable
    private Address staffAddress;

    @Data
    public static class Address {
        private String address1;
        private String address2;
        private String country;
        private String city;
        private String state;
        private String zipcode;

        @Nullable
        private LatLng coordinate;
    }

    @Data
    public static class LatLng {
        private double latitude;
        private double longitude;
    }
}
