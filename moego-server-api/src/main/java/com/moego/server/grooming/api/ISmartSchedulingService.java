/*
 * @since 2024-09-17 17:59:02
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.ss.SmartScheduleResultDto;
import com.moego.server.grooming.params.ss.SmartScheduleRequest;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface ISmartSchedulingService {
    @PostMapping("/service/grooming/smartScheduling/smartSchedule")
    SmartScheduleResultDto smartSchedule(
            @RequestParam(value = "businessId") Integer businessId, @RequestBody @Valid SmartScheduleRequest request);
}
