package com.moego.server.grooming.client;

import com.moego.server.grooming.api.IPetCustomizedServiceService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-grooming-server",
        url = "${moego.server.url.grooming}",
        contextId = "IPetCustomizedServiceClient")
public interface IPetCustomizedServiceClient extends IPetCustomizedServiceService {}
