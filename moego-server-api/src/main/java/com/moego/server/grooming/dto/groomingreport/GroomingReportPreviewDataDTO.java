package com.moego.server.grooming.dto.groomingreport;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Sample data 构造的 Grooming report preview 对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroomingReportPreviewDataDTO {

    @Schema(description = "report summary info")
    private GroomingReportSummaryInfoDTO reportSummary;

    @Schema(description = "sample value for grooming report")
    private GroomingReportSampleValue sampleValue;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GroomingReportSampleValue {

        private String comment;
        private String petAvatarUrl;
        private List<String> showcaseUrls;
        private BodyViewUrl bodyViewUrls;
    }
}
