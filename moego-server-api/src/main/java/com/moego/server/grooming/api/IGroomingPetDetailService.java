package com.moego.server.grooming.api;

import com.moego.api.common.Range;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.GroomingPetServiceListInfoDTO;
import com.moego.server.grooming.dto.PetDetailDTO;
import com.moego.server.grooming.params.CommonIdsParams;
import com.moego.server.grooming.params.EditPetDetailStaffCommissionParam;
import com.moego.server.grooming.params.PetPassAwayParams;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Validated
public interface IGroomingPetDetailService {

    @PostMapping("/service/grooming/queryPetDetail")
    List<GroomingPetDetailDTO> queryPetDetailByGroomingId(@RequestBody CommonIdsParams commonIdsParams);

    /**
     * DONE(account structure): 取消 company 维度的预约
     *
     * 提供给customer模块，当pet去世后或者删除后，操作对应的grooming appointment
     *
     * @param params
     * @return
     */
    @PutMapping("/service/grooming/pet/remove")
    Boolean petPassAwayOrDelete(@RequestBody PetPassAwayParams params);

    @GetMapping("/service/grooming/appointment/finished")
    List<GroomingPetServiceListInfoDTO> queryFinishedPetDetailByBusinessIdList(
            @RequestParam("businessIdList") List<Integer> businessIdList,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    @GetMapping("/service/grooming/initRouteOptimizationTimes")
    void initRouteOptimizationTimes(@RequestParam("companyId") Long companyId);

    /**
     * 查询包含指定日期范围的所有 boarding PetDetail。
     *
     * @param param {@link ListBoardingPetDetailByDateRangeParam}
     * @return pet detail list
     */
    @PostMapping("/service/grooming/pet-detail/listBoardingPetDetailByDateRange")
    List<PetDetailDTO> listBoardingPetDetailByDateRange(@RequestBody ListBoardingPetDetailByDateRangeParam param);

    /**
     * 查询包含指定日期范围的所有 PetDetail。
     *
     * @param param {@link ListPetDetailByDateRangeParam}
     * @return pet detail list
     */
    @PostMapping("/service/grooming/pet-detail/listPetDetailByDateRange")
    List<PetDetailDTO> listPetDetailByDateRange(@RequestBody ListPetDetailByDateRangeParam param);

    /**
     * 根据petDetailId列表查询详情，包括operation信息
     *
     * @param petDetailIdList petDetailId列表
     * @return order item pet detail list
     */
    @GetMapping("/service/grooming/pet-detail/listOrderItemPetDetail")
    List<GroomingPetDetailDTO> listGroomingPetDetail(
            @RequestParam("businessId") Long businessId,
            @RequestParam("groomingId") Long groomingId,
            @RequestParam("petDetailIdList") List<Long> petDetailIdList);

    @PostMapping("/service/grooming/pet-detail/updateStaff")
    void updatePetDetailStaff(@RequestBody EditPetDetailStaffCommissionParam params);

    @PostMapping("/service/grooming/queryAllPetDetail")
    List<GroomingPetDetailDTO> queryAllPetDetailByGroomingId(
            @RequestParam("businessId") Long businessId, @RequestParam("groomingId") Long groomingId);

    @Builder
    record ListBoardingPetDetailByDateRangeParam(int businessId, @NotNull Range<String> dateRange) {}

    @Builder
    record ListPetDetailByDateRangeParam(int businessId, @NotNull Range<String> dateRange) {}
}
