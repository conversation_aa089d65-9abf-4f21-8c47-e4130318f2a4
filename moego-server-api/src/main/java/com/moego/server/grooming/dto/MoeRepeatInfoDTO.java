package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class MoeRepeatInfoDTO extends GroomingRepeatDTO {

    @Deprecated
    private String startsOnDate;

    @Deprecated
    private String setEndOnDate;

    @Schema(description = "当前预约 upcoming 数量小于 repeat expiry reminder 时 upcoming 的数量，不为空时需要展示")
    private Integer expiryUpcomingCount;

    private List<RepeatAppointmentDto> existsAppointments;
}
