package com.moego.server.grooming.dto.appointment;

import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class StaffUpcomingAppointmentCountDTO {

    private Long staffId;
    private List<LocationUpcomingAppointmentCount> locationUpcomingCountList;

    @Data
    @Builder(toBuilder = true)
    public static class LocationUpcomingAppointmentCount {
        private Long businessId;
        private Integer upcomingAppointmentCount;
    }
}
