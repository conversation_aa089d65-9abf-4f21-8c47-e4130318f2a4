package com.moego.server.grooming.client;

import com.moego.server.grooming.api.IAbandonedScheduleMessageSettingService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-grooming-server",
        url = "${moego.server.url.grooming}",
        contextId = "IAbandonedScheduleMessageSettingClient")
public interface IAbandonedScheduleMessageSettingClient extends IAbandonedScheduleMessageSettingService {}
