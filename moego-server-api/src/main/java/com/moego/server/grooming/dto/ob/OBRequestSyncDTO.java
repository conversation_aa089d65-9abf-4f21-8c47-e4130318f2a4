package com.moego.server.grooming.dto.ob;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/3/28
 */
@Data
@Accessors(chain = true)
public class OBRequestSyncDTO {
    public static final String KEY = "grooming:ob-request-sync-count";
    public static final String CONSUMER_KEY = "grooming:ob-request-consumer-count";
    public static final String FAILED_KEY = "grooming:ob-request-failed";
    private int producerOffset;
    private int producerCount;
    private int consumerOffset;
    private int consumerCount;
}
