package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ConflictInfoDTO {

    @Schema(description = "已存在预约的 id")
    private Integer appointmentId;

    @Schema(description = "预约内的 staff id list")
    private List<Integer> staffIdList;

    @Schema(description = "预约内每个 staff 的 conflict info")
    private List<StaffConflictInfoDTO> staffConflictInfoList;

    @Schema(description = "appointment date")
    private String date;

    @Schema(description = "appointment 的开始时间")
    private Integer startTime;

    @Schema(description = "appointment 的结束时间")
    private Integer endTime;

    @Schema(description = "入参传的 serviceTime，和老接口保持一致")
    private Integer duration;

    @Schema(description = "是否冲突: true-不冲突, false-冲突")
    private Boolean isNotConflict;
}
