package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.BookOnlineStaffTimeDTO;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface IBookOnlineStaffTimeService {

    @GetMapping("/service/grooming/book-online-staff-time/getBookOnlineStaffTimeByBusinessId")
    List<BookOnlineStaffTimeDTO> listBookOnlineStaffTimeByBusinessId(@RequestParam Integer businessId);
}
