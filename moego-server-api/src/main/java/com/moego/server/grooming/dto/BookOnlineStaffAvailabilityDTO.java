package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BookOnlineStaffAvailabilityDTO {

    @NotNull
    private Integer staffId;

    @Schema(description = "by working hour type该员工是否enable: 0-disable, 1-enable")
    @Max(1)
    @Min(0)
    private Byte byWorkingHourEnable;

    @Schema(description = "by slot type该员工是否enable: 0-disable, 1-enable")
    @Max(1)
    @Min(0)
    private Byte bySlotEnable;

    @Schema(description = "该员工是否使用 working hour: 0-disable, 1-enable")
    @Max(1)
    @Min(0)
    private Byte syncWithWorkingHour;
}
