package com.moego.server.grooming.dto;

import lombok.Data;

/**
 * 预约订单详情表(新)
 */
@Data
public class PetDetailDTO {
    /**
     * 预约订单详情id
     */
    private Integer id;
    /**
     * 预约订单主表id(mm_appointment_table)
     */
    private Integer groomingId;
    /**
     * 宠物id
     */
    private Integer petId;
    /**
     * 负责服务的员工id
     */
    private Integer staffId;
    /**
     * 服务id
     */
    private Integer serviceId;
    /**
     * 1-主服务 2-额外服务
     */
    private Integer serviceType;
    /**
     * 服务预估时间(考虑save time)
     */
    private Integer serviceTime;
    /**
     * 服务预估费用(考虑save price)
     */
    private Double servicePrice;
    /**
     * 服务开始时间
     */
    private Long startTime;
    /**
     * 服务结束时间
     */
    private Long endTime;
    /**
     * 状态 1-正常 2-已删除  3因为修改的删除
     */
    private Integer status;
    /**
     * 修改时间
     */
    private Long updateTime;
    /**
     * 服务价格的生效范围类型  1-this appt 2 this and future(this appt和未来创建的预约会生效)
     */
    private Integer scopeTypePrice;
    /**
     * 服务时间的生效范围类型  1-this appt 2 this and future(this appt和未来创建的预约会生效)
     */
    private Integer scopeTypeTime;
    /**
     * 标星的staff id
     */
    private Integer starStaffId;
    /**
     * 使用package的packageServiceid
     */
    private Integer packageServiceId;
    /**
     *
     */
    private String serviceName;
    /**
     *
     */
    private String serviceDescription;
    /**
     * 税率id
     */
    private Integer taxId;
    /**
     * 税率
     */
    private Double taxRate;
    /**
     * enable operation
     */
    private Boolean enableOperation;
    /**
     * work mode, 0-parallel, 1-sequence
     */
    private Integer workMode;
    /**
     * color code
     */
    private String serviceColorCode;
    /**
     * 开始日期
     */
    private String startDate;
    /**
     * 结束日期
     */
    private String endDate;
    /**
     * service item, 1-grooming, 2-boarding, 3-daycare
     */
    private Integer serviceItemType;
    /**
     * lodging id
     */
    private Long lodgingId;
    /**
     * 1 - per session, 2 - per night, 3 - per hour
     */
    private Integer priceUnit;
    /**
     * add-on specific dates, yyyy-MM-dd
     */
    private String specificDates;
    /**
     * add-on associated service id
     */
    private Long associatedServiceId;
}
