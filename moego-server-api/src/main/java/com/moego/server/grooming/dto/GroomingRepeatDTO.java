package com.moego.server.grooming.dto;

import java.util.List;
import lombok.Data;

/**
 * moe_grooming_repeat 对象
 */
@Data
public class GroomingRepeatDTO {

    private Integer id;
    private Integer customerId;
    private Integer businessId;
    private Integer staffId;
    private Byte repeatType;
    private Integer repeatEvery;
    private Integer repeatBy;
    private String startsOn;
    private Integer times;
    private Long createTime;
    private Long updateTime;
    private Byte status;
    private String endOn;
    private Byte isNotice;
    private String setEndOn;
    private Byte repeatEveryType;
    private Byte monthDay;
    private Byte monthWeekTimes;
    private Byte monthWeekDay;
    private String type;

    // smart scheduling标志，0-不是，1-是，用于区分普通repeat和repeat with ss
    private Byte ssFlag;
    // smart scheduling for repeat配置项
    private Integer ssBeforeDays;
    private Integer ssAfterDays;

    private List<Integer> repeatByDays;
}
