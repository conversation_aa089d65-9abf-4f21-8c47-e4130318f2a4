package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.CompanyBusinessIdDTO;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
public interface IBookOnlinePetLimitService {
    /**
     * please use {@link #getInUsedPetSizeIdListV2(CompanyBusinessIdDTO)} instead
     * @param businessId
     * @return
     */
    @Deprecated
    @GetMapping("/service/grooming/ob/pet/limit/used/size")
    List<Long> getInUsedPetSizeIdList(@RequestParam("businessId") Integer businessId);

    @PostMapping("/service/grooming/ob/pet/limit/used/size/v2")
    List<Long> getInUsedPetSizeIdListV2(@RequestBody CompanyBusinessIdDTO idDTO);
}
