package com.moego.server.grooming.params;

import com.moego.idl.models.order.v1.OrderDetailModelV1;
import com.moego.idl.service.order.v2.PreviewDepositOrderResponse;
import jakarta.annotation.Nullable;
import java.util.List;
import lombok.Data;

/**
 * 区别于{@link BookOnlineDepositParams}，这个是为 new invoice 的 deposit order 提供的必要参数。
 */
@Data
public class BookOnlineDepositV2Params {
    @Nullable
    private OrderDetailModelV1 depositOrderPreview;

    @Nullable
    private List<PreviewDepositOrderResponse.PriceItemByRule> depositOrderPriceItems;
}
