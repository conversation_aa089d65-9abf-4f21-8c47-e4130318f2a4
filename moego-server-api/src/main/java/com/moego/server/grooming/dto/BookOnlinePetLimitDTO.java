package com.moego.server.grooming.dto;

import com.moego.server.business.dto.PetSizeDTO;
import com.moego.server.customer.dto.MoePetBreedDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BookOnlinePetLimitDTO {

    private Long id;

    private Integer businessId;

    /**
     * type 1-size 2-breed
     */
    private Byte type;

    private Integer maxNumber;

    /**
     * size info
     */
    private PetSizeDTO petSizeDTO;

    /**
     * breed info petTypeId
     */
    private Integer petTypeId;

    /**
     * breed info allBreed
     */
    private Boolean allBreed;

    /**
     * breed info detail
     */
    private List<MoePetBreedDTO> petBreedDTOList;
}
