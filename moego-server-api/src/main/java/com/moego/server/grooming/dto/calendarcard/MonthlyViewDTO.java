package com.moego.server.grooming.dto.calendarcard;

import com.moego.server.grooming.dto.AutoAssignDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.BookingTypeEnum;
import com.moego.server.grooming.enums.calendar.CardTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class MonthlyViewDTO {
    @Schema(description = "card unique id, 唯一标识一张卡片，用于前端操作")
    private String cardId;

    private Long appointmentId;
    private String appointmentDate;
    private Long startTime;
    private Long endTime;
    private String colorCode;
    private String customerLastName;
    private String customerFirstName;
    private String customerColor;
    private String serviceColorCode;
    private Integer staffId;
    private Boolean noStartTime;
    private Long id;
    private CardTypeEnum cardType;
    private List<Integer> petDetailsIds;

    @Schema(description = "预约状态，1 - unconfirmed，2 - confirmed，3 - finished，4 - cancelled，5 - ready，6 - checkin")
    private AppointmentStatusEnum appointmentStatus;

    @Schema(description = "book 来源类型，0 - 普通预约，1 - online booking")
    private BookingTypeEnum bookingType;

    private Boolean isBlock;

    @Schema(description = "描述信息， 仅对 block 类型有效")
    private String desc;

    private Integer customerId;

    private Integer petDetailId;
    private Integer repeatId;

    private Boolean isAutoAccept;
    private AutoAssignDTO autoAssign;
}
