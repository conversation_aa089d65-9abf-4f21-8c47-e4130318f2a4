package com.moego.server.grooming.dto;

import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.PickupNotificationStatusEnum;
import com.moego.server.payment.dto.PreAuthDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class GroomingTicketDetailDTO {

    private Boolean requiredSign; // 是否签署agreement true不需要签署

    private Integer id;
    private Integer businessId;
    private Integer invoiceId;
    private Integer repeatId;
    private Integer customerId;

    @Deprecated // deprecated by <PERSON><PERSON><PERSON> on ERP-1747, use appointmentStatus instead
    private Integer status;

    @Schema(description = "预约状态")
    private AppointmentStatusEnum appointmentStatus;

    private Long checkInTime;
    private Long readyTime;
    private Long checkOutTime;
    private String appointmentDate;
    private String appointmentEndDate;
    private Integer appointmentStartTime;
    private Integer appointmentEndTime;
    private String colorCode;
    private Integer createdById;
    private Long updatedById;
    private Byte bookOnlineStatus;
    private Byte isWaitingList;
    private Long createTime;
    private Integer isPaid;

    private Integer noShow;
    private BigDecimal noShowFee;
    private Integer noShowStatus;
    private Integer noShowInvoiceId;
    private String noShowPaymentStatus;

    private String cancelReason;
    // 创建人
    private String createByLastName;
    private String createByFirstName;
    private GroomingCustomerInfoDTO groomingCustomerInfo;
    private GroomingTicketCommentsDTO groomingTicketComments;
    private GroomingTicketNotesDTO groomingAlertNotes;
    /**
     * Only for ob requests
     */
    private GroomingTicketNotesDTO additionalNote;

    private List<GroomingPetInfoDetailDTO> petInfoDetails;
    private String customerQuestionJson;
    private String customerAnswerJson;

    private Boolean reviewBoosterSent; // 是否已经发送review booster

    @Schema(description = "已支付金额")
    private BigDecimal paidAmount;

    @Schema(description = "预先支付的金额")
    private BigDecimal prepaidAmount;

    @Schema(description = "退款金额")
    private BigDecimal refundAmount;

    @Schema(description = "预支付金额占总金额的比例")
    private Double prepayRate;

    @Schema(description = "预支付记录状态，参考BookOnlineDepositConst")
    private Byte prepayStatus;

    private PreAuthDTO preAuthInfo;

    private Integer source;

    @Schema(description = "是否成功发送 pickup notification")
    private PickupNotificationStatusEnum pickupNotificationSendStatus;

    private String pickupNotificationFailedReason;
    private Boolean allPetsStartAtSameTime;

    private Boolean noStartTime;
    private String sourcePlatform;

    private Boolean isAutoAccept;
    private AutoAssignDTO autoAssign;

    @Schema(description = "关联的 waitList")
    private Long waitListId;

    @Schema(description = "当前 ticket 包含的 service 类型")
    private List<Integer> serviceItems;

    private AppointmentTrackingViewDTO appointmentTracking;
}
