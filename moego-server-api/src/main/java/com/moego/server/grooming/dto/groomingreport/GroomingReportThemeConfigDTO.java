package com.moego.server.grooming.dto.groomingreport;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class GroomingReportThemeConfigDTO {

    private String name;
    private String code;
    private String color;
    private String lightColor;
    private String imgUrl;
    private String icon;
    private String emailBottomImgUrl;
    private Boolean recommend;
    private Byte status;

    @Schema(description = "0-正常，1-需要升级到 Growth+")
    private Byte tag;
}
