package com.moego.server.grooming.dto.appointment.history;

import java.math.BigDecimal;
import java.util.List;

public record EditAppointmentLogDTO() {
    public record AppointmentLogDTO(List<PetLogDTO> pets) {
        public record PetLogDTO(String petName, List<ServiceLogDTO> services) {
            public record ServiceLogDTO(
                    String serviceName, String staffName, BigDecimal price, Integer duration, Boolean enable) {
                public record ServiceOperationLogDTO(
                        String operationName, String staffName, BigDecimal price, Integer duration) {}
            }
        }
    }
}
