package com.moego.server.grooming.dto;

import lombok.Data;

@Data
public class AppointmentDTO {

    private Integer id;
    private String orderId;
    private Long companyId;
    private Integer businessId;
    private Integer customerId;
    private String appointmentDate;
    private Integer appointmentStartTime;
    private String appointmentEndDate;
    private Integer appointmentEndTime;
    private Byte isWaitingList;
    private Integer isDeprecate;
    private Integer moveWaitingBy;
    private Long confirmedTime;
    private Long checkInTime;
    private Long checkOutTime;
    private Long canceledTime;
    private Byte status;
    private Byte bookOnlineStatus;
    private Integer customerAddressId;
    private Integer repeatId;
    private Byte isPaid;
    private String colorCode;
    private Byte noShow;
    private Byte isPustNotification;
    private Byte cancelByType;
    private Integer source;
    private Integer serviceTypeInclude;
    private Long createTime;
}
