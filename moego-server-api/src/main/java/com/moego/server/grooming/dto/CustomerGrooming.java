package com.moego.server.grooming.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.moego.server.grooming.dto.waitlist.WaitListCompatibleDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class CustomerGrooming {

    //    private String bookingType;//预约类型
    //    private Date appointmentDate;//预约时间
    //    private String appointmentDateString;//预约时间
    //    private String orderId;//预约orderId
    //    private Integer groomingId;//预约id
    //    private String petId;//宠物id
    //    private String petName;//预约id
    //    private String status;//预约状态

    private Integer id;
    private String orderId;
    private Boolean hasExtraOrder;
    private String appointmentDate;
    private String appointmentEndDate;
    private Integer appointmentStartTime;
    private Integer appointmentEndTime;
    private Long confirmedTime;
    private Long checkInTime;
    private Long checkOutTime;
    private Long canceledTime;

    @Deprecated // deprecated by ZhangDong on ERP-1747, use appointmentStatus instead
    private Integer status;

    private Integer bookOnlineStatus;
    private Integer customerId;
    private Integer customerAddressId;
    /**
     * Legacy invoice (V1) 专用字段，V2、V3、V4 因为有多个 order 不能使用该字段，使用 isPaid 字段替代
     * @see #isPaid
     */
    private String paymentStatus;
    // no-show 单支付状态
    private String noShowPaymentStatus;

    @Deprecated // use paymentStatus instead
    private Integer isPaid;

    private String colorCode;
    private Integer noShow;
    private Long createTime;
    private Integer groomingCount;
    private Byte cancelByType;

    private Long appointmentOrderId;
    private Long noShowOrderId;
    // 有no-show展示no-show id，没no-show展示主单id
    @Deprecated // use appointmentOrderId or noShowOrderId instead.
    private Integer invoiceId;

    private Integer noShowStatus;
    private BigDecimal noShowFee;
    private BigDecimal cancellationFee;
    private String cancelReason;

    private List<GroomingCustomerPetdetailDTO> petServiceList;
    private List<GroomingEvaluationServiceDetailDTO> evaluationServiceList;

    @Schema(description = "总金额")
    private BigDecimal subTotalAmount;

    @Schema(description = "已支付金额")
    private BigDecimal paidAmount;

    @Schema(description = "预先支付的金额")
    private BigDecimal prepaidAmount;

    @Schema(description = "退款金额")
    private BigDecimal refundAmount;

    @Schema(description = "预支付金额占总金额的比例")
    private Double prepayRate;

    @Schema(description = "预支付记录状态，参考BookOnlineDepositConst")
    private Byte prepayStatus;

    @Schema(description = "new field for appointment status")
    private AppointmentStatusEnum appointmentStatus;

    // 老版 waitList 查询接口兼容字段
    private WaitListCompatibleDTO waitListDTO;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(type = "string", description = "business id")
    private Integer businessId;

    @Schema(description = "当前 appt 包含的 service 类型 bitmap 值")
    private Integer serviceTypeInclude;

    @Schema(description = "当前 appt 包含的 service 类型列表")
    private List<Integer> serviceItems;
}
