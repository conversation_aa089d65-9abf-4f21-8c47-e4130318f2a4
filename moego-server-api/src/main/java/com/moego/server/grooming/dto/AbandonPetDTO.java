package com.moego.server.grooming.dto;

import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AbandonPetDTO {

    private Integer id;
    private Integer businessId;
    private String bookingFlowId;
    private Integer petId;
    private Integer indexId;
    private Integer serviceId;
    private String addonIds;
    private Integer petTypeId;
    private String avatarPath;
    private String petName;
    private String breed;
    private String weight;
    private String hairLength;
    private String vaccineList;
    private String birthday;
    private Byte gender;
    private String fixed;
    private String behavior;
    private String vetName;
    private String vetPhoneNumber;
    private String vetAddress;
    private String emergencyContactName;
    private String emergencyContactPhone;
    private String petQuestionAnswers;
    private Date createTime;
    private Date updateTime;
    private Long companyId;
    private String healthIssues;
}
