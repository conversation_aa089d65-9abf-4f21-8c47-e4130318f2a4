package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/9/25
 */
@Data
@Accessors(chain = true)
public class AppointmentDetailClientPortalDTO {
    /**
     * Appointment id
     */
    private Integer id;

    private Integer businessId;
    private Integer customerId;
    private String appointmentDate;
    private Integer appointmentStartTime;
    private Integer appointmentEndTime;
    private boolean noStartTime;
    private Byte status;
    private Byte paymentStatus;
    private Byte bookOnlineStatus;
    private Integer source;
    private List<PetDetailClientPortalDTO> petDetails;

    @Data
    @Accessors(chain = true)
    public static class PetDetailClientPortalDTO {
        private Integer petId;
        private Integer staffId;
        private Integer serviceId;
        private Integer serviceTime;
        private BigDecimal servicePrice;
    }
}
