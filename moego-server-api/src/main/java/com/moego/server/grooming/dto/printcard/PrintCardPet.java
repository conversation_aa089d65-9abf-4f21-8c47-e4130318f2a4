package com.moego.server.grooming.dto.printcard;

import java.util.List;
import java.util.Map;

public record PrintCardPet(
        String petName,
        Integer petTypeId,
        String breed,
        String gender,
        String avatarPath,
        String weight,
        List<String> petNotes,
        List<String> petCodeList,
        Map<String, String> vaccineMap,
        String vetName,
        String vetPhoneNumber,
        String healthIssues,
        String petAppearanceColor,
        String petAppearanceNotes,
        String petFixed,
        String birthday,
        List<PrintPetCodeBinding> petCodeBindings) {}
