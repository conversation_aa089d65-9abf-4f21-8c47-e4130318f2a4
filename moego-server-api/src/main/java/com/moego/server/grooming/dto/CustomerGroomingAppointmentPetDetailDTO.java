package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class CustomerGroomingAppointmentPetDetailDTO {

    private Integer petDetailId;
    private Integer groomingId;
    private Integer petId;
    private Integer staffId;
    private Integer serviceId;
    private String serviceName;
    private Integer serviceTime;
    private BigDecimal servicePrice;
    private Byte serviceType;
    private Byte serviceInactive;
    private String petName;
    private String petBreed;
    private Byte status;
    // service 是否删除
    private Byte serviceIsDelete;
    private Byte petIsDelete;
    private Byte serviceAvailableForBookingOnline;
    private Integer lifeStatus;

    /**
     * work mode
     * 0 - parallel
     * 1 - sequence
     */
    private Integer workMode;

    private Integer scopeTypePrice;
    private Integer scopeTypeTime;

    private List<GroomingServiceOperationDTO> operationList;
}
