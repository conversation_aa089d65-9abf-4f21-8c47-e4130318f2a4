package com.moego.server.grooming.dto;

import java.time.DayOfWeek;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * {@link ClientType} 等等的类型不要使用枚举，在（反）序列化时，老代码没办法处理新加的枚举！
 *
 * <AUTHOR>
 */
@Data
public class AbandonedScheduleMessageSettingDTO {

    private Integer id;
    private Integer businessId;
    /**
     * @see ClientType
     */
    private List<String> clientTypes;
    /**
     * @see com.moego.server.grooming.dto.AbandonRecordDTO.AbandonStep
     */
    private List<String> abandonedSteps;
    /**
     * @see SendOutType
     */
    private String sendOutType;
    /**
     * sendOutType 为 {@link SendOutType#ON} 时的配置
     */
    private List<DayOfWeek> onTypeDays;
    /**
     * sendOutType 为 {@link SendOutType#ON} 时的配置
     */
    private Integer onTypeMinute;
    /**
     * sendOutType 为 {@link SendOutType#WAIT_FOR} 时的配置
     */
    private Integer waitForTypeMinute;

    private String message;
    private Boolean isEnabled;
    private Date createdAt;
    private Date updatedAt;
    private Date enabledAt;

    public enum ClientType {
        NEW_VISITORS,
        EXISTING_CLIENTS
    }

    public enum SendOutType {
        ON,
        WAIT_FOR
    }
}
