package com.moego.server.grooming.dto.report;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 5/24/21 3:00 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportTrendPeriodDTO {

    @Schema(description = "上一个周期的统计数据，key：日期，value：统计值")
    private Map<String, Object> lastPeriod;

    @Schema(description = "当前周期的统计数据，key：日期，value：统计值")
    private Map<String, Object> currentPeriod;
}
