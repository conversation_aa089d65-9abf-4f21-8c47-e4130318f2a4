package com.moego.server.grooming.dto;

import java.util.List;
import lombok.Data;

@Data
public class AppointmentReminderSendDTO {

    private Integer id;
    private String orderId;
    private Integer businessId;
    private Integer customerId;
    private String appointmentDate;
    private Integer appointmentStartTime;
    private Integer appointmentEndTime;
    private Byte status;
    private Integer customerAddressId;
    private Long createTime;
    private Long updateTime;

    private Integer petId;
    private String petName;

    private Integer serviceId;
    private String serviceName;

    private String customerFirstName;
    private String customerLastName;

    private Integer staffId;
    private String staffFirstName;
    private String staffLastName;

    private String date;
    private String time;
    private String dayOfWeek;

    List<GroomingPetDetailDTO> groomingPetDetails;
    private List<EvaluationServiceDetailDTO> evaluationServiceDetails;
}
