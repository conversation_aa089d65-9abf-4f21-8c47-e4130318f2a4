package com.moego.server.customer.api;

import com.moego.server.customer.dto.CustomerMigrationResult;
import com.moego.server.customer.params.CustomerMigrationParams;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface ICustomerMigrationService {
    @PostMapping("/service/customer/account/structure/migration")
    CustomerMigrationResult migrate(@RequestBody CustomerMigrationParams params);

    @PostMapping("/service/customer/account/structure/migration/fixAddress")
    void fixAddress();
}
