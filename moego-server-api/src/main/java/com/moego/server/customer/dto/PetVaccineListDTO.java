package com.moego.server.customer.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.idl.models.offering.v1.ServiceItemType;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PetVaccineListDTO {

    private Integer id;

    private String name;

    private Integer businessId;

    private Integer sort;

    private Integer createTime;

    private Integer updateTime;

    private Boolean onlyForSpecificPetType;

    private List<PetType> availablePetTypes;

    private List<ServiceItemType> requiredByServiceItemTypes;
}
