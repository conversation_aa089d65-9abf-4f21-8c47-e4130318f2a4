package com.moego.server.customer.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class CustomerBasicDTO {

    private Integer id;
    private Integer customerId;
    /**
     * please use preferredBusinessId instead
     */
    @Deprecated
    private Integer businessId;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(type = "string", description = "preferred business id")
    private Long preferredBusinessId;

    @Schema(hidden = true)
    private Long companyId;

    private String avatarPath;
    private String email;
    private String firstName;
    private String lastName;

    @Schema(description = "customer code(8), all customers have and only have pre-generated one.")
    private String customerCode;

    private Byte status;
    private Byte inactive;
    private String clientColor;
    private Byte isBlockMessage;
    private Byte isBlockOnlineBooking;

    @Deprecated
    private String loginEmail;

    private Integer referralSourceId;
    private String referralSourceDesc;
    private Byte sendAutoEmail;
    private Byte sendAutoMessage;
    private Byte sendAppAutoMessage;

    @Deprecated
    private Byte unconfirmedReminderBy; // 已弃用，请使用apptReminderByList

    private Integer preferredGroomerId;
    private Integer preferredFrequencyDay;
    private Byte preferredFrequencyType;
    private String lastServiceTime;
    private String expectedServiceDate;
    private String source;
    private String externalId;
    private Long createTime;
    private Long updateTime;
    private Byte isRecurring;
    // 新增字段：customer prefer的日期和时间段
    private Integer[] preferredDay;
    private Integer[] preferredTime;

    private Byte isUnsubscribed;

    @Schema(description = "对应复选框选项： 1-Message 2-email  3-phone call")
    private List<Byte> apptReminderByList;

    /**
     * Pet parent app account id
     */
    private Long accountId;

    private LocalDateTime birthday;

    // CRM-leads 字段
    private String type;
    private String lifeCycle;
    private String actionState;
    private Long allocateStaffId;
}
