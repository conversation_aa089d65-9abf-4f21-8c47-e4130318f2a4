package com.moego.server.customer.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.moego.server.customer.dto.AdditionalContactDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SaveWithPetCustomerVo {

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(type = "string", description = "preferred business id")
    private Long preferredBusinessId;

    @Size(max = 50)
    private String firstName;

    @Size(max = 50)
    private String lastName;

    @Size(max = 30)
    private String phoneNumber;

    @Size(max = 255)
    private String avatarPath;

    @Size(max = 50)
    private String email;

    @Size(max = 255)
    private String address1;

    @Size(max = 255)
    private String city;

    @Size(max = 255)
    private String state;

    @Size(max = 10)
    private String zipcode;

    @Size(max = 255)
    private String country;

    @Size(max = 255)
    private String address2;

    @Size(max = 50)
    private String lat;

    @Size(max = 50)
    private String lng;

    private Integer referralSourceId;

    @Size(max = 255)
    private String referralSourceDesc;

    @Valid
    private List<SaveWithPetPetVo> petList;

    // hide para

    private String clientColor;

    private Integer preferredGroomerId;

    /**
     * Preferred frequency
     */
    private Integer preferredFrequencyDay;

    /**
     * Preferred frequency 0-by days 1-by weeks
     */
    private Byte preferredFrequencyType;

    /**
     * Preferred day of the week
     */
    private Integer[] preferredDay;

    /**
     * Preferred time of the day
     */
    private Integer[] preferredTime;

    private String rawId;
    private Long rawCreateTime;
    private Long rawUpdateTime;

    @Schema(description = "Client source")
    private String source;

    private Byte isUnsubscribed;
    private Byte sendAutoMessage;
    private Byte sendAutoEmail;

    @Schema(description = "对应复选框选项： 1-Message 2-email  3-phone call")
    private List<Byte> apptReminderByList;

    @Schema(description = "credit card token for stripe, for COF use")
    @Size(max = 128)
    private String chargeToken;

    @Schema(description = "标记是否添加COF")
    @Null
    @JsonIgnore
    private Boolean isAddCof;

    private LocalDateTime birthday;

    private AdditionalContactDTO emergencyContact;

    private AdditionalContactDTO pickupContact;
}
