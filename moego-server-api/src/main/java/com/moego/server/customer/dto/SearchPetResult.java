package com.moego.server.customer.dto;

import com.moego.common.utils.Pagination;
import java.util.List;
import lombok.Data;

public record SearchPetResult(List<PetWithCustomerDTO> pets, Pagination pagination) {

    @Data
    public static class PetWithCustomerDTO {

        private Integer petId;

        private String petName;

        private String petAvatarPath;

        private Integer petTypeId;

        private String petBreed;

        // null means no weight, please check null before using it
        private Double petWeight;

        private String petCoatType;

        private String petBirthday;

        private Integer customerId;

        private String customerFirstName;

        private String customerLastName;

        private Double score;
    }
}
