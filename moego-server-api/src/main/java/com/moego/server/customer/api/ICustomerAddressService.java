package com.moego.server.customer.api;

import com.moego.server.customer.params.FindAddressIdsByKeywordParam;
import java.util.Set;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface ICustomerAddressService {
    /**
     * DONE(account structure): OB Abandon Record 搜索用，暂时不需要 company 维度，保留 business 维度，后面有需要再改
     */
    @PostMapping("/service/customer/address/findAddressIdsByKeyword")
    Set<Integer> findAddressIdsByKeyword(@RequestBody FindAddressIdsByKeywordParam param);
}
