package com.moego.server.customer.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Set;
import lombok.Data;

@Data
public class CustomerNearByParams {

    public static final String CLIENT_STATUS_ACTIVE = "activeClient";
    public static final String CLIENT_STATUS_INACTIVE = "inactiveClient";
    public static final String CLIENT_STATUS_LAPSED = "lapsed";
    public static final String CLIENT_STATUS_BLOCK_MESSAGE = "blockMessage";

    // use businessIds and companyId instead of businessId
    @Deprecated
    private Integer businessId;

    @JsonIgnore
    private Long companyId;

    @JsonIgnore
    private Set<Integer> businessIds;

    private Double lat; // 经度
    private Double lng; // 纬度
    private Double customerRadius; // 半径（英里）

    private Double radius; // 半径（米)

    private String tags;
    /**
     *     1.Added from online booking
     *
     *     2.Added from intake form
     *
     *     3.Clients on the waiting list
     *
     *     4.Last service was 30 days ago
     */
    private Integer category;

    private String statusSetting;

    private CustomerSearchStatusVo status; // 新增筛选条件支持

    public void computeRadius() {
        if (customerRadius != null && customerRadius != 0) {
            radius = customerRadius * 1609.344;
        }
    }
}
