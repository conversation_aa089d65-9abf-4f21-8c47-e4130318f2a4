package com.moego.server.customer.api;

import com.moego.server.customer.dto.OBLoginTokenDTO;
import com.moego.server.customer.dto.OBLogoutDTO;
import com.moego.server.customer.params.CreateOBLoginTokenParams;
import com.moego.server.customer.params.OBLogoutParams;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface ICustomerOnlineBookingService {
    /**
     * create ob login token
     */
    @PostMapping("/service/customer/ob/login/token")
    OBLoginTokenDTO genLoginToken(@RequestBody CreateOBLoginTokenParams params);

    /**
     * please use logout v2
     */
    @Deprecated
    @PostMapping("/service/customer/ob/logout")
    Boolean logout(@RequestParam("sessionId") Integer sessionId);

    /**
     * logout ob
     */
    @PostMapping("/service/customer/ob/logout-v2")
    OBLogoutDTO logoutV2(@RequestBody OBLogoutParams params);
}
