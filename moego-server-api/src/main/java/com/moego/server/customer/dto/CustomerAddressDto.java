package com.moego.server.customer.dto;

import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
public class CustomerAddressDto {

    private Integer customerAddressId;

    @Positive
    private Integer customerId;

    @Size(max = 255)
    private String address1;

    @Size(max = 255)
    private String address2;

    @Size(max = 255)
    private String city;

    @Size(max = 255)
    private String country;

    @Size(max = 255)
    private String lat;

    @Size(max = 255)
    private String lng;

    @Size(max = 255)
    private String state;

    @Size(max = 255)
    private String zipcode;

    @PositiveOrZero
    private Byte isPrimary;

    /**
     * Customer address 可能来自两个地方：
     * <p> B 端（customer address 表）
     * <p> C 端（profile request address 表）
     */
    private Boolean isProfileRequestAddress = false;

    @Getter
    @RequiredArgsConstructor
    public enum IsPrimary {
        TRUE((byte) 1),
        FALSE((byte) 0);
        private final Byte value;
    }
}
