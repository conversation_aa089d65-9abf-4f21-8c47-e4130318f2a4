package com.moego.server.customer.api;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface ICustomerFixService {

    record FixCustomerEmailParams(Integer fromId, Integer toId, Integer batchSize) {}

    record FixCustomerEmailResult(
            int noOwnContactCount,
            int multipleOwnContactCount,
            int updateContactEmailCount,
            int updateCustomerEmailCount,
            int conflictEmailCount) {}

    @PostMapping("/service/customer/fix/email")
    FixCustomerEmailResult fixCustomerEmail(@RequestBody FixCustomerEmailParams params);
}
