package com.moego.server.customer.dto;

import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ProfileRequestAddressDTO {
    private Integer id;
    private Integer customerId;
    private Integer customerAddressId;
    private String address1;
    private String address2;
    private String city;
    private String state;
    private String zipcode;
    private String country;
    private String lat;
    private String lng;
    private Boolean isPrimary;
    private Date createdAt;
    private Date updatedAt;

    public static CustomerAddressDto toCustomerAddressDTO(ProfileRequestAddressDTO dto) {
        CustomerAddressDto result = new CustomerAddressDto();
        result.setCustomerId(dto.getCustomerId());
        result.setAddress1(dto.getAddress1());
        result.setAddress2(dto.getAddress2());
        result.setCity(dto.getCity());
        result.setCountry(dto.getCountry());
        result.setLat(dto.getLat());
        result.setLng(dto.getLng());
        result.setState(dto.getState());
        result.setZipcode(dto.getZipcode());
        result.setIsPrimary(
                dto.getIsPrimary()
                        ? CustomerAddressDto.IsPrimary.TRUE.getValue()
                        : CustomerAddressDto.IsPrimary.FALSE.getValue());

        if (dto.getCustomerAddressId() != null) {
            result.setIsProfileRequestAddress(false);
            result.setCustomerAddressId(dto.getCustomerAddressId());
        } else {
            result.setIsProfileRequestAddress(true);
            result.setCustomerAddressId(dto.getId());
        }

        return result;
    }
}
