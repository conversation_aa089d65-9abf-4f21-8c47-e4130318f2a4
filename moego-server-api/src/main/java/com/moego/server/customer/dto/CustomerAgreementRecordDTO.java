package com.moego.server.customer.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class CustomerAgreementRecordDTO {

    private Integer agreementId;
    private Integer agreementRequiredType;
    private String agreementHeader;

    private List<AgreementRecordTimeDTO> signedDates;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(type = "string", description = "business id")
    private Integer businessId;
}
