package com.moego.server.customer.api;

import com.moego.server.customer.dto.PetPhotoDTO;
import com.moego.server.customer.params.PetPhotoParams;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IPetPhotoService {
    /**
     * DONE(account structure): 已兼容 company / business 鉴权
     *
     * create new pet photo
     *
     * @param tokenBusinessId
     * @param petPhotoSaveVo
     * @return
     */
    @PostMapping("/service/customer/petPhoto/savePetPhoto")
    Integer savePetPhoto(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId, @RequestBody PetPhotoParams petPhotoSaveVo);

    /**
     * DONE(account structure)
     */
    @GetMapping("/service/customer/petPhoto/getPetPhotoById")
    PetPhotoDTO getPetPhotoById(@RequestParam("petPhotoId") int petPhotoId);
}
