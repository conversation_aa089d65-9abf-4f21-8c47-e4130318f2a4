package com.moego.server.customer.params;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
@Deprecated
public class CustomerLoginParams {

    // todo: remove businessId
    @Deprecated
    private Integer businessId;

    // @NotBlank todo: open this after removing businessId
    private String businessName;

    @NotEmpty
    private String phoneNumber;

    private String verifyCode;
}
