package com.moego.server.customer.api;

import com.moego.server.customer.dto.GroomingCalenderCustomerInfo;
import com.moego.server.customer.dto.GroomingCustomerInfoDTO;
import com.moego.server.customer.params.GroomingCustomerInfoParams;
import com.moego.server.customer.params.GroomingQueryCustomerParams;
import java.util.List;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2021/11/16 11:40 AM
 */
public interface ICustomerGroomingService {
    /**
     * 给grooming模块获取预约列表信息调用 获取customer相关的数据，会根据dataRule控制信息返回
     *
     * @param customerInfoParams customerInfoParams
     * @return list
     */
    @PostMapping("/service/customer/grooming/getGroomingCalenderCustomerInfo")
    List<GroomingCalenderCustomerInfo> getGroomingCalenderCustomerInfo(
            @RequestBody GroomingCustomerInfoParams customerInfoParams);

    /**
     * DONE(account structure)
     *
     * 根据customerIds查询customer信息
     * * @param groomingQueryCustomerParams
     *
     * @return
     */
    @PostMapping("/service/customer/grooming/getCustomerGroomingInfo")
    List<GroomingCustomerInfoDTO> getCustomerGroomingInfo(
            @RequestBody GroomingQueryCustomerParams groomingQueryCustomerParams);

    /**
     * DONE(account structure): 已改为 company 维度
     *
     * 当staff被删除时，需要同步清理商户下preferred_groomer_id为该staffId的记录
     *
     * @param businessId 商户Id
     * @param staffId    被删除的staffId
     * @return updated row
     */
    @PostMapping("/service/customer/grooming/updateCustomerPreferredGroomerId")
    Integer updateCustomerPreferredGroomerId(
            @RequestParam("businessId") Integer businessId, @RequestParam("staffId") Integer staffId);

    /**
     * DONE(account structure): 无需修改
     */
    @PutMapping("/service/customer/grooming/markCustomerRecurring")
    void markCustomerRecurring(@RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 无需修改
     */
    @DeleteMapping("/service/customer/grooming/markCustomerNonRecurring")
    void markCustomerNonRecurring(@RequestParam("customerId") Integer customerId);
}
