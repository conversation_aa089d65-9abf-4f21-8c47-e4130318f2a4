package com.moego.server.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class GroomingCustomerInfoDTO {

    private Integer customerId;
    private String email;
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private String avatarPath;
    private Byte sendAutoEmail;
    private Byte sendAutoMessage;
    private Byte sendAppAutoMessage;

    private Byte unconfirmedReminderBy;

    @Schema(description = "对应复选框选项： 1-Message 2-email  3-phone call")
    private List<Byte> apptReminderByList;

    private Byte status;
    private Byte inactive;
}
