package com.moego.server.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/2/1 6:09 PM
 */
@Data
public class CustomerWithAddressAndContact {

    private Integer customerId;
    private String firstName;
    private String lastName;
    private String email;
    private Long createTime;

    @Schema(description = "1-正常 2-删除")
    private Byte status;

    @Schema(description = "0-正常 1-inactive")
    private Byte inactive;

    @Schema(description = "client Primary contact")
    private String primaryContact;

    @Schema(description = "client Additional contact")
    private List<String> additionalContact;

    @Schema(description = "client address")
    private List<String> addresses;
}
