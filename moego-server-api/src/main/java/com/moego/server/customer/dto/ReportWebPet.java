package com.moego.server.customer.dto;

import java.util.List;
import lombok.Data;

/**
 * BeanUtil copy with ReportPet
 */
@Data
public class ReportWebPet {

    private Integer petId;
    private String petName;
    private String ownerName;
    private String breed;
    private Boolean isMix;
    private String type;
    private String birthday;
    private String gender;
    private String hairLength;
    private String weight;
    private String fixed;
    private String behavior;
    private String vetName;
    private String vetContactNumber;
    private String vetAddress;
    private String emergencyContactName;
    private String emergencyContactNumber;
    private String healthIssue;
    private Boolean isPassAway;
    private List<String> petCodes;

    private String vaccination;
    private String expirationDate;
    private String primaryNumber;
    private String email;
    private String status;

    private Integer totalPets;
    private String petCode;
}
