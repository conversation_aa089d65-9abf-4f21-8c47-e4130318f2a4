package com.moego.server.customer.dto;

import java.util.List;
import lombok.Data;

@Data
public class CustomerAgreementInfoDTO {

    private Integer id;
    private Integer customerId;
    private Integer businessId;
    private Integer agreementId;
    private String agreementHeader;
    private String agreementContent;
    private String uploadImages;
    private String signature;
    private Byte type;
    private Long createTime;
    private String servicesType;
    private Long updateTime;

    // 顾客姓名
    private String customerFirstName;
    private String customerLastName;

    // 商家名称
    private String businessName;

    // 签约时保存的输入值
    private List<String> inputs;
}
