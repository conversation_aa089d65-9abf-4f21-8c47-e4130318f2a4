package com.moego.server.customer.api;

import com.moego.server.customer.dto.ClientAccountInfoDTO;
import com.moego.server.customer.dto.ClientAccountLicenseTokenDTO;
import com.moego.server.customer.params.ClientAccountLicenseParams;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface ICustomerAccountService {
    @PostMapping("/service/customer/account/register/license")
    ClientAccountLicenseTokenDTO applyForRegisterLicense(@RequestBody ClientAccountLicenseParams params);

    @GetMapping("/service/customer/account/info")
    ClientAccountInfoDTO queryAccount(@RequestParam Integer accountId);
}
