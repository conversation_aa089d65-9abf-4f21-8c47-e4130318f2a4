package com.moego.server.customer.params;

import com.moego.common.params.PageParams;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2020-09-21 22:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RebookReminderParams extends PageParams {

    private Integer businessId;
    private List<Integer> dismissIds;
    private String atDay;
    private String startDate;
    private String endDate;
}
