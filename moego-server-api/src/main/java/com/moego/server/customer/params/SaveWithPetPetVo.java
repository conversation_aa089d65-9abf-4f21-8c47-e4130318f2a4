package com.moego.server.customer.params;

import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SaveWithPetPetVo {

    @Size(max = 255)
    private String avatarPath;

    @Size(max = 50)
    private String petName;

    @Size(max = 50)
    private String breed;

    private Byte breedMix;
    private Integer petTypeId;
    private Byte gender;

    @Size(max = 50)
    private String birthday;

    @Size(max = 50)
    private String weight;

    @Size(max = 50)
    private String fixed;

    @Size(max = 50)
    private String behavior;

    @Size(max = 50)
    private String hairLength;

    private Byte expiryNotification;

    @Size(max = 100)
    private String vetName;

    @Size(max = 30)
    private String vetPhone;

    @Size(max = 100)
    private String vetAddress;

    @Size(max = 100)
    private String emergencyContactName;

    @Size(max = 30)
    private String emergencyContactPhone;

    @Size(max = 3000)
    private String healthIssues;

    private List<Integer> petCodeIdList;

    private Byte evaluationStatus;
}
