package com.moego.server.customer.dto;

import java.util.StringJoiner;
import lombok.Data;
import org.springframework.util.StringUtils;

@Data
public class CustomerNearByDTO {

    /**
     * Client name ,  第一只宠物的 Name 和 breed 。剩余宠物数量。Client Primary 地址信息
     */

    // pet name & breed
    private Integer petId;

    private String petName;
    private String petBreed;
    private Integer petNum;

    // client full name
    private Integer customerId;
    private String customerLastName;
    private String customerFirstName;

    // last service 日期
    private String lastServiceTime;

    // client full address
    private String address1;
    private String address2;
    private String country;
    private String state;
    // City
    private String city;
    // Zipcode
    private String zipcode;

    private String lat; // 经度
    private String lng; // 纬度

    private String clientFullAddress;

    private String avatarPath; // 头像

    private String clientColor;

    private CustomerGroomingAppointmentDTOC upcomingBooking; // 下一个预约

    public void splicingClientFullAddress() {
        StringJoiner sj = new StringJoiner(", ");

        if (StringUtils.hasText(address1)) {
            sj.add(address1);
        }
        if (StringUtils.hasText(address2)) {
            sj.add(address2);
        }
        if (StringUtils.hasText(city)) {
            sj.add(city);
        }
        if (StringUtils.hasText(state)) {
            sj.add(state);
        }
        if (StringUtils.hasText(country)) {
            sj.add(country);
        }
        if (StringUtils.hasText(zipcode)) {
            sj.add(zipcode);
        }

        clientFullAddress = sj.toString();
    }
}
