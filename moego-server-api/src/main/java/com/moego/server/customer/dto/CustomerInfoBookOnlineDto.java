package com.moego.server.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerInfoBookOnlineDto extends CustomerBasicDTO {

    // phone number email
    private String phoneNumber;
    private Integer primaryAddressId;
    private String primaryAddress;
    private String primaryLat;
    private String primaryLng;
    private String primaryAddressZipcode;
    // isHave
    private Boolean isHaveAddress;
    private Boolean isNeedSignature;
    private Boolean outOfArea;
    private Map<Integer, Boolean> agreementIsNeedSignature;

    @Deprecated
    private Boolean hasStripeCard;

    @Schema(description = "商家primary card processor 对应的customer是否已绑定卡")
    private Boolean hasCreditCard;
}
