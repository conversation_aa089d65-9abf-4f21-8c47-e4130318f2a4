package com.moego.server.customer.api;

import com.moego.server.customer.dto.CustomerPetPetCodeDTO;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IPetCodeService {
    /**
     * DONE(account structure): 不需要兼容;
     * 根据petIdlist查询pet信息
     *
     * @param petIdList
     * @return
     */
    @PostMapping("/service/customer/petCode/getCustomerPetPetCodeListByIdList")
    List<CustomerPetPetCodeDTO> getCustomerPetPetCodeListByIdList(
            @RequestParam("isGetObInfo") Boolean isGetObInfo, @RequestBody List<Integer> petIdList);
}
