package com.moego.server.customer.api;

import com.moego.common.params.SortIdListParams;
import com.moego.server.customer.dto.FormSaveDetailDTO;
import com.moego.server.customer.dto.IntakeFormCreateWithDetailsParam;
import com.moego.server.customer.dto.IntakeFormCreateWithDetailsResult;
import com.moego.server.customer.dto.IntakeFormDTO;
import com.moego.server.customer.dto.ListIntakeFormWithDetailsParam;
import com.moego.server.customer.dto.ListIntakeFormWithDetailsResult;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IIntakeFormService {
    /**
     * 删除intake form 和agreements的关联关系
     */
    @DeleteMapping("/service/customer/intakeForm/deleteAgreementsRelation")
    int deleteAgreementsRelation(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId, @RequestBody SortIdListParams commonIdsParams);

    @GetMapping("/service/customer/intakeForm/getSubmissionCountForNotification")
    Integer getSubmissionCountForNotification(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/customer/intakeForm/get")
    IntakeFormDTO getIntakeForm(@RequestParam("id") Integer id);

    @GetMapping("/service/customer/intakeForm/list")
    List<IntakeFormDTO> getIntakeForms(
            @RequestParam("companyId") Long companyId, @RequestParam("businessId") Long businessId);

    /**
     * 查询指定时间范围内的 IntakeForm 保存记录
     *
     * @param companyId 必填
     * @param customerId 必填
     * @param startTime 必填，开始时间，10 位 Unix 时间戳
     * @param endTime 可选，结束时间，10 位 Unix 时间戳，可为 null
     * @return 表单保存详情列表
     */
    @GetMapping("/service/customer/intakeForm/queryIntakeFormSaveByTimeRange")
    List<FormSaveDetailDTO> queryIntakeFormSaveByTimeRange(
            @RequestParam("companyId") Long companyId,
            @RequestParam("customerId") Long customerId,
            @RequestParam("startTime") Integer startTime,
            @RequestParam(value = "endTime", required = false) Integer endTime);

    @PostMapping("/service/customer/intakeForm/listIntakeFormWithDetails")
    ListIntakeFormWithDetailsResult listIntakeFormWithDetails(
            @RequestBody @Valid ListIntakeFormWithDetailsParam listIntakeFormWithDetailsParam);

    @PostMapping("/service/customer/intakeForm/createIntakeFormWithDetails")
    IntakeFormCreateWithDetailsResult createIntakeFormWithDetails(
            @RequestBody @Valid IntakeFormCreateWithDetailsParam intakeFormCreateWithDetailsParam);
}
