package com.moego.server.customer.dto;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PetListDetailDto {

    private Integer petId;
    private String petName;
    private Integer petTypeId;
    private String typeName;
    private String avatarPath;
    private Integer lifeStatus;
    private String deactivateReason;
    private String breed;
    private Integer breedMix;
    private String birthday;
    private Integer gender;
    private String hairLength;
    private String weight;
    private String fixed;
    private String behavior;
    private Byte status;
    private String petAppearanceColor;
    private String petAppearanceNotes;
    private List<MoePetCodeDTO> petCodeList;
    private VaccineStatusDto vaccineStatus;
}
