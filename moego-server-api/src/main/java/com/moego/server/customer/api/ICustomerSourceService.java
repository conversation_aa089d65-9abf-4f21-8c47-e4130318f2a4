package com.moego.server.customer.api;

import com.moego.server.customer.dto.CustomerSourceDto;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
public interface ICustomerSourceService {

    /**
     * DONE(account structure): 迁移后的用户请使用新接口
     */
    @Deprecated
    @GetMapping("/service/customer/source")
    List<CustomerSourceDto> getSourceList(@RequestParam("businessId") Integer businessId);
}
