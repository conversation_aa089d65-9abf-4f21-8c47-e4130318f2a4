package com.moego.server.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerInfoDto extends CustomerBasicDTO {

    private String phoneNumber;
    private CustomerAddressDto address;
    private String referralSource;
    private String referralSourceDesc;
    private Boolean isNewCustomer;
    private Boolean isProspectCustomer;
    private Boolean hasPetParentAppAccount;

    @Schema(description = "share appt 状态  0 all 1 unconfirm 2confirm 3 finished")
    private Byte shareApptStatus;

    @Schema(description = "0 all  1 in x days  2 next x appointment  3 manually apptids")
    private Byte shareRangeType;

    @Schema(description = "不同type时的value")
    private Integer shareRangeValue;

    //    private String shareApptJson;
    @Schema(description = "当share_range_type为3时，记录的所有apptIds，仅shareRangeType为3时生效")
    private List<Integer> shareApptIds;
}
