package com.moego.server.customer.api;

import com.moego.server.customer.dto.CustomerBasicWithPetDto;
import com.moego.server.customer.dto.CustomerContactDto;
import com.moego.server.customer.dto.PhoneNumberEmailDto;
import com.moego.server.customer.params.CustomerIdListParams;
import java.util.List;
import java.util.Map;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface ICustomerContactService {

    /**
     * DONE(account structure): 内部放开到 company 维度鉴权
     */
    @GetMapping("/service/customer/contact/getCustomerPrimaryPhoneNumberEmail")
    CustomerContactDto getCustomerPrimaryPhoneNumberEmail(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 内部已判断 company / business 维度鉴权
     */
    @PostMapping("/service/customer/contact/basic/withPet")
    Map<Integer, CustomerBasicWithPetDto> getCustomerPetBasicByCustomerIds(
            @RequestParam("businessId") Integer businessId, @RequestBody List<Integer> customerIds);

    /**
     * DONE(account structure): 内部放开到 company 维度鉴权
     * <p>
     * 根据contactId，获取单个contactId
     *
     * @param contactId
     * @return
     */
    @PostMapping("/service/customer/contact/queryCustomerContact")
    CustomerContactDto queryCustomerContact(
            @RequestParam("businessId") Integer businessId, @RequestParam("contactId") Integer contactId);

    /**
     * DONE(account structure): 内部已判断 company / business 维度鉴权
     * <p>
     * 批量获取customer的发送手机号和email
     *
     * @param businessId
     * @param idListParams
     * @return
     */
    @PostMapping("/service/customer/contact/queryCustomerContactList")
    Map<Integer, PhoneNumberEmailDto> queryCustomerContactList(
            @RequestParam("businessId") Integer businessId, @RequestBody CustomerIdListParams idListParams);

    /**
     * DONE(account structure): 无需修改
     */
    @PostMapping("/service/customer/contact/getCustomerContacts")
    Map<Integer, List<CustomerContactDto>> getCustomerContacts(@RequestBody List<Integer> customerIds);

    /**
     * DONE(account structure): 无需修改
     */
    @GetMapping("/service/customer/contact/getCustomerByPhoneNumber")
    CustomerContactDto getContactByPhoneNumber(
            @RequestParam("customerId") Integer customerId, @RequestParam("phoneNumber") String phoneNumber);

    /**
     * DONE(account structure): 无需修改
     * 这个接口没有调用方了，即将下线，请勿使用
     */
    @Deprecated
    @GetMapping("/service/customer/contact/getCustomerListByPhoneNumber")
    List<Integer> getCustomerIdListByPhoneNumber(@RequestParam("phoneNumber") String phoneNumber);

    /**
     * DONE(account structure): 无需修改
     */
    @GetMapping("/service/customer/contact/getOtherCustomerIdListByPhoneNumber")
    List<Integer> getOtherCustomerIdListByPhoneNumber(
            @RequestParam("phoneNumber") String phoneNumber, @RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 无需修改
     */
    @GetMapping("/service/customer/contact/getCustomerContactById")
    CustomerContactDto getCustomerContactById(@RequestParam("contactId") Integer contactId);

    /**
     * DONE(account structure): 内部已判断 company / business 维度鉴权
     */
    @GetMapping("/service/customer/contact/getCustomerOwnerPhone")
    CustomerContactDto getCustomerOwnerPhone(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 内部已判断 company / business 维度鉴权
     */
    @GetMapping("/service/customer/contact/listCustomerOwnerPhone")
    List<CustomerContactDto> listCustomerOwnerPhone(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerIds") List<Integer> customerIds);
}
