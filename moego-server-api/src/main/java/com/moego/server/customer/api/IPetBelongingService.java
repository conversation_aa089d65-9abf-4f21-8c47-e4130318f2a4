package com.moego.server.customer.api;

import com.moego.server.customer.dto.PetBelongingDTO;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Validated
public interface IPetBelongingService {

    /**
     * Insert a record, null properties will be ignored.
     *
     * @param dto dto
     * @return inserted id
     */
    @PostMapping("/service/customer/petBelonging")
    long insert(@RequestBody @NotNull PetBelongingDTO dto);

    /**
     * Get a record by id, not include deleted record.
     *
     * @param id id
     * @return existing record, null if not existed
     */
    @GetMapping("/service/customer/petBelonging/{id}")
    PetBelongingDTO get(@PathVariable("id") long id);

    @GetMapping("/service/customer/petBelonging/list")
    List<PetBelongingDTO> getPetBelongs(@RequestParam("appointmentIds") List<Integer> appointmentIds);

    /**
     * Update a record by id, null properties will be ignored.
     *
     * @param dto dto
     * @return affected rows
     */
    @PutMapping("/service/customer/petBelonging")
    int update(@RequestBody @NotNull PetBelongingDTO dto);

    /**
     * Delete a record by id.
     *
     * @param id id
     * @return deleted rows
     */
    @DeleteMapping("/service/customer/petBelonging/{id}")
    int delete(@PathVariable("id") long id);
}
