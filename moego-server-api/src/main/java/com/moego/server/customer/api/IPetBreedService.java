package com.moego.server.customer.api;

import com.moego.server.customer.dto.MoePetBreedDTO;
import java.util.Map;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface IPetBreedService {
    /**
     * DONE(account structure): 已兼容 company 维度
     *
     * get moego pet breed
     *
     * @param businessId
     * @return
     */
    @GetMapping("/service/customer/pet/breed")
    Map<Integer, MoePetBreedDTO> getPetBreed(@RequestParam("businessId") Integer businessId);
}
