package com.moego.server.customer.api;

import com.moego.server.customer.dto.ProfileRequestAddressDTO;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
public interface IProfileRequestAddressService {
    /**
     * Insert a request profile address.
     *
     * <p> 如果 {@link ProfileRequestAddressDTO#getCustomerAddressId()} 不为空，则更新对应的地址。
     *
     * @param requestProfileAddress {@link ProfileRequestAddressDTO}
     * @return inserted id，如果 {@link ProfileRequestAddressDTO#getCustomerAddressId()} 不为空，则返回已存在记录的 id。
     */
    @PostMapping("/service/customer/profile-request-addresses")
    int insert(@RequestBody ProfileRequestAddressDTO requestProfileAddress);

    /**
     * Update a profile request address by id, ignore null fields.
     *
     * @param profileRequestAddress {@link ProfileRequestAddressDTO}
     * @return updated count
     */
    @PutMapping("/service/customer/profile-request-addresses/update")
    int update(@RequestBody ProfileRequestAddressDTO profileRequestAddress);

    /**
     * Get a profile request address by id, return null if not found or deleted.
     *
     * @param id id
     * @return {@link ProfileRequestAddressDTO}
     */
    @GetMapping("/service/customer/profile-request-addresses/{id}")
    ProfileRequestAddressDTO get(@PathVariable("id") Integer id);

    /**
     * Delete profile request address by ids.
     *
     * @param ids ids
     * @return deleted count
     */
    @DeleteMapping("/service/customer/profile-request-addresses")
    int delete(@RequestBody Collection<Integer> ids);

    /**
     * 删除指定 customer id 的所有 profile request address。
     *
     * @param customerIds customer ids
     * @return deleted count
     */
    @DeleteMapping("/service/customer/profile-request-addresses/deleteByCustomerIds")
    int deleteByCustomerIds(@RequestBody Collection<Integer> customerIds);

    /**
     * List profile request address by customer id.
     *
     * @param customerIds customer ids
     * @return customer id -> profile request address list
     */
    @PostMapping("/service/customer/profile-request-addresses/listByCustomerIds")
    Map<Integer, List<ProfileRequestAddressDTO>> listByCustomerIds(@RequestBody Collection<Integer> customerIds);
}
