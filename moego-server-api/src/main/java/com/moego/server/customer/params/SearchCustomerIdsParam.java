package com.moego.server.customer.params;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SearchCustomerIdsParam {

    @NotNull
    private Integer businessId;

    @Nullable
    private String keyword;

    private List<String> clientTypes;
}
