package com.moego.server.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PetIncidentReportDto {

    @Schema(description = "incident report id")
    private Long id;

    @Schema(description = "pet id list")
    private List<Long> petIds;

    @Schema(description = "incident time")
    private LocalDateTime incidentTime;

    @Schema(description = "incident type id")
    private Long incidentTypeId;

    @Schema(description = "incident description")
    private String description;

    @Schema(description = "attachment file list")
    private List<AttachmentFiles> attachmentFiles;

    @Schema(description = "report business id")
    private Long businessId;

    @Schema(description = "is member injured")
    private Boolean isStaffInjured;

    @Schema(description = "is pet injured")
    private Boolean isPetInjured;

    @Schema(description = "is vet visit")
    private Boolean isVetVisited;

    @Data
    public static class AttachmentFiles {
        @Schema(description = "attachment file url")
        private String url;

        @Schema(description = "attachment file name")
        private String name;
    }
}
