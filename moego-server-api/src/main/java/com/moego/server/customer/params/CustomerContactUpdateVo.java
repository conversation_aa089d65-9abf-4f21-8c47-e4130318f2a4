package com.moego.server.customer.params;

import com.moego.common.enums.CustomerContactEnum;
import java.util.Objects;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 */
@Data
public class CustomerContactUpdateVo {

    /**
     * 更新 customer 时会用到该实体，导致该实体虽然为 update 实体，但是也会被用于新增
     */
    private Integer customerContactId;

    @Length(max = 100)
    private String firstName;

    @Length(max = 100)
    private String lastName;

    @Length(max = 30)
    private String phoneNumber;

    @Length(max = 50)
    private String email;

    @Length(max = 100)
    private String title;

    @Range(min = 0, max = 1)
    private Byte isPrimary;

    private Byte type;

    public boolean check() {
        // 不支持修改 OWNER
        return !Objects.equals(type, CustomerContactEnum.TYPE_OWNER);
    }
}
