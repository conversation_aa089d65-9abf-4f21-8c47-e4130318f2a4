package com.moego.server.business.api;

import com.moego.server.business.dto.ChangeAccountStatusDTO;
import com.moego.server.business.dto.ResetAccountTokenDTO;
import com.moego.server.business.dto.StaffAccountBusinessDto;
import com.moego.server.business.params.AccountIdParams;
import com.moego.server.business.params.ChangeAccountStatusParams;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IBusinessAccountService {
    /**
     * 根据accountId返回account和business所有的数据
     *
     * @param accountIdParams
     * @return
     */
    @PostMapping("/service/business/account/getAccountInfo")
    StaffAccountBusinessDto getAccountInfo(@RequestBody AccountIdParams accountIdParams);

    /**
     * 清空指定账号的 account token 和 staff token
     * 当旧 app 完全下线后，该接口不应当再被调用
     * @param accountIdParams
     * @return
     */
    @Deprecated
    @PostMapping("/service/business/account/resetAccountTokenAndStaffToken")
    ResetAccountTokenDTO resetAccountTokenAndStaffToken(@RequestBody AccountIdParams accountIdParams);

    /**
     * 修改指定账号在旧的 account 表中的 status，并重置 account token 和 staff token
     * 当旧 app 完全下线后，该接口不应当再被调用
     *
     * @param params
     * @return
     */
    @Deprecated
    @PostMapping("/service/business/account/changeAccountStatusAndResetToken")
    ChangeAccountStatusDTO changeAccountStatusAndResetToken(@RequestBody ChangeAccountStatusParams params);

    @PostMapping("/service/business/account/getRelevantAccountIds")
    List<Integer> getRelevantAccountIds(@RequestParam("accountId") @NotNull Integer accountId);
}
