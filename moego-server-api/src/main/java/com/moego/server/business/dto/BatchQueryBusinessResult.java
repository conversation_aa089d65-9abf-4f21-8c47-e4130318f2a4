package com.moego.server.business.dto;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchQueryBusinessResult {
    private Map<Integer, TaskBusinessInfoDto> businessMap;
    private Integer pageNum;
    private Boolean hasNext;
    private Long total;
    private Integer pageSize;
}
