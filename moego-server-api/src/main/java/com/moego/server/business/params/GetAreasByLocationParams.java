package com.moego.server.business.params;

import lombok.Data;
import org.springframework.util.StringUtils;

@Data
public class GetAreasByLocationParams {
    private Long Id;
    private Double lat;
    private Double lng;
    private String zipcode;

    public GetAreasByLocationParams(Long Id, String lat, String lng, String zipcode) {
        this.Id = Id;
        if (StringUtils.hasText(lat)) {
            this.lat = Double.parseDouble(lat);
        }
        if (StringUtils.hasText(lng)) {
            this.lng = Double.parseDouble(lng);
        }
        this.zipcode = zipcode;
    }
}
