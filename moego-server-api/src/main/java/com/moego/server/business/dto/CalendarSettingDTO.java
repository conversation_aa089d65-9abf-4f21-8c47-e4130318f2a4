package com.moego.server.business.dto;

import com.moego.common.enums.BusinessCalendarEnum;
import com.moego.server.business.enums.CalendarCardAreaShowType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalendarSettingDTO {

    // 0 for false, 1 for true
    private Integer showPopupView;
    private Integer showClientName;
    private Integer showPetName;
    private Integer showServiceName;
    private Integer showAddress;

    // 1 - appointment color, 2 - service color 3 - staff color
    private Integer showColorCodeWith;
    // 1 Stripe color 2 Background color
    private Byte showColorCodeDisplay;

    private Integer showComment;

    // number of minutes, eg. 11:00AM -> 660
    private Integer calendarViewStartAt;

    // number of minutes, eg. 11:00AM -> 660
    private Integer calendarViewEndAt;

    // 1 - standard, 2 - tight
    private Integer weeklyViewZoomLevel;

    // 1 - standard, 2 - large
    private Integer calendarZoomLevel;

    // which account made the change, null if never set / default value
    private Integer updateBy;

    private Integer businessId;
    private Long updateTime;

    @Schema(description = "0 - not show, 1 - show address, 2 - show area")
    private CalendarCardAreaShowType showAddressType;

    private Boolean showPetCode;
    private Boolean showPrice;
    private Boolean showVaccinationAlert;
    private Boolean showAgreementNotSignedAlert;
    private Boolean showCurrentTimeIndicator;
    private Boolean showStaffServiceArea;
    private Boolean showStaffEstimatedRevenue;
    private Boolean showSlotLocation;
    private Boolean showSlotTime;

    public static CalendarSettingDTOBuilder defaultValueBuilder() {
        return CalendarSettingDTO.builder()
                .showColorCodeWith(1)
                .showClientName(1)
                .showPetName(1)
                .showPetCode(true)
                .showPopupView(1)
                .showServiceName(1)
                .showAddress(1)
                .showColorCodeWith(1)
                .showColorCodeDisplay(BusinessCalendarEnum.SHOW_COLOR_CODE_DISPLAY_STRIPE_COLOR)
                .showComment(1)
                .calendarViewStartAt(BusinessCalendarEnum.DEFAULT_CALENDAR_VIEW_START_AT)
                .calendarViewEndAt(BusinessCalendarEnum.DEFAULT_CALENDAR_VIEW_END_AT)
                .calendarZoomLevel(1)
                .weeklyViewZoomLevel(1)
                .showAddressType(CalendarCardAreaShowType.NOT_SHOW)
                .showPetCode(false)
                .showPrice(false)
                .showVaccinationAlert(false)
                .showAgreementNotSignedAlert(false)
                .showCurrentTimeIndicator(true)
                .showStaffServiceArea(false)
                .showSlotLocation(false)
                .showSlotTime(false);
    }
}
