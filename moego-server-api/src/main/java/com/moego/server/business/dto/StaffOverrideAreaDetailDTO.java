package com.moego.server.business.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class StaffOverrideAreaDetailDTO {

    private Integer id;

    private Integer businessId;

    private Integer staffId;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate overrideDate;

    private List<WorkingAreaDto> workingArea;
}
