/*
 * @since 2023-12-04 10:07:03
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.business.api;

import com.moego.server.business.dto.DescribeRolesDTO;
import com.moego.server.business.params.DescribeRolesParams;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface IBusinessRoleService {
    @PostMapping("/service/business/role/describeRoles")
    DescribeRolesDTO describeRoles(@RequestBody @Valid @NotNull DescribeRolesParams params);
}
