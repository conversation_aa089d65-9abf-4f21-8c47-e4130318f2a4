package com.moego.server.business.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class StaffWorkingHourDetailDTO {

    private Integer businessId;

    private Integer staffId;

    private Byte scheduleType;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    private StaffWorkingHourDayDetailDTO firstWeek;

    private StaffWorkingHourDayDetailDTO secondWeek;

    private StaffWorkingHourDayDetailDTO thirdWeek;

    private StaffWorkingHourDayDetailDTO forthWeek;
}
