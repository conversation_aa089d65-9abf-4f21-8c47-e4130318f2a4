package com.moego.server.business.client;

import com.moego.server.business.api.IBusinessPaymentMethodService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-business-server",
        url = "${moego.server.url.business}",
        contextId = "IBusinessPaymentMethodClient")
public interface IBusinessPaymentMethodClient extends IBusinessPaymentMethodService {}
