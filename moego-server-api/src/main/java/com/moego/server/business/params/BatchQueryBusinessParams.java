package com.moego.server.business.params;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class BatchQueryBusinessParams {
    // start 1
    private Integer pageNum;
    // default 3000
    private Integer pageSize;
    // 是否付费中
    private Boolean isPremium;
    // 是否有 staff push calendar 打开
    private Boolean isPushCalendar;
}
