package com.moego.server.business.api;

import com.moego.server.business.dto.InitSessionDTO;
import com.moego.server.business.params.InitSessionParams;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface IBusinessSessionService {
    @PostMapping("/service/business/session/init")
    @Deprecated
    InitSessionDTO initSession(@RequestBody InitSessionParams params);

    @PostMapping("/service/business/session/init/v2")
    InitSessionDTO initSessionV2(@RequestBody InitSessionParams params);
}
