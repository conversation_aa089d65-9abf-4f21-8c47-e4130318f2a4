package com.moego.server.business.dto;

import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * Geo area DTO.
 *
 * <AUTHOR>
 */
@Data
public class GeoAreaDTO {
    private Integer id;
    private Long companyId;
    private Byte areaType;
    private List<List<Double>> areaPolygon;
    private List<String> zipCodes;
    private Integer businessId;
    private String areaName;
    private String colorCode;
    private Date createTime;
    private Date updateTime;

    public interface AreaType {
        Byte POLYGON = 1;
        Byte ZIPCODE = 2;
    }
}
