package com.moego.server.business.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class PayrollExceptionParams {

    private Integer id;

    @JsonIgnore
    private Integer businessId;

    @Schema(description = "关联 serviceId")
    @NotNull
    private Integer serviceId;

    @Schema(description = "service 计算的 rate, 2位小数")
    @DecimalMax(value = "100")
    @DecimalMin(value = "0")
    private BigDecimal rate;

    @Schema(description = "是否应用到全部 staff")
    private Boolean isAllStaff;

    @Schema(description = "应用到的 staff id 列表")
    private List<Integer> staffIdList;

    private Boolean isDelete;
}
