package com.moego.server.business.api;

import com.moego.server.business.dto.MoeBusinessTaxDto;
import com.moego.server.business.params.InfoIdParams;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IBusinessTaxService {
    @PostMapping("/service/business/tax/getTaxById")
    MoeBusinessTaxDto getTaxById(@RequestBody InfoIdParams taxIdParams);

    /**
     * 根据id集合获取tax
     *
     * @param ids
     * @return
     */
    @PostMapping("/service/business/tax/getTaxListByIds")
    List<MoeBusinessTaxDto> getTaxListByIds(@RequestBody List<Integer> ids);

    @GetMapping("/service/business/tax/queryTaxById")
    MoeBusinessTaxDto queryTaxById(
            @RequestParam("businessId") Integer businessId, @RequestParam("taxId") Integer taxId);
}
