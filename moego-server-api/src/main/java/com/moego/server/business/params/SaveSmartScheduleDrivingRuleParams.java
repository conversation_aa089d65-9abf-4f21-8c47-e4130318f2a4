package com.moego.server.business.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import java.util.List;
import lombok.Data;

@Data
public class SaveSmartScheduleDrivingRuleParams {

    @JsonIgnore
    private Integer businessId; // 参数传递，当前请求的 businessId

    @JsonIgnore
    private Integer operatorId; // 参数传递，当前请求的 staffId

    @Schema(description = "record id: 有传是修改，不传是新增")
    @PositiveOrZero
    private Integer id;

    @Positive
    private Integer maxDist;

    @Positive
    private Integer maxTime;

    @Schema(description = "关联的 van id 列表：不传不更新，传空数组会解绑之前的 van")
    private List<Integer> assignedVanIdList;
}
