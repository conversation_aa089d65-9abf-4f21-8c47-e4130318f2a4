package com.moego.server.business.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Schema(description = "calendar card area show type", type = "integer")
public enum CalendarCardAreaShowType {
    NOT_SHOW(0),
    ADDRESS(1),
    AREA(2);

    private final int value;

    @JsonValue
    public int getValue() {
        return value;
    }

    public static CalendarCardAreaShowType fromValue(int value) {
        for (CalendarCardAreaShowType status : CalendarCardAreaShowType.values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        return NOT_SHOW;
    }
}
