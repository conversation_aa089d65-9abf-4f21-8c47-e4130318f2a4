package com.moego.server.business.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import lombok.Data;

@Data
public class StaffWorkingAreaDetailDTO {

    private Integer businessId;

    private Integer staffId;

    private Byte scheduleType;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    private StaffWorkingAreaDayDetailDTO firstWeek;

    private StaffWorkingAreaDayDetailDTO secondWeek;

    private StaffWorkingAreaDayDetailDTO thirdWeek;

    private StaffWorkingAreaDayDetailDTO forthWeek;
}
