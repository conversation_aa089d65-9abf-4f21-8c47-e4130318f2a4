package com.moego.server.business.dto;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceAreaInsideBatchResult {

    private Integer staffId;
    private Double lat;
    private Double lng;

    // date -> isInside
    private Map<String, Boolean> isInsideMap;
}
