package com.moego.server.business.api;

import com.moego.common.response.ResponseResult;
import com.moego.server.business.dto.PaymentMethodDto;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface IBusinessPaymentMethodService {
    /**
     * 获取支持的payment method列表
     *
     * @param tokenBusinessId
     * @return
     */
    @GetMapping("/service/business/paymentMethod/getActivePayment")
    ResponseResult<List<PaymentMethodDto>> getActivePayment(@RequestParam("tokenBusinessId") Integer tokenBusinessId);

    @GetMapping("/service/business/paymentMethod/checkCreditCardIsActive")
    Boolean checkCreditCardIsActive(@RequestParam("businessId") Integer businessId);
}
