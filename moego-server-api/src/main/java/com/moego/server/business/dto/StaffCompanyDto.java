package com.moego.server.business.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/3/12 10:47 AM
 */
@Data
@Schema(description = "all companies under this account")
public class StaffCompanyDto {

    @Schema(description = "staff 权限类型 0-company staff 1-company owner 2-enterprise owner 3-enterprise staff")
    Byte employeeCategory;

    @Deprecated
    @Schema(description = "deprecated: app 类型： 0-Mobile Grooming   1-Grooming Salon  2 Hybrid")
    Byte appType;

    @Schema(description = "business_mode 类型： 0-Mobile Grooming   1-Grooming Salon ")
    Byte businessMode;

    String businessName;
    Integer businessId;
    Integer companyId;
    String avatarPath;
    String companyName;
    Integer ownerAccountId;
    Integer usedStaffCount;
    Integer usedVanCount;

    // account dashboard address
    String address1;
    String address2;
    String addressCity;
    String addressState;
    String addressZipcode;
    String addressCountry;
    String country;
}
