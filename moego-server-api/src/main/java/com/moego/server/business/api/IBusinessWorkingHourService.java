package com.moego.server.business.api;

import com.moego.server.business.dto.BusinessWorkingHourDetailDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IBusinessWorkingHourService {
    @GetMapping("/service/business/working/hour")
    BusinessWorkingHourDetailDTO getBusinessWorkingHour(@RequestParam("businessId") Integer businessId);

    @PostMapping("/service/business/working/hour")
    Boolean updateBusinessWorkingHour(@RequestBody BusinessWorkingHourDetailDTO workingHourDetailDTO);
}
