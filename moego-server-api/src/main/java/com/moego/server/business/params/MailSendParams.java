package com.moego.server.business.params;

import com.microtripit.mandrillapp.lutung.view.MandrillMessage;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-06-16 21:54
 */
@Data
public class MailSendParams {

    private String subject;
    private String html;
    private String text;
    private String from_email;
    private String from_name;
    private List<MandrillMessage.Recipient> to;
    private Map<String, String> headers;
    private Boolean important;
    private Boolean track_opens;
    private Boolean track_clicks;
    private Boolean auto_text;
    private Boolean auto_html;
    private Boolean inline_css;
    private Boolean url_strip_qs;
    private Boolean preserve_recipients;
    private Boolean view_content_link;
    private String bcc_address;
    private String tracking_domain;
    private String signing_domain;
    private String return_path_domain;
    private Boolean merge;
    private String merge_language;
    private List<MandrillMessage.MergeVar> global_merge_vars;
    private List<MandrillMessage.MergeVarBucket> merge_vars;
    private List<String> tags;
    private String subaccount;
    private List<String> google_analytics_domains;
    private String google_analytics_campaign;
    private Map<String, String> metadata;
    private List<MandrillMessage.RecipientMetadata> recipient_metadata;
    private List<MandrillMessage.MessageContent> attachments;
    private List<MandrillMessage.MessageContent> images;
    private Boolean async;
    private Date sendAt;
    private String templateName;
    private Map<String, String> templateContent;
}
