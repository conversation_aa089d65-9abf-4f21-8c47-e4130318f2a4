package com.moego.server.business.params;

import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StaffCACDBatchRequest {
    private Integer businessId;
    private List<Integer> staffIdList;
    private String startDate; // include
    private String endDate; // include
    private Map<Long, LocationParams> locations; // id -> location，其中 id 作为接口中 location 的唯一标识
}
