package com.moego.server.business.api;

import com.moego.server.business.dto.BusinessEmailDTO;
import com.moego.server.business.dto.BusinessEmailListDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface IBusinessEmailService {

    // TODO 该接口已经废弃，使用 getBusinessNotificationEmailList 接口，后续删除
    @GetMapping("/service/business/email")
    @Deprecated
    BusinessEmailDTO getBusinessEmail(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/business/email/list")
    BusinessEmailListDTO getBusinessNotificationEmailList(@RequestParam("businessId") Integer businessId);
}
