package com.moego.server.business.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class StaffPayrollSettingDTO {

    private Integer id;
    private Integer businessId;
    private Integer staffId;

    @Schema(description = "service commission 开关")
    private Boolean serviceCommissionEnable;

    @Schema(description = "service commission 类型: 1-fixed rate, 2-tier rate")
    private Byte serviceCommissionType;

    @Schema(description = "tier 计算类型: 1-sliding scale, 2-progressive")
    private Byte tierType;

    @Schema(description = "service rate, 百分比, 2位小数")
    private BigDecimal servicePayRate;

    @Schema(description = "addon rate, 百分比, 2位小数")
    private BigDecimal addonPayRate;

    @Schema(description = "tier rate 配置列表")
    private List<PayrollTierConfig> tierConfig;

    @Schema(description = "hourly commission 开关")
    private Boolean hourlyCommissionEnable;

    @Schema(description = "hourly pay 时薪，2位小数")
    private BigDecimal hourlyPay;

    @Schema(description = "tips commission 开关")
    private Boolean tipsCommissionEnable;

    @Schema(description = "tips rate, 百分比, 2位小数")
    private BigDecimal tipsPayRate;

    private Integer createTime;
    private Integer updateTime;
}
