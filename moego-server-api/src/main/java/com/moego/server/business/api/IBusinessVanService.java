package com.moego.server.business.api;

import com.moego.server.business.dto.DescribeVansDTO;
import com.moego.server.business.params.DescribeVansParams;
import com.moego.server.business.params.GetVanStaffBindingsParams;
import jakarta.validation.Valid;
import java.util.Map;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface IBusinessVanService {
    @PostMapping("/service/business/van/getVanIdsByBusinessAndStaffIds")
    Map<Integer, Integer> getVanStaffBindingsByIds(@RequestBody @Valid GetVanStaffBindingsParams params);

    @PostMapping("/service/business/van/describeVans")
    DescribeVansDTO describeVans(@RequestBody @Valid DescribeVansParams params);
}
