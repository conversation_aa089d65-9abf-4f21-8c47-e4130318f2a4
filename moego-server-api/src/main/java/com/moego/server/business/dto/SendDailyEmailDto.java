package com.moego.server.business.dto;

import lombok.Data;

@Data
public class SendDailyEmailDto {

    private Integer businessId;
    private String timezoneName;
    private Integer timezoneSeconds;
    private String currencySymbol;
    private String ownerEmail;
    private String businessName;
    private Byte sendDaily;
    private Byte timeFormatType;

    public SendDailyEmailDto() {}

    public SendDailyEmailDto(
            Integer businessId,
            String timezoneName,
            Integer timezoneSeconds,
            String currencySymbol,
            String ownerEmail,
            String businessName,
            Byte sendDaily,
            Byte timeFormatType) {
        this.businessId = businessId;
        this.timezoneName = timezoneName;
        this.timezoneSeconds = timezoneSeconds;
        this.currencySymbol = currencySymbol;
        this.ownerEmail = ownerEmail;
        this.businessName = businessName;
        this.sendDaily = sendDaily;
        this.timeFormatType = timeFormatType;
    }
}
