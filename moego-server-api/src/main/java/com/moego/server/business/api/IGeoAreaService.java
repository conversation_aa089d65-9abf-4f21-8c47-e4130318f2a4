package com.moego.server.business.api;

import com.moego.server.business.dto.GeoAreaDTO;
import java.util.Collection;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
public interface IGeoAreaService {

    /**
     * List non-deleted geo area by area ids.
     *
     * @param areaIds area ids
     * @return geo area list
     */
    @PostMapping("/service/business/listGeoArea")
    List<GeoAreaDTO> listGeoArea(@RequestBody Collection<Integer> areaIds);
}
