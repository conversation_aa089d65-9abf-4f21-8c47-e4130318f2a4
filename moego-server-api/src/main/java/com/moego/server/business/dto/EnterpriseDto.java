package com.moego.server.business.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class EnterpriseDto {

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(type = "string", description = "enterprise id")
    private Long id;

    private String name;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(type = "string", description = "owner account id")
    private Long accountId;
}
