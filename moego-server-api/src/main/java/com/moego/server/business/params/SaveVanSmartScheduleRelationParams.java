package com.moego.server.business.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SaveVanSmartScheduleRelationParams {

    @JsonIgnore
    private Integer businessId;

    @JsonIgnore
    private Integer operatorId;

    @NotNull
    private Integer vanId;

    @PositiveOrZero
    private Integer drivingRuleId;

    @PositiveOrZero
    private Integer locationId;
}
