package com.moego.server.business.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class PayrollTierConfig {

    @Schema(description = "tier start 金额，2位小数，第一个必须为0")
    private BigDecimal start;

    @Schema(description = "tier end 金额，2位小数")
    private BigDecimal end;

    @Schema(description = "tier rate，2位小数")
    private BigDecimal rate;
}
