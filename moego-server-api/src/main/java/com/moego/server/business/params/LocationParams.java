package com.moego.server.business.params;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

@Data
@NoArgsConstructor
public class LocationParams {
    private Double lat;
    private Double lng;
    private String zipcode;

    public LocationParams(Double lat, Double lng, String zipcode) {
        this.lat = lat;
        this.lng = lng;
        this.zipcode = zipcode;
    }

    public LocationParams(String lat, String lng, String zipcode) {
        if (StringUtils.hasText(lat)) {
            this.lat = Double.parseDouble(lat);
        }
        if (StringUtils.hasText(lng)) {
            this.lng = Double.parseDouble(lng);
        }
        this.zipcode = zipcode;
    }
}
