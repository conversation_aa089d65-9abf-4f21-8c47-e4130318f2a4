package com.moego.lib.utils;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.utils.model.Pair;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.BiFunction;
import org.junit.jupiter.api.Test;

class PageUtilTest {

    @Test
    void fetchAll_WithEmptyResult() {
        // Arrange
        BiFunction<Integer, Integer, Pair<List<String>, Integer>> loader =
                (page, size) -> Pair.of(Collections.emptyList(), 0);

        // Act
        List<String> result = PageUtil.fetchAll(10, loader);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void fetchAll_WithSinglePage() {
        // Arrange
        List<String> data = Arrays.asList("a", "b", "c");
        BiFunction<Integer, Integer, Pair<List<String>, Integer>> loader = (page, size) -> Pair.of(data, data.size());

        // Act
        List<String> result = PageUtil.fetchAll(10, loader);

        // Assert
        assertThat(result).hasSize(3).containsExactly("a", "b", "c");
    }

    @Test
    void fetchAll_WithMultiplePages() {
        // Arrange
        List<String> page1 = Arrays.asList("a", "b");
        List<String> page2 = Arrays.asList("c", "d");
        List<String> page3 = Collections.singletonList("e");

        BiFunction<Integer, Integer, Pair<List<String>, Integer>> loader = (page, size) -> {
            List<String> data;
            switch (page) {
                case 1:
                    data = page1;
                    break;
                case 2:
                    data = page2;
                    break;
                case 3:
                    data = page3;
                    break;
                default:
                    data = Collections.emptyList();
            }
            return Pair.of(data, 5);
        };

        // Act
        List<String> result = PageUtil.fetchAll(2, loader);

        // Assert
        assertThat(result).hasSize(5).containsExactly("a", "b", "c", "d", "e");
    }

    @Test
    void fetchAll_WithDefaultPageSize() {
        // Arrange
        List<String> data = Arrays.asList("a", "b", "c");
        BiFunction<Integer, Integer, Pair<List<String>, Integer>> loader = (page, size) -> Pair.of(data, data.size());

        // Act
        List<String> result = PageUtil.fetchAll(loader);

        // Assert
        assertThat(result).hasSize(3).containsExactly("a", "b", "c");
    }

    @Test
    void fetchAllGrpc_WithEmptyResult() {
        // Arrange
        BiFunction<Integer, Integer, Pair<List<String>, PaginationResponse>> loader = (page, size) -> Pair.of(
                Collections.emptyList(),
                PaginationResponse.newBuilder().setTotal(0).build());

        // Act
        List<String> result = PageUtil.fetchAllGrpc(10, loader);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void fetchAllGrpc_WithSinglePage() {
        // Arrange
        List<String> data = Arrays.asList("a", "b", "c");
        BiFunction<Integer, Integer, Pair<List<String>, PaginationResponse>> loader = (page, size) -> Pair.of(
                data, PaginationResponse.newBuilder().setTotal(data.size()).build());

        // Act
        List<String> result = PageUtil.fetchAllGrpc(10, loader);

        // Assert
        assertThat(result).hasSize(3).containsExactly("a", "b", "c");
    }

    @Test
    void fetchAllGrpc_WithMultiplePages() {
        // Arrange
        List<String> page1 = Arrays.asList("a", "b");
        List<String> page2 = Arrays.asList("c", "d");
        List<String> page3 = Collections.singletonList("e");

        BiFunction<Integer, Integer, Pair<List<String>, PaginationResponse>> loader = (page, size) -> {
            List<String> data;
            switch (page) {
                case 1:
                    data = page1;
                    break;
                case 2:
                    data = page2;
                    break;
                case 3:
                    data = page3;
                    break;
                default:
                    data = Collections.emptyList();
            }
            return Pair.of(data, PaginationResponse.newBuilder().setTotal(5).build());
        };

        // Act
        List<String> result = PageUtil.fetchAllGrpc(2, loader);

        // Assert
        assertThat(result).hasSize(5).containsExactly("a", "b", "c", "d", "e");
    }

    @Test
    void fetchAllGrpc_WithDefaultPageSize() {
        // Arrange
        List<String> data = Arrays.asList("a", "b", "c");
        BiFunction<Integer, Integer, Pair<List<String>, PaginationResponse>> loader = (page, size) -> Pair.of(
                data, PaginationResponse.newBuilder().setTotal(data.size()).build());

        // Act
        List<String> result = PageUtil.fetchAllGrpc(loader);

        // Assert
        assertThat(result).hasSize(3).containsExactly("a", "b", "c");
    }
}
