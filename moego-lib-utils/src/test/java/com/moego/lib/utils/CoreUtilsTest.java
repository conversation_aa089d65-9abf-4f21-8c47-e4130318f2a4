package com.moego.lib.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import org.junit.jupiter.api.Test;

public class CoreUtilsTest {

    @Test
    public void testRandom() {
        for (long i = 0, last = CoreUtils.getId(); i < 10000; ++i) {
            var next = CoreUtils.getId();
            assertTrue(last < next);
            last = next;
        }

        for (long i = 0, last = CoreUtils.getId(10000, 20000); i < 10000; ++i) {
            var next = CoreUtils.getId(10000, 20000);
            assertTrue(10000 <= next && next < 20000);
            assertTrue(last != next);
            last = next;
        }

        for (int n = 0; n < 1000; ++n) {
            assertEquals(32, CoreUtils.randomString(32).length());
            var s0 = CoreUtils.randomDigest(32);
            assertTrue(s0.matches("[0-9a-fA-F]{32}"));
            var i = CoreUtils.random(1000, 2000);
            assertTrue(1000 <= i && i < 2000);
            var l = CoreUtils.random(1000L, 2000L);
            assertTrue(1000L <= l && l < 2000L);
            var d = CoreUtils.randomDouble();
            assertTrue(0 <= d && d < 1);
            var s1 = CoreUtils.uuid();
            assertTrue(s1.matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"));
            var s2 = CoreUtils.uuidShort();
            assertTrue(s2.matches("[0-9a-f]{32}"));
        }

        System.out.println("--> test random OK !");
    }

    @Test
    public void testDigitToString() {
        assertEquals("00", CoreUtils.byteToHexString((byte) 0));
        assertEquals("64", CoreUtils.byteToHexString((byte) 100));
        assertEquals("9c", CoreUtils.byteToHexString((byte) -100));
        assertEquals("80", CoreUtils.byteToHexString(Byte.MIN_VALUE));
        assertEquals("7f", CoreUtils.byteToHexString(Byte.MAX_VALUE));

        assertEquals("0000", CoreUtils.shortToHexString((short) 0));
        assertEquals("2710", CoreUtils.shortToHexString((short) 10000));
        assertEquals("d8f0", CoreUtils.shortToHexString((short) -10000));
        assertEquals("8000", CoreUtils.shortToHexString(Short.MIN_VALUE));
        assertEquals("7fff", CoreUtils.shortToHexString(Short.MAX_VALUE));

        assertEquals("00000000", CoreUtils.intToHexString(0));
        assertEquals("00000000", CoreUtils.intToHexString(-0));
        assertEquals("000003e8", CoreUtils.intToHexString(1000));
        assertEquals("fffffc18", CoreUtils.intToHexString(-1000));
        assertEquals("00002710", CoreUtils.intToHexString(10000));
        assertEquals("000186a0", CoreUtils.intToHexString(100000));
        assertEquals("00989680", CoreUtils.intToHexString(10000000));
        assertEquals("05f5e100", CoreUtils.intToHexString(100000000));
        assertEquals("80000000", CoreUtils.intToHexString(Integer.MIN_VALUE));
        assertEquals("7fffffff", CoreUtils.intToHexString(Integer.MAX_VALUE));
        assertEquals(Integer.toHexString(Integer.MIN_VALUE), CoreUtils.intToHexString(Integer.MIN_VALUE));
        assertEquals(Integer.toHexString(Integer.MAX_VALUE), CoreUtils.intToHexString(Integer.MAX_VALUE));

        assertEquals("0000000000000000", CoreUtils.longToHexString(0));
        assertEquals("0000000005f5e100", CoreUtils.longToHexString(100000000L));
        assertEquals("fffffffffa0a1f00", CoreUtils.longToHexString(-100000000L));
        assertEquals("0000000211d1ae38", CoreUtils.longToHexString(8888888888L));
        assertEquals("000000174876e7ff", CoreUtils.longToHexString(99999999999L));
        assertEquals("000000174876e7ff", CoreUtils.longToHexString(99999999999L));
        assertEquals("ffffffff80000000", CoreUtils.longToHexString(Integer.MIN_VALUE));
        assertEquals("000000007fffffff", CoreUtils.longToHexString(Integer.MAX_VALUE));
        assertEquals("8000000000000000", CoreUtils.longToHexString(Long.MIN_VALUE));
        assertEquals("7fffffffffffffff", CoreUtils.longToHexString(Long.MAX_VALUE));
        assertEquals(Long.toHexString(Long.MIN_VALUE), CoreUtils.longToHexString(Long.MIN_VALUE));
        assertEquals(Long.toHexString(Long.MAX_VALUE), CoreUtils.longToHexString(Long.MAX_VALUE));

        System.out.println("--> test digit to string OK !");
    }

    @Test
    public void testHex() {
        for (int i = 0; i < 1000; ++i) {
            var s1 = CoreUtils.randomDigest(32);
            assertTrue(s1.matches("[0-9a-f]{32}"));
            assertTrue(CoreUtils.isDigestString(s1));

            var bytes = CoreUtils.hexStringToBytes(s1);
            assertEquals(16, bytes.length);

            var s2 = CoreUtils.bytesToHexString(bytes);
            assertTrue(CoreUtils.isDigestString(s2));
            assertEquals(s1, s2);
        }

        System.out.println("--> test HEX format OK !");
    }

    @Test
    public void testCalculateDistance() {
        double centerLat = 38.87908, centerLng = -76.98280;
        double aLat = 38.89040, aLng = -77.00934;
        double bLat = 38.87316, bLng = -77.00718;
        double cLat = 38.84602, cLng = -77.00962;
        double dLat = 38.85531, dLng = -76.91726;
        double eLat = 38.88980, eLng = -76.937589;
        double fLat = 38.90940, fLng = -76.960229;

        assertEquals(2619.43, CoreUtils.straightDistanceByAngle(centerLat, centerLng, aLat, aLng), 0.01);
        assertEquals(2210.75, CoreUtils.straightDistanceByAngle(centerLat, centerLng, bLat, bLng), 0.01);
        assertEquals(4348.11, CoreUtils.straightDistanceByAngle(centerLat, centerLng, cLat, cLng), 0.01);
        assertEquals(6259.63, CoreUtils.straightDistanceByAngle(centerLat, centerLng, dLat, dLng), 0.01);
        assertEquals(4090.79, CoreUtils.straightDistanceByAngle(centerLat, centerLng, eLat, eLng), 0.01);
        assertEquals(3896.44, CoreUtils.straightDistanceByAngle(centerLat, centerLng, fLat, fLng), 0.01);

        assertEquals(2619.43, CoreUtils.straightDistanceByAngle(aLat, aLng, centerLat, centerLng), 0.01);
        assertEquals(2210.75, CoreUtils.straightDistanceByAngle(bLat, bLng, centerLat, centerLng), 0.01);
        assertEquals(4348.11, CoreUtils.straightDistanceByAngle(cLat, cLng, centerLat, centerLng), 0.01);
        assertEquals(6259.63, CoreUtils.straightDistanceByAngle(dLat, dLng, centerLat, centerLng), 0.01);
        assertEquals(4090.79, CoreUtils.straightDistanceByAngle(eLat, eLng, centerLat, centerLng), 0.01);
        assertEquals(3896.44, CoreUtils.straightDistanceByAngle(fLat, fLng, centerLat, centerLng), 0.01);

        System.out.println("--> test calculate straight distance OK !");
    }

    @Test
    public void testObjectToType() {
        assertNull(CoreUtils.objToByte(null));
        assertNull(CoreUtils.objToShort(null));
        assertNull(CoreUtils.objToInteger(null));
        assertNull(CoreUtils.objToLong(null));
        assertNull(CoreUtils.objToFloat(null));
        assertNull(CoreUtils.objToDouble(null));
        assertNull(CoreUtils.objToBoolean(null));
        assertNull(CoreUtils.objToString(null));

        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToByte(Collections.EMPTY_LIST));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToShort(Collections.EMPTY_MAP));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToInteger(Collections.EMPTY_SET));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToLong(new int[1]));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToFloat(new Object()));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToDouble(Collections.EMPTY_LIST));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToBoolean(Collections.EMPTY_LIST));

        final byte byteValue = 0x3f;
        final short shortValue = 8255;
        final int intValue = 1048639;
        final long longValue = 1099511627839L;
        final float floatValue = 63.3456F;
        final double doubleValue = 6.33456789;
        final boolean trueValue = true;
        final boolean falseValue = false;
        final String emptyText = "";
        final String doubleText = "63.4567";
        final String integerText = "63";
        final String booleanText = "true";
        final String justText = "abc";
        final var intArray = new int[] {1, 2, 3};
        final var strList = List.of("a", "b", "c");
        final var complexArray = new Object[] {1, new int[] {5, 6}, List.of("a", "b")};
        final var complexList = List.of("2", new int[] {5, 6}, List.of("x", "y"));
        final var complexMap = new TreeMap<>(Map.of("k1", 100, "k2", strList, "k3", "v"));

        // test for object to byte
        assertEquals(byteValue, CoreUtils.objToByte(byteValue));
        assertEquals(byteValue, CoreUtils.objToByte(shortValue));
        assertEquals(byteValue, CoreUtils.objToByte(intValue));
        assertEquals(byteValue, CoreUtils.objToByte(longValue));
        assertEquals(byteValue, CoreUtils.objToByte(floatValue));
        assertEquals((byte) 6, CoreUtils.objToByte(doubleValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToByte(trueValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToByte(falseValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToByte(emptyText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToByte(doubleText));
        assertEquals((byte) 63, CoreUtils.objToByte(integerText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToByte(booleanText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToByte(justText));

        // test for object to short
        assertEquals((short) 63, CoreUtils.objToShort(byteValue));
        assertEquals(shortValue, CoreUtils.objToShort(shortValue));
        assertEquals((short) 63, CoreUtils.objToShort(intValue));
        assertEquals((short) 63, CoreUtils.objToShort(longValue));
        assertEquals((short) 63, CoreUtils.objToShort(floatValue));
        assertEquals((short) 6, CoreUtils.objToShort(doubleValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToShort(trueValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToShort(falseValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToShort(emptyText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToShort(doubleText));
        assertEquals((short) 63, CoreUtils.objToShort(integerText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToShort(booleanText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToShort(justText));

        // test for object to int
        assertEquals(byteValue, CoreUtils.objToInteger(byteValue));
        assertEquals(shortValue, CoreUtils.objToInteger(shortValue));
        assertEquals(intValue, CoreUtils.objToInteger(intValue));
        assertEquals(byteValue, CoreUtils.objToInteger(longValue));
        assertEquals(byteValue, CoreUtils.objToInteger(floatValue));
        assertEquals(6, CoreUtils.objToInteger(doubleValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToInteger(trueValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToInteger(falseValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToInteger(emptyText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToInteger(doubleText));
        assertEquals(63, CoreUtils.objToInteger(integerText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToInteger(booleanText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToInteger(justText));

        // test for object to long
        assertEquals(byteValue, CoreUtils.objToLong(byteValue));
        assertEquals(shortValue, CoreUtils.objToLong(shortValue));
        assertEquals(intValue, CoreUtils.objToLong(intValue));
        assertEquals(longValue, CoreUtils.objToLong(longValue));
        assertEquals(63, CoreUtils.objToLong(floatValue));
        assertEquals(6, CoreUtils.objToLong(doubleValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToLong(trueValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToLong(falseValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToLong(emptyText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToLong(doubleText));
        assertEquals(63, CoreUtils.objToLong(integerText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToLong(booleanText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToLong(justText));

        // test for object to float
        assertEquals(63.0F, CoreUtils.objToFloat(byteValue), 0.01F);
        assertEquals(8255.0F, CoreUtils.objToFloat(shortValue), 0.01F);
        assertEquals(1048639.0F, CoreUtils.objToFloat(intValue), 0.01F);
        assertEquals(1099511627776.0F, CoreUtils.objToFloat(longValue), 0.0001F);
        assertEquals(floatValue, CoreUtils.objToFloat(floatValue), 0.0001F);
        assertEquals(6.334567F, CoreUtils.objToFloat(doubleValue), 0.000001F);
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToFloat(trueValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToFloat(falseValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToFloat(emptyText));
        assertEquals(63.4567F, CoreUtils.objToFloat(doubleText), 0.000001F);
        assertEquals(63.0000F, CoreUtils.objToFloat(integerText), 0.000001F);
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToFloat(booleanText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToFloat(justText));

        // test for object to double
        assertTrue(CoreUtils.doubleEquals(63.0, CoreUtils.objToDouble(byteValue), 0.01));
        assertTrue(CoreUtils.doubleEquals(8255.0, CoreUtils.objToDouble(shortValue), 0.01));
        assertTrue(CoreUtils.doubleEquals(1048639.0, CoreUtils.objToDouble(intValue), 0.0001));
        assertTrue(CoreUtils.doubleEquals(1099511627839.0, CoreUtils.objToDouble(longValue), 0.0001));
        assertTrue(CoreUtils.doubleEquals(floatValue, CoreUtils.objToDouble(floatValue), 0.0001));
        assertTrue(CoreUtils.doubleEquals(6.334567, CoreUtils.objToDouble(doubleValue), 0.000001));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToDouble(trueValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToDouble(falseValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToDouble(emptyText));
        assertTrue(CoreUtils.doubleEquals(63.4567, CoreUtils.objToDouble(doubleText), 0.000001));
        assertTrue(CoreUtils.doubleEquals(63.0000, CoreUtils.objToDouble(integerText), 0.000001));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToDouble(booleanText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToDouble(justText));

        // test for object to boolean
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToBoolean(byteValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToBoolean(shortValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToBoolean(intValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToBoolean(longValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToBoolean(floatValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToBoolean(doubleValue));
        assertEquals(true, CoreUtils.objToBoolean(trueValue));
        assertEquals(false, CoreUtils.objToBoolean(falseValue));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToBoolean(emptyText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToBoolean(doubleText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToBoolean(integerText));
        assertEquals(true, CoreUtils.objToBoolean(booleanText));
        assertThrows(IllegalArgumentException.class, () -> CoreUtils.objToBoolean(justText));

        // test for object to string
        assertEquals("63", CoreUtils.objToString(byteValue));
        assertEquals("8255", CoreUtils.objToString(shortValue));
        assertEquals("1048639", CoreUtils.objToString(intValue));
        assertEquals("1099511627839", CoreUtils.objToString(longValue));
        assertEquals("63.3456", CoreUtils.objToString(floatValue));
        assertEquals("6.33456789", CoreUtils.objToString(doubleValue));
        assertEquals("true", CoreUtils.objToString(trueValue));
        assertEquals("false", CoreUtils.objToString(falseValue));
        assertEquals("", CoreUtils.objToString(emptyText));
        assertEquals("63.4567", CoreUtils.objToString(doubleText));
        assertEquals("63", CoreUtils.objToString(integerText));
        assertEquals("true", CoreUtils.objToString(booleanText));
        assertEquals("abc", CoreUtils.objToString(justText));
        assertEquals("[]", CoreUtils.objToString(Collections.EMPTY_LIST));
        assertEquals("[]", CoreUtils.objToString(Collections.EMPTY_SET));
        assertEquals("{}", CoreUtils.objToString(Collections.EMPTY_MAP));
        assertEquals("[1, 2, 3]", CoreUtils.objToString(intArray));
        assertEquals("[a, b, c]", CoreUtils.objToString(strList));
        assertEquals("[1, [5, 6], [a, b]]", CoreUtils.objToString(complexArray));
        assertEquals("[2, [5, 6], [x, y]]", CoreUtils.objToString(complexList));
        assertEquals("{k1=100, k2=[a, b, c], k3=v}", CoreUtils.objToString(complexMap));

        // test for object to type
        assertNull(CoreUtils.objToType(Object.class, null));
        assertEquals(byteValue, CoreUtils.objToType(Byte.class, byteValue));
        assertEquals(shortValue, CoreUtils.objToType(Short.class, shortValue));
        assertEquals(intValue, CoreUtils.objToType(Integer.class, intValue));
        assertEquals(longValue, CoreUtils.objToType(Long.class, longValue));
        assertEquals(floatValue, CoreUtils.objToType(Float.class, floatValue));
        assertTrue(CoreUtils.doubleEquals(doubleValue, CoreUtils.objToType(Double.class, doubleValue), 0.000001));
        assertEquals(trueValue, CoreUtils.objToType(Boolean.class, trueValue));
        assertEquals(falseValue, CoreUtils.objToType(Boolean.class, falseValue));
        assertEquals(booleanText, CoreUtils.objToType(String.class, booleanText));
        assertEquals(integerText, CoreUtils.objToType(String.class, integerText));
        assertEquals(doubleText, CoreUtils.objToType(String.class, doubleText));
        assertEquals(emptyText, CoreUtils.objToType(String.class, emptyText));
        assertEquals(justText, CoreUtils.objToType(String.class, justText));
        assertEquals("[2, [5, 6], [x, y]]", CoreUtils.objToType(String.class, complexList));
        assertEquals(complexList, CoreUtils.objToType(complexList.getClass(), complexList));
        assertEquals(complexList, CoreUtils.objToType(List.class, complexList));
        assertEquals(complexArray, CoreUtils.objToType(Object[].class, complexArray));
        assertEquals(complexMap, CoreUtils.objToType(Map.class, complexMap));

        // test for object to type with default value
        assertEquals(byteValue, CoreUtils.objToByte(justText, byteValue));
        assertEquals(shortValue, CoreUtils.objToShort(justText, shortValue));
        assertEquals(intValue, CoreUtils.objToInteger(justText, intValue));
        assertEquals(longValue, CoreUtils.objToLong(justText, longValue));
        assertEquals(floatValue, CoreUtils.objToFloat(justText, floatValue));
        assertTrue(CoreUtils.doubleEquals(doubleValue, CoreUtils.objToDouble(justText, doubleValue), 0.000001));
        assertEquals(trueValue, CoreUtils.objToBoolean(justText, trueValue));
        assertEquals(intValue, CoreUtils.objToType(Integer.class, justText, intValue));
        assertEquals(justText, CoreUtils.objToType(String.class, null, justText));
        assertEquals(justText, CoreUtils.objToType(String.class, justText, emptyText));

        System.out.println("--> test object to type OK!");
    }

    @Test
    public void testObjectToList() {
        assertNull(CoreUtils.objToList(null, Integer.class));
        assertEquals(List.of(1, 2, 3), CoreUtils.objToList(List.of("1", 2, "3"), Integer.class));
        assertEquals(List.of(1, 2, 3), CoreUtils.objToList(new TreeSet<>(Set.of("1", "2", "3")), Integer.class));
        assertEquals(List.of(1, 2, 3), CoreUtils.objToList(new Object[] {"1", 2, "3"}, Integer.class));
        assertThrows(ClassCastException.class, () -> CoreUtils.objToList("123", Integer.class));

        System.out.println("--> test object to list OK!");
    }

    @Test
    public void testObjectToMap() {
        assertNull(CoreUtils.objToMap(null, String.class, Integer.class));
        assertEquals(Map.of("a", 1L, "1", 2L), CoreUtils.objToMap(Map.of(1, "2", "a", "1"), String.class, Long.class));
        assertThrows(ClassCastException.class, () -> CoreUtils.objToMap("a,1,1,2", String.class, Long.class));

        System.out.println("--> test object to map OK!");
    }
}
