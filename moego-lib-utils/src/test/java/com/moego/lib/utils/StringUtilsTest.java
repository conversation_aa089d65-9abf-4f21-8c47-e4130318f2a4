package com.moego.lib.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.moego.lib.utils.model.Pair;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

public class StringUtilsTest {

    @Test
    public void testJoin() {
        List<Long> longList = List.of(6L, 5L, 8L, 7L, 9L);
        Long[] longArray = {6L, 5L, 8L, 7L, 9L};

        assertEquals("6, 5, 8, 7, 9", StringUtils.join(", ", longList));
        assertEquals("6, 5, 8, 7, 9", StringUtils.join(", ", longArray));

        System.out.println("--> test string join ok !");
    }

    @Test
    public void testDoubleToString() {
        assertEquals("100", StringUtils.doubleToString(100, -1));
        assertEquals("100", StringUtils.doubleToString(100.0, -1));
        assertEquals("100", StringUtils.doubleToString(100.1, -1));
        assertEquals("100", StringUtils.doubleToString(100.12, -1));

        assertEquals("100", StringUtils.doubleToString(100, 0));
        assertEquals("100", StringUtils.doubleToString(100.0, 0));
        assertEquals("100", StringUtils.doubleToString(100.1, 0));
        assertEquals("100", StringUtils.doubleToString(100.12, 0));

        assertEquals("100.00", StringUtils.doubleToString(100, 2));
        assertEquals("100.00", StringUtils.doubleToString(100.0, 2));
        assertEquals("100.10", StringUtils.doubleToString(100.10, 2));
        assertEquals("100.10", StringUtils.doubleToString(100.100, 2));
        assertEquals("100.10", StringUtils.doubleToString(100.102, 2));
        assertEquals("100.12", StringUtils.doubleToString(100.123, 2));
        assertEquals("100.12", StringUtils.doubleToString(100.1234, 2));
        assertEquals("100.12", StringUtils.doubleToString(100.1289, 2));

        System.out.println("--> test double to string ok !");
    }

    @Test
    public void testMaskText() {
        String text = "abc_def_opq_rst_uvw_xyz";
        assertEquals("****************", StringUtils.maskText(""));
        assertEquals("****************", StringUtils.maskText(text.substring(0, 1)));
        assertEquals("****************", StringUtils.maskText(text.substring(0, 5)));
        assertEquals("a**************e", StringUtils.maskText(text.substring(0, 6)));
        assertEquals("a**************f", StringUtils.maskText(text.substring(0, 7)));
        assertEquals("a**************_", StringUtils.maskText(text.substring(0, 8)));
        assertEquals("ab************_o", StringUtils.maskText(text.substring(0, 9)));
        assertEquals("ab************op", StringUtils.maskText(text.substring(0, 10)));
        assertEquals("abc**********opq", StringUtils.maskText(text.substring(0, 11)));
        assertEquals("abc**********pq_", StringUtils.maskText(text.substring(0, 12)));
        assertEquals("abc_********rst_", StringUtils.maskText(text.substring(0, 16)));
        assertEquals("abc_de****_rst_u", StringUtils.maskText(text.substring(0, 17)));
        assertEquals("abc_de****vw_xyz", StringUtils.maskText(text));

        System.out.println("--> test mask text ok !");
    }

    @Test
    public void testSubString() {
        assertNull(StringUtils.substring(null, 0, 2));
        assertEquals("", StringUtils.substring("1234567890", 7, 3));
        assertEquals("", StringUtils.substring("1234567890", -7, -3));
        assertEquals("", StringUtils.substring("1234567890", 3, 3));
        assertEquals("", StringUtils.substring("1234567890", 11, 13));
        assertEquals("567", StringUtils.substring("1234567890", 4, 7));
        assertEquals("123", StringUtils.substring("1234567890", -7, 3));
        assertEquals("890", StringUtils.substring("1234567890", 7, 13));

        System.out.println("--> test substring ok !");
    }

    @Test
    public void testFindAndReplace() {
        String text = "a${KEY}b#{KEY}c${KEY}de${k1.v1}x${x{x}x${x}x}x${x{x}x}x";
        String pattern = "\\$\\{[-._:\\w]+\\}";
        var pairs = StringUtils.find(text, pattern);
        assertEquals(
                "[{${x}, {39, 43}}, {${k1.v1}, {23, 31}}, {${KEY}, {15, 21}}, {${KEY}, {1, 7}}]", pairs.toString());

        Map<String, String> dictionary = new HashMap<>();
        dictionary.put("${KEY}", " V1 ");
        dictionary.put("${k1.v1}", " V2 ");
        dictionary.put("${x}", " V3 ");

        var s1 = StringUtils.replace(text, pairs, dictionary);
        assertEquals("a V1 b#{KEY}c V1 de V2 x${x{x}x V3 x}x${x{x}x}x", s1);

        dictionary.remove("${k1.v1}");
        var s2 = StringUtils.replace(text, pairs, dictionary);
        assertEquals("a V1 b#{KEY}c V1 de${k1.v1}x${x{x}x V3 x}x${x{x}x}x", s2);

        dictionary.put("${k1.v1}", null);
        var s3 = StringUtils.replace(text, pairs, dictionary);
        assertEquals("a V1 b#{KEY}c V1 dex${x{x}x V3 x}x${x{x}x}x", s3);

        dictionary.remove("${k1.v1}");
        List<Pair<Pair<Integer, Integer>, String>> values = new ArrayList<>();
        for (var pair : pairs) {
            if (dictionary.containsKey(pair.key())) {
                values.add(Pair.of(pair.value(), dictionary.get(pair.key())));
            }
        }

        var s4 = StringUtils.replace(text, values);
        assertEquals("a V1 b#{KEY}c V1 de${k1.v1}x${x{x}x V3 x}x${x{x}x}x", s4);

        System.out.println("--> test find and replace text ok !");
    }

    @Test
    void testAsciiCharacters() {
        String input = "Hello, World! 1234567890";
        String result = StringUtils.filterNonUtf8mb3Chars(input);
        assertEquals("Hello, World! 1234567890", result, "ASCII characters should remain unchanged.");
    }

    @Test
    void testEuropeanCharacters() {
        String input = "Café Münchner Straße";
        String result = StringUtils.filterNonUtf8mb3Chars(input);
        assertEquals("Café Münchner Straße", result, "European characters should remain unchanged.");
    }

    @Test
    void testCjkCharacters() {
        String input = "你好，世界！こんにちは世界！안녕하세요 세계!";
        String result = StringUtils.filterNonUtf8mb3Chars(input);
        assertEquals(
                "你好，世界！こんにちは世界！안녕하세요 세계!",
                result,
                "CJK (Chinese, Japanese, Korean) characters should remain unchanged.");
    }

    @Test
    void testEmojiCharacters() {
        String input = "Hello 🌞, how are you? 😊";
        String result = StringUtils.filterNonUtf8mb3Chars(input);
        assertEquals("Hello , how are you? ", result, "Emoji characters should be removed.");
    }

    @Test
    void testMixedTextWithEmojiAndSpecialSymbols() {
        String input = "Text with emoji 😊 and special characters ♞ that are not utf8mb3.";
        String result = StringUtils.filterNonUtf8mb3Chars(input);
        assertEquals(
                "Text with emoji  and special characters ♞ that are not utf8mb3.",
                result,
                "Emoji and non-utf8mb3 special characters should be removed.");
    }

    @Test
    void testSpecialSymbolsUtf8mb3() {
        String input = "Symbols like ©, ®, ™ should remain as they are part of utf8mb3.";
        String result = StringUtils.filterNonUtf8mb3Chars(input);
        assertEquals(
                "Symbols like ©, ®, ™ should remain as they are part of utf8mb3.",
                result,
                "Common special symbols should remain unchanged.");
    }

    @Test
    void testHighUnicodeCharacters() {
        String input = "This contains high Unicode like 𝄞 and 🤖, which should be removed.";
        String result = StringUtils.filterNonUtf8mb3Chars(input);
        assertEquals(
                "This contains high Unicode like  and , which should be removed.",
                result,
                "High Unicode characters outside utf8mb3 should be removed.");
    }

    @Test
    void testNullInput() {
        String result = StringUtils.filterNonUtf8mb3Chars(null);
        assertNull(result, "Null input should return null.");
    }

    @Test
    void testEmptyInput() {
        String result = StringUtils.filterNonUtf8mb3Chars("");
        assertEquals("", result, "Empty input should return an empty string.");
    }
}
