package com.moego.lib.utils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class RandomUtilsTest {

    @Test
    public void testRandomUpperCharString() {
        for (int i = 0; i < 100; i++) {
            var r = RandomUtils.randomUpperCharString(6);
            Assertions.assertTrue(r.matches("[A-Z]{6}"));
        }
    }

    @Test
    public void testRandomLowerCharString() {
        for (int i = 0; i < 100; i++) {
            var r = RandomUtils.randomLowerCharString(6);
            Assertions.assertTrue(r.matches("[a-z]{6}"));
        }
    }

    @Test
    public void testRandomNumString() {
        for (int i = 0; i < 100; i++) {
            var r = RandomUtils.randomNumString(6);
            Assertions.assertTrue(r.matches("[0-9]{6}"));
        }
    }

    @Test
    public void testRandomCharAndNumString() {
        for (int i = 0; i < 100; i++) {
            var r = RandomUtils.randomCharAndNumString(6);
            Assertions.assertTrue(r.matches("[A-Za-z0-9]{6}"));
        }
    }

    @Test
    public void testRandomLowerCharAndNumString() {
        for (int i = 0; i < 100; i++) {
            var r = RandomUtils.randomLowerCharAndNumString(6);
            Assertions.assertTrue(r.matches("[a-z0-9]{6}"));
        }
    }

    @Test
    public void testRandomUpperCharAndNumString() {
        for (int i = 0; i < 100; i++) {
            var r = RandomUtils.randomUpperCharAndNumString(6);
            Assertions.assertTrue(r.matches("[A-Z0-9]{6}"));
        }
    }

    @Test
    public void testRandomUpperCharAndNonzeroNumString() {
        for (int i = 0; i < 100; i++) {
            var r = RandomUtils.randomUpperCharAndNonzeroNumString(6);
            Assertions.assertEquals(6, r.length());
            Assertions.assertTrue(r.matches("[A-Z1-9]{6}"));
        }
    }

    @Test
    public void testRandomIntUpperAndLower() {
        for (int i = 0; i < 100; i++) {
            int r = RandomUtils.randomInt(10, 20);
            Assertions.assertTrue(r >= 10 && r < 20);
        }
    }

    @Test
    public void testRandomInt() {
        for (int i = 0; i < 100; i++) {
            int r = RandomUtils.randomInt(10);
            Assertions.assertTrue(r >= 0 && r < 10);
        }
    }
}
