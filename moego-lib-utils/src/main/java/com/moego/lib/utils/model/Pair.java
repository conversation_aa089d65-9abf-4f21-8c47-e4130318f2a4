package com.moego.lib.utils.model;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

public class Pair<K, V> implements Serializable {

    @Serial
    private static final long serialVersionUID = 3011956546342717869L;

    private K key;
    private V value;

    public Pair() {}

    public Pair(K k, V v) {
        this.key = k;
        this.value = v;
    }

    public K key() {
        return this.key;
    }

    public void key(K k) {
        this.key = k;
    }

    public V value() {
        return this.value;
    }

    public void value(V v) {
        this.value = v;
    }

    @Override
    public String toString() {
        return "{" + this.key + ", " + this.value + "}";
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null) return false;

        if (o instanceof Pair<?, ?> that) {
            return Objects.equals(key(), that.key()) && Objects.equals(value(), that.value());
        }

        return false;
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.key, this.value);
    }

    public static <K, V> Pair<K, V> of(K k, V v) {
        return new Pair<>(k, v);
    }
}
