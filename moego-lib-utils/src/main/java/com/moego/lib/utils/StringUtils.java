package com.moego.lib.utils;

import com.moego.lib.utils.model.Pair;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class StringUtils {

    public static boolean isBlank(String str) {
        return str == null || str.isBlank();
    }

    public static boolean isEmpty(String str) {
        return str == null || str.isEmpty();
    }

    public static <T> String join(String delimiter, Iterable<T> coll) {
        if (coll == null) {
            return "";
        }
        List<String> elements = new ArrayList<>();
        for (var element : coll) {
            elements.add(element.toString());
        }

        return String.join(delimiter, elements);
    }

    public static <T> String join(String delimiter, T[] coll) {
        if (coll == null || coll.length < 1) {
            return "";
        }

        return String.join(delimiter, Arrays.stream(coll).map(String::valueOf).toList());
    }

    public static String doubleToString(double v, int precision) {
        if (0 < precision) {
            var df = new DecimalFormat("0." + "0".repeat(precision));
            df.setRoundingMode(RoundingMode.DOWN);
            return df.format(v);
        }

        return String.valueOf((long) v);
    }

    /**
     * This method obfuscates strings and is mainly used for the output of some sensitive information, such as in logs.
     * It obfuscates at least 4 characters. Returns text with a fixed length of 16 characters.
     */
    public static String maskText(String text) {
        if (16 < text.length()) {
            return text.substring(0, 6) + "****" + text.substring(text.length() - 6);
        } else if (12 < text.length()) {
            return text.substring(0, 4) + "********" + text.substring(text.length() - 4);
        } else if (10 < text.length()) {
            return text.substring(0, 3) + "**********" + text.substring(text.length() - 3);
        } else if (8 < text.length()) {
            return text.substring(0, 2) + "************" + text.substring(text.length() - 2);
        } else if (5 < text.length()) {
            return text.charAt(0) + "**************" + text.substring(text.length() - 1);
        } else {
            return "****************";
        }
    }

    /**
     * Finds string using the specified regular expression.
     * @param text    the string to be searched
     * @param pattern regular expression
     * @return Returns the matched substring list and the corresponding position
     */
    public static List<Pair<String, Pair<Integer, Integer>>> find(String text, String pattern) {
        Matcher matcher = Pattern.compile(pattern).matcher(text);
        List<Pair<String, Pair<Integer, Integer>>> keys = new ArrayList<>();
        while (matcher.find()) {
            int start = matcher.start();
            int end = matcher.end();
            String key = text.substring(start, end);
            keys.add(0, Pair.of(key, Pair.of(start, end)));
        }

        return keys;
    }

    /**
     * Replace string based on position
     * Note: the pairs list needs to be sorted in reverse order according to the key(position).
     * @param src   the original string to be replaced
     * @param pairs key-value pairs of 'position' and 'replacement value'
     * @return Returns the replaced string
     */
    public static String replace(String src, List<Pair<Pair<Integer, Integer>, String>> pairs) {
        if (pairs == null || pairs.isEmpty()) {
            return src;
        }
        StringBuilder sb = new StringBuilder(src);
        for (var pair : pairs) {
            var site = pair.key();
            sb.replace(site.key(), site.value(), pair.value() == null ? "" : pair.value());
        }

        return sb.toString();
    }

    /**
     * Replace string based on position and dictionary
     * @param src   the original string to be replaced
     * @param pairs are returned by the above #find(text, pattern) method
     * @param dictionary mapping of substrings to be replaced and replaced values
     * @return Returns the replaced string
     */
    public static String replace(
            String src, List<Pair<String, Pair<Integer, Integer>>> pairs, Map<String, String> dictionary) {
        List<Pair<Pair<Integer, Integer>, String>> values = new ArrayList<>();
        for (var pair : pairs) {
            if (dictionary.containsKey(pair.key())) {
                values.add(Pair.of(pair.value(), dictionary.get(pair.key())));
            }
        }

        return replace(src, values);
    }

    /**
     * Secure truncated string
     * @param src   the original string to be truncated
     * @param start the start position to truncate
     * @param end   the end position to truncate
     * @return Returns the truncated substring
     */
    public static String substring(String src, int start, int end) {
        if (src == null) {
            return null;
        }
        if (start < 0) {
            start = 0;
        }
        if (start < end && start < src.length()) {
            if (end < src.length()) {
                return src.substring(start, end);
            } else {
                return src.substring(start);
            }
        }

        return "";
    }

    private static final Pattern NON_UTF8MB3_PATTERN =
            Pattern.compile("[\\ud800\\udc00-\\udbff\\udfff\\ud800-\\udfff]");

    public static String filterNonUtf8mb3Chars(String text) {
        if (text == null) {
            return null;
        }
        return NON_UTF8MB3_PATTERN.matcher(text).replaceAll("");
    }
}
