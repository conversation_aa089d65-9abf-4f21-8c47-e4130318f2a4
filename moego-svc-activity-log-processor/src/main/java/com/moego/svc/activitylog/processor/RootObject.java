package com.moego.svc.activitylog.processor;

import java.lang.reflect.Method;

/**
 * Simple root object for expression evaluation.
 *
 * <p> Support {@code #root.args}, {@code #root.target}, {@code #root.method}, {@code #root.targetClass}
 *
 * @param method      method
 * @param args        args
 * @param target      target
 * @param targetClass target class
 * <AUTHOR>
 */
public record RootObject(Method method, Object[] args, Object target, Class<?> targetClass) {}
