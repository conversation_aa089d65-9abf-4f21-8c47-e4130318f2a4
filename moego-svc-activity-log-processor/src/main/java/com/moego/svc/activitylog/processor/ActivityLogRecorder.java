package com.moego.svc.activitylog.processor;

import com.moego.lib.messaging.Messages;
import com.moego.svc.activitylog.event.ActivityLogEvent;
import com.moego.svc.activitylog.event.enums.ResourceType;
import java.util.Optional;
import lombok.experimental.UtilityClass;
import org.springframework.lang.Nullable;
import org.springframework.util.ClassUtils;

/**
 * Utility class for recording activity logs.
 *
 * <p> Example:
 * <pre>{@code
 * ActivityLogRecorder.record(
 *      "resource_type",
 *      "resource_id",
 *      "action",
 *      "details"
 * )
 * }</pre>
 *
 * <AUTHOR>
 */
@UtilityClass
public class ActivityLogRecorder {

    /**
     * Record an activity log.
     *
     * <p> NOTE:
     * <p> action should start with a capital letter, e.g. "Create", "Batch Create", not "create", "batch create".
     * <p> resourceId and details are nullable.
     *
     * @param action       action
     * @param resourceType {@link ResourceType}
     * @param resourceId   resource id, nullable
     * @param details      details, nullable
     * @see com.moego.svc.activitylog.event.enums.Action
     * @see com.moego.svc.activitylog.event.enums.ResourceType
     */
    public static void record(
            String action, ResourceType resourceType, @Nullable Object resourceId, @Nullable Object details) {
        ActivityLogEvent event = ActivityLogs.event(action, resourceType, resourceId, details);
        event.setClassName(details != null ? details.getClass().getName() : null);
        Messages.asyncSendTx(ActivityLogEvent.TOPIC, event);
    }

    /**
     * @param businessId   business id, nullable
     * @param action       action
     * @param resourceType {@link ResourceType}
     * @param resourceId   resource id, nullable
     * @param details      details, nullable
     * @see #record(String, ResourceType, Object, Object)
     */
    public static void record(
            Object businessId,
            String action,
            ResourceType resourceType,
            @Nullable Object resourceId,
            @Nullable Object details) {
        ActivityLogEvent event = ActivityLogs.event(action, resourceType, resourceId, details);
        Optional.ofNullable(toLong(businessId)).ifPresent(event::setBusinessId);
        event.setClassName(details != null ? details.getClass().getName() : null);
        Messages.asyncSendTx(ActivityLogEvent.TOPIC, event);
    }

    /**
     * @param businessId   business id, nullable
     * @param operatorId   operator id, nullable
     * @param action       action
     * @param resourceType {@link ResourceType}
     * @param resourceId   resource id, nullable
     * @param details      details, nullable
     * @see #record(String, ResourceType, Object, Object)
     */
    public static void record(
            Object businessId,
            Object operatorId,
            String action,
            ResourceType resourceType,
            @Nullable Object resourceId,
            @Nullable Object details) {
        ActivityLogEvent event = ActivityLogs.event(action, resourceType, resourceId, details);
        Optional.ofNullable(toLong(businessId)).ifPresent(event::setBusinessId);
        Optional.ofNullable(operatorId).map(Object::toString).ifPresent(event::setOperatorId);
        event.setClassName(details != null ? details.getClass().getName() : null);
        Messages.asyncSendTx(ActivityLogEvent.TOPIC, event);
    }

    /**
     * Record an activity log.
     *
     * @param event {@link ActivityLogEvent}
     */
    public static void record(ActivityLogEvent event) {
        Messages.asyncSendTx(ActivityLogEvent.TOPIC, event);
    }

    public static void recordRoot(
            String action, ResourceType resourceType, @Nullable Object resourceId, @Nullable Object details) {
        ActivityLogEvent event = ActivityLogs.rootEvent(action, resourceType, resourceId, details);
        event.setClassName(details != null ? details.getClass().getName() : null);
        Messages.asyncSendTx(ActivityLogEvent.TOPIC, event);
    }

    public static void recordRoot(
            Object businessId,
            String action,
            ResourceType resourceType,
            @Nullable Object resourceId,
            @Nullable Object details) {
        ActivityLogEvent event = ActivityLogs.rootEvent(action, resourceType, resourceId, details);
        Optional.ofNullable(toLong(businessId)).ifPresent(event::setBusinessId);
        event.setClassName(details != null ? details.getClass().getName() : null);
        Messages.asyncSendTx(ActivityLogEvent.TOPIC, event);
    }

    static Long toLong(Object id) {
        if (ClassUtils.isAssignableValue(Long.class, id)) {
            return (Long) id;
        }
        if (ClassUtils.isAssignableValue(Integer.class, id)) {
            return ((Integer) id).longValue();
        }
        if (ClassUtils.isAssignableValue(String.class, id)) {
            return Long.parseLong((String) id);
        }
        return null;
    }
}
