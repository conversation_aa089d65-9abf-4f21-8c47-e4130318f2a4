package com.moego.svc.activitylog.processor;

import org.springframework.beans.factory.BeanFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(prefix = ActivityLogProperties.PREFIX, name = "enabled", matchIfMissing = true)
@EnableConfigurationProperties(ActivityLogProperties.class)
public class ActivityLogProcessorAutoConfiguration {

    @Bean
    public ActivityLogAspect activityLogAspect(BeanFactory beanFactory) {
        return new ActivityLogAspect(beanFactory);
    }
}
