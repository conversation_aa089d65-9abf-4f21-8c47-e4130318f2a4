plugins {
    id 'io.spring.dependency-management' version "${springDependencyManagementVersion}" apply false
    id 'com.diffplug.spotless' version "${spotlessVersion}" apply false
    id "com.github.spotbugs" version "${spotbugsVersion}" apply false
}

subprojects {
    group = 'com.moego'

    apply plugin: 'java'
    apply plugin: 'java-library'

    repositories {
        mavenLocal()
        mavenCentral()
    }
    compileJava {
        options.encoding = 'UTF-8'
        options.compilerArgs << '-parameters'
    }
    compileTestJava {
        options.encoding = 'UTF-8'
        options.compilerArgs << '-parameters'
    }
    test {
        useJUnitPlatform()
    }
    // dependency management
    apply plugin: 'io.spring.dependency-management'
    dependencyManagement {
        imports {
            mavenBom "org.springframework.boot:spring-boot-dependencies:${springBootVersion}"
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        }
    }
    // spotless
    apply plugin: 'com.diffplug.spotless'
    spotless {
        encoding 'UTF-8'
        java {
            toggleOffOn()
            removeUnusedImports()
            trimTrailingWhitespace()
            endWithNewline()
            palantirJavaFormat()

            targetExclude "build/generated/**"
            custom('Refuse wildcard imports', {
                if (it =~ /\nimport .*\*;/) {
                    throw new IllegalStateException("Do not use wildcard imports, 'spotlessApply' cannot resolve this issue, please fix it manually.")
                }
            })
        }
    }
    // spotbugs
    apply plugin: 'com.github.spotbugs'
    spotbugs {
        spotbugsTest.enabled = false
        omitVisitors.addAll 'FindReturnRef', 'MethodReturnCheck'
    }
}
