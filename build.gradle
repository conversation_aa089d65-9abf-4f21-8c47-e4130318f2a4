plugins {
  id 'java'

  // 添加 jacoco 插件
  id 'jacoco'
  id 'net.razvan.jacoco-to-cobertura' version "1.2.0"

  id 'org.springframework.boot' version "${springBootVersion}"
  id 'io.spring.dependency-management' version "${springDependencyManagementVersion}"
  id 'com.diffplug.spotless' version "${spotlessVersion}"
  id "com.github.spotbugs" version "${spotbugsVersion}"
  id "com.dorongold.task-tree" version "2.1.1"
}

repositories {
  mavenLocal()
  mavenCentral()
  maven { url 'https://jitpack.io' }
}

apply from: "https://moego.s3.us-west-2.amazonaws.com/ops/sbc/${System.getProperty('sbc.scriptBranch', '')}SourceBranchControl.gradle"
applySourceBranchControlDependencies()

dependencyManagement {
  imports {
    mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
  }
}

dependencies {
  compileOnly 'org.projectlombok:lombok'
  compileOnly("com.github.spotbugs:spotbugs-annotations:4.7.3")
  annotationProcessor 'org.projectlombok:lombok'
  annotationProcessor 'org.projectlombok:lombok-mapstruct-binding:0.2.0'
  annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

  testImplementation 'org.springframework.boot:spring-boot-starter-test'

  implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
  implementation 'com.mandrillapp.wrapper.lutung:lutung:0.0.8'
  implementation "io.awspring.cloud:spring-cloud-aws-starter-secrets-manager:${awsSpringCloudVersion}"

  testImplementation "io.grpc:grpc-testing:${grpcVersion}"

  implementation "org.mapstruct:mapstruct:${mapstructVersion}"
  annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"
  testAnnotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"

  annotationProcessor 'no.entur.mapstruct.spi:protobuf-spi-impl:1.43'
  testAnnotationProcessor 'no.entur.mapstruct.spi:protobuf-spi-impl:1.43'

  implementation 'org.springframework.boot:spring-boot-starter-web'
  implementation 'org.springframework.boot:spring-boot-starter-validation'
  // cache
  implementation 'com.github.ben-manes.caffeine:caffeine'

  implementation 'com.auth0:java-jwt:4.5.0'
}

compileJava {
  options.compilerArgs << '-parameters'
}

tasks.withType(JavaCompile).configureEach {
  options.encoding = 'UTF-8'
}

tasks.named("bootJar") {
  archiveBaseName = 'moego-server'
}

test {
  useJUnitPlatform()
}

spotless {
  encoding 'UTF-8'
  java {
    toggleOffOn()
    removeUnusedImports()
    trimTrailingWhitespace()
    endWithNewline()
    palantirJavaFormat()

    targetExclude "build/generated/**"
    custom('Refuse wildcard imports', {
      if (it =~ /\nimport .*\*;/) {
        throw new IllegalStateException("Do not use wildcard imports, 'spotlessApply' cannot resolve this issue, please fix it manually.")
      }
    } as Closure<String>)

    custom('Refuse new BizException', {
      if (it =~ /new BizException(.*)/) {
        throw new IllegalStateException("Do not use new BizException, use ExceptionUtil instead.")
      }
    } as Closure<String>)
  }
}

spotbugs {
  spotbugsTest.enabled = false
  omitVisitors.addAll 'FindReturnRef', 'MethodReturnCheck'
  excludeFilter.set(file("${rootDir}/config/spotbugs/excludeFilter.xml"))
}

// 添加 jacoco report task
jacocoTestReport {
  reports {
    xml {
      required = true
      destination file("${rootProject.buildDir}/reports/jacoco/test/jacocoTestReport.xml")
    }
  }
  afterEvaluate {
    classDirectories.setFrom(files(classDirectories.files.collect {
      fileTree(dir: it, exclude: [
        "**/*ConverterImpl*",
        "**/*MapperImpl*",
        "**/jooq/generated/**",
      ])
    }))
  }
}
