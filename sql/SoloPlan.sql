ALTER TABLE `moe_business`.`moe_company`
    ADD COLUMN `company_type` tinyint NOT NULL DEFAULT 0 COMMENT '0 mobile 1 salon 2 hybrid';

CREATE TABLE `moe_question_record` (
       `id` bigint unsigned NOT NULL AUTO_INCREMENT,
       `company_id` bigint NOT NULL DEFAULT '0',
       `source_from` varchar(50) NOT NULL DEFAULT '',
       `source_from_other` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '对应注册时source选other输入内容',
       `appt_per_week` varchar(50) NOT NULL DEFAULT '',
       `pet_per_month` varchar(50) NOT NULL DEFAULT '',
       `business_years` varchar(50) NOT NULL DEFAULT '',
       `move_from` varchar(50) NOT NULL DEFAULT '',
       `total_locations` varchar(50) NOT NULL DEFAULT '',
       `total_vans` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
       `is_required` tinyint NOT NULL DEFAULT '0',
       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
       PRIMARY KEY (`id`),
       UNIQUE KEY `unique_cid` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci