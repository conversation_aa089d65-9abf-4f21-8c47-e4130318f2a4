create table moe_business.moe_staff_working_location (
  id bigint(20)  not null auto_increment,
  staff_id bigint(20)  not null  default 0 comment '员工id',
  company_id bigint(20)  not null default 0 comment '公司id',
  working_location_id bigint(20)  not null default 0 comment '工作地点的 business id',
  primary key (id),
  index idx_cid_staff (company_id, staff_id)
) ENGINE = InnoDB
   DEFAULT CHARSET = utf8mb4;

alter table moe_business.moe_staff add column `working_in_all_locations` boolean not null default false comment '是否在所有 location 工作';

alter table moe_business.moe_company add column `currency_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'USD';
alter table moe_business.moe_company add column `currency_symbol` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '$';
alter table moe_business.moe_company add column `date_format_type` tinyint NOT NULL DEFAULT '1' COMMENT '1="MM/DD/YYYY", 2="DD/MM/YYYY", 3="DD.MM.YYYY", 4="YYYY.MM.DD", 5="YYYY/MM/DD"';
alter table moe_business.moe_company add column `time_format_type` tinyint NOT NULL DEFAULT '2' COMMENT '1=24小时制, 2=12小时制';
alter table moe_business.moe_company add column `unit_of_weight_type` tinyint NOT NULL DEFAULT '1' COMMENT '1=pound, 2=kilogram';
alter table moe_business.moe_company add column `unit_of_distance_type` tinyint NOT NULL DEFAULT '1' COMMENT 'mile(1), kilometer(2)';
alter table moe_business.moe_company add column `notification_sound_enable` tinyint NOT NULL DEFAULT '1' COMMENT '是否打开通知声音，0-关闭，1-打开';
ALTER TABLE moe_business.moe_company ADD COLUMN `enterprise_id` int  NOT NULL DEFAULT 0 COMMENT 'enterprise_id，未关联enterprise为0';
alter table moe_business.moe_company add column `country_alpha2_code` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'Country code alpha2';
alter table moe_business.moe_company add column `timezone_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'America/Los_Angeles';
alter table moe_business.moe_company add column `timezone_seconds` int NOT NULL DEFAULT '0';
alter table moe_business.moe_company add column `source` int NOT NULL DEFAULT '0' COMMENT 'how to know us, 0 - unspecify, 1 - other, 2 - facebook, 3 - internet search, 4 - capterra, 5 - square, 6 - expo';
alter table moe_business.moe_company add column `know_about_us` varchar(255) not null default '' comment '对应 source = 1(other) 时用户填写的内容';
ALTER TABLE moe_business.moe_company ADD INDEX `enterprise_id_idx`(`enterprise_id`) USING BTREE;

ALTER TABLE moe_business.moe_staff ADD COLUMN `enterprise_id` int  NOT NULL DEFAULT 0 COMMENT 'enterprise_id，非enterprise staff 为0';
ALTER TABLE moe_business.moe_staff ADD INDEX `enterprise_id_idx`(`enterprise_id`) USING BTREE;

create table moe_business.moe_enterprise
(
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'enterprise iD',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `account_id` int NOT NULL DEFAULT '0' COMMENT '创建enterprise的accountId',
  `created_at`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `updated_at`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
)ENGINE=InnoDB AUTO_INCREMENT=100000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- moe_staff
ALTER TABLE moe_business.moe_staff ADD COLUMN `color_code` varchar(20) NOT NULL DEFAULT '0' COMMENT 'staff color code';
ALTER TABLE moe_business.moe_staff ADD COLUMN `last_visit_business_id` int NOT NULL DEFAULT '0' COMMENT 'last visit business id';
ALTER TABLE moe_business.moe_staff ADD COLUMN `profile_email` varchar(100) NOT NULL DEFAULT '' COMMENT 'staff profile email, not linked with account';
ALTER TABLE moe_business.moe_staff ADD COLUMN `require_access_code` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'require access code, for clock in/out or other actions';

-- 新增 company clock in/out setting 表
create table moe_business.moe_clock_in_out_setting
(
  `id`                  bigint   NOT NULL AUTO_INCREMENT COMMENT 'id',
  `company_id`          bigint   NOT NULL DEFAULT '0' COMMENT 'company id',
  `enable_clock_in_out` tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否开启clockInOut',
  `clock_in_out_notify` tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否开启clockInOut通知',
  `created_at`          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `updated_at`          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
  `updated_by`          bigint   NOT NULL DEFAULT '0' COMMENT 'update by',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `company_id` (`company_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

# staff payroll setting 修改索引：1.把原来的 business_id staff_id 唯一索引修改为普通索引；2.新增 company_id staff_id 唯一索引
ALTER TABLE moe_business.moe_staff_payroll_setting DROP INDEX `udx_biz_id_staff_id`;
ALTER TABLE moe_business.moe_staff_payroll_setting ADD INDEX `idx_biz_id_staff_id`(`business_id`, `staff_id`) USING BTREE;
ALTER TABLE moe_business.moe_staff_payroll_setting ADD UNIQUE INDEX `udx_company_id_staff_id`(`company_id`, `staff_id`) USING BTREE;
ALTER TABLE moe_business.moe_staff_payroll_setting MODIFY COLUMN business_id int unsigned DEFAULT 0 NULL;

# moe_staff_appointment 表增加 business_id，这个表是记录 staff 可以返回的其它 staff calendar 的关联记录，multi-location 需要支持按 location 配置 staff，所以新增 business_id
ALTER TABLE moe_business.moe_staff_appointment ADD COLUMN `business_id` bigint NOT NULL DEFAULT '0' COMMENT 'business id';

# 移除 moe_staff_appointment staff access 自己的记录，手动
DELETE FROM moe_business.moe_staff_appointment WHERE staff_id = staff_id_appointment;

# moe_staff_notification 表增加 company_id, business_id
alter table moe_business.moe_staff_notification add column `company_id` bigint NOT NULL DEFAULT '0' COMMENT 'company_id';
alter table moe_business.moe_staff_notification add column `business_id` bigint NOT NULL DEFAULT '0' COMMENT 'business_id';
