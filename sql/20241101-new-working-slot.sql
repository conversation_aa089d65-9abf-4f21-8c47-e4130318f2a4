CREATE TABLE `staff_availability` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `company_id` BIGINT NOT NULL DEFAULT 0,
    `business_id` BIGINT NOT NULL DEFAULT 0,
    `staff_id` BIGINT NOT NULL DEFAULT 0,
    `slot_schedule_type` INT NOT NULL DEFAULT 0 COMMENT 'slot every n week',
    `slot_start_sunday` DATE NOT NULL DEFAULT (CURRENT_DATE) COMMENT 'schedule_type start sunday date',
    `time_schedule_type` INT NOT NULL DEFAULT 0 COMMENT 'time every n week',
    `time_start_sunday` DATE NOT NULL DEFAULT (CURRENT_DATE) COMMENT 'schedule_type start sunday date',
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='staff available setting';

CREATE UNIQUE INDEX `uniqed_staff_id` ON `staff_availability` (
    `business_id`,
    `staff_id`
);

CREATE TABLE `staff_availability_slot_day` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `company_id` BIGINT NOT NULL DEFAULT 0,
    `business_id` BIGINT NOT NULL DEFAULT 0,
    `staff_id` BIGINT NOT NULL DEFAULT 0,
    `override_date` DATE DEFAULT NULL,
    `is_available` BOOLEAN NOT NULL DEFAULT false COMMENT 'day available',
    `schedule_type` INT NOT NULL DEFAULT 0 COMMENT 'every n week',
    `day_of_week` INT NOT NULL DEFAULT 0,
    `start_time` INT NOT NULL DEFAULT 0,
    `end_time` INT NOT NULL DEFAULT 0,
    `capacity` INT NOT NULL DEFAULT 0,
    `limit_ids` JSON NOT NULL DEFAULT ('[]'),
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='staff slot setting';

CREATE TABLE `staff_availability_time_day` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `company_id` BIGINT NOT NULL DEFAULT 0,
    `business_id` BIGINT NOT NULL DEFAULT 0,
    `staff_id` BIGINT NOT NULL DEFAULT 0,
    `override_date` DATE DEFAULT NULL,
    `is_available` BOOLEAN NOT NULL DEFAULT false COMMENT 'day available',
    `schedule_type` INT NOT NULL DEFAULT 0 COMMENT 'every n week',
    `day_of_week` INT NOT NULL DEFAULT 0,
    `limit_ids` JSON NOT NULL DEFAULT ('[]'),
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='staff slot/time setting';

CREATE INDEX `idx_staff_type_day` ON `staff_availability_slot_day` (
    `staff_id`,
    `schedule_type`,
    `day_of_week`
);
CREATE INDEX `idx_staff_type_date` ON `staff_availability_slot_day` (
    `staff_id`,
    `override_date`
);
CREATE INDEX `idx_staff_type_day` ON `staff_availability_time_day` (
    `staff_id`,
    `schedule_type`,
    `day_of_week`
);
CREATE INDEX `idx_staff_type_date` ON `staff_availability_time_day` (
    `staff_id`,
    `override_date`
);
CREATE TABLE `staff_availability_day_hour` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `day_type` INT NOT NULL DEFAULT 0,
    `day_id` BIGINT NOT NULL DEFAULT 0,
    `start_time` INT NOT NULL DEFAULT 0,
    `end_time` INT NOT NULL DEFAULT 0,
    `capacity` INT NOT NULL DEFAULT 0,
    `limit_ids` JSON NOT NULL DEFAULT ('[]'),
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    PRIMARY KEY (`id`)
) ENGINE=InnoDB;

CREATE INDEX `idx_day` ON `staff_availability_day_hour` (`day_id`);

CREATE TABLE `day_hour_limit` (
      `id` bigint NOT NULL AUTO_INCREMENT,
      `type` int NOT NULL DEFAULT 0,
      `pet_size_ids` json NOT NULL DEFAULT ('[]'),
      `pet_type_id` bigint NOT NULL DEFAULT 0,
      `is_all_breed` boolean NOT NULL DEFAULT false,
      `breed_ids` json NOT NULL DEFAULT ('[]'),
      `service_ids` json NOT NULL DEFAULT ('[]'),
      `is_all_service` boolean NOT NULL DEFAULT false,
      `capacity` int NOT NULL DEFAULT 0,
      `created_at` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
      `updated_at` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
      PRIMARY KEY (`id`)
) COMMENT='slot/time limit setting';
