CREATE TABLE `moe_business`.`moe_staff_login_time`  (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `staff_id` bigint NOT NULL DEFAULT 0,
    `login_limit_type` tinyint not null default 1 comment '0-unknown 1-anytime 2-time range; see StaffLoginLimitType',
    `start_time` bigint NOT NULL DEFAULT 0 COMMENT '一天中的某一分钟，0~1440',
    `end_time` bigint NOT NULL DEFAULT 0  COMMENT '一天中的某一分钟，0~1440',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_staff_id`(`staff_id`) USING BTREE
);
