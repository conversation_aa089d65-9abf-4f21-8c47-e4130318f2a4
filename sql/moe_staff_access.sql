-- 新增 moe_staff_access 表
create table moe_business.moe_staff_access
(
  `id`                         bigint   NOT NULL AUTO_INCREMENT COMMENT 'id',
  `staff_id`                   bigint   NOT NULL COMMENT 'staff id',
  `company_id`                 bigint   NOT NULL DEFAULT '0' COMMENT 'company id',
  `location_id`                bigint   NOT NULL DEFAULT '0' COMMENT 'location/business id',
  `access_location_all_staffs` bool     NOT NULL DEFAULT false COMMENT 'can access current location all working staffs',
  `access_staff_ids`           json     NOT NULL DEFAULT (JSON_ARRAY()) COMMENT 'access staff id list',
  `updated_at`                 datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
  `updated_by`                 bigint   NOT NULL DEFAULT '0' COMMENT 'update by',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `udx_staff_id_location_id` (`staff_id`, `location_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

# 2024-04-24 废弃旧表，修改表名为 legacy_xxx，等代码上线后执行
ALTER TABLE moe_business.moe_staff_appointment RENAME moe_business.legacy_moe_staff_appointment;

# 2024-05-29 新增字段
ALTER TABLE moe_business.moe_staff_access
ADD COLUMN `is_shown_on_calendar` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'staff的calendar是否在business上显示';
