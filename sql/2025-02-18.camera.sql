CREATE TABLE `moe_business`.`idogcam_config` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `company_id` bigint NOT NULL DEFAULT 0,
  `kennel_id` varchar(255) NOT NULL DEFAULT '',
  `erp_code` varchar(255) NOT NULL DEFAULT '',
  `is_active` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_company_kennel_erp` (`company_id`, `kennel_id`, `erp_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `moe_business`.`abckam_config` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `company_id` bigint NOT NULL,
  `abckam_id` varchar(255) NOT NULL DEFAULT '',
  `is_active` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_company_abckam` (`company_id`, `abckam_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `moe_business`.`camera` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `company_id` bigint NOT NULL,
  `business_id` bigint NOT NULL,
  `config_id` bigint NOT NULL comment "idogcam_config.id or abckam_config.id",
  `type` tinyint NOT NULL comment "1 idogcat 2 abckam",
  `origin_camera_id` varchar(255) NOT NULL DEFAULT '',
  `origin_camera_title` varchar(255) NOT NULL DEFAULT '' comment 'Third-party data title',
  `origin_status` int NOT NULL DEFAULT 0 comment "Third-party data origin status",
  `video_url` varchar(512) NOT NULL DEFAULT '',
  `is_active` tinyint(1) NOT NULL DEFAULT 0,
  `visibility_type` int NOT NULL DEFAULT 0 comment "1 public 2 private",
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_company_type_origin_camera` (`company_id`, `type`, `origin_camera_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


ALTER TABLE `moe_business`.`camera`
    ADD COLUMN `auth` varchar(255) NOT NULL DEFAULT '';

alter table `moe_business`.`camera`
    alter column business_id set default 0;

INSERT INTO `moe_payment`.`moe_feature`
(name, code, allow_type, create_time, enable, quota, update_time,is_deleted)
VALUES
    ('lodging unit camera', 'unitCamera', 2, '2025-02-25 09:00:00', 0, 0, '2025-02-25 09:00:00',0)